import assert from "assert";

import { Hu<PERSON>potService } from "../server/hubspotService";

describe("momentpathweb - hubspot", function () {

  it("Hubspot Service - syncOrg fails if called with an invalid orgId", async function () {
    try {
      await HubspotService.syncOrg({ orgId: 'missing-org-id' });
    } catch (e) {
      // Expect an error with message "Org not found: missing-org-id":
      assert.equal(e.message, "Org not found: missing-org-id");
    }
  });

  it("Hubspot Service - syncOrg succeeds with a valid orgId", async function () {
    const result = await HubspotService.syncOrg({ orgId: 'test-id' });
    // Assert that the result is an object:
    assert.equal(typeof result, "object");
    // Assert that the result has the expected keys:
    assert.equal(JSON.stringify(result), JSON.stringify({
      error: false,
      message: 'success',
      processedCount: 3,
      successCount: 3,
      errorCount: 0
    }));
  }).timeout(30 * 1000); // Is a 30 second timeout appropriate here? For some reason syncOrg hangs.

});
