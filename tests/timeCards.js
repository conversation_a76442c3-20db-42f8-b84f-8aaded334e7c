import assert from "assert";
import { Orgs } from "../lib/collections/orgs";
import { TimeCards } from "../lib/collections/timeCards";

describe("momentpathweb - timeCards", function () {

    let org;

    beforeEach(async function () {
        // Remove all timeCards
        await TimeCards.removeAsync({});
        // Re-assign the org. May not need to do this before each test.
        // This does make sure it's refetched from the database before each test, though.
        org = await Orgs.findOneAsync({ _id: 'test-id' });
        assert(org, "Org not found");
    });

    describe("can be queried for with options", function () {
        it("no options are passed", async function () {
            // We have not inserted any TimeCards at this point, so the query
            // should succeed and the timecards variable should be a Mongo cursor.
            const timecardCursor = await TimeCards.queryWithOptions(org);
            assert(timecardCursor, "timecardCursor is null");
            // Fetch the timecards from the cursor.
            const timecards = timecardCursor.fetch();
            assert(Array.isArray(timecards), "timecards is not an array");
            assert.equal(timecards.length, 0, "timecards is not empty");
        });
    })

});
