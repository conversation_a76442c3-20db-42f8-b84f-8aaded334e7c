import { Meteor } from 'meteor/meteor';

// Helper function to send log messages to the server
const sendLog = (level, message, data = {}) => {
    // Send the log and data to the server
    Meteor.callAsync('log.client', level, message, data).then((result) => {
        console.log(`Successfully sent ${level} log to server:`, result);
    }).catch((err) => {
        console.error(`Failed to send ${level} log to server:`, err);
    });
};

// Client-side logger that sends log messages to the server
const clientLogger = {
    emerg: (message, data = {}) => sendLog('emerg', message, data),
    alert: (message, data = {}) => sendLog('alert', message, data),
    crit: (message, data = {}) => sendLog('crit', message, data),
    error: (message, data = {}) => sendLog('error', message, data),
    warning: (message, data = {}) => sendLog('warning', message, data),
    notice: (message, data = {}) => sendLog('notice', message, data),
    info: (message, data = {}) => sendLog('info', message, data),
    debug: (message, data = {}) => sendLog('debug', message, data),
};

// A method to mimic console.trace, but without sending stack information
clientLogger.trace = (message) => {
    console.trace(message);  // Log trace in console for developer visibility
    sendLog('debug', message);  // Send message to server without stack
};

export default clientLogger;