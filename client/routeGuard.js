import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
let allowRouting = true;
let originalRouteFunc = null;
let originalRouteArgs = null;
let routePromptFunc = null;
function wrapRouting(original) {
    return (...args) => {
        if (allowRouting) {
            return original.apply(null, args);
        } else {
            originalRouteFunc = original;
            originalRouteArgs = args;
            if (routePromptFunc) {
                routePromptFunc();
            }
        }
    }
}

FlowRouter._page.redirect = wrapRouting(FlowRouter._page.redirect);
FlowRouter._page.show = wrapRouting(FlowRouter._page.show);