import { AvailableCustomizations } from '../../../lib/customizations';
import moment from 'moment-timezone';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal } from '../../app/main';
import { ReactiveVar } from 'meteor/reactive-var';
import { MomentUtils } from '../../../lib/util/momentUtils';

/**
 * Service for managing check-in forms and related operations.
 */
export class CheckInFormClientService {

    /**
     * @param {Object} parentData - Data from the parent component or context.
     * @param {boolean} parentData.closeModalOnSave - Whether to close the modal on save.
     * @param {string} parentData._id - ID of the person being checked in.
     * @param {boolean} parentData.hideSave - Whether to hide the save button.
     * @param {boolean} showReasons - Whether to show reasons for late check-in or early drop-off.
     */
    constructor(parentData, showReasons) {
        this.showAlert = new ReactiveVar(false);
        this.alertText = new ReactiveVar("");
        this.checkinPerson = new ReactiveVar(null);
        this.closeModalOnSave = parentData.closeModalOnSave;
        this.personId = parentData._id;
        this.hideSave = parentData.hideSave;
        this.showReasons = showReasons;
    }

    /**
     * Determines if the save button should be hidden.
     * @returns {string} "hidden" if the save button should be hidden, otherwise an empty string.
     */
    getHideSave() {
        return this.hideSave ? "hidden" : "";
    }

    /**
     * Gets the current state of the alert visibility.
     * @returns {boolean} True if the alert is visible, otherwise false.
     */
    getShowAlert() {
        return this.showAlert.get();
    }

    /**
     * Gets the text of the active alert.
     * @returns {string} The text of the active alert.
     */
    getActiveAlertText() {
        return this.alertText.get();
    }

    /**
     * Determines if the pay type can be selected.
     * @param {Object} org - The organization data.
     * @returns {boolean} True if the pay type can be selected, otherwise false.
     */
    canSelectPayType(org) {

        if (!org) {
            return false;
        }

        const person = this.checkinPerson.get();

        if (!person) {
            return false;
        }

        const orgHasCustomization = org.hasCustomization(AvailableCustomizations.PEOPLE_STAFF_PAY);
        const personIsStaffOrAdmin = person.type === 'staff' || person.type === 'admin';

        return orgHasCustomization && personIsStaffOrAdmin;
    }

    /**
     * Gets the custom pay types for the organization.
     * @param {Object} org - The organization data.
     * @returns {Array} The list of custom pay types.
     */
    getCustomPayTypes(org) {
        return org.getCustomPayTypes();
    }

    /**
     * Determines the selected pay type.
     * @param {string|null} selected - The currently selected pay type ID.
     * @param {string} id - The pay type ID to check.
     * @param {Object} org - The organization data.
     * @returns {string} "selected" if the pay type is selected, otherwise an empty string.
     */
    getSelectedPayType(selected, id, org) {
        const orgHasCustomization = org.hasCustomization(AvailableCustomizations.PEOPLE_CHECKIN_PREK_DEFAULT);
        if (!selected && orgHasCustomization) {
            // Default to PreK Standard if the person is a PreK staff
            if (this.isCurrentStaffPreK(org)) {
                const adpCode = this.getAdpCodeFromId(id, org);
                return adpCode === "PKR" ? "selected" : "";
            }
        }

        return selected === id ? "selected" : "";
    }

    /**
     * Gets the ADP code for a given pay type ID.
     * @param {string} id - The pay type ID.
     * @param {Object} org - The organization data.
     * @returns {string} The ADP code for the pay type, or an empty string if not found.
     */
    getAdpCodeFromId(id, org) {
        const payTypes = org.getCustomPayTypes();
        const payType = payTypes.find(payType => payType._id === id);
        return payType?.adpCode ?? "";
    }

    /**
     * Determines if the current staff member is a PreK staff.
     * @param {Object} org - The organization data.
     * @returns {boolean} True if the current staff member is PreK, otherwise false.
     */
    isCurrentStaffPreK(org) {
        const person = this.checkinPerson.get();
        const prefix = org.profileDataPrefix();
        if (prefix) {
            return person.profileData?.workDepartment?.toLowerCase() === 'prek';
        }
        return person.workDepartment?.toLowerCase() === 'prek';
    }

    /**
     * Retrieves the groups for the current user's organization.
     * @returns {Mongo.Cursor|null} A cursor for the groups, or null if no user is logged in.
     */
    groups() {
        const user = Meteor.user();

        if (!user) {
            return;
        }

        return Groups.find({
            orgId: user.orgId
        }, {sort: {"name": 1}});
    }

    /**
     * Determines if the check-in time can be changed.
     * @param {Object} org - The organization data.
     * @returns {boolean} True if the time can be changed, otherwise false.
     */
    allowChangeTime(org) {
        const person = this.checkinPerson.get();

        if (!person) {
            return false;
        }

        const isStaff = person.type === 'staff';
        const orgHasCustomization = org.hasCustomization(AvailableCustomizations.MOMENTS_CHECKIN_STAFF_LOCKDOWN);

        return !(isStaff && orgHasCustomization);
    }

    /**
     * Gets the current time in the organization's timezone.
     * @param {Object} org - The organization data.
     * @returns {string} The current time in "HH:mm" format.
     */
    currentTime(org) {
        const timezone = org.getTimezone();
        return new moment.tz(timezone).format("HH:mm");
    }

    /**
     * Retrieves the family members of the current person.
     * @returns {Array|null} The list of family members, or null if no person is set.
     */
    familyMembers() {
        const person = this.checkinPerson.get();

        if (!person) {
            return;
        }

        return person.findOwnedRelationships();
    }

    /**
     * Gets the available health check types for the organization.
     * @param {Object} org - The organization data.
     * @returns {Array|undefined} The list of health check types, or undefined if the customization is not enabled.
     */
    availableHealthCheckTypes(org) {
        if (org.hasCustomization(AvailableCustomizations.MOMENTS_CHECKIN_SHOW_HEALTH_CHECK)) {
            return org.availableCheckInHealthCheckTypes();
        }
    }

    /**
     * Handles family check-in and updates the session with check-in information.
     * @param {Object} org - The organization data.
     * @param {Object} session - The session object to update.
     * @returns {Object|undefined} The family check-in data, or undefined if conditions are not met.
     */
    familyCheckin(org, session) {
        if (!org) {
            return;
        }

        const person = this.checkinPerson.get();

        if (!person) {
            return;
        }

        const timezone = org.getTimezone();
        const orgHasCustomization = org.hasCustomization(AvailableCustomizations.PEOPLE_FAMILY_CHECKIN_ENABLED);
        const personHasFamilyCheckInToday = person.familyCheckIn?.checkInTime > new moment.tz(timezone).startOf("day").valueOf();

        if (orgHasCustomization && personHasFamilyCheckInToday) {
            const familyCheckInData = person.familyCheckIn;
            familyCheckInData.formFields = [];
            const pinCodeCheckinFields = org.pinCodeCheckinFields() || [];

            pinCodeCheckinFields.forEach(field => {
                familyCheckInData.formFields.push({
                    label: field.label,
                    value: person.familyCheckIn.formFieldData[field.dataId]
                });
            });

            session.set("familyCheckInInfo",familyCheckInData);

            Meteor.callAsync("getPeopleById", { _id: familyCheckInData.checkedInById }).then((person) => {
                familyCheckInData.byName = person.firstName + " " + person.lastName;
                session.set("familyCheckInInfo", familyCheckInData);
            }).catch((error) => {
                mpSwal.fire('Error', `Error fetching person by ID: ${error}`, 'error');
            });

            return session.get("familyCheckInInfo");
        }
    }


    /**
     * Closes the alert by resetting its state.
     */
    closeAlert() {
        this.showAlert.set(false);
        this.alertText.set("");
    }

    /**
     * Handles the group health check button click event.
     * @param {Event} event - The DOM event.
     */
    handleGroupHealthCheckButton(event) {
        const selected = $(event.currentTarget);
        $(selected).siblings().removeClass('active');
        $(selected).addClass("active");
    }

    /**
     * Saves the check-in data and handles UI updates.
     */
    checkinSave() {
        const closeModalOnSave = this.closeModalOnSave;
        $("#checkin-save").prop('disabled', true).prop("value", "Checking In...");

        const personId = this.personId;
        const currentUser = Meteor.user()?.fetchPerson();
        const pickerValue = moment($("#checkin-time").val(), ["h:mm a", "HH:mm"]);
        const checkInData = {
            personId: personId,
            groupId: $("#checkin-group-id").val(),
            groupName: $("#checkin-group-id option:selected").text(),
            comments: $("#checkin-comments").val(),
            time: pickerValue.valueOf(),
            prettyTime: pickerValue.format("h:mm a"),
            checkedInById: currentUser && currentUser._id,
        };

        const person = this.checkinPerson.get();

        const orgHasStaffPay = Orgs.current().hasCustomization(AvailableCustomizations.PEOPLE_STAFF_PAY);
        const orgHasShowTransportation = Orgs.current().hasCustomization(AvailableCustomizations.MOMENTS_CHECKIN_SHOW_TRANSPORTATION);
        const orgHasShowHealthCheck = Orgs.current().hasCustomization(AvailableCustomizations.MOMENTS_CHECKIN_SHOW_HEALTH_CHECK);

        const personIsStaffOrAdmin = person && (person.type === USER_TYPES.STAFF || person.type === USER_TYPES.ADMIN);

        if (orgHasStaffPay && personIsStaffOrAdmin) {
            checkInData.selectedPayTypeId = $("#checkin-paytype option:selected").val()
        }

        if (orgHasShowTransportation) {
            checkInData.transportation = $("#checkin-transportation").val();
        }

        if (orgHasShowHealthCheck) {
            const healthChecks = {};

            $(".btn-group-health-check label.active").each(function (i, b) {
                const healthType = $(b).data("healthtype");
                healthChecks[healthType] = $(b).data("healthlevel");
            });

            checkInData.healthChecks = healthChecks;

            checkInData.healthCheckCovid19Symptoms = $("#healthCheckCovid19Symptoms button.active").data("value");
            checkInData.healthCheckCovid19ProlongedContact = $("#healthCheckCovid19ProlongedContact button.active").data("value");
            checkInData.healthCheckCovid19TemperatureAboveLimit = $("#healthCheckCovid19TemperatureAboveLimit button.active").data("value");
            checkInData.healthCheckCovid19Temperature = $("#healthCheckCovid19Temperature").val();

            if (!checkInData.healthCheckCovid19Symptoms || !checkInData.healthCheckCovid19ProlongedContact ||
                !checkInData.healthCheckCovid19TemperatureAboveLimit) {
                $("#checkin-save").prop('disabled', false).prop("value", "Check In");
                this.alertText.set("Missing COVID-19 health check information");
                this.showAlert.set(true);
                return;
            }
        }

        // Throw error requiring input for this if customization enabled
        const selectedReason = $('#late-reason').val();
        try {
            MomentUtils.saveEarlyLateReason(checkInData, selectedReason, this.showReasons.get())
        } catch (e) {
			mpSwal.fire("Error", e.message, "error");
            $("#checkin-save").prop('disabled', false).prop("value", "Check In");
			return;
		}

        Meteor.callAsync('checkIn', checkInData, false)
            .then(res => {
                $("#checkin-save").prop('disabled', false).prop("value", "Check In");
                mpSwal.fire("Success", "Check In Saved", "success");
                if (closeModalOnSave) {
                    hideModal(closeModalOnSave);
                }
            })
            .catch(error => {
                $("#checkin-save").prop('disabled', false).prop("value", "Check In");
                this.alertText.set(error.reason);
                this.showAlert.set(true);
            });
    }
}