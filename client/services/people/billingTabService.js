import { People } from '../../../lib/collections/people';
import { Org } from '../../../lib/collections/orgs';
import { AvailableCustomizations } from '../../../lib/customizations';
import { Meteor } from 'meteor/meteor';

/**
 * Service class for managing billing tab actions.
 */
export class BillingTabService {
    /**
     * Generate a manual invoice for a person.
     *
     * @param { string } personId - The _id of the person (child).
     * @param { string } invoiceDueDate - The due date for the invoice in MM/DD/YYYY format.
     * @param { Org } org - The organization document.
     */
    static async generateManualInvoice(personId, invoiceDueDate, org) {
        const child =  People.findOne({_id: personId});
        const orgHasRegistrationFlow = org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
        const pendingItemCharges = child?.billing?.pendingCharges;

        if (pendingItemCharges && pendingItemCharges.length && orgHasRegistrationFlow) {
            Meteor.callAsync('generateItemSchedulesForChild', pendingItemCharges, personId).then((schedules) => {
                const allReservationIds = schedules.map(s => s.reservationId);
                Meteor.callAsync("generateManualInvoice", { personId, invoiceDueDate }, null, false, allReservationIds).then(() => {
                }).catch((error) => {
                    mpSwal.fire(error.error || "Error", error.reason || error.message, "error");
                });
            }).catch((error) => {
                mpSwal.fire(error.error || "Error", error.reason || error.message, "error");
            })
        } else {
            Meteor.callAsync("generateManualInvoice", { personId, invoiceDueDate }).then(() => {
            }).catch((error) => {
                mpSwal.fire(error.error || "Error", error.reason || error.message, "error");
            });
        }
    }
}