import { Meteor } from 'meteor/meteor';
import { AvailableCustomizations } from '../../../lib/customizations';
import { BillingUtils } from '../../../lib/util/billingUtils';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { AvailableActionTypes, AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import { UserPermissionService } from '../../../lib/userPermissionService';
import '../../app/billing/_billingPayment';
import { showModal } from '../../app/main';
import { People } from '../../../lib/collections/people';
import '../../app/simpleModal/simpleModal';

/**
 * Service class for managing payment information actions for a person.
 */
export class PersonPaymentInfoClientService {
    /**
     * Creates an instance of PersonPaymentInfoClientService.
     *
     * @param {Object} org - The organization object.
     * @param {string} personId - The ID of the person for whom payment actions are managed.
     */
    constructor(org, personId) {
        this.org = org;
        this.personId = personId;
    }

    /**
     * Gets the current organization.
     *
     * @return {Object}
     */
    getCurrentOrg() {
        return this.org;
    }

    /**
     * Determines if the service charge notice should be shown.
     *
     * @returns {boolean} True if passthrough fees and billing platform fees customization are enabled.
     */
    showServiceChargeNotice() {
        const passthroughFees =
            this.org.billing?.passthroughFees &&
            this.org.hasCustomization(AvailableCustomizations.BILLING_PLATFORM_FEES);
        return !!passthroughFees;
    }

    /**
     * Checks if ACH (bank account payments) are disabled.
     *
     * @returns {boolean} True if the organization has the DISABLE_ACH customization.
     */
    preventAch() {
        return this.org.hasCustomization(AvailableCustomizations.DISABLE_ACH);
    }

    /**
     * Checks if credit card payments are disabled.
     *
     * @returns {boolean} True if the organization has the DISABLE_CREDIT_CARDS customization.
     */
    preventCreditCard() {
        return this.org.hasCustomization(AvailableCustomizations.DISABLE_CREDIT_CARDS);
    }

    /**
     * Determines if AutoPay is allowed for the organization.
     *
     * @returns {boolean} True if AutoPay is allowed, false otherwise.
     */
    allowAutoPay() {
        return !this.org.hasCustomization(AvailableCustomizations.QUEUE_AUTO_PAYMENTS);
    }

    /**
     * Checks if AutoPay is required for the organization.
     *
     * @returns {boolean} True if AutoPay enrollment is required, false otherwise.
     */
    autoPayRequired() {
        return this.org.billing?.autopayEnrollmentRequired || false;
    }

    /**
     * Checks if the current user has admin or super admin privileges.
     *
     * @returns {boolean} True if the user is an admin or super admin, false otherwise.
     */
    isAdmin() {
        const userPerson = Meteor.user()?.fetchPerson();

        if (!userPerson) {
            return false;
        }

        const isSuperAdmin = userPerson.superAdmin ?? false;
        const isAdminType = userPerson.type === USER_TYPES.ADMIN;
        return isSuperAdmin || isAdminType;
    }

    hasUpdatePermission() {
        const userPerson = Meteor.user()?.fetchPerson();

        if (!userPerson) {
            return false;
        }

        return UserPermissionService.processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_AUTO_PAY_UPDATE, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN,
            currentPerson: userPerson,
            currentOrg: this.org,
        });
    }

    /**
     * Opens the modal to add or replace a credit card.
     */
    handleAddOrReplaceCreditCard() {
        showModal('simpleModal', {
            title: 'Add/Replace Credit Card',
            data: { providerAdyen: true },
            template: 'billingPaymentCreditCard',
            onRendered: () => this.mountAdyenCardComponent(),
            onSave: this.saveCreditCard.bind(this),
        });
    }

    /**
     * Mounts the Adyen secured fields card component to the DOM.
     */
    mountAdyenCardComponent() {
        const instance = Template.instance();
        instance.adyenIsValid = false;

        const configuration = {
            locale: 'en_US',
            environment: this.org.billingCardProviderShared().getEnvironment() || 'test',
            clientKey: this.org.billingCardProviderShared().getClientKey(),
        };

        const checkout = new AdyenCheckout(configuration);
        const customCard = checkout.create('securedfields', {
            onChange: (state) => {
                instance.adyenState = state.data;
                instance.adyenIsValid = state.isValid;
            },
        });
        customCard.mount('#customCard-container');
    }

    /**
     * Handles saving a credit card payment method.
     *
     * @param {Event} saveEvent - The save button click event.
     * @param {Object} instance - The template instance containing Adyen state.
     */
    saveCreditCard(saveEvent, instance) {
        if (!instance.adyenIsValid || !instance.adyenState) {
            $(saveEvent.target).html('Save').prop('disabled', false);
            return mpSwal.fire('Error', 'You are missing some required details.', 'error');
        }

        Meteor.callAsync('adyenAddPaymentMethod', {
            sourceType: 'card',
            adyenComponentState: instance.adyenState,
            personId: this.personId,
        })
            .then(() => $('#simpleModal').modal('hide'))
            .catch((error) => {
                $(saveEvent.target).html('Save').prop('disabled', false);
                mpSwal.fire('Error', error.reason, 'error');
            });
    }

    /**
     * Opens the modal to add or replace a bank account.
     */
    handleAddOrReplaceBankAccount() {
        showModal('simpleModal', {
            title: 'Add/Replace Bank Account',
            template: 'billingPaymentBankAccount',
            data: { providerAdyen: true },
            onRendered: () => this.mountAdyenAchComponent(),
            onSave: this.saveBankAccount.bind(this),
        });
    }

    /**
     * Mounts the Adyen ACH payment component to the DOM.
     */
    mountAdyenAchComponent() {
        const instance = Template.instance();
        const configuration = {
            locale: 'en_US',
            environment: this.org.billingCardProviderShared().getEnvironment() || 'test',
            clientKey: this.org.billingCardProviderShared().getClientKey(),
            onChange: (state) => {
                instance.adyenState = state.data;
                instance.adyenIsValid = state.isValid;
            }
        };

        const checkout = new AdyenCheckout(configuration);
        instance.adyenAch = checkout.create('ach').mount('#ach-component-container');
    }

    /**
     * Handles saving a bank account payment method.
     *
     * @param {Event} saveEvent - The save button click event.
     * @param {Object} instance - The template instance containing Adyen state.
     * @param {Object} formFieldData - The form data from the bank account modal.
     */
    saveBankAccount(saveEvent, instance, formFieldData) {
        if (!formFieldData['is-checking']) {
            $(saveEvent.target).html('Submit').prop('disabled', false);
            return mpSwal.fire('Error', 'Please confirm your account is a US-based checking account.', 'error');
        }

        if (formFieldData.account_number !== formFieldData.account_number_verify) {
            $(saveEvent.target).html('Submit').prop('disabled', false);
            return mpSwal.fire('Error', 'Account numbers do not match.', 'error');
        }

        if (!instance.adyenIsValid || !instance.adyenState) {
            $(saveEvent.target).html('Save').prop('disabled', false);
            return mpSwal.fire('Error', 'You are missing some required details.', 'error');
        }

        Meteor.callAsync('adyenAddPaymentMethod', {
            sourceType: 'bank_account',
            adyenComponentState: instance.adyenState,
            personId: this.personId,
        })
            .then(() => $('#simpleModal').modal('hide'))
            .catch((error) => {
                $(saveEvent.target).html('Save').prop('disabled', false);
                mpSwal.fire('Error', error.reason, 'error');
            });
    }

    /**
     * Removes a payment method and AutoPay if it is configured.
     *
     * @param {string} paymentType - The type of payment method ('card' or 'bank_account').
     * @param {string} personId - The ID of the person.
     * @param {boolean} isAutoPay - Whether the payment method is set as AutoPay.
     */
    removeSourceAndAutopay(paymentType, personId, isAutoPay) {
        Meteor.callAsync('removeCustomerSource', { type: paymentType, personId })
            .catch((error) => mpSwal.fire('Error', error.reason, 'error'));

        if (isAutoPay) {
            Meteor.callAsync('removeAutoPay', { personId })
                .catch((error) => mpSwal.fire('Error', error.reason, 'error'));
        }
    }

    /**
     * Handles the removal of a payment option after confirming through a modal.
     *
     * @param {string} paymentType - The type of payment method to remove.
     */
    async handleRemovePaymentOption(paymentType) {
        const person = await People.findOneAsync(this.personId);
        const isAutoPay = person.autoPayMethod() === paymentType;
        const modalObject = BillingUtils.removePaymentMethodModalObject(isAutoPay, paymentType);

        mpSwal.fire(modalObject).then((result) => {
            if (result.value) {
                this.removeSourceAndAutopay(paymentType, this.personId, isAutoPay);
            }
        });
    }

    /**
     * Opens the modal to configure AutoPay settings.
     */
    async handleAddAutoPay() {
        const person = await People.findOneAsync(this.personId);
        const currentOrg = this.org;

        showModal('simpleModal', {
            title: 'Configure AutoPay',
            template: 'billingConfigureAutoPay',
            data: {
                disableBankAccount:
                    !person.connectedBankAccount() || person.connectedBankAccount().status !== 'verified',
                disableCreditCard: !person.connectedCreditCard(),
                showServiceChargeNotice: currentOrg.billing?.passthroughFees,
                currentOrg,
            },
            onSave: (saveEvent, instance, formFieldData) => {
                formFieldData.personId = this.personId;
                Meteor.callAsync('configureAutoPay', formFieldData)
                    .then(() => $('#simpleModal').modal('hide'))
                    .catch((error) => {
                        $(saveEvent.target).html('Submit').prop('disabled', false);
                        mpSwal.fire('Error', error.reason, 'error');
                    });
            },
        });
    }

    /**
     * Handles disabling AutoPay after confirmation.
     */
    handleDisableAutoPay() {
        mpSwal
            .fire({
                title: 'Are you sure?',
                text: 'This will disable automatic payment on your account.',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, disable AutoPay',
            })
            .then((result) => {
                if (result.value) {
                    Meteor.callAsync('removeAutoPay', { personId: this.personId })
                        .catch((error) => mpSwal.fire('Error', error.reason, 'error'));
                }
            });
    }
}