import { Meteor } from 'meteor/meteor';
import { OrgsLib } from '../../../../../lib/orgsLib';
import { AvailableCustomizations } from '../../../../../lib/customizations';
import moment from 'moment-timezone';
import { ReactiveVar } from 'meteor/reactive-var';
import { HOLIDAY_SCHEDULE_TYPES } from '../../../../../lib/constants/holidayConstants';

/**
 * Represents a widget for managing and displaying schedule enrollments.
 */
export class ScheduleEnrollmentsWidget {

    /**
     * Creates a new instance of ScheduleEnrollmentsWidget.
     *
     * @param {Object} org - The organization object.
     * @param {string} org.timezone - The timezone of the organization.
     */
    constructor(org) {
        this.isLoading = new ReactiveVar(false);
        this.widgetData = new ReactiveVar();
        this.isContentLoading = new ReactiveVar(false);
        this.org = org;
        this.timezone = org.getTimezone();
        this.noOfScheduleTypes = 5;
        this.showMoreClicked = new ReactiveVar(false);
        this.rangeFilter = new ReactiveVar({
            startDate: null,
            endDate: null
        });
    }

    /**
     * Gets the week days based on whether weekends are enabled for the organization.
     *
     * @returns {string[]} An array of abbreviated day names.
     */
    getWeekDays() {
        const hasWeekends = OrgsLib.orgHasCustomization(this.org, AvailableCustomizations.WEEKENDS_ENABLED);

        if (hasWeekends) {
            return ['Su','M', 'T', 'W', 'R', 'F','Sa'];
        } else {
            return ['M', 'T', 'W', 'R', 'F'];
        }
    }

    /**
     * Gets the schedule types to display in the widget.
     *
     * @returns {Array<Object>|undefined} An array of schedule types or undefined if no data is available.
     */
    getScheduleTypes() {
        const widgetData = this.widgetData.get();
        if (widgetData) {
            this.rangeFilter.set({
                startDate: widgetData.rangeStart,
                endDate: widgetData.rangeEnd,
            });

            if (!this.showMoreClicked.get()) {
                const scheduleTypes = widgetData.scheduleTypes;
                return scheduleTypes.slice(0, this.noOfScheduleTypes);
            } else {
                return widgetData.scheduleTypes;
            }
        }
    }

    /**
     * Determines whether a day label should be displayed.
     *
     * @param {string} day - The abbreviated day name.
     * @returns {boolean} True if the day label should be displayed; otherwise, false.
     */
    showScheduleTypeDaysLabel(day) {
        const days = this.getWeekDays();
        return days.includes(day);
    }

    /**
     * Gets the loading state of the widget.
     *
     * @returns {boolean} True if the widget is loading; otherwise, false.
     */
    getIsLoading() {
        return this.isLoading.get();
    }

    /**
     * Gets the content loading state of the widget.
     *
     * @returns {boolean} True if the widget content is loading; otherwise, false.
     */
    getIsContentLoading() {
        return this.isContentLoading.get();
    }

    /**
     * Determines whether a warning should be shown based on enrollment capacity.
     *
     * @param {number|string} coming - The number of people coming.
     * @param {number|string} cap - The capacity.
     * @returns {string} A CSS class indicating the warning status.
     */
    getShowWarning(coming, cap) {
        return parseInt(coming) > parseInt(cap) ? 'text-danger' : '';
    }

    /**
     * Retrieves the range filter as a formatted string.
     *
     * @returns {string} The formatted range filter.
     */
    getRangeFilter() {
        const rangeFilter = this.rangeFilter.get();
        return ` ${rangeFilter.startDate} - ${rangeFilter.endDate} `;
    }

    /**
     * Determines whether the "show more" button should be displayed.
     *
     * @returns {boolean} True if the "show more" button should be displayed; otherwise, false.
     */
    showMore() {
        if (this.widgetData.get() && !this.showMoreClicked.get()) {
            return (this.noOfScheduleTypes < this.widgetData.get().scheduleTypes.length);
        }
        return false;
    }

    /**
     * Initializes the date picker for selecting enrollment date ranges.
     */
    initializeDatePicker() {
        const enableSundays = (date) => {
            const day = date.getDay();
            return day === 0; // Enable Sundays only
        };

        $("#btnFilter").datepicker({
            zIndexOffset: 9999,
            autoclose: true,
            beforeShowDay: enableSundays,
        }).on("changeDate", async (event) => {
            this.showMoreClicked.set(false);
            this.isContentLoading.set(true);
            const newDate = new moment.tz(event.format(), "MM/DD/YYYY", this.timezone);
            const rangeStart = newDate.startOf("week").format("MM/DD/YYYY");
            const rangeEnd = newDate.endOf("week").format("MM/DD/YYYY");

            try {
                const result = await Meteor.callAsync("rangedEnrollmentData", { rangeStart, rangeEnd });
                this.widgetData.set(result);
            } catch (error) {
                mpSwal.fire("Error", error.reason || error.message, "error");
            }

            this.isContentLoading.set(false);
        });
    }

    getHolidayInfo(abbr) {
        const widgetData = this.widgetData.get();

        if (!widgetData?.holidaysByDay) {
            return null;
        }

        const holiday = widgetData.holidaysByDay[abbr];

        if (!holiday) {
            return null;
        }

        // Convert schedule type IDs to friendly names
        const scheduleTypes = widgetData.scheduleTypes || [];
        const idToTitle = scheduleTypes.reduce((acc, st) => {
            acc[st.id] = st.title;
            return acc;
        }, {});

        const permittedScheduleTypeNames =
            holiday.permittedScheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.NONE) || holiday.permittedScheduleTypes.length === 0
                ? []
                : holiday.permittedScheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.ALL)
                    ? scheduleTypes.map(st => st.title)
                    : holiday.permittedScheduleTypes.map(id => idToTitle[id] || id);

        return {
            name: holiday.name,
            permittedScheduleTypeNames
        };
    }
}