import { Meteor } from 'meteor/meteor';
import { AvailableActionTypes, AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import { AvailableCustomizations } from '../../../lib/customizations';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { AllocationTypes } from '../../../lib/discountTypes';
import { OrgsLib } from '../../../lib/orgsLib';
import { InvoiceUpdateService } from '../../../lib/invoiceUpdateService';
import moment from "moment-timezone";
import { BillingHelpers } from '../../app/billing/_billingHelpers';
import { Invoices } from '../../../lib/collections/invoices';
import { Relationships } from '../../../lib/collections/relationships';
import { processPermissions } from '../../../lib/permissions';
import { ReactiveVar } from 'meteor/reactive-var';
import { showModal } from '../../app/main';
import _ from '../../../lib/util/underscore';

/**
 * Client service for managing invoice details.
 */
export class InvoiceDetailClientService {

    /**
     * Constructor for InvoiceDetailClientService.
     * @param {Object} invoice - The invoice object.
     * @param {Object} invoicePerson - The person associated with the invoice.
     */
    constructor(invoice, invoicePerson, currentOrg) {
        this.invoice = new ReactiveVar(invoice);
        this.openBalances = new ReactiveVar([]);
        this.invoicePerson = invoicePerson;
        this.currentOrg = currentOrg;
    }

    /**
     * Creates an instance of InvoiceDetailClientService.
     * @param {string} invoiceId - The ID of the invoice to load.
     * @returns {Promise<InvoiceDetailClientService>} - A new instance of InvoiceDetailClientService.
     * @throws {Error} - If there is an issue during creation.
     */
    static async create(invoiceId, currentOrg) {
        try {
            const invoice = await this.getCurrentInvoice(invoiceId);
            const invoicePerson = invoice ? await this.getPersonById(invoice.personId) : null;
            return new InvoiceDetailClientService(invoice, invoicePerson, currentOrg);
        } catch (e) {
            throw new Error(`Error in InvoiceDetailClientService.create: ${e.message || e.reason}`);
        }
    }

    /**
     * Fetches the current invoice by ID.
     * @param {string} invoiceId - The ID of the invoice.
     * @returns {Promise<Object|null>} - The invoice object or null if not found.
     * @throws {Error} - If the invoice cannot be retrieved.
     */
    static async getCurrentInvoice(invoiceId) {
        if (!invoiceId) {
            return null;
        }

        try {
            return await Invoices.findOneAsync({ _id: invoiceId });
        } catch (e) {
            throw new Error(`Error in InvoiceDetailClientService.getCurrentInvoice: ${e.message || e.reason}`);
        }
    }

    /**
     * Fetches a person by their ID.
     * @param {string} id - The ID of the person.
     * @returns {Promise<Object|null>} - The person object or null if not found.
     * @throws {Error} - If the person cannot be retrieved.
     */
    static async getPersonById(id) {
        if (!id) {
            return null;
        }

        try {
            return await Meteor.callAsync('getPeopleById', {_id: id });
        } catch (e) {
            throw new Error(`Error in InvoiceDetailClientService.getPersonById: ${e.message || e.reason}`);
        }
    }

    /**
     * Retrieves the current invoice data.
     * @returns {Object} - The current invoice object.
     */
    invoiceData() {
        return this.invoice.get();
    }

    /**
     * Retrieves the person associated with the invoice.
     * @returns {Object|null} - The person object or null if not found.
     */
    targetPerson() {
        const invoice = this.invoice.get();

        if (!invoice) {
            return null;
        }

        if (invoice.personId) {
            return this.invoicePerson;
        }
    }

    /**
     * Determines if the given line item can be voided.
     * @param {Object} lineItemDetail - The line item detail object.
     * @returns {boolean} - Whether the line item can be voided.
     */
    showVoid(lineItemDetail) {
        const userHasPermissions = processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_PAYMENTS_VOID, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });

        const typeIsVoidable = lineItemDetail.type === "manual_payment" || lineItemDetail.payment_type === "credit_memo" || lineItemDetail.type === "reimbursable" || lineItemDetail.type === "other" || lineItemDetail.type === "payroll_deduction"
        const isNotVoidedRefundedAdjustedOrUnapplied = !lineItemDetail.voidedReason && !lineItemDetail.voidedAt && !lineItemDetail.refundedAt && !lineItemDetail.adjustments && !lineItemDetail.description?.includes('Unapplied Cash');
        return userHasPermissions && typeIsVoidable && isNotVoidedRefundedAdjustedOrUnapplied;
    }

    /**
     * Determines if a manual refund can be issued for the given line item.
     * @param {Object} lineItemDetail - The line item detail object.
     * @returns {boolean} - Whether a manual refund can be issued.
     */
    showManualRefund(lineItemDetail) {
        const userHasPermissions = processPermissions({
            assertions: [{context: AvailablePermissions.BILLING_PAYMENTS_REFUND, action: AvailableActionTypes.EDIT}],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
        const isManualType = lineItemDetail.type === "manual_payment";
        const isNotVoidedRefundedOrZeroAmount = !lineItemDetail.voidedReason && !lineItemDetail.voidedAt && !lineItemDetail.refundedAt && lineItemDetail.creditAmount !== 0;
        return userHasPermissions && isManualType && isNotVoidedRefundedOrZeroAmount;
    }

    /**
     * Determines if the given line item can be reversed.
     * @param {Object} lineItemDetail - The line item detail object.
     * @returns {boolean} - Whether the line item can be reversed.
     */
    showReverse(lineItemDetail) {
        const userHasPermissions = processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_INVOICES, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });

        const validTypes = ['bad_debt', 'agency_write_off', 'reallocation_to_payer', 'reallocation_to_family', 'reallocation_to_collections', 'reallocation_to_bad-debt', 'reallocation_to_agency', 'collections_write_off'];
        const isValidType = validTypes.includes(lineItemDetail.type);
        const isNotReversed = !lineItemDetail.reversedAt;

        return userHasPermissions && isValidType && isNotReversed;
    }

    /**
     * Determines if the given line item can be modified.
     * @param {Object} currentOrg - The current organization object.
     * @param {Object} lineItemDetail - The line item detail object.
     * @returns {boolean} - Whether the line item can be modified.
     */
    showModify(currentOrg, lineItemDetail) {
        if (!currentOrg) {
            return false;
        }

        const orgAllowsModification = !currentOrg.hasCustomization(AvailableCustomizations.DISABLE_INVOICE_MODIFICATION);
        const userHasPermissions = processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_OTHER_CREDITS_MODIFY, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
        const isOtherType = lineItemDetail.type === "other";
        const isNotVoided = !lineItemDetail.voidedReason;
        const isNotAdjustedAndZeroed = !(lineItemDetail.adjustments && lineItemDetail.creditAmount === 0);

        return orgAllowsModification && userHasPermissions && isOtherType && isNotVoided && isNotAdjustedAndZeroed;
    }

    /**
     * Determines if the given line item can be adjusted.
     * @param {Object} lineItemDetail - The line item detail object.
     * @returns {boolean} - Whether the line item can be adjusted.
     */
    showAdjust(lineItemDetail) {
        const validTypes = ["manual_payment", "credit_memo", "reimbursable", "other", "payroll_deduction"];
        const hasPermission = processPermissions({
            assertions: [
                { context: AvailablePermissions.BILLING_PAYMENTS_VOID, action: AvailableActionTypes.EDIT },
                { context: AvailablePermissions.BILLING_PAYMENTS_REFUND, action: AvailableActionTypes.EDIT },
            ],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });

        const isAdjustable = validTypes.includes(lineItemDetail.type) || validTypes.includes(lineItemDetail.payment_type);
        const isVoidedOrRefunded = !lineItemDetail.voidedReason && !lineItemDetail.voidedAt && !lineItemDetail.refundedAt && !lineItemDetail.reversedAt;

        return hasPermission && isAdjustable && isVoidedOrRefunded && lineItemDetail.creditAmount > 0;
    }

    /**
     * Determines if the invoice can be re-sent.
     * @returns {boolean} - Whether the invoice can be re-sent.
     */
    showResendInvoice() {
        return processPermissions({
            assertions: [
                { context: AvailablePermissions.BILLING_INVOICES, action: AvailableActionTypes.EDIT },
                { context: AvailablePermissions.BILLING_INVOICES_RESEND, action: AvailableActionTypes.EDIT }
            ],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
    }

    /**
     * Determines if payers can be reallocated for the given discount.
     * @param {Object} payerDiscount - The payer discount object.
     * @returns {boolean} - Whether payers can be reallocated.
     */
    showReallocatePayers(payerDiscount) {
        if (!this.invoice.get()) {
            return false;
        }

        const userHasPermissions = processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_INVOICES_MODIFY, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });

        const discountIsReimbursable = payerDiscount.type === AllocationTypes.SUBSIDY;
        const isNotVoided = !payerDiscount.voidedAt;

        return userHasPermissions && discountIsReimbursable && isNotVoided;
    }

    /**
     * Determines if the invoice can be credited.
     * @returns {boolean} - Whether the invoice can be credited.
     */
    showCreditInvoice() {
        return processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_PAYMENTS, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
    }

    /**
     * Determines if the invoice notes can be edited.
     * @returns {boolean} - Whether the invoice notes can be edited.
     */
    showEditInvoiceNotes() {
        return processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_INVOICES, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
    }

    /**
     * Determines if the invoice can be voided.
     * @returns {boolean} - Whether the invoice can be voided.
     */
    showVoidInvoice() {
        return processPermissions({
            assertions: [
                { context: AvailablePermissions.BILLING_INVOICES, action: AvailableActionTypes.EDIT },
                { context: AvailablePermissions.BILLING_INVOICES_VOID, action:AvailableActionTypes.EDIT}
            ],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
    }

    /**
     * Determines if a refund can be issued for the invoice.
     * @returns {boolean} - Whether a refund can be issued.
     */
    showIssueRefund() {
        return processPermissions({
            assertions: [
                { context: AvailablePermissions.BILLING_PAYMENTS, action: AvailableActionTypes.EDIT  },
                {context: AvailablePermissions.BILLING_PAYMENTS_REFUND, action: AvailableActionTypes.EDIT }
            ],
            evaluator: (person) =>person.type === USER_TYPES.ADMIN
        });
    }

    /**
     * Determines if a discount can be added to the invoice.
     * @param {Object} currentOrg - The current organization object.
     * @returns {boolean} - Whether a discount can be added.
     */
    showAddDiscount(currentOrg) {
        if (!currentOrg) {
            return false;
        }

        const orgAllowsModification = !currentOrg.hasCustomization(AvailableCustomizations.DISABLE_INVOICE_MODIFICATION);
        const userHasPermissions = processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_INVOICES_MODIFY, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });

        return orgAllowsModification && userHasPermissions;
    }

    /**
     * Determines if a specific discount can be modified.
     * @param {Object} currentOrg - The current organization object.
     * @param {Object} discount - The discount object.
     * @returns {boolean} - Whether the discount can be modified.
     */
    showModifyDiscount(currentOrg, discount) {
        const invoice = this.invoice.get();

        if (!invoice || !currentOrg) {
            return false;
        }

        const orgAllowsModification = !currentOrg.hasCustomization(AvailableCustomizations.DISABLE_INVOICE_MODIFICATION);
        const userHasPermissions = processPermissions({
            assertions: [{ context: AvailablePermissions.BILLING_INVOICES_MODIFY, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type === USER_TYPES.ADMIN
        });
        const invoiceHasOpenAmounts = invoice.openAmount > 0;
        const discountIsReimbursable = discount.type === AllocationTypes.SUBSIDY;
        const discountIsNotVoided = !discount.voidedAt;

        return orgAllowsModification && userHasPermissions && (invoiceHasOpenAmounts || discountIsReimbursable) && discountIsNotVoided;
    }

    /**
     * Retrieves the open balances for the invoice.
     * @returns {Array<Object>} - An array of open balance objects.
     */
    getOpenBalances() {
        return this.openBalances.get();
    }

    /**
     * Initializes the open balances for the invoice.
     * @param {Object} currentOrg - The current organization object.
     * @returns {Promise<void>} - Resolves when initialization is complete.
     */
    async initOpenBalances() {
        const invoice = this.invoice.get();
        const person = this.invoicePerson;
        const currentOrg = this.currentOrg;

        if (!invoice || !person || !currentOrg) {
            return;
        }

        this.openBalances.set([]); // reset if already set.

        const pushBalanceRow = (row) => {
            const rows = this.openBalances.get();
            rows.push(row);
            this.openBalances.set(rows);
        }

        const familySplits = invoice.getFamilySplits();
        if (familySplits) {
            Object.entries(familySplits).forEach(([familyPersonId, amount]) => {
                if (amount > 0) {
                    Meteor.callAsync("getPeopleByQueryAndOptions",
                        { _id: familyPersonId },
                        {
                            "fields": { _id: 1, type: 1, lastName: 1, firstName: 1 }
                        })
                        .then((resPeopleList) => {
                            const familyPerson = resPeopleList.length && resPeopleList[0]
                            pushBalanceRow({
                                source: familyPerson.firstName + " " + familyPerson.lastName + " (" + amount + "%)",
                                paidAmount: invoice.amountPaidByFamilyMember(familyPersonId),
                                openAmount: invoice.amountDueForFamilyMember(familyPersonId)
                            })
                        })
                        .catch((error) => {
                            mpSwal.fire("Error", `Error while fetching invoiced person - ${error}`, "error");
                        });
                }
            });
        } else {
            const aFamilyMember = await Relationships.findOneAsync({targetId: person._id, relationshipType:"family"});

            if (aFamilyMember) {
                pushBalanceRow({
                    source: "Family",
                    allocatedAmount: invoice.originalAmount,
                    paidAmount: aFamilyMember && invoice.amountPaidByFamilyMember(aFamilyMember._id),
                    openAmount: invoice.openAmount
                });
            }
        }

        if (invoice.openPayerAmounts) {
            Object.entries(invoice.openPayerAmounts).forEach(([payerSource, payerAmount]) => {
                const availableOrgPayerSources =  OrgsLib.availablePayerSources(currentOrg, true);
                const source = availableOrgPayerSources.find(orgSource => orgSource.type === payerSource);
                const paidAmount = (invoice.credits || []).reduce((memo, credit) => {
                    if (
                        credit.creditReason === AllocationTypes.SUBSIDY &&
                        credit.creditPayerSource === payerSource
                    ) {
                        return memo + credit.amount;
                    }
                    return memo;
                }, 0);

                const openAmount = payerAmount - paidAmount;

                if (source) {
                    pushBalanceRow({
                        source: "Reimbursable: " + source.description,
                        allocatedAmount: payerAmount,
                        paidAmount,
                        openAmount
                    })
                }
            });
        }
    }

    /**
     * Calculates the invoiced amount minus discounts.
     * @param {Object} invoice - The invoice object.
     * @returns {number} - The invoiced amount minus discounts.
     */
    getInvoicedAmountMinusDiscounts(invoice) {
        let totalAmount = invoice.originalAmount;

        for (const lineItem of invoice.lineItems) {
            if (!lineItem.appliedDiscounts?.length) {
                continue;
            }

            for (const appliedDiscount of lineItem.appliedDiscounts) {
                const { type, amount } = appliedDiscount;
                if (type === AllocationTypes.SUBSIDY || type === AllocationTypes.COPAY) {
                    totalAmount += amount;
                }
            }
        }

        return totalAmount;
    }

    /**
     * Updates the current invoice with fresh data.
     * @returns {Promise<void>} - Resolves when the invoice is updated.
     */
    async updateCurrentInvoice() {
        const invoiceId = this.invoice.get()._id;
        const updatedInvoice = await InvoiceDetailClientService.getCurrentInvoice(invoiceId);

        this.invoice.set(updatedInvoice);
        await this.initOpenBalances();
    }

    /**
     * Handles modifying a discount on the invoice.
     * @param {Event} event - The event triggered by the UI.
     */
    invoiceModifyDiscount(event) {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        const lineItemId = $(event.currentTarget).data("id");
        const lineItem = _.find(invoice.lineItems, (li) => li._id === lineItemId);
        const discountId = $(event.currentTarget).data("index");

        const currentDiscount = lineItem.appliedDiscounts[discountId];
        const currentLabel = currentDiscount.originalAllocation.allocationDescription;
        const currentAmount = currentDiscount.amount;

        let mostRecentModificationAmount = 0;

        const modificationHistory = currentDiscount.modificationHistory || [];
        if (modificationHistory.length > 0) {
            mostRecentModificationAmount = modificationHistory[modificationHistory.length - 1].newAmount;
        }

        showModal("simpleModal", {
            title: "Modify Discount/Reimbursement",
            template: "billingModifyDiscountModal",
            actionButtonLabel: "Save",
            data: {
                currentLabel,
                currentAmount
            },
            onSave: (e, i, formFieldData) => {
                formFieldData.invoiceId = invoiceId;
                formFieldData.lineItemId = lineItemId;
                formFieldData.discountId = discountId;
                formFieldData.originalAmount = currentAmount.toFixed(2).toString();

                if (
                    parseFloat(formFieldData.newAmount) !== parseFloat(formFieldData.originalAmount) &&
                    !isNaN(parseFloat(formFieldData.newAmount)) &&
                    !isNaN(parseFloat(formFieldData.originalAmount))
                ) {
                    const newAmount = parseFloat(formFieldData.newAmount);
                    formFieldData.modifiedDiscount = InvoiceUpdateService.calculateModfiedDiscountAmount(newAmount, currentDiscount, mostRecentModificationAmount);
                }

                Meteor.callAsync("modifyDiscount", formFieldData)
                    .then(async (result) => {
                        await this.updateCurrentInvoice();
                        $("#simpleModal").modal("hide");
                    })
                    .catch((error) => {
                        $(e.target).html('Save').prop("disabled", false);
                        mpSwal.fire("Error", error.reason || error.message, "error");
                    });
            }
        });
    }

    /**
     * Handles voiding a discount on the invoice.
     * @param {Event} event - The event triggered by the UI.
     */
    invoiceVoidDiscount(event) {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        const lineItemId = $(event.currentTarget).data("id");
        const discountId = $(event.currentTarget).data("index");

        mpSwal.fire({
            title: "Are you sure?",
            text: "This will void the current discount and modify the open amount of the invoice.",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, void discount"
        }).then((result) => {
            if (result.value) {
                Meteor.callAsync("modifyDiscount", {
                    invoiceId,
                    lineItemId,
                    discountId,
                    void: true
                }).then(async (result) => {
                    await this.updateCurrentInvoice();
                }).catch((error) => {
                        $(event.target).html('Save').prop("disabled", false);
                        mpSwal.fire("Error", error.reason || error.message, "error");
                    });
            }
        });
    }

    /**
     * Handles adding a discount to the invoice.
     * @param {Event} event - The event triggered by the UI.
     * @param {Object} currentOrg - The current organization object.
     */
    invoiceAddDiscount(event, currentOrg) {
        if (!currentOrg) {
            mpSwal.fire("Error", "No organization found.", "error");
        }

        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        const lineItemId = $(event.currentTarget).data("id");
        const discountType = $(event.currentTarget).data("type");
        const aFamilyMember = Relationships.findOne({targetId: invoice.personId, relationshipType:"family"});

        showModal("simpleModal", {
            title: "Add Discount/Reimbursement",
            template: "billingAddDiscountModal",
            actionButtonLabel: "Save",
            data: {
                discountType
            },
            onSave: (e, i, formFieldData) => {
                formFieldData.invoiceId = invoiceId;
                formFieldData.lineItemId = lineItemId;
                formFieldData.discountType = discountType;
                const discountAmount = parseFloat(formFieldData.discount_amount);

                if (!(formFieldData.allocation_discount_type || formFieldData.allocation_reimbursement_type)) {
                    $(e.target).html('Save').prop("disabled", false);
                    mpSwal.fire("Error", "You must select a discount type.", "error");
                    return;
                }

                if (isNaN(discountAmount)) {
                    $(e.target).html('Save').prop("disabled", false);
                    mpSwal.fire("Error", "Discount amount must be a number.", "error");
                    return;
                }

                if (discountAmount < 0) {
                    $(e.target).html('Save').prop("disabled", false);
                    mpSwal.fire("Error", "Discount amount cannot be less than $0.", "error");
                    return;
                }

                if (invoice.openAmount < discountAmount && aFamilyMember) {
                    mpSwal.fire({
                        title: "Notice",
                        text: "Saving this adjustment will result in a credit balance on the parent’s account. Would you like to proceed?",
                        showCancelButton: true,
                        confirmButtonText: "Yes",
                    }).then( result => {
                        if (result.value) {
                            // Discount amount will use up all the open amount
                            formFieldData.discount_amount = invoice.openAmount;
                            Meteor.callAsync("addDiscount", formFieldData)
                                .then(async (result) => {
                                    let description = '';
                                    const orgAvailableDiscountTypes = OrgsLib.availableDiscountTypes(currentOrg, false, false)

                                    if (discountType === AllocationTypes.DISCOUNT) {
                                        const matchedDiscount = orgAvailableDiscountTypes.find(d => d.type === formFieldData.allocation_discount_type);
                                        description = matchedDiscount ? `${matchedDiscount.description} discount` : '';
                                    }

                                    if (discountType === AllocationTypes.SUBSIDY) {
                                        const matchedPayerSource = orgAvailableDiscountTypes.find(p => p.type === formFieldData.allocation_reimbursement_type);
                                        description = matchedPayerSource ? `${matchedPayerSource.description} reimbursement` : '';
                                    }

                                    const options = {
                                        type: 'other',
                                        notes: `Other Credit - ${description}`,
                                        amount: discountAmount - invoice.openAmount,
                                        personId: aFamilyMember.personId
                                    }

                                    Meteor.callAsync("insertBillingCreditMemo", options)
                                        .then((result) => {
                                            $("#simpleModal").modal('hide');
                                        })
                                        .catch((error) => {
                                            $(e.target).html('Save').prop("disabled", false);
                                            mpSwal.fire("Error", error.reason || error.message, "error");
                                        });
                                    await this.updateCurrentInvoice();
                                })
                                .catch((error) => {
                                    $(e.target).html('Save').prop("disabled", false);
                                    mpSwal.fire("Error", error.reason || error.message, "error");
                                });
                        }
                    })
                    return;
                }

                Meteor.callAsync("addDiscount", formFieldData)
                    .then(async (result) => {
                        await this.updateCurrentInvoice();
                        $("#simpleModal").modal('hide');
                    })
                    .catch((error) => {
                        $(e.target).html('Save').prop("disabled", false);
                        mpSwal.fire("Error", error.reason || error.message, "error");
                    });
            }
        });
    }

    /**
     * Handles reallocating a discount for the invoice.
     * @param {Event} event - The event triggered by the UI.
     */
    invoiceReallocateDiscount(event) {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        const lineItemId = $(event.currentTarget).data("id");
        const lineItem = _.find(invoice.lineItems, (li) => li._id === lineItemId);
        const allocationIndex = $(event.currentTarget).data("index");
        const indexedAllocations = lineItem?.appliedDiscounts?.map( (ad, i) => { ad.index = i; return ad; });
        const allocations = indexedAllocations.filter( ad => ad.type === AllocationTypes.SUBSIDY && !ad.voidedAt);
        const allocation = indexedAllocations[allocationIndex];
        const remainingAmount = invoice.openPayerAmounts[allocation.source];
        const familyShare = invoice.openAmount;

        showModal("simpleModal", {
            title: "Reallocate Reimbursement",
            template: "billingReallocatePayerModal",
            actionButtonLabel: "Save",
            data: {
                allocations,
                allocateFromDescription: allocation.originalAllocation.allocationDescription,
                allocateFromAmount: remainingAmount,
                familyShare,
                reallocationDescription: "Reallocation from payer " + allocation.source + " on invoice #" + invoice.invoiceNumber
            },
            onSave: (e, i, formFieldData) => {
                formFieldData.invoiceId = invoiceId;
                formFieldData.lineItemId = lineItemId;

                Meteor.callAsync("reallocatePayer", formFieldData)
                    .then(async (result) => {
                        await this.updateCurrentInvoice();
                        $("#simpleModal").modal('hide');
                        mpSwal.fire("Success", result, "success");
                    })
                    .catch((error) => {
                        $(e.target).html('Save').prop("disabled", false);
                        mpSwal.fire("Error", error.error, "error");
                    });
            }
        });
    }

    /**
     * Removes a reimbursable day from a line item.
     * @param {Event} event - The event triggered by the UI.
     */
    invoiceRemoveReimbursableDay(event) {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        const lineItemId = $(event.currentTarget).data("lineitem-id");
        const allocationIndex = $(event.currentTarget).data("allocation-id");
        const coveredDay = $(event.currentTarget).data("day");

        mpSwal.fire({
            title: "Are you sure?",
            text: "This will remove the selected reimbursable day from the line item.",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, remove it"
        }).then(result => {
            if (result.value) {
                Meteor.callAsync("removeReimbursableDay", { invoiceId, lineItemId, allocationIndex, coveredDay }).then(async (result) => {
                    await this.updateCurrentInvoice();
                });
            }
        })
    }

    /**
     * Adds a reimbursable day to a line item.
     * @param {Event} event - The event triggered by the UI.
     */
    invoiceAddReimbursableDay(event) {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        const lineItemId = $(event.currentTarget).data("lineitem-id");
        const allocationIndex = $(event.currentTarget).data("allocation-id");

        mpSwal.fire({
            title: "Add reimbursable day",
            html:"<p><form><input id='add-reimbursable-day-input' type='text' class='form-control'></form></p>",
            showCancelButton: true,
            confirmButtonText: "Add selected day",
            didRender: function() {
                $("#add-reimbursable-day-input").datepicker({ autoclose: true, todayHighlight: true });
            },
        }).then(result => {
            if (result.value) {
                const dateValue = $("#add-reimbursable-day-input").val();
                const selectedDate = moment(dateValue);

                if (!selectedDate.isValid()) {
                    return mpSwal.fire("Error", "Invalid date value selected");
                }

                Meteor.callAsync("addReimbursableDay", { invoiceId, lineItemId, allocationIndex, coveredDay:selectedDate.format("YYYY-MM-DD") }).then(async (result) => {
                    await this.updateCurrentInvoice();
                });
            }
        });
    }

    /**
     * Resends the invoice.
     */
    resendInvoice() {
        const invoice = this.invoice.get();
        mpSwal.fire({
            title: "Resend Invoice",
            text: "Are you sure you would like to resend invoice #" + invoice.invoiceNumber,
            type: "warning",
            showCancelButton: true
        }).then(result => {
            if (result.value) {
                Meteor.callAsync("resendInvoice", {invoiceId: invoice._id})
                    .then((resendResult) => {
                        mpSwal.fire("Invoice re-sent");
                    })
                    .catch((error) => {
                        mpSwal.fire("Error", error.reason || error.message, "error");
                    });
            }
        });
    }

    /**
     * Voids the invoice.
     */
    voidInvoice() {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;

        mpSwal.fire({
            title: "Are you sure?",
            text: "This will void the invoice and set the amount open to $0.",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, void it!"
        }).then(result => {
            if (result.value){
                Meteor.callAsync("voidInvoice", {invoiceId})
                    .then(async (result) => {
                        await this.updateCurrentInvoice();
                        mpSwal.fire("Invoice successfully voided");
                    })
                    .catch((error) => {
                        mpSwal.fire("Error voiding invoice", error.reason, "error");
                    });
            }
        });
    }

    /**
     * Credits the invoice.
     * @returns {Promise<void>} - Resolves when the invoice is credited.
     */
    async creditInvoice() {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        await BillingHelpers.showCreditModal(invoiceId, invoice, null, null, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Issues a refund for the invoice.
     */
    issueRefund() {
        const invoice = this.invoice.get();
        const invoiceId = invoice._id;
        BillingHelpers.showRefundModal(invoiceId, invoice, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Handles voiding a specific line item.
     * @param {Event} event - The event triggered by the UI.
     */
    voidLineItem(event) {
        const creditIndex = $(event.currentTarget).data("id");
        const invoice = this.invoice.get();
        BillingHelpers.showVoidCreditLineModal(invoice._id, creditIndex, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Modifies a specific line item.
     * @param {Event} event - The event triggered by the UI.
     * @returns {Promise<void>} - Resolves when the line item is modified.
     */
    async modifyLineItem(event) {
        const creditIndex = $(event.currentTarget).data("id");
        const invoice = this.invoice.get();
        await BillingHelpers.showCreditModal(invoice._id, invoice, null, creditIndex, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Issues a manual refund for a specific line item.
     * @param {Event} event - The event triggered by the UI.
     */
    manualRefundLineItem(event) {
        const creditIndex = $(event.currentTarget).data("id");
        const invoice = this.invoice.get();
        BillingHelpers.showManualRefundCreditLineModal(invoice._id, creditIndex, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Reverses a specific line item.
     * @param {Object} lineItemDetail - The line item detail object.
     */
    reverseLineItem(lineItemDetail) {
        const options = {
            invoice: this.invoice.get(),
            creditIndex: lineItemDetail.i ?? null,
            allocationIndex: lineItemDetail.ai ?? null,
            createdInvoice: lineItemDetail.createdInvoice ?? null
        }

        BillingHelpers.showReverseLineItemModal(options, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Adjusts a specific line item.
     * @param {Object} lineItemDetail - The line item detail object.
     */
    adjustLineItem(lineItemDetail) {
        const invoice = this.invoice.get();
        const person = this.targetPerson();

        if (!invoice || !person || lineItemDetail.i === undefined) {
            let errorMessage = "Unable to find ";

            if (!invoice) {
                errorMessage += "invoice";
            }

            if (!person) {
                errorMessage += "person";
            }

            if (lineItemDetail.i === undefined) {
                errorMessage += "credit";
            }

            mpSwal.fire("Error", errorMessage.trim(), "error");
            return;
        }

        const options = {
            invoice,
            creditIndex: lineItemDetail.i,
            person,
        }

        BillingHelpers.showAdjustLineItemModal(options, this.updateCurrentInvoice.bind(this));
    }

    /**
     * Edits the invoice notes.
     */
    editInvoiceNotes() {
        const  invoice = this.invoice.get();
        mpSwal.fire({
            title:"Edit Invoice Notes",
            input:"textarea",
            inputValue: invoice.invoiceNotes || "",
            showCancelButton: true,
            confirmButtonText: "Save"
        }).then((result) => {
            if (result.hasOwnProperty('value')) {
                Meteor.callAsync("updateInvoiceNotes", { invoiceId: invoice._id, notes: result.value || "" })
                    .then(async (result) => {
                       await this.updateCurrentInvoice();
                    })
                    .catch((error) => {
                        mpSwal.fire("Error", error.reason, "error");
                    });
            }
        });
    }
}
