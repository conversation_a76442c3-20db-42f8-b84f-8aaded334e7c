import { MANUAL_PAYMENTS } from '../../../lib/constants/billingConstants';
import { ReactiveVar } from 'meteor/reactive-var';
/**
 * Service for managing payment settings.
 */
export class PaymentSettingsClientService {

    /**
     * Constructs a PaymentSettingsClientService instance.
     * @param {Object} org - The organization object containing billing data.
     * @param {Object} org.billing - The billing settings for the organization.
     * @param {string[]} [org.billing.excludedManualPayTypes=[]] - Manual payment types excluded from use.
     * @param {boolean} [org.billing.paymentMethodRequired=false] - Whether a payment method is required.
     * @param {boolean} [org.billing.autopayEnrollmentRequired=false] - Whether autopay enrollment is required.
     */
    constructor(org) {
        this.excludedManualPayTypes = new ReactiveVar(org.billing?.excludedManualPayTypes || []);
        this.paymentMethodRequired = new ReactiveVar(org.billing?.paymentMethodRequired || false);
        this.autopayEnrollmentRequired = new ReactiveVar(org.billing?.autopayEnrollmentRequired || false);
        this.manualPayTypes = MANUAL_PAYMENTS.TYPES;
    }

    /**
     * Retrieves the available manual payment types.
     * @returns {Array<Object>} List of manual payment types.
     */
    getManualPayTypes() {
        return this.manualPayTypes;
    }

    /**
     * Checks if the optional payment method is disabled based on autopay enrollment.
     * @returns {boolean} True if optional payment methods are disabled, false otherwise.
     */
    isOptionalPaymentMethodDisabled() {
        return this.autopayEnrollmentRequired.get();
    }

    /**
     * Validates and updates the autopay enrollment requirement.
     * If autopay enrollment is required, a payment method is also required.
     * @param {boolean} required - Whether autopay enrollment is required.
     */
    validateAutopayEnrollmentChange(required) {
        this.autopayEnrollmentRequired.set(required);
        if (required) {
            this.paymentMethodRequired.set(true);
        }
    }

    /**
     * Validates and updates the payment method requirement.
     * A payment method is required if autopay enrollment is required or explicitly set.
     * @param {boolean} required - Whether a payment method is required.
     */
    validatePaymentMethodChange(required) {
        this.paymentMethodRequired.set(required || this.autopayEnrollmentRequired.get());
    }

    /**
     * Determines if an item is selected based on the given type and source.
     * @param {string|boolean} type - The value to check for selection.
     * @param {string} source - The source of the selection ('payTypes', 'paymentMethod', or 'autopayEnrollment').
     * @returns {boolean} True if the item is selected, false otherwise.
     */
    isSelected(type, source) {
        switch (source) {
            case 'payTypes':
                return !this.excludedManualPayTypes.get().includes(type);
            case 'paymentMethod':
                return this.paymentMethodRequired.get() === type;
            case 'autopayEnrollment':
                return this.autopayEnrollmentRequired.get() === type;
            default:
                return false;
        }
    }

    /**
     * Gets the payment method option label based on the requirement.
     * @param {boolean} paymentMethodRequired - Whether a payment method is required.
     * @returns {string} The label for the payment method requirement.
     */
    static paymentMethodOptionValue(paymentMethodRequired) {
        if (paymentMethodRequired) {
            return "Required - Family members must keep at least one payment method on file at all times.";
        } else {
            return "Optional - Family members are not required to keep a payment method on file.";
        }
    }

    /**
     * Gets the autopay enrollment option label based on the requirement.
     * @param {boolean} autopayEnrollmentRequired - Whether autopay enrollment is required.
     * @returns {string} The label for the autopay enrollment requirement.
     */
    static autopayEnrollmentOptionValue(autopayEnrollmentRequired) {
        if (autopayEnrollmentRequired) {
            return "Required - Family members must remain enrolled in autopay once initially enrolled.";
        } else {
            return "Optional - Family members are not required to enroll in autopay.";
        }
    }

    /**
     * Transforms form field data into payment settings values.
     * @param {Object} formFieldData - The form data containing payment settings.
     * @param {string[]} [formFieldData.manualPaymentTypes] - The included manual payment types.
     * @param {string} formFieldData.paymentMethod - The payment method requirement ('required' or 'optional').
     * @param {string} formFieldData.autopayEnrollment - The autopay enrollment requirement ('required' or 'optional').
     * @returns {Object} The transformed payment settings values.
     */
    static getFormValuesFromOnSaveData(formFieldData) {
        return {
            excludedManualPayTypes: this.getExcludedManualPayTypes(formFieldData.manualPaymentTypes),
            paymentMethodRequired: formFieldData.paymentMethod === "required",
            autopayEnrollmentRequired: formFieldData.autopayEnrollment === "required"
        };
    }

    /**
     * Gets the excluded manual payment types based on the included types.
     * If no included types are provided, all payment types are excluded.
     * @param {string[]} includedManualPayTypes - The included manual payment types.
     * @returns {string[]} The excluded manual payment types.
     */
    static getExcludedManualPayTypes(includedManualPayTypes) {
        if (!Array.isArray(includedManualPayTypes)) {
            return MANUAL_PAYMENTS.TYPES.map(type => type.value);
        }

        return MANUAL_PAYMENTS.TYPES.filter(type => !includedManualPayTypes.includes(type.value)).map(type => type.value);
    }
}