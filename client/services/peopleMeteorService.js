export function getPeopleById(curId) {
	return new Promise((resolve, reject) => {
		Meteor.callAsync('getPeopleById', { _id: curId }).then((res) => {
			resolve(res);
		}).catch((err) => {
			reject(err);
		});
	});
}

export function getPeopleData(query, options) {
	return new Promise((resolve, reject) => {
		Meteor.callAsync("getPeopleByQueryAndOptions", query, options).then((res) => {
		  resolve(res);
		}).catch((err) => {
		  reject(err);
		});
	})
}
