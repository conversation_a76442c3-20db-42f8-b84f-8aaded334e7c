import { AvailableCustomizations } from '../../../lib/customizations';
import moment from 'moment-timezone';
import { OrgsLib } from '../../../lib/orgsLib';
import { Orgs } from '../../../lib/collections/orgs';
const _ = require('lodash');
import { ReactiveVar } from 'meteor/reactive-var';

/**
 * Service for managing schedule types for an organization.
 */
export class AddScheduleTypeClientService {

    /**
     * Constructs an instance of AddScheduleTypeClientService.
     *
     * @param {Object} org - The organization object.
     */
    constructor(org) {
        this.orgGroups = new ReactiveVar([]);

        if (org) {
            this.currentOrg = new ReactiveVar(org);
            this.initializeOrgGroups(org);
        }
    }

    /**
     * Updates the current organization data by fetching it from the database.
     */
    updateOrg() {
        this.currentOrg.set(Orgs.current());
    }

    /**
     * Sets the list of groups for the organization.
     *
     * @param {Array<Object>} groups - The array of group objects.
     */
    setGroups(groups) {
        this.orgGroups.set(groups);
    }

    /**
     * Initializes the organization groups by calling the server method.
     *
     * @param {Object} org - The organization object.
     */
    initializeOrgGroups(org) {
        Meteor.callAsync('getOrgGroups', org._id).then(response => {
            this.setGroups(response);
        }).catch(error => {
            mpSwal.fire('Error', `Error getting groups: ${error.message || error.reason}`, 'error');
        });
    }

    /**
     * Checks if the organization has the registration flow customization enabled.
     *
     * @returns {boolean} - True if the customization is enabled, otherwise false.
     */
    hasRegistrationFlow() {
        return OrgsLib.orgHasCustomization(this.currentOrg.get(), AvailableCustomizations.REGISTRATION_FLOW);
    }

    /**
     * Checks if the organization has the weekends customization enabled.
     *
     * @returns {boolean} - True if the customization is enabled, otherwise false.
     */
    hasWeekendsEnabled() {
        return OrgsLib.orgHasCustomization(this.currentOrg.get(), AvailableCustomizations.WEEKENDS_ENABLED);
    }

    /**
     * Checks if the organization has the automatic group updating customization enabled.
     *
     * @returns {boolean} - True if the customization is enabled, otherwise false.
     */
    hasAutomaticGroupUpdating() {
        return OrgsLib.orgHasCustomization(this.currentOrg.get(),AvailableCustomizations.UPDATE_DEFAULT_GROUP_BY_SCHEDULE);
    }

    /**
     * Retrieves the schedule types for the current organization.
     *
     * @returns {Array<Object>} - An array of schedule type objects.
     */
    getScheduleTypes() {
        return OrgsLib.getScheduleTypes(this.currentOrg.get());
    }

    /**
     * Retrieves the groups for the current organization.
     *
     * @returns {Array<Object>} - An array of group objects.
     */
    getGroups() {
        return this.orgGroups.get();
    }

    /**
     * Gets the default group name for a given group ID.
     *
     * @param {string} groupId - The ID of the group.
     * @returns {string} - The name of the group or an empty string if not found.
     */
    getDefaultGroupName(groupId) {
        const foundGroup = this.getGroups().find(group => group._id === groupId);
        if (foundGroup) {
            return foundGroup.name;
        } else {
            return '';
        }
    }

    /**
     * Removes a schedule type by its ID.
     *
     * @param {string} scheduleTypeId - The ID of the schedule type to be removed.
     */
    removeScheduleType(scheduleTypeId) {
        const org = this.currentOrg.get();
        mpSwal.fire({
            title: "Are you sure?",
            text: "Deleting this schedule type will disassociate it from all active schedules, and remove it from the billing plan configuration for your organization",
            icon: "warning",
            showCancelButton: true,
            closeOnConfirm: false
        }).then(result => {
            if (result.value) {
                Meteor.callAsync("removeScheduleType", { scheduleTypeId }).then(response => {
                    mpSwal.fire('Success', "Schedule type removed", 'success');
                    this.updateOrg();
                }).catch(error => {
                    mpSwal.fire('Error', `Error while removing schedule type: ${error.message || error.reason}`, 'error');
                });

                Meteor.callAsync("removeScheduleTypeFromPlansForAllGroupsInOrg", org._id, scheduleTypeId)
            }
        });
    }

    /**
     * Populates the form fields for editing a schedule type.
     *
     * @param {Object} scheduleType - The schedule type object to be edited.
     */
    editScheduleType(scheduleType) {
        $("input[name=schedule-type-id]").val(scheduleType._id);
        const scheduleTypeTimeBlock = scheduleType.startTime ? "scheduled" : "all-day";
        $("#AddScheduleType-Name").val(scheduleType.type);

        if (scheduleType.defaultGroupId) {
            $("#AddScheduleType-DefaultGroup").val(scheduleType.defaultGroupId);
        }

        $("input[name=scheduleTypeTimeBlock][value=" + scheduleTypeTimeBlock + "]").prop("checked", "checked");
        $("#schedule-type-start-time").val(scheduleType.startTime);
        $("#schedule-type-end-time").val(scheduleType.endTime);
        $("#AddScheduleType-maxEnrollmentMon").val(scheduleType.maxEnrollment?.[1]);
        $("#AddScheduleType-maxEnrollmentTue").val(scheduleType.maxEnrollment?.[2]);
        $("#AddScheduleType-maxEnrollmentWed").val(scheduleType.maxEnrollment?.[3]);
        $("#AddScheduleType-maxEnrollmentThu").val(scheduleType.maxEnrollment?.[4]);
        $("#AddScheduleType-maxEnrollmentFri").val(scheduleType.maxEnrollment?.[5]);

        if (this.hasWeekendsEnabled()) {
            $("#AddScheduleType-maxEnrollmentSat").val(scheduleType.maxEnrollment?.[6] ?? 0);
            $("#AddScheduleType-maxEnrollmentSun").val(scheduleType.maxEnrollment?.[0] ?? 0);
        }

        $("#AddScheduleType-FTE").val(scheduleType.fteCount);
        $("#scheduleId").val(scheduleType._id);
        $("#schedule-type-hide-forecasting").prop("checked", scheduleType.hideInForecasting);

        $("#frmAddScheduleType").show();
        $("#btnAddScheduleType").hide();
    }

    /**
     * Hides and resets the add/update.
     */
    hideAndResetForm() {
        $("#frmAddScheduleType").hide();
        $("#frmAddScheduleType")[0].reset();
        $("input[name=scheduleTypeTimeBlock][value=all-day]").prop("checked", true);
        $("#btnAddScheduleType").show();
    }

    /**
     * Retrieves the form data for the schedule type.
     * @return {{scheduleType: (*|jQuery), scheduleTypeTimeBlock: (*|jQuery), startTime: (*|jQuery), endTime: (*|jQuery), fteCount: (*|jQuery), hideInForecasting: (*|jQuery), existingId: (*|jQuery), defaultGroupId: (*|jQuery)}}
     */
    getFormData() {
        const formData = {
            scheduleType: $("#AddScheduleType-Name").val(),
            scheduleTypeTimeBlock: $("input[name=scheduleTypeTimeBlock]:checked").val(),
            startTime: $("#schedule-type-start-time").val(),
            endTime: $("#schedule-type-end-time").val(),
            fteCount: $("#AddScheduleType-FTE").val(),
            hideInForecasting: $("#schedule-type-hide-forecasting").prop("checked"),
            existingId: $("input[name=schedule-type-id]").val(),
            defaultGroupId: $("#AddScheduleType-DefaultGroup").val(),
        };

        if (this.hasRegistrationFlow()) {
            formData.maxEnrollment = {
                1: $("#AddScheduleType-maxEnrollmentMon").val() ?? 0,
                2: $("#AddScheduleType-maxEnrollmentTue").val() ?? 0,
                3: $("#AddScheduleType-maxEnrollmentWed").val() ?? 0,
                4: $("#AddScheduleType-maxEnrollmentThu").val() ?? 0,
                5: $("#AddScheduleType-maxEnrollmentFri").val() ?? 0,
            }
        }

        if (this.hasWeekendsEnabled()) {
            formData.maxEnrollment[6] = $("#AddScheduleType-maxEnrollmentSat").val() ?? 0;
            formData.maxEnrollment[0] = $("#AddScheduleType-maxEnrollmentSun").val() ?? 0;
        }

        return formData;
    }

    /**
     * Saves the new or updated schedule type using the form data.
     */
    async saveAddScheduleType() {
        const {
            scheduleType,
            scheduleTypeTimeBlock,
            startTime,
            endTime,
            existingId,
            fteCount,
            maxEnrollment,
            hideInForecasting,
            defaultGroupId
        } = this.getFormData();
        const randomId = Random.id();

        mpSwal.fire({
            title: "Are you sure?",
            text: "Adding this schedule type will automatically add it to the billing plan configuration for your organization. Please ensure you configure the plans that pertain to this schedule under Groups > Billing Plans",
            icon: "warning",
            showCancelButton: true,
            closeOnConfirm: true
        }).then(async result => {
            if (result.value) {

                if (_.isEmpty(scheduleType)) {
                    return mpSwal.fire({ icon: "error", title: "Cannot save empty field", text: "enter a schedule type name" });
                }

                if (scheduleTypeTimeBlock === "scheduled" && (!moment(startTime, "h:mm a").isValid() || !moment(endTime, "hh:mm a").isValid())) {
                    return mpSwal.fire({ icon: "error", title: "Invalid times for schedule", text: "Please enter valid times in format HH:MM AM/PM" });
                }

                const insertOptions = {
                    existingId,
                    randomId,
                    scheduleType,
                    scheduleTypeTimeBlock,
                    startTime,
                    endTime,
                    maxEnrollment,
                    fteCount,
                    hideInForecasting
                }

                const org = this.currentOrg.get();

                if (defaultGroupId && OrgsLib.orgHasCustomization(org, AvailableCustomizations.UPDATE_DEFAULT_GROUP_BY_SCHEDULE)) {
                    insertOptions.defaultGroupId = defaultGroupId;
                }

                await Meteor.callAsync("insertUpdateScheduleType", insertOptions).then(response => {
                    mpSwal.fire('Success', "Schedule type saved", 'success');
                    this.updateOrg();
                }).catch(error => {
                    mpSwal.fire('Error', `Error while adding/updating schedule type: ${error.message || error.reason}`, 'error');
                });

                await Meteor.callAsync("addScheduleTypeToPlansForAllGroupsInOrg", org._id, existingId || randomId);
            }
        });
    }
}