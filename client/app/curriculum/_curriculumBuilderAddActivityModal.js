import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Curriculums } from '../../../lib/collections/curriculum';
import './_curriculumBuilderAddActivityModal.html';
import _ from '../../../lib/util/underscore';
import { orgLanguageTranformationUtil } from "../../../lib/util/orgLanguageTranformationUtil";
import $ from 'jquery';

Template._curriculumBuilderAddActivityModal.events({
	"click #show-copy-activity-panel": (e, i) => {
		e.preventDefault();
		$("#copy-btn-select").removeAttr('hidden');
		$("#copy-btn-label").attr('hidden', '');
	},
	"click #copy-activity": (e,i) => {
		e.preventDefault();
		const selections = $("#inputExistingActivities").select2('data'),
			selectedActivity = selections && selections[0] && selections[0].item;

		if (selectedActivity) {
			i.copyData.set(selectedActivity);
			$('#inputCurriculumAddActivityTypes').val(selectedActivity.selectedTypes);
			$('#inputCurriculumAddActivityTypes').trigger('change');
			
			const selectedAgeGroup = selectedActivity.selectedAgeGroup;
			if (Curriculums.activitiesAgeGroups()) $("#inputAge").val(selectedAgeGroup);
			$('#inputCurriculumAddActivityStandards').empty().trigger("change");
			$("#inputCurriculumAddActivityStandards").select2({
				data: _.map( getMappedStandards(selectedAgeGroup), (s) => {
					const currentTag = s.source+"|"+s.standardId;
					return {id: currentTag,
						text: s.label,
						selected: _.contains(selectedActivity.selectedStandards, currentTag)
					};
				})
			});
			
			$("#inputOriginalCurriculumId").val(selectedActivity._id);
		}
	},
	"change #inputAge": (e,i) => {
		const selection = $(e.currentTarget).val();
		$('#inputCurriculumAddActivityStandards').empty().trigger("change");
		$("#inputCurriculumAddActivityStandards").select2({
			data: _.map( getMappedStandards(selection), (s) => ({
				id: s.source+"|"+s.standardId,
				text: s.label
			}))
		})
	},
  'change #curriculumInputFile': function(event, instance) {
		event.preventDefault();
		var uploadFiles = $(event.target);
		var fieldId = uploadFiles.first().data("fieldid");
		var uploadFile = uploadFiles[0];
    if (uploadFile && uploadFile.files.length > 0 ) {
      var arr = [];
      for (let x=0; x < uploadFile.files.length; x++) {
        arr.push(uploadFile.files[x].name);
      }
      $("#curriculumFileLabelSpan").text(arr.join(', '));
    }
  }
});

Template._curriculumBuilderAddActivityModal.helpers({
	availableCurriculumTypes() {
		return Orgs.current().availableCurriculumTypes({mapped: true});
	},
	'showCurriculumStandards': function() {
		return Curriculums.getStandards();
	},
	'availableCurriculumStandards': function() {
		const data = Template.instance().data;
		if (data.pendingCurriculum) return getMappedStandards(data.pendingCurriculum.selectedAgeGroup);
		return getMappedStandards(data.selectedAgeGroup);
	},
	'replaceBackticks': function(s) {
		return s.replace('`', ' > ');
	},
	isTypeSelected(type) {
		const data = Template.instance().data;
		if (data.pendingCurriculum) return _.contains(data.pendingCurriculum.selectedTypes, type);
		return _.contains(data.selectedTypes, type);
	},
	isStandardSelected(source, standardId) {
		const data = Template.instance().data;
		if (data.pendingCurriculum) return _.contains(data.pendingCurriculum.selectedStandards, source+"|"+standardId);
		return _.contains(data.selectedStandards, source+"|"+standardId);
	},
	canShowCopy() {
		const instance = Template.instance();
		if (instance.data.hideCopy) return false;
		if (instance.data._id) return false;

		return true;
	},
	isEditing() {
		return Template.instance().data._id;
	},
	activityData() {
		const instance = Template.instance();
		if (instance.data.pendingCurriculum) return instance.data.pendingCurriculum
		return instance.copyData.get() || instance.data;
	},
	availableAgeGroups() {
		return Curriculums.activitiesAgeGroups();
	},
	showMaterials() {
		return !Orgs.current().hasCustomization("modules/curriculum/hideMaterials");
	},
	showHomework() {
		return !Orgs.current().hasCustomization("modules/curriculum/hideHomework");
	},
	'getActivitiesTag': function() {
		return orgLanguageTranformationUtil.getActivities('tags');
	},
});
Template._curriculumBuilderAddActivityModal.onCreated( function () {
	const self = this;
	self.copyData = new ReactiveVar();
	self.selectedAgeGroup = new ReactiveVar();
})

Template._curriculumBuilderAddActivityModal.rendered = function() {

	$("#inputExistingActivities").select2({
		ajax: {

			transport: function (params, success, failure) {
				/*
				var request = new AjaxRequest(params.url, params);
				request.on('success', success);
				request.on('failure', failure);
				*/
				
				Meteor.callAsync("searchCurriculumItems", {searchText: params.data.term}).then(result => {
					success( {
						results: result
					});
				}).catch(error => {
					return;
				});
			}
		},
		dropdownParent: $('#formCurriculumAddActivity'),
		templateResult: formatActivityItem,
		escapeMarkup: function (markup) { return markup; }, // let our custom formatter work
	});

	$("#inputCurriculumAddActivityStandards").select2( {
    multiple: true,
		dropdownParent: $('#formCurriculumAddActivity'),
		templateSelection: function(item)
		    {	
		        return item.text;
			}
		}
	);
	$("#inputCurriculumAddActivityTypes").select2( {
    multiple: true,
		dropdownParent: $('#formCurriculumAddActivity'),
		templateSelection: function(item)
		    {
		        return item.text;
			}
		}
	);

};

function getMappedStandards(selectedAgeGroup) {
	
	const baseStandards = Curriculums.getStandards();
	let standards;
	if (Curriculums.activitiesAgeGroups()) {
		standards = _.filter(baseStandards, (standard) => standard.ageGroup == selectedAgeGroup );	
	} else {
		standards = baseStandards;
		//standards = _.map(standards, (standard) => { standard.label = standard.source + ": " + standard.benchmark; return standard; });
	}
	standards = _.map(standards, (standard) => { standard.label = (standard.category || standard.source) + ": " + standard.benchmark; return standard; });

	return standards;
}

function formatActivityItem (item) {
	
	if (item.loading) {
	  return item.text;
	}
  
	return `<div class="activity-search-item clearfix">
		<div class="activity-search-item__title">${item.text}</div>
		<div class="activity-search-item__description">${item.description}</div>
	</div>`;

	var markup = "<div class='select2-result-repository clearfix'>" +
	  "<div class='select2-result-repository__avatar'><img src='" + repo.owner.avatar_url + "' /></div>" +
	  "<div class='select2-result-repository__meta'>" +
		"<div class='select2-result-repository__title'>" + repo.full_name + "</div>";
  
	if (repo.description) {
	  markup += "<div class='select2-result-repository__description'>" + repo.description + "</div>";
	}
  
	markup += "<div class='select2-result-repository__statistics'>" +
	  "<div class='select2-result-repository__forks'><i class='fa fa-flash'></i> " + repo.forks_count + " Forks</div>" +
	  "<div class='select2-result-repository__stargazers'><i class='fa fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
	  "<div class='select2-result-repository__watchers'><i class='fa fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
	"</div>" +
	"</div></div>";
  
	return markup;
}
  
