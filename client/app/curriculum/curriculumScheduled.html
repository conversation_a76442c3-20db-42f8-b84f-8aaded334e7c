<template name="curriculumScheduled">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#if userCanAddOrModifyCurriculum}}
        <div class="d-flex flex-row justify-content-end mb-4">
          {{#unless requireThemes}}
            <div class="btn btn-primary font-weight-bolder btn-text-white mr-4" id="newCurriculumLink"  data-cy="add-activity-btn">
              <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add Activity
            </div>
          {{/unless}}
          <div data-cy="scheduled-activities-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="newThemeLink">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add Theme
          </div>
        </div>
      {{/if}}
      <div class="card card-custom gutter-b">
        <div class="card-body">
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <div class="form-group">
                <label>Group filter:</label>
                <select data-cy="scheduled-activities-group-filter" class="form-control" id="filterGroup">
                  <option value="">All</option>
                  {{#each groups}}
                    <option value="{{_id}}">{{name}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
            {{#if standards}}
              <div class="col-md-3 col-sm-6">
                <div class="form-group">
                  <label>Standard filter:</label>
                  <select class="form-control" id="filterStandard">
                    <option value="">All</option>
                    {{#each standards}}
                      <option value="{{standardId}}">{{benchmark}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            {{/if}}
            <div class="col-md-3 col-sm-6">
              <div class="form-group">
                <label>Start Date:</label>
                <input data-cy="start-date-activities" type="text" class="form-control pull-right" id="startDate" value="{{startDate}}">
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <div class="form-group">
                <label>End Date:</label>
                <input data-cy="end-date-activities" type="text" class="form-control pull-right" id="endDate" value="{{endDate}}">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label>Search text:</label>
                <input data-cy="search-text-activities" type="text" class="form-control pull-right" id="searchText">
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label>Group By:</label>
                <div class="radio-inline">
                  <label class="radio radio-primary">
                    <input data-cy="group-by-date" type="radio" name="group-by" value="date" checked/>
                    <span></span>
                    Date
                  </label>
                  <label class="radio">
                    <input data-cy="group-by-theme" type="radio" name="group-by" value="theme"/>
                    <span></span>
                    Theme
                  </label>
                </div>
              </div>
            </div>
          </div>
          {{#if curriculum.empty}}
            <div class="d-flex flex-row align-items-center justify-content-center mt-12">
              <div class="alert alert-custom alert-notice alert-light-info fade show max-w-1000px" id="no-announcement-alert" role="alert">
                <div class="alert-icon"><i class="fad fa-bullhorn"></i></div>
                <div class="alert-text">
                  <h3>Currently, no activities are scheduled that meet your search criteria.</h3>
                  {{#unless requireThemes}}<br/>Add <b>activities</b> to schedule learning moments or inform families of future activities.<br/>{{/unless}}
                  <br/>Add <b>themes</b> to organize and group activities together into time blocks.<br/>
                </div>
                <div class="alert-close">
                  <button type="button" class="close" data-dismiss="alert" aria-label="Close" id="no-announcement-close-alert">
                    <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
                  </button>
                </div>
              </div>
            </div>
          {{/if}}
        </div>
      </div>
      {{#each curriculum.unmatchedThemes}}
        <div class="card card-custom gutter-b" data-id="{{_id}}">
          <div class="card-body">
            <div class="row">
              <div class="d-flex flex-column justify-content-center col-4">
                <span data-cy="scheduled-theme-name" class="font-weight-bolder font-size-h5 text-primary">Theme: {{name}}</span>
                <span data-cy="scheduled-name-of-theme">No activities scheduled</span>
              </div>
              <div class="d-flex align-items-center col-3">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-calendar-check"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Scheduled</span>
                    <span data-cy="scheduled-date" class="font-weight-bolder font-size-h5">{{formatScheduledDays selectedDays}}</span>
                  </div>
                </div>
              </div>
              <div class="d-flex align-items-center col-4">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-users"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Recipient(s)</span>
                    <span data-cy="recipients" class="font-weight-bolder font-size-h5">
                      {{getRecipientGroupList findRecipientGroups}}
                    </span>
                  </div>
                </div>
              </div>
              {{#if userCanAddOrModifyCurriculum}}
                <div class="d-flex justify-content-end align-items-center col-1">
                  <div class="d-flex flex-row align-items-center justify-content-end">
                    <div class="dropdown">
                      <div data-cy="scheduled-activities-dropdown" class="btn btn-icon btn-clean" data-toggle="dropdown" >
                          <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                      </div>
                      <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                        <span data-cy="manage-theme" class="dropdown-item clickable-row manageThemeLink" data-id="{{_id}}">Manage Theme</span>
                      </div>
                    </div>
                  </div>
                </div>
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
      {{#each curriculum.allCurriculums}}
        <div class="card card-custom gutter-b" data-id="{{_id}}">
          <div class="card-body">
            <div class="row">
              <div class="d-flex flex-column justify-content-center col-4">
                <a data-cy="scheduled-theme-name" href="/activities/{{_id}}" class="font-weight-bolder font-size-h5 text-primary">{{headline}}</a>
                {{#if findTheme}}
                  <span data-cy="scheduled-name-of-theme" >Theme: <a href="/activities/themes/{{findTheme._id}}">{{findTheme.name}}</a></span>
                {{/if}}
              </div>
              <div class="d-flex align-items-center col-3">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-calendar-check"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Scheduled</span>
                    <span data-cy="scheduled-date" class="font-weight-bolder font-size-h5">{{scheduledDateFormatted}}</span>
                  </div>
                </div>
              </div>
              <div class="d-flex align-items-center col-4">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-users"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Recipient(s)</span>
                    <span data-cy="recipients" class="font-weight-bolder font-size-h5">
                      {{#if sendToAll}}
                        All
                      {{else}}
                        {{getRecipientGroupList findRecipientGroups}}
                      {{/if}}
                    </span>
                  </div>
                </div>
              </div>
              {{#if userCanAddOrModifyCurriculum}}
                <div class="d-flex justify-content-end align-items-center col-1">
                  <div class="d-flex flex-row align-items-center justify-content-end">
                    <div class="dropdown">
                      <div class="btn btn-icon btn-clean" data-toggle="dropdown" >
                          <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                      </div>
                      <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                      {{#unless curriculumThemeId}}
                        <span class="dropdown-item clickable-row editCurriculumLink" data-id="{{_id}}" >Edit</span>
                        <span class="dropdown-item clickable-row deleteCurriculumLink" data-id="{{_id}}">Delete</span>
                      {{else}}
                        <span data-cy="manage-theme" class="dropdown-item clickable-row manageThemeLink" data-id="{{curriculumThemeId}}">Manage Theme</span>
                      {{/unless}}
                      </div>
                    </div>
                  </div>
                </div>
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>
