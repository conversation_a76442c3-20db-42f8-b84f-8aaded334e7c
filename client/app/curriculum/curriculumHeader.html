<template name="curriculumHeader">
  <ul class="menu-nav">
    <li id="curriculumDefinitionsTab" class="menu-item {{#if routeContainsHashLocal 'curriculumDefinitionsTab'}}menu-item-active{{/if}}" aria-haspopup="true">
      <a href="/activities#curriculumDefinitionsTab" class="menu-link" data-cy="all-activities-header">
        <span class="menu-text">All Activities</span>
      </a>
    </li>
    {{#if hasCustomization "curriculumBank/management"}}
      <li id="curriculumThemesTab" class="menu-item {{#if routeContainsHashLocal 'curriculumThemesTab'}}menu-item-active{{/if}}" aria-haspopup="true">
        <a href="/activities#curriculumThemesTab" class="menu-link" data-cy="all-themes-header">
          <span class="menu-text">All Themes</span>
        </a>
      </li>
    {{/if}}
    <li id="scheduledCurriculumTab" class="menu-item {{#if routeContainsHashLocal 'scheduledCurriculumTab'}}menu-item-active{{/if}}" aria-haspopup="true">
      <a href="/activities#scheduledCurriculumTab" class="menu-link" data-cy="scheduled-activities-header">
        <span class="menu-text">Scheduled Activities</span>
      </a>
    </li>
    {{#if showConfigure}}
      <li  id="configureCurriculumTab" class="menu-item {{#if routeContainsHashLocal 'configureCurriculumTab'}}menu-item-active{{/if}}" aria-haspopup="true">
        <a href="/activities#configureCurriculumTab" class="menu-link" data-cy="configure-header">
          <span class="menu-text">Configure</span>
        </a>
      </li>
    {{/if}}
  </ul>
</template>
