<template name="curriculumBank">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#if userCanAddOrModifyCurriculumBank}}
        <div class="d-flex flex-row justify-content-end mb-4">
          <div data-cy="create-new-activity-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="newCurriculumBankLink">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Create New Activity
          </div>
        </div>
      {{/if}}
      <div class="card card-custom gutter-b">
        <div class="card-body">
          <div class="row">
              <div class="col-md-6 col-sm-6">
                <div class="form-group">
                  <input data-cy="input-activity-search" name="search-definitions" type="text" class="form-control">
                </div>
              </div>
              <div class="col-md-2 col-sm-2">
                <div data-cy="search-activity-btn" class="btn btn-primary font-weight-bolder" id="btn-search-definitions">Search</div>
              </div>
              <div class="col-md-4 col-sm-4 d-md-inline-flex align-items-md-baseline" >
              {{#if availableAgeGroups}}
                  <label class="mr-5">Age</label>
                  <select data-cy="age-activity-selector" class="form-control" id="inputFilterAge">
                    <option value="">All</option>
                    {{#each ageGroup in availableAgeGroups}}
                      <option value="{{ageGroup.label}}">{{ageGroup.label}}</option>
                    {{/each}}
                  </select>
              {{/if}}
              </div>
          </div>
          <div class="row">
            <div class="col-md-6 col-sm-6">
              <div class="checkbox-inline">
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" name="filterPublished" id="filterPublished" data-cy="filter-publish-chkbox" />
                  <span></span>
                  Published
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" name="filterDraft" id="filterDraft" data-cy="filter-draft-chkbox"/>
                  <span></span>
                  Draft
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" name="filterApproval" id="filterApproval" data-cy="filter-approval-chkbox"/>
                  <span></span>
                  Waiting for Approval
                </label> 
              </div>
              <span class="form-text text-muted">Activity Filter</span>
            </div>
            <div class="col-md-6 col-sm-6 d-flex flex-row justify-content-md-end align-items-center">
                <span class="page-item-set" style="cursor:pointer;" data-action="subtract" data-cy="pagination-previous-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
                <span class="font-size-h2" data-cy="pagination-records-count">{{getItemCountStart}} - {{getItemCountEnd(getTotalCount)}} of {{getTotalCount}}</span>
                <span class="page-item-set" style="cursor:pointer;" data-action="add" data-cy="pagination-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
            </div>
          </div>
          {{#if hasCurriculumBankNotification}}
            <div class="d-flex flex-row mt-2">
              <span class="font-weight-bolder font-size-h3">My Activity Notifications</span>
            </div>
          {{/if}}
          {{#each notification in curriculumBankNotifications}}
            <div class="d-flex flex-row align-items-center mt-2">
              <div class="alert alert-custom alert-notice {{getStatusColor notification.status}} fade show max-w-600px" id="no-announcement-alert" role="alert">
                <div class="d-flex flex-column">
                  <div class="alert-text font-size-h5 font-weight-bold">{{notification.title}} - {{notification.status}}</div>
                  {{#if notification.note}}
                    <div class="alert-text ml-4">{{notification.note}}</div>
                  {{/if}}
                  {{#if notification.publishNote}}
                    <div class="alert-text ml-4">{{notification.publishNote}}</div>
                  {{/if}}
                </div>
                {{#if canDismissNotification notification.status}}
                  <div class="alert-close">
                    <button type="button" class="close notificationClose" data-id="{{notification._id}}" data-dismiss="alert" aria-label="Close">
                      <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
                    </button>
                  </div>
                {{/if}}
              </div>
            </div>
          {{/each}}
        </div>
      </div>
      {{#each result in results}}
        <div class="card card-custom gutter-b" data-id="{{result.id}}">
          <div class="card-body">
            <div class="row">
                <div class="d-flex flex-column col-3">
                  <div data-cy="activity-title" class="font-size-h3 text-primary font-weight-bolder">{{getTitle result}}</div>
                  {{#if getAgeGroups result}}
                  <div data-cy="activity-age-group" class="text-secondary">Age Groups: {{getAgeGroups result}}</div>
                  {{/if}}
                  {{#if hasSourceLabel}}
                  <div data-cy="activity-source" class="text-secondary">{{getSourceLabel result}}</div>
                  {{/if}}
                </div>
                {{#if result.createdBy}}
                  <div class="d-flex align-items-center col-2">
                    <div class="d-flex align-items-end justify-content-start">
                      <span class="mr-2">
                        <i class="icon-2x text-bright-blue fad fa-user-plus"></i>
                      </span>
                      <div class="d-flex flex-column text-dark-75">
                        <span class="font-weight-bolder font-size-xs mb-1">Created By</span>
                        <span data-cy="created-by-name" class="font-weight-bolder font-size-lg">{{result.createdBy.name}}</span>
                        <span data-cy="created-by-org" class="font-weight-bold font-size-sm text-muted">{{result.createdBy.orgName}}</span>
                      </div>
                    </div>
                  </div>
                {{/if}}
                {{#if result.modifiedBy}}
                  <div class="d-flex align-items-center col-2">
                    <div class="d-flex align-items-end justify-content-start">
                      <span class="mr-2">
                        <i class="icon-2x text-bright-blue fad fa-user-edit"></i>
                      </span>
                      <div class="d-flex flex-column text-dark-75">
                        <span class="font-weight-bolder font-size-xs mb-1">Modified By</span>
                        <span data-cy="modified-by-name" class="font-weight-bolder font-size-lg">{{result.modifiedBy.name}}</span>
                        <span data-cy="modified-by-org" class="font-weight-bold font-size-sm text-muted">{{result.modifiedBy.orgName}}</span>
                      </div>
                    </div>
                  </div>
                {{/if}}
                {{#if result.updateRequestBy}}
                  <div class="d-flex align-items-center col-2">
                    <div class="d-flex align-items-end justify-content-start">
                      <span class="mr-2">
                        <i class="icon-2x text-bright-blue fad fa-pen-square"></i>
                      </span>
                      <div class="d-flex flex-column text-dark-75">
                        <span class="font-weight-bolder font-size-xs mb-1">Update Requested By</span>
                        <span data-cy="update-requested-by-name" class="font-weight-bolder font-size-lg">{{result.updateRequestBy.name}}</span>
                        <span data-cy="update-requested-by-org" class="font-weight-bold font-size-sm text-muted">{{result.updateRequestBy.orgName}}</span>
                      </div>
                    </div>
                  </div>
                {{else}}
                  {{#if result.approvedBy}}
                    <div class="d-flex align-items-center col-2">
                      <div class="d-flex align-items-end justify-content-start">
                        <span class="mr-2">
                          <i class="icon-2x text-bright-blue fad fa-user-check"></i>
                        </span>
                        <div class="d-flex flex-column text-dark-75">
                          <span class="font-weight-bolder font-size-xs mb-1">Approved By</span>
                          <span data-cy="approved-by-name" class="font-weight-bolder font-size-lg">{{result.approvedBy.name}}</span>
                          <span data-cy="approved-by-org" class="font-weight-bold font-size-sm text-muted">{{result.approvedBy.orgName}}</span>
                        </div>
                      </div>
                    </div>
                  {{/if}}
                {{/if}}
              {{#if canManageContent result}}
                <div class="d-flex justify-content-end align-items-center {{getColSize result}}">
                  <div class="d-flex flex-row align-items-center justify-content-end">
                    <div class="dropdown">
                      <div data-cy="dropdown-activity-options" class="btn {{getActionBtnColor result}} font-weight-bolder btn-text-white" id="btnCurriculumBankActions" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                      {{getActionDropdownLabel result}}<i class="fad-regular fad fa-angle-down ml-4 text-white"></i>
                      </div>
                      <div class="dropdown-menu dropdown-menu-right" aria-labelledby="btnCurriculumBankActions">
                        {{#if result.waitingForApproval}}
                          {{#if canApprove result}}
                            <span data-cy="review-activity" class="dropdown-item clickable-row reviewCurriculumBankLink" data-id="{{result._id}}">Review</span>
                          {{else}}
                            <span data-cy="locked-activity" class="dropdown-item clickable-row">Locked Until Review Complete</span>
                          {{/if}}
                        {{else}}
                          {{#if canEdit result}}
                            <span data-cy="edit-activity" class="dropdown-item clickable-row editCurriculumDefinitionLink" data-id="{{result._id}}">Edit</span>
                          {{/if}}
                          {{#if canPublish result}}
                            {{#if result.published}}
                              <span data-cy="unpublish-activity" class="dropdown-item clickable-row unpublishCurriculumBankLink" data-id="{{result._id}}">Unpublish</span>
                            {{else}}
                              <span data-cy="publish-activity" class="dropdown-item clickable-row publishCurriculumBankLink" data-id="{{result._id}}">Publish</span>
                              <span data-cy="delete-activity" class="dropdown-item clickable-row deleteCurriculumBankLink" data-id="{{result._id}}">Delete</span>
                            {{/if}}
                          {{/if}}
                        {{/if}}
                      </div>
                    </div>
                  </div>
                </div>
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>
