<template name="curriculumDefinitions">
    <div class="d-flex flex-column-fluid">
      <div class="container">
        <div class="card card-custom gutter-b">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 col-sm-6">
                <div class="form-group">
                  <label>Text Search</label>
                  <input name="search-definitions" type="text" class="form-control">
                </div>
              </div>
              <div class="col-md-6 col-sm-6">
                <div class="form-group">
                  <label>{{ getActivitiesTag }}:</label>
                  <select class="form-control" multiple name='search-tags' id="input-search-tags">
                    {{#each availableCurriculumTypes}}
                      <option value="{{this}}" >{{replaceBackticks this}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 col-sm-6">
                <div class="btn btn-primary font-weight-bolder" id="btn-search-definitions">Search</div>
              </div>
            </div>
          </div>
        </div>
        {{#each result in results}}
          <div class="card card-custom gutter-b" data-id="{{result.id}}">
            <div class="card-body">
              <div class="d-flex flex-row flex-grow-1 justify-content-between">
                <div class="d-flex flex-column">
                  <a href="/activities/{{result.id}}" class="font-size-h3 text-primary font-weight-bolder">{{result.text}}</a>
                  <span>{{result.description}}</span>
                </div>
                {{#if canManageContent}}
                  <div class="d-flex flex-column">
                    <div class="dropdown">
                      <div class="btn btn-icon btn-clean" data-toggle="dropdown" >
                          <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                      </div>
                      <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                        {{#if canEdit}}
                          <span class="dropdown-item clickable-row editCurriculumDefinitionLink" data-id="{{result.id}}">Edit</span>
                        {{/if}}
                        {{#if canArchive}}
                          <span class="dropdown-item clickable-row archiveCurriculumDefinitionLink" data-id="{{result.id}}">Archive</span>
                        {{/if}}
                      </div>
                    </div>
                  </div>
                {{/if}}
              </div>
            </div>
          </div>
        {{/each}}
      </div>
    </div>
</template>
