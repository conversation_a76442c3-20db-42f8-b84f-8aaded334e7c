<template name="_curriculumFormModal">
  <div id="_curriculumFormModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" style="width: 100%;height: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Activity</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-gray-100">
          {{#with curriculum}}
          <form id="curriculumForm">
            <input type="hidden" id="stayOpen" name="stayOpen" value="">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Title</label>
              <div class="col-lg-9 col-xl-6">
                <input type="text" class="form-control form-control-lg" id="inputHeadline" data-cy="curriculum-title" placeholder="Title" value="{{headline}}">
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Choose {{ getActivitiesTag }}</label>
              <div class="col-lg-9 col-xl-6">
                <select class="form-control form-control-lg" multiple name="curriculumTypes" id="inputCurriculumTypes" data-cy="curriculum-tags">
                  {{#each availableCurriculumTypes}}
                    <option value="{{this}}">{{replaceBackticks this}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
            {{#if availableCurriculumStandards}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Standard(s)</label>
                <div class="col-lg-9 col-xl-6">
                  <select class="form-control form-control-lg" multiple name="curriculumStandards" id="inputCurriculumStandards">
                    {{#each availableCurriculumStandards}}
                      <option label="{{source}}|{{standardId}}" value="{{source}}|{{standardId}}">{{source}}: {{benchmark}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            {{/if}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
              <div class="col-lg-9 col-xl-6">
                <textarea class="form-control form-control-lg" rows="3" placeholder="Notes" id="inputMessage" data-cy="curriculum-description">{{message}}</textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Materials</label>
              <div class="col-lg-9 col-xl-6">
                <textarea class="form-control form-control-lg" rows="3" placeholder="Materials" id="inputMaterials" data-cy="curriculum-materials">{{materials}}</textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Homework</label>
              <div class="col-lg-9 col-xl-6">
                <textarea class="form-control form-control-lg" rows="3" placeholder="Homework" id="inputHomework" data-cy="curriculum-homework">{{homework}}</textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Internal Notes</label>
              <div class="col-lg-9 col-xl-6">
                <textarea class="form-control form-control-lg" rows="3" placeholder="Internal notes (Viewable by Staff/Admins only)" id="inputInternalNotes" data-cy="curriculum-internal-notes">{{internalNotes}}</textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Internal Link</label>
              <div class="col-lg-9 col-xl-6">
                <input type="text" class="form-control form-control-lg" id="inputInternalLink" name="internalLink" data-cy="curriculum-internal-link" placeholder="Internal Link (Viewable by Staff/Admins only)" value="{{internalLink}}">
              </div>
            </div>
            {{#unless bankDataEntry}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right">Schedule Curriculum</label>
                <div class="col-lg-9 col-xl-6">
                  <div class="radio-inline">
                    <label class="radio radio-primary">
                      <input type="radio" name="scheduleFor" value="today" data-cy="schedule-today" checked={{checkedIfScheduledDate "today"}}>
                      <span></span>
                      Today
                    </label>
                    <label class="radio radio-primary">
                      <input type="radio" name="scheduleFor" value="future" data-cy="schedule-future" checked={{checkedIfScheduledDate "future"}}>
                      <span></span>
                      Future Date
                    </label>
                  </div>
                  <div class="row">
                    <input type="text" class="form-control form-control-lg" id="scheduleCurriculumDate" data-cy="schedule-date-picker" value="{{formattedScheduledDate}}">
                  </div>
                </div>
              </div>
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right">Send To</label>
                <div class="col-lg-9 col-xl-6">
                  <div class="radio-inline">
                    <label class="radio radio-primary">
                      <input type="radio" name="sendTo" value="allGroups" data-cy="send-to-all-groups" checked={{checkedIfGroups "empty"}}>
                      <span></span>
                      All Groups
                    </label>
                    <label class="radio radio-primary">
                      <input type="radio" name="sendTo" value="selectGroups" data-cy="send-to-selected-groups" checked={{checkedIfGroups "selected"}}>
                      <span></span>
                      Selected Groups
                    </label>
                  </div>
                  <div class="row">
                    <select multiple class="form-control form-control-lg" id="selectedGroups" data-cy="selected-groups-dropdown">
                      {{#each groups}}
                        <option value="{{_id}}">{{name}}</option>
                      {{/each}}
                    </select>
                  </div>
                </div>
              </div>
            {{/unless}}
            {{#if isNewCurriculum}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Attach PDF(s)</label>
                <div class="col-lg-9 col-xl-6">
                  <label id="curriculumInputLabel" for="curriculumInputFile" class="btn btn-primary font-weight-bolder" data-cy="attach-media-btn">
                    <input type="file" id="curriculumInputFile" multiple style="display:none;" data-cy="attach-media-input"> 
                    <i class="fad fa-cloud-upload"></i> Attach Media/File(s)
                  </label>
                  <span id="curriculumFileLabelSpan" data-cy="media-file-label"></span>
                </div>
              </div>
            {{/if}}
            {{#unless isNewCurriculum}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right">Current Attachments</label>
                <div class="col-lg-9 col-xl-6">
                  <div class="checkbox-list">
                    {{#each mediaFiles}}
                      <label class="checkbox checkbox-primary">
                        <input type="checkbox" name="remove-current-file" value="{{mediaToken}}"/>
                        <span></span>
                        Remove {{mediaName}}
                      </label>
                    {{/each}}
                  </div>
                </div>
              </div>
            {{/unless}}
          </form>
          {{/with}}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary font-weight-bolder mr-2" id="curriculumFormSubmit" data-cy="save-activity">Save</button>
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>
