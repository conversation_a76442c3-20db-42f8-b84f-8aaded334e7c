<template name="curriculum">
  <div class="container">
    <div class="flex-column-fluid ml-lg-8">
      <div class="card card-custom gutter-bs">
        <div class="card-body">
          {{#if showOptions}}
            <div class="btn-group pull-right">
              {{#if curriculum}}
                <a href="/activities/builder/{{curriculumId}}" class="btn btn-primary font-weight-bolder">Manage Theme</a>
              {{else}}
                <button type="button" class="btn btn-primary editCurriculumLink font-weight-bolder">Edit Activity</button>
              {{/if}}
              <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu dropdown-menu-right">
                {{#unless hasCustomization "curriculumBank/activities"}}
                  <span class="dropdown-item clickable-row editCurriculumLink">Edit Activity</span>
                {{/unless}}
                <span class="dropdown-item clickable-row copyToCurriculumLink">Copy To Date</span>
                <span class="dropdown-item clickable-row deleteCurriculumLink">Delete Activity</span>
              </div>
            </div>
          {{/if}}

          <h3 class="text-primary">{{curriculum.headline}}</h3>

          {{#if curriculumThemeId}}
            <b>Theme:</b><br/>
            {{curriculum.findTheme.name}}<br/>
            <br/>
          {{/if}}
      
          <b>Scheduled Date:</b><br/>
          {{scheduledDateFormatted}}<br/>
          <br/>
          {{#if formattedSelectedTypes}}
            <b>{{ getActivitiesTag }}(s):</b> <br/>     	 		
            {{#each type in formattedSelectedTypes}}
              {{type}}
            {{/each}}
            <br/>
            <br/>
          {{/if}}
          {{#if curriculum.message}}
            <b>Notes:</b> <br/>
            {{curriculum.message}}<br/>
            <br/>
          {{/if}}
          {{#if curriculum.materials}}
            <b>Materials:</b> <br/>
            {{curriculum.materials}}<br/>
            <br/>
          {{/if}}
          {{#if curriculum.homework}}
            <b>Homework:</b> <br/>
            {{curriculum.homework}}<br/>
            <br/>
          {{/if}}
          {{#if curriculum.teacherNotes}}
            <b>Teacher Notes:</b> <br/>
            {{curriculum.teacherNotes}}<br/>
            <br/>
          {{/if}}
          {{#if curriculum.findStandards}}
            <b>Standards:</b> <br/>
              {{#each curriculum.findStandards}}
                {{this}}<br/>
              {{/each}}
            <br/>
            <br/>
          {{/if}}
          {{#if curriculum.mediaFiles}}
            <b>Attachments:</b><br/>
            {{#each curriculum.mediaFiles}}
              {{mediaName}} <a href="#" onclick="window.open('{{attachmentHandler mediaUrl}}', '_blank');" style="font-weight:normal;font-size:14px"> (View PDF)</a><br/>
            {{/each}}
            <br/>
          {{/if}}
          <b>Recipients:</b><br/>
          {{#if curriculum.sendToAll}}
            All
          {{else}}
            {{#each curriculum.findRecipientGroups}}
              {{name}}<br/>
            {{/each}}
          {{/if}}
          <br/>
          <br/>
        </div>
      </div>
    </div>
  </div>
</template>
