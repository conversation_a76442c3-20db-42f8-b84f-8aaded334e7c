<template name="curriculumContainer">
  <div class="container">
  {{#if routeContainsHash 'curriculumDefinitionsTab'}}
    {{#if hasCustomization "curriculumBank/management"}}
      {{> curriculumBank}}
    {{else}}
      {{> curriculumDefinitions}}
    {{/if}}
  {{/if}}
  {{#if hasCustomization "curriculumBank/management"}}
    {{#if routeContainsHash 'curriculumThemesTab'}}
      {{> curriculumThemeBankList}}
    {{/if}}
  {{/if}}
  {{#if routeContainsHash 'scheduledCurriculumTab'}}
    {{> curriculumScheduled}}
  {{/if}}
  {{#if routeContainsHash 'configureCurriculumTab'}}
    {{> curriculumConfigure}}
  {{/if}}
  </div>
</template>
