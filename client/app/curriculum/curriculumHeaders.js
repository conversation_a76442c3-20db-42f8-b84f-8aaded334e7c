import { Template } from 'meteor/templating';
import { processPermissions } from '../../../lib/permissions';
import { ReactiveVar } from 'meteor/reactive-var';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './curriculumHeader.html';

Template.curriculumHeader.onCreated(function (){
  let headerLinked = {
    'curriculumDefinitionsTab': true,
    'curriculumThemesTab': false,
    'scheduledCurriculumTab': false,
    'configureCurriculumTab': false
  }
  this.headerLinkClicked = new ReactiveVar(headerLinked);
})
Template.curriculumHeader.helpers({
  showConfigure() {
    return processPermissions({
      assertions: [{ context: "activities", action: "edit" }],
      evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
    });
  },
  routeContainsHashLocal(str) {
    const links = Template.instance().headerLinkClicked.get();
    return links[str];
  }
})
Template.curriculumHeader.events({
  'click .menu-item': function(e, i) {
    const headerLinks = Template.instance().headerLinkClicked.get();
    for (let k of Object.keys(headerLinks)) {
      headerLinks[k] = k === e.currentTarget.id ? true : false;   
    }
    Template.instance().headerLinkClicked.set(headerLinks);
  }
});
