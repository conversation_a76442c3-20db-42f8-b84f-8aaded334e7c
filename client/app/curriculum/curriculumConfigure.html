<template name="curriculumConfigure">
  <div class="flex-row-fluid ml-lg-8">
    <!--begin::Card-->
    <div class="card card-custom gutter-bs">
      <!--Begin::Header-->
      <div class="card-header card-header-tabs-line">
        <div class="card-toolbar">
          <ul class="nav nav-tabs nav-tabs-space-lg nav-tabs-line nav-tabs-bold nav-tabs-line-3x" role="tablist">
              <li class="nav-item mr-3">
                <a class="nav-link active" data-toggle="tab" href="#availableTypesTab">
                  <span class="nav-icon"><i class="fad fa-tags mr-2"></i></span>
                  <span class="nav-text font-weight-bold">Tags</span>
                </a>
              </li>
            <li class="nav-item mr-3">
              <a class="nav-link" data-toggle="tab" href="#availableStandardsTab">
                <span class="nav-icon"><i class="fad fa-th-list mr-2"></i></span>
                <span data-cy="available-standards-tab" class="nav-text font-weight-bold">Available Standards</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
      <!--end::Header-->
      <!--Begin::Body-->
      <div class="card-body px-0">
        <div class="tab-content pt-5">
          <div class="tab-pane active" id="availableTypesTab" role="tabpanel">
            <div class="d-flex flex-column-fluid">
              <div class="container">
                <div class="d-flex flex-column justify-content-center">
                  <h4>Current Curriculum Tags</h4>
                  <div data-cy="curriculum-tags" class="types-tree mt-6"></div>
                  <div class="d-flex flex-row align-items-center justify-content-center my-6">
                    <div data-cy="add-root-btn" class="btn btn-primary btn-sm mr-4 font-weight-bolder" id="btnAddRoot"><i class="fad fa-plus fa-swap-opacity mr-2"></i>Add Root</div>
                    <div data-cy="add-child-btn" class="btn btn-primary btn-sm mr-4 font-weight-bolder" id="btnAddChild"><i class="fad fa-plus fa-swap-opacity mr-2"></i>Add Child</div>
                    <div data-cy="rename-btn" class="btn btn-primary btn-sm mr-4 font-weight-bolder" id="btnRename"><i class="fad fa-pencil mr-2"></i>Rename</div>
                    <div data-cy="delete-btn" class="btn btn-danger btn-sm mr-4 font-weight-bolder" id="btnDelete"><i class="fad fa-swap-opacity fa-minus"></i>Delete</div>
                  </div>
                </div>
              </div>
              
            </div>
          </div>
          <div class="tab-pane" id="availableStandardsTab" role="tabpanel">
            <div class="d-flex flex-column-fluid">
              <div class="container">
                {{#each standards}}
                  <div class="card card-custom gutter-b" data-id="{{_id}}">
                    <div data-cy="standards-table" class="card-body">
                      <div class="row">
                        <div class="d-flex align-items-center col-6">
                          <span data-cy="standard-id" class="font-weight-bolder font-size-h5 text-primary">{{standardId}}</span>
                        </div>
                        <div class="d-flex align-items-center col-3">
                          <div class="d-flex align-items-center justify-content-start">
                            <span class="mr-4">
                              <i class="icon-2x text-bright-blue fad fa-check-circle"></i>
                            </span>
                            <div class="d-flex flex-column text-dark-75">
                              <span class="font-weight-bolder font-size-sm">Benchmark</span>
                              <span data-cy="standard-benchmark" class="font-weight-bolder font-size-h5">{{benchmark}}</span>
                            </div>
                          </div>
                        </div>
                        <div class="d-flex align-items-center col-3">
                          <div class="d-flex align-items-center justify-content-start">
                            <span class="mr-4">
                              <i class="icon-2x text-bright-blue fad fa-info-circle"></i>
                            </span>
                            <div class="d-flex flex-column text-dark-75">
                              <span class="font-weight-bolder font-size-sm">Detail</span>
                              <span data-cy="standard-detail" class="font-weight-bolder font-size-h5">{{detail}}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                {{/each}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
