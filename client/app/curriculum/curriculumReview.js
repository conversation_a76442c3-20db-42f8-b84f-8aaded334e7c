import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Curriculums } from '../../../lib/collections/curriculum';
import { showModal, hideModal } from "../main";
import _ from '../../../lib/util/underscore';
import '../simpleModal/simpleModal.js';
import './_curriculumFormModal';
import './_curriculumBuilderAddActivityModal';
import './curriculumReview.html';
import { sentenceCase } from 'change-case';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AvailableCustomizations } from '../../../lib/customizations';

Template.curriculumReview.onCreated(function () {
    this.curriculumBank = new ReactiveVar({});

    Meteor.callAsync("getCurriculumBankRecordById", this.data._id).then(result => {
        this.curriculumBank.set(result);
    }).catch(error => {
        const message = error.reason || error.message;
        mpSwal.fire("Error", `Error fetching curriculum bank record: ${message}`, "error");
    });
});


Template.curriculumReview.helpers({
  'getDataSource': function(field) {
    const bankObj = Template.instance().curriculumBank.get();
    const data = bankObj[field] || {}
    const renderArr = [];
    const standards = Curriculums.getStandards();
    for (const [key, value] of Object.entries(data)) {
      let fieldType = "textarea";
      let label = sentenceCase(key);
      let displayValues = value;
      if (key == "selectedStandards") {
        displayValues = [];
        for (const val of value) {
          const standardId = val.split("|")
          const standard = _.find(standards, function(i) {
            return i.standardId == standardId?.[1];
          });
          displayValues.push(standard?.benchmark);
        }
      } else if (key == "mediaFiles") {
        displayValues = [];
        fieldType = "links";
        for (const val of value) {
          displayValues.push({name: val.mediaName, link: val.mediaUrl});
        }
      }

      renderArr.push({label, value: displayValues, fieldType})
    }

    if (_.isEmpty(renderArr)) {
      renderArr.push({label: "Note", value: "No Existing Data"})
    }
    return renderArr
  },
});

Template.curriculumReview.events({
    'click #btnApproveCurriculumBank': function (e, i) {
        const _id = this._id
        mpSwal.fire({
            title: "Approval Note",
            showCancelButton: true,
            html: `<div id='frmApprovalNote'>Add an optional note regarding the approval. <br/><br/>
        <textarea name="approvalNote" style="width:100%"></textarea><br/><br/>
        <b>Publish Now?</b> <br/>
        <div class="radio-list">
          <label class="radio radio-primary">
            <input type="radio" name="publish" value="yes" checked>
            <span></span>
            Yes, publish
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="publish" value="no">
            <span></span>
            Set as Draft
          </label>
        </div>
        <b>Edit scheduled activities</b> <br/>
        <div class="mt-4 radio-list">
          <label class="radio radio-primary">
            <input type="radio" name="activityEdit" value="current">
            <span></span>
            Today's activities
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="activityEdit" value="currentFuture" checked>
            <span></span>
            Today's and future activities
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="activityEdit" value="all">
            <span></span>
            All activities
          </label>
        </div>
        </div><br/>
        `,
            preConfirm: function () {
                return new Promise(function (resolve) {
                    resolve([
                        $('#frmApprovalNote textarea[name=approvalNote]').val(),
                        $('#frmApprovalNote input[name=publish]:checked').val(),
                        $('#frmApprovalNote input[name=activityEdit]:checked').val(),
                    ]);
                });
            }
        }).then((result) => {
            if (result.value) {
                const approvalNote = result.value[0];
                const publish = result.value[1];
                const propagate = result.value[2];

                Meteor.callAsync("approveCurriculumBankRecord", _id, approvalNote, publish, propagate).then(() => {
                    FlowRouter.go("/activities#curriculumDefinitionsTab");
                }).catch(error => {
                    mpSwal.fire("Error", error.reason || error.message, "error");
                });
            }
        })
    },

    'click #btnRejectCurriculumBank': function (e, i) {
        const _id = this._id
        mpSwal.fire({
            title: "Rejection Note",
            showCancelButton: true,
            html: `<div id='frmRejectionNote'>Add an optional note regarding the rejection. <br/><br/>
        <textarea name="rejectionNote" style="width:100%"></textarea>`,
            preConfirm: function () {
                return new Promise(function (resolve) {
                    resolve([
                        $('#frmRejectionNote textarea[name=rejectionNote]').val()
                    ]);
                });
            }
        }).then((result) => {
            if (result.value) {
                Meteor.callAsync("rejectCurriculumBankRecord", _id, result.value[0]).then(() => {
                    FlowRouter.go("/activities#curriculumDefinitionsTab");
                }).catch(error => {
                    mpSwal.fire("Error", error.reason, "error");
                });
            }
        })
    },

    'click #btnEditUpdateCurriculumBank': function (e, instance) {
        e.preventDefault();
        const bankObj = instance.curriculumBank.get();
        const _id = bankObj._id;
        const currentOrg = Orgs.current();
        if (currentOrg.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_REQUIRE_THEMES)) {
            showModal(
                "simpleModal",
                {
                    title: "Edit Activity",
                    template: "_curriculumBuilderAddActivityModal",
                    data: {hideCopy: true, pendingCurriculum: bankObj.updateData},
                    onSave: (e, i, formFieldData) => {

                        if (formFieldData.notes) {
                            // backwards compatability
                            formFieldData.message = formFieldData.notes;
                            delete formFieldData.notes;
                        }

                        formFieldData.selectedTypes = $("#inputCurriculumAddActivityTypes").val();
                        formFieldData.selectedStandards = $("#inputCurriculumAddActivityStandards").val();
                        formFieldData.selectedAgeGroup = $("#inputAge").val();

                        const submitCurriculumForm = function () {
                            Meteor.callAsync("updatePendingCurriculumBankRecord", _id, formFieldData).then(result => {
                                if (result) {
                                    Meteor.callAsync("getCurriculumBankRecordById", _id).then(result => {
                                        instance.curriculumBank.set(result);
                                    }).catch(error => {
                                        const message = error.reason || error.message;
                                        mpSwal.fire("Error", `Error fetching curriculum bank record: ${message}`, "error");
                                    });
                                }

                                hideModal("#simpleModal");
                            }).catch(error => {
                                $(e.target).html('Save').prop("disabled", false);
                                mpSwal.fire("Error", error.reason, "error");
                            });
                        }

                        const uploadFile = document.getElementById("curriculumInputFile");

                        if (uploadFile?.files.length > 0) {
                            processFilesAndSend(formFieldData, uploadFile, submitCurriculumForm)
                        } else {
                            submitCurriculumForm();
                        }

                    }
                },
                "#simpleModal"
            );
        } else {
            const submitCurriculumFormModal = function (data) {
                delete data.removeFiles;
                delete data.scheduledDate;
                delete data.selectedGroups;
                Meteor.callAsync("updatePendingCurriculumBankRecord", _id, data).then(result => {
                    if (result) {
                        Meteor.callAsync("getCurriculumBankRecordById", _id).then(result => {
                            instance.curriculumBank.set(result);
                        }).catch(error => {
                            const message = error.reason || error.message;
                            mpSwal.fire("Error", `Error fetching curriculum bank record: ${message}`, "error");
                        });
                    }

                    hideModal("#_curriculumFormModal");
                }).catch(error => {
                    $(e.target).html('Save').prop("disabled", false);
                    mpSwal.fire("Error", error.reason, "error");
                });
            }

            showModal(
                "_curriculumFormModal",
                {
                    onSave: submitCurriculumFormModal,
                    bankDataEntry: true,
                    pendingCurriculum: bankObj.updateData
                },
                "#_curriculumFormModal"
            )
        }
    }
})

function processFilesAndSend ( formFieldData, uploadFile, submitCurriculumForm ) {
	formFieldData.newMediaFile = [];
	let uploadsCompleted = 0;

	_.each(uploadFile.files, (file) => {
		var metaContext = {tokenId: tokenString()};

		var uploader = new Slingshot.Upload("myDocumentUploads", metaContext);

		uploader.send(file, function (error, downloadUrl) {
			if (error) {
				alert (error);
				$(e.target).html('Save').prop("disabled", false);
			}
			else {
				downloadUrl = remapDownloadUrl (downloadUrl);
				var mediaType = file.type;
				
				var uploadedFile = {
					name: file.name,
					mediaUrl: downloadUrl,
					mediaToken: metaContext.tokenId,
					mediaFileType: mediaType,
					mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId
				};
				
				formFieldData.newMediaFile.push(uploadedFile);
				uploadsCompleted++;
				if (uploadsCompleted >= uploadFile.files.length)
					submitCurriculumForm();
			}
		});
	});
}

var tokenString = function() {
  var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
  var string_length = 20;
  var randomstring = '';
  for (var i=0; i<string_length; i++) {
    var rnum = Math.floor(Math.random() * chars.length);
    randomstring += chars.substring(rnum,rnum+1);
  }
  return randomstring;
};
