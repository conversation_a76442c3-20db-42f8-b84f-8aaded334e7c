import { Template } from 'meteor/templating';
import './curriculumBank.js';
import './curriculumDefinitions.js';
import './themeBank/list.js';
import './curriculumScheduled.js';
import './curriculumConfigure.js';
import './curriculumContainer.html';

Template.curriculumContainer.helpers({
	"activeIfSelected": function(tab, opt) {
		const selected = tab || "scheduled";

		return opt == selected ? "active" : "";
	},
})
