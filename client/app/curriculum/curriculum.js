import moment from 'moment-timezone';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs.js';
import _ from '../../../lib/util/underscore';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from "../main";
import './curriculumBankActivityModal.js';
import './_curriculumCopyToFormModal.js';
import './_curriculumFormModal.js';
import './curriculum.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import { offlinePreventCheck } from '../../../mpweb.js';
import { Curriculum } from '../../../lib/collections/curriculum.js';

Template.curriculum.onCreated(function () {
  this.curriculumData = new ReactiveVar(null);
  const self = this;

  const curriculumId = FlowRouter.getParam('_id');

  const fetchCurriculum = () => {
    Meteor.callAsync('getCurriculumById', curriculumId)
      .then((res) => {
        self.curriculumData.set(new Curriculum(res));
      })
      .catch((err) => {
        console.error("[Template] Failed to fetch curriculum:", err);
      });
  };

  fetchCurriculum();

  this.autorun(() => {
    if (Session.get("refreshCurriculum")) {
      fetchCurriculum();
      Session.set("refreshCurriculum", false);
    }
  });
});

Template.curriculum.helpers({
	curriculum() {
		return Template.instance().curriculumData.get();
	},
	curriculumId () {
		const data = Template.instance().curriculumData.get();
		return data ? data.curriculumThemeId : '';
	},
	'scheduledDateFormatted': function() {
		const data = Template.instance().curriculumData.get();
		return data ? moment(data.scheduledDate).format("MM/DD/YYYY") : '';
	},
	'formattedSelectedTypes': function() {
		const data = Template.instance().curriculumData.get();
    	return data?.selectedTypes?.map(t => t.replace("`", " > "));
	},
	'showOptions': function() {
		return processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
		});
	},
	'getActivitiesTag': function() {
		return orgLanguageTranformationUtil.getActivities('tags');
	},
});

Template.curriculum.events({
	'click .editCurriculumLink': function (event, template) {
		if (offlinePreventCheck()) return false;
		const curriculum = template.curriculumData.get(); 
		if (!curriculum) {
			return;
		}
		const org = Orgs.current();
		Session.set("curriculumId", curriculum._id);
		if (org.hasCustomization("curriculumBank/activities")) {
			showModal("curriculumBankActivityModal", { curriculum, showSchedulingFields: true }, "#curriculumBankActivityModal");
		} else {
			showModal("_curriculumFormModal", {}, "#_curriculumFormModal");
		}
	},
	'click .copyToCurriculumLink': function (event, template) {
		if (offlinePreventCheck()) return false;
		const curriculum = template.curriculumData.get(); 
		if (!curriculum) {
			return;
		}
		showModal("_curriculumCopyToFormModal", { id: curriculum._id }, "#_curriculumCopyToFormModal");
	},
	'click .deleteCurriculumLink': function (event, template) {
		if (offlinePreventCheck()) return false;
		const curriculum = template.curriculumData.get(); 
		if (!curriculum) {
			return;
		}
		const curriculumId = curriculum._id;
		return mpSwal.fire({  
			title: "Are you sure?",   
			text: "You will not be able to recover this curriculum once deleted!",   
			icon: "warning",   
			showCancelButton: true,   
			confirmButtonText: "Yes, delete it!",   
		}).then(async result => {
			if (result.value) {
				await Meteor.callAsync("deleteCurriculum", curriculumId);
				FlowRouter.go("/activities#scheduledCurriculumTab");
			}
		});
	},
})
Template.curriculum.onDestroyed(function () {
  Session.set("refreshCurriculum", false);
});
