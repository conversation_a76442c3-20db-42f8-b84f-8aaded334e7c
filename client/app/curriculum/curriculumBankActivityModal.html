<template name="curriculumBankActivityModal">
  <div id="curriculumBankActivityModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" style="width: 100%;height: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="curriculumBankModalLabel">Activity</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          {{#unless curriculum}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Copy Existing Activity</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="select-copy-existing-activity" class="form-control form-control-lg" name="existingActivities" id="inputExistingActivities">
                  <option value="">Select to Search</option>
                  {{#each curriculumInBank}}
                    <option value="{{_id}}">{{sourceData.headline}} {{#if sourceLabel bankId}} ({{sourceLabel bankId}}) {{/if}}</option>
                  {{/each}}
                </select>
                <br/>
                <button data-cy="copy-btn" class="btn btn-primary font-weight-bolder" id="copyActivityFromBank" style="margin-top:6px">Copy</button>
              </div>
            </div>
          {{else}}
            <form id="formCurriculumBankActivity">
              {{#each getLockedFields}}
                {{#if trueIfEq fieldType "links"}}
                  <div class="form-group row">
                    <label class="col-xl-3 col-lg-3 text-right col-form-label">{{label}}</label>
                    <div class="col-lg-9 col-xl-6">
                      {{#each v in value}}
                        <a href="{{v.link}}" target=”_blank”>{{v.name}}</a>
                      {{/each}}
                    </div>
                  </div>
                {{else}}
                  <div class="form-group row">
                    <label class="col-xl-3 col-lg-3 text-right col-form-label">{{label}}</label>
                    <div class="col-lg-9 col-xl-6">
                      <textarea class="form-control form-control-lg" value="{{value}}" disabled></textarea>
                    </div>
                  </div>
                {{/if}}
              {{/each}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Teacher Notes</label>
                <div class="col-lg-9 col-xl-6">
                  <textarea class="form-control form-control-lg" rows="3" placeholder="Teacher Notes" id="inputTeacherNotes">{{curriculum.teacherNotes}}</textarea>
                </div>
              </div>
              {{#if showSchedulingFields}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right">Schedule Curriculum</label>
                  <div class="col-lg-9 col-xl-6">
                    <div class="radio-inline">
                      <label class="radio radio-primary">
                        <input type="radio" name="scheduleFor" value="today" checked={{checkedIfScheduledDate "today"}}>
                        <span></span>
                        Today
                      </label>
                      <label class="radio radio-primary">
                        <input type="radio" name="scheduleFor" value="future" checked={{checkedIfScheduledDate "future"}}>
                        <span></span>
                        Future Date
                      </label>
                    </div>
                    <div class="form-group row col-lg-9 col-xl-6">
                      <input type="text" class="form-control form-control-lg" id="scheduleCurriculumDate" value="{{formattedScheduledDate}}">
                    </div>
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right">Send To</label>
                  <div class="col-lg-9 col-xl-6">
                    <div class="radio-inline">
                      <label class="radio radio-primary">
                        <input type="radio" name="sendTo" value="allGroups" checked={{checkedIfGroups "empty"}}>
                        <span></span>
                        All Groups
                      </label>
                      <label class="radio radio-primary">
                        <input type="radio" name="sendTo" value="selectGroups" checked={{checkedIfGroups "selected"}}>
                        <span></span>
                        Selected Groups
                      </label>
                    </div>
                    <div class="form-group row">
                      <select multiple class="form-control form-control-lg" id="selectedGroups">
                        {{#each groups}}
                          <option value="{{_id}}">{{name}}</option>
                        {{/each}}
                      </select>
                    </div>
                  </div>
                </div>
              {{/if}}
            </form>
          {{/unless}}
        </div>
        <div class="modal-footer">
          <button data-cy="curriculum-bank-activity-form-save" type="button" class="btn btn-primary font-weight-bolder mr-2" id="curriculumBankActivityFormSave">Save</button>
          <button data-cy="curriculum-bank-activity-form-close" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>