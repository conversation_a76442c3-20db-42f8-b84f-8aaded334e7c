import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../../lib/collections/orgs';
import { Curriculum, Curriculums } from '../../../../lib/collections/curriculum';
import { CurriculumBanks } from '../../../../lib/collections/curriculumBank';
import _ from '../../../../lib/util/underscore';
import './detailForm.html';
import Sortable from 'sortablejs';
import $ from 'jquery';

Template.curriculumThemeBankDetailForm.onCreated(function () {
  this.themeBank = new ReactiveDict(this.data.curriculumThemeBankSource);
  this.sortableLists = [];
  this.activeCopyIndex = new ReactiveVar(-1);
  this.activities = new ReactiveVar("");
  this.selectedActivitiesForTheme = new ReactiveVar([]);
});

Template.curriculumThemeBankDetailForm.onRendered(function () {
  // Make the initial activities within each day sortable

  filterActivity(Template.instance())

  const lists = document.querySelectorAll('.sortable-activities-list');
  for (const list of lists) {
    this.sortableLists.push(Sortable.create(list, {
      handle: ".sort-handle",
      draggable: ".sortable-activity",
      direction: 'vertical'
    }));
  }
});

Template.curriculumThemeBankDetailForm.helpers({
  "disabled": function () {
    return Template.instance().data.disabled || false;
  },
  "getStandardNames": function (act) {
    const tempCurriculum = new Curriculum(act);
    return tempCurriculum.findStandards();
  },
  "curriculumInBank": function () {
    return Template.instance().activities.get();
  },
  "availableActivityAgeGroups": function () {
    var ageGroups = ["None"].concat(_.map(Curriculums.activitiesAgeGroups(), c => c.label));
    return ageGroups;
  },
  "showAddDays": function () {
    const enabledDays = Template.instance().themeBank.get("selectedDays") || [];
    const disabled = Template.instance().data.disabled || false;
    return enabledDays.length < 5 && !disabled;
  },
  days() {
    const enabledDays = Template.instance().themeBank.get("selectedDays") || [];
    return enabledDays;
  },
  displayDayNum(index) {
    return parseInt(index) + 1;
  },
  dayActivities(day) {
    const activityDay = Template.instance().selectedActivitiesForTheme.get().filter(item => day.includes(item._id))
    return activityDay.sort((a, b) => day.indexOf(a._id) - day.indexOf(b._id));
  },
  activeCopyForDay(index) {
    return index == Template.instance().activeCopyIndex.get();
  },
  copyActivityToIndex(index) {
    const selections = $("#inputExistingActivities").select2('data'),
      selectedActivityId = selections && selections[0] && selections[0].id;

    const data = CurriculumBanks.findOne(selectedActivityId)
    if (data) {
      const sourceData = data.sourceData;
      sourceData.curriculumBankSourceId = selectedActivityId;
      i.activeCurriculum.set(sourceData);
      setTimeout(function () {
        $("#selectedGroups").multiselect("refresh");
        $("#scheduleCurriculumDate").datepicker({ autoclose: true, todayHighlight: true });
      }, 300)
    };
  },
  formatSelectedTypes(types) {
    return types.map((t) => t.replace("`", " > ")).join(", ");
  },
  'sourceLabel'(bankId) {
		const org = Orgs.current();
		if (org?.hasCustomization("curriculumBank/globalAndLocal")) {
		  return (bankId == org._id) ? "Local" : "Global";			
		}
  }
})

Template.curriculumThemeBankDetailForm.events({
  "change #group-activities-age-group": function (e, i) {
    i.themeBank.set("selectedAgeGroup", $("#group-activities-age-group").val())
    filterActivity(i)
  },
  "click .remove-day": function (e, i) {
    const idx = $(e.currentTarget).attr("data-day");
    const days = i.themeBank.get("selectedDays") || [];
    days.splice(parseInt(idx), 1);
    i.themeBank.set("selectedDays", days);
  },
  "click .btn-add-activity": function (e, i) {
    const idx = $(e.currentTarget).attr("data-day");
    i.activeCopyIndex.set(parseInt(idx));

    setTimeout(function () {
      $("#inputExistingActivities").select2({
        selectionCssClass: "form-control form-control-lg"
      });
    }, 300);
  },
  "click #copyActivityFromThemeBank": function (e, i) {
    const idx = $(e.currentTarget).attr("data-day");
    const selections = $("#inputExistingActivities").select2('data'),
      selectedActivityId = selections && selections[0] && selections[0].id;
    const enabledDays = i.themeBank.get("selectedDays")
    enabledDays[parseInt(idx)].push(selectedActivityId);

    i.themeBank.set("selectedDays", enabledDays);

    // Make sure that the activities are sortable
    setTimeout(() => {
      if (i.sortableLists?.length && i.sortableLists[parseInt(idx)]) {
        i.sortableLists[parseInt(idx)].destroy();
      }
      const list = document.querySelectorAll('.sortable-activities-list')[parseInt(idx)];
      const sortable = Sortable.create(list, {
        handle: ".sort-handle",
        draggable: ".sortable-activity",
        direction: 'vertical'
      });
      i.sortableLists[parseInt(idx)] = sortable;
    }, 500);

    i.activeCopyIndex.set(-1);
  },
  "click .btn-delete-activity": function (e, i) {
    const idx = parseInt($(e.currentTarget).attr("data-day"));
    const id = $(e.currentTarget).attr("data-id");
    const enabledDays = i.themeBank.get("selectedDays");
    if (enabledDays && enabledDays[idx]) {
      enabledDays[idx] = _.without(enabledDays[idx], id);
    }

    i.themeBank.set("selectedDays", enabledDays);
  },
  "click #btnShowAddDays": function (e, i) {
    var currentDays = $(".dayCard").length;

    if (currentDays < 5) {
      mpSwal.fire({
        title: "Add Days",
        html: `<div id='frmAddDays' class="d-flex align-items-center justify-content-center">
        Add Days to the Theme
        <input type="number" class="ml-4 form-control form-control-solid w-auto" name="daysToAdd" step="1" min="1" max="${5 - currentDays}" value="1"></input>
        </div><br/>
          `,
        preConfirm: function () {
          //swal.showValidationError('Server Error!');
          return new Promise(function (resolve) {
            resolve([
              $('#frmAddDays input[name=daysToAdd]').val(),
            ]);
          });
        }
      }).then(result => {
        if (result && result.value && result.value[0]) {
          const elements = parseInt(result.value[0]);
          const existingDays = i.themeBank.get("selectedDays") || [];
          for (let x = 0; x < elements; x++) {
            existingDays.push([]);
          }
          i.themeBank.set("selectedDays", existingDays)

        }
      })
    }
  },
  "click #saveCurriculumThemeBank": function (e, i) {
    e.preventDefault();
    if (i.data.disabled == "true") return;
    const curriculumThemeData = {};
    curriculumThemeData.name = $("#inputThemeName").val();
    curriculumThemeData.description = $("#inputThemeDescription").val();
    curriculumThemeData.selectedAgeGroup = $("#group-activities-age-group").val();
    curriculumThemeData.selectedDays = i.themeBank.get("selectedDays");

    i.data.onSave(curriculumThemeData);
  }
})

function filterActivity(instance) {
  const query = { published: true }
  if (instance.themeBank.get("selectedAgeGroup") && instance.themeBank.get("selectedAgeGroup") !== 'None') {
    query['sourceData.selectedAgeGroup'] = instance.themeBank.get("selectedAgeGroup");
  }
  Meteor.callAsync('getCurriculumBankRecord', query).then(result => {
    instance.activities.set(result.curriculumBankFetchedRecord);
  });
  Meteor.callAsync('getCurriculumBankRecord', { published: true }).then(result => {
    instance.selectedActivitiesForTheme.set(result.curriculumBankFetchedRecord);
  });
}