<template name="curriculumThemeBankCopyModal">
  <div id="curriculumThemeBankCopyModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable" style="width: 100%;height: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Copy Theme</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          {{#if showOrgSelection}}
            <div class="d-flex flex-row my-10 justify-content-center align-items-center">
              <div class="mr-4"><span class="font-size-h4 text-primary font-weight-bold">Select Org(s)</span></div>
              <div class="d-flex">
                {{> announcementOrgsField }}
              </div>
            </div>
          {{/if}}
          <div class="d-flex flex-row justify-content-center">
            <div class="d-flex flex-column align-items-center justify-content-center">
              <div class="mb-4"><span class="font-size-h4 text-primary font-weight-bold">Add up to {{dayLength}} day(s)</span></div>
              <div id="days-picker" class="mb-4" style="width:280px;"></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary font-weight-bolder mr-2" id="curriculumThemeBankCopySubmit">Save</button>
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>