<template name="curriculumThemeBankDetailForm">
  {{#with curriculumThemeBankSource}}
    <form id="curriculumThemeBankForm">
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Name</label>
        <div class="col-lg-9 col-xl-6">
          <input data-cy="theme-name-input" type="text" class="form-control form-control-lg" id="inputThemeName" placeholder="Name" value="{{name}}" disabled={{disabled}}>
        </div>
      </div>
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
        <div class="col-lg-9 col-xl-6">
          <textarea data-cy="theme-description-input" class="form-control form-control-lg" id="inputThemeDescription" disabled={{disabled}}>{{description}}</textarea>
        </div>
      </div>
      <div class="form-group row">
        <label for="group-activities-age-group" class="col-xl-3 col-lg-3 text-right col-form-label">Activities Age Group</label>
        <div class="col-lg-9 col-xl-6">
          <select class="form-control" id="group-activities-age-group" disabled={{disabled}}>
            {{#each value in availableActivityAgeGroups}}
              <option value="{{value}}" {{selectedIfEqual selectedAgeGroup value}}>{{value}}</option>
            {{/each}}
          </select>
        </div>
      </div>
      {{#unless days}}
        <div class="d-flex flex-row align-items-center justify-content-center mt-12">
          <div class="alert alert-custom alert-notice alert-light-info fade show max-w-1000px" id="no-themedays-alert" role="alert">
            <div class="alert-icon"><i class="fad fa-bullhorn"></i></div>
            <div class="alert-text">
              <h4>Your theme currently doesn't have any scheduled days. Add day(s) below to start building your theme.</h4>
            </div>
            <div class="alert-close">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close" id="no-themedays-close-alert">
                <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
              </button>
            </div>
          </div>
        </div>
      {{else}}
        {{#each day in days}}
          {{#let day_idx=@index}}
          <div class="card card-custom gutter-b dayCard">
            <div class="card-body">
              <div class="d-flex flex-row align-items-center justify-content-between mb-4">
                <div>
                  <span class="font-size-h3 text-primary font-weight-bold">Day {{displayDayNum day_idx}}</span>
                </div>
                {{#unless disabled}}
                  <div class="btn btn-danger font-weight-bolder btn-text-white remove-day" data-day="{{day_idx}}">
                    <i class="fad fa-swap-opacity fa-minus mr-2"></i>Remove Day
                  </div>
                {{/unless}}
              </div>
              <div class="separator separator-dashed my-8"></div>
              <div id="activity-{{ @index }}" class="sortable-activities-list">
              {{#each activity in (dayActivities day)}}
                <div class="sortable-activity" data-id="{{ activity._id }}">
                    <div class="row">
                      <div class="col-sm-1 my-auto text-center sort-handle">
                        <i class="fad fad-primary fa-arrows-up-down"></i>
                      </div>
                      <div class="col-sm-4">
                        <span class="font-size-h4 font-weight-bolder">{{activity.sourceData.headline}}</span>
                        <p class="font-size-base">{{activity.sourceData.message}}</p>
                        {{#if activity.sourceData.homework}}
                          <div class="font-size-lg font-weight-bold">Homework:</div>
                          <p class="font-size-base">{{activity.sourceData.homework}}</p>
                        {{/if}}
                        {{#if activity.sourceData.materials}}
                          <div class="font-size-lg font-weight-bold">Materials:</div>
                          <p class="font-size-base">{{activity.sourceData.materials}}</p>
                        {{/if}}
                        {{#if activity.sourceData.teacherNotes}}
                          <div class="font-size-lg font-weight-bold">Teacher Notes:</div>
                          <p class="font-size-base">{{activity.sourceData.teacherNotes}}</p>
                        {{/if}}
                        {{#if activity.sourceData.internalNotes}}
                          <div class="font-size-lg font-weight-bold">Internal Notes:</div>
                          <p class="font-size-base">{{activity.sourceData.internalNotes}}</p>
                        {{/if}}
                        {{#if activity.sourceData.internalLink}}
                          <div class="font-size-lg font-weight-bold">Internal Link:</div>
                          <a href="{{activity.sourceData.internalLink}}" target="_blank" class="font-size-base">View Internal Link</a>
                        {{/if}}
                      </div>
                      <div class="col-sm-5">
                        {{#if activity.sourceData.selectedTypes}}
                          <div class="font-size-lg font-weight-bold">Tag(s):</div>
                          {{formatSelectedTypes activity.sourceData.selectedTypes}}<br/><br/>
                        {{/if}}
                        {{#if activity.sourceData.selectedStandards}}
                        <div class="font-size-lg font-weight-bold">Standard(s):</div>
                          {{#each standard in (getStandardNames activity.sourceData)}}
                            {{standard}}<br/>
                          {{/each}}
                          <br/>
                        {{/if}}
                        {{#if activity.sourceData.mediaFiles}}
                          <div class="font-size-lg font-weight-bold">Attachment(s):</div>
                          {{#each activity.sourceData.mediaFiles}}
                            {{mediaName}} <a href="#" onclick="window.open('{{attachmentHandler mediaUrl}}', '_blank');" style="font-weight:normal;font-size:14px"> (View PDF)</a><br/>
                          {{/each}}
                        {{/if}}
                      </div>
                      {{#unless disabled}}
                        <div class="d-flex justify-content-end align-items-center col-2">
                          <div class="d-flex flex-row align-items-center justify-content-end">
                            <div class="dropdown">
                              <div class="btn btn-icon btn-clean" data-toggle="dropdown" >
                                <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                              </div>
                              <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                <span class="dropdown-item clickable-row btn-delete-activity" data-day="{{day_idx}}" data-id="{{activity._id}}" >Remove</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      {{/unless}}
                    </div>
                    <div class="separator separator-dashed my-8"></div>
                </div>
              {{/each}}
              </div>
              {{#unless activeCopyForDay @index}}
                {{#unless disabled}}
                  <div class="d-flex flex-row align-items-center justify-content-center mt-4">
                    <div class="btn btn-primary font-weight-bolder btn-text-white btn-add-activity" data-day="{{@index}}" disabled={{disabled}}>
                      <i data-cy="add-activity-theme" class="fad fa-swap-opacity fa-plus mr-2"></i>Add Activity
                    </div>
                  </div>
                {{/unless}}
              {{else}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label">Copy Existing Activity</label>
                  <div class="col-lg-9 col-xl-6">
                    <select data-cy="existing-activities" class="form-control form-control-lg" name="existingActivities" id="inputExistingActivities">
                      <option value="">Select to Search</option>
                      {{#each curriculumInBank}}
                        <option value="{{_id}}">{{sourceData.headline}} {{#if sourceLabel bankId}} ({{sourceLabel bankId}}) {{/if}}</option>
                      {{/each}}
                    </select>
                    <br/>
                    <div data-cy="copy-activity-btn" class="btn btn-primary font-weight-bolder" id="copyActivityFromThemeBank" data-day="{{@index}}" style="margin-top:6px">Copy</div>
                  </div>
                </div>
              {{/unless}}
            </div>
          </div>
          {{/let}}
        {{/each}}
      {{/unless}}
      {{#if showAddDays}}
        <div class="d-flex flex-row align-items-center justify-content-center mt-4" id="showAddDaysContainer">
          <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnShowAddDays">
            <i data-cy="add-days-btn" class="fad fa-swap-opacity fa-plus mr-2"></i>Add Day(s)
          </div>
        </div>
      {{/if}}
    </form>
    <input data-cy="save-theme-bank" type="button" class="btn btn-primary font-weight-bolder" id="saveCurriculumThemeBank" value="Save" hidden/>
  {{/with}}
</template>