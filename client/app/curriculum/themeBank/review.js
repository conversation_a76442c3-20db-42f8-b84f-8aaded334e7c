import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './detailForm';
import './review.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.curriculumThemeBankReview.onCreated(function () {
  this.curriculumThemeBank =  new ReactiveVar({});

  Meteor.callAsync("getCurriculumThemeBankRecordById", this.data._id).then(result => {
    this.curriculumThemeBank.set(result);
  }).catch(error => {
    const message = error.reason || error.message;
    mpSwal.fire("Error", `Error fetching curriculum theme bank record: ${message}`, "error");
  });
});

Template.curriculumThemeBankReview.helpers({
  "updateData": function () {
    const bankObj = Template.instance().curriculumThemeBank.get();
    return bankObj?.updateData || {};
  },
  "sourceData": function () {
    const bankObj = Template.instance().curriculumThemeBank.get();
    return bankObj?.sourceData || {};
  },
  "getDisabled": function() {
    return true;
  }
})

Template.curriculumThemeBankReview.events({
  'click #btnApproveCurriculumThemeBank': function(e, i) {
    const _id = this._id
    mpSwal.fire({
      title: "Approval Note",
      showCancelButton:true,
      html: `<div id='frmApprovalNote'>Add an optional note regarding the approval. <br/><br/>
        <textarea name="approvalNote" style="width:100%"></textarea><br/><br/>
        <b>Publish Now?</b> <br/>
        <div class="radio-list">
          <label class="radio radio-primary">
            <input type="radio" name="publish" value="yes" checked>
            <span></span>
            Yes, publish
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="publish" value="no">
            <span></span>
            Set as Draft
          </label>
        </div>
        <b>Edit scheduled themes</b> <br/>
        <div class="mt-4 radio-list">
          <label class="radio radio-primary">
            <input type="radio" name="themeEdit" value="current" checked>
            <span></span>
            Current themes
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="themeEdit" value="currentFuture">
            <span></span>
            Current and future themes
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="themeEdit" value="all">
            <span></span>
            All themes
          </label>
        </div>
        
        </div><br/>
        `,
      preConfirm: function() {
        return new Promise(function (resolve) {
          resolve([
            $('#frmApprovalNote textarea[name=approvalNote]').val(),
            $('#frmApprovalNote input[name=publish]:checked').val(),
            $('#frmApprovalNote input[name=themeEdit]:checked').val(),
          ]);
        });
      }
    }).then((result) => {
      if (result.value) {
        const approvalNote = result.value[0];
        const publish = result.value[1];
        const propagate = result.value[2];

        Meteor.callAsync("approveCurriculumThemeBankRecord", _id, approvalNote, publish, propagate).then(() => {
          FlowRouter.go("/activities#curriculumThemesTab");
        }).catch(error => {
          mpSwal.fire("Error", error.reason || error.message, "error");
        });
      }
    })
  },
  'click #btnRejectCurriculumThemeBank': function(e, i) {
    const _id = this._id
    mpSwal.fire({
      title: "Rejection Note",
      showCancelButton:true,
      html: `<div id='frmRejectionNote'>Add an optional note regarding the rejection. <br/><br/>
        <textarea name="rejectionNote" style="width:100%"></textarea>`,
      preConfirm: function() {
        return new Promise(function (resolve) {
          resolve([
            $('#frmRejectionNote textarea[name=rejectionNote]').val()
          ]);
        });
      }
    }).then((result) => {
      if (result.value) {
        Meteor.callAsync("rejectCurriculumThemeBankRecord", _id, result.value[0]).then(() => {
          FlowRouter.go("/activities#curriculumThemesTab");
        }).catch(error => {
          mpSwal.fire("Error", error.reason, "error");
        });
      }
    })
  },
})
