<template name="curriculumThemeBankReview">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      <div class="d-flex flex-row justify-content-end mb-4">
        <div data-cy="theme-approve-review-btn" class="btn btn-success font-weight-bolder mr-4" id="btnApproveCurriculumThemeBank">
          <i class="fad-regular fad fa-check mr-2 text-white"></i>Approve
        </div>
        <div data-cy="theme-reject-review-btn" class="btn btn-danger font-weight-bolder mr-4" id="btnRejectCurriculumThemeBank">
          <i class="fad-regular fad fa-stop-circle mr-2 fa-swap-opacity text-white"></i>Reject
        </div>
        <!-- <div class="btn btn-primary font-weight-bolder" id="btnEditUpdateCurriculumThemeBank">
          <i class="fad-regular fad fa-pen-square mr-2 fa-swap-opacity text-white"></i>Edit Update
        </div> -->
      </div>
      <div class="card card-custom gutter-b">
        <div class="d-flex flex-row justify-content-between my-4">
          <div class="d-flex flex-column" style="width: 50%;">
            <span class="font-size-h3 ml-12 my-4">Existing Theme</span>
            {{> curriculumThemeBankDetailForm curriculumThemeBankSource=sourceData disabled=getDisabled}}
          </div>
          <div class="d-flex flex-column" style="width: 50%; border-left: 1px dashed var(--primary);">
            <span class="font-size-h3 ml-12 my-4">Proposed Update</span>
            {{> curriculumThemeBankDetailForm curriculumThemeBankSource=updateData disabled=getDisabled}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>