import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';

import _ from '../../../../lib/util/underscore';
import '../../announcements/_announcementOrgsField';
import { hideModal } from "../../main";
import './copyModal.html';
import moment from 'moment';
import $ from 'jquery';

Template.curriculumThemeBankCopyModal.rendered = function() {
  $("#days-picker").datepicker({
		todayHighlight: true,
		multidate: true
	});
}

Template.curriculumThemeBankCopyModal.helpers({
  'dayLength': function() {
    const theme = Template.instance().data.theme;
    return theme?.sourceData?.selectedDays?.length || 0;
  },
  showOrgSelection() {
		const user = Meteor.user();
		const userPerson = (user) ? user.fetchPerson() : {};
		return userPerson.isMasterAdmin();
	},
});

Template.curriculumThemeBankCopyModal.events({
  "click #curriculumThemeBankCopySubmit": function(e,i) {
    e.preventDefault();
    $("#curriculumThemeBankCopySubmit").prop('disabled', true).prop("value", "Copying Theme(s)...");
    const theme = i.data.theme;
    const orgIds = $("#announcementOrgs").val() || [];
    const dates = $("#days-picker").datepicker('getDates');

    let selectedDays = []
    _.each(dates, (sd) => {
			const sdMoment = new moment(sd);
			selectedDays.push(sdMoment.utc().valueOf());
		});
    
    selectedDays = selectedDays.sort(function (a, b) {  return a - b;  });
    Meteor.callAsync("copyCurriculumThemeBank", {orgIds, selectedDays, themeId: theme._id}).then(result => {
      $("#curriculumThemeBankCopySubmit").prop('disabled', false).prop("value", "Save");
      mpSwal.fire("Success", result.message, "success");
      hideModal("#curriculumThemeBankCopyModal");
    }).catch(error => {
      $("#curriculumThemeBankCopySubmit").prop('disabled', false).prop("value", "Save");
      mpSwal.fire("Error", error.reason, "error");
    });
  },
})