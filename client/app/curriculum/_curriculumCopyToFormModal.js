import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_curriculumCopyToFormModal.html';
import { hideModal } from '../main';

Template._curriculumCopyToFormModal.rendered = function() {
	$("#curriculumCopyToDate").datepicker({ autoclose: true, todayHighlight: true });
}

Template._curriculumCopyToFormModal.events({
	'click #curriculumCopyToFormSubmit': function(event, template) {
		event.preventDefault();
		var selectedDate = moment($("#curriculumCopyToDate").val(), "MM/DD/YYYY");

		if (!selectedDate.isValid()) {
			mpSwal.fire("Need a valid date to copy curriculum");
			return;
		}
		$("#curriculumCopyToFormSubmit").prop('disabled', true);

		var curriculumCopyToData = {};
		curriculumCopyToData.sourceId = template.data.id;
		curriculumCopyToData.scheduledDate = moment($("#curriculumCopyToDate").val(), "MM/DD/YYYY").valueOf();
		Meteor.callAsync('copyCurriculum', curriculumCopyToData).then(response => {
			$("#curriculumCopyToFormSubmit").prop('disabled', false);
			hideModal("#_curriculumCopyToFormModal")
			mpSwal.fire("Copy successful!");
		}).catch(error => {
			$("#curriculumCopyToFormSubmit").prop('disabled', false);
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});
