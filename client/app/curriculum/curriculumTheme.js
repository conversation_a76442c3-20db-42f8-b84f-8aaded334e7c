import { Template } from 'meteor/templating';
import _ from '../../../lib/util/underscore';
import './curriculumTheme.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { processPermissions } from '../../../lib/permissions';
import { Curriculum } from '../../../lib/collections/curriculum';

Template.curriculumTheme.onCreated( function() {
	const self=this;
	self.currentActivities = new ReactiveVar([]);
	self.autorun(async() => {
		let currentActivities;
		await Meteor.callAsync('getCurriculumData', { themeId: FlowRouter.current().params._id }, { sort: { scheduledDate: 1, sortOrder: 1 } }).then((res) => {
			currentActivities = res.map((d) => {
				return new Curriculum(d); 
			})
		self.currentActivities.set(currentActivities);
		});
	})
});
Template.curriculumTheme.helpers({
	days() {
		return (this.selectedDays ?? []).sort();
	},
	dayActivities(day) {
		return _.filter(Template.instance().currentActivities.get(), (a) => {
			const scheduledDate = new moment(a.scheduledDate);
			const dayToRender = new moment(day); 
			return scheduledDate.isSame(dayToRender, 'day');
		});
	},
	formatSelectedTypes(types) {
		return types.map( (t) => t.replace("`", " > ")).join(", ");
	},
	formatSelectedStandards(standards) {
		return standards.map( (s) => s.replace("|", " ")).join(',');
	},
	showManageButton() {
		return processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
		});
	}
});

Template.curriculumTheme.events({
	"click #btnManageTheme": (e,i) => {
		e.preventDefault()
		var id = FlowRouter.current().params._id;
		FlowRouter.go(`/activities/builder/${id}`);
	}
})
