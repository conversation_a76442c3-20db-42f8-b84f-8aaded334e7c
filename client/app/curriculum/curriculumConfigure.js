import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Curriculums} from '../../../lib/collections/curriculum.js';
import _ from '../../../lib/util/underscore';
import './curriculumConfigure.html';
import jsTree from 'jstree';
import $ from 'jquery';

Template.curriculumConfigure.helpers({
	'standards': function() {
    	return Curriculums.getStandards();
	},
});

Template.curriculumConfigure.onRendered( function() {

	var self = this;
	Meteor.callAsync("getCurriculumTypes").then(result => {
		const mappedTypes = processTreeData(result);
    
		self.typesTree = $(".types-tree").jstree({
			"core": {
				"themes" : { "stripes" : true, "variant": "large" },
				"check_callback" : true,
				"data": mappedTypes,
			},
			"plugins" : [ "dnd", "wholerow", "sort" ],
      "types" : {
          "default" : {
              "icon" : "fad fa-folder text-primary"
          },
          "file" : {
              "icon" : "fad fa-file text-primary"
          }
      },
		}).on("create_node.jstree", updateTree)
		.on("rename_node.jstree", updateTree)
		.on("move_node.jstree", updateTree)
		.on("delete_node.jstree", updateTree);
	}).catch(error => {
		return mpSwal.fire("Error", "Error loading curriculum types");
	});
});

Template.curriculumConfigure.events( {
	"click #btnSaveTypes": function(e,i) {
		const newValues = processTreeNode($(".types-tree").jstree(true).get_json());
		Meteor.callAsync("updateCurriculumTypes", newValues).then(result => {
			mpSwal.fire("Curriculum Types Updated");
		}).catch(error => {
			mpSwal.fire("Error saving types", error.reason);
		});
	},
	"click #btnAddChild": function(e,i) {
		tree_addchild();
	},
	"click #btnAddRoot": function(e,i) {
		tree_addroot();
	},
	"click #btnRename": function(e,i) {
		tree_rename();
	},
	"click #btnDelete": function(e,i) {
		tree_delete();
	}
});

function processTreeData(nodeList) {
	return _.map(nodeList, (t) => {
		if (typeof t === 'object' && t !== null) {
			let n = {text: t.label};
			if (t.children) n.children = processTreeData(t.children);
			return n;
		}
		else
			return {text: t};
	});
}
function processTreeNode(nodeList) {
	return _.map( nodeList, (node) => {
		let mappedNode = { label: node.text};
		if (node.children && node.children.length > 0) mappedNode.children = processTreeNode(node.children);
		return mappedNode;
	});
}
function tree_addroot() {
	var ref = $('.types-tree').jstree(true);
	sel = ref.create_node("#", {});
	if (sel) ref.edit(sel);
}
function tree_addchild() {
	var ref = $('.types-tree').jstree(true),
		sel = ref.get_selected();
	if(!sel.length) { return false; }
	sel = sel[0];
	sel = ref.create_node(sel, {});
	if(sel) {
		ref.edit(sel);
	}
};
function tree_rename() {
	var ref = $('.types-tree').jstree(true),
		sel = ref.get_selected();
	if(!sel.length) { return false; }
	sel = sel[0];
	ref.edit(sel);
};
function tree_delete() {
	var ref = $('.types-tree').jstree(true),
		sel = ref.get_selected();
	if(!sel.length) { return false; }
	ref.delete_node(sel);
};

function updateTree() {
	$("#btnSaveTypes").prop("disabled", true).html("<i class='fa fa-spinner fa-spin'></i> Saving");
	const newValues = processTreeNode($(".types-tree").jstree(true).get_json());
		Meteor.callAsync("updateCurriculumTypes", newValues).then(result => {
			$("#btnSaveTypes").prop("disabled", false).html("Save Changes");
		}).catch(error => {
			mpSwal.fire("Error saving types", error.reason);
			$("#btnSaveTypes").prop("disabled", false).html("Save Changes");
		});
}
