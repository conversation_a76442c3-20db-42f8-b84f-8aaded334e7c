import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Groups } from '../../../lib/collections/groups';
import { CurriculumTheme } from '../../../lib/collections/curriculumThemes';
import './_curriculumBuilderUpdateThemeDetailsPanel';
import './curriculumBuilder.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.curriculumBuilder.onCreated(function() {
	const self=this;
	self.theme = new ReactiveVar({});
	self.autorun(async () =>{
		let curriculumTheme;
		await Meteor.callAsync('getCurriculumThemeById', FlowRouter.current().params._id).then((res) => {
		  curriculumTheme = new CurriculumTheme(res);
		  self.theme.set(curriculumTheme);
		});
	})
})
Template.curriculumBuilder.helpers({
	showAddDays() {
		return true;
	},
  	theme() {
		const themeId = FlowRouter.current().params._id,
			theme = themeId && Template.instance().theme.get();
		
		return theme;
	},
	isNew() {
		const themeId = FlowRouter.current().params._id;
		return !themeId;
	},	
	groups() {
		return Groups.find({}, {sort:{name:1}});
	},
});

Template.curriculumBuilder.events({
	"click #btn-create-theme": (e,i) => {
		e.preventDefault();
		Meteor.callAsync("insertCurriculumTheme", {
			name: $("input[name='theme-name']").val(),
			description: $("textarea[name='theme-description']").val(),
			selectedGroups: $("#theme-groups").val()
		}).then(result => {
			FlowRouter.go("/activities/builder/" + result);
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});

Template.curriculumBuilder.onRendered( function() {
	$("#theme-groups").multiselect();
})
