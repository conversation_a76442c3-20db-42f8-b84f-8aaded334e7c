import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Curriculum } from '../../../lib/collections/curriculum';
import _ from '../../../lib/util/underscore';
import { showModal, hideModal } from "../main";
import '../simpleModal/simpleModal';
import './curriculumBankActivityModal';
import './_curriculumBuilderUpdateThemeDetailsPanel.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { remapDownloadUrl } from '../../../mpweb';
import Sortable from 'sortablejs';
import moment from 'moment';
import { CurriculumTheme } from '../../../lib/collections/curriculumThemes';

Template._curriculumBuilderUpdateThemeDetailsPanel.onCreated( function() {
	const self=this;
	self.dataLoaded = ReactiveVar(false);
	self.themeBuilder = ReactiveVar("");
	self.currentActivities = new ReactiveVar([]);
	self.theme = new ReactiveVar(null);
	self.autorun(async() => {
		let currentActivities;
		await Meteor.callAsync('getCurriculumData', { themeId: FlowRouter.current().params._id }, { sort: { scheduledDate: 1, sortOrder: 1 } }).then((res) => {
			currentActivities = res.map((d) => {
				return new Curriculum(d); 
			})
		self.currentActivities.set(currentActivities);
		});
		self.theme.set(self.data.theme);
	})
});

async function fetchCurrentActivities(themeId, instance) {
	let currentActivities;
	await Meteor.callAsync('getCurriculumData', { themeId: themeId }, { sort: { scheduledDate: 1, sortOrder: 1 } }).then((res) => {
		currentActivities = res.map((d) => {
			return new Curriculum(d); 
		})
	instance.currentActivities.set(currentActivities);
	});
}

async function fetchTheme(themeId, instance) {
	const theme = await Meteor.callAsync('getCurriculumThemeById', themeId);
	if (!theme) {
		console.error("[fetchTheme] No theme found for id:", themeId);
		return;
	}
	instance.theme.set(new CurriculumTheme(theme));
}

Template._curriculumBuilderUpdateThemeDetailsPanel.onRendered( function() {
	$("#theme-groups").multiselect();
	$("#days-picker").datepicker({
		todayHighlight: true,
		multidate: true
	});
	this.autorun(() => {
		const theme = this.theme.get();
		if (theme) {
		  $("#theme-groups").multiselect("refresh");
		}
	});
	// Make the activities within each day sortable
	const lists = document.querySelectorAll('.sortable-activities-list');
	for (const list of lists) {
		const sortable = Sortable.create(list, {
			handle: ".sort-handle",
			draggable: ".sortable-activity",
			direction: 'vertical',
			onUpdate: function (e) {
				const activities = e.target.querySelectorAll('.sortable-activity');
				const activityIds = Array.from(activities).map(a => a.dataset.id);
				Meteor.callAsync('setCurriculumActivitiesOrder', { activityIds }).catch(error => {
					mpSwal.fire('Error', error.reason, 'error');
				});
			}
		});
	}
});
Template._curriculumBuilderUpdateThemeDetailsPanel.helpers({
	groups() {
		return Groups.find({}, {sort:{name:1}});
	},
	days() {
		const theme = Template.instance().theme.get();

		if (!theme || !theme.selectedDays) {
			return [];
		}

		const enabledDays = theme.selectedDays;
		return enabledDays.sort();
	},
	dayActivities(day) {
		return _.filter(Template.instance().currentActivities.get(), (a) => {
			const scheduledDate = new moment(a.scheduledDate);
			const dayToRender = new moment(day); 
			return scheduledDate.isSame(dayToRender, 'day');
		});
	},
	isGroupSelected(groupId) {
		const theme = Template.instance().theme.get();
		return theme && _.contains(theme.selectedGroups, groupId);
	},
	formatSelectedTypes(types) {
		return types.map( (t) => t.replace("`", " > ")).join(", ");
	},
	formatSelectedStandards(standards) {
		return standards.map( (s) => s.replace("|", " ")).join(',');
	},
	nextWeekdayLabel() {
		const theme = Template.instance().theme.get();

		if (!theme || !theme.selectedDays) {
			return '';
		}

		const nextDay = findNextDay(theme.selectedDays);
		return nextDay && nextDay.format("ddd M/D/YYYY");
	}
});
Template._curriculumBuilderUpdateThemeDetailsPanel.events({
	"click #btn-show-add-days": (e,i) => {
		$("#section-add-days").removeAttr('hidden');
		$("#btn-show-add-days").attr('hidden', '');
	},
	"click #btnAddSelectedDays": async function(e, i) {
		const selectedDates = $("#days-picker").datepicker('getDates'),
			theme = i.theme.get(),
			enabledDays = theme.scheduledDays, 
			addDays = [],
			themeId = FlowRouter.current().params._id;
		_.each(selectedDates, (sd) => {
			const sdMoment = new moment(sd);
			if (!_.find(enabledDays, (ed) => ed.scheduledDate == sdMoment.valueOf())) 
				addDays.push(sdMoment.valueOf());
		});
		Meteor.callAsync("updateCurriculumTheme", {themeId, addDays}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
		await fetchTheme(themeId, i);
		$("#days-picker").datepicker('clearDates');

		$("#section-add-days").attr('hidden', '');
		$("#btn-show-add-days").removeAttr('hidden');
	},
	"click .remove-day": async function(e, i) {
		const selectedDay = $(e.currentTarget).data("day"),
			themeId = FlowRouter.current().params._id;
		Meteor.callAsync("updateCurriculumTheme", {themeId, removeDay: selectedDay}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
		await fetchTheme(themeId, i);
	},
	"click #btnAddNextDay": async function(e,i) {
		const nextDay = findNextDay(i.theme.get().selectedDays),
			addDays = [nextDay.valueOf()],
			themeId = FlowRouter.current().params._id;

		Meteor.callAsync("updateCurriculumTheme", {themeId, addDays}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
		await fetchTheme(themeId, i);
		$("#section-add-days").addClass('hidden');
		$("#show-add-days-container").removeClass('hidden');
	},
	"click .btn-add-activity": function(e, instance) {
		const selectedDate = $(e.currentTarget).data("day"),
			themeId = FlowRouter.current().params._id,
			org = Orgs.current();
		selectedAgeGroup(instance, $("#theme-groups").val());
		let selectedGroups = instance.themeBuilder.get();
		// We divert activity management on the following customization temporarily
		if (org.hasCustomization("curriculumBank/activities")) {
			showModal("curriculumBankActivityModal", {selectedDate, themeId, selectedGroups: selectedGroups, showSchedulingFields: false}, "#curriculumBankActivityModal");
		} else {
			showModal("simpleModal", {
				title: "Add Activity",
				template: "_curriculumBuilderAddActivityModal",
				data: {},
				onSave: (e, modalInstance, formFieldData) => {
					formFieldData.scheduledDate = selectedDate;
					formFieldData.curriculumThemeId = themeId;
					formFieldData.selectedTypes = $("#inputCurriculumAddActivityTypes").val();
					formFieldData.selectedStandards = $("#inputCurriculumAddActivityStandards").val();
					formFieldData.originalCurriculumId = $("#inputOriginalCurriculumId").val();
					formFieldData.selectedAgeGroup = $("#inputAge").val();
	
					const submitCurriculumForm = async function () {
						Meteor.callAsync("insertCurriculum", formFieldData).then(result => {
							hideModal("#simpleModal");
						}).catch((error) => {
							$(e.target).html('Save').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
						await fetchTheme(themeId, instance);
						await fetchCurrentActivities(themeId, instance);
					}
	
					var uploadFile = document.getElementById("curriculumInputFile");
	
					if (uploadFile && uploadFile.files.length > 0) {
						processFilesAndSend( formFieldData, uploadFile, submitCurriculumForm)
	
					} else
						submitCurriculumForm();
	
				}
			}, "#simpleModal");
		}

	},
	"click #btn-update-theme": async function(e, i) {
		e.preventDefault();
		const options = {
			name: $("input[name='theme-name']").val(),
			description: $("textarea[name='theme-description']").val(),
			selectedGroups: $("#theme-groups").val()
		}, themeId = FlowRouter.current().params._id;
		options.themeId = themeId;
		selectedAgeGroup(i, $("#theme-groups").val());
		Meteor.callAsync("updateCurriculumTheme", options).then(result => {
			mpSwal.fire("Success", "Updated Theme", "success");
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error"); 
		});
		await fetchTheme(themeId, i);
	},
	"click .btn-edit-activity": async function (e, instance) {
		let activity;
		const activityId = $(e.currentTarget).data("id"),
			themeId = FlowRouter.current().params._id,
			org = Orgs.current();
		selectedAgeGroup(instance, $("#theme-groups").val());
		let selectedGroups = instance.themeBuilder.get();
		await Meteor.callAsync('getCurriculumById', activityId).then((res) => {
			activity = new Curriculum(res);
		});
		// We divert activity management on the following customization temporarily
		if (org.hasCustomization("curriculumBank/activities")) {
			showModal("curriculumBankActivityModal", { curriculum: activity, selectedGroups: selectedGroups,  showSchedulingFields: false }, "#curriculumBankActivityModal");
		} else {
			showModal("simpleModal", {
				title: "Edit Activity",
				template: "_curriculumBuilderAddActivityModal",
				data: activity,
				onSave: (e, i, formFieldData) => {
					formFieldData.selectedTypes = $("#inputCurriculumAddActivityTypes").val();
					formFieldData.selectedStandards = $("#inputCurriculumAddActivityStandards").val();
					formFieldData.curriculumId = activityId;
	
					formFieldData.removeFiles = $.map($("input[name='remove-current-file']:checked"), (n, i) => { return n.value} )
	
					const submitCurriculumForm = async function () {
						Meteor.callAsync("updateCurriculum", formFieldData).then(result => {
							hideModal("#simpleModal");
						}).catch(error => {
							$(e.target).html('Save').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
						await fetchTheme(themeId, instance);
						await fetchCurrentActivities(themeId, instance);
					}
	
					var uploadFile = document.getElementById("curriculumInputFile");
	
					if (uploadFile && uploadFile.files.length > 0) {
						processFilesAndSend( formFieldData, uploadFile, submitCurriculumForm)

					} else
						submitCurriculumForm();
	
				}
			}, "#simpleModal");
		}
	},
	"click .btn-move-activity"(e,i) {
		const activityId = $(e.currentTarget).data("id"),
			days = {};
		_.each(Template.instance().theme.get().selectedDays, (d) => { days[d] = new moment(d).format("ddd MMM Do, YYYY")});
		mpSwal.fire({title: 'Move Activity',
			input: 'select',
			inputOptions: days,
			inputPlaceholder: 'Select a day',
			showCancelButton: true
		}).then(async (result) => {
			if (result.value) {
				Meteor.callAsync('moveCurriculum', {activityId, scheduledDate: result.value}).then(result => {
					mpSwal.fire("Transfer completed successfully");
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
				await fetchTheme(i.theme.get()._id, i);
				await fetchCurrentActivities(i.theme.get()._id, i);
			}
		});	
	},
	"click .btn-delete-activity"(e,i) {
		const activityId = $(e.currentTarget).data("id");
		mpSwal.fire({
			title: "Are you sure?",
			text: "Deleting this activity will remove it from the theme.",
			type: "warning",
			showCancelButton: true,
			closeOnConfirm:false
		}).then(async (result) => {
			if (result.value) {
				Meteor.callAsync("deleteCurriculum", activityId).then(response => {
					mpSwal.fire("Activity deleted.");
				}).catch(error => {
					mpSwal.fire("Activity deleted.");
				});
				await fetchTheme(i.theme.get()._id, i);
				await fetchCurrentActivities(i.theme.get()._id, i);
			}
		});
	},
	"click #btnDeleteTheme": async function(e,i) {
		let currentActivities;
		await Meteor.callAsync('getCurriculumData', { themeId: i.theme.get()._id }, { sort: { scheduledDate: 1, sortOrder: 1 } }).then((res) => {
			currentActivities = res.map((d) => {
				return new Curriculum(d); 
			})
		});
		if (currentActivities.length > 0)
			return mpSwal.fire("Error", "You must remove all activities before you can delete this theme.", "error");
		mpSwal.fire({
			title: "Are you sure?",
			text: "This will permanently remove this activity theme.",
			type: "warning",
			showCancelButton: true,
			closeOnConfirm: false
		}).then(async result => {
			if (result.value) {
				Meteor.callAsync("deleteCurriculumTheme", {themeId: i.theme.get()._id}).then(result => {
					FlowRouter.go("/activities#scheduledCurriculumTab");
					mpSwal.fire("Theme deleted successfully.");
				}).catch(error => {
					console.log("error deleteCurriculumTheme: ", error);
					mpSwal.fire("Error", "There was a problem deleting your theme: " + error.reason, "error");
				});
				await fetchTheme(i.theme.get()._id, i);
			}
		});
	}
});

function processFilesAndSend ( formFieldData, uploadFile, submitCurriculumForm ) {
	formFieldData.newMediaFile = [];
	let uploadsCompleted = 0;

	_.each(uploadFile.files, (file) => {
		var metaContext = {tokenId: tokenString()};

		var uploader = new Slingshot.Upload("myDocumentUploads", metaContext);

		uploader.send(file, function (error, downloadUrl) {
			if (error) {
				alert (error);
				$(e.target).html('Save').prop("disabled", false);
			}
			else {
				downloadUrl = remapDownloadUrl(downloadUrl);
				console.log("download = " + downloadUrl);
				var mediaType = file.type;
				
				var uploadedFile = {
					name: file.name,
					mediaUrl: downloadUrl,
					mediaToken: metaContext.tokenId,
					mediaFileType: mediaType,
					mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId
				};
				
				formFieldData.newMediaFile.push(uploadedFile);
				uploadsCompleted++;
				if (uploadsCompleted >= uploadFile.files.length)
					submitCurriculumForm();
			}
		});
	});
}

function findNextDay (days) {
	const enabledDays = days || [],
			sortedDays = enabledDays.sort(),
			lastDay = sortedDays ? new moment(_.last(sortedDays)) : new moment().startOf("day");
	let	nextDay;
	if (lastDay.day() == 5 || lastDay.day() == 6) {
		nextDay = lastDay.weekday(8);
	} else
		nextDay = lastDay.add(1, "day");

	return nextDay;
}

var tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};

function selectedAgeGroup(instance, selectedGroup) {
	const query = {
		_id: { '$in':  selectedGroup}
	};

	const selectedAgeGroup = [];
	const selectedGroups =  Groups.find(query).fetch();
	if(selectedGroups.length > 0) {
		for (const groups of selectedGroups) {
			if (groups.activitiesAgeGroup) {
				selectedAgeGroup.push(groups.activitiesAgeGroup);
			}
		}
	}
	instance.themeBuilder.set(selectedAgeGroup);
}
