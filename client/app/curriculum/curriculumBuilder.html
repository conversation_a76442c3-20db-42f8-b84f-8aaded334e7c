<template name="curriculumBuilder">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#if isNew}}
        <div class="card card-custom gutter-b">
          <div class="card-body">
            <div class="row mb-6">
              <span class="font-size-h3 text-primary font-weight-bolder">Create New Theme</span>
            </div>
            <form id="newThemeForm">
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Theme Name</label>
                <div class="col-lg-9 col-xl-6">
                  <input data-cy="theme-name" class="form-control form-control-lg form-control-solid" type="text" name="theme-name"/>
                </div>
              </div>
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
                <div class="col-lg-9 col-xl-6">
                  <textarea data-cy="description-theme" class="form-control form-control-lg form-control-solid" name="theme-description"></textarea>
                </div>
              </div>
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Group(s)</label>
                <div class="col-lg-9 col-xl-6">
                  <select data-cy="select-groups" multiple class="form-control form-control-lg form-control-solid" id="theme-groups">
                    {{#each groups}}
                      <option value="{{_id}}">{{name}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            </form>
            <div class="d-flex flex-row align-items-center justify-content-center mt-4">
              <div data-cy="btn-create-theme" class="btn btn-primary font-weight-bolder btn-text-white" id="btn-create-theme">
                Create
              </div>
            </div>
          </div>
        </div>
      {{else}}
        {{#unless theme}}
          {{> loading}}
        {{else}}
          {{> _curriculumBuilderUpdateThemeDetailsPanel theme=theme}}
        {{/unless}}
      {{/if}}
    </div>
  </div>
</template>
