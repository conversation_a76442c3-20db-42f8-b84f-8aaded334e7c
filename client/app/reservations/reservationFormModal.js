import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { Reservations } from '../../../lib/collections/reservations';
import _ from '../../../lib/util/underscore';
import './reservationFormModal.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from "moment-timezone";
import {calculateReservationWeeklyHours} from "./reservationsWeeklyHours";
import { AvailableCustomizations } from '../../../lib/customizations';
import { getPeopleById, getPeopleData } from '../../services/peopleMeteorService';
import { AVAILABLE_WEEKDAYS } from '../../../lib/constants/enrollmentConstants';
import { People, Person } from '../../../lib/collections/people';
import { Groups } from '../../../lib/collections/groups';
import $ from 'jquery';
import { hideModal, showModal } from '../main';
import { AvailableActionTypes, AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import '../people/_billingTab';
import { processPermissions } from '../../../lib/permissions';

const isProcessing = new ReactiveVar(false);
const dayMap = {
	1: 'Mon',
	2: 'Tue',
	3: 'Wed',
	4: 'Thu',
	5: 'Fri'
};

var repeatingReservation;
var showLinkedBillingPlan;

Template.reservationFormModal.onCreated(function() {
	const subsCache = new SubsCache(-1, -1);
	subsCache.subscribe('theReservations',{reservationId: Template.instance().data.reservationId});
	const org = Orgs.current();
	const orgHasWeekends = org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);
	const forceLinkedPlan = org.hasCustomization(AvailableCustomizations.SCHEDULE_TYPES_FORCED_LINK_TO_PLANS);
	const cancellationReasons = org.cancellationReasons?.sort((a, b) => (a.order || 0) - (b.order || 0)) ?? [];
	// BUGS-2359 maybe the method call is taking an extraordinarily long time to return on prod? Lets try and pull the data on the client since its just org data.
	this.cancellationReasons = new ReactiveVar(cancellationReasons);
	this.targetGroupChanged = new ReactiveVar(false);
	this.targetGroup = new ReactiveVar(reservation()?.groupId || "");
	this.scheduleTypes = new ReactiveVar(Orgs.current().getScheduleTypes());
	this.reservationType = new ReactiveVar("");
	this.repeatingReservation = new ReactiveVar("dnr");
	repeatingReservation = this.repeatingReservation;
	this.hasScheduledEndDate = new ReactiveVar(false);
	this.recurrenceParent = new ReactiveVar(false);
	this.startDate = new ReactiveVar();
	this.endDate = new ReactiveVar();
	this.showLinkedBillingPlan = new ReactiveVar(!this.data.reservationId && forceLinkedPlan);
	showLinkedBillingPlan = this.showLinkedBillingPlan;
	this.reservationStartDate = new ReactiveVar(0);
	this.reservationEndDate = new ReactiveVar(0);
	this.reservationStartTime = new ReactiveVar('');
	this.reservationEndTime = new ReactiveVar('');
	this.recurringDays = new ReactiveVar([]);
	this.originalRecurringDays = new ReactiveVar([]);
	this.totalReservationsWeeklyHours = new ReactiveVar(0);
	this.currentReservationWeeklyHours = new ReactiveVar(0);
	this.editingReservation = new ReactiveVar(this.data.reservationId ? true : false);
	this.previousScheduleType = null;
	this.plansToRemove = null;
	const defaultAvailableDays = {
		1: true,
		2: true,
		3: true,
		4: true,
		5: true,
	}

	if (orgHasWeekends) {
		defaultAvailableDays[6] = true;
		defaultAvailableDays[0] = true;

		dayMap[6] = 'Sat';
		dayMap[0] = 'Sun';
	}

	this.availableDays = new ReactiveVar(defaultAvailableDays);
	this.processAvailableDays = new ReactiveVar(true);
	this.hasRegistrationFlow = org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);

	isProcessing.set(false);
  this.repeatingReservation.set('repeats');
	this.availablePeopleQuery = new ReactiveVar();
	this.availablePeopleData = new ReactiveVar();
	this.availablePeopleQueryCheck = new ReactiveVar();
	this.personIdPlanButton = new ReactiveVar();
	this.personPlanButton = new ReactiveVar();
	this.reservationPersonId = new ReactiveVar();
	this.reservationPerson = new ReactiveVar();

	const person = People.findOne(profileScreenPersonId(), { fields: { defaultGroupId: 1 } });
	this.defaultGroupId = new ReactiveVar(person?.defaultGroupId)

	if (!person){
		Meteor.call('getChildDefaultGroupId', reservation().selectedPerson, (err, response) => {
			this.defaultGroupId.set(response.defaultGroupId);

		})
	}

	this.allGroups = new ReactiveVar();
	this.allGroups.set(Groups.find({}, {
		fields: {
			_id: 1,
			name: 1,
			scheduleTypeToPlans: 1
		},
		sort: {name: 1}
	}).fetch());

	this.autorun(() => {
		const query = this.availablePeopleQuery.get();
		if (JSON.stringify(query) !== this.availablePeopleQueryCheck.get()) {
			this.availablePeopleQueryCheck.set(JSON.stringify(query));
			getPeopleData(query, {sort: {"firstName" : 1, "lastName" : 1}}).then(res => {
				this.availablePeopleData.set(res);
			}).catch(err => {
				console.log(err);
			});
		}

		const personId = this.personIdPlanButton.get();
		if (personId) {
			getPeopleById(personId).then(res => {
				if (res) {
					this.personPlanButton.set(res);
				}
			}).catch(err => {
				console.log(err);
			});
		}
		const reservationPersonId = this.reservationPersonId.get();
		if (reservationPersonId) {
			getPeopleById(reservationPersonId).then(res => {
				if (JSON.stringify(res) !== JSON.stringify(this.reservationPerson.get())) {
					this.reservationPerson.set(res);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	});
	this.currentReservation = reservation();
});
Template.reservationFormModal.helpers({
	'isDisabled': function() {
		return isProcessing.get() ? 'disabled' : '';
	}
})

Template.reservationFormModal.events({
	'change #targetGroup': function () {
		if (!Template.instance().showLinkedBillingPlan.get() && Template.instance().editingReservation.get()) {
			Template.instance().showLinkedBillingPlan.set(true);
		}
		Template.instance().targetGroupChanged.set(true);
		Template.instance().targetGroup.set($("#targetGroup").val());
	},
	'click #editEventSeries': function (event, i) {
		const reservationId = Template.instance().recurrenceParent.get()
		setTimeout(function () {
			showModal("reservationFormModal", {reservationId}, "#reservationFormModal");
		}, 500);
		hideModal("#reservationFormModal");

	},
	'click .createReservation': async function (event) {
		event.preventDefault();

		if(isProcessing.get()){
			return;
		}

		// Store template instance in a variable to use in Promise callbacks
		const template = Template.instance();
		const allGroups = template.allGroups.get();
		const defaultGroupId = template.defaultGroupId.get();
		const editingReservation = template.editingReservation.get();

		isProcessing.set(true);

		const reservationData = {};
		const timezone = Orgs.current().getTimezone();
		const scheduledDateRaw = $("#scheduleReservationDate").val();

		if(scheduledDateRaw === ""){
			mpSwal.fire("Error", `Please select a date`, "error");
			isProcessing.set(false);
			return;
		}

		if ($("#enrollmentDateField").val() === ""){
			isProcessing.set(false);
			return mpSwal.fire("Error", "A first invoice date must be selected for a linked schedule entry.", "error");
		}

		reservationData.scheduledDate = moment.tz(scheduledDateRaw, "MM/DD/YYYY", timezone).valueOf() || null;

		const scheduledTime = moment.tz($("#scheduledTime").val(), ["h:mm a", "HH:mm"], timezone);
		if (scheduledTime !== "" && scheduledTime.isValid()) {
			reservationData.scheduledTime = scheduledTime.format("h:mm a");
		}

		const scheduledEndTime = moment.tz($("#scheduledEndTime").val(), ["h:mm a", "HH:mm"], timezone) || null;
		if (scheduledEndTime !== "" && scheduledEndTime.isValid()) {
			reservationData.scheduledEndTime = scheduledEndTime.format("h:mm a");
		}

		reservationData.selectedPerson = $("#selectedPerson").val();
		reservationData.reservationType = reservation().reservationType;
		reservationData.scheduleType = $("#scheduleType").val();
		if (!reservationData.selectedPerson) {
			isProcessing.set(false);
			return mpSwal.fire({ title: 'Person Required' });
		}

		if (template.hasScheduledEndDate.get() && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
			reservationData.cancellationReasonId = document.getElementById("cancellationReasonId").value;
			if (!reservationData.cancellationReasonId) {
				isProcessing.set(false);
				return mpSwal.fire({
					title: 'Cancellation Reason Required'
				});
			}
		}


		const groupId = $("#targetGroup").val();
		if (groupId) {
			reservationData.groupId = groupId;
		}

		const reservationId = template.data.reservationId
		let priorOpenReservations;

		const changeDefaultGroup = await setDefaultGroupFlag(reservationData, template);

		if (repeatingReservation.get() === 'repeats') {
			let selectedDays = [];
			AVAILABLE_WEEKDAYS.forEach(day => {
				if ($("input[id=recurs" + day + "]").prop("checked")) selectedDays.push(day.toLowerCase());
			});

			if (selectedDays.length === 0) {
				isProcessing.set(false);
				return mpSwal.fire("Error", "You need to select at least one day of the week for a recurring item.", "error");
			}

			reservationData.recurringFrequency = $("input[name=frequency]").val();
			reservationData.recurringDays = selectedDays;

			const scheduledEndDateRaw = $("#scheduleReservationEndDate").val();
			if ($("#setEndDate").is(":checked")) {
				reservationData.scheduledEndDate = moment.tz(scheduledEndDateRaw, "MM/DD/YYYY", timezone).valueOf();
			} else {
				reservationData.scheduledEndDate = null;
			}
			const poQuery = {
				recurringFrequency: {"$exists" : true},
				scheduledEndDate: null,
				scheduledDate: {"$lt": reservationData.scheduledDate},
				recurringDays: {"$in": selectedDays},
			};

			if (reservationId) {
				poQuery._id = {"$ne": reservationId};
				poQuery.selectedPerson = reservationData.selectedPerson;
			} else {
				poQuery.selectedPerson= {"$in":reservationData.selectedPerson};
			}

			if (reservationData.scheduleType) {
				poQuery.scheduleType = reservationData.scheduleType;
			}

			priorOpenReservations = Reservations.find(poQuery).fetch();

			if (showLinkedBillingPlan.get()) {
				const groupIdToUse = reservationData.groupId === "default" ? defaultGroupId : reservationData.groupId;
				const selectedGroup = allGroups.find(group => group._id === groupIdToUse);
				const hasPlans = selectedGroup?.scheduleTypeToPlans?.[reservationData.scheduleType]?.length > 0 || false;

				const plan = $("select[name='plan']").val();
				const formAllocations = $("input[name='allocations']").val();
				const allocations = ((formAllocations || "") != "") ? JSON.parse(formAllocations) : null;
				const enrollment_date = scheduledDateRaw;
				const expiration_date = reservationData.scheduledEndDate && scheduledEndDateRaw;
				const override_rate = $("input[name=override_rate]").val();
				const errorMessageExtension = 'No billing plans available - Update your configuration in Groups > Billing Plans';
				const baseMessage = 'A plan must be selected for a linked schedule entry';
				if (!hasPlans || !plan || plan === "") {
					isProcessing.set(false);
					return mpSwal.fire(
						"Error",
						!hasPlans ? errorMessageExtension : baseMessage,
						"error"
					);
				}

				reservationData.linkedPlan = {
					plan,
					allocations,
					enrollment_date,
					expiration_date,
					override_rate
				}

				if (editingReservation) {
					const personId = profileScreenPersonId();
					const $button = $(`.btnUnlinkPlan[reservation-id="${reservationId}"]`);

					if ($button.length) {
						$button.removeData();

						const planId = $button.data("id");
						const createdAt = $button.data("created-at");

						Meteor.callAsync("removeBillingPlan", {personId, planId, createdAt});
					}
				}
			}
		}

		const generatedFromBillingCharge = $("select[name='plan']").val()
		if(generatedFromBillingCharge){
			reservationData.generatedFromBillingCharge = generatedFromBillingCharge
		}

		const successAction = async function(result, event) {
			Session.set("reservationId", null);
			const stayOpen = event ? $(event.currentTarget).attr("data-stayopen") : null;

			if (changeDefaultGroup) {
				const payload = {
					personId: reservationData.selectedPerson,
					defaultGroupId: reservationData.groupId
				}
				console.log("before updateDefaultGroupId ==>")
				await Meteor.callAsync('updateDefaultGroupId', payload);
			}

			if (result && result.responseMessage) {
				if (_.flatten([reservationData.selectedPerson]).length === 1 && result.allowOverlapOverride) {
					mpSwal.fire({
						title: "Overlapping Schedule Requested",
						text: result.responseMessage + " Overlapped schedules can cause unintended consequences to FTE counts. Do you want to override?",
						showCancelButton: true,
						confirmButtonText: "Yes",
						cancelButtonText: "No"
					}).then(result => {
						if (result.value) {
							reservationData.overrideOverlap = true;
							callReservationCreateOrUpdate();
						}
					})
				} else {
					mpSwal.fire("Result", result.responseMessage, "warning");
				}
			}

			if (stayOpen && stayOpen === "true") {
				resetReservationForm(FlowRouter.current().path.includes("/people/"));
				isProcessing.set(false);
			} else {
				hideModal("#reservationFormModal");
			}
		}

		const callReservationCreateOrUpdate = function() {
			if (reservationId) {
				reservationData.reservationId = reservationId;
				Meteor.callAsync('updateReservation', reservationData).then(async result => {
					await successAction(result);
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
					isProcessing.set(false);
				});
			} else {
				Meteor.callAsync('insertReservation', reservationData).then(async result => {
					await successAction(result, event);
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
					isProcessing.set(false);
				});
			}
		}

		const plansToRemove = template.plansToRemove;
		const handlePlanRemovalAndContinue = function() {
			if (plansToRemove && plansToRemove.length > 0) {
				Promise.all(
					plansToRemove.map(planData =>
						Meteor.callAsync("removeBillingPlan", planData)
							.catch(error => {
								mpSwal.fire("Error", `Failed to remove billing plan: ${error.reason || error.message}`, "error");
								return Promise.reject(error);
							})
					)
				).then(() => {
					callReservationCreateOrUpdate();
				}).catch(error => {
					isProcessing.set(false);
				});
			} else {
				callReservationCreateOrUpdate();
			}
		};

		if (priorOpenReservations && priorOpenReservations.length > 0) {
			mpSwal.fire({
				title: "Prior Open Schedule Entries",
				text: "There are prior schedule entries that must be ended before this new entry can be saved. Set the close date for the prior entries to the date before this new entry?",
				showCancelButton: true,
				confirmButtonText:"Yes",
				cancelButtonText:"No"
			}).then(result => {
				if (result.value) {
					reservationData.closePriors = true;
					handlePlanRemovalAndContinue();
				}
			})
		} else {
			handlePlanRemovalAndContinue();
		}
	},
	'change #selectedType': function() {
		var newType = $("#selectedType").val();
		Template.instance().reservationType.set(newType);
	},
	'change #repeatSelect': function () {
		var newSelect = $("#repeatSelect").val();
		Template.instance().repeatingReservation.set(newSelect);
	},
	'change #scheduleReservationDate': function(event, template) {
		const startDate = moment.tz($("#scheduleReservationDate").val(), "MM/DD/YYYY", Orgs.current().getTimezone()).valueOf();
		template.reservationStartDate.set(startDate);

		const totalHours =  template.totalReservationsWeeklyHours.get();
		const previousReservationWeeklyHours =  template.currentReservationWeeklyHours.get();

		const currentReservation = {
			scheduledDate: startDate,
			scheduledEndDate: template.reservationEndDate.get(),
			scheduledEndTime: template.reservationEndTime.get(),
			scheduledTime: template.reservationStartTime.get(),
			selectedPerson: template.data.selectedReservationPersonId,
			orgId: Orgs.current()._id,
			recurringDays: template.recurringDays.get()
		};

		updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template);
	},
	"click #btnAddLinkedBillingPlan": function(e, i) {
		e.preventDefault();
		i.showLinkedBillingPlan.set(true);
	},
	"click #btnCloseLinkedPlan": function(e, i){
		e.preventDefault();
		i.showLinkedBillingPlan.set(false)
	},
	'change #scheduleReservationEndDate': function(event, template) {
		const endDate =  moment.tz($("#scheduleReservationEndDate").val(), "MM/DD/YYYY", Orgs.current().getTimezone()).valueOf();
		template.reservationEndDate.set(endDate);

		const totalHours =  template.totalReservationsWeeklyHours.get();
		const previousReservationWeeklyHours =  template.currentReservationWeeklyHours.get();

		const currentReservation = {
			scheduledDate: template.reservationStartDate.get(),
			scheduledEndDate: endDate,
			scheduledEndTime: template.reservationEndTime.get(),
			scheduledTime: template.reservationStartTime.get(),
			selectedPerson: template.data.selectedReservationPersonId,
			orgId: Orgs.current()._id,
			recurringDays: template.recurringDays.get()
		};

		updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template);
	},
	'change #scheduledTime': function(event, template) {
		const startTime = moment.tz($("#scheduledTime").val(),["h:mm a","HH:mm"], Orgs.current().getTimezone());
		if (startTime !== "" && startTime.isValid()) {
			template.reservationStartTime.set(startTime.format("h:mm a"));

			const totalHours =  template.totalReservationsWeeklyHours.get();
			const previousReservationWeeklyHours =  template.currentReservationWeeklyHours.get();

			const currentReservation = {
				scheduledDate: template.reservationStartDate.get(),
				scheduledEndDate: template.reservationEndDate.get(),
				scheduledEndTime: template.reservationEndTime.get(),
				scheduledTime: startTime.format("h:mm a"),
				selectedPerson: template.data.selectedReservationPersonId,
				orgId: Orgs.current()._id,
				recurringDays: template.recurringDays.get()
			};

			updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template);
		}
	},
	'change #scheduledEndTime': function(event, template) {
		const endTime = moment.tz($("#scheduledEndTime").val(),["h:mm a","HH:mm"], Orgs.current().getTimezone());
		if (endTime !== "" && endTime.isValid()) {
			template.reservationEndTime.set(endTime.format("h:mm a"));
			const totalHours =  template.totalReservationsWeeklyHours.get();
			const previousReservationWeeklyHours =  template.currentReservationWeeklyHours.get();

			const currentReservation = {
				scheduledDate: template.reservationStartDate.get(),
				scheduledEndDate: template.reservationEndDate.get(),
				scheduledEndTime: endTime.format("h:mm a"),
				scheduledTime: template.reservationStartTime.get(),
				selectedPerson: template.data.selectedReservationPersonId,
				orgId: Orgs.current()._id,
				recurringDays: template.recurringDays.get()
			};

			updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template);
		}
	},
	"change input[name=recurs]": function(event, template) {
		let selectedDays = [];
		_.each(AVAILABLE_WEEKDAYS, (day) => {
			if ($("input[id=recurs" + day + "]").prop("checked")) selectedDays.push(day.toLowerCase());
		});
		template.recurringDays.set(selectedDays);

		const totalHours =  template.totalReservationsWeeklyHours.get();
		const previousReservationWeeklyHours =  template.currentReservationWeeklyHours.get();

		const currentReservation = {
			scheduledDate: template.reservationStartDate.get(),
			scheduledEndDate: template.reservationEndDate.get(),
			scheduledEndTime: template.reservationEndTime.get(),
			scheduledTime: template.reservationStartTime.get(),
			selectedPerson: template.data.selectedReservationPersonId,
			orgId: Orgs.current()._id,
			recurringDays: selectedDays
		}

		updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template)
	},
	'click #setEndDate': function(event, template) {
		var active = $("#setEndDate").is(":checked");
		template.hasScheduledEndDate.set(active);
		if (!active) {
			const endDate = null;
			template.reservationEndDate.set(endDate);

			const totalHours = template.totalReservationsWeeklyHours.get();
			const previousReservationWeeklyHours = template.currentReservationWeeklyHours.get();

			const currentReservation = {
				scheduledDate: template.reservationStartDate.get(),
				scheduledEndDate: endDate,
				scheduledEndTime: template.reservationEndTime.get(),
				scheduledTime: template.reservationStartTime.get(),
				selectedPerson: template.data.selectedReservationPersonId,
				orgId: Orgs.current()._id,
				recurringDays: template.recurringDays.get()
			}

			updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template)
		}
	},
	'change #scheduleType': function(event, template) {
		const $scheduleType = $("#scheduleType");

		if (!template.previousScheduleType) {
			template.previousScheduleType = reservation().scheduleType || $scheduleType.find('option:first').val();
		}

		const previousValue = template.previousScheduleType;
		const newValue = event.currentTarget.value;

		if (template.editingReservation.get()) {
			const reservationId = template.data.reservationId;
			const person = template.personPlanButton.get() || template.reservationPerson.get();

			const linkedPlans = person?.billing?.enrolledPlans?.filter(ep => ep.reservationId === reservationId) || [];
			const hasLinkedPlan = linkedPlans.length > 0;

			if (hasLinkedPlan) {
				const pendingNewValue = newValue;

				mpSwal.fire({
					title: "Warning",
					text: "By changing the schedule type, you will also remove the billing plan linked to this schedule. Would you like to continue?",
					icon: "warning",
					showCancelButton: true,
					confirmButtonText: "Yes",
					cancelButtonText: "No"
				}).then(result => {
					if (result.value) {
						$scheduleType.val(pendingNewValue);
						template.previousScheduleType = pendingNewValue;

						template.plansToRemove = linkedPlans.map(plan => ({
							personId: profileScreenPersonId() || person._id,
							planId: plan._id,
							createdAt: plan.createdAt
						}));

						if (!template.showLinkedBillingPlan.get() && template.editingReservation.get()) {
							template.showLinkedBillingPlan.set(true);
						}
						checkCapacityAvailability(template);
					} else {
						$scheduleType.val(previousValue);
					}
				});

				$scheduleType.val(previousValue);
				return false;
			}
		}

		template.previousScheduleType = newValue;
		if (!template.showLinkedBillingPlan.get() && template.editingReservation.get()) {
			template.showLinkedBillingPlan.set(true);
		}
		checkCapacityAvailability(template);
	}
});

/**
 * Determines if default group flag should be set based on person and schedule data
 * @param {Object} data - The reservation data containing selectedPerson and scheduling info
 * @param {Object} template - The template instance containing reactive variables
 * @returns {Promise<boolean>} Whether the default group should be changed
 */
const setDefaultGroupFlag = async function (data, template) {
	// Only run when editing single user
	if (typeof data.selectedPerson !== 'string') {
		return false;
	}

	let person = await getPeopleById(data.selectedPerson);
	person = new Person(person);
	const defaultId = person.getChildDefaultGroup()?._id;
	if (!defaultId) {
		return false;
	}

	const now = new moment().unix();
	const startDate = new moment(data.scheduledDate).unix();
	const currentResGroup = person.getChildCurrentSchedules().find(schedule =>
		schedule.groupId === person.getChildDefaultGroup()._id &&
		schedule.scheduleType === data.scheduleType
	);

	return !(!currentResGroup || !template.targetGroupChanged.get() || now < startDate);
}

const reservation = function () {
	if (Template.instance().currentReservation) {
		return Template.instance().currentReservation;
	}
	const reservationId = Template.instance().data.reservationId,
		selectedPersonId = Template.instance().data.selectedReservationPersonId,
		reservationType = Template.instance().data.personType || "person",
		timezone = Orgs.current().getTimezone();

	if (!reservationId) {
		const times = Orgs.current().getScheduleTypes().map(({endTime, startTime}) => {
		  return { endTime, startTime };
		});

		const earlier = times.reduce((prev, next) => {
		  const { startTime } = next;
		  if (!( typeof startTime === 'string' || startTime instanceof String)) return prev;
		  const transformed = new moment.tz(startTime, 'hh:mm a', timezone).valueOf();
		  if (transformed < prev) return transformed;
		  return prev;
		}, Number.POSITIVE_INFINITY);

		const latest = times.reduce((prev, next) => {
		  const { endTime } = next;
		  if (!( typeof endTime === 'string' || endTime instanceof String)) return prev;
		  const transformed = new moment.tz(endTime, 'hh:mm a', timezone).valueOf();
		  if (transformed > prev) return transformed;
		  return prev;
		}, Number.NEGATIVE_INFINITY);
			return {
				scheduledDate: new moment.tz(timezone).startOf('day').valueOf(),
				selectedPerson: selectedPersonId,
				reservationType,
				recurringFrequency: 1,
		  recurringType: 'weekly',
		  scheduledTime: earlier !== Number.POSITIVE_INFINITY ? new moment(earlier).format('hh:mm a') : '07:00 am',
		  scheduledEndTime: latest !== Number.NEGATIVE_INFINITY ? new moment(latest).format('hh:mm a') : '06:00 pm',
		  recurringDays: [
			'mon',
			'tue',
			'wed',
			'thu',
			'fri'
		  ],
			};
	} else {
		return Reservations.findOne(reservationId);
	}
}

var availablePeople = function (reservationType) {
	const selectedPersonId = Template.instance().data.selectedReservationPersonId || reservation()?.selectedPerson,
		selectedPerson = selectedPersonId && People.findOne(selectedPersonId);

	const existingRes = (Template.instance().data.reservationId) ? reservation() : null;
	const parsedReservationType = (existingRes) ? existingRes.reservationType : (reservationType || Template.instance().data.personType || "person");
	const existingReservationPersonId =  reservation() ? reservation().selectedPerson : Session.get("selectedReservationPersonId");
	if (existingReservationPersonId !== Template.instance().reservationPersonId.get()) {
		Template.instance().reservationPersonId.set(existingReservationPersonId);
	}
	const existingReservationPerson = Template.instance().reservationPerson.get();
	var query = {type: parsedReservationType,
		orgId: Meteor.user()["orgId"],
	};

	if (!existingReservationPerson?.inActive) query["inActive"] = {"$ne": true};

	Template.instance().availablePeopleQuery.set(query);

	var outs  = Template.instance().availablePeopleData.get();
	_.each(outs, (p) => { 
		if ( ( reservation() && reservation().selectedPerson == p._id) || 
			  ( !reservation() && Session.get("selectedReservationPersonId") ==  p._id ) ) 
				p.selected= true;
	});

	return outs;
}

Template.reservationFormModal.rendered = function () {
	var self = this;

	var currentReservation = reservation();
	let repeatSelect = 'repeats';
	if (Template.instance().data.reservationId) {
		self.currentReservationWeeklyHours.set(calculateReservationWeeklyHours(currentReservation));
		repeatSelect = currentReservation?.recurringFrequency ? 'repeats' : 'dnr';
	}
	const timezone = Orgs.current().getTimezone();
	const currentScheduleType = currentReservation?.scheduleType || $("#scheduleType").find('option:first').val();
	this.previousScheduleType = currentScheduleType;
	var reservationType = (currentReservation ? currentReservation.reservationType : null);
	var recurrenceParent = (currentReservation && currentReservation.recurrenceId) ? currentReservation.recurrenceId : false;
	const startDate = moment.tz(currentReservation.scheduledDate, timezone).valueOf();
	const endDate = currentReservation.scheduledEndDate ? moment.tz(currentReservation.scheduledEndDate, timezone).valueOf() : 0;
	const startTime = currentReservation.scheduledTime ? moment.tz(currentReservation.scheduledTime,"h:mm a", timezone).format("h:mm a") : '';
	const endTime = currentReservation.scheduledEndTime ? moment.tz(currentReservation.scheduledEndTime,"h:mm a", timezone).format("h:mm a") : '';
	var recurringDaysArray = currentReservation.recurringDays && currentReservation.recurringDays.length ? currentReservation.recurringDays : [];

	self.recurrenceParent.set(recurrenceParent);
	self.reservationType.set(reservationType);
	self.repeatingReservation.set(repeatSelect);
	self.reservationStartDate.set(startDate);
	self.reservationEndDate.set(endDate);
	self.reservationStartTime.set(startTime);
	self.reservationEndTime.set(endTime);
	self.recurringDays.set(recurringDaysArray);
	self.originalRecurringDays.set(recurringDaysArray);
	self.totalReservationsWeeklyHours.set(Template.instance().data.totalHoursScheduled);
	var hasEndDate = currentReservation.scheduledEndDate ? true : false;
	self.hasScheduledEndDate.set(hasEndDate);

	// $("#selectedPerson").multiselect( {
	// 		enableCaseInsensitiveFiltering:true,
	// 		enableFiltering:true,
	// 		buttonWidth:"350px"
	// 	});

	$("#selectedPerson").select2({
		width:'100%',
    	multiple: (currentReservation && currentReservation._id) ? false : true,
    	selectionCssClass:"form-control form-control-lg form-control-solid"
	});

	$("#scheduleReservationDate").datepicker({
		autoclose: true, todayHighlight: true,
	}).on("changeDate", function(e) {
		const zonedTime = new moment.tz(e.target.value, 'MM/DD/YYYY', Orgs.current().getTimezone());
		self.startDate.set(zonedTime.valueOf());
		self.reservationStartDate.set(zonedTime.valueOf());
		const totalHours =  self.totalReservationsWeeklyHours.get();
		const previousReservationWeeklyHours =  self.currentReservationWeeklyHours.get();

		const currentReservation = {
			scheduledDate: self.reservationStartDate.get(),
			scheduledEndDate: self.reservationEndDate.get(),
			scheduledEndTime: self.reservationEndTime.get(),
			scheduledTime: self.reservationStartTime.get(),
			selectedPerson: self.data.selectedReservationPersonId,
			orgId: Orgs.current()._id,
			recurringDays: self.recurringDays.get()
		};

		updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, self);
		checkCapacityAvailability(self);
	});

	$("#scheduleReservationEndDate").datepicker({
		autoclose: true, todayHighlight: true
	}).on("changeDate", function(e) {
		const zonedTime = new moment.tz(e.target.value, 'MM/DD/YYYY', Orgs.current().getTimezone());
		self.endDate.set(zonedTime.valueOf());
		self.reservationEndDate.set(zonedTime.valueOf());
		const totalHours =  self.totalReservationsWeeklyHours.get();
		const previousReservationWeeklyHours = self.currentReservationWeeklyHours.get();

		const currentReservation = {
			scheduledDate:self.reservationStartDate.get(),
			scheduledEndDate: self.reservationEndDate.get(),
			scheduledEndTime: self.reservationEndTime.get(),
			scheduledTime: self.reservationStartTime.get(),
			selectedPerson: self.data.selectedReservationPersonId,
			orgId: Orgs.current()._id,
			recurringDays: self.recurringDays.get()
		};

		updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, self);
		checkCapacityAvailability(self);
	});

	// Disable days based on enrollment cap
	const org = Orgs.current();
	const hasRegistrationFlow = org && org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	const hasWeekends = org && org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);

	if (hasRegistrationFlow && this.data.reservationId) {
		self.processAvailableDays.set(false);
		const startDate = moment.tz(currentReservation.scheduledDate, org.getTimezone()).format("MM/DD/YYYY");
		const endDateString = endDate ? moment.tz(endDate, org.getTimezone()).format("MM/DD/YYYY") : null;
		const scheduleType = currentReservation.scheduleType;
		const excludedReservationIds = this.data.reservationId ? [this.data.reservationId] : [];
		Meteor.callAsync('getOrgScheduleTypesAvailabilities', org._id, startDate, null, endDateString, excludedReservationIds).then(result => {
			const matchedSchedule = result[scheduleType];

			if (matchedSchedule) {
				const availableDays = {
					1: matchedSchedule[1] > 0,
					2: matchedSchedule[2] > 0,
					3: matchedSchedule[3] > 0,
					4: matchedSchedule[4] > 0,
					5: matchedSchedule[5] > 0
				};

				if (hasWeekends) {
					availableDays[0] = matchedSchedule[0] > 0;
					availableDays[6] = matchedSchedule[6] > 0;
				}

				self.availableDays.set(availableDays);
			}
		}).catch(error => {
			console.log(error);
		});
	}
};

Template.reservationFormModal.helpers({
	"hasRecurrenceParent": function() {
		return Template.instance().recurrenceParent.get() != false;
	},
	"isMultipleSelect": function() {
		return this.reservationId ? "" : "multiple";
	},
	"isNew": function() {
		var ret =  (this.reservationId || "") != "" ? "" : "multiple";
		return ret;
	},
	"isRepeatingReservation": function() {
		return Template.instance().repeatingReservation.get() === 'repeats';
	},
	"scheduledEndDateActive": function() {
		return Template.instance().hasScheduledEndDate.get();
	},
	'cancellationReasonActive': function() {
		return Template.instance().hasScheduledEndDate.get() && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	},
	'cancellationReasons': function() {
		return Template.instance().cancellationReasons.get().filter(reason => !reason.archived || reason._id === reservation()?.cancellationReasonId);
	},
	"people": function() {
		return availablePeople(Template.instance().reservationType.get());
	},
	"reservation": function() {
		return reservation();
	},
	"formattedScheduledDate": function() {
		return moment.tz(reservation().scheduledDate, Orgs.current().getTimezone()).format("MM/DD/YYYY");
	},
	"formattedScheduledEndDate": function() {
		if (reservation().scheduledEndDate) {
			return  moment.tz(reservation().scheduledEndDate, Orgs.current().getTimezone()).format("MM/DD/YYYY");
		}
		return moment.tz(Orgs.current().getTimezone()).add(1, 'month').format("MM/DD/YYYY");
	},
	"timeFormatted": function() {
		return moment.tz(reservation().scheduledTime,"h:mm a", Orgs.current().getTimezone()).format("HH:mm");
	},
	"endTimeFormatted": function() {
		return moment.tz(reservation().scheduledEndTime,"h:mm a", Orgs.current().getTimezone()).format("HH:mm");
	},
  "reservationTypeEnabled": function() {
  	return (Meteor.user().fetchPerson().type == "admin" && Orgs.current().hasCustomization("reservations/reservationType/enabled"));
  },
  "showScheduledEndTime": function() {
  	return (Template.instance().reservationType.get() == "staff");
	},
	"checkedForDay": function(day) {
		return (reservation().recurringDays && _.contains(reservation().recurringDays, day)) ? "checked" : "";
	},
	"scheduleTypes": function() {
		// Get currently selected group ID from dropdown
		const targetGroupId = Template.instance().targetGroup.get();
		const defaultGroupId = Template.instance().defaultGroupId.get();
		const allGroups = Template.instance().allGroups.get();

		// Use default group if none selected or "default" chosen
		const effectiveGroupId = (targetGroupId === 'default' || !targetGroupId) ? defaultGroupId : targetGroupId;

		// Return empty if no valid group ID
		if (!effectiveGroupId) return [];
		const foundGroup = allGroups.find(group => group._id === effectiveGroupId);

		// Get schedule type IDs from group's plans mapping
		const scheduleKeys = foundGroup?.scheduleTypeToPlans ? Object.keys(foundGroup.scheduleTypeToPlans) : [];
		const allScheduleTypes = Template.instance().scheduleTypes.get();
		return _.sortBy(allScheduleTypes.filter(type => scheduleKeys.includes(type._id)), "type");
	},
	"availableGroups": function() {
		return Template.instance().allGroups.get();
	},
	"linkedPlanDetails": function() {
		const instance = Template.instance();
		return {
			availableBillingPlans: Orgs.current().availableBillingPlans()?.filter(plan => !plan.details?.selectiveWeeks?.length),
			enrollmentDate: new moment.tz(Orgs.current().getTimezone()).format("MM/DD/YYYY"),
			personId: profileScreenPersonId(),
			startDate: instance.startDate.get() || reservation().scheduledDate,
			endDate: instance.endDate.get() || reservation().scheduledEndDate
		}
	},
	"showLinkedBillingPlanForm": function() {
		return Template.instance().showLinkedBillingPlan.get();
	},
	"showLinkedBillingPlanButton": function() {
		const reservationId = Template.instance().data.reservationId,
			personId = profileScreenPersonId();

		Template.instance().personIdPlanButton.set(personId);
		const	currentPerson = Template.instance().personPlanButton.get();
		const currentPlan = !reservationId ? null : currentPerson?.billing?.enrolledPlans?.find(ep => ep.reservationId === reservationId);
		return !Template.instance().showLinkedBillingPlan.get() &&
			Template.instance().repeatingReservation.get() === 'repeats' &&
			currentPerson &&
			!currentPlan;
	},
	"totalHours": function() {
		const sum =  Template.instance().totalReservationsWeeklyHours.get();
		var h = Math.floor(sum / 3600);
		var m = Math.floor(sum % 3600 / 60);

		var hDisplay = h > 0 ? h + (h === 1 ? " hour " : " hours ") : "";
		var mDisplay = m > 0 ? m + (m === 1 ? " minute " : " minutes ") : "";
		return hDisplay + mDisplay;
	},
	"overtime": function() {
		const regularHours = 40 * 3600;
		if (Template.instance().totalReservationsWeeklyHours.get() > regularHours) {
			return true;
		}
		return false;
	},
	disabledIfUnavailable: function(day) {
		if (!Template.instance().hasRegistrationFlow) {
			return '';
		}
		const disabled = !Template.instance().availableDays.get()[day];
		if (disabled) {
			const checkbox = document.querySelector(`#recurs${ dayMap[day] }`);
			const checked = checkbox?.checked ?? false;
			const selected = Template.instance().originalRecurringDays.get().includes((dayMap[day] ?? '').toLowerCase());
			// If at the initial stage of editing the schedule and the day was already selected before, don't disable it.
			// !Template.instance().processAvailableDays.get() => initial stage of editing the schedule
			if (!Template.instance().processAvailableDays.get() && selected) {
				return '';
			}
			if (checked) {
				checkbox.checked = false;
			}
		}

		return disabled ? 'disabled' : '';
	},
	unavailableNoteClass(day) {
		if (!Template.instance().hasRegistrationFlow) {
			return 'd-none';
		}

		const disabled = !Template.instance().availableDays.get()[day];
		if (disabled) {
			const checkbox = document.querySelector(`#recurs${ dayMap[day] }`);
			const checked = checkbox?.checked ?? false;
			const selected = Template.instance().originalRecurringDays.get().includes((dayMap[day] ?? '').toLowerCase());
			// If at the initial stage of editing the schedule and the day was already selected before, don't show note.
			// !Template.instance().processAvailableDays.get() => initial stage of editing the schedule
			if (!Template.instance().processAvailableDays.get() && selected) {
				return 'd-none';
			}
		}

		return !Template.instance().availableDays.get()[day] ? '' : 'd-none';
	},
	showCloseLinkedPlanButton(){
		return processPermissions({
			assertions: [{ context: AvailablePermissions.RESERVATIONS_REMOVE_LINKED_BILLING_PLAN, action: AvailableActionTypes.EDIT }],
			evaluator: (person) => person.type === "admin",
		});
	}
});

function resetReservationForm(preservePersonId) {
	const currentReservationPersonId =   $("#selectedPerson").val();
	const timezone = Orgs.current().getTimezone();
	$("#reservationForm")[0].reset();
	$("#scheduleReservationDate").val(moment.tz(timezone).startOf('day').format("MM/DD/YYYY"));
	$("#scheduleReservationEndDate").val(moment.tz(timezone).startOf('day').format("MM/DD/YYYY"));
	if (preservePersonId) {
		$("#selectedPerson").multiselect("select", currentReservationPersonId);
	}
	$("#selectedPerson").multiselect("rebuild");
}

function profileScreenPersonId() {
	return FlowRouter.current().path.includes("/people/") && FlowRouter.getParam("_id");
}

function updateHoursRealtime(currentReservation, totalHours, previousReservationWeeklyHours, template) {
	const currentReservationWeeklyHours = calculateReservationWeeklyHours(currentReservation);
	template.currentReservationWeeklyHours.set(currentReservationWeeklyHours);
	const result = totalHours - previousReservationWeeklyHours + currentReservationWeeklyHours;
	template.totalReservationsWeeklyHours.set(result);
}

function checkCapacityAvailability(instance) {
	const org = Orgs.current();
	const hasRegistrationFlow = org && org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	const hasWeekends = org && org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);
	if (hasRegistrationFlow) {
		const startDate = document.querySelector("#scheduleReservationDate").value;
		const endDate = document.querySelector("#scheduleReservationEndDate").value;
		const scheduleType = document.querySelector("#scheduleType").value;
		instance.processAvailableDays.set(true);
		const excludedReservationIds = instance.data.reservationId ? [instance.data.reservationId] : [];
		Meteor.callAsync('getOrgScheduleTypesAvailabilities', org._id, startDate, null, endDate, excludedReservationIds).then(result => {
			const matchedSchedule = result[scheduleType];
			if (matchedSchedule) {
				const availableDays = {
					1: matchedSchedule[1] > 0,
					2: matchedSchedule[2] > 0,
					3: matchedSchedule[3] > 0,
					4: matchedSchedule[4] > 0,
					5: matchedSchedule[5] > 0
				};

				if (hasWeekends) {
					availableDays[0] = matchedSchedule[0] > 0;
					availableDays[6] = matchedSchedule[6] > 0;
				}

				instance.availableDays.set(availableDays);
			} else {
				const availableDays = {
					1: true,
					2: true,
					3: true,
					4: true,
					5: true
				};

				if (hasWeekends) {
					availableDays[0] = true;
					availableDays[6] = true;
				}

				instance.availableDays.set(availableDays);
			}
		}).catch(error => {
			console.log(error);
		});
	}
}