<template name="reservationCancelFormModal">
  <div id="reservationCancelFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">
            {{#if reservation.cancellationReason}}
							Update Cancelled Reservation 
						{{ else}} 
							Cancel Reservation
						{{/if}}
          </h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="reservationCancelForm">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Reason</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="cancel-reason" class="form-control form-control-lg form-control-solid" id="selectedReason">
                  {{#each cancellationReason in cancellationReasons}}
                    <option value="{{cancellationReason}}" {{selectedIfEqual reservation.cancellationReason cancellationReason}}>{{cancellationReason}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
              <div class="col-lg-9 col-xl-6">
                <textarea data-cy="cancel-comment" class="form-control form-control-lg form-control-solid" rows="3" id="cancellation-comment">{{reservation.cancellationComments}}</textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" data-cy="cancel-reservation-btn" class="btn btn-primary font-weight-bolder mr-2" id="cancelReservation">{{#if reservation.cancellationReason}}Update Cancellation{{else}}Cancel Reservation{{/if}}</button>
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
