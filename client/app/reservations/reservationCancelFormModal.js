import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { Reservations } from '../../../lib/collections/reservations';
import './reservationCancelFormModal.html';
import { hideModal } from '../main';

Template.reservationCancelFormModal.events({
	'click #cancelReservation': function(event) {
		event.preventDefault();
		var reservationCancelData = {};
		
		reservationCancelData.reservationId = Template.instance().data.reservationId;
		if (Template.instance().data.reservationInstanceDate) reservationCancelData.occurrenceTime = Template.instance().data.reservationInstanceDate;
		reservationCancelData.selectedReason = $("#selectedReason").val();
		reservationCancelData.comment = $('#cancellation-comment').val();

		var successAction = function() {
			Session.set("reservationId", null);
			hideModal("#reservationCancelFormModal");
		}

		Meteor.callAsync('cancelReservation', reservationCancelData).then(res => {
			successAction();
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		}); 
		
	}
});

Template.reservationCancelFormModal.helpers({
	reservation() {
		return Reservations.findOne({_id: Template.instance().data.reservationId});
	},
	cancellationReasons() {
		let reasons = ["Approved", "Approved with Qualification", "No Call", "Late Cancellation"]
		if (Orgs.current().isChildCare()) {
			reasons.push("Absent - Sick", "Absent - Vacation", "PTO");
		}
		return reasons;
	}
});
