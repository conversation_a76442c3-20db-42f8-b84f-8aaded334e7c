import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Reservations } from '../../../lib/collections/reservations';
import _ from '../../../lib/util/underscore';
import './reservationsList.html';
import moment from "moment-timezone";
import { getPeopleData } from "../../services/peopleMeteorService";
import { processPermissions } from '../../../lib/permissions';
import '../components/dateRangePicker/dateRangePicker';
import { showModal } from '../main';
import './reservationFormModal';
import './reservationCancelFormModal';
import { offlinePreventCheck } from '../../../mpweb';

Template.reservationsList.onCreated(function () {
	const timezone = Orgs.current().getTimezone();
	this.filterGroup = new ReactiveVar("");
	this.filterType = new ReactiveVar("");
	this.filterStartDate = new ReactiveVar(new moment.tz(timezone));
	this.filterEndDate = new ReactiveVar(new moment.tz(timezone).add(1, "week"));
	this.peopleQuery = new ReactiveVar();
	this.peopleIds = new ReactiveVar();
	this.selectedPeopleId = new ReactiveVar();
	this.selectedPeople = new ReactiveVar();
	var instance = this;
	this.totalReservations = new ReactiveVar(0);
	this.currentPage = new ReactiveVar(1);
	this.reservationsData = new ReactiveVar([]);
	instance.currentPage = new ReactiveVar(1);
	instance.fetchedReservations = new ReactiveVar([]);

	const isE2E = localStorage.getItem('e2eCypress') === 'true';
	instance.itemsPerPage = isE2E ? 2 : 100;
	
	instance.autorun(() => {
        const currentPage = instance.currentPage.get();
        const itemsPerPage = instance.itemsPerPage;
		const skip = (instance.currentPage.get() - 1) * instance.itemsPerPage;
		const startDate = moment(this.filterStartDate.get()).format("MM/DD/YYYY");
		const endDate = moment(this.filterEndDate.get()).add(1, "day").format("MM/DD/YYYY");
		const filterGroup = instance.filterGroup.get(); 
        Meteor.call("getReservations", { startDate, endDate, skip, limit: itemsPerPage,filterGroup }, (error, result) => {
            if (!error && result?.reservations) {
                instance.fetchedReservations.set(result.reservations);
                instance.totalReservations.set(result.totalCount);

				const selectedPeopleId = result.reservations.map(r => r.selectedPerson);
                instance.selectedPeopleId.set(selectedPeopleId);

            } else {
                console.error("Error fetching reservations:", error);
            }
        });
    });

	instance.autorun(function () {
		const fields = { _id: 1 };
		const query = instance.peopleQuery.get();
		if (query) {
			getPeopleData(query, { fields }).then(res => {
				if (res?.length) {
					const ids = [];
					res.forEach((p) => { ids.push(p._id); });
					instance.peopleIds.set(ids);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	});

	instance.autorun(function () {
		const query = { _id: { $in: instance.selectedPeopleId.get() } };
		getPeopleData(query, { fields: { _id: 1, firstName: 1, lastName: 1 } }).then(res => {
			if (res?.length) {
				instance.selectedPeople.set(res);
			}
		}).catch(err => {
			console.log(err);
		});
	});
});

Template.reservationsList.events({
	'click #newReservationLink': function(event, template) {
		
    	showModal("reservationFormModal", {reservationId: null, selectedReservationPersonId: null}, "#reservationFormModal");
	},
	'click .editReservationLink': function(event) {
		
    	showModal("reservationFormModal", { reservationId: $(event.target).attr("data-id") }, "#reservationFormModal")
	},
	'click .cancelReservationLink': function(event) {
		
		var reservationId = $(event.target).data("id");
		if (!this.recurringFrequency) {
      showModal("reservationCancelFormModal", {reservationId: $(event.target).attr("data-id")}, "#reservationCancelFormModal")
		} else {
			mpSwal.fire({
				title: "Cancellation Type",
				text: "This is a recurring event. Do you want to cancel this instance or delete the series?",
				icon: "warning",
				showCancelButton: true,
				showCloseButton: true,
				cancelButtonText: "Delete Series",
				confirmButtonText: "Cancel Instance"
			}).then(async (result) => {
				if (result.value) {
          showModal("reservationCancelFormModal", { reservationId: $(event.target).attr("data-id"), reservationInstanceDate: this.scheduledDate }, "#reservationCancelFormModal");
				} else if (result.dismiss == "cancel") await Meteor.callAsync("deleteReservation", this.originalId);
			});
		}
	},
	"change #filterGroup": function(event, template) {
    template.filterGroup.set( $("#filterGroup").val() );
  },
 	"change #filterType": function(event, template) {
    template.filterType.set( $("#filterType").val() );
	},
	"change #reservationsStartDate": function(event, template) {
		if (offlinePreventCheck()) return false;
		template.filterStartDate.set( $("#reservationsStartDate").val());
	},
	"change #reservationsEndDate": function(event, template) {
		if (offlinePreventCheck()) return false;
		template.filterEndDate.set( $("#reservationsEndDate").val());
	},
	'click .page-item-set[data-action="previous"]'(event, instance) {
		if (instance.currentPage.get() > 1) {
			instance.currentPage.set(instance.currentPage.get() - 1);
		}
	},

	'click .page-item-set[data-action="next"]'(event, instance) {
		const totalPages = Math.ceil(instance.totalReservations.get() / instance.itemsPerPage);
		if (instance.currentPage.get() < totalPages) {
			instance.currentPage.set(instance.currentPage.get() + 1);
		}
	}
});
Template.reservationsList.helpers({
	"dateFields": function() {
		return {
			filterStartDate: Template.instance().filterStartDate,
			filterEndDate: Template.instance().filterEndDate,
		}
	},
  "formattedStartDate": function() {
    return Template.instance().filterStartDate.get();
  },
  "formattedEndDate": function() {
    return Template.instance().filterEndDate.get();
  },
	'userCanAddOrModifyReservations': function() {
		return processPermissions({
			assertions: [{ context: "reservations", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type == "staff"
		  });
	},
	"reservations" : function () {
		const instance = Template.instance();
		return instance.fetchedReservations.get() || [];
	},
	'scheduledDateFormatted': function() {
		return moment(this.scheduledDate).format("MM/DD/YYYY");
	},
	"groups": function() {
    return Groups.find({}, {sort: {name: 1}});
  },
  "reservationTypeEnabled": function() {
  	return (Meteor.user().fetchPerson().type == "admin" && Orgs.current().hasCustomization("reservations/reservationType/enabled"));
  },
	"idToEdit": function() {
		return this.originalId || this._id;
	},
	scheduleTypes() {
		return Orgs.current().getScheduleTypes();
	},
	scheduleTypeNameFor(id) {
		const stdef = _.find(Orgs.current().getScheduleTypes(), st => st._id == id);
		return stdef && stdef.type;
	},
	"selectedPeople": function(selectedPersonId, firstName) {
		if (selectedPersonId) {
			const person = Template.instance().selectedPeople.get()?.find(p => p._id == selectedPersonId);
			if(person){
				return person.firstName + " " + person.lastName;
			}
			return "";
		}
	},
	'pageStart': function () {
		const instance = Template.instance();
		const total = instance.totalReservations.get();
		if (total === 0) return 0;
		return Math.min((instance.currentPage.get() - 1) * instance.itemsPerPage + 1, total);
	},

	'pageEnd': function () {
		const instance = Template.instance();
		const total = instance.totalReservations.get();
		if (total === 0) return 0;
		return Math.min(instance.currentPage.get() * instance.itemsPerPage, total);
	},

	'pageTotal': function () {
		const instance = Template.instance();
		const total = instance.totalReservations.get() || 0;
		return total;
	}
});
