import { TimeCards } from '../../../lib/collections/timeCards';
import moment from "moment-timezone";
import { AVAILABLE_WEEKDAYS } from '../../../lib/constants/enrollmentConstants';

export function calculateWorkedHoursCurrentWeek(personId, orgId) {
    if (!personId || !orgId) {
        return 0;
    }
    const startOfWeek = moment().startOf('week');
    const now = moment();

    let sum = 0;
    const query = {
        orgId: orgId,
        personId: personId,
        checkInDate: { $exists: true },
        checkOutDate: { $exists: true },
        checkInTime: { $exists: true },
        checkOutTime: { $exists: true },
        void: {$ne: true},
        timeCardDateStamp: {
            $gte: startOfWeek.valueOf(),
            $lte: now.valueOf() }
    };
    let timeCards = TimeCards.find(query).fetch();
    timeCards = timeCards.filter(tc =>  moment(tc.checkOutDate + " " + tc.checkOutTime).isBefore(now));
    //console.log(timeCards.length, 'time cards length')

    for (const timeCard of timeCards) {
        const checkInTime = moment(timeCard.checkInTime, 'h:mm a');
        const checkOutTime = moment(timeCard.checkOutTime, 'h:mm a');
        const workHours = moment.duration(checkOutTime.diff(checkInTime)).asSeconds();
        sum += workHours <= 0 ? 0 : workHours;
    }
    return sum;
}

export function calculateReservationWeeklyHours(reservation) {
    let sum = 0;
    const reservationStartDate = reservation.scheduledDate;
    const reservationEndDate = reservation.scheduledEndDate;
    const recurringDays = reservation.recurringDays;
    if (!reservation.scheduledTime || !reservation.scheduledEndTime) {
        return 0;
    }
    const startTime = moment(reservation.scheduledTime, 'h:mm a');
    const endTime = moment(reservation.scheduledEndTime, 'h:mm a');
    const todayDate = moment().startOf("day").format("MM/DD/YYYY");
    const endOfWeek = moment().endOf("week");
    const now = moment();

    // calculate total duration
    const duration = moment.duration(endTime.diff(startTime)).asSeconds();
    if (reservationStartDate && duration > 0) {
        if (recurringDays && recurringDays.length > 0) {
            if (reservationEndDate && ((reservationStartDate >=  reservationEndDate) || (moment(reservationEndDate).isBefore(now)))) {
                return 0;
            }
            else {
                let weekdays = AVAILABLE_WEEKDAYS.map(day => day.toLowerCase());
                const weekdaysIndex = weekdays.indexOf(now.format("ddd").toLowerCase());
                const hasReservationToday = recurringDays.indexOf(now.format("ddd").toLowerCase()) > -1;
                if (hasReservationToday) {
                    // check if today reservation has passed
                    const todayReservation = moment(todayDate + " " + reservation.scheduledEndTime);
                    weekdays.splice(0, todayReservation.isBefore(now) ? weekdaysIndex + 1: weekdaysIndex);
                }
                else {
                    weekdays.splice(0, weekdaysIndex + 1);
                }
                for (const day of recurringDays) {
                    if (weekdays.includes(day)) {
                        sum += duration;
                    }
                }
            }
        }
        else {
            // Non-recurring schedule
            const reservationStartDateFormatted = moment(reservationStartDate).format("MM/DD/YYYY");
            const reservationEndTimeValue = moment(reservationStartDateFormatted + " " + reservation.scheduledEndTime);
            if (reservationEndTimeValue.isBetween(now, endOfWeek)) {
                sum += duration;
            }
        }

    }
    return sum;
}