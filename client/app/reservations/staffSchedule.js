import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Groups } from '../../../lib/collections/groups';
import { Reservations } from '../../../lib/collections/reservations';
import './staffSchedule.html';
import {FlowRouter} from 'meteor/ostrio:flow-router-extra';
import { getPeopleData } from '../../services/peopleMeteorService';
import '../../layout/loading';
import moment from 'moment-timezone';

Template.staffSchedule.created = function() {
    const self = this;
    self.printMode = new ReactiveVar(false);
    const start = parseInt(FlowRouter.getQueryParam('rangeStart') ?? new moment().valueOf());
    self.rangeStart = new ReactiveVar(new moment(start).startOf('week').valueOf());
    self.loaded = new ReactiveVar(false);
    self.autorun(function() {
        const rangeStartDate = new moment(self.rangeStart.get()).format("MM/DD/YYYY"),
            rangeEndDate = new moment(self.rangeStart.get()).add(8, 'day').format("MM/DD/YYYY");
        self.subscribe('theReservationsForStaff', { startDate: rangeStartDate, endDate: rangeEndDate });
    });

    self.staffData = new ReactiveVar();
    self.childAttendanceIds = new ReactiveVar();
    self.childAttendanceData = new ReactiveVar();
    self.autorun(function() {
        getPeopleData({type: 'staff'}, {sort: {firstName: 1, lastName: 1}}).then(res => {
            self.staffData.set(res);
        }).catch(err => {
            console.log(err);
        });
    });
    self.autorun(function() {
        getPeopleData({_id: {$in: self.childAttendanceIds.get()}}).then(res => {
            if (res?.length){
                self.childAttendanceData.set(res);
            }
        }).catch(err => {
            console.log(err);
        });
    });
};

Template.staffSchedule.rendered = function() {
    const self = this;
    $("#btnCalendar").datepicker({
        autoclose: true,
        zIndexOffset:9999
    }).on("changeDate", function(event) {
        const newDate = new moment(event.date);
        self.rangeStart.set( newDate.startOf('week').valueOf());
    });
}

Template.staffSchedule.helpers({
    isPrintMode() {
        return FlowRouter.current().queryParams['view'] === 'print';
    },
    days: () => {
        const rangeStart = Template.instance().rangeStart.get();

        const dateItems = [];
        for (let i = 0; i< 7; i++) {
            const day = new moment(rangeStart).add(i, 'day').valueOf();
            dateItems.push(day);
        }
        return dateItems;
    },
    loaded: () => {
        return Template.instance().loaded.get();
    },
    dateLabel: function() {
        const rangeStart = Template.instance().rangeStart.get();
        const rangeEnd = new moment(rangeStart).add(6, 'day').add(1, 'hour');
        return new moment(rangeStart).format("M/D/YYYY") + ' - ' + rangeEnd.format("M/D/YYYY");
    },
    staffTimes: function () {
        Template.instance().loaded.set(false);
        const rangeStart = Template.instance().rangeStart.get();
        const rangeEnd = new moment(rangeStart).add(7, 'day').valueOf();
        const groups = Groups.find({}, {sort: {name: 1}}).fetch();
        const staff = Template.instance().staffData.get();
        const timesArray = [];
        const reservations = Reservations.findWithRecurrence({startDateValue: rangeStart - (3600 * 10000), endDateValue: rangeEnd + (3600 * 10000), query: {
                reservationType: 'staff',
                cancellationReason: null
            }});
        const childAttendance = Reservations.findWithRecurrence({startDateValue: rangeStart - (3600 * 10000), endDateValue: rangeEnd + (3600 * 10000), query: {
                reservationType: 'person',
                cancellationReason: null
            }});
        const childAttendanceIds = [];
        for (const childRes of childAttendance) {
            childAttendanceIds.push(childRes.selectedPerson);
        }
        if (childAttendanceIds?.length !== Template.instance().childAttendanceIds.get()?.length) {
            Template.instance().childAttendanceIds.set(childAttendanceIds);
        }
        for (const group of groups) {
            const groupObj = { name: group.name, ratio: group.ratio, staffArray: [] };
            if (staff) {
                for (const p of staff) {
                    let staffFound = false;
                    const staffDays = [[], [], [], [], [], [], []];
                    const defaultGroup = p.defaultGroupId;
                    for (const res of reservations) {
                        if ((res.groupId === group._id || (!res.groupId && defaultGroup === group._id)) && res.selectedPerson === p._id) {
                            const day = Math.round((res.scheduledDate - rangeStart) / (86400 * 1000));
                            if (day >= 7 || day < 0) {
                                continue;
                            }
                            staffFound = true;
                            let label = res.scheduledTime ? res.scheduledTime.replace(' ', '&nbsp;') : 'scheduled';
                            if (res.scheduledTime && res.scheduledEndTime) {
                                label += ' - ' + res.scheduledEndTime.replace(' ', '&nbsp;');
                            }
                            staffDays[day].push(label);
                        }
                    }
                    if (!staffFound) {
                        continue;
                    }
                    for (let staffDay of staffDays) {
                        // make sure our times are sorted
                        staffDay.sort((a, b) => {
                            const aStart = a.split(' - ')[0].replace('&nbsp;', ' ');
                            const bStart = b.split(' - ')[0].replace('&nbsp;', ' ');
                            const testA = new moment('2020-01-01 ' + aStart, 'YYYY-MM-DD h:m a').valueOf();
                            const testB = new moment('2020-01-01 ' + bStart, 'YYYY-MM-DD h:m a').valueOf();
                            return testA - testB;
                        });
                        staffDay = staffDay.filter((l, i) => i === 0 || l !== 'scheduled');
                    }
                    groupObj.staffArray.push({ name: p.firstName + ' ' + p.lastName, id: p._id, days: staffDays });
                }
            }
            const childrenArray = [[], [], [], [], [], [], []];
            for (const childRes of childAttendance) {
                const attendanceData = Template.instance().childAttendanceData.get()
                const child = attendanceData?.find(p => childRes.selectedPerson === p._id);
                const defaultGroup = child?.defaultGroupId || '';
                if (childRes.groupId === group._id || (!childRes.groupId && defaultGroup === group._id)) {
                    const day = Math.round((childRes.scheduledDate - rangeStart) / (86400 * 1000));
                    if (day >= 7 || day < 0) {
                        continue;
                    }
                    if (!childrenArray[day].includes(childRes.selectedPerson)) {
                        childrenArray[day].push(childRes.selectedPerson);
                    }
                }
            }
            groupObj.childDays = childrenArray.map(a => a.length);
            timesArray.push(groupObj);
        }
        Template.instance().loaded.set(true);
        return timesArray;
    }
});

Template.staffSchedule.events({
    'click .move-date': function(event, i) {
        const selectedAction = event.currentTarget.dataset.action;
        const mult = selectedAction === "add" ? 1 : -1;
        i.rangeStart.set(new moment(i.rangeStart.get()).add(mult * 7, 'day').valueOf());
    },
    'click .person-link': function(evt, i) {
        const pId = evt.currentTarget.dataset.id;
        FlowRouter.go('/people/' + pId + '#reservations');
    },
    "click #printButton": function(event, instance) {
        // window.open(`/scheduling?view=print&rangeStart=${instance.rangeStart.get()}#staff`, '', 'left=0,top=0,width=1250,height=900,toolbar=0,scrollbars=0,status=0');
        window.print();
    },
})