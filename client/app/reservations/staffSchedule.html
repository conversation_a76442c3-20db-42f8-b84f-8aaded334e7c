<template name="staffSchedule">
  <div class="calendar container">
    <div class="print-only">
      <div class="h1 align-content-center">
        Staff Scheduling
      </div>
    </div>

    <div class="d-flex flex-row flex-grow-1 justify-content-between mb-6">
      <div class="d-flex flex-row align-items-center">
          <span class="mr-6">
            <span data-cy="calendar" style="cursor:pointer;" id="btnCalendar"><i class="fad fad-primary fa-calendar-day icon-2x"></i></span>
          </span>
        <span class="font-size-h3 font-weight-bold mr-6 min-w-200px text-center">{{dateLabel}}</span>
        <span class="move-date d-print-none" style="cursor:pointer;" data-action="subtract" data-cy="calendar-previous-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
        <span class="move-date d-print-none" style="cursor:pointer;" data-action="add" data-cy="calendar-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x"></i></span>
      </div>
      <div class="d-flex flex-row align-items-center justify-content-end">
          <button data-cy="print-pdf" class="btn btn-secondary ml-4 d-print-none" type="button" id="printButton">
            <i class="fad fa-print"></i>
            Print / PDF
          </button>
      </div>
    </div>
    {{#if showLoading Template.subscriptionsReady}}
    {{> loading}}
    {{/if}}

    <div data-cy="calendar-staff-table" class="calendar-detail calendar-staff-card">
      <div class="list-table">
        <div class="list-table-row">
          <div class="list-table-column">
            <table class="calendar-grid-table week staff">
              <tr class="calendar-grid-row">
                <th class="calendar-grid-col">Staff</th>
                {{#each label in days}}
                <th class="calendar-grid-col">{{formatDate label "ddd"}}</th>
                {{/each}}
              </tr>
              {{#each groupObj in staffTimes }}
                  <tr class="calendar-grid-row">
                    <td class="calendar-grid-col section-header" colspan="8">{{groupObj.name}}</td>
                  </tr>
                {{#each staffObj in groupObj.staffArray }}
                  <tr data-cy="staff-row" class="calendar-grid-row staff-row">
                    <td data-cy="staff-name" class="calendar-grid-col person-link" data-id="{{staffObj.id}}">{{ staffObj.name }}</td>
                    {{#each day in staffObj.days}}
                      <td class="calendar-grid-col">
                        {{#each label in day}}
                          {{#if gt @index 0}}
                          <br>
                          {{/if}}
                          {{{label}}}
                        {{/each}}
                      </td>
                    {{/each}}
                  </tr>
                {{/each}}
              <tr data-cy="child-schedule-row" class="calendar-grid-row child-schedule-row">
                <td data-cy="children-col" class="calendar-grid-col">Children Scheduled
                  {{#if gt groupObj.ratio 0}}
                  (1:{{groupObj.ratio}} ratio required)
                  {{/if}}
                </td>
                {{#each dayCount in groupObj.childDays}}
                  <td data-cy="day-count-col" class="calendar-grid-col">
                    {{#if gt dayCount 0}}
                      {{dayCount}}
                    {{/if}}
                  </td>
                {{/each}}
              </tr>
              {{/each}}
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>