<template name="reservationsList">
  <div class="container d-flex flex-row justify-content-end mb-4">
    {{#if reservationTypeEnabled}}
      <div class="mr-4 d-flex flex-column justify-content-end">
        <label>Type:</label>
        <select class="form-control min-w-150px" id="filterType">
          <option value="" selected>All</option>
          <option value="person">People</option>
          <option value="staff">Staff</option>
        </select>
      </div>
    {{/if}}
    <!-- Filter & Pagination Row -->
    <div class="container-fluid d-flex flex-row justify-content-between align-items-center mb-4 px-0">

      <!-- Pagination Section -->
      <div class="d-flex flex-row justify-content-end">
        <div class="d-flex flex-row ml-6 justify-content-center align-items-center">
          <span class="page-item-set" style="cursor:pointer;" data-action="previous" data-cy="reservation-pagination-prev-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
				  <span class="font-size-h2" data-cy="fb-pagination-records-count">{{pageStart}}-{{pageEnd}} of {{pageTotal}}</span>
				  <span class="page-item-set" style="cursor:pointer;" data-action="next" data-cy="reservation-pagination-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
        </div>
      </div>

      <!-- Filters Section (Right) -->
      <div class="d-flex flex-row ">
        <div class="d-flex flex-column justify-content-end">
          <label>Group:</label>
          <select data-cy="filter-group" class="form-control min-w-150px" id="filterGroup">
            <option value="">All</option>
            {{#each groups}}
            <option value="{{_id}}">{{name}}</option>
            {{/each}}
          </select>
        </div>
        <div class="d-flex flex-column justify-content-end ml-4">
          <label>Date Range:</label>
          {{> dateRangePicker dateFields}}
        </div>
        {{#if userCanAddOrModifyReservations}}
        <div class="d-flex flex-column justify-content-end ml-4">
          <div data-cy="add-schedule-item" class="btn btn-primary font-weight-bolder btn-text-white"
            id="newReservationLink">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add
          </div>
        </div>
        {{/if}}
      </div>
    </div>
  </div>

  <div class="container">
    <div class="card card-cusom">
      <table data-cy="reservation-list-table" class="table" id="subsidy-report-table" style="display:block;overflow-x:auto;border:none;width:100%">
        <tbody style="display:table; width:100%">
          <tr>                  
            <th style="border-top:none;">Name</th>
            <th style="border-top:none;">Date</th>
            <th style="border-top:none;">Time</th>
            {{#if reservationTypeEnabled }}
              <th style="border-top:none;">Type</th>
            {{/if}}
            {{#if scheduleTypes}}
  					  <th style="border-top:none;">Schedule Type</th>
  					{{/if}}
            {{#if userCanAddOrModifyReservations}}
              <th style="border-top:none;">Actions</th>
            {{/if}}
          </tr>
          {{#each reservations}}
            <tr>    
              <td data-cy="reservation-name">{{selectedPeople selectedPerson}}</td>
              <td data-cy="reservation-date">{{#if recurringFrequency}}<span class="mr-2"><i class="fad fad-primary fa-sync-alt"></i></span>{{/if}}{{scheduledDateFormatted}}</td>
              <td data-cy="reservation-time">{{scheduledTime}} {{#if scheduledEndTime}} - {{scheduledEndTime}}{{/if}}</td>
              {{#if reservationTypeEnabled}}
                <td>{{reservationType.capitalizeFirstLetter}}</td>
              {{/if}}
              {{#if scheduleTypes}}
    					  <td data-cy="reservation-schedule-type" >{{scheduleTypeNameFor scheduleType}}</td>
    					{{/if}}
              {{#if userCanAddOrModifyReservations}}
                <td>
                  <a href="#" data-cy="edit-reservation" class="font-weight-bold text-primary editReservationLink" data-id="{{idToEdit}}">Edit</a> | 
                  <a href="#" data-cy="cancel-reservation" class="font-weight-bold text-primary cancelReservationLink" data-id="{{idToEdit}}">Cancel</a> 
                </td>
              {{/if}}
            </tr>
          {{/each}}
        </tbody>
      </table>
    </div>
  </div>
</template>
