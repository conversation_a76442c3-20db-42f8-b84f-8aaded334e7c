import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal } from "../main";
import _ from '../../../lib/util/underscore';
import './momentForm.js';
import './momentFormModal.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { MomentDefinitions } from '../../../lib/collections/momentDefinitions.js';

function handleFormSubmit(event, template, completionHandler) {
	event.preventDefault();

	$("#momentFormSave").prop('disabled', true).text("Posting...");

	let momentData = {};
	let time = $("#dateTimePicker").val();
	let timeBegin = (time) ? moment(time, ["h:mm a", "HH:mm"]).format("h:mm a") : null;

	let dateVal = $("#momentDatePicker").val();
	let dateBegin = (dateVal) ? moment(dateVal).format("MM/DD/YYYY") : null;

	switch (Session.get('activeMomentType')) {
		case 'potty':
			momentData["comment"] = $("#comment").val();
			momentData["pottyType"] = $("#pottyType button.active").data("pottytype") || "";
			if (Orgs.current().hasCustomization("moments/potty/showContinence"))
				momentData["pottyTypeContinence"] = $("#pottyTypeContinence button.active").data("pottytypecontinence") || "";
			momentData["pottyTraining"] = $("#pottyTraining button.active").data("pottytraining") || "";
			momentData["pottyAppliedOintment"] = $("#pottyAppliedOintment").is(':checked');
			momentData["momentType"] = "potty";
			break;
		case 'food':
			const foodItems = $(".food-item-container").map(function () {
				const firstButton = $(this).find("button").first(),
					activeButton = $(this).find("button.active").first();
				return {
					name: unescape($(firstButton).data("name")),
					amount: $(activeButton).data('food-item-amount')
				};
			}
			).toArray();
			momentData["comment"] = $("#comment").val();
			momentData["foodType"] = $("#foodType button.active").data("foodtype") || "";
			if (Orgs.current().hasCustomization("moments/food/showPercentage"))
				momentData["foodAmountPercent"] = $("#foodAmountPercent").val() || "";
			if (momentData["foodType"] == "Bottle") {
				momentData["foodBottleAmountBreastmilkOffered"] = parseFloat($(".foodBottleWholeAmount[data-category='BreastmilkOffered']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='BreastmilkOffered']").val());
				momentData["foodBottleAmountBreastmilkConsumed"] = parseFloat($(".foodBottleWholeAmount[data-category='BreastmilkConsumed']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='BreastmilkConsumed']").val());
				momentData["foodBottleAmountFormulaOffered"] = parseFloat($(".foodBottleWholeAmount[data-category='FormulaOffered']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='FormulaOffered']").val());
				momentData["foodBottleAmountFormulaConsumed"] = parseFloat($(".foodBottleWholeAmount[data-category='FormulaConsumed']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='FormulaConsumed']").val());
				momentData["foodBottleAmountMilkOffered"] = parseFloat($(".foodBottleWholeAmount[data-category='MilkOffered']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='MilkOffered']").val());
				momentData["foodBottleAmountMilkConsumed"] = parseFloat($(".foodBottleWholeAmount[data-category='MilkConsumed']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='MilkConsumed']").val());
			}
			else if (momentData["foodType"] == "Baby Food") {
				momentData["foodBabyFoodType"] = $("#foodBabyFoodType button.active").data("babyfoodtype") || "";
				momentData["foodBottleAmountBabyFoodConsumed"] = parseFloat($(".foodBottleWholeAmount[data-category='BabyFoodConsumed']").val()) + parseFloat($(".foodBottleFractionalAmount[data-category='BabyFoodConsumed']").val());
			} else if (momentData["foodType"] == "Cereal") {
				momentData["foodBottleAmountCerealConsumed"] = parseFloat($(".foodBottleWholeAmount[data-category='CerealConsumed']").val());
			}
			else if (momentData["foodType"] == "Tube")
				momentData["foodTubeAmount"] = $("#foodTubeAmount").val();
			else if (!_.isEmpty(foodItems)) {
				momentData["foodItems"] = foodItems;
			} else
				momentData["foodAmount"] = $("#foodAmount button.active").data("foodamount") || "";
			momentData["momentType"] = "food";
			break;
		case 'sleep':
			momentData["comment"] = $("#comment").val();
			if (!Orgs.current().hasCustomization("moments/sleep/showEndSleepButton")
				|| (Orgs.current().hasCustomization("moments/sleep/showEndSleepButton") &&
					Session.equals("activeMomentSleepShowEndTimeBox", true)))
				momentData["endTime"] = moment($("#dateTimePickerEnd").val(), ["h:mm a", "HH:mm"]).format("h:mm a");

			momentData["momentType"] = "sleep";
			momentData["sleepDidNotSleep"] = $("#sleepDidNotSleep").is(':checked');
			break;
		case 'activity':
			momentData["comment"] = $("#comment").val();
			momentData["activityEngagement"] = $("#activityEngagement button.active").data("activityengagement") || "";
			momentData["activityType"] = $("#activityType").val() || "";
			momentData["momentType"] = "activity";
			break;
		case 'supplies':
			momentData["comment"] = $("#comment").val();
			momentData["supplyType"] = $("#supplyType").val() || "";
			momentData["momentType"] = "supplies";
			break;
		case 'medical':
			if (Orgs.current().hasCustomization("moments/medical/useProfileMedications")) {
				momentData["medicalMedicationId"] = $("#medication").val();
				if (!momentData["medicalMedicationId"])
					return mpSwal.fire("You must select a medication before you can post this moment.");
			} else {
				momentData["medicalMedicationName"] = $("#medicationName").val();
				momentData["medicalDoctorName"] = $("#doctorName").val();
				momentData["medicalMedicineType"] = $("#medicineType").val() || "";
				momentData["medicalMedicineAmount"] = $("#medicationAmount").val();
			}
			momentData["comment"] = $("#comment").val();
			momentData["medicalAdministeredBy"] = $("#administeredBy").val() || "";
			momentData["momentType"] = "medical";
			break;
		case 'mood':
			momentData["comment"] = $("#comment").val();
			momentData["moodLevel"] = $("#moodLevel button.active").data("moodlevel") || "";
			momentData["momentType"] = "mood";
			break;
		case 'checkout':
			if (Orgs.current().hasCustomization("moments/checkout/showTransportation"))
				momentData["checkOutTransportation"] = $("#checkOutTransportation").val();
			momentData["momentType"] = "checkout";
			momentData["comment"] = $("#comment").val();
			break;
		case 'checkin':
			if (Orgs.current().hasCustomization("moments/checkin/showTransportation"))
				momentData["checkInTransportation"] = $("#checkInTransportation").val()
			momentData["momentType"] = "checkin";
			momentData["comment"] = $("#comment").val();
			break;
		case 'move':
			momentData["momentType"] = "move";
			break;
		case 'alert':
			momentData["momentType"] = "alert";
			momentData["comment"] = $("#comment").val();
			momentData["alertSendTypeEmail"] = $("#inputAlertEmail").prop("checked");
			momentData["alertSendTypeText"] = $("#inputAlertSms").prop("checked");
			momentData["alertSendTypePush"] = $("#inputAlertPush").prop("checked");
			momentData["smsAlert"] = Session.get("newMomentSmsAlertText");
			if (!(momentData["alertSendTypeEmail"] || momentData["alertSendTypeText"] || momentData["alertSendTypePush"])) {
				$("#momentFormSave").prop('disabled', false).text("Post");
				return mpSwal.fire("You need to select at least one delivery method for the notification.");
			}
			momentData["alertShowCheckin"] = $("#alertShowCheckin").is(':checked');
			break;
		case 'prospect':
			momentData["momentType"] = "prospect";
			momentData["prospectType"] = $("#prospectType button.active").data("prospecttype") || "";
			momentData["comment"] = $("#comment").val();
			break;
		case 'learning':
			momentData["momentType"] = "learning";
			momentData["comment"] = $("#comment").val();
			momentData["learningType"] = $("#learningType").val();
			momentData["learningCurriculumId"] = $("#learningCurriculum").val();
			const learningAssessmentLevel = $("#learningAssessmentLevel button.active").data("learningassessment");
			if (learningAssessmentLevel != null) momentData["learningAssessmentLevel"] = learningAssessmentLevel;
			break;
		case 'portfolio':
			momentData["momentType"] = "portfolio";
			momentData["comment"] = $("#comment").val();
			if (!Session.get("editMomentId")) {
				const portfolioCurriculumId = $("#learningCurriculum").val();
				momentData["portfolioCurriculumId"] = portfolioCurriculumId;
			}
			const assessments = Session.get("activeMomentPortfolioAssessments");
			momentData["portfolioAssessments"] = assessments;
			break;
		case 'incident':
			momentData["momentType"] = "incident";
			momentData["comment"] = $("#comment").val();
			momentData["incidentNature"] = $("#incidentNature").val();
			momentData["incidentActionTaken"] = $("#incidentActionTaken").val();
			momentData["incidentLocation"] = $("#incidentLocation").val();
			break;
		case 'illness':
			momentData["momentType"] = "illness";
			momentData["comment"] = $("#comment").val();
			momentData["illnessSymptoms"] = $("#illnessSymptoms").val();
			break;
		case 'ouch':
			momentData["momentType"] = "ouch";
			momentData["comment"] = $("#comment").val();
			momentData["ouchDescription"] = $("#ouchDescription").val();
			momentData["ouchCare"] = $("#ouchCare").val();
			momentData["ouchContactedParent"] = $("#ouchContactedParent button.active").data("parentcontact") || ""; //$("#ouchCalledParent").is(':checked');
			if ($("#calledParentTime").val() && $("#calledParentTime").val() != "") momentData["ouchCalledParentTime"] = new moment($("#calledParentTime").val(), ["h:mm a", "HH:mm"]).format("h:mm a");
			momentData["ouchContactedDoctor"] = $("#ouchContactedDoctor button.active").data("doctorcontact") || "";//$("#ouchCalledDoctor").is(':checked');
			if ($("#calledDoctorTime").val() && $("#calledDoctorTime").val() != "") momentData["ouchCalledDoctorTime"] = new moment($("#calledDoctorTime").val(), ["h:mm a", "HH:mm"]).format("h:mm a");
			momentData["ouchNurseNotified"] = $("#ouchNurseNotified").is(':checked');
			momentData["ouchProfessionalMedication"] = $("#ouchProfessionalMedication").is(':checked');
			break;
		default:
			let momentDefinition = MomentDefinitions.findByMomentType(Session.get('activeMomentType'));
			if (momentDefinition) {
				momentData["isDynamic"] = true;
				momentData["momentDefinitionId"] = momentDefinition._id;
				momentData["momentType"] = momentDefinition.momentType;
				momentData["dynamicFieldValues"] = {};
				for (const momentField of momentDefinition.momentFields) {
					switch (momentField.fieldType) {
						case "text":
						case "string":
						case "customerDefinedList":
						case "select":
							momentData["dynamicFieldValues"][momentField.dataId] = $("#momentField" + momentField.dataId).val();
							break;
						case "buttons":
							momentData["dynamicFieldValues"][momentField.dataId] = $("#momentField" + momentField.dataId + " button.active").data("value");
							break;
						case "checkbox":
							momentData["dynamicFieldValues"][momentField.dataId] = $("#momentField" + momentField.dataId).is(":checked");
							break;
					}
				}
				if (!momentDefinition.hideComment) momentData["comment"] = $("#comment").val();
				if (momentDefinition.internalOnly) momentData["adminOnly"] = $("#momentFieldadminOnly").prop("checked");
			} else {
				momentData["momentType"] = "comment";
				momentData["comment"] = $("#comment").val();
			}
			break;
	}

	momentData["time"] = timeBegin;
	momentData["date"] = dateBegin;
	/*
	  checked with OG app and the 'owner' here and the This obj are undefined
	*/
	// momentData["owner"] = this._id;

	if ($("#inputTagAllInGroup").prop("checked")) {
		momentData["tagAllInGroup"] = true;
		momentData["tagGroupId"] = Session.get("currentGroupId");
	} else if ($("#inputSendEntireOrg").prop("checked")) {
		momentData["tagEntireOrg"] = true;
	} else {
		momentData["tagAllInGroup"] = false;
		let taggedData = $('#moment-tagify').val();
		if (taggedData) {
			try {
				let tagArr = [];
				let arr = JSON.parse(taggedData);
				arr.forEach((item, i) => {
					tagArr.push(`${item.type}|${item.id}`)
				});
				momentData["taggedPeople"] = tagArr;
			} catch (e) {
				console.log(e);
				mpSwal.fire("Error", "There is an issue with tagging", "error");
			}
		} else {
			momentData["taggedPeople"] = Session.get("taggedPeople");
		}
	}

	if ($("#inputSendOnlyCheckins").prop("checked") && $("#inputSendEntireOrg").prop("checked") == false) {
		momentData["tagOnlyCheckins"] = true;
		momentData["tagEntireOrg"] = true;
	}

	if (!momentData.taggedPeople && !momentData.tagEntireOrg && !momentData.tagAllInGroup) {
		$("#momentFormSave").prop('disabled', false).text("Post");
		return mpSwal.fire("At least one person must be tagged.");
	}

	if (template.data && template.data.momentId) momentData["_id"] = template.data.momentId;
	if (Session.get("activeMomentLockMomentType")) momentData["isDemo"] = true;

	let currentFileList = Session.get("momentFormFileList");

	let postMoment = function (passedMoment) {
		let successAction = function () {
			Session.set("tickleUploader", false);
			_.each(currentFileList, function (cf) { console.log("adding cf:", cf); if (cf.queued) Syncs.insert(cf); });
			Session.set("tickleUploader", true);
			Session.set("editMomentId", null);
			// $("#newPostForm")[0].reset();
			console.log("calling completionhandler");
			completionHandler();
		};
		Meteor.callAsync("insertMoment", passedMoment).then(res => {
			if (!Meteor.isCordova || Meteor.status().connected)
				successAction();
		}).catch(error => { mpSwal.fire("Error", error.error, "error");
			$("#momentFormSave").prop('disabled', false).text("Post");
		}); 

		if (!Meteor.status().connected) {
			console.log("calling successAction without connection");
			successAction();
		}

	};

	if (currentFileList && currentFileList.length > 0) {
		momentData["mediaFiles"] = _.pluck(currentFileList, "uploadedFile");
	}
	
	if (momentData["momentType"] == "alert") {
		mpSwal.fire({
			title: "Are you sure?",
			text: "This alert will be sent immediately to all tagged people",
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, send it!"
		}).then(result => {
			if (result.value)
				postMoment(momentData);
			else if (result.dismiss) {
				$("#momentFormSave").prop('disabled', false).text("Post");
				//TODO: do I need to acutally close this
				// swal.close();
			}
		});
	} else {
		postMoment(momentData);
	}
}

Template.momentFormModal.helpers({
	getPreTag: function () {
		// var current = FlowRouter.current();
		// if (current.path.includes("/people/")) {
		// 	return `person|${current.params._id}`;
		// }
		return null;
	},
	shouldFade: function () {
		return Template.instance().data.shouldFade || false;
	},
	getMomentId: function () {
		return Template.instance().data.momentId || null;
	},
});

Template.momentFormModal.events({
	"click #momentFormSave": function (e, i) {
		handleFormSubmit(e, i, () => hideModal("#momentFormModal"));
	},
});
