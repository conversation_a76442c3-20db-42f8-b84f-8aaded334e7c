<template name="momentList">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#unless hideFilterButton}}
        <div class="d-flex flex-row justify-content-end mb-4">
          <div data-cy="filter-moments-profile" class="btn btn-primary font-weight-bolder btn-text-white" id="filterMomentsLink" data-toggle="collapse" data-target="#filterMomentsCollapse" aria-expanded="false" aria-controls="filterMomentsCollapse">
            <i class="fad-regular fad fa-filter fa-swap-opacity mr-2" style="color:#fff"></i>Filter{{filterText}}
          </div>
        </div>
      {{/unless}}
      <div class="collapse mb-4" id="filterMomentsCollapse">
        <div class="card card-custom gutter-b" >
          <div class="card-body">
            <div class="d-flex flex-row justify-content-between">
              <div class="d-flex flex-column">
                <span class="text-muted font-weight-bolder">Movement:</span>
                <div class="mb-2">
                  <a data-cy="select-all-movement" href="#" class="select-all-filter-group font-weight-bolder" data-group="check-ins">Select all</a> |  <a data-cy="clear-all-movement" href="#" class="clear-filter-group font-weight-bolder" data-group="check-ins">Clear</a>
                </div>
                <div class="checkbox-list">
                  <label class="checkbox checkbox-primary">
                    <input data-cy="check-ins" type="checkbox" class="moment-filter" checked={{activeFilter "check-ins"}} data-group="check-ins" data-target="check-ins"/>
                    <span></span>
                    Check-ins
                  </label>
                  <label class="checkbox checkbox-primary">
                    <input data-cy="check-outs" type="checkbox" class="moment-filter" checked={{activeFilter "check-outs"}} data-group="check-ins" data-target="check-outs"/>
                    <span></span>
                    Check-outs
                  </label>
                  <label class="checkbox checkbox-primary">
                    <input data-cy="moves" type="checkbox" class="moment-filter" checked={{activeFilter "moves"}} data-group="check-ins" data-target="moves"/>
                    <span></span>
                    Moves
                  </label>
                </div>
              </div>
              <div class="d-flex flex-column">
                <span class="text-muted font-weight-bolder">Moments:</span>
                <div class="mb-2">
                  <a data-cy="select-all-moments" href="#" class="select-all-filter-group font-weight-bolder" data-group="momentTypes">Select all</a> | <a data-cy="clear-all-moments" href="#" class="clear-filter-group font-weight-bolder" data-group="momentTypes">Clear</a><br/>
                </div>
                <div class="checkbox-list">
                  {{#each mt in availableMomentTypes}}
                  <label class="checkbox checkbox-primary">
                    <input data-cy="check-{{mt.momentType}}" type="checkbox" class="moment-filter" checked={{activeFilter mt.momentType}} data-group="momentTypes" data-target={{mt.momentType}} />
                    <span></span>
                    {{mt.prettyName}}
                  </label>
                  {{/each}}
                </div>
              </div>
              <div class="d-flex flex-column">
                <span class="text-muted font-weight-bolder">Other:</span>
                <div class="checkbox-list">
                  <label class="checkbox checkbox-primary">
                    <input data-cy="media-only" type="checkbox" class="moment-filter" checked={{activeFilter "media-only"}} data-group="other" data-target="media-only"/>
                    <span></span>
                    Media only
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{#each post}}
        {{ > _momentListRow }}
      {{/each}}
      {{#if shouldShowMoreButton}}
        <div class="d-flex align-items-center justify-content-center">
          <div id="showMoreMoments" class="btn btn-primary font-weight-bolder">Show more</div>
        </div>
      {{/if}}
    </div>
  </div>
</template>
