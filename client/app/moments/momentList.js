import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import './momentList.html';
import './partials/_momentListRow';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {AvailableCustomizations} from "../../../lib/customizations";
import { People } from '../../../lib/collections/people';
import { Moments } from '../../../lib/collections/moments';



const updateFilters = function(target, instance, session) {
  const key = target.data("target"),
  currentValue = instance.activeFilters.get(key) || (key=="media-only" ? "hide" : "show"); 

  instance.activeFilters.set(key, currentValue == "show" ? "hide" : "show" );
  instance.momentLimit.set(20);
  session.set("momentListFilters", instance.activeFilters.all());
  subscribeToMoments(instance)
}

Template.momentList.onCreated( function() {
	const currentUrl = FlowRouter.current().path,
		defaults = (currentUrl.includes("/people") || currentUrl.includes("/group")) ? {} : Session.get('momentListFilters');
	
	this.activeFilters = new ReactiveDict(null, defaults);
	this.momentLimit = new ReactiveVar(20);

	const instance = this;
	subscribeToMoments(instance)
});

function subscribeToMoments(instance) {
	const filterState = instance.activeFilters.all();
	const currentOrg = Orgs.current();
	const momentLimit = instance.momentLimit.get();
	if (currentOrg) {
		if (instance.data.source == "person") {
			const currentPath = FlowRouter.current();
			const ownerId = currentPath.params._id;
			instance.subscribe('personMomentsWithOptions', { personId: ownerId, limit: momentLimit, filterState: currentFilterState(filterState, currentOrg) });
		} else if (instance.data.source == "group") {
			instance.subscribe('groupMomentsWithOptions', { limit: momentLimit, groupId: instance.data._id, filterState: currentFilterState(filterState, currentOrg) });
		} else {
			instance.subscribe('dashboardMomentsWithOptions', { limit: momentLimit, showAll: true, filterState: currentFilterState(filterState, currentOrg) });
		}
	}
}

Template.momentList.helpers({
  "hideFilterButton": function() {
    return Template.instance().data.hideFilter == "true";
  },
	"shouldShowMoreButton": function() {
		return (Moments.find().count() >= parseInt(Template.instance().momentLimit.get()));
	},
	"activeFilter": function(name) {
		const filterStatus = Template.instance().activeFilters.get(name) || (name=="media-only" ? "hide" : "show");
		return filterStatus == "show";
	},
	"post": function() {
		const user = Meteor.user();
		const currentPerson = user && user.fetchPerson();
		const currentPersonType = currentPerson ? currentPerson.type : "family";
		var ownerId = null;
		var query = {};
		if (FlowRouter.current().route.name == "person") {
			var currentPath = FlowRouter.current();			
			ownerId=currentPath.params._id;
			query={$or: [{owner:ownerId}, {taggedPeople:ownerId}]};
		}

		if (FlowRouter.current().route.name == "moment.show") {
			query = {_id: FlowRouter.current().params.id};
			console.log("here wtih this id:", query);
		}

		const filterState = currentFilterState(Template.instance().activeFilters.all(), Orgs.current()),
			showAllTypes = filterState.showAll,
			showMediaOnly = filterState.showMediaOnly;

		return Moments.find(query, {sort: {sortStamp: -1}}).map( function (m, index) {
			m.peopleDescription = "";
			var peopleNamesList = [];

			if (m["taggedPeople"]) {
				m.avatarUrl = "";
				var backupUrl = "";
				var backupInitials = "";

				for(var i = 0; i < m["taggedPeople"].length; i++) {
					const peopleQuery = { _id: m["taggedPeople"][i] };
					if (!_.contains(["admin", "staff"], currentPersonType)) {
						peopleQuery.type = { $in: ["family", "person"] };
					}

					let p = People.findOne(peopleQuery);
					if (p) {
						if (m["taggedPeople"][i] != ownerId) {
							peopleNamesList.push("<a href='#' data-id='" + p._id + "' class='linkPerson'>" + p.firstName + " " + p.lastName + "</a>");
							backupUrl = p.getAvatarUrl();
							if (backupInitials == "") backupInitials = p.personInitials();
						} else {
							peopleNamesList.unshift("<a href='#' data-id='"+ p._id + "' class='linkPerson'>" + p.firstName + " " + p.lastName +"</a>");
							m.avatarUrl = p.getAvatarUrl();
							m.taggedPersonInitials = p.personInitials();
						}
					}
				}
				if (peopleNamesList.length > 0) {
					if (!m.taggedPersonInitials && backupInitials != "" ) m.taggedPersonInitials = backupInitials;
					m.peopleDescription += peopleNamesList[0];
					if (peopleNamesList.length > 1) {
						m.peopleDescription += " with ";
						for(var i=1; i<peopleNamesList.length; i++) {
							m.peopleDescription += peopleNamesList[i];
							if (i<(peopleNamesList.length - 1)) m.peopleDescription += ", ";
						}
					}
				}
				
				if (m.avatarUrl == "" && backupUrl != "") m.avatarUrl = backupUrl;
				if (m.avatarUrl == "/img/default_user_image_500_500.png") m.avatarUrl = "";
			}
			
			// FUTURE config option -- allow humanized time descriptions
			//var x = moment(m.createdAt), y = moment(), z = moment.duration(y - x).humanize();
			//m.timeDescription = z + " ago";
			
			//m.timeDescription = moment(m.createdAt).format("ddd M/D/YY h:mm a");

			m.timeDescription = (m.date ? moment(m.date,"MM/DD/YYYY").format("ddd M/D/YY") : moment(m.sortStamp).format("ddd M/D/YY"))
					+ " " 
					+ (m.time || moment(m.sortStamp).format("h:mm a"));
			m.momentDisplayTemplate = m["momentType"] + "MomentDisplay";

			return m;
		}).filter( (m) => {
			return (showAllTypes || _.contains( filterState.activeFilters, m.momentType)) && (!showMediaOnly || (m.mediaFiles && m.mediaFiles.length > 0));
		});
	},
	"isVideoClass": function() {
		return "video";
	},
	"showMobileVideoContainer": function() {
		return Meteor.isCordova;
	},
	"availableMomentTypes": function() {
		return availableMomentTypes(Orgs.current());
	},
	"header": function() {
		return Template.instance().data && Template.instance().data.header;
	},
	"filterText": function() {
		const allFilters = Template.instance().activeFilters && Template.instance().activeFilters.all();
		let keyCount = 0;
		_.each( (allFilters), (val, key) => {  if ((key != "media-only" && val == "hide") || (key=="media-only" && val =="show")) keyCount++;});
		return keyCount > 0 ? " (" + keyCount + ")" : "";
	}
});

function availableMomentTypes(org) {
	let types = org && org.availableMomentTypes();
	const user = Meteor.user();
	const currentPerson = user && user.fetchPerson();
	const currentPersonType = currentPerson ? currentPerson.type : "family";
	if (types && currentPersonType !== "family" && currentPersonType !== "person") types.push({momentType: 'nameToFace', prettyName: "Name To Face"});
	if (org && org.hasCustomization("inquiries/enabled")) types.push({"momentType": "prospect", prettyName: "Prospect"});
	if (org && org.hasCustomization(AvailableCustomizations.DOOR_LOCKS_ZKTECO)) types.push({"momentType": "doorUnlocked", prettyName: "Door Unlocked"});
	return _.sortBy(types, "prettyName");
}

function currentFilterState(activeFilters, org) {
	let filterState = [], showAll;
	_.each(availableMomentTypes(org), (mt) => {
		const selection = activeFilters[mt.momentType];
		if (selection != "hide") {
			filterState.push(mt.momentType);
		}
	});

	showAll = filterState.length == availableMomentTypes(org).length;
	
	if (activeFilters["check-ins"] != "hide") filterState.push("checkin");  else showAll = false;
	if (activeFilters["check-outs"] != "hide") filterState.push("checkout"); else showAll = false;
	if (activeFilters["moves"] != "hide") filterState.push("move"); else showAll = false;
	if (activeFilters["media-only"] == "show") filterState.push("media-only");
	const out = {
		showAll,
		showMediaOnly: _.contains(filterState, "media-only"),
		activeFilters: filterState
	};
	
	return out;
}

Template.momentList.events({
	'click .moment-filter': function(e, instance) {
		const target = $(e.currentTarget);
    updateFilters(target, instance, Session);
	},
	"click .linkPerson": function(e) {
		e.preventDefault();
		var target = $(e.currentTarget);
		if (FlowRouter.getQueryParam("printable"))
			window.open("/people/" + target.attr("data-id"));
		else
			FlowRouter.go("person", { _id :target.attr("data-id") });
	},
	"click .deleteMoment": function(e) {
		var momentId = $(e.target).data("id");
		return mpSwal.fire({  
			title: "Are you sure?",   
			text: "You will not be able to recover this moment once deleted!",   
			type: "warning",   
			showCancelButton: true,   
			confirmButtonText: "Yes, delete it!"
		}).then( result => {   
			if (result.value) Meteor.callAsync("deleteMoment", momentId).then(result => {}).catch(error => {
				mpSwal.fire("Error", error.reason, "error");
			});
		});
	},
	"click .modifyMoment": function(e) {
		var momentId = $(e.target).data("id");
		Session.set("editMomentId", momentId);
		/*if (Meteor.isCordova) {
			$(window).scrollTop(0);
			$("#momentForm").fullscreen();
		}*/
		
		$("#momentForm").modal();
	},
	"click .viewMomentData": function(e) {	
		var momentId = $(e.target).data("id");
		var m = Moments.findOne(momentId);
		if (m) {
			
			mpSwal.fire({
				title: "Moment Data",
				html: "<b>Created Time:</b> " + moment(m.createdAt).format("M/D/YY h:mm a") +
					"<br/><b>Moment Time:</b> " + moment(m.sortStamp).format("M/D/YY h:mm a") 
			});
		}
	},
	"click .showMediaLink": function(e) {
		var videoId = $(e.currentTarget).data("id");
		var tokenId = $(e.currentTarget).data("mediatoken");
		var currentMoment = Moments.findOne(videoId);
		
		var currentMedia = _.find(currentMoment.attachedMedia(), function (m) { return m.mediaToken == tokenId});
		
		if (currentMedia && currentMedia.isVideo) {
			if (Meteor.isCordova) {
				const videoUrl = currentMedia.pathForVideoFormat("mp4");
				window.plugins.streamingMedia.playVideo(videoUrl);

			} else {
				pathString = currentMedia.pathForVideoFormat("mp4") + "|" + currentMedia.pathForVideoFormat("webm");
				Session.set("videoPlayerLocation", pathString );
				$("#videoPlayerModal").modal();
			}
		} else {
			
		}
	},
	"click .shareMoment": function(e) {
		if (window.plugins && window.plugins.socialsharing) {
			var momentId = $(e.target).data("id");
			var currentMoment = Moments.findOne(momentId);
			var attachedFiles = [];
			var videoLink;
			
			_.each(currentMoment.attachedMedia(), function(am) {
				console.log("am", am);
				if (am.isPhoto) attachedFiles.push(am.getTimelinePhoto);
				if (am.isVideo) {
					attachedFiles.push(am.mediaVideoPath + ".mp4");
					videoLink = am.mediaRedirectorPath;
				}
			});
			var options = {
				message: (currentMoment.comment || ""), //+ (attachedFiles.length == 0 && videoLink) ? videoLink : null,
				subject: 'A shared moment from MomentPath'
			};
			if (attachedFiles.length > 0 ) options.files = attachedFiles;
			//if ( videoLink) options.url = videoLink;

			console.log("sharing options", options);
			
			var onSuccess = function(result) {
			  console.log("Share completed? " + result.completed); // On Android apps mostly return false even while it's true
			  console.log("Shared to app: " + result.app); // On Android result.app is currently empty. On iOS it's empty when sharing is cancelled (result.completed=false)
			}

			var onError = function(msg) {
			  console.log("Sharing failed with message: " + msg);
			}

			window.plugins.socialsharing.shareWithOptions(options, onSuccess, onError);
		}
	},
	"click .sendLike": function(e) {
		e.preventDefault();
		const momentId = $(e.currentTarget).data("id");
		const likeType = $(e.currentTarget).data("type");
		Meteor.callAsync("timelineLikeMoment", {momentId, likeType}).then(res => {}).catch(err => {});
	},
	"click .open-reaction-buttons": function(e) {
		var momentId = $(e.currentTarget).data("id");
		$(e.currentTarget).hide();
		$(".reaction-buttons[data-id='" + momentId + "']").show().css("display", "inline-block");
	},
	"click #showMoreMoments": function(e, instance) {
		instance.momentLimit.set(instance.momentLimit.get() + 20);
		subscribeToMoments(instance)
	},
	"click .select-all-filter-group": function(e, instance) {
		e.preventDefault();
		const groupName = $(e.currentTarget).data("group");
		$("input[data-group='" + groupName + "']:not(:checked)").each(function(i, b) {
      const target = $(b);
      updateFilters(target, instance, Session);
    });
	},
	"click .clear-filter-group": function(e, instance) {
		e.preventDefault();
		const groupName = $(e.currentTarget).data("group");
    console.log("click clear");
		$("input[data-group='" + groupName + "']:checked").each(function(i, b) {
      const target = $(b);
      updateFilters(target, instance, Session);
    });
	}
});

// Template.baseMomentDisplay.helpers({
// 	"showShareButton": function() {
// 		return (this.attachedMedia().length > 0 && window.plugins && window.plugins.socialsharing);
// 	},
// 	"showLikeButton": function() {
// 		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
// 		return (this.momentType != "checkin" && this.momentType !="checkout" && currentPerson && currentPerson.type=="family" && !this.likedByPerson(currentPerson._id));
// 	},
// 	"isMomentEditable": function() {
// 		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
// 		return ( !Router.current().url.includes("/welcome") && currentPerson && 
// 					processPermissions({
// 						assertions: [{ context: "moments", action: "edit"}],
// 						evaluator: (person) => person.type=="admin" || person.type == "staff"
// 					})
// 				) ? true : false;
// 	},
// 	"reactionText": function() {
// 		const meteorUser = Meteor.user(), person = meteorUser && meteorUser.fetchPerson();
// 		var personType = person.type;
// 		if (personType=="staff" || personType=="admin") {
// 			outputText = "";
// 			_.each(this.reactions, function(r) { 
// 				outputText += (outputText != "" ? ", " : "") + r.sender;
// 			});
// 			if (outputText != "")
// 				return "Liked by: " + outputText;
// 		} else if (personType=="family" && this.likedByPerson(person._id)) {
// 			return "Liked by you";
// 		}
// 	},
// 	"reactionData": function() {
// 		const reactionsMap = [], meteorUser = Meteor.user(), person = meteorUser && meteorUser.fetchPerson();
// 		const iconMap = {
// 			like: 'thumb-up-2.png',
// 			smile: 'thumb-up-2.png',
// 			love: 'heart-2.png',
// 			celebrate: 'party-popper.png',
// 			laugh: 'big-grin.png',
// 			wink: 'upside-down-face.png',
// 			not_sure: 'upside-down-face.png',
// 			meh: 'frowning-face.png',
// 			sad: 'frowning-face.png',
// 		};
// 		let activePersonalReaction = false;
// 		_.each( this.reactions, (r, ownerId) => {
// 			const reactionType = r.likeType || "smile", item = _.find( reactionsMap, (ri) => ri.reactionType == reactionType);
// 			const rp = People.findOne({_id: ownerId});
// 			if (item) {
// 				if (rp) item.nameList += ", " + rp.firstName + " " + rp.lastName
// 				item.count++;
// 			}
// 			else {
// 				const reaction = {reactionType, count: 1, icon: iconMap[reactionType]};
// 				if (rp && (person.type == "admin" || person.type == "staff"))
// 					reaction["nameList"] = rp.firstName + " " + rp.lastName;
// 				reactionsMap.push(reaction );
// 			}
// 			if (person._id == ownerId) activePersonalReaction = true;
// 		});
// 
// 		return reactionsMap;
// 	},
// 	"includeInlineVideo": function() {
// 		return this.isVideo && Meteor.isCordova;
// 	},
// 	"engagementData": function() {
// 		if (!Router.current().url.includes("welcome")) return;
// 
// 		return true;
// 	},
// 	"isMedia": function() {
// 		return (this && this.mediaFileType && _.contains(["image","video"],this.mediaFileType));
// 	},
// 	"attachmentLink": function() {
// 		return Meteor.settings.public.photoBaseUrl + "uploads/" + this.mediaPath;
// 	},
// 	"attachmentName": function() {
// 		return this.mediaFileName || "view";
// 	}
// });

// Template.checkinMomentDisplay.helpers({
// 	"extractCheckInHealthChecks": function() {
// 		return _.map(this.checkInHealthChecks, function(value, key) { 
// 			return {checkName: key, checkValue: value, checkIcon: value=="good" ? "fa-smile-o" : "fa-frown-o"};
// 		});
// 	},
// 	"pinCodeFormFields": function() {
// 		let values = [];
// 		const currentMoment = this;
// 		_.each(Orgs.current() && Orgs.current().pinCodeCheckinFields(), (field) => {
// 			if (currentMoment[field.dataId]) values.push({fieldLabel: field.label, fieldValue: currentMoment[field.dataId]});
// 		})
// 		return values;
// 	}
// });
// 
// Template.checkoutMomentDisplay.helpers({
// 	"extractCheckOutHealthChecks": function() {
// 		return _.map(this.checkOutHealthChecks, function(value, key) { 
// 			return {checkName: key, checkValue: value, checkIcon: value=="good" ? "fa-smile-o" : "fa-frown-o"};
// 		});
// 	}
// });
// Template.baseMomentDisplay.rendered = function() {
// 	this.$(".post").fadeIn(500);
// };
