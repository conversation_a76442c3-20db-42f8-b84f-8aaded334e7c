import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import $ from 'jquery';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import './momentForm.html';
import { filetypeinfo } from 'magic-bytes.js';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import { Log } from '../../../lib/util/log';
import { AvailableCustomizations } from '../../../lib/customizations';
import {
	DefaultCheckedOutMomentTypes,
	ExpandedCheckedOutMomentTypes
} from '../../../lib/constants/momentTypeConstants';
import { StringUtils } from '../../../lib/util/stringUtils';
import {MiscUtils} from "../../../lib/util/miscUtils";
import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService"
import { Groups } from '../../../lib/collections/groups';
import { Moments } from '../../../lib/collections/moments';
import Tagify from '@yaireo/tagify'
import { MomentUppy } from '../../../mpweb';
import './momentForm.html';
import './momentForms/activityMomentForm';
import './momentForms/alertMomentForm';
import './momentForms/checkinMomentForm';
import './momentForms/checkoutMomentForm';
import './momentForms/commentMomentForm';
import './momentForms/dynamicMomentForm';
import './momentForms/foodMomentForm';
import './momentForms/illnessMomentForm';
import './momentForms/learningMomentForm';
import './momentForms/medicalMomentForm';
import './momentForms/moodMomentForm';
import './momentForms/ouchMomentForm';
import './momentForms/portfolioMomentForm';
import './momentForms/pottyMomentForm';
import './momentForms/prospectMomentForm';
import './momentForms/sleepMomentForm';
import './momentForms/suppliesMomentForm';
import './momentForms/incidentMomentForm';
import { Syncs } from '../../../lib/collections/syncs';

let getTaggedPeople = async function (template) {
	let taggedPeople = Session.get("taggedPeople");
	let momentId = template.data.momentId || null;
	let preTag = template.data.preTag || null;
	if (momentId) {
		let currentMoment = Moments.findOne(momentId);
		taggedPeople = currentMoment.taggedPeople.map(function (p) { return "person|" + p; })
	} else if (preTag) {
		taggedPeople = [preTag];
	} else {
		if (!Session.get("taggedPeople")) return [];
	}

	const peoplePromises = taggedPeople.map(async function (p) {
		let curType = p.split("|")[0];
		let curId = p.split("|")[1];
		let curName = "";
		if (curType === "person") {
			let curPerson = await getPeopleById(curId);
			if (curPerson) curName = curPerson.firstName + " " + curPerson.lastName;
		} else if (curType === "role") {
			if (curId === "all_staff") curName = "All Staff";
		} else {
			let curGroup = Groups.findOne(curId);
			if (curGroup) curName = curGroup.name;
		}

		return {
			"type": curType, "id": curId, "value": curName
		};
	});

	const result = await Promise.all(peoplePromises);
	return result;
}

let convertTagifyToSessionVar = function (val) {
	if (val) {
		try {
			let tagArr = [];
			let arr = JSON.parse(val);
			arr.forEach((item, i) => {
				tagArr.push(`${item.type}|${item.id}`)
			});
			Session.set("taggedPeople", tagArr);
		} catch (e) {
			Log.error(e);
		}
	}
}

Template.momentForm.events({
	'change #momentTypeSelection': function (event, template) {
		let name = $(event.currentTarget).val();
		if (Session.get("activeMomentLockMomentType") && name != "comment") return;
		Session.set('activeMomentType', name);
		// changing the moment type can affect who should receive it (ie alert/portfolio)
		// right now brute force recreate the module with the appropriate set of people
		initTagifyModule(template);
	},
	'click #clearTagSearch': function (event) {
		event.preventDefault();
		Template.instance().searchFilterText.set("");
		$("#tag-people-search").val("");
	},
	'change #moment-tagify': function (event) {
		convertTagifyToSessionVar($("#moment-tagify").val())
	},
	'click .suggestedPeopleList .list-group-item': function (event) {
		let targetData = this.type + "|" + this.value;

		let currentList = Session.get("taggedPeople");
		if (!currentList) currentList = [];
		currentList = currentList.concat(targetData);
		Session.set("taggedPeople", _.uniq(currentList));
		$(".modal-body").scrollTop(0);
	},
	'input #tag-people-search': function () {
		Template.instance().searchFilterText.set($("#tag-people-search").val());
	},
	'click .deleteTag': function (event) {
		let targetData = this.type + "|" + this.id;
		let currentList = Session.get("taggedPeople");
		Session.set("taggedPeople", _.without(currentList, targetData));
	},
	'click #momentTypeButtons button': function (event) {
		let name = $(event.currentTarget).data('moment-type');
		if (Session.get("activeMomentLockMomentType") && name != "comment") return;
		Session.set('activeMomentType', name);
		$("#tabButtonMomentDetails").click();
		$(".modal").scrollTop(0);
	},
	'click #inputTagAllInGroup': function () {
		Session.set("isTagAllInGroupChecked", $("#inputTagAllInGroup").prop("checked"));
	},
	'click #taggedPeopleDetailButton': function () {
		$("#tabButtonMomentPeople").click();
	},
	'click #taggedPeopleDetailColumn': function (event) {
		event.preventDefault();
		$("#tabButtonMomentPeople").click();
	},
	'click #btnDirectCamera': function (event) {
		MeteorCamera.getPicture({}, function (error, data) {
			let metaContext = { tokenId: StringUtils.tokenString() };
			let fileName = metaContext.tokenId;

			let file = dataURItoBlob(data);
			handleFileUpload(metaContext, file, fileName);
		});
	},
	'change #exampleInputFile': function (event) {
		event.preventDefault();
		let uploadFile = $(event.currentTarget)[0]; //$(this)[0];
		if (uploadFile && uploadFile.files.length > 0) {
			let uploadPromises = [];
			for (let i = 0; i < uploadFile.files.length; i++) {
				let metaContext = { tokenId: StringUtils.tokenString() };
				let file = uploadFile.files[i];
				uploadPromises.push(handleFileUpload(metaContext, file));
			}
			Promise.all(uploadPromises)
			.then(() => {
				$("#exampleInputFile").val('');
			})
			.catch(error => {
				console.error("Error uploading files:", error);
				mpSwal.fire("Error", error.reason || error.error || error.message, "error");
			});
		}
	},
	'click .removeAttachedMedia': function (event) {

		let tokenId = $(event.target).data('id');
		let currentFileList = [];
		_.each(Session.get("momentFormFileList"), function (item) {
			if (item.metaContext.tokenId != tokenId) currentFileList.push(item);
		});
		Session.set("momentFormFileList", currentFileList);
	},
	'input #comment': function () {
		const actionLog = Session.get("activeMomentActionLog") || "";
		if (!actionLog.includes("commentEntered")) Session.set("activeMomentActionLog", actionLog + "|commentEntered");
	},
	'click #btnUseSamplePhoto': function () {
		var blob = null;
		var xhr = new XMLHttpRequest();
		const imageName = ["adult", "senior"].indexOf(Orgs.current().registrationIndustry) >= 0 ? "sample-image-senior.jpg" : "sample-image-child.jpg";
		xhr.open("GET", "/img/" + imageName);
		xhr.responseType = "blob";//force the HTTP response, response-type header to be blob
		xhr.onload = function () {
			blob = xhr.response;//xhr.response is now a blob object
			var file = new File([blob], "myimage.png", { 'type': 'image/png' });
			handleFileUpload({ tokenId: StringUtils.tokenString() }, file, "myimage.png");
		}
		xhr.send();
	}
});

Template.momentForm.helpers({
	formatMomentEnabledString: function (momentType) {
		return `moments/${momentType}/enabled`;
	},
	formatMomentAdminOnlyString: function (momentType) {
		return `moments/${momentType}/adminOnly`;
	},
	activeMomentForm: function () {
		let formName = "commentMomentForm";
		if (Session.get("activeMomentType"))
			formName = Session.get("activeMomentType") + "MomentForm";
		if (!Template.hasOwnProperty(formName)&& formName !== 'incidentMomentForm')
			formName = "dynamicMomentForm";
		return formName;
	},
	people: function () {
		const person = Meteor.user().fetchPerson();
		let peopleQuery = { type: { "$in": ["person", "staff", "admin"] }, inActive: { $ne: true } };
		if (!_.contains(["alert", "portfolio"], Session.get("activeMomentType")))
			peopleQuery["checkedIn"] = true;
		peopleQuery = { ...peopleQuery, orgId: person.orgId };
		//Support an All Staff tag
		return getPeopleData(peopleQuery, { sort: { lastName: 1, firstName: 1 } }).then((data) => {
			let people = data?.map(function (p) {
				return {
					value: p._id, label: p.firstName + ' ' + p.lastName,
					type: "person", avatarUrl: getAvatarUrl(p),
					checkInGroupId: p.checkInGroupId, defaultGroupId: p.defaultGroupId
				};
			});
			people.unshift({ value: 'all_staff', label: "All Staff", type: "role" })

			let groups = [];
			if (Orgs.current() && !Orgs.current().hasCustomization("moments/disableTagGroups")) {
				groups = Groups.find({}, { sort: { name: 1 } }).map(function (g) {
					return { value: g._id, label: g.name + " (group)", type: "group" };
				});
			}
			let fullList = people.concat(groups);
	
			const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson()
			let currentTagList = Session.get("taggedPeople");
			let currentCheckInGroupId = currentPerson && currentPerson.checkInGroupId && currentPerson.checkInGroupId != "" ? currentPerson.checkInGroupId : null;
	
			if (!currentTagList) currentTagList = [];
			let untaggedList = _.filter(fullList, function (i) { return !_.contains(currentTagList, i.type + "|" + i.value); });
			let currentSearchFilter = Template.instance().searchFilterText.get();
	
			_.each(untaggedList, function (i, index) {
				if (i.type == "person") {
					if (i.checkInGroupId == currentCheckInGroupId ||
						((!i.checkInGroupId || !currentCheckInGroupId) && currentPerson && i.defaultGroupId == currentPerson.defaultGroupId))
						i.rank -= 1000;
				} else {
					i.rank = index;
				}
			});
			let currentFilterString = currentSearchFilter.toLowerCase().trim();
	
			let searchFilteredList = _.filter(untaggedList, function (i) {
				let currentItemLabel = i.label.trim().toLowerCase();
	
				if (currentFilterString.trim() != "" && (currentItemLabel.startsWith(currentFilterString) || currentItemLabel.indexOf(" " + currentFilterString) !== -1))
					i.rank -= 10000;
	
				return currentFilterString == "" ||
					currentItemLabel.indexOf(currentFilterString) !== -1;
			});
			let output = _.first(_.sortBy(searchFilteredList, "rank"), 30);
	
			return output;
		}).catch(err => {
			console.log(err);
		})

		
	},
	currentMoment: function () {
		let currentMomentId = Template.instance().data.momentId; //Session.get("editMomentId");
		let currentMoment = currentMomentId ? Moments.findOne(currentMomentId) : null;

		if (currentMoment) {
			Session.set("activeMomentActivityType", currentMoment.activityType);
			Session.set("activeMomentActivityEngagement", currentMoment.activityEngagement);
			Session.set("activeMomentType", currentMoment.momentType);
			Session.set("activeMomentPottyType", currentMoment.pottyType);
			Session.set("activeMomentPottyTypeContinence", currentMoment.pottyTypeContinence);
			Session.set("activeMomentPottyTraining", currentMoment.pottyTraining);
			Session.set("activeMomentFoodType", currentMoment.foodType);
			Session.set("activeMomentFoodAmount", currentMoment.foodAmount);
			Session.set("activeMomentFoodAmountPercent", currentMoment.foodAmountPercent);
			Session.set("activeMomentFoodBottleAmount", currentMoment.foodBottleAmount);
			Session.set("activeMomentFoodTubeAmount", currentMoment.foodTubeAmount);
			Session.set("activeMomentFoodFoodItemSelections", currentMoment.foodItems);
			Session.set("activeMomentFoodBabyFoodType", currentMoment.foodBabyFoodType);
			Session.set("activeMomentMedicalAdministeredBy", currentMoment.medicalAdministeredBy);
			Session.set("activeMomentMoodLevel", currentMoment.moodLevel);
			Session.set("activeMomentSleepShowEndTimeBox", currentMoment.endTime ? true : null);
			Session.set("activeMomentCheckInTransportation", currentMoment.checkInTransportation);
			Session.set("activeMomentCheckOutTransportation", currentMoment.checkOutTransportation);
			Session.set("activeMomentCheckOutPickedUpBy", currentMoment.checkedOutById);
			Session.set("activeMomentProspectType", currentMoment.prospectType);
			Session.set("activeMomentSupplyType", currentMoment.supplyType);
			Session.set("activeMomentLearningType", currentMoment.learningType);
			Session.set("activeMomentLearningCurriculum", currentMoment.learningCurriculumId);
			Session.set("activeMomentLearningAssessmentLevel", currentMoment.learningAssessmentLevel);
			Session.set("activeMomentMedicalMedicineType", currentMoment.medicalMedicineType);
			Session.set("activeMomentMedicalMedicineAmount", currentMoment.medicalMedicineAmount);
			Session.set("activeMomentMedicalMedication", currentMoment.medicalMedicationId);
			Session.set("activeMomentOuchContactedParent", currentMoment.ouchContactedParent);
			Session.set("activeMomentOuchContactedDoctor", currentMoment.ouchContactedDoctor);
			Session.set("activeMomentPortfolioAssessmentLevel", null);
			Session.set("activeMomentPortfolioCurriculum", currentMoment.portfolioCurriculumId);
			Session.set("activeMomentPortfolioAssessments", currentMoment.portfolioAssessments);
			Session.set("activeMomentSmsAlertText", currentMoment.smsAlert);
			Session.set("newMomentSmsAlertText", null);
			Session.set("activeMomentSymptoms", currentMoment.illnessSymptoms);
			Session.set("taggedPeople", currentMoment.taggedPeople.map(function (p) { return "person|" + p; }));
			Session.set("momentFormFileList", []);
			currentMoment.endTime = currentMoment.endTime || moment().format('h:mm a');
			return currentMoment;
		}
		else {
			$('#momentTypeComment').closest('.btn').button('toggle');
			return {
				time: moment().format('h:mm a'),
				endTime: moment().format('h:mm a'),
				date: moment().format('MM/DD/YYYY'),
				momentType: "comment",
				comment: ""
			};
		}
	},
	currentAttachedMedia: function () {
		return Session.get("momentFormFileList") || [];
	},
	currentMomentType: function () {
		return Session.get("activeMomentType");
	},
	currentGroup: function () {
		return Session.get("currentGroupName");
	},
	isNewMoment: function () {
		let momentId = Template.instance().data.momentId || null;
		return Session.get("activeMomentType") == "prospect" || momentId ? false : true;
	},
	hasCenterOptions: function () {
		let entireOrg = Session.get("activeMomentType") == "alert";
		let showGroup = Session.get("currentGroupId") && Session.get("activeMomentType") != "alert" ? Session.get("currentGroupId") : false;

		return entireOrg || showGroup;
	},
	showGroupTagOption: function () {
		return Session.get("currentGroupId") && Session.get("activeMomentType") != "alert" ? Session.get("currentGroupId") : false;
	},
	hideFileUpload: function () {
		return false;
	},
	showEntireOrgOption: function () {
		return Session.get("activeMomentType") == "alert";
	},
	momentDefinitions: function () {
		if (Orgs.current())
			return Orgs.current().availableDynamicMomentTypes();
	},
	suppressDetailsTagList: function () {
		return Session.get("activeMomentHideDetailsTagged");
	},
	showInsertSamplePhoto: function () {
		return Session.get("activeMomentLockMomentType");
	},
	'getMomentTypesPottyPrettyName': function () {
		return orgLanguageTranformationUtil.getMomentTypesPotty('prettyName');
	},
});

Template.momentForm.created = function () {
	//keep a local copy of the tagify prototype;
	this.tagifyModule = null;
}

Template.momentForm.rendered = async function () {
	// var webUploader = new ReactiveVar();
	let template = Template.instance();
	await initTagifyModule(template);

}

function getAvatarUrl(person) {
	if (person.avatarPath && person.avatarPath.length > 0)
		return Meteor.settings.public.photoBaseUrl + "resized/" + person.avatarPath + "-medium.jpg";
	else if (person.avatarUrl && person.avatarUrl.length > 0)
		return person.avatarUrl;
	else
		return "/img/default_user_image_500_500.png";
}

async function initTagifyModule(template) {
	// Pre-destroy existing Tagify instance
	const person = Meteor.user().fetchPerson();
	const org = Orgs.current();
	const orgAllowsCheckedOutMoments = org.hasCustomization(AvailableCustomizations.ALLOW_MOMENTS_WHEN_CHECKED_OUT);
	let preTagify = template.tagifyModule;
	if (preTagify) {
		preTagify.destroy();
	}

	let existingTaggedPeople = await getTaggedPeople(template);
	const userCheckedInGroupId = person.checkInGroupId || person.defaultGroupId;
	const allowableCheckedOutMoments = orgAllowsCheckedOutMoments ? ExpandedCheckedOutMomentTypes : DefaultCheckedOutMomentTypes;
	let tagWhitelist = [{ id: 'all_staff', value: "All Staff", type: "role" }];
	let peopleQuery = { type: { "$in": ["person", "staff", "admin"] }, inActive: { $ne: true } };

	if (!_.contains(allowableCheckedOutMoments, Session.get("activeMomentType"))) {
		peopleQuery["checkedIn"] = true;
	}
	peopleQuery = { ...peopleQuery, orgId: person.orgId };

	// Fetch people and add their default group ID and checkInGroupId
	getPeopleData(peopleQuery, { sort: { lastName: 1, firstName: 1 } }).then((res) => {
		let people = res?.map(function (p) {
			let name = p.firstName + ' ' + p.lastName;
			return {
				id: p._id,
				value: name.trim(),
				type: "person",
				avatar: getAvatarUrl(p),
				defaultGroupId: p.defaultGroupId,
				checkInGroupId: p.checkInGroupId
			};
		});
	
		let groups = [];
		if (org && !org.hasCustomization(AvailableCustomizations.DISABLE_TAG_GROUPS)) {
			groups = Groups.find({}, { sort: { name: 1 } }).map(function (g) {
				return { id: g._id, value: g.name.trim(), type: "group" };
			});
		}
	
		let designations = [];
		if (org && org.valueOverrides && org.valueOverrides.designations && person.type === "admin") {
			for (const designation of org.valueOverrides.designations) {
				designations.push({ id: designation.replace(/\s/g, ''), value: designation, type: "designation" });
			}
		}
	
		let groupedWhitelist = [];
	
		if (person.checkInGroupId) {
			groupedWhitelist = addCheckedInSameGroupSection(groupedWhitelist, people, userCheckedInGroupId, groups, org);
			groupedWhitelist = addCheckedOutSameGroupSection(groupedWhitelist, people, userCheckedInGroupId, groups, org);
		}
		groupedWhitelist = addCheckedInOtherGroupsSection(groupedWhitelist, people, userCheckedInGroupId, groups, org);
		groupedWhitelist = addCheckedOutOtherGroupsSection(groupedWhitelist, people, userCheckedInGroupId, org);
	
		groupedWhitelist = MiscUtils.sortGroupedWhitelist(groupedWhitelist);
	
		// Combine with designations, existing tagged people, and other necessary tags
		tagWhitelist = tagWhitelist.concat(designations).concat(groupedWhitelist).concat(existingTaggedPeople);
		tagWhitelist = _.uniq(tagWhitelist, false, function (z) { return z.id; });
	
		let toEl = $('#moment-tagify')[0];
	
		function suggestionItemTemplate(tagData) {
			try {
				if (tagData.type === 'separator') {
					return `<div class="text-center font-weight-bold" style="background-color: var(--primary); color: white; padding: 5px; border-radius: 2px; margin-bottom: 5px;">
													${tagData.value}
											</div>`;
				}
	
				let html = '';
				html += '<div id="' + tagData.value + '" class="tagify__dropdown__item">';
				html += '   <div class="d-flex align-items-center">';
				html += '       <span class="symbol mr-2">';
				html += '           <span class="symbol-label" style="background-image: url(\'' + (tagData.avatar ? tagData.avatar : '') + '\')"></span>';
				html += '       </span>';
				html += '       <div class="d-flex flex-column">';
				html += '           <span class="text-muted font-weight-bold">' + (tagData.value ? tagData.value : '') + '</span>';
				html += '       </div>';
				html += '   </div>';
				html += '</div>';
				return html;
			} catch (err) { Log.log(err); }
		}
	
		// Initialize Tagify on the above input node reference
		let tagify = new Tagify(toEl, {
			delimiters: ", ", // add new tags when a comma or a space character is entered
			enforceWhitelist: true,
			dropdown: {
				position: "manual",
				maxItems: Infinity,
				enabled: 0,
				classname: "customSuggestionsList",
				searchKeys: ['value', 'type']  // Very important to set by which keys to search for suggestions when typing
			},
			/*templates: {
				dropdownItem: suggestionItemTemplate
			},*/
			transformTag: function (tagData) {
				tagData.class = 'tagify__tag tagify__tag--primary';
			},
			whitelist: tagWhitelist,
		});

		// Handle input event to filter out separators during search
		tagify.on('input', function (e) {
			let value = e.detail.value.toLowerCase();
			// If the input is empty, reset to the original whitelist including separators
			if (value === '') {
				tagify.settings.whitelist = tagWhitelist;
			} else {
				// Filter out separators and only show items that match the search value
				tagify.settings.whitelist = tagWhitelist.filter(item =>
					item.type !== 'separator' && item.value.toLowerCase().includes(value)
				);
			}
	
			tagify.dropdown.show.call(tagify);
		});
	
		tagify.addTags(existingTaggedPeople);
	
		tagify.on("focus", function (e) {
			// Reset whitelist to the original when focusing back on the input
			tagify.settings.whitelist = tagWhitelist;
			tagify.dropdown.show.call(tagify); // load the list
			tagify.DOM.scope.parentNode.appendChild(tagify.DOM.dropdown);
		});
	
		tagify.on("blur", function (e) {
			// This removes the dropdown from the DOM, ensuring no lingering issues
			tagify.dropdown.hide.call(tagify); // hide the list
			tagify.DOM.scope.parentNode.removeChild(tagify.DOM.dropdown);
		});
	
		template.tagifyModule = tagify;
	}).catch(err => {
		console.log(err);
	})
	
}

function handleFileUpload(metaContext, file, fileName) {

	const postButtonOriginalText = $("#momentFormSave").text();

	const actionLog = Session.get("activeMomentActionLog") || "";
	if (!actionLog.includes("mediaAttached")) Session.set("activeMomentActionLog", actionLog + "|mediaAttached");

	let mediaType = file.type;
	if (fileName) file.name = fileName;
	const promise = new Promise((resolve, reject) => {
		const fileReader = new FileReader();
		fileReader.onloadend = (f) => {
			const bytes = new Uint8Array(f.target.result);
			resolve(filetypeinfo(bytes));
		}
		fileReader.readAsArrayBuffer(file);
	});
	promise.then((typeInfo) => {
		const mimeType = typeInfo?.[0]?.mime;
		if (!mimeType || (!mimeType.match(/image\/*|video\/*/) && mimeType !== 'application/pdf')) {
			mpSwal.fire("Error", file.name + ": Only image, video, and PDF files are supported.", "error");
			return;
		}

		let uploadedFile = {
			name: file.name,
			mediaUrl: null,
			mediaToken: metaContext.tokenId,
			mediaFileType: mediaType.slice(0, mediaType.lastIndexOf("/")),
			mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId,
			mediaTypeRaw: mediaType
		};

		let fileData = {
			metaContext: metaContext,
			uploadedFile: uploadedFile,
			ready: false,
			error: false,
			queued: false,
			uploaded: false
		};

		let syncAccessor = { 'metaContext.tokenId': metaContext.tokenId };

		$("#momentFormSave").prop('disabled', true).text("Queuing...");
		fileData.status = "uploading";
		fileData.evaporateDirect = true;
		Syncs.insert(fileData);

		MomentUppy.addFile({
			name: uploadedFile.mediaPath,
			type: uploadedFile.mediaTypeRaw,
			data: file,
			source: 'Local',
			isRemote: false,
			meta: { syncAccessor: syncAccessor }
		});
		MomentUppy.upload();

		$("#momentFormSave").prop('disabled', false).text(postButtonOriginalText);

		let currentFileList = Session.get("momentFormFileList");
		if (!Array.isArray(currentFileList)) currentFileList = [];
		currentFileList.push(fileData);
		Session.set("momentFormFileList", currentFileList);
	});
}


// Helper function to get group name by ID
function getGroupName(groupId, groups) {
	let group = groups.find(g => g.id === groupId);
	return group ? group.value : 'Unknown Group';
}

function addCheckedInSameGroupSection(groupedWhitelist, people, userCheckedInGroupId, groups, org) {
	let checkedInSameGroup = people.filter(person => person.checkInGroupId === userCheckedInGroupId);
	if (checkedInSameGroup.length > 0) {
		groupedWhitelist.push({
			id: `separator-checked-in-same-group`,
			value: `Checked In: ${getGroupName(userCheckedInGroupId, groups)}`,
			type: 'separator',
			color: org.primaryColor
		});
		groupedWhitelist.push({
			id: userCheckedInGroupId,
			value: `Group: ${getGroupName(userCheckedInGroupId, groups)}`,
			type: 'group',
		})
		groupedWhitelist = groupedWhitelist.concat(checkedInSameGroup);
	}
	return groupedWhitelist;
}

function addCheckedOutSameGroupSection(groupedWhitelist, people, userCheckedInGroupId, groups, org) {
	let checkedOutSameGroup = people.filter(person => !person.checkInGroupId && person.defaultGroupId === userCheckedInGroupId);
	if (checkedOutSameGroup.length > 0) {
		groupedWhitelist.push({
			id: `separator-checked-out-same-group`,
			value: `Checked Out: ${getGroupName(userCheckedInGroupId, groups)}`,
			type: 'separator',
			color: org.primaryColor
		});
		groupedWhitelist = groupedWhitelist.concat(checkedOutSameGroup);
	}
	return groupedWhitelist;
}

function addCheckedInOtherGroupsSection(groupedWhitelist, people, userCheckedInGroupId, groups, org) {
	let checkedInOtherGroups = people.filter(person => person.checkInGroupId && person.checkInGroupId !== userCheckedInGroupId);
	if (checkedInOtherGroups.length > 0) {
		groupedWhitelist.push({
			id: `separator-checked-in-other-groups`,
			value: `Checked In: ${org.name}`,
			type: 'separator',
			color: org.primaryColor
		});

		// Add the groups the user is not checked into
		groups.forEach(group => {
			if (group.id !== userCheckedInGroupId) {
				groupedWhitelist.push({
					id: `${group.id}`,
					value: `Group: ${getGroupName(group.id, groups)}`,
					type: 'group',
				});
			}
		});

		groupedWhitelist = groupedWhitelist.concat(checkedInOtherGroups);
	}
	return groupedWhitelist;
}

function addCheckedOutOtherGroupsSection(groupedWhitelist, people, userCheckedInGroupId, org) {
	let checkedOutOtherGroups = people.filter(person => !person.checkInGroupId && (!person.defaultGroupId || person.defaultGroupId !== userCheckedInGroupId));
	if (checkedOutOtherGroups.length > 0) {
		groupedWhitelist.push({
			id: `separator-checked-out-other-groups`,
			value: `Checked Out: ${org.name}`,
			type: 'separator',
			color: org.primaryColor
		});
		groupedWhitelist = groupedWhitelist.concat(checkedOutOtherGroups);
	}
	return groupedWhitelist;
}

