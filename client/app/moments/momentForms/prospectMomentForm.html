<template name="prospectMomentForm">
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Contact Type</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div id="prospectType">
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentProspectType 'Call'}}" data-prospecttype="Call">Call</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentProspectType 'Walk-in'}}" data-prospecttype="Walk-in">Walk-in</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentProspectType 'Email'}}" data-prospecttype="Email">Email</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentProspectType 'Letter'}}" data-prospecttype="Letter">Letter</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentProspectType 'Text'}}" data-prospecttype="Text">Text</button>
      </div>
    </div>
  </div>
  {{> momentFormTimePicker}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea class="form-control" rows="3" placeholder="Additional info" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
