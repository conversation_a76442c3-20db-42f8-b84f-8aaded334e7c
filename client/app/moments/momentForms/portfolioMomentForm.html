<template name="portfolioMomentForm">
		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Activity</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
        {{#if portfolioCurriculum}}
					<span style="font-size:125%">{{portfolioCurriculum.headline}}</span>
				{{else}}
				<select data-cy="select-activity-portfolio" class="form-control" id="learningCurriculum">
					<option value=""></option>
					{{#each availableCurriculums}}
					<option value="{{_id}}" {{selectedIfEqual currentMomentPortfolioCurriculum _id}}>{{headline}}</option>
					{{/each}}
				</select>
				{{/if}}
			</div>
		</div>
		{{#if showAgeRange}}
		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Age Range</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<select data-cy="select-age-range" class="form-control" id="observationAgeRange">
					<option value="">Choose to enable additional standards...</option>
					{{#each ageRange in ageRanges}}
					<option value="{{_id}}" {{selectedIfEqual currentMomentObservationAgeRange ageRange.label}}>{{ageRange.label}}</option>
					{{/each}}
				</select>
			</div>
		</div>
		{{else}}
		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<button data-cy="show-all-standards-btn" id="btnShowAgeRange" class="btn btn-primary">Show All Standards for Age Range</button>
			</div>
		</div>
		{{/if}}
		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Observations</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				{{#each assessment in currentAssessments}}
					<div class="row my-4">
						<div class="col-sm-8" style="font-size:125%">
							{{assessment.label}}
						</div>
						<div class="col-sm-3" style="font-size:125%">
							{{assessment.valueLabel}}
						</div>
						<div class="col-sm-1">
							<a href="#" class="remove-assessment" data-pos="{{assessment.pos}}"><i class="fad fa-times"  style="font-size:24px;color:var(--primary)"></i></a>
						</div>
					</div>
				{{/each}}
				<select data-cy="select-standards" class="form-control" id="selectNewAssessmentActivity" >
					<option value="">Select {{ getEntityTypeStandard }} to Add</option>
					{{#each standard in activityStandards}}
						<option value="{{standard.standardId}}"> {{standard.category}} {{standard.benchmark}}</option>
					{{/each}}
				</select>
				<div id="portfolioAssessmentLevels" style="margin-top:4px;margin-bottom:12px;padding:0">
  				{{#each availableAssessmentLevels}}
  				    <button data-cy="assessment-btn" type="button" class="btn btn-primary {{activeIfEq currentMomentPortfolioAssessmentLevel value}}" data-learningassessment="{{value}}">{{label}}</button>
  				{{/each}}
				</div>
				<button data-cy="add-btn" type="button" class="btn btn-primary" id="btnAddAssessment" style="margin-bottom:6px">Add</button>			
			</div>
		</div>
		{{>momentFormTimePicker}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <textarea data-cy="moment-comment" class="form-control" rows="3" placeholder="Additional Info" id="comment">{{comment}}</textarea>
      </div>
    </div>
</template>
