<template name="activityMomentForm">
  {{#unless hasCustomization "moments/activity/childDefaults"}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Engagement</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <div id="activityEngagement">
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentActivityEngagement 'Active'}}" data-activityengagement="Active">Active</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentActivityEngagement 'Passive'}}" data-activityengagement="Passive">Passive</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentActivityEngagement 'Refused'}}" data-activityengagement="Refused">Refused</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentActivityEngagement 'Out of Center'}}" data-activityengagement="Out of Center">Out of Center</button>
        </div>
      </div>
    </div>
  {{/unless}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Activity Type</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <select data-cy="select-activity-moment" class="form-control" id="activityType">
        {{#each availableActivityTypes}}
          <option value="{{name}}" {{selectedIfEqual currentMomentActivityType name}}>{{name}}</option>
        {{/each}}
      </select>
    </div>
  </div>
  {{> momentFormTimePicker}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea data-cy="moment-comment" class="form-control" rows="3" placeholder="What's happening?" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
