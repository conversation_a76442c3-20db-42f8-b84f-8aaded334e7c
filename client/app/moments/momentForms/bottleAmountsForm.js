import { Template } from 'meteor/templating';
import _ from '../../../../lib/util/underscore';
import './bottleAmountsForm.html';

Template.bottleAmountsForm.helpers({
	'bottleAmounts': function(amountType) {
		if (amountType=="fractional")
			return [
				{amountLabel:"0", amountValue: 0}, {amountLabel:"1/4", amountValue:0.25}, 
				{amountLabel:"1/2", amountValue:0.5}, {amountLabel:"3/4", amountValue:0.75}
			];
		else
			return _.map(_.range(11), function(v) { return {amountLabel: v.toString(), amountValue: v};});
	},
	'isAmountSelected': function(amountType, amountCategory, amountValue) {
		let currentMoment = Template.instance().data.currentMoment;
		let bottleAmount = currentMoment ? currentMoment["foodBottleAmount" + amountCategory] || 0 : 0; 

		if (amountType=="fractional") {
			let fractionalAmount = bottleAmount % 1; 
			return fractionalAmount == amountValue ? "selected" : "";
		} else {
			return Math.floor(bottleAmount) == amountValue ? "selected" : "";
		}
	}
});
