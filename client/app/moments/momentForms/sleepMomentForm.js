import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import './sleepMomentForm.html';

Template.sleepMomentForm.rendered = function() {
	$("#momentDatePicker").datepicker({ autoclose: true, todayHighlight: true })
}

Template.sleepMomentForm.events({
	'click #sleepEndSleep': function(event) {
		Session.set("activeMomentSleepShowEndTimeBox", true);
	}
});

Template.sleepMomentForm.helpers({
	"timeFormatted": function() {
		let curTime = new moment(this.time,"h:mm a");
		return curTime.format("HH:mm");
	},
	"dateFormatted": function() {
		let curDate = this.date ? new moment(this.date).format('MM/DD/YYYY') : new moment(this.createdAt).format('MM/DD/YYYY');
		return curDate;
	},
	"endTimeFormatted": function() {
		let curTime = new moment(this.endTime,"h:mm a");
		return curTime.format("HH:mm");
	},
	"displayEndTimeBox": function() {
		if ( Orgs.current().hasCustomization("moments/sleep/showEndSleepButton") && 
				   !Session.equals("activeMomentSleepShowEndTimeBox", true) ) 
				return "display:none";
	},
	"displayEndTimeButton": function() {
		if ( Orgs.current().hasCustomization("moments/sleep/showEndSleepButton") && 
				   Session.equals("activeMomentSleepShowEndTimeBox", null) 
				)
			return "";
		else
			return "display:none";
	}
});
