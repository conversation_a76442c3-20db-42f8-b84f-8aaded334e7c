import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import './ouchMomentForm.html';
import './momentFormTimePicker';

Template.ouchMomentForm.events({
	"click #ouchCalledParent": function() {
		let cb = $("#ouchCalledParent");
		if (cb.is(':checked')) $("#calledParentTimeArea").show(); else $("#calledParentTimeArea").hide();
	},
	"click #ouchCalledDoctor": function() {
		let cb = $("#ouchCalledDoctor");
		if (cb.is(':checked')) $("#calledDoctorTimeArea").show(); else $("#calledDoctorTimeArea").hide();
	},
	'click #ouchContactedParent button': function(event) {
		let name = $(event.target).data('parentcontact');
		if (name == "Phone" || name=="Email" || name=="In Person")  $("#calledParentTimeArea").show(); else $("#calledParentTimeArea").hide();
		Session.set("activeMomentOuchContactedParent", name);
	},
	'click #ouchContactedDoctor button': function(event) {
		let name = $(event.target).data('doctorcontact');
		if (name == "Phone" || name=="Email" || name=="In Person")  $("#calledDoctorTimeArea").show(); else $("#calledDoctorTimeArea").hide();
		Session.set("activeMomentOuchContactedDoctor", name);
	}
});

Template.ouchMomentForm.helpers({
	"calledParentTimeFormatted": function() {
		let curTime = new moment(this.calledParentTime, "h:mm a");
		return curTime.format("HH:mm");
	},
	"calledDoctorTimeFormatted": function() {
		let curTime = new moment(this.calledDoctorTime, "h:mm a");
		return curTime.format("HH:mm");
	},
	'currentMomentOuchContactedParent': function() {
		return Session.get("activeMomentOuchContactedParent");
	},
	'currentMomentOuchContactedDoctor': function() {
		return Session.get("activeMomentOuchContactedDoctor");
	}
});
