<template name="sleepMomentForm">
  <div class="form-group row" autocomplete="off">
    <label class="col-xl-3 col-lg-3 text-right col-form-label" for="momentDatePicker">Date</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <input class="form-control" type="text" data-DateTimePicker name="momentDatePicker" id="momentDatePicker" value="{{dateFormatted}}"/>
    </div>
  </div>
  <div class="form-group row" autocomplete="off">
    <label class="col-xl-3 col-lg-3 text-right col-form-label" for="dateTimePicker">Start Time</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <input data-cy="sleep-start-time" class="form-control" type="time" data-DateTimePicker name="timePicker" id="dateTimePicker" value="{{timeFormatted}}"/>
    </div>
  </div>
  <div class="form-group row" autocomplete="off">
    <label class="col-xl-3 col-lg-3 text-right col-form-label" for="dateTimePickerEnd">End Time</label>
    <div id="dateTimePickerEndContainer" class="col-lg-6 col-md-9 col-sm-12" style="{{displayEndTimeBox}}">
      <input data-cy="sleep-end-time" class="form-control" type="time" data-DateTimePicker name="timePicker" id="dateTimePickerEnd" value="{{endTimeFormatted}}"/>
    </div>
    <div style="{{displayEndTimeButton}}">
      <button data-cy="sleep-end-time-btn" type="button" class="btn btn-primary" id="sleepEndSleep">End Sleep</button>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Did Not Sleep</label>
    <div class="col-9 col-form-label">
      <div class="checkbox-list">
        <label class="checkbox checkbox-primary">
          <input data-cy="did-not-sleep-btn" type="checkbox" name="didNotSleep" id="sleepDidNotSleep" {{checkedIfEq sleepDidNotSleep true}}>
          <span></span>
        </label>
      </div>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea data-cy="moment-comment" class="form-control" rows="3" placeholder="What's happening?" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
