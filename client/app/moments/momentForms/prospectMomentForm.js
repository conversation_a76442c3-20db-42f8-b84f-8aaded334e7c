import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import './prospectMomentForm.html';
import './momentFormTimePicker';

Template.prospectMomentForm.events({
	'click #prospectType button': function(event) {
		let name = $(event.target).data('prospecttype');
		Session.set("activeMomentProspectType", name);
	}
});

Template.prospectMomentForm.helpers({
	'currentMomentProspectType': function() {
		return Session.get("activeMomentProspectType");
	}
});
