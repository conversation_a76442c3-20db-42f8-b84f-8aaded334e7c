import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './portfolioMomentForm.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../../lib/util/orgLanguageTranformationUtil';
import moment from "moment-timezone";
import {getPeopleData} from "../../../services/peopleMeteorService"
import './momentFormTimePicker';
import { Curriculum, Curriculums } from '../../../../lib/collections/curriculum';
import { Groups } from '../../../../lib/collections/groups';

Template.portfolioMomentForm.helpers({
	'availableCurriculumTypes': function() {
		if (Orgs.current()) {
			let types =  Orgs.current().availableCurriculumTypes({mapped: true});
			return _.map(types, function(v) {return {value: v, name: v.replace('`', ' > ')};});
		}
	},
	'availableCurriculums': async function() {
		const tags = Session.get("taggedPeople");
		const org = Orgs.current();
		const startRange =  new moment.tz(org.getTimezone()).startOf("day").valueOf();
		const endRange = new moment.tz(org.getTimezone()).add(1,"day").startOf("day").valueOf();
		const query = {$and: [
					  {scheduledDate: {$gte: startRange, $lt: endRange}}
					]};
		const selectedGroupsQuery = {$or: [ {selectedGroups: [] } ] };
		
		const groupIds = new Set();
		const taggedPeopleList = Session.get("taggedPeopleList");
		if (tags && tags.length) {
			let i = 0;
			for (const tag of tags) {
				switch (tag.split('|')[0]) {
					case "person":
						const person = taggedPeopleList[i];
						if (!person) {
							break;
						}
						if (person.defaultGroupId) {
							groupIds.add(person.defaultGroupId)
						}
						if (person.checkInGroupId) {
							groupIds.add(person.checkInGroupId)
						}
						break;
					case "group":
						groupIds.add(tag.split('|')[1]);
						break;
					default:
						break;
				}
				++i;
			}
		}


		if (groupIds.size) {
			groupIds.forEach(id => selectedGroupsQuery["$or"].push({selectedGroups: {$elemMatch: {$eq: id}}}));
		}

		if (FlowRouter.current().path.includes("groups") && Session.get("activeMomentParentSource") == "group") {
			selectedGroupsQuery["$or"].push({selectedGroups: {$elemMatch: {$eq: Session.get("activeMomentParentSourceId")}}});
		}

		query["$and"].push(selectedGroupsQuery);
    		
		let out;
		await Meteor.callAsync('getCurriculumData', query).then((res) => {
			out = res.map((d) => {
				return new Curriculum(d); 
			})
		});
		
		return out;
	},
	'activityStandards': async function() {
		const curriculumId = Session.get("activeMomentPortfolioCurriculum");
    	const specificGroupId = Session.get("portfolioStandardGroupFilter");
		const ageGroup = Template.instance().ageRange.get();
		
		if (ageGroup) {
			return getAgeGroupStandards(ageGroup);
		}
		else if (curriculumId) {
			let curriculum;
			await Meteor.callAsync('getCurriculumById', curriculumId).then((res) => {
				curriculum = new Curriculum(res);
			});
			if (curriculum) return curriculum.findStandardsRaw();
		} else if (specificGroupId) {
			const currentGroup = Groups.findOne(specificGroupId);
			const availablePortfolioStandards = [];
			Curriculums.getStandards().map( (standard) => {
				if (currentGroup && currentGroup.activitiesAgeGroup == standard.ageGroup) {
					availablePortfolioStandards.push({
						standardId: standard && standard.standardId,
						category: standard.category,
						benchmark: standard.benchmark,
					})
				}
			})
			return availablePortfolioStandards;
		}
	},
	'availableAssessmentLevels': function() {
		return Orgs.current() && Orgs.current().getAvailableAssessmentLevels();
	},
	'currentMomentPortfolioCurriculum': function() {
		return Session.get("activeMomentPortfolioCurriculum");
	},
	'currentMomentPortfolioType': function() {
		return Session.get("activeMomentPortfolioType");
	},
	"currentMomentPortfolioAssessmentLevel": function() {
		return Session.get("activeMomentPortfolioAssessmentLevel")
	},
	"currentAssessments": function() {
		const assessments = Session.get("activeMomentPortfolioAssessments"),
			allStandards = Curriculums.getStandards();
		let i=0, output = [];
		_.each( assessments, (assessment) => {
			const matchedStandard = _.find(allStandards, (s) => s.standardId == assessment.standardId),
				matchedAssessmentLevel = _.find(Orgs.current() && Orgs.current().getAvailableAssessmentLevels(), (l) => l.value == assessment.value) ;
			
			if (matchedStandard && matchedAssessmentLevel) output.push({
				label: (matchedStandard.category ? matchedStandard.category : matchedStandard.source) + ": " + matchedStandard.benchmark,
				value: assessment.value,
				valueLabel: matchedAssessmentLevel.label,
				pos: i
			});
			i = i + 1;
		} );
		return output;
	},
	"ageRanges": function() {
		return Curriculums.activitiesAgeGroups();
	},
	showAgeRange: function() {
		return Template.instance().showAgeRange.get();
	},
	currentMomentObservationAgeRange: function() {
		return Template.instance().ageRange.get();
	},
	'getEntityTypeStandard': function () {	
		return orgLanguageTranformationUtil.getEntityType('standard');
	},
});


Template.portfolioMomentForm.events({
	"change #learningCurriculum": function(e, t) {
		let curriculumId = $(e.target).val();
		console.log("curriculumId set", curriculumId)
		Session.set("activeMomentPortfolioCurriculum", curriculumId);
	},
	"change #learningType": function(e, t) {
		let name = $(event.target).val();
		Session.set("activeMomentPortfolioType", name);
		
		let startRange = new Date().setHours(0,0,0,0);
		let endRange = moment(startRange).add(1,'day').valueOf();

		let selectedGroupsQuery = [{selectedGroups: []}];
		if (Template.parentData(3) && Template.parentData(3).checkInGroupId ) 
			selectedGroupsQuery.push({selectedGroups: Template.parentData(3).checkInGroupId});

		let query = {$and: [{$or: selectedGroupsQuery},
					  {scheduledDate: {$gte: startRange, $lt: endRange}},
					  {$or:[ {selectedType: Session.get("activeMomentPortfolioType")}, {type: Session.get("activeMomentPortfolioType")} ] } 
					]};

		let q = Curriculums.find(query).map(function(d) { return d.message;}).join("\n");
		//$("#comment").val( q);
	},
	'click #portfolioAssessmentLevels button': function(event) {
		let name = $(event.target).data('learningassessment');
		Session.set("activeMomentPortfolioAssessmentLevel", name);
		console.log("sent to:", name);
	},
	"click #btnAddAssessment": function(event) {
		const assessments = _.clone(Session.get("activeMomentPortfolioAssessments") || []),
			standardId = $("#selectNewAssessmentActivity").val(),
			assessmentValue = Session.get("activeMomentPortfolioAssessmentLevel");
			console.log("adding", standardId, assessmentValue);

		assessments.push({
			standardId, 
			value: assessmentValue
		})
		Session.set("activeMomentPortfolioAssessments", assessments);
		$("#selectNewAssessmentActivity").val(null);
		Session.set("activeMomentPortfolioAssessmentLevel", null);
	},
	"click .remove-assessment": function(event) {
		const assessmentPos = $(event.currentTarget).data("pos");
		let assessments = _.clone(Session.get("activeMomentPortfolioAssessments") );
		console.log($(event.currentTarget), "pos:", assessmentPos);
		assessments.splice(parseInt(assessmentPos),1);
		
		Session.set("activeMomentPortfolioAssessments", assessments);
	},
	"change #observationAgeRange": function(event, instance) {
		const ageRange = $(event.target).val();
		instance.ageRange.set(ageRange);
	},
	"click #btnShowAgeRange": function(event, instance) {
		event.preventDefault();

    	const specificGroupId = Session.get("portfolioStandardGroupFilter");
		console.log("specific groupID", specificGroupId)
		if (specificGroupId) {
			const currentGroup = Groups.findOne(specificGroupId);
			if (currentGroup && currentGroup.activitiesAgeGroup ) {
				instance.ageRange.set(currentGroup.activitiesAgeGroup);		
			}	
		}

		instance.showAgeRange.set(true);
	}
});

Template.portfolioMomentForm.onCreated( function() {
	const self = this;
	self.ageRange = new ReactiveVar();
	self.showAgeRange = new ReactiveVar();
	this.autorun(() => {
		const tags = Session.get("taggedPeople");
		if (tags && tags.length) {
			let query = {_id: {$in: []}};
			for (const tag of tags) {
				query._id['$in'].push(tag.split('|')[1]);
			}

			getPeopleData(query).then((res) => {
				if (res) {
					Session.set("taggedPeopleList", res);
				}
			}).catch(err => {
				console.log(err);
			});

		} else {
			Session.set("taggedPeopleList", []);
		}
	});
});

Template.portfolioMomentForm.onRendered( function () {
	$('#selectNewAssessmentActivity').selectpicker({
		container: '#momentForm',
		liveSearch: true
	});
	const self = this;
	Tracker.autorun( function() {
		const ageRange = self.ageRange.get();
		const curriculumId = Session.get("activeMomentPortfolioCurriculum");
		console.log("resetting with", ageRange, curriculumId)
		Tracker.afterFlush( function() { 
			setTimeout(function() {
				$('#selectNewAssessmentActivity').selectpicker('refresh'); 
			}, 1500);
		});
	});
});

function getAgeGroupStandards(ageGroup) {
	return _.chain(Curriculums.getStandards())
		.filter( standard => standard.ageGroup == ageGroup)
		.sortBy( standard => standard.category)
		.value()
}

Template.portfolioMomentForm.onDestroyed(function() {
	delete Session.keys["taggedPeopleList"];
});