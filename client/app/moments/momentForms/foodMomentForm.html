<template name="foodMomentForm">
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Type</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div id="foodType">
        <button data-cy="food-moment-breakfast" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Breakfast'}} mb-2" data-foodtype="Breakfast">Breakfast</button>
        <button data-cy="food-moment-am-snack" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'AM Snack'}} mb-2" data-foodtype="AM Snack">AM Snack</button>
        <button data-cy="food-moment-lunch" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Lunch'}} mb-2" data-foodtype="Lunch">Lunch</button>
        <button data-cy="food-moment-pm-snack" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'PM Snack'}} mb-2" data-foodtype="PM Snack">PM Snack</button>
        {{#if hasCustomization "moments/food/showTube"}}
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Tube'}} mb-2" data-foodtype="Tube">Tube</button>
        {{/if}}
          <button data-cy="food-moment-late-snack" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Late Snack'}} mb-2" data-foodtype="Late Snack">Late Snack</button>
          <button data-cy="food-moment-dinner" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Dinner'}} mb-2" data-foodtype="Dinner">Dinner</button>
        {{#if showBottle}}
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Bottle'}} mb-2" data-foodtype="Bottle">Bottle</button>
        {{/if}}
        {{#if showBabyFood}}
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Baby Food'}} mb-2" data-foodtype="Baby Food">Baby Food</button>
        {{/if}}
        {{#if showCereal}}
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentFoodType 'Cereal'}} mb-2" data-foodtype="Cereal">Cereal</button>
        {{/if}}
      </div>
    </div>
  </div>
  {{#if availableFoodItems}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Food Item</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input type="hidden" name="food-id" value="">
        {{#each item in availableFoodItems}}
          <div class="row my-4">
            <label class="col-sm-3">{{item.name}}</label>
            <div class="btn-group col-sm-9 food-item-container">
              <button data-cy="food-moment-all-items" type="button" class="btn btn-primary {{activeIfItemAmountEq item.name 'All'}}" data-id="{{@index}}" data-name="{{escapeString item.name}}" data-food-item-amount="All">All</button>
              <button data-cy="food-moment-Most-items" type="button" class="btn btn-primary {{activeIfItemAmountEq item.name 'Most'}}" data-id="{{@index}}" data-name="{{escapeString item.name}}" data-food-item-amount="Most">Most</button>
              <button data-cy="food-moment-Some-items" type="button" class="btn btn-primary {{activeIfItemAmountEq item.name 'Some'}}" data-id="{{@index}}" data-name="{{escapeString item.name}}" data-food-item-amount="Some">Some</button>
              <button data-cy="food-moment-None-items" type="button" class="btn btn-primary {{activeIfItemAmountEq item.name 'None'}}" data-id="{{@index}}" data-name="{{escapeString item.name}}" data-food-item-amount="None">None</button>
            </div>
          </div>
        {{/each}}
      </div>
    </div>
  {{/if}}
  {{#if showTubeAmount}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Amount</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <div class="col-sm-10">
          <select class="form-control" name="foodTubeAmount" id="foodTubeAmount">
            <option value="0" {{selectedIfEqual currentMomentFoodFoodTubeAmount '0'}}>0</option>
            <option value="60" {{selectedIfEqual currentMomentFoodFoodTubeAmount '60'}}>60</option>
            <option value="120" {{selectedIfEqual currentMomentFoodFoodTubeAmount '120'}}>120</option>
            <option value="180" {{selectedIfEqual currentMomentFoodFoodTubeAmount '180'}}>180</option>
            <option value="240" {{selectedIfEqual currentMomentFoodFoodTubeAmount '240'}}>240</option>
            <option value="300" {{selectedIfEqual currentMomentFoodFoodTubeAmount '300'}}>300</option>
            <option value="360" {{selectedIfEqual currentMomentFoodFoodTubeAmount '360'}}>360</option>
            <option value="420" {{selectedIfEqual currentMomentFoodFoodTubeAmount '420'}}>420</option>
            <option value="480" {{selectedIfEqual currentMomentFoodFoodTubeAmount '480'}}>480</option>
            <option value="540" {{selectedIfEqual currentMomentFoodFoodTubeAmount '540'}}>540</option>
          </select>
        </div>
        <div class="col-sm-2">
          ml
        </div>
      </div>
    </div>
  {{/if}}
  {{#if showBottleAmount}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Breastmilk Offered" label="BreastmilkOffered" units="oz."}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Breastmilk Consumed" label="BreastmilkConsumed" units="oz."}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Formula Offered" label="FormulaOffered" units="oz."}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Formula Consumed" label="FormulaConsumed" units="oz."}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Milk Offered" label="MilkOffered" units="oz."}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Milk Consumed" label="MilkConsumed" units="oz."}}
  {{else if showBabyFoodAmount}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Food Type</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <div id="foodBabyFoodType">
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentBabyFoodType 'Fruit'}}" data-babyfoodtype="Fruit">Fruit</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentBabyFoodType 'Vegetable'}}" data-babyfoodtype="Vegetable">Vegetable</button>
        </div>
      </div>
    </div>
    {{> bottleAmountsForm currentMoment=this displayLabel="Amount Eaten" label="BabyFoodConsumed" units=(foodUnits 'babyFood')}}
  {{else if showCerealAmount}}
    {{> bottleAmountsForm currentMoment=this displayLabel="Amount Eaten" label="CerealConsumed" units=(foodUnits 'cereal') hideFractionalAmount=true}}
  {{else}}
    {{#if hasCustomization "moments/food/showPercentage"}}
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Amount</label>
        <div class="col-lg-6 col-md-9 col-sm-12">
          <select class="form-control" id="foodAmountPercent">
            <option value="100%" {{selectedIfEqual currentMomentFoodAmountPercent '100%'}}>100%</option>
            <option value="90%" {{selectedIfEqual currentMomentFoodAmountPercent '90%'}}>90%</option>
            <option value="80%" {{selectedIfEqual currentMomentFoodAmountPercent '80%'}}>80%</option>
            <option value="70%" {{selectedIfEqual currentMomentFoodAmountPercent '70%'}}>70%</option>
            <option value="60%" {{selectedIfEqual currentMomentFoodAmountPercent '60%'}}>60%</option>
            <option value="50%" {{selectedIfEqual currentMomentFoodAmountPercent '50%'}}>50%</option>
            <option value="40%" {{selectedIfEqual currentMomentFoodAmountPercent '40%'}}>40%</option>
            <option value="30%" {{selectedIfEqual currentMomentFoodAmountPercent '30%'}}>30%</option>
            <option value="20%" {{selectedIfEqual currentMomentFoodAmountPercent '20%'}}>20%</option>
            <option value="10%" {{selectedIfEqual currentMomentFoodAmountPercent '10%'}}>10%</option>
            <option value="0%" {{selectedIfEqual currentMomentFoodAmountPercent '0%'}}>0%</option>
          </select>
        </div>
      </div>
    {{else}}
      {{#unless availableFoodItems}}
        <div class="form-group row">
          <label class="col-xl-3 col-lg-3 text-right col-form-label">Amount</label>
          <div class="col-lg-6 col-md-9 col-sm-12">
            <div id="foodAmount">
              <button data-cy="food-moment-all" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodAmount 'All'}}" data-foodamount="All">{{ getMomentTypesFoodAll }}</button>
              <button data-cy="food-moment-most" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodAmount 'Most'}}" data-foodamount="Most">{{ getMomentTypesFoodMost }}</button>
              <button data-cy="food-moment-some" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodAmount 'Some'}}" data-foodamount="Some">{{ getMomentTypesFoodSome }}</button>
              <button data-cy="food-moment-none" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodAmount 'None'}}" data-foodamount="None">{{ getMomentTypesFoodNone }}</button>
              <button data-cy="food-moment-not-offered" type="button" class="btn btn-primary {{activeIfEq currentMomentFoodAmount 'Not Offered'}}" data-foodamount="Not Offered">{{ getMomentTypesFoodNotOffered }}</button>
            </div>
          </div>
        </div>
      {{/unless}}
    {{/if}}
  {{/if}}
  {{>momentFormTimePicker}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea data-cy="moment-comment" class="form-control" rows="3" placeholder="What's happening?" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
