import { Template } from 'meteor/templating';
import _ from '../../../../lib/util/underscore';
import './momentFormTimePicker.html';

Template.momentFormTimePicker.rendered = function() {
	$("#momentDatePicker").datepicker({ autoclose: true, todayHighlight: true });
}

Template.momentFormTimePicker.helpers({
	"timeFormatted": function() {
		return new moment(this.time, "h:mm a").format("HH:mm");
	},
	"dateFormatted": function() {		
		let curDate = this.date ? new moment(this.date).format('MM/DD/YYYY') : new moment(this.createdAt).format('MM/DD/YYYY');
		return curDate;
	}
});
