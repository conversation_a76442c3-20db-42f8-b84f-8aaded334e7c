<template name="dynamicMomentForm">
  {{#each momentField in momentDefinition.momentFields}}
    <div class="form-group row align-items-center">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">{{momentField.label}} {{#if momentField.required}}<span class="text-danger font-weight-bolder">*required</span>{{/if}}</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        {{#if trueIfEq momentField.fieldType "text" }}
          <textarea class="form-control" rows="3" placeholder="{{momentField.label}}" id="momentField{{momentField.dataId}}">{{momentDataFor momentField.dataId}}</textarea>
        {{else if trueIfEq momentField.fieldType "string"}}
          <input type="text" class="form-control" id="momentField{{momentField.dataId}}" value="{{momentDataFor momentField.dataId}}" placeholder="{{momentField.label}}">
        {{else if trueIfEq momentField.fieldType "select"}}
          <select class="form-control" id="momentField{{momentField.dataId}}" multiple={{momentField.multi}}>
            {{#if momentField.allowBlankOption}}
              <option value=""></option>
            {{/if}}
            {{#each momentFieldValue in momentField.fieldValues}}
              <option value="{{getFieldValuePart momentFieldValue 'value'}}" {{selectedIfEqual (momentDataFor momentField.dataId) (getFieldValuePart momentFieldValue 'value')}}>{{getFieldValuePart momentFieldValue 'label'}}</option>
            {{/each}}
          </select>
        {{else if trueIfEq momentField.fieldType "buttons"}}
          <div class="btn-group" id="momentField{{momentField.dataId}}">
            {{#each momentFieldValue in momentField.fieldValues}}
              <button type="button" class="btn btn-primary {{activeIfEq (momentDataFor momentField.dataId) momentFieldValue.fieldValue}}" data-value="{{momentFieldValue.fieldValue}}">
                {{#if momentFieldValue.fieldValueIcon}}<i class="fa {{momentFieldValue.fieldValueIcon}} icon-2x"></i>{{else}}{{momentFieldValue.fieldValue}}{{/if}}
              </button>
            {{/each}}
          </div>
        {{else if trueIfEq momentField.fieldType "peopleSelect"}}
          <select name="administeredBy" class="form-control" id="administeredBy">
            {{#if momentField.allowBlankOption}}
              <option value=""></option>
            {{/if}}
            {{#each staffPerson in staffPeople}}
              <option value="{{staffPerson._id}}" {{selectedIfEqual (momentDataFor momentField.dataId) staffPerson._id}}>{{staffPerson.name}}</option>
            {{/each}}
          </select>
        {{else if trueIfEq momentField.fieldType "checkbox"}}
          <div class="col-9 col-form-label">
            <div class="checkbox-list">
              <label class="checkbox checkbox-primary">
                <input type="checkbox" name="momentField{{momentField.dataId}}" id="momentField{{momentField.dataId}}" {{checkedIfEq (momentDataFor momentField.dataId) true}}> 
                <span></span>
              </label>
            </div>
          </div>
        {{else if trueIfEq momentField.fieldType "timePicker"}}
          <input type="time" name="momentField{{momentField.dataId}}" id="momentField{{momentField.dataId}}" value="{{formatTimeField (momentDataFor momentField.dataId)}}">
        {{else if trueIfEq momentField.fieldType "customerDefinedList"}}
          <select class="form-control" id="momentField{{momentField.dataId}}">
            {{#each momentFieldValue in (customerDefinedListValues momentField)}}
              <option value="{{momentFieldValue.value}}">{{momentFieldValue.value}}</option>
            {{/each}}
          </select>
        {{/if}}
      </div>
    </div>
  {{/each}}
	{{#if momentDefinition.internalOnly}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Admin-only visible?</label>
      <div class="col-9 col-form-label">
        <div class="checkbox-list">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" name="momentFieldadminOnly" id="momentFieldadminOnly" {{checkedIfEq adminOnly true}}> 
            <span></span>
          </label>
        </div>
      </div>
    </div>
	{{/if}}
	{{#unless momentDefinition.hideTimePicker}}
	 {{> momentFormTimePicker}}
	{{/unless}}
	{{#unless momentDefinition.hideComment}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <textarea class="form-control" rows="3" placeholder="What's happening" id="comment">{{comment}}</textarea>
      </div>
    </div>
  {{/unless}}
</template>
