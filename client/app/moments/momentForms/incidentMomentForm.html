<template name="incidentMomentForm">
    {{> momentFormTimePicker}}
    {{#if hasCustomization "moments/incident/extraIncidentFields"}}
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Nature of Incident/What Happened</label>
        <div class="col-lg-6 col-md-9 col-sm-12">
          <input data-cy="incident-nature-input" type="text" class="form-control" id="incidentNature" placeholder="What happened?" value="{{incidentNature}}">
        </div>
      </div>
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Action Taken to Correct Behavior</label>
        <div class="col-lg-6 col-md-9 col-sm-12">
          <input data-cy="action-taken-input" type="text" class="form-control" id="incidentActionTaken" placeholder="Action taken" value="{{incidentActionTaken}}">
        </div>
      </div>
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Incident Location</label>
        <div class="col-lg-6 col-md-9 col-sm-12">
          <input data-cy="incident-location-input" type="text" class="form-control" id="incidentLocation" placeholder="Incident location" value="{{incidentLocation}}">
        </div>
      </div>
    {{/if}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <textarea data-cy="moment-comment" class="form-control" rows="3" placeholder="Additional info" id="comment">{{comment}}</textarea>
      </div>
    </div>
</template>
