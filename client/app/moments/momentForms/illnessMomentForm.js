import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import _ from '../../../../lib/util/underscore';
import './illnessMomentForm.html';

Template.illnessMomentForm.helpers({
	'availableSymptoms': function() {
		return ['Fever (101 degrees or higher)',
				'Eyes (green/yellow discharge/inflamation of the eye)',
				'Diarrhea (runny, watery, bloody stools or2 or more loose stools within 24 hours)',
				'Vomitting (2 or more times within 24 hours)',
				'Ears (pain/ear drainage, or child rubs/pulls ears)',
				'Unusual spots/rashes/blisters (present and/or spreadeing over a period of time)',
				'Head Lice (children with lice will be referred for treatment)',
				'Other (describe in Comment box)'];
	},
	'currentMomentSymptoms': function() {
    console.log(Session.get("activeMomentSymptoms"));
		return Session.get("activeMomentSymptoms");
	}
});
