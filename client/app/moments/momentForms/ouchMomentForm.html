<template name="ouchMomentForm">
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Describe injury/accident in detail (include location, size, etc.)</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea class="form-control" rows="3" placeholder="Description" id="ouchDescription">{{ouchDescription}}</textarea>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">How was injury cared for?</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <input type="text" class="form-control" placheholder="description" id="ouchCare" value="{{ouchCare}}">
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Contacted Parent</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div id="ouchContactedParent">
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedParent 'Phone'}}" data-parentcontact="Phone">Phone</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedParent 'Email'}}" data-parentcontact="Email">Email</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedParent 'In Person'}}" data-parentcontact="In Person">In Person</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedParent 'Did Not Notify'}}" data-parentcontact="Did Not Notify">Did Not Notify</button>
      </div>
    </div>
  </div>
  <div class="form-group row" autocomplete="off" id="calledParentTimeArea" style="{{#unless trueIfEq calledParent true}}display:none{{/unless}}">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Time</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <input class="form-control" type="time" data-DateTimePicker name="timePicker" id="calledParentTime" placeholder="End time" value="{{calledParentTimeFormatted}}"/>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Contacted Doctor</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div id="ouchContactedDoctor">
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedDoctor 'Phone'}}" data-doctorcontact="Phone">Phone</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedDoctor 'Email'}}" data-doctorcontact="Email">Email</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedDoctor 'In Person'}}" data-doctorcontact="In Person">In Person</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentOuchContactedDoctor 'Did Not Notify'}}" data-doctorcontact="Did Not Notify">Did Not Notify</button>
      </div>
    </div>
  </div>
  <div class="form-group row" autocomplete="off" id="calledDoctorTimeArea" style="{{#unless trueIfEq calledDoctor true}}display:none{{/unless}}">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Time</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <input class="form-control" type="time" data-DateTimePicker name="timePicker" id="calledDoctorTime" placeholder="End time" value="{{calledDoctorTimeFormatted}}"/>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">School Nurse Notified</label>
    <div class="col-9 col-form-label">
      <div class="checkbox-list">
        <label class="checkbox checkbox-primary">
          <input type="checkbox" name="nurseNotified" id="ouchNurseNotified" {{checkedIfEq ouchNurseNotified true}}> 
          <span></span>
        </label>
      </div>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Professional Medication Necessary?</label>
    <div class="col-9 col-form-label">
      <div class="checkbox-list">
        <label class="checkbox checkbox-primary">
          <input type="checkbox" name="professionalMedication" id="ouchProfessionalMedication" {{checkedIfEq ouchProfessionalMedication true}}>
          <span></span>
        </label>
      </div>
    </div>
  </div>
  {{> momentFormTimePicker}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea class="form-control" rows="3" placeholder="Additional info" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
