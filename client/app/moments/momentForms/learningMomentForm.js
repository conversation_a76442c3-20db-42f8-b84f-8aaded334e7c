import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './learningMomentForm.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from "moment-timezone";
import {getPeopleData} from "../../../services/peopleMeteorService"
import './momentFormTimePicker';
import { Curriculum, Curriculums } from '../../../../lib/collections/curriculum';

Template.learningMomentForm.onCreated(function() {
	this.autorun(() => {
		const tags = Session.get("taggedPeople");
		if (tags && tags.length) {
			let query = {_id: {$in: []}};
			for (const tag of tags) {
				query._id['$in'].push(tag.split('|')[1]);
			}

			getPeopleData(query).then((res) => {
				if (res) {
					Session.set("taggedPeopleList", res);
				}
			}).catch(err => {
				console.log(err);
			});

		} else {
			Session.set("taggedPeopleList", []);
		}
	})
})

Template.learningMomentForm.helpers({
	'availableCurriculumTypes': function() {
		if (Orgs.current()) {
			var types =  Orgs.current().availableCurriculumTypes({mapped: true});
			return _.map(types, function(v) {return {value: v, name: v.replace('`', ' > ')};});
		}
	},
	'availableCurriculums': async function() {
		const tags = Session.get("taggedPeople");
		const org = Orgs.current();
		const startRange =  new moment.tz(org.getTimezone()).startOf("day").valueOf();
		const endRange = new moment.tz(org.getTimezone()).add(1,"day").startOf("day").valueOf();
		const query = {$and: [
					  {scheduledDate: {$gte: startRange, $lt: endRange}}
					]};
		const selectedGroupsQuery = {$or: [ {selectedGroups: [] } ] };

		const groupIds = new Set();
		const taggedPeopleList = Session.get("taggedPeopleList");
		if (tags && tags.length) {
			let i = 0;
			for (const tag of tags) {
				switch (tag.split('|')[0]) {
					case "person":
						const person = taggedPeopleList[i];
						if (!person) {
							break;
						}
						if (person.defaultGroupId) {
							groupIds.add(person.defaultGroupId)
						}
						if (person.checkInGroupId) {
							groupIds.add(person.checkInGroupId)
						}
						break;
					case "group":
						groupIds.add(tag.split('|')[1]);
						break;
					default:
						break;
				}
				++i;
			}
		}


		if (groupIds.size) {
			groupIds.forEach(id => selectedGroupsQuery["$or"].push({selectedGroups: {$elemMatch: {$eq: id}}}));
		}
		
		if (FlowRouter.current().path.includes("groups") && Session.get("activeMomentParentSource") == "group") {
			selectedGroupsQuery["$or"].push({selectedGroups: {$elemMatch: {$eq: Session.get("activeMomentParentSourceId")}}});
		}

		query["$and"].push(selectedGroupsQuery);
    		
		let out;
		await Meteor.callAsync('getCurriculumData', query).then((res) => {
			out = res.map((d) => {
				return new Curriculum(d); 
			})
		});
		
		return out;
	},
	'availableAssessmentLevels': function() {
		return Orgs.current() && Orgs.current().getAvailableAssessmentLevels();
	},
	'currentMomentLearningCurriculum': function() {
		return Session.get("activeMomentLearningCurriculum");
	},
	'currentMomentLearningType': function() {
		return Session.get("activeMomentLearningType");
	},
	"currentMomentLearningAssessmentLevel": function() {
		return Session.get("activeMomentLearningAssessmentLevel")
	}
});


Template.learningMomentForm.events({
	"change #learningCurriculum": function(e, t) {
		let curriculumId = $(event.target).val();
		Session.set("activeMomentLearningCurriculum", curriculumId);
		/*
		var c = Curriculums.findOne({_id: curriculumId});
		if (c) {
			$("#comment").val(c.message);
		}*/
	},
	"change #learningType": function(e, t) {
		let name = $(event.target).val();
		Session.set("activeMomentLearningType", name);

		let selectedGroupsQuery = [{selectedGroups: []}];
    //TODO: modify this for person activity screen
		if (Template.parentData(3) && Template.parentData(3).checkInGroupId ) 
			selectedGroupsQuery.push({selectedGroups: Template.parentData(3).checkInGroupId});

		let query = {$and: [{$or: selectedGroupsQuery},
					  {scheduledDate: {$gte: startRange, $lt: endRange}},
					  {$or:[ {selectedType: Session.get("activeMomentLearningType")}, {type: Session.get("activeMomentLearningType")} ] } 
					]};

		let q = Curriculums.find(query).map(function(d) { return d.message;}).join("\n");

		//$("#comment").val( q);
	},
	'click #learningAssessmentLevel button': function(event) {
		let name = $(event.target).data('learningassessment');
		Session.set("activeMomentLearningAssessmentLevel", name);
	},
});

Template.learningMomentForm.onDestroyed(function() {
	delete Session.keys["taggedPeopleList"];
});