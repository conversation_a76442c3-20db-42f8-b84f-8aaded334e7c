import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './medicalMomentForm.html';
import {getPeopleById, getPeopleData} from "../../../services/peopleMeteorService";
import './momentFormTimePicker';
import { Person } from '../../../../lib/collections/people';

Template.medicalMomentForm.onCreated(function () {
	this.staffPeopleResult = new ReactiveVar([]);
	const query = {
		orgId: Meteor.user().orgId, 
		inActive:{$ne:true},
		$or: [{type:"admin"}, {type:"staff"}]
	};
	
	getPeopleData(query).then((res) => {
		if (res) {
			const result = res.map((d) => {
				return {_id:d._id, name: d.firstName + " " + d.lastName};
			});
			this.staffPeopleResult.set(result);
		}
	}).catch(err => {
		console.log(err);
	});

	this.autorun(function() {
		const taggedPeopleList = Session.get("taggedPeople") || [];
		if (taggedPeopleList.length != 1 || !taggedPeopleList[0].startsWith("person|")){
			Session.set("taggedPeopleObj", {})
		}
		else{
			const taggedId = taggedPeopleList[0].split("|")[1]
			getPeopleById(taggedId).then((res) => {
				if (res) {
					Session.set("taggedPeopleObj", res)
				}
			}).catch(err => {
				console.log(err);
			});
		}
	});
});

Template.medicalMomentForm.onDestroyed(function () {
	delete Session.keys['taggedPeopleObj']
})

Template.medicalMomentForm.helpers({
	'staffPeople': function() {
		return Template.instance().staffPeopleResult.get();
	},
	'availableMedicineTypes': function() {
		let options = [];
		if(Orgs.current().hasCustomization("moments/medical/organizationSpecificTypes"))
 			options = Orgs.current().valueOverrides ? Orgs.current().valueOverrides.medicineTypes : [];
		return _.map(options,
			function(val) {
				return {name: val}
			});
	},
	'currentMedicalAdministeredBy': function() {
		let x = Session.get("activeMomentMedicalAdministeredBy") || Meteor.user()._id;
		return x;
	},
	'currentMedicalMedicineType': function() {
		return Session.get("activeMomentMedicalMedicineType");
	},
	'currentMedicalMedicineAmount': function() {
		return Session.get("activeMomentMedicalMedicineAmount");
	},
	'allowFreeEntry': function() {
		return !(Orgs.current() && Orgs.current().hasCustomization("moments/medical/useProfileMedications"));
	},
	'availableMedications': function() {
		const taggedPerson = new Person(Session.get("taggedPeopleObj"))
		const medications = taggedPerson && taggedPerson.currentMedications();
		if (medications && medications.length) {
			return medications.map(medication => ({
				...medication,
				_id: medication._id || medication.salesforceRecordId
			}));
		}
		return
	},
	'medicationDescription': function() {
		return this.name + " (" + this.dosage + " - " + this.frequencyDescription + ")";
	},
	'currentMedicalMedicationId': function() {
		return Session.get("activeMomentMedicalMedication");
	}
});
