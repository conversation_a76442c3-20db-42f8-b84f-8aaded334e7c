<template name="pottyMomentForm">
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Type</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div id="pottyType">
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyType 'Wet'}}" data-pottytype="Wet">{{ getMomentTypesPottyWet }}</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyType 'BM'}}" data-pottytype="BM">{{ getMomentTypesPottyBM }}</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyType 'Wet+BM'}}" data-pottytype="Wet+BM">{{ getMomentTypesPottyWetBM }}</button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyType 'Dry'}}" data-pottytype="Dry">{{ getMomentTypesPottyDry }}</button>
      </div>
    </div>
  </div>
  {{#if hasCustomization "moments/potty/showContinence"}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Continence</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <div id="pottyTypeContinence">
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyTypeContinence 'Continent'}}" data-pottytypecontinence="Continent">Continent</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyTypeContinence 'Incontinent'}}" data-pottytypecontinence="Incontinent">Incontinent</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyTypeContinence 'Refused'}}" data-pottytypecontinence="Refused">Refused</button>
        </div>
      </div>
    </div>
  {{/if}}
  {{#if showTraining}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Applied Ointment</label>
      <div class="col-9 col-form-label">
        <div class="checkbox-list">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" name="appliedOintment" id="pottyAppliedOintment" {{checkedIfEq pottyAppliedOintment true}}/>
            <span></span>
          </label>
        </div>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Training</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <div id="pottyTraining">
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyTraining 'Tried'}}" data-pottytraining="Tried">Tried</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyTraining 'Successful'}}" data-pottytraining="Successful">Successful</button>
          <button type="button" class="btn btn-primary {{activeIfEq currentMomentPottyTraining 'Accident'}}" data-pottytraining="Accident">Accident</button>
        </div>
      </div>
    </div>
  {{/if}}
  {{>momentFormTimePicker}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea class="form-control" rows="3" placeholder="What's happening?" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
