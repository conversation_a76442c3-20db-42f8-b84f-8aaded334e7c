<template name="alertMomentForm">
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Delivery</label>
    <div class="col-9 col-form-label">
      <div class="checkbox-list">
        <label class="checkbox checkbox-primary">
          <input type="checkbox" name="alertEmail" id="inputAlertEmail" {{checkedIfEq alertSendTypeEmail true}}/>
          <span></span>
          Email
        </label>
        <label class="checkbox checkbox-primary">
          <input type="checkbox" name="alertSMS" id="inputAlertSms" {{checkedIfEq alertSendTypeText true}}/>
          <span></span>
          SMS (text)
        </label>
        <label class="checkbox checkbox-primary">
          <input type="checkbox" name="alertPush" id="inputAlertPush" {{checkedIfEq alertSendTypePush true}}/>
          <span></span>
          Push (app)
        </label>
      </div>
    </div>
  </div>
  {{#if hasCustomization "people/pinCodeCheckin/enabled"}}
  	<div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Show next check-in/out</label>
      <div class="col-9 col-form-label">
        <div class="checkbox-list">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" name="showCheckin" id="alertShowCheckin" {{checkedIfEq alertShowCheckin true}}/>
            <span></span>
          </label>
        </div>
      </div>
  	</div>
  {{/if}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea class="form-control" rows="3" placeholder="What's happening?" id="comment">{{comment}}</textarea>
    </div>
  </div>
  {{#if hasSmsAlert}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">SMS Preview ({{getAvailableCharacters}} charcters left)</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <textarea class="form-control" rows="3" id="smsPreview" disabled>{{getSmsAlert}}</textarea>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Customize SMS Notification</label>
      <div class="col-1 col-form-label">
        <div class="checkbox-inline">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" name="overrideAlertSms" id="overrideAlertSms" {{shouldDisable}} />
            <span></span>
          </label>
        </div>
      </div>
      {{#if hasCustomizeSms}}
        <div class="col-6 col-form-label">
          <textarea class="form-control" rows="3" id="smsCustomize" {{shouldDisable}}>{{getCustomSmsAlert}}</textarea>
        </div>
      {{/if}}
    </div>
  {{/if}}
</template>
