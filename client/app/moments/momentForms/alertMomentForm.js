import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import './alertMomentForm.html';
const { SegmentedMessage } = require('sms-segments-calculator');
const GSM_7 = 153; // multi part body length
const UCS_2 = 67; // multi part body lenght

const parseSegmentedMessage = (str, postfixText = "") => {
  let localStr = str;
  const segmentedMessage = new SegmentedMessage(localStr);
  const subStrLength = (segmentedMessage.encodingName == "GSM-7") ? GSM_7 : UCS_2;
  localStr = localStr.substr(0, subStrLength);
  if (postfixText.length > 0) {
    localStr = localStr.slice(0, (postfixText.length * -1)) + postfixText;
  }
  return {str: localStr.substr(0, subStrLength), smsEncodingLength: subStrLength};
}

const smsCustomize = (e, i) => {
  const val = $("#smsCustomize").val() || "";
  const overrideAlertSms = i.overrideAlertSms.get();
  if (overrideAlertSms) {
    const str = `${i.prefixText.get()}: ${val}${i.postfixText.get()}`;
    const sanitizedText = parseSegmentedMessage(str, i.postfixText.get());
    // i.smsAlertText.set(sanitizedText.str);
    Session.set("newMomentSmsAlertText", sanitizedText.str);
    i.smsEncodingLength.set(sanitizedText.smsEncodingLength);
    i.smsAvailableCharacters.set(sanitizedText.smsEncodingLength - sanitizedText.str.length);
  };
}

const smsComment = (e,i) => {
  const comment = $("#comment").val() || "";
  const overrideAlertSms = i.overrideAlertSms.get();
  if (!overrideAlertSms) {
    const str = `${i.prefixText.get()}: ${comment}${i.postfixText.get()}`;
    const sanitizedText = parseSegmentedMessage(str, i.postfixText.get());
    // i.smsAlertText.set(sanitizedText.str);
    Session.set("newMomentSmsAlertText", sanitizedText.str);
    i.smsEncodingLength.set(sanitizedText.smsEncodingLength);
    i.smsAvailableCharacters.set(sanitizedText.smsEncodingLength - sanitizedText.str.length);
  };
}

Template.alertMomentForm.onCreated(function() {
  const hasAlert = Session.get("activeMomentSmsAlertText") != null;
  this.hasSmsAlert = new ReactiveVar(hasAlert);
  this.overrideAlertSms = new ReactiveVar(hasAlert);
  const org = Orgs.current();
  this.prefixText = new ReactiveVar(org?.whiteLabel?.smsPrefix ?? org.getLongName());
  this.postfixText = new ReactiveVar(org?.whiteLabel?.smsPostfix ?? "- read more mp://open");
  // this.smsAlertText = (hasAlert) ? new ReactiveVar(Session.get("activeMomentSmsAlertText")) : new ReactiveVar(this.prefixText.get() + ": " + this.postfixText.get());
  const smsAlertText = (hasAlert) ? Session.get("activeMomentSmsAlertText") : this.prefixText.get() + ": " + this.postfixText.get();
  Session.set("newMomentSmsAlertText", smsAlertText);
  this.customSmsAlertText = new ReactiveVar("");
  // this.smsEncodingLength = new ReactiveVar(parseSegmentedMessage(this.smsAlertText.get()).smsEncodingLength);
  // this.smsAvailableCharacters = new ReactiveVar(this.smsEncodingLength.get() - this.smsAlertText.get().length);

  this.smsEncodingLength = new ReactiveVar(parseSegmentedMessage(smsAlertText).smsEncodingLength);
  this.smsAvailableCharacters = new ReactiveVar(this.smsEncodingLength.get() - smsAlertText.length);
})

Template.alertMomentForm.events({
	'change #inputAlertSms': function(e, i) {
		i.hasSmsAlert.set($(e.currentTarget).prop("checked"));
	},
  'change #overrideAlertSms': function(e, i) {
    const overrideAlertSms = $(e.currentTarget).prop("checked");
    i.overrideAlertSms.set(overrideAlertSms);
    if (overrideAlertSms) {
      smsCustomize(e,i)
    } else {
      smsComment(e,i);
    }
	},
  'change/keyup/paste #comment': function(e, i) {
    smsComment(e,i);
  },
  'change/keyup/paste #smsCustomize': function(e, i) {
    smsCustomize(e,i);
  },
});

Template.alertMomentForm.helpers({
  'getAvailableCharacters': function() {
    return Template.instance().smsAvailableCharacters.get();
  },
	'hasSmsAlert': function() {
		return Template.instance().hasSmsAlert.get();
	},
  'hasCustomizeSms': function() {
    return Template.instance().overrideAlertSms.get();
  },
  'shouldDisable': function() {
    const hasExistingAlert = Session.get("activeMomentSmsAlertText") != null;
    return (hasExistingAlert) ? "disabled" : "";
  },
  'getCustomSmsAlert': function() {
    const hasExistingAlert = Session.get("activeMomentSmsAlertText") != null;
    if (hasExistingAlert) {
      return Session.get("activeMomentSmsAlertText");
    } else {
      return Template.instance().customSmsAlertText.get();
    }
  },
  'getSmsAlert': function() {
    return Session.get("newMomentSmsAlertText");
		// return Template.instance().smsAlertText.get();
	},
});
