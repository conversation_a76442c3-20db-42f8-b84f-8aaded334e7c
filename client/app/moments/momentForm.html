<template name="momentForm">
  <form class="form pt-9" id="momentForm">
    <div class="card-body">
  		<div  class="form-group row">
  			<label data-cy="moment-type-label" class="col-xl-3 col-lg-3 text-right col-form-label">Moment Type</label>
        {{#unless isNewMoment}}
          <div class="col-lg-9 col-xl-6">
            <input class="form-control form-control-solid" type="text" value="{{currentMoment.momentTypePrettyTranslation}}" disabled/>
          </div>
        {{else}}
          <div class="col-lg-6 col-md-9 col-sm-12">
            <select data-cy="moment-type-selection" class="form-control" name="momentTypeSelection" id="momentTypeSelection">
              <option value="comment" {{selectedIfEqual currentMomentType 'comment'}}>Comment</option>
              {{#if hasCustomizationWithAdminCheck "moments/potty/enabled" "moments/potty/adminOnly"}}
                <option value="potty" {{selectedIfEqual currentMomentType 'potty'}}>{{ getMomentTypesPottyPrettyName }}</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/food/enabled" "moments/food/adminOnly"}}
                <option value="food" {{selectedIfEqual currentMomentType 'food'}}>Food</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/sleep/enabled" "moments/sleep/adminOnly"}}
                <option value="sleep" {{selectedIfEqual currentMomentType 'sleep'}}>Sleep</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/activity/enabled" "moments/activity/adminOnly"}}
                <option value="activity" {{selectedIfEqual currentMomentType 'activity'}}>Activity</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/medical/enabled" "moments/medical/adminOnly"}}
                <option value="medical" {{selectedIfEqual currentMomentType 'medical'}}>Medical</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/mood/enabled" "moments/mood/adminOnly"}}
                <option value="mood" {{selectedIfEqual currentMomentType 'mood'}}>Mood</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/incident/enabled" "moments/incident/adminOnly"}}
                <option value="incident" {{selectedIfEqual currentMomentType 'incident'}}>Incident</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/alert/enabled" "moments/alert/adminOnly"}}
                <option value="alert" {{selectedIfEqual currentMomentType 'alert'}}>Notification</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/supplies/enabled" "moments/supplies/adminOnly"}}
                <option value="supplies" {{selectedIfEqual currentMomentType 'supplies'}}>Supplies</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/learning/enabled" "moments/learning/adminOnly"}}
                <option value="learning" {{selectedIfEqual currentMomentType 'learning'}}>Learning</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/illness/enabled" "moments/illness/adminOnly"}}
                <option value="illness" {{selectedIfEqual currentMomentType 'illness'}}>Illness</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/ouch/enabled" "moments/ouch/adminOnly"}}
                <option value="ouch" {{selectedIfEqual currentMomentType 'ouch'}}>Ouch</option>
              {{/if}}
              {{#if hasCustomizationWithAdminCheck "moments/portfolio/enabled" "moments/portfolio/adminOnly"}}
                <option value="portfolio" {{selectedIfEqual currentMomentType 'portfolio'}}>Portfolio</option>
              {{/if}}
              {{#each momentDefinition in momentDefinitions}}
                {{#if customizationExistsWithAdminCheck (formatMomentEnabledString momentDefinition.momentType) (formatMomentAdminOnlyString momentDefinition.momentType)}}  
                  <option value="{{momentDefinition.momentType}}" {{selectedIfEqual currentMomentType momentDefinition.momentType}}>{{momentDefinition.momentTypePretty}}</option>
                {{/if}}
              {{/each}}
            </select>
          </div>
        {{/unless}}
  		</div>
      {{#unless suppressDetailsTagList}}
        {{#if hasCenterOptions}}
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">All Center Tagging Options</label>
            <div class="col-9 col-form-label">
              <div class="checkbox-list col-4 px-0">
                {{#if showEntireOrgOption}}
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" name="sendOnlyCheckins" id="inputSendOnlyCheckins"/>
                    <span></span>
                    Tag everyone checked in now
                  </label>
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" name="sendEntireOrg" id="inputSendEntireOrg"/>
                    <span></span>
                    Tag everyone
                  </label>
                {{/if}}
                {{#if showGroupTagOption}}
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" name="tagAllInGroup" id="inputTagAllInGroup"/>
                    <span></span>
                    Tag people currently checked into group(s)
                  </label>
                {{/if}}
              </div>
            </div>
          </div>
        {{/if}}
    		<div class="form-group row">
    			<label data-cy="tag-people-input" class="col-xl-3 col-lg-3 text-right col-form-label">Tagged People</label>
    			<div class="col-lg-6 col-md-9 col-sm-12" data-cy="tag-people">
            <input data-cy="tag-users-list" id="moment-tagify" class="form-control tagify" name='users-list-tags' placeholder='Tag People'>
    			</div>
    		</div>
      {{/unless}}
      <div class="separator separator-dashed my-10"></div>
      <div class="row">
        <div class="col-lg-9 col-xl-6 offset-xl-3 mb-5">
          <span data-cy="moment-details" class="font-size-h3">Moment Details:</span>
        </div>
      </div>
      {{#if activeMomentForm}}
        {{> Template.dynamic template=activeMomentForm data=currentMoment}}
      {{/if}}
      <div class="separator separator-dashed my-10"></div>
      {{#unless hideFileUpload}}
        <div class="form-group row">
          <label class="col-xl-3 col-lg-3 text-right col-form-label">Attach Media</label>
          <div class="col-lg-6 col-md-9 col-sm-12">
            <label for="exampleInputFile" class="btn btn-primary custom-file-upload font-weight-bolder">
              <input type="file" id="exampleInputFile" multiple="multiple" accept="image/*,video/*,application/pdf" data-cy="moment-input-files">
              <i class="fad fa-cloud-upload"></i> Choose File
            </label>
            {{#if showInsertSamplePhoto }}
              <a href="#" id="btnUseSamplePhoto" style="margin-left:15px;text-decoration: underline;font-size:18px">Insert Sample Photo</a>
            {{/if}}
            {{#each currentAttachedMedia}}
              <br/>{{uploadedFile.name}} (<a data-id="{{metaContext.tokenId}}" class="removeAttachedMedia" style="cursor:pointer;">remove</a>) {{status}}<br/>
            {{/each}}
            <!-- <div id="imagearea"></div> -->
          </div>
        </div>
      {{/unless}}
  		<div class="separator separator-dashed my-10"></div>
    </div>
	</form>
</template>
