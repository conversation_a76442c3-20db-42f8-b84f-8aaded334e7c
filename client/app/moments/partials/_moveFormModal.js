import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_moveFormModal.html';
import { Groups } from '../../../../lib/collections/groups';
import { hideModal } from '../../main';

Template._moveFormModal.helpers({
	'groups': function() {
		return Groups.find({}, {sort:{name:1}});
	}
});

Template._moveFormModal.rendered = function() {
	if (this && this.data && this.data.checkInGroupId)
		$("#switchDestinationGroup").val(this.data.checkInGroupId);
};

Template._moveFormModal.events({
	'click #move-modal-save': function(event, template){
		event.preventDefault();

		var switchData = {
			personId: template.data.personId,
			groupId: $("#switchDestinationGroup").val(),
			groupName: $("#switchDestinationGroup option:selected").text(),
			switchAllInGroup: $("#switchAllInGroup").is(":checked")
		};
		
		Meteor.callAsync('switchGroup', switchData).then(res => {}).catch(err => {});
		hideModal("#_moveFormModal")
	}
});
