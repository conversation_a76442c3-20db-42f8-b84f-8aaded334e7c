<template name="_moveFormModal">
  <div id="_moveFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Change Group</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmMove">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Choose Group</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="switch-destination-group" class="form-control form-control-lg form-control-solid" name="switchDestinationGroup" id="switchDestinationGroup">
                  <option value=""></option>
                  {{#each groups}}
                    <option value="{{_id}}">{{ name }}</option>
                  {{/each}}
                </select>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Options</label>
              <div class="col-lg-9 col-xl-6">
                <div class="checkbox-list">
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" id="switchAllInGroup"/>
                    <span></span>
                    Move all checked-in people from current group to this group
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <button data-cy="switch-group-btn" type="button" class="btn btn-primary font-weight-bolder mr-2" id="move-modal-save">Switch Group</button>
          <button data-cy="switch-group-close" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
