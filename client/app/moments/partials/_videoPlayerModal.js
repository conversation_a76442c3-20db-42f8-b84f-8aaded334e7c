import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import _ from '../../../../lib/util/underscore';
import './_videoPlayerModal.html';

Template._videoPlayerModal.created = function() {
	var instance = this;
	this.currentLocation = "";
};

Template._videoPlayerModal.helpers({
	"pathForVideoFormat": function(format) {
		var mediaVar = Session.get("videoPlayerLocation");
		
		if (mediaVar && mediaVar != "") {
			var media = mediaVar.split("|");

			var output = "";
			switch (format) {
				case 'mp4': 
					output = media[0];
					break;
				case 'webm':
					output = media[1];
					break;
			}

			return output;
		}
	},
	"pathForVideoThumbnail": function() {

	}
});

Template._videoPlayerModal.rendered = function() {

	$("#_videoPlayerModal").on("hidden.bs.modal", function() {
		var players = $("video.video-center");
		_.each(players, function(player) { console.log("shutting down one"); player.pause(); });
	});

	var self = this;

	this.autorun(function() {
		var mediaVar = Session.get("videoPlayerLocation");
		if (mediaVar && mediaVar != "" && mediaVar != self.currentLocation) {
			self.currentLocation = mediaVar;
			var media = mediaVar.split("|");
			$(".videocontent").empty();
			var vidElem = document.createElement('video');
			vidElem.className="video-center";
			vidElem.controls = true;
			vidElem.preload = "auto";
			//vidElem.style.width = "100%";
			var sourceMP4 = document.createElement('source');
			sourceMP4.type = "video/mp4";
			sourceMP4.src = media[0];
			vidElem.appendChild(sourceMP4);
			var sourceWebm = document.createElement('source');
			sourceWebm.type = "video/webm";
			sourceWebm.src = media[1];
			vidElem.appendChild(sourceWebm);
			$(".videocontent")[0].append(vidElem);
		}
		
	});
}
