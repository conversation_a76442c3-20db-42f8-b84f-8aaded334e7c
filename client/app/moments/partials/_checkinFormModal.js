import { Template } from 'meteor/templating';
import './_checkinFormModal.html';
import { People } from "../../../../lib/collections/people";
import './_checkoutForm.js'
import './_checkinForm.js'

Template._checkinFormModal.helpers({
  "getPersonId": function() {
    return Template.instance().data.personId;
  },
  "getPersonName": function() {
    var person = People.findOne(Template.instance().data.personId);
    return `${person.firstName} ${person.lastName}`;
  }
});

Template._checkinFormModal.events({
  "click #checkin-modal-save": function(e, template) {
    console.log("parent");
    $("#checkin-save").trigger("click");
  }
})
