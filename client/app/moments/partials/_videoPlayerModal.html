<template name="_videoPlayerModal">
  <div id="_videoPlayerModal" class="modal">
  	<div class="modal-dialog modal-dialog-scrollable modal-xl" style="width: 100%;height: 100%;">
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Video Player</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-gray-100 d-flex justify-content-center">
          <div class="videowrapper d-flex justify-content-center align-items-center">
              <div class="videocontent video-center d-flex justify-content-center">
              <video id="videoPlayer" class="video-center" controls preload="auto" >
                <source src="{{pathForVideoFormat 'mp4'}}" type='video/mp4'>
                <source src="{{pathForVideoFormat 'webm'}}" type='video/webm'>
                <p class="vjs-no-js">
                  To view this video please enable JavaScript, and consider upgrading to a web browser that
                  <a href="http://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
                </p>
              </video>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
