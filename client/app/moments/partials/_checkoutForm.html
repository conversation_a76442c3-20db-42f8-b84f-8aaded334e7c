<template name="_checkoutForm">
  <form class="px-8 py-8" id="checkout-dropdown-form">
    {{#unless hideName}}
      <h5>New Check Out</h5>
      <h3 class="mb-6">{{ person.firstName}} {{person.lastName}}</h3>
    {{/unless}}
    <div class="form-group" autocomplete="off">
      <label for="checkin-time">Time</label>
      {{#if allowChangeTime}}
        <input data-cy="checkout-time" class="form-control" type="time" id="checkout-time" value="{{currentTime}}"/>
      {{else}}
        <input class="form-control" type="time" id="checkout-time" value="{{currentTime}}" disabled/>
      {{/if}}
    </div>
    <div class="form-group">
      <label for="checkout-date">Date</label>
      <input class="form-control" type="text" id="checkout-date" value="{{dateFormatted}}"/>
    </div>
    <div class="form-group">
      <label for="checkout-mood">Mood</label>
      <div class="btn-group ml-4" id="checkout-mood">
        <div class="btn-group" data-toggle="buttons" id="checkout-mood">
          <label class="btn btn-icon btn-primary"  data-moodlevel="Happy">
             <input type="radio" style="display:none;"><span class="icon-2x fad fa-swap-opacity fa-grin-beam text-white"></span>
          </label>
          <label class="btn btn-icon btn-primary"  data-moodlevel="SoSo">
             <input type="radio" style="display:none;"><span class="icon-2x fad fa-swap-opacity fa-meh text-white"></span>
          </label>
          <label class="btn btn-icon btn-primary"  data-moodlevel="Sad">
             <input type="radio" style="display:none;"><span class="icon-2x fad fa-swap-opacity fa-frown-open text-white"></span>
          </label>
        </div>
      </div>
    </div>
    {{#if hasCustomization "moments/checkin/showTransportation"}}
      <div class="form-group">
        <label for="checkout-transportation">Transportation</label>
        <select class="form-control" name="checkout-transportation" id="checkout-transportation" autocomplete="off">
          <option value=""></option>
          <option value="bus">Leaving by bus</option>
          <option value="driver">Leaving with driver</option>
          <option value="family">Leaving by family</option>
        </select>
      </div>
    {{/if}}
    {{#if hasCustomization "moments/checkin/showHealthCheck"}}
      <div class="form-group">
        <label>Health Check</label><br/>
        <br/>
        <table>
          {{#each availableHealthCheckTypes}}

          <tr><td>{{this}}:</td>
          <td>
            <div class="btn-group btn-group-health-check" data-toggle="buttons" id="healthCheck{{this}}">
              <label class="btn btn-icon btn-primary" data-healthtype="{{this}}" data-healthlevel="good">
                 <input type="radio" style="display:none;"><span class="icon-2x fad fa-smile text-white fa-swap-opacity" ></span>
              </label>
              <label class="btn btn-icon btn-primary" data-healthtype="{{this}}" data-healthlevel="bad">
                 <input type="radio" style="display:none;"><span class="icon-2x fad fa-frown-open text-white fa-swap-opacity"></span>
              </label>
            </div>
          </td>
          </tr>
          {{/each}}
        </table>
      </div>
    {{/if}}
    <div class="form-group">
      <label for="checkout-pickup-by">Picked Up By</label>
      <select class="form-control" id="checkout-pickup-by">
        <option value=""></option>
        {{#each checkedOutByPeople}}
          <option value="{{fetchPerson._id}}">{{fetchPerson.firstName}} {{fetchPerson.lastName}}</option>
        {{/each}}
      </select>
    </div>
    {{#if showEarlyCheckOutReason}}
    <div class="form-group" autocomplete="off">
      <label for="early-reason">Early Check Out Reason</label>
      <select data-cy="reasons" class="form-control" name="early-reason" id="early-reason" autocomplete="off" required>
        <option value=""></option>
        {{#each earlyCheckOutReasons}}
          <option value="{{this._id}}">{{this.reason}}</option>
        {{/each}}
      </select>
    </div>
    {{/if}}
    <div class="form-group">
      <label for="checkout-comments">Comments</label>
      <textarea class="form-control form-control-solid" rows="3" id="checkout-comments"></textarea>
    </div>
    {{#if showStaffCertificationMessage}}
      <div class="alert alert-custom alert-notice {{#if getTimeCertified}}alert-light-success{{else}}alert-light-info{{/if}} fade show" id="checkout-certify-time" role="alert">
        <div class="alert-icon"><i class="fad {{#if getTimeCertified}}fa-shield-check{{else}}fa-exclamation-triangle{{/if}}"></i></div>
        <div class="alert-text">I certify these hours are a true and accurate record of my time worked on this day.</div>
        {{#unless getTimeCertified}}
          <div class="alert-close">
            <div class="btn btn-icon bg-white" id="checkout-certify-time-btn">
              <span aria-hidden="true"><i class="text-secondary icon-2x fad fa-check"></i></span>
            </div>
          </div>
        {{/unless}}
      </div>
    {{/if}}
    {{#if getShowAlert}}
      <div class="alert alert-custom alert-notice alert-light-danger fade show" id="checkout-alert" role="alert">
        <div class="alert-icon"><i class="fad fa-exclamation-triangle"></i></div>
        <div data-cy="check-alert-text" class="alert-text">{{getActiveAlertText}}</div>
        <div class="alert-close">
          <button type="button" class="close" data-dismiss="alert" aria-label="Close" id="checkout-close-alert">
            <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
          </button>
        </div>
      </div>
    {{/if}}
    <input type="button" class="btn btn-primary font-weight-bolder" value="Check Out" id="checkOutFormSubmit" {{hideSave}}/>
  </form>
</template>
