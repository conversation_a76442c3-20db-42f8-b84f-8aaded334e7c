<template name="_checkinFormModal">
  <div id="_checkinFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">New Check In: {{getPersonName}}</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
            {{> _checkinForm _id=getPersonId hideName=true hideSave=true closeModalOnSave="#_checkinFormModal"}}
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <button data-cy="checkin-modal-save" type="button" class="btn btn-primary font-weight-bolder mr-2" id="checkin-modal-save">Check In</button>
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
