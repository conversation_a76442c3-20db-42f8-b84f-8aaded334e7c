import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './_checkinForm.html';
import { MomentUtils } from '../../../../lib/util/momentUtils';
import { LateCheckInEarlyDropOffModes } from '../../../../lib/constants/momentTypeConstants';
import { AvailableCustomizations } from '../../../../lib/customizations';
const { CheckInFormClientService } = require('../../../services/moments/CheckInFormClientService');

const moment = require('moment-timezone');

Template._checkinForm.onCreated(function() {
	this.currentOrg = Orgs.current();
	this.showLateCheckInReason = new ReactiveVar(false);
	this.service = new CheckInFormClientService(this.data, this.showLateCheckInReason);

	Meteor.callAsync("getPeopleById", { _id: this.data._id }).then((person) => {
		this.service.checkinPerson.set(person);
	}).catch((error) => {
		mpSwal.fire('Error', `Error fetching person by ID: ${error}`, 'error');
	});

	this.autorun(() => {
		Meteor.subscribe("thePerson", { personById: this.data._id });
	});
});

Template._checkinForm.onRendered(function() {
	if (this.currentOrg.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
		const personId = this.data._id;
		const org = this.currentOrg;
		const timezone = org.getTimezone();
		const selectedTime = moment.tz(document.getElementById('checkin-time').value, "HH:mm", timezone).valueOf();
		this.showLateCheckInReason.set(MomentUtils.isEarlyOrLateCheckInCheckOut(personId, org, timezone, selectedTime, LateCheckInEarlyDropOffModes.LATE_DROP_OFF));
	}
});

Template._checkinForm.helpers({
	'hideSave': function() {
		return Template.instance().service.getHideSave();
	},

	'getShowAlert': function() {
		return Template.instance().service.getShowAlert();
	},

	'getActiveAlertText': function() {
		return Template.instance().service.getActiveAlertText();
	},

	'canSelectPayType': function() {
		return Template.instance().service.canSelectPayType(Template.instance().currentOrg);
	},

	'getCustomPayTypes': function() {
		return Template.instance().service.getCustomPayTypes(Template.instance().currentOrg);
	},

	'getSelectedPayType': function(selected, id) {
		return Template.instance().service.getSelectedPayType(selected, id, Template.instance().currentOrg);
	},

	'groups': function() {
		return Template.instance().service.groups();
	},

	'defaultGroupId': function() {
		return (Session.get("currentGroupId") || '');
	},

	'allowChangeTime': function() {
		return Template.instance().service.allowChangeTime(Template.instance().currentOrg);
	},

	'currentTime': function() {
		return Template.instance().service.currentTime(Template.instance().currentOrg);
	},

	'familyMembers': function() {
		return Template.instance().service.familyMembers();
	},

	'person': function() {
		return Template.instance().service.checkinPerson.get();
	},

	'availableHealthCheckTypes': function() {
		return Template.instance().service.availableHealthCheckTypes(Template.instance().currentOrg);
	},

	'familyCheckin': function() {
		return Template.instance().service.familyCheckin(Template.instance().currentOrg, Session);
	},

	'showFullScreen': function() {
		return Meteor.isCordova;
	},

	'showLateCheckInReason': function() {
		return Template.instance().showLateCheckInReason.get();
	},

	'lateCheckInReasons': function() {
		return Template.instance().currentOrg.pickDropReasons
	}
});

Template._checkinForm.events({
	'click #checkin-close-alert': function(event, instance) {
		instance.service.closeAlert();
	},

	'click .btn-group-health-check button': function(event, instance) {
		event.preventDefault();
		instance.service.handleGroupHealthCheckButton(event);
	},

	'click .btn-group-button-bar button': function(event, instance) {
		event.preventDefault();
		instance.service.handleGroupHealthCheckButton(event);
	},

	'click #checkin-save': function(event, template) {
		event.preventDefault();
		template.service.checkinSave();
	},

	'change #checkin-time': function(event, instance) {
		if (instance.currentOrg.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
			const personId = instance.data._id;
			const org = instance.currentOrg;
			const timezone = org.getTimezone();
			const selectedTime = moment.tz(event.target.value, "HH:mm", timezone).valueOf();
			instance.showLateCheckInReason.set(MomentUtils.isEarlyOrLateCheckInCheckOut(personId, org, timezone, selectedTime, LateCheckInEarlyDropOffModes.LATE_DROP_OFF));
		}
	}
});

Template._checkinForm.destroyed = function() {
	delete Session.keys['familyCheckInInfo'];
};

