<template name="_checkinForm">
  <form class="px-8 py-8" id="checkin-dropdown-form">
    {{#unless hideName}}
      <h5>New Check In</h5>
      <h3 class="mb-6">{{ person.firstName}} {{person.lastName}}</h3>
    {{/unless}}
    {{#if familyCheckin}}
      <div class="form-group">
        <label for="family-checkin-info">Today's Family Checkin Info</label>
        <table id="family-checkin-info" style="border-collapse: separate;border-spacing: 0px 8px;">
          <tr >
            <td align=right style="vertical-align:top;width:300px"><b>By:</b></td>
            <td class="pl-4">{{familyCheckin.byName}}</td>
          </tr>
          <tr >
            <td align=right style="vertical-align:top;width:300px"><b>Time:</b></td>
            <td class="pl-4">{{formatDate familyCheckin.checkInTime "h:mm a"}}</td>
          </tr>
          {{#if familyCheckin.absent}}
            <tr >
              <td></td>
              <td><b>Absence reported</b></td>
            </tr>
            <tr >
              <td align=right style="vertical-align:top;width:300px"><b>Reason:</b></td>
              <td class="pl-4">{{familyCheckin.absentReason}}</td>
            </tr>
          {{else}}
            {{#each familyCheckin.formFields}}
              <tr >
                <td align=right style="vertical-align:top;width:300px"><b>{{label}}:</b></td>
                <td class="pl-4">{{value}}</td>
              </tr>
            {{/each}}
          {{/if}}
        </table>
      </div>
    {{/if}}
    {{#if canSelectPayType}}
      <div class="form-group">
        <label for="checkin-paytype">Select Pay Type</label>
        <select class="form-control" id="checkin-paytype">
          <option value="standard" {{getSelectedPayType currentMoment.selectedPayTypeId "standard"}}>Standard</option>
          {{#each getCustomPayTypes}}
            <option value={{_id}} {{getSelectedPayType currentMoment.selectedPayTypeId _id}}>{{type}}</option>
          {{/each}}
        </select>
      </div>
    {{/if}}
    <div class="form-group">
      <label for="checkin-group-id">Choose Group</label>
      <select class="form-control" id="checkin-group-id" autocomplete="off">
        <option value="" selected="{{trueIfEq defaultGroupId ''}}"></option>
        {{#each groups}}
          <option value="{{_id}}" selected={{trueIfEq _id defaultGroupId}}>{{ name }}</option>
        {{/each}}
      </select>
    </div>
    {{#if hasCustomization "moments/checkin/showTransportation"}}
      <div class="form-group">
        <label for="checkin-transportation">Transportation</label>
        <select class="form-control" name="checkin-transportation" id="checkin-transportation" autocomplete="off">
          <option value=""></option>
          <option value="bus">Arrived by bus</option>
          <option value="driver">Arrived with driver</option>
          <option value="family">Arrived by family</option>
        </select>
      </div>
    {{/if}}
    {{#if hasCustomization "moments/checkin/showCheckedInBy"}}
      <div class="form-group">
      <label for="checkin-checkedinby">Checked In By</label>
        <select class="form-control" name="checkedInBy" id="checkedInBy" autocomplete="off">
          <option value=""></option>
          {{# each familyMembers}}
            <option value="{{personId}}">{{fetchPerson.firstName}} {{fetchPerson.lastName}}</option>
          {{/each}}
        </select>
      </div>
    {{/if}}
    <div class="form-group" autocomplete="off">
      <label for="checkin-time">Time</label>
      {{#if allowChangeTime}}
        <input data-cy="checkin-time" class="form-control" type="time" id="checkin-time" value="{{currentTime}}"/>
      {{else}}
        <input class="form-control" type="time" id="checkin-time" value="{{currentTime}}" disabled/>
      {{/if}}
    </div>
    {{#if showLateCheckInReason}}
    <div class="form-group" autocomplete="off">
      <label for="late-reason">Late Check In Reason</label>
      <select data-cy="reasons" class="form-control" name="late-reason" id="late-reason" autocomplete="off" required>
        <option value=""></option>
        {{#each lateCheckInReasons}}
          <option value="{{this._id}}">{{this.reason}}</option>
        {{/each}}
      </select>
    </div>
    {{/if}}
    {{#if hasCustomization "moments/checkin/showHealthCheck"}}
      <div class="form-group" autocomplete="off">
        <label>Health Check</label><br/>
        <br/>
        <table>
        {{#each availableHealthCheckTypes}}
          <tr>
            <td>{{this}}:</td>
            <td>
              <div class="btn-group btn-group-health-check" data-toggle="buttons" id="healthCheck{{this}}">
                <label class="btn btn-icon btn-primary" data-healthtype="{{this}}" data-healthlevel="good">
                   <input type="radio" style="display:none;"><span class="icon-2x fad fa-smile text-white fa-swap-opacity" ></span>
                </label>
                <label class="btn btn-icon btn-primary" data-healthtype="{{this}}" data-healthlevel="bad">
                   <input type="radio" style="display:none;"><span class="icon-2x fad fa-frown-open text-white fa-swap-opacity"></span>
                </label>
              </div>
            </td>
          </tr>
        {{/each}}
        </table>
      </div>
      <hr noshade/>
      <b>COVID-19 Screening</b><br/>
      <div class="form-group mt-2">
        Have you had any of the following <b>new</b> symptoms in the last seven days: fever or chills, cough <b>(either new, or different than your usual cough)</b>, sore throat, shortness of breath, or any other flu-like symptoms?<br/>
        <div class="btn-group btn-group-button-bar mb-4" id="healthCheckCovid19Symptoms">
          <button type="button" class="btn btn-primary font-weight-bolder" data-value="yes">Yes</button>
          <button type="button" class="btn btn-primary font-weight-bolder" data-value="no">No</button>
        </div>
        <br/>
        In the past week, have you been in close (less than 6 feet), prolonged contact (more than 2-3 minutes) with someone with suspected or confirmed COVID-19 without using infection protection and control precautions?<br/>
        <div class="btn-group btn-group-button-bar mb-4" id="healthCheckCovid19ProlongedContact">
          <button type="button" class="btn btn-primary font-weight-bolder" data-value="yes">Yes</button>
          <button type="button" class="btn btn-primary font-weight-bolder" data-value="no">No</button>
        </div>
        <br/>
        All workers and visitors on official business must submit to a temperature check. Is Temperature 100.4℉ [38℃] or above?<br/>
        <div class="btn-group btn-group-button-bar mb-4" id="healthCheckCovid19TemperatureAboveLimit">
          <button type="button" class="btn btn-primary font-weight-bolder" data-value="yes">Yes</button>
          <button type="button" class="btn btn-primary font-weight-bolder" data-value="no">No</button>
        </div>
        <br/>
        Temperature:<br/>
        <input class="form-control" type="text" id='healthCheckCovid19Temperature'><br/>
      </div>
    {{/if}}
    <div class="form-group">
      <label for="checkin-comments">Comments</label>
      <textarea class="form-control form-control-solid" rows="3" id="checkin-comments" placeholder="Enter any special instructions for the day or leave this blank..."></textarea>
    </div>
    {{#if getShowAlert}}
      <div class="alert alert-custom alert-notice alert-light-danger fade show" id="checkin-alert" role="alert">
        <div class="alert-icon"><i class="fad fa-exclamation-triangle"></i></div>
        <div class="alert-text">{{getActiveAlertText}}</div>
        <div class="alert-close">
          <button type="button" class="close" data-dismiss="alert" aria-label="Close" id="checkin-close-alert">
            <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
          </button>
        </div>
      </div>
    {{/if}}
    <input type="button" class="btn btn-primary font-weight-bolder" id="checkin-save" value="Check In" {{hideSave}}/>
  </form>
</template>
