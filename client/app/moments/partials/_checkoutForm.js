import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Orgs } from '../../../../lib/collections/orgs';
import './_checkoutForm.html';
import { Person } from '../../../../lib/collections/people';
import { hideModal } from '../../main';
import { MomentUtils } from '../../../../lib/util/momentUtils';
import { LateCheckInEarlyDropOffModes } from '../../../../lib/constants/momentTypeConstants';
import { AvailableCustomizations } from '../../../../lib/customizations';
const moment = require('moment-timezone');

Template._checkoutForm.onCreated(function () {
	var self = this;
	self.currentOrg = Orgs.current();
	self.showEarlyCheckOutReason = new ReactiveVar(false);
	self.timeCertified = new ReactiveVar(false);
	self.showAlert = new ReactiveVar(false);
	self.alertText = new ReactiveVar("");
	self.checkoutPerson = new ReactiveVar({});

	Meteor.callAsync("getPeopleById", { _id: this.data._id }).then((person) => {
		self.checkoutPerson.set(person);
	}).catch((error) => {
		console.error('Error fetching person by ID:', error);
	});
	Meteor.subscribe("thePerson", { personById: this.data._id });

});

Template._checkoutForm.onRendered(function() {
	if (this.currentOrg.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
		const personId = this.data._id;
		const org = this.currentOrg;
		const timezone = org.getTimezone();
		const selectedTime = moment.tz(document.getElementById('checkout-time').value, "HH:mm", timezone).valueOf();
		this.showEarlyCheckOutReason.set(MomentUtils.isEarlyOrLateCheckInCheckOut(personId, org, timezone, selectedTime, LateCheckInEarlyDropOffModes.EARLY_PICK_UP));
	}
});

Template._checkoutForm.events({
	'click #checkout-close-alert': function (event, template) {
		template.showAlert.set(false);
		template.alertText.set("");
	},
	'click #checkout-certify-time-btn': function (event, template) {
		template.timeCertified.set(true);
	},
	'click #checkOutFormSubmit': function (event, template) {
		event.preventDefault();
		var closeModalOnSave = template.data.closeModalOnSave;
		$("#checkOutFormSubmit").prop('disabled', true).prop("value", "Checking Out...");

		var timeCertified = template.timeCertified.get()
		var personId = template.data._id;
		const currentPerson = Template.instance().checkoutPerson.get();
		const org = Template.instance().currentOrg;
		var pickerValue = moment($("#checkout-time").val(), ["h:mm a", "HH:mm"]);
		var checkoutMood = $("#checkout-mood label.active").data("moodlevel") || null;
		var dateValue = moment($("#checkout-date").val(), "MM/DD/YYYY").format("MM/DD/YYYY");

		var checkOutData = {
			personId: personId,
			comments: $("#checkout-comments").val(),
			time: pickerValue.valueOf(),
			date: dateValue,
			prettyTime: pickerValue.format("h:mm a"),
			mood: checkoutMood,
			checkedOutById: $("#checkout-pickup-by").val()
		};
		if (org.hasCustomization("moments/checkout/showTransportation"))
			checkOutData.transportation = $("#checkout-transportation").val();

		if (org.hasCustomization("moments/checkin/showHealthCheck")) {
			var healthChecks = {};
			$(".btn-group-health-check label.active").each(function (i, b) {
				var healthtype = $(b).data("healthtype");
				var healthlevel = $(b).data("healthlevel");
				healthChecks[healthtype] = healthlevel;
			});
			checkOutData.healthChecks = healthChecks;
		}

		if (org.hasCustomization("moments/checkout/showStaffCertificationMessage") && currentPerson.type == "staff" && !timeCertified) {
			$("#checkOutFormSubmit").prop('disabled', false).prop("value", "Check Out");
			template.alertText.set("You must certify your time");
			template.showAlert.set(true);
			return;
		}

		const selectedReason = $('#early-reason').val();
		try {
			MomentUtils.saveEarlyLateReason(checkOutData, selectedReason, template.showEarlyCheckOutReason.get())
		} catch (e) {
			mpSwal.fire("Error", e.message, "error");
			$("#checkOutFormSubmit").prop('disabled', false).prop("value", "Check Out");
			return;
		}

    Meteor.callAsync('checkOut', checkOutData).then(res => {
			$("#checkOutFormSubmit").prop('disabled', false).prop("value","Check Out");
			mpSwal.fire("Success", "Checkout Saved", "success");
			if (closeModalOnSave) {
				hideModal(closeModalOnSave);
			}
			// $('#newCheckOutForm')[0].reset();
			// $("#checkOutForm").modal('hide');
		}).catch(error => {
			$("#checkOutFormSubmit").prop('disabled', false).prop("value","Check Out");
			template.alertText.set(error.reason);
			template.showAlert.set(true);
		});
      // if ( !Meteor.status().connected) {
      //   $('#newCheckOutForm')[0].reset();
      //   $("#checkOutForm").modal('hide');
      //   $("#checkOutFormSubmit").prop('disabled', false).prop("value","Check Out");
      // }
		
	},
	'click .btn-group-health-check button': function (event) {
		event.preventDefault();
		var selected = $(event.currentTarget);
		$(selected).siblings().removeClass('active');
		$(selected).addClass("active");
	},

	'change #checkout-time': function(event, instance) {
		if (instance.currentOrg.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
			const personId = instance.data._id;
			const org = instance.currentOrg;
			const timezone = org.getTimezone();
			const selectedTime = moment.tz(event.target.value, "HH:mm", timezone).valueOf();
			instance.showEarlyCheckOutReason.set(MomentUtils.isEarlyOrLateCheckInCheckOut(personId, org, timezone, selectedTime, LateCheckInEarlyDropOffModes.EARLY_PICK_UP));
		}
	}
});

Template._checkoutForm.onCreated(function () {
	this.moodLevel = new ReactiveVar("");
});

Template._checkoutForm.helpers({
	"hideSave": function () {
		var hideSave = Template.instance().data.hideSave;
		return (hideSave) ? "hidden" : "";
	},
	'getShowAlert': function () {
		return Template.instance().showAlert.get();
	},
	'getActiveAlertText': function () {
		return Template.instance().alertText.get();
	},
	'getTimeCertified': function () {
		return Template.instance().timeCertified.get();
	},
	'currentTime': function () {
		const timezone = Template.instance().currentOrg.getTimezone()
		return new moment.tz(timezone).format("HH:mm");
	},
	'person': function () {
		return Template.instance().checkoutPerson.get();
	},
	'dateFormatted': function () {
		var curDate = new moment().format('MM/DD/YYYY');
		return curDate;
	},
	'checkedOutByPeople': function () {
		var currentPerson = new Person( Template.instance().checkoutPerson.get() );
		if (currentPerson) {
			var relationships = currentPerson.findOwnedRelationships();
			return relationships;
		}
	},
	'availableHealthCheckTypes': function () {
		if (Orgs.current().hasCustomization("moments/checkin/showHealthCheck"))
			return Orgs.current().availableCheckInHealthCheckTypes();
	},
	'allowChangeTime': function () {
		var person = Template.instance().checkoutPerson.get();
		if (person && person.type == "staff" && Orgs.current().hasCustomization("moments/checkin/staffLockdown"))
			return false;
		else
			return true;
	},
	'showStaffCertificationMessage': function () {
		const currentPerson = Template.instance().checkoutPerson.get();
		return (Template.instance().currentOrg.hasCustomization("moments/checkout/showStaffCertificationMessage") && currentPerson.type == "staff")
	},

	'showEarlyCheckOutReason': function() {
		return Template.instance().showEarlyCheckOutReason.get();
	},

	'earlyCheckOutReasons': function() {
		return Template.instance().currentOrg.pickDropReasons
	}
});

Template._checkoutForm.rendered = function () {
	$("#checkout-date").datepicker({ autoclose: true, todayHighlight: true })
};
