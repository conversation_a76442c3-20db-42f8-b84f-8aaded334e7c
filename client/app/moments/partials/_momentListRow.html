<template name="_momentListRow">
  <div class="card card-custom gutter-b" data-id="{{_id}}">
		<div class="card-body">
			<!--begin::Top-->
			<div class="d-flex">
        <div class="flex-shrink-0 mr-7">
          {{#if trueIfEq avatarUrl ""}}
              <div class="d-flex avatar-circle align-items-center justify-content-center"
                   style="background-color:{{getAvatarBackground taggedPersonInitials}}">
                  <span data-cy="person-initials-moment" class="initials">{{taggedPersonInitials}}</span>
              </div>
          {{else}}
            <div class="people-list-user-img" style="background-image:url({{avatarUrl}})"></div>
          {{/if}}
        </div>
        <div class="flex-grow-1">
          <!--begin::Title-->
          <div class="d-flex align-items-center justify-content-between flex-wrap">
            <!--begin::User-->
            <div class="d-flex flex-column flex-grow-1">
              <div class="d-flex align-items-center">
                <!--begin::Name-->
                <div class="d-flex flex-grow-1 align-items-center">
                  <span class="icon-2x mr-2 text-bright-blue fad {{iconForType momentType}}"></span>
                  <span data-cy="moment-type-name" class="align-items-center text-dark font-size-h5 font-weight-bold mr-3">{{ momentTypePrettyTranslation }}</span>
                </div>
                <!--end::Name-->
              </div>
              <!--begin::Contacts-->
              {{#if getAttributionName}}
                <div class="d-flex flex-wrap">
                  <span data-cy="posted-by-moment" class="text-muted font-weight-bold">Posted By: {{getAttributionName}}</span>
                </div>
              {{/if}}
              <div class="d-flex flex-column flex-wrap">
                {{#if peopleDescription}}
                  <span data-cy="people-description-moment" class="text-muted font-weight-bold mt-2">{{{ peopleDescription }}}</span>
                {{/if}}
                <span data-cy="moment-date-time" class="text-muted font-weight-bold mt-2">{{timeDescription}}</span>
              </div>
            </div>
            <!--begin::User-->
          </div>
          <!--end::Title-->
          <!--begin::Content-->
          <!--end::Content-->
        </div>
        <div class="flex-shrink-0 ml-7">
          {{#if isMomentEditable}}
            <div class="dropdown" id="moment-list-dropdown">
              <div data-cy="moment-list-dropdown" class="btn btn-icon btn-clean" data-toggle="dropdown" >
                  <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
              </div>
              <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                <span data-cy="modify-moment" class="dropdown-item clickable-row" id="moment-modify">Modify Moment</span>
                <span data-cy="delete-moment" class="dropdown-item clickable-row" id="moment-delete">Delete Moment</span>
                <span data-cy="view-moment-data" class="dropdown-item clickable-row" id="moment-data">View Moment Data</span>
              </div>
            </div>
          {{/if}}
        </div>
      </div>
      {{#if hasMedia}}
        <div class="d-flex flex-column mt-4">
          {{#each attachedMedia}}
          {{#if isMedia}}
              <a href="#" class="showMediaLink {{#if trueIfEq isVideo true}}video{{/if}}" data-id="{{../_id}}" data-mediatoken="{{mediaToken}}">
                <img src="{{getTimelinePhoto}}" class="img-responsive timelinePhoto mt-4" alt="Photo" style="display:none;border-radius:16px;" onload="showme(this);" onerror="imgError(this);" data-id="{{../_id}}" data-hasvideo="{{isVideo}}">
              </a>
              
              <i class="icon-2x fad fa-spinner fa-spin timelinePhotoSpinner"></i>
            {{else}}
              <span style="font-weight-bolder mt-2">
              <b><i class="text-bright-blue fad fa-paperclip mr-2"></i> Attachment:</b> <a href="{{getTimelinePhoto}}" target="_blank">{{#if fileName}}{{fileName}}{{else}}View{{/if}}</a><br/>
              </span>
            {{/if}}
          {{/each}}
        </div>
      {{/if}}
      <div data-cy="moment-description" class="d-flex flex-column mt-4">
        <p class="text-muted">
          {{> Template.dynamic template=momentDisplayTemplate}}
          {{{formattedDesc}}}
          <span class="text-muted">{{ comment }}</span>
        </p>
      </div>
      <div class="d-flex flex-column mt-4">
        {{#if reactionData}}
          {{#each reaction in reactionData}}
            <div class="reaction-item {{#if reaction.nameList}}reaction-item-block{{/if}}">
              <img src="/img/{{reaction.icon}}"> {{reaction.count}} {{#if reaction.nameList}}- {{reaction.nameList}} {{/if}}
            </div>
          {{/each}}  
        {{/if}}
        {{#if showLikeButton}}
          <div class="d-flex flex-row" style="cursor:pointer;">
            <a class="link-black likeMoment open-reaction-buttons" data-id="{{_id}}" >
                <i class="fad fa-plus mr-4"></i>Reaction
            </a>
          </div>
          <div class="reaction-buttons" data-id="{{_id}}">
              <a href="#" class="link-black likeMoment sendLike" data-id="{{_id}}" data-type="smile">
                <img src="/img/thumb-up-2.png"> 
              </a>
              <a href="#" class="link-black likeMoment sendLike" data-id="{{_id}}"  data-type="laugh">
                <img src="/img/big-grin.png"> 
              </a>
              <a href="#" class="link-black likeMoment sendLike" data-id="{{_id}}"  data-type="wink">
                <img src="/img/upside-down-face.png"> 
              </a>
              <a href="#" class="link-black likeMoment sendLike" data-id="{{_id}}"  data-type="meh">
                <img src="/img/frowning-face.png"> 
              </a>
          </div>
        {{/if}}
      </div>
    </div>
  </div>
</template>

<template name="checkoutMomentDisplay">
  {{#if notEmptyString mood}}
    <div class="d-flex align-items-center">
      <span class="text-muted font-weight-bolder min-w-60px">Mood:</span>
      <i class="icon-2x fad {{moodIcon}} text-bright-blue"></i>
    </div>
  {{/if}}
  {{#if notEmptyString checkOutTransportation}}
    <div class="d-flex align-items-center mt-2">
      <span class="text-muted font-weight-bolder mr-2">Transportation: {{checkOutTransportation}}</span>
    </div>
  {{/if}}
  {{#if checkOutHealthChecks}}
    <div class="d-flex flex-column mt-2">
      <span class="text-muted font-weight-bolder mr-2">Health Checks:</span>
      {{#each extractCheckOutHealthChecks}}
        <div class="d-flex align-items-center flex-row mb-2">
          <span class="text-muted min-w-60px">{{checkName}}:</span>
          <i class="icon-2x fad {{checkIcon}} text-bright-blue"></i>
        </div>
      {{/each}}
    </div>
  {{/if}}
</template>

<template name="checkinMomentDisplay">
  {{#if notEmptyString checkInTransportation}}
    <div class="d-flex align-items-center mt-2">
      <span class="text-muted font-weight-bolder mr-2">Arrived by: {{checkInTransportation}}</span>
    </div>
  {{/if}}
  {{#if checkInHealthChecks}}
    <div class="d-flex flex-column mt-2">
      <span class="text-muted font-weight-bolder mr-2">Health Checks:</span>
      {{#each extractCheckInHealthChecks}}
        <div class="d-flex align-items-center flex-row mb-2">
          <span class="text-muted min-w-60px">{{checkName}}:</span>
          <i class="icon-2x fad {{checkIcon}} text-bright-blue"></i>
        </div>
      {{/each}}
    </div>
  {{/if}}
  {{#if checkInHealthCheckCovid19Symptoms}}
    <div class="d-flex flex-column mt-2">
      <span class="text-muted font-weight-bolder">COVID-19 Health Check:</span>
      <span class="text-muted">Symptoms: {{checkInHealthCheckCovid19Symptoms}}</span>
      <span class="text-muted">Contact: {{checkInHealthCheckCovid19ProlongedContact}}</span>
      <span class="text-muted">Temperature over 100.4: {{checkInHealthCheckCovid19TemperatureAboveLimit}}</span>
      <span class="text-muted">Temperature: {{checkInHealthCheckCovid19Temperature}}</span>
    </div>
  {{/if}}
  {{#each pinCodeFormFields}}
    <div class="d-flex flex-column mt-1">
      <span class="text-muted">{{fieldLabel}}: {{fieldValue}}</span>
    </div>
  {{/each}}
</template>
