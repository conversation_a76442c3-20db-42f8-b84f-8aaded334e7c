<template name="paymentStatements">
  <div class="d-flex flex-column container">
    <h2>Your Payment Statements</h2>
    <div class="card-body bg-white border-lg">
      <div class="form-group row">
        <label for="user-memberships" class="col-2 col-form-label">Select Community</label>
        {{#unless loading}}
          <div class="col-6">
            <select class="form-control" id="user-memberships">
              {{#each value in getMemberships}}
                <option value="{{value.orgId}}">{{value.orgName}}</option>
              {{/each}}
            </select>
          </div>
        {{/unless}}
      </div>
      <div class="form-group row">
        <button class="btn btn-primary font-weight-bolder btnPaymentStatement">Payment Statement</button>
      </div>
    </div>
  </div>
</template>
