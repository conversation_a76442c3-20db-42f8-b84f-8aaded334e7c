import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './paymentStatements.html';
import '../people/_billingTab.html';
import '../people/_billingTab.js';
import { showModal } from '../main';

Template.paymentStatements.onCreated(function() {
  let self = this;
  self.memberships = new ReactiveVar([]);
  self.loading = new ReactiveVar(true);

  Meteor.callAsync('getAllOrgMembershipsForUser')
  .then((res)=>{
    self.memberships.set(res);
    self.loading.set(false)
  })
  .catch((err)=>{
    mpSwal.fire("Error", err.reason, "error");
  });
});

Template.paymentStatements.helpers({
  'getMemberships': function() {
    return Template.instance().memberships.get();
  }
});

Template.paymentStatements.events({
  'click .btnPaymentStatement': function(e, i) {
    e.preventDefault();

    const orgId = $("#user-memberships").val();
    const memberships = i.memberships.get();

    const selectedMembership = _.find(memberships, (m) => m.orgId == orgId);
    showModal("personBillingYearEndStatement", { ...selectedMembership, deactivatedUser: true })
  }
})
