<template name="_campaignMessageFormModal">
  <div id="_campaignMessageFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">New Message</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmCampaignMessage">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">To</label>
              <div class="col-lg-9 col-xl-6">
                {{#if selectedPerson}}
                  <label class="text-left col-form-label">Family of {{selectedPerson.firstName}} {{selectedPerson.lastName}}</label>
                {{else}}
                  <select class="form-control form-control-lg form-control-solid" name="recipients" multiple>

                  </select>
                {{/if}}
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Method</label>
              <div class="col-lg-9 col-xl-6">
                <div class="radio-inline">
                  <label class="radio">
                    <input type="radio" name="optionsMethod" value="email" checked/>
                    <span></span>
                    Email
                  </label>
                  <label class="radio">
                    <input type="radio" name="optionsMethod" value="sms" />
                    <span></span>
                    SMS (text)
                  </label>
                </div>
              </div>
            </div>
            <div class="form-group row" id="campaignSubjectRow">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Subject</label>
              <div class="col-lg-9 col-xl-6">
                <input type="text" class="form-control form-control-lg form-control-solid" name="subject" />
              </div>
            </div>
            <div class="form-group row" id="campaignTextEditorRow">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Message</label>
              <div class="col-lg-9 col-xl-6">
                <div id="messageEditor" style="height:200px">
								</div>
              </div>
            </div>
            <div class="form-group row" id="campaignSmsEditorRow" style="display:none">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Message</label>
              <div class="col-lg-9 col-xl-6">
                <textarea class="form-control form-control-lg form-control-solid" name="smsMessage"></textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <div class="btn btn-primary font-weight-bolder mr-2" id="btnSend">Send</div>
          <div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</div>
        </div>
  		</div>
  	</div>
  </div>
</template>
