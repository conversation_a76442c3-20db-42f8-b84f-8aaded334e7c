import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService";
import { Person } from "../../../lib/collections/people";
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs.js';
import { Groups } from "../../../lib/collections/groups.js";
import _ from '../../../lib/util/underscore';
import { showModal } from "../main";
import '../people/_addPersonModal.js';
import './inquiries.html';
import { offlinePreventCheck } from "../../../mpweb.js";

Template.inquiries.onCreated(function() {
	this.filterInactive = new ReactiveVar(Session.get("inquiryFilterInactive") || false);
	this.filterFields = new ReactiveDict(null, Session.get("inquiryFilterDict") || {} );
	this.sortField = new ReactiveVar("");
	this.sortDirection = new ReactiveVar("asc");
	this.peopleProspectList = new ReactiveVar([]);


	this.autorun(() => {
		var conditions = [];
		conditions.push( {type: "prospect"} );
		var filterInactive = Template.instance().filterInactive.get();
		if (!filterInactive) 
			conditions.push({inActive: {$ne: true}});
		
		var query = (conditions.length > 0) ? {$and: conditions} : {};
		var sortField = Template.instance().sortField.get(), sortDirection = Template.instance().sortDirection.get();
		var sort = {};
		if (sortField == "firstName")
			sort = {firstName: sortDirection == "asc" ? 1 : -1, lastName: 1};
		else if (sortField == "lastName")
			sort = {lastName: sortDirection == "asc" ? 1 : -1, firstName: 1};
		else if (sortField == "age" || sortField == "birthday") {
			const sortKey = "profileData.birthday";
			sort = {}; sort[sortKey] = sortDirection == "asc" ? 1 : -1;
		} else if (sortField != "") {
			const sortKey = "profileData." + sortField;
			sort = {}; sort[sortKey] = sortDirection == "asc" ? 1 : -1;
		}
		const dateSortKey = "profileData.registrationSubmissionDate";
		if (!sort[dateSortKey]) sort[dateSortKey] = -1;

		getPeopleData(query, {sort: sort}).then(peopleRes => {
			const tempPeople = peopleRes.map((item, index) => {
				item.rank = index + 1;
				return new Person(item); // Convert to collection document to get access to Person extended methods.
			});
			this.peopleProspectList.set(tempPeople)
		}).catch(err => {
			console.log(err);
		});
		
		
	})
});

Template.inquiries.rendered = function() {
	$("#inputStartDateBegin").datepicker({ autoclose: true, todayHighlight: true });
	$("#inputStartDateEnd").datepicker({ autoclose: true, todayHighlight: true });
}

Template.inquiries.helpers({
	"getFilterCount": function() {
		const filterFields = Template.instance().filterFields.all();
		const filterInactive = Template.instance().filterInactive.get();
		let count = 0;
		for (const f in filterFields) {
			if (filterFields[f]) ++count;
		};
		if (filterInactive) ++count;
		if (count > 0) return count;
		return false;
	},
	'prospects': function() {
		// var conditions = [];
		// conditions.push( {type: "prospect"} );
		// var filterInactive = Template.instance().filterInactive.get();
		// if (!filterInactive) 
		// 	conditions.push({inActive: {$ne: true}});
		
		// var query = (conditions.length > 0) ? {$and: conditions} : {};
		// var sortField = Template.instance().sortField.get(), sortDirection = Template.instance().sortDirection.get();
		// var sort = {};
		// if (sortField == "firstName")
		// 	sort = {firstName: sortDirection == "asc" ? 1 : -1, lastName: 1};
		// else if (sortField == "lastName")
		// 	sort = {lastName: sortDirection == "asc" ? 1 : -1, firstName: 1};
		// else if (sortField == "age" || sortField == "birthday") {
		// 	const sortKey = "profileData.birthday";
		// 	sort = {}; sort[sortKey] = sortDirection == "asc" ? 1 : -1;
		// } else if (sortField != "") {
		// 	const sortKey = "profileData." + sortField;
		// 	sort = {}; sort[sortKey] = sortDirection == "asc" ? 1 : -1;
		// }
		// const dateSortKey = "profileData.registrationSubmissionDate";
		// if (!sort[dateSortKey]) sort[dateSortKey] = -1;
		
		// let results = People.find(query,{sort: sort}).map(function (person, index) {
		// 	person.rank = index + 1;
		// 	return person;
		// });

		const results =  Template.instance().peopleProspectList.get()

		const startDateBegin = Template.instance().filterFields.get("startDateBegin"),
			startDateEnd = Template.instance().filterFields.get("startDateEnd"),
			ageBegin = Template.instance().filterFields.get("ageBegin"),
			ageEnd = Template.instance().filterFields.get("ageEnd"),
			centerPreference = Template.instance().filterFields.get("centerPreference"),
			designatedGroupId = Template.instance().filterFields.get("designatedGroup"),
			sibling = Template.instance().filterFields.get("sibling"),
			employee = Template.instance().filterFields.get("employee"),
			onmywayprek = Template.instance().filterFields.get("onmywayprek"),
			householdSize = Template.instance().filterFields.get("householdSize"),
			incomeBegin = Template.instance().filterFields.get("incomeBegin"),
			incomeEnd = Template.instance().filterFields.get("incomeEnd");

		return _.filter(results, (r) => {
			const prefix = r.profileData ? "profileData." : "",
				startDate = _.deep(r, prefix + "desiredStartDate"),
				birthdayMoment = _.deep(r, prefix + "birthday") && new moment(_.deep(r, prefix + "birthday")), 
				age = birthdayMoment && birthdayMoment.isValid() && moment().diff(birthdayMoment, 'years', true),
				income = parseInt(_.deep(r, prefix + "householdIncome")),
				rCenterPreference = _.deep(r, prefix + "centerPreference"),
				rdesignatedGroupId = _.deep(r, prefix + "designatedGroupId"),
				rHasSibling = _.deep(r, prefix + "hasSibling"),
				rEmployee = _.deep(r, prefix + "employee"),
				rOnmywayprek = _.deep(r, prefix + "onmywayprekEligible"),
				rHouseholdSize = _.deep(r, prefix + "householdSize");
				
			return (!startDateBegin || (startDate && new moment(startDate).valueOf() >= startDateBegin )) &&
				(!startDateEnd || (startDate && new moment(startDate).valueOf() <= startDateEnd )) &&
				(!ageBegin || (age && age >= ageBegin)) &&
				(!ageEnd || (age && age <= ageEnd)) &&
				(!centerPreference || (rCenterPreference && rCenterPreference == centerPreference)) &&
				(!designatedGroupId || (rdesignatedGroupId && rdesignatedGroupId == designatedGroupId)) &&
				(!sibling || (rHasSibling && rHasSibling == sibling)) &&
				(!employee || (rEmployee && rEmployee == employee)) &&
				(!onmywayprek || (rOnmywayprek && rOnmywayprek == onmywayprek)) &&
				(!incomeBegin || (income && income >=incomeBegin)) &&
				(!incomeEnd || (income && income <=incomeEnd)) &&
				(householdSize==null || (rHouseholdSize && rHouseholdSize == householdSize));
		});
	},
	'canAddPerson': function() {
		const currentUser = Meteor.user(), currentPerson = currentUser && currentUser.fetchPerson();
		if (currentPerson)
			return (currentPerson.type == "admin");
	},
	'sortIcon': function(fieldName) {
		var sortField = Template.instance().sortField.get();
		var sortDirection = Template.instance().sortDirection.get();
		if (sortField == fieldName && sortDirection == "asc")
			return "fa-sort-alpha-asc";
		else if (sortField == fieldName && sortDirection == "desc")
			return "fa-sort-alpha-desc";
		else
			return "fa-sort";
	},
	'lastInteractionDateFormatted': function() {
		return (this.lastInteractionDate || this.createdAt) ? moment(this.lastInteractionDate || this.createdAt).format("MM/DD/YYYY") : "";
	},
	'showAdditionalInquiryFields' : function() {
		return (Orgs.current() && Orgs.current().hasCustomization("inquiries/registration/enabled"));
	},
	'calcAge': function() {
		if (this.profileData && this.profileData.birthday) {
			const birthdayMoment = new moment(this.profileData.birthday);
			if (birthdayMoment.isValid())
				return moment().diff(birthdayMoment, 'years', true).toFixed(1);
		}
	},
	'profileFieldValue': function(path) {
		const self = this;
		let key = (self.profileData ? "profileData" : "" ) + "." + path.replace("/", ".");
		
		let result = _.deep(self, key);
		if (key.endsWith("Date") && result) {
			const momentValue = new moment(result);
			if (momentValue.isValid())
				return momentValue.format("M/DD/YYYY");
		} 
		
		return result;
	},
	'availableLocations': function() {
		const pfs = Orgs.current() && Orgs.current().valueOverrides && Orgs.current().valueOverrides.inquiryProfileFields,
			centers = pfs && _.find( pfs,  (pf) => { return pf.name == 'centerPreference';});
		if (centers) return centers.values;
	},
	'groups': function() {
		return Groups.find({}, {sort:{name:1}});
	},
	'filterFieldValue': function(fieldLabel) {
		const val = Template.instance().filterFields.get(fieldLabel);
		
		if (val && (fieldLabel == "startDateBegin" || fieldLabel == "startDateEnd") )
			return new moment(val).format("MM/DD/YYYY");
		else 
			return val;
	},
	'activeFilterCount': function() {
		return   _.filter( Template.instance().filterFields.all(), (v,k) => v).length;
	}
});

Template.inquiries.events({
	'click #newProspectLink': function(event, template) {
		if (offlinePreventCheck()) return false;
		Session.set("currentEditPersonId", "");
		Session.set("editPersonModalPersonType", "prospect");
    showModal("_addPersonModal", {}, "#_addPersonModal");
	},
  "change #filterInactive": function(event, template) {
  	template.filterInactive.set( $("#filterInactive").prop('checked'));
  },
  "click .applySort": function(event, template) {
  	var fieldName = $(event.target).data("id");
  	if (fieldName != template.sortField.get()) {
  		template.sortField.set(fieldName);
  		template.sortDirection.set("asc");
  	} else {
  		template.sortDirection.set( template.sortDirection.get() == "asc" ? "desc" : "asc");
  	}
	},
	"click #btnUpdate": function(event, instance) {
		const startDateBegin = $("#inputStartDateBegin").val(), startDateEnd = $("#inputStartDateEnd").val(),
			startDateBeginMoment = startDateBegin && new moment(startDateBegin),
			startDateEndMoment = startDateBegin && new moment(startDateEnd);
		if (startDateBeginMoment && startDateBeginMoment.isValid())
			instance.filterFields.set("startDateBegin", startDateBeginMoment.valueOf());
		else
			instance.filterFields.set("startDateBegin", null);

		if (startDateEndMoment && startDateEndMoment.isValid())
			instance.filterFields.set("startDateEnd", startDateEndMoment.valueOf());
		else
			instance.filterFields.set("startDateEnd", null);

		const ageBegin = $("#inputAgeBegin").val(), ageEnd = $("#inputAgeEnd").val();
		instance.filterFields.set("ageBegin", ageBegin ? ageBegin : null);
		instance.filterFields.set("ageEnd", ageEnd ? ageEnd : null);

		const centerPreference = $("#inputCenterPreference").val();
		instance.filterFields.set("centerPreference", centerPreference ? centerPreference : null);

		const group = $("#inputGroup").val();
		instance.filterFields.set("designatedGroup", group ? group : null);

		const sibling = $("#inputSibling").val();
		instance.filterFields.set("sibling", sibling ? sibling : null);

		const employee = $("#inputEmployee").val();
		instance.filterFields.set("employee", employee ? employee : null);

		const householdSize = $("#inputHouseholdSize").val();
		instance.filterFields.set("householdSize", householdSize ? parseInt(householdSize) : null);

		const onmywayprek = $("#inputOnmywayprek").val();
		instance.filterFields.set("onmywayprek", onmywayprek ? onmywayprek : null);

		const incomeBegin = $("#inputIncomeBegin").val(), incomeEnd = $("#inputIncomeEnd").val();
		instance.filterFields.set("incomeBegin", incomeBegin ? incomeBegin : null);
		instance.filterFields.set("incomeEnd", incomeEnd ? incomeEnd : null);

		Session.set("inquiryFilterDict", instance.filterFields.all());
	},
	"click #btnResetFilters": function(event, instance) {
		instance.filterFields.clear();
    Session.set("inquiryFilterDict", {});
	}
});
