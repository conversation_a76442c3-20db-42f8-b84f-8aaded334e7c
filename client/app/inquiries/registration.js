import { <PERSON><PERSON>outer } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './registration.html';

Template.registration.onCreated( function()  {
	this.registrationComplete = new ReactiveVar(false);
});
Template.registration.helpers({
	registrationComplete() { return Template.instance().registrationComplete.get(); },
	allowedParents() { return [1,2]; },
	allowedChildren() { return [1,2,3,4];},
	days() { return [
		{id: "mon", name:"Monday"},
		{id: "tue", name:"Tuesday"},
		{id: "wed", name:"Wednesday"},
		{id: "thu", name:"Thursday"},
		{id: "fri", name:"Friday"}
	];},
	dayHours() { return [
		{value:"6", label:"6 AM"},
		{value:"7", label:"7 AM"},
		{value:"8", label:"8 AM"},
		{value:"9", label:"9 AM"},
		{value:"10", label:"10 AM"},
		{value:"11", label:"11 AM"},
		{value:"12", label:"12 PM"},
		{value:"13", label:"1 PM"},
		{value:"14", label:"2 PM"},
		{value:"15", label:"3 PM"},
		{value:"16", label:"4 PM"},
		{value:"17", label:"5 PM"},
		{value:"18", label:"6 PM"},
	];}
});
Template.registration.events({
	"submit #registrationForm": function(event, instance) {
		event.preventDefault();
		const orgId = FlowRouter.getQueryParam("orgId") || "";
		const formFields = $( "#registrationForm" ).serializeArray();
		Meteor.callAsync('submitRegistration', {
			orgId: orgId,
			formFields: formFields
		}).then((result) => {
			instance.registrationComplete.set(true);
		}).catch((error) => {
			mpSwal.fire({
				title: "Problem with registration", 
				html: "<span style='white-space:pre-wrap'>" + error.reason + "</span>"
			});
		});
	}
})
