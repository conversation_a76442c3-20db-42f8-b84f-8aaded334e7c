<template name="registrationBrightside">
<div class="container">
	<div class="row">
		<div class="col-sm-offset-1 col-md-offset-2 col-sm-10 col-md-8 col-xs-12" style="background-color:#fff">
				<br/>
				<div style="text-align:center">
					<img src="https://assets.momentpath.com/customers/brightside_logo.jpg" style="width:250px">
					<h1>Family Registration</h1>
				</div>
				<br/>

				{{#unless registrationComplete}}
				<form id="registrationForm">
					
					<label>Center Preference (please choose):</label><br/>
					<select name="center_preference" class="form-control">
						<option value="">First Available</option>
						<option value="East Liberty">East Liberty</option>
						<option value="Squirrel Hill">Squirrel Hill</option>
						<option value="925 Liberty">925 Liberty</option>
						<option value="415 Smithfield">415 Smithfield</option>
						<option value="4 Smithfield">4 Smithfield</option>
						<option value="McKeesport">McKeesport</option>
						<option value="McKees Rocks">McKees Rocks</option>
						<option value="North Versailles">North Versailles</option>
						<option value="Jeannette">Jeannette</option>
					</select>
					<label>How did you hear about us?</label><br/>
					<select name="referral_source" class="form-control">
						<option value="Family">Family</option>
						<option value="Friend">Friend</option>
						<option value="Coworker">Coworker</option>
						<option value="Website">Website</option>
						<option value="Referral">Referral</option>
						<option value="Other">Other</option>
					</select>
					<label>If other, where did you hear about us?</label><br/>
					<input type="text" name="other_referral_source" class="form-control"/>
					
					{{#each childNum in allowedChildren}}
					<hr/>
					<label>Child {{childNum}} Information:</label><br/>

					<div class="row">
						<div class="col-xs-6">
							<label>First name:</label> <br/>
							<input type="text" name="child_{{childNum}}_first_name" class="form-control"> 
						</div>
						<div class="col-xs-6">
							<label>Last name: </label><br/>
							<input type="text" name="child_{{childNum}}_last_name" class="form-control">
						</div>
					</div>
						
					<div class="row">
						<div class="col-xs-6">
							<label>Birthday or Due Date:</label>
							<input type="date" name="child_{{childNum}}_dob" class="form-control">
						</div>
						<div class="col-xs-6">
							<label>Desired start date:</label>
							<input type="date" name="child_{{childNum}}_start_date" class="form-control">
						</div>
					</div>
					{{/each}}

					{{#each parentNum in allowedParents}}
					<hr/>
					<label>Parent/Guardian {{parentNum}} Information:</label><br/>
			
					<div class="row">
						<div class="col-xs-6">
							<label>First name:</label> <br/>
							<input type="text" name="parent_{{parentNum}}_first_name" class="form-control"> 
						</div>
						<div class="col-xs-6">
							<label>Last name: </label><br/>
							<input type="text" name="parent_{{parentNum}}_last_name" class="form-control">
						</div>
					</div>
						
					<div class="row">
						<div class="col-xs-6">
							<label>Date of birth:</label>
							<input type="date" name="parent_{{parentNum}}_dob" class="form-control">
						</div>
					</div>

					<div class="row">
						<div class="col-xs-12">
							<label>Address:</label>
							<input type="text" name="parent_{{parentNum}}_address" class="form-control">
						</div>
					</div>
					<div class="row">
						<div class="col-sm-5">
							<label>City:</label><br/>
							<input type="text" name="parent_{{parentNum}}_city" class="form-control">
						</div>
						<div class="col-sm-3">
							<label>State:</label><br/>
							<input type="text" name="parent_{{parentNum}}_state" class="form-control">
						</div>
						<div class="col-sm-4">
							<label>Zipcode:</label>
							<input type="text" name="parent_{{parentNum}}_zipcode" class="form-control">
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
							<label>Phone:</label>
							<input type="text" name="parent_{{parentNum}}_phone" class="form-control" >
						</div>
						<div class="col-sm-6">
							<label>Email:</label>
							<input type="text" name="parent_{{parentNum}}_email" class="form-control">
						</div>
					</div>
					{{/each}}
					<hr/>
					<label>What is your preferred contact method?</label><br/>
					<select name="contact_method" class="form-control">
						<option value="Email">Email</option>
						<option value="Phone call">Phone call</option>
						<option value="Text message">Text message</option>
					</select>
					<label>Is your child a sibling of a Brightside Academy student?</label><br/>
					<select name="sibling" class="form-control">
						<option value="No">No</option>
						<option value="Yes">Yes</option>
					</select>
					<label>Are you currently an employee of Brightside Academy?</label><br/>
					<select name="employee" class="form-control">
						<option value="No">No</option>
						<option value="Yes">Yes</option>
					</select>
					<label>Are you eligible for On My Way PreK?</label><br/>
					<select name="onmywayprek_eligible" class="form-control">
						<option value="No">No</option>
						<option value="Yes">Yes</option>
					</select>
					<label>Schedule:</label><br/>
					<select name="schedule" class="form-control">
						<option value=""></option>
						<option value="Part-time">Part-time</option>
						<option value="Full-time">Full-time</option>
					</select>
					<!--
					<label>Request a tour?</label><br/>
					<select name="tour_request" class="form-control">
						<option value=""></option>
						<option value="Yes">Yes</option>
						<option value="No">No</option>
					</select>
					-->
					<label>Household Income (Weekly Gross $)</label>
					<input type="number" min="0.00" step="0.01"  name="household_income" class="form-control">
					<label>Family Size (# in household)</label>
					<select name="household_size" class="form-control">
						<option value="1">1</option>
						<option value="2">2</option>
						<option value="3">3</option>
						<option value="4">4</option>
						<option value="5">5</option>
						<option value="6">6</option>
						<option value="7">7</option>
						<option value="8">8</option>
						<option value="9">9</option>
						<option value="10">10</option>
					</select>

					<label>Additional Information</label>
					<textarea name="additional_information" class="form-control"></textarea>
					<br/>
					{{#if showScheduleSelections}}
					<label>Hours:</label><br/>
					<table id="hoursTable">
					{{#each day in days}}
						<tr>
							<td>{{day.name}}</td>
							<td><select name="day_{{day.id}}_start_hour" class="form-control">
								<option value=""></option>
								{{#each dayHour in dayHours}}
									<option value="{{dayHour.value}}">{{dayHour.label}}</option>
								{{/each}}
							</select></td>
							<td style="text-align:center"> to </td>
							<td><select name="day_{{day.id}}_end_hour" class="form-control">
								<option value=""></option>
								{{#each dayHour in dayHours}}
									<option value="{{dayHour.value}}">{{dayHour.label}}</option>
								{{/each}}
							</select></td>
						</tr>
					{{/each}}
					</table>
					{{/if}}
					<hr/>
					<div class="form-errors"></div>
					<div style="text-align:center">
						<p>By submitting this form, you acknowledge the terms and conditions.</p>
						<input type="submit" class="form-control btn btn-primary">
					</div>
					<br/>
					<br/>
				</form>
				{{else}}
				<div style="text-align:center">
					<h3>Registration submission completed</h3>
					<p>Your registration submission has been successfully received. We will contact you to confirm your registration status.</p>
				</div>
				{{/unless}}
		</div>
	</div>
</div>
</template>