<template name="inquiries">
  <div class="container d-flex flex-row justify-content-end mb-4">
    <div class="btn btn-primary font-weight-bolder btn-text-white {{#if canAddPerson}}mr-4{{/if}}" id="filterPeopleLink" data-toggle="collapse" data-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse" data-cy="filter-people">
      <i class="fad-regular fad fa-filter fa-swap-opacity mr-2" style="color:#fff"></i>Filter {{#if getFilterCount}}({{getFilterCount}}){{/if}}
    </div>
    {{#if canAddPerson}}
      <div class="btn btn-primary font-weight-bolder btn-text-white" id="newProspectLink" data-cy="new-prospect-link">
        <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add
      </div>
    {{/if}}
  </div>
  <div id="filterCollapse" class="container collapse mb-4">
    <div class="card card-custom gutter-b">
      <div class="card-body">
        <div class="d-flex flex-row justify-content-between">
          <div class="d-flex flex-column">
            <span class="font-weight-bold">Desired Start Date</span>
            <div class="mt-2">From</div>
            <input type="text" class="form-control form-control-solid" id="inputStartDateBegin" value={{filterFieldValue "startDateBegin"}} data-cy="start-date-begin">
            <div class="mt-2">To</div>
            <input type="text" class="form-control form-control-solid" id="inputStartDateEnd" value={{filterFieldValue "startDateEnd"}} data-cy="start-date-end">
          </div>
          <div class="d-flex flex-column">
            <span class="font-weight-bold">Age</span>
            <div class="mt-2">From</div>
            <input type="number" class="form-control form-control-solid" id="inputAgeBegin" value={{filterFieldValue "ageBegin"}} data-cy="age-begin">
            <div class="mt-2">To</div>
            <input type="number" class="form-control form-control-solid" id="inputAgeEnd" value={{filterFieldValue "ageEnd"}} data-cy="age-end">
          </div>
          <div class="d-flex flex-column">
            <span class="font-weight-bold">Household Income</span>
            <div class="mt-2">From</div>
            <input type="number" class="form-control form-control-solid" id="inputIncomeBegin" value={{filterFieldValue "incomeBegin"}} data-cy="income-begin">
            <div class="mt-2">To</div>
            <input type="number" class="form-control form-control-solid" id="inputIncomeEnd" value={{filterFieldValue "incomeEnd"}} data-cy="income-end">
          </div>
          <div class="d-flex flex-column">
            {{#if availableLocations}}
              <span class="font-weight-bold">Center Preference</span>
              <select name="center_preference" id="inputCenterPreference" class="form-control form-control-solid mb-4" data-cy="center-preference">
                <option value=""></option>
                {{#each option in availableLocations}}
                  <option value="{{option}}" {{selectedIfEqual (filterFieldValue "centerPreference") option}} >{{option}}</option>
                {{/each}}
              </select>
            {{/if}}
            <span class="font-weight-bold">Group</span>
            <select name="group" id="inputGroup" class="form-control form-control-solid mb-4" data-cy="group">
              <option value=""></option>
              {{#each group in groups}}
                <option value="{{group._id}}" {{selectedIfEqual (filterFieldValue "designatedGroup") group._id}} >{{group.name}}</option>
              {{/each}}
            </select>
            <span class="font-weight-bold">Household Size</span>
            <select name="household_size" id="inputHouseholdSize" class="form-control form-control-solid mb-4" data-cy="house-hold-size">
              <option value="" {{selectedIfEqual (filterFieldValue "householdSize") ""}}></option>
              {{#each number in (numbersBetween 0 10)}}
                <option value="{{number}}" {{selectedIfEqual (filterFieldValue "householdSize") number}}>{{number}}</option>
              {{/each}}
            </select>
            <div class="checkbox-list">
              <label class="checkbox checkbox-primary">
                <input type="checkbox" id="filterInactive" data-cy="filter-inactive">
                <span></span>
                Show inactive
              </label>
            </div>
          </div>
          <div class="d-flex flex-column">
            <span class="font-weight-bold">Sibling</span>
            <select name="sibling" id="inputSibling" class="form-control form-control-solid mb-4" data-cy="sibling">
              <option value=""></option>
              <option value="Yes" {{selectedIfEqual (filterFieldValue "sibling") "Yes"}} >Yes</option>
              <option value="No" {{selectedIfEqual (filterFieldValue "sibling") "No"}}>No</option>
            </select>
            <span class="font-weight-bold">Employee</span>
            <select name="employee" id="inputEmployee" class="form-control form-control-solid mb-4" data-cy="employee">
              <option value=""></option>
              <option value="Yes" {{selectedIfEqual (filterFieldValue "employee") "Yes"}}>Yes</option>
              <option value="No" {{selectedIfEqual (filterFieldValue "employee") "No"}}>No</option>
            </select>
            <span class="font-weight-bold">OnMyWayPreK Eligible</span>
            <select name="employee" id="inputOnmywayprek" class="form-control form-control-solid" data-cy="on-my-way-prek">
              <option value=""></option>
              <option value="Yes" {{selectedIfEqual (filterFieldValue "onmywayprek") "Yes"}}>Yes</option>
              <option value="No" {{selectedIfEqual (filterFieldValue "onmywayprek") "No"}}>No</option>
            </select>
          </div>
        </div>
        <div class="d-flex flex-row mt-6">
          <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-results">
            Update Results
          </div>
          <div class="btn btn-secondary font-weight-bolder ml-4" id="btnResetFilters" data-cy="reset-filters">
            Reset Filters
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="d-flex flex-column-fluid">
    <div class="container">
      <div class="card card-custom gutter-b px-10 py-10">
        <table class="table">
          <tbody>
            <tr>
  						<th style="width: 10px">#</th>
  						<th><a href="#" data-id="firstName" class="applySort">
  						<i class="fa {{sortIcon "firstName"}} pull-right" aria-hidden="true" data-id="firstName" data-cy="first-name-sort"></i>
  						</a>First Name</th>
  						<th><a href="#" data-id="lastName" class="applySort"><i class="fa {{sortIcon "lastName"}} pull-right" aria-hidden="true" data-id="lastName" data-cy="last-name-sort"></i></a>Last Name</th>
  						<th><a href="#" data-id="lastInteractionDate" class="applySort"><i class="fa {{sortIcon "lastInteractionDate"}} pull-right" aria-hidden="true" data-id="lastInteractionDate" data-cy="last-interaction-date-sort"></i></a>Last Interaction Date</th>
  						<th><a href="#" data-id="birthday" class="applySort"><i class="fa {{sortIcon "birthday"}} pull-right" aria-hidden="true" data-id="birthday" data-cy="birthday-sort"></i></a>Birthday</th>
  						<th><a href="#" data-id="age" class="applySort"><i class="fa {{sortIcon "age"}} pull-right" aria-hidden="true" data-id="age" data-cy="age-sort"></i></a>Age</th>
  						{{#if showAdditionalInquiryFields}}
  						
    						<th><a href="#" data-id="centerPreference" class="applySort"><i class="fa {{sortIcon "center_preference"}} pull-right" aria-hidden="true" data-id="centerPreference" data-cy="center-preference-sort"></i></a>Center Preference</th>
    						<th><a href="#" data-id="desiredStartDate" class="applySort"><i class="fa {{sortIcon "desiredStartDate"}} pull-right" aria-hidden="true" data-id="desiredStartDate" data-cy="desired-start-date-sort"></i></a>Start Date</th>
    						<th><a href="#" data-id="registrationSubmissionDate" class="applySort"><i class="fa {{sortIcon "registrationSubmissionDate"}} pull-right" aria-hidden="true" data-id="registrationSubmissionDate" data-cy="registration-submission-date-sort"></i></a>Submission Date</th>
  						{{/if}}
  					</tr>
            {{# each prospects}}
              <tr data-cy="inquiries-details">
                <td>{{rank}}</td>
                <td><a href="{{pathFor 'person' _id=_id}}" data-cy="first-name-col">{{firstName}}</a></td>
                <td><a href="{{pathFor 'person' _id=_id}}" data-cy="last-name-col">{{lastName}}</a></td>
                <td data-cy="last-interaction-date-col">
                  {{lastInteractionDateFormatted}}
                </td>
                <td data-cy="birthday-col">
                  {{formatDate (profileFieldValue "birthday") "M/DD/YYYY"}}
                </td>
                <td data-cy="age-col"> 
                  {{calcAge (profileFieldValue "birthday")}}
                </td>
                {{#if showAdditionalInquiryFields}}
                  <td data-cy="center-preference-col">
                    {{profileFieldValue "centerPreference"}}
                  </td>
                  <td data-cy="desired-start-date-col">
                    {{profileFieldValue "desiredStartDate"}}
                  </td>
                  <td data-cy="registration-submission-date-col">
                    {{formatDate (profileFieldValue "registrationSubmissionDate") 'MM/DD/YYYY'}}
                  </td>
                {{/if}}
              </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
