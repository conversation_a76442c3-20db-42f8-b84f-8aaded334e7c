import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_campaignMessageFormModal.html';
import $ from 'jquery';
import '../billing/_invoiceLedgerModal';
import { hideModal } from '../main';

var quill;

Template._campaignMessageFormModal.onRendered( function() {
	if (!(this.data && this.data.selectedPerson)) {
		$("select[name='recipients']").select2( {
			dropdownParent: $('#invoiceLedgerModal'),
			ajax: {

				transport: function (params, success, failure) {
					/*
					var request = new AjaxRequest(params.url, params);
					request.on('success', success);
					request.on('failure', failure);
					*/
					
					Meteor.callAsync("searchCampaignRecipients", {searchText: params.data.term}).then((result) => {
						success( {
							results: result
						});
					}).catch((error) => {
						console.log("error in searchCampaignRecipients", error);
						return;
					});
				}
			},
			templateResult: formatRecipientItem,
			escapeMarkup: function (markup) { return markup; }, // let our custom formatter work
		} );
	}
	quill = new Quill('#messageEditor', {
		theme: 'snow'
	});
});

Template._campaignMessageFormModal.helpers({
	selectedPerson() {
		const person = Template.instance().data.selectedPerson;
		return person;
	}
});

Template._campaignMessageFormModal.events({
	"click input[name='optionsMethod']": function(e, i) {
		const selectedMethod = $("input[name='optionsMethod']:checked").val();
		if (selectedMethod == "sms") {
			$("#campaignTextEditorRow").hide();
			$("#campaignSmsEditorRow").show();
			$("#campaignSubjectRow").hide();
		} else {
			$("#campaignTextEditorRow").show();
			$("#campaignSmsEditorRow").hide();
			$("#campaignSubjectRow").show();
		}
	},
	"click #btnSend": function(event, i) {
		const selectedPerson = i.data && i.data.selectedPerson,
			recipients = selectedPerson ? [{id: selectedPerson._id, type: "family"}] : $("select[name='recipients']").select2('data').map( (r) => ({id: r.id, text: r.text, type: r.type})),
			selectedMethod = $("input[name='optionsMethod']:checked").val(),
			quillDelta = quill.getContents(),
			htmlMessage = quillGetHTML(quillDelta),
			smsMessage = $("textarea[name='smsMessage']").val().trim(),
			subject = $("input[name='subject']").val();
		if (recipients.length == 0 ) return mpSwal.fire("Please select at least one recipient before continuing.");
		
		if ((selectedMethod == "sms" && smsMessage =="") || htmlMessage=="")
			return mpSwal.fire("Your message cannot be blank.");
		
		$(event.target).html('Sending...').prop("disabled", true);

		const options = {
			recipients,
			selectedMethod,
			subject,
			htmlMessage,
			smsMessage
		};

		Meteor.callAsync("sendCampaignMessage", options).then((result) => {
			$(event.target).html('Send').prop("disabled", false);
			hideModal("#_campaignMessageFormModal");
			mpSwal.fire("Success", "Message(s) queued for delivery", "success");
		}).catch((error) => {
			$(event.target).html('Send').prop("disabled", false);
			return mpSwal.fire("Error", error.reason, "error");
		});
	},
});

function formatRecipientItem (item) {
	
	if (item.loading) {
	  return item.text;
	}
  
	return `<div class="activity-search-item clearfix">
		<div class="activity-search-item__title">${item.text}</div>
		<div class="activity-search-item__description">${item.description}</div>
	</div>`;
}

function quillGetHTML(inputDelta) { var tempQuill=new Quill(document.createElement("div")); tempQuill.setContents(inputDelta); return tempQuill.root.innerHTML; }
