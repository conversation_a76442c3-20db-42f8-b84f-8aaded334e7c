import {
	PLAN_BUNDLE_TYPE,
	PUNCH_CARD_TYPE,
	SCALED_PLAN_FREQUENCIES,
	ITEM_TYPE, PLAN_TYPE, BillingFrequencies, PLANS_CHARGED_BY_CHECKINS,
	BILLING_ADMIN_PLANS_PEOPLE
} from "../../../lib/constants/billingConstants";
import { AvailableCustomizations } from '../../../lib/customizations';
import moment from "moment-timezone";
import { RegistrationDesignationUtils } from "../../../lib/registrationDesignationUtils";
import { MiscUtils } from "../../../lib/util/miscUtils";
import { getPeopleData } from "../../services/peopleMeteorService";
import { AVAILABLE_WEEKDAYS } from '../../../lib/constants/enrollmentConstants';
import $ from 'jquery';
import './_billingAdminPlanDetails';
import './_billingAdminPlanBundle';
import './_billingAdminPlanDetails';
import './billingAdminPlans.html';
import './billingProgramDetailEditor/billingProgramDetailEditor';
import { processPermissions } from "../../../lib/permissions";
import { Orgs } from "../../../lib/collections/orgs";
import { hideModal, showModal } from "../main";
import _ from '../../../lib/util/underscore';
import { Groups } from "../../../lib/collections/groups";
import '../simpleModal/simpleModal';

function getPlanModalTitle(action) {
	return `${action} Charge Details`;
}

Template.billingAdminPlans.onCreated( function() {
	var self=this;
	self.searchText = new ReactiveVar();
	self.showArchived = new ReactiveVar();

	let query = {inActive:{$ne:true}};
	const currentUser = Meteor.user();
	query["type"] = "person";
	query["orgId"] = currentUser["orgId"];
	const fields = {_id: 1, firstName: 1, lastName: 1, type: 1};
	getPeopleData(query, { fields }).then(res => {
		const results = res?.map( (p) => ({_id: p._id, type:p.type, label:p.lastName + ", " + p.firstName}));
		Session.set(BILLING_ADMIN_PLANS_PEOPLE, results);
	}).catch(err => {
		console.log(err);
	});
});

Template.billingAdminPlans.helpers({
	plans() {
		const searchText = Template.instance().searchText.get();
		const showArchived = Template.instance().showArchived.get();
		const todayDate = new moment.tz(Orgs.current().getTimezone()).startOf("day").valueOf();
		const plansAndItems = _.deep(Orgs.current(), "billing.plansAndItems");

		return plansAndItems
			.map(p => {
				p.isSuspended = p.suspendUntil && p.suspendUntil > todayDate;
				p.isExpired = p.expires && p.expires <= todayDate;
				return p;
			})
			.filter(p =>
				(!searchText || p.description?.toLowerCase().includes(searchText.toLowerCase())) &&
				(showArchived || !p.archived)
			)
			.sort((a, b) => (a.description || '').localeCompare(b.description || ''));
	},
	showAddButton() {
		return processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	showActions() {
		return processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit"}, { context: "billing/invoices", action: "edit"}, {context: "billing/invoices/itemCharges", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	showPropagateSettings() {
		return processPermissions({
      assertions: [{ context: "org/propagateSettings", action: "edit" } ],
      evaluator: (person) => person.superAdmin == true,
    });
	},
	showEditActions() {
		return processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit"} ],

			evaluator: (person) => person.type=="admin"
		});
	},
	showLedgerAccountName() {
		return Orgs.current().hasCustomization("billing/requireLedgerAccountName/enabled");
	},
	showChargeMultiple() {
		return this.type === ITEM_TYPE && processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit"}, {context: "billing/invoices/itemCharges", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	isNotBundle() {
		return this.type !== PLAN_BUNDLE_TYPE;
	},
	formatPlanType() {
		if (this.type == PUNCH_CARD_TYPE) {
			return 'Punch Card'
		} else {
			return this.type.capitalizeFirstLetter()
		}
	},
	hasRegistrationFlow() {
        return Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
    },
	formatExpires(expires){
		return expires ? moment(expires).format("MM/DD/YYYY") : "";
	}
});
Template.billingAdminPlans.events({
	"click #btnAddPlan": () => {
		showModal("simpleModal", {
			title: getPlanModalTitle('Add'),
			template: "billingAdminAddPlanModal",
			data: {
				type: PLAN_TYPE,
				showLedgerAccountName: Orgs.current().hasCustomization(AvailableCustomizations.REQUIRE_LEDGER_ACCOUNT)
			},
			multiValues: true,
			onSave: (e, i, formFieldData) => {
				if (formFieldData.type !== PLAN_BUNDLE_TYPE) {
					if (!formFieldData.description || (formFieldData.type === PLAN_TYPE && !formFieldData.frequency) || !formFieldData.amount) {
						$(e.target).html('Submit').prop("disabled", false);
						return mpSwal.fire("Missing data", "Please make sure to enter all fields");
					}
					const hasRegistrationFlow = Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
					if (hasRegistrationFlow && formFieldData.type === ITEM_TYPE) {
						validateDaysForItem(formFieldData)
					}
					if (hasRegistrationFlow && formFieldData.type === PLAN_TYPE) {
						validateWeeksForPlan(formFieldData, e)
					}

					// Add checkbox and designation selection data to field data
					addCheckedInformationToFieldData(formFieldData)

					addUpdateOfferedDays(formFieldData);

					if (tinymce.get('programDetailEditor')) {
						formFieldData.programDetails = tinymce.get('programDetailEditor').getContent();
					}

					formFieldData.offeredAtOrgIds = $("#reportOrgs").val() || [];

					Meteor.callAsync("insertBillingPlan", formFieldData)
					.then((result)=>{
						hideModal("#simpleModal");
					})
					.catch((error)=>{
						$(e.target).html('Submit').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				} else {
					const orgPlans = _.deep(Orgs.current(), "billing.plansAndItems");
					let showError = false;
					let errorMessage = '';
					if (!formFieldData.plan_0 || !formFieldData.plan_1 || formFieldData.plan_0 === formFieldData.plan_1) {
						showError = true;
						errorMessage = 'Please select two different plans.';
					}

					const plan1 = orgPlans.find(p => p._id === formFieldData.plan_0);
					const plan2 = orgPlans.find(p => p._id === formFieldData.plan_1);

					if (!plan1 || !plan2) {
						showError = true;
						errorMessage = 'Please make sure to select valid plans.';
					}

					const plan1Max = plan1.requiredEnrollmentMax ?? 5;
					const plan1Min = (plan1.requiredEnrollmentMin ?? 1) - 1;
					const plan2Max = plan2.requiredEnrollmentMax ?? 5;
					const plan2Min = (plan2.requiredEnrollmentMin ?? 1) - 1;

					for (let i = plan1Min; i < plan1Max; i++) {
						for (let j = plan2Min; j < plan2Max; j++) {
							const fieldKey = `scaledAmount_${i}_${j}`;
							if (!formFieldData[fieldKey]) {
								showError = true;
								errorMessage = errorMessage || 'Please make sure to enter all fields correctly.';
							}
						}
					}
					if (showError) {
						$(e.target).html('Submit').prop('disabled', false);
						return mpSwal.fire('Missing data', errorMessage);
					}

					// Add checkbox and designation selection data to field data
					addCheckedInformationToFieldData(formFieldData);

					if (tinymce.get('programDetailEditor')) {
						formFieldData.programDetails = tinymce.get('programDetailEditor').getContent();
					}
					
					Meteor.callAsync("insertBillingPlanBundle", formFieldData)
					.then((result)=>{	
						hideModal("#simpleModal");	
					})
					.catch((error)=>{
						$(e.target).html('Submit').prop('disabled', false);
						mpSwal.fire('Error', error.reason, 'error');
					});
				}
			}
		}, "#simpleModal");
	},
	"click .propagate-plan": (e, i) => {
		const planId = $(e.target).data("id");
		mpSwal.fire({
			title: "Propagate Billing Plan/Item",
			text: "Existing plans on child orgs will update Description and Amount fields. Otherwise, new plans will be added with all fields",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, Propagate"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("propagateSettings", { dataType: "billing.plansAndItems", value: { _id: planId }})
				.then((result)=>{
					mpSwal.fire("Success", "Settings Updated", "success")
				})
				.catch((error)=>{
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .delete-plan": (e, i) => {
		const planId = $(e.target).data("id");
		mpSwal.fire({
			title:"Are you sure?",
			text:"Deleting this plan will stop invoicing for all customers enrolled in it",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, delete plan"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("deleteBillingPlan", planId)
				.catch((error)=>{
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .edit-plan": function (e, i) {
		const planId = $(e.target).data("id");
		const plan = this;
		plan.showLedgerAccountName = Orgs.current().hasCustomization(AvailableCustomizations.REQUIRE_LEDGER_ACCOUNT);

		showModal("simpleModal", {
			title: getPlanModalTitle('Edit'),
			template: "billingAdminAddPlanModal",
			data: plan,
			multiValues: true,
			onSave: (e, i, formFieldData) => {
				formFieldData._id = planId;
				if (formFieldData.type !== PLAN_BUNDLE_TYPE) {
					if (!formFieldData.description || (formFieldData.type === PLAN_TYPE && !formFieldData.frequency) || !formFieldData.amount) {
						$(e.target).html('Submit').prop("disabled", false);
						return mpSwal.fire("Missing data", "Please make sure to enter all fields");
					}
					const hasRegistrationFlow = Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
					if (hasRegistrationFlow && formFieldData.type === ITEM_TYPE) {
						validateDaysForItem(formFieldData, e)
					}

					if (hasRegistrationFlow && formFieldData.type === PLAN_TYPE) {
						validateWeeksForPlan(formFieldData, e)
					}

					// Add checkbox and designation selection data to field data
					addCheckedInformationToFieldData(formFieldData)

					addUpdateOfferedDays(formFieldData);

					if (tinymce.get('programDetailEditor')) {
						formFieldData.programDetails = tinymce.get('programDetailEditor').getContent();
					}

					formFieldData.offeredAtOrgIds = $("#reportOrgs").val() || [];

					Meteor.callAsync("updateBillingPlan", formFieldData)
					.then((result)=>{
						hideModal("#simpleModal");
					})
					.catch((error)=>{
						$(e.target).html('Submit').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				} else {
					const orgPlans = _.deep(Orgs.current(), "billing.plansAndItems");
					let showError = false;
					let errorMessage = '';
					if (!formFieldData.plan_0 || !formFieldData.plan_1 || formFieldData.plan_0 === formFieldData.plan_1) {
						showError = true;
						errorMessage = 'Please select two different plans.';
					}

					const plan1 = orgPlans.find(p => p._id === formFieldData.plan_0);
					const plan2 = orgPlans.find(p => p._id === formFieldData.plan_1);

					if (!plan1 || !plan2) {
						showError = true;
						errorMessage = 'Please make sure to select valid plans.';
					}

					const plan1Max = plan1.requiredEnrollmentMax ?? 5;
					const plan1Min = (plan1.requiredEnrollmentMin ?? 1) - 1;
					const plan2Max = plan2.requiredEnrollmentMax ?? 5;
					const plan2Min = (plan2.requiredEnrollmentMin ?? 1) - 1;

					for (let i = plan1Min; i < plan1Max; i++) {
						for (let j = plan2Min; j < plan2Max; j++) {
							const fieldKey = `scaledAmount_${i}_${j}`;
							if (!formFieldData[fieldKey]) {
								showError = true;
								errorMessage = errorMessage || 'Please make sure to enter all fields correctly.';
							}
						}
					}
					if (showError) {
						$(e.target).html('Submit').prop('disabled', false);
						return mpSwal.fire('Missing data', errorMessage);
					}

					// Add checkbox and designation selection data to field data
					addCheckedInformationToFieldData(formFieldData);

					if (tinymce.get('programDetailEditor')) {
						formFieldData.programDetails = tinymce.get('programDetailEditor').getContent();
					}

					Meteor.callAsync('updateBillingPlanBundle', formFieldData)
					.then((result)=>{
						hideModal("#simpleModal");
					})
					.catch((error)=>{
						$(e.target).html('Submit').prop('disabled', false);
						mpSwal.fire('Error', error.reason, 'error');
					});
				}
			}
		}, "#simpleModal");
	},
	"click .suspend-plan": (e, i) => {
		const timezone = Orgs.current().getTimezone();
		const planId = $(e.target).data("id"), curDate = new moment.tz(timezone).format("MM/DD/YYYY");
		mpSwal.fire({
			title: 'Enter resume date for plan cancellation',
			html:
				`<div class="input-group date row no-gutters" data-provide="datepicker" style="max-width:150px;margin:0 auto">
                    <div class="col-10">
                        <input type="text" class="form-control" id="suspend-date-picker" value="${curDate}">
                    </div>
			  		<div class="input-group-addon col-1 ml-n6">
				  		<span class="fa fa-calendar mt-4"></span>
			  		</div>
		  		</div>`,
			onOpen: () => {
				$("#suspend-date-picker").datepicker({
					zIndexOffset:9999
				});
			},
			showCancelButton: true,
			focusConfirm: true,
			preConfirm: () => {
				const x = document.getElementById('suspend-date-picker').value;
			  return x;
			}
		}).then( async (result) => {
				if (result.value && moment.tz(result.value, 'MM/DD/YYYY', true, timezone).isValid()) {
					const resultValue = moment.tz(result.value, 'MM/DD/YYYY', timezone).valueOf();
					if (resultValue < moment.tz(timezone).valueOf()) {
						mpSwal.fire({icon: "error", title: "Please choose a resume date in the future"});
					} else {
						await Meteor.callAsync('removeBillingPlanFromAllGroupsInOrg', Orgs.current()._id, planId);
						await Meteor.callAsync("suspendBillingPlan", planId, result.value);
					}
				}
			});
	},
	"click .cancel-suspension": (e,i) => {
		const org = Orgs.current();
		const planId = $(e.target).data("id");
		mpSwal.fire({
			title: "Cancel suspension?",
			text: "Are you sure?",
			showCancelButton:true,
		}).then ( (result) => {
			if (result.value) {
				Meteor.callAsync("suspendBillingPlan", planId, null)
				.finally(() => {
					mpSwal.fire({title: "Plan resumed"});
				});
				Meteor.callAsync('addBillingPlanToAllGroupsWithSelectAllInOrg', org._id, org.availableBillingPlans() , planId);
			}
		})
	},
	"click .archive-plan": (e,i) => {
		const planId = $(e.target).data("id"),
			plan = _.deep(Orgs.current(), "billing.plansAndItems").find( p => p._id == planId);
		if (!plan) return;
		let popUpOptions = {};
		if (plan.type === PLAN_TYPE) {
			popUpOptions = {
				title: "Archive plan?",
				html: `<p>Archiving this plan will make it unavailable for future enrollments. Existing plans enrollments will not be affected.
					   <form><input type='checkbox' id='archive-unenroll'> Remove all existing enrollments in this plan</form></p>`,
				showCancelButton: true,
			};
		} else if (plan.type === ITEM_TYPE) {
			popUpOptions = {
				title: "Archive item?",
				html: `<p>Archiving this item will make it unavailable for future manual charges. Existing charges will not be affected.</p>`,
				showCancelButton: true,
			};
		} else {
			popUpOptions = {
				title: "Archive bundle?",
				html: `<p>Warning! Archiving will make this plan bundle unavailable for future enrollments. Existing plan bundles will not be affected. Do you want to proceed?
					   <form><input type='checkbox' id='archive-unenroll'> Remove this plan bundle from all child records</form></p>`,
				showCancelButton: true,
			};
		}
		mpSwal.fire(popUpOptions).then ( result => {
			if (result.value) {
				const unenroll = $("#archive-unenroll").prop("checked");
				const options = { planId, archive: true, unenroll };
				
				Meteor.callAsync("archiveBillingPlan", options)
				.finally(() => {
					mpSwal.fire({title: plan.type.capitalizeFirstLetter() + " archived"});
				});
				Meteor.callAsync('removeBillingPlanFromAllGroupsInOrg', Orgs.current()._id, planId)
			}
		});
	},
	"click .unarchive-plan": (e,i) => {
		const org = Orgs.current();
		const planId = $(e.target).data('id');
		const plan = _.deep(org, 'billing.plansAndItems').find( p => p._id === planId);
		mpSwal.fire({
			title: `Unarchive ${plan.type}?`,
			text: `This will re-activate the ${plan.type}, making it available for enrollments or charges.`,
			showCancelButton:true,
		}).then ( result => {
			if (result.value) {
				const options = { planId, archive: false };
				Meteor.callAsync("archiveBillingPlan", options)
				.finally(() => {
					mpSwal.fire({ title: 'Unarchived' });
				});
				Meteor.callAsync('addBillingPlanToAllGroupsWithSelectAllInOrg', org._id, org.availableBillingPlans() , planId)
			}
		});
	},
	"input input[id='search-text']": (e,i) => {
		const query= $(e.currentTarget).val();
		if ((query || "") != "") {
			$("#plan-search-group .fa-times").removeClass("hidden");
			$("#plan-search-group .fa-search").addClass("hidden");
		} else {
			$("#plan-search-group .fa-times").addClass("hidden");
			$("#plan-search-group .fa-search").removeClass("hidden");
		}
		i.searchText.set(query);
	},
	"click #search-btn-clear": (e,i) => {
		$("input[id='search-text']").val("");
		$("#plan-search-group .fa-times").addClass("hidden");
		$("#plan-search-group .fa-search").removeClass("hidden");
		i.searchText.set("");
	},
	"change #showArchived": (e,i) => {
		const showArchived = $("#showArchived").prop("checked");
		i.showArchived.set(showArchived);
	},
	"click .batch-charge": (e, i) => {
		const planId = $(e.target).data("id");
		const planInfo = _.find(_.deep(Orgs.current(), "billing.plansAndItems"), p => p._id === planId);

		showModal("simpleModal", {
			title:"Charge Multiple",
			template: "billingAdminChargeMultiple",
			data: {
				planId,
				planInfo
			},
			onSave: (e, i, formFieldData) => { 
				formFieldData.item = planId;
				formFieldData.targets = $("#targets").val();
				let targetDescription = "", showall = false, peopleCount = 0;
				_.each(formFieldData.targets, target => {
					if (target === "group:allenrollees") showall = true;
					else if (target.startsWith("group:")) {
						const group = Groups.findOne(target.replace("group:",""));
						targetDescription += (targetDescription !== "" ? ", " : "") + group.name;
					} else {
						peopleCount++;
					}
				});
				if (peopleCount > 0) 
					targetDescription += (targetDescription !== "" ? " and " : "") + peopleCount + " individual(s)";
				if (showall)
					targetDescription = "all enrollees";
				mpSwal.fire({
					title: "Confirm Multiple Charges",
					text: `You are about to post charges for: ${targetDescription}. Are you sure?`,
					showCancelButton:true,
				}).then ( (result) => {
					if (result.value) {
						Meteor.callAsync("insertBillingCharge", formFieldData)
						.then((result)=>{
							$("#simpleModal").modal("hide");
							mpSwal.fire("Success", "Item charges posted successfully.");
						})
						.catch((error)=>{
							$(e.target).html('Save').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
					} else
						$(e.target).html('Save').prop("disabled", false);
				});
			}
		});
	},
});

/**
 * Helper function for adding and editing an item to validate days input data
 * @param {Object} formFieldData Object containing information with all billing fields
 * @returns 
 */
function validateDaysForItem(formFieldData) {
	let selectedDays = [];
	_.each(AVAILABLE_WEEKDAYS, (day) => {
		if ($("input[name=recurs" + day + "]").prop("checked")) {
			selectedDays.push(day.toLowerCase());
		}
	});
	formFieldData.recurring_days = selectedDays

	const dates = document.querySelectorAll('.individual-service-datepicker');
	let validDates = [];
	for (const date of dates) {
		if (moment(date.value, 'MM/DD/YYYY', true).isValid()) {
			validDates.push(date.value);
		}
	}
	formFieldData.individual_dates = validDates;
}

/**
 * Helper function for adding and editing a plan to validate week input data
 * @param {Object} formFieldData Object containing information with all billing fields
 * @param {Object} e Event object
 * @returns 
 */
function validateWeeksForPlan(formFieldData, e) {
	// Make sure the selective weeks are set correctly
	const weekStarts = document.querySelectorAll('.selective-week-datepicker-start');
	const weekAmounts = document.querySelectorAll('.selective-week-amount');
	const validWeeks = [];
	const validAmounts = [];
	for (const weekStart of weekStarts) {
		const index = weekStart.dataset.id;
		const weekEnd = document.querySelector(`.selective-week-datepicker-end[data-id="${ index }"]`);
		if (moment(weekStart.value, 'MM/DD/YYYY', true).isValid() && weekEnd && moment(weekEnd.value, 'MM/DD/YYYY', true).isValid()) {
			validWeeks.push([weekStart.value, weekEnd.value]);
		}
	}

	for (const weekAmount of weekAmounts) {
		if (weekAmount.value && !isNaN(weekAmount.value) && weekAmount.value >= 0) {
			validAmounts.push(weekAmount.value);
		} else {
			$(e.target).html('Submit').prop("disabled", false);
			return mpSwal.fire('Missing data', 'Please enter valid selective week amounts.');
		}
	}
	// There must be selective weeks if that is the date type selection
	if (formFieldData.date_type === 'timePeriodSelectiveWeeks' && (!validWeeks.length || !validAmounts.length)) {
		$(e.target).html('Submit').prop("disabled", false);
		return mpSwal.fire('Missing data', 'Please enter the selective weeks within the time period.');
	}

	if (validWeeks.length !== validAmounts.length) {
		$(e.target).html('Submit').prop("disabled", false);
		return mpSwal.fire('Missing data', 'The number of valid selective weeks does not match the number of valid amounts.');
	}

	formFieldData.selectiveWeeks = validWeeks;
	formFieldData.selectiveWeekAmounts = validAmounts;
}

/**
 * Helper function for adding and editing a plan to get checkbox and designation information into formFieldData object
 * @param {Object} formFieldData Object containing information with all billing fields
 * @returns 
 */
function addCheckedInformationToFieldData(formFieldData) {
	if (document.getElementById('regFeeExempt')) {
		formFieldData.regFeeExempt = document.getElementById('regFeeExempt').checked;
	}
	if (document.getElementById('siblingDiscountExempt')) {
		formFieldData.siblingDiscountExempt = document.getElementById('siblingDiscountExempt').checked;
	}
	if (document.getElementById('designationsSelect')) {
		formFieldData.designations = MiscUtils.getMultiselectValues(document.getElementById('designationsSelect'));
	}

	if (document.getElementById('excludeSaturdays')) {
		formFieldData.excludeSaturdays = document.getElementById('excludeSaturdays').checked;
	}

	if (document.getElementById('excludeSundays')) {
		formFieldData.excludeSundays = document.getElementById('excludeSundays').checked;
	}
}

// modal for adding/editing plans
Template.billingAdminAddPlanModal.onCreated( function() {
	const self = this;
	const currentType = Template.instance().data.type;
	self.org = Orgs.current();
	const openWeekends = self.org?.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);

	const defaultOfferedDays = openWeekends ? ["saturday", "monday", "tuesday", "wednesday", "thursday", "friday", "sunday"] : ["monday", "tuesday", "wednesday", "thursday", "friday"];
	self.designationOptions = new ReactiveVar(RegistrationDesignationUtils.getConfigurableDesignations(this.org));
	self.offeredDays = new ReactiveVar(Template.instance().data.programOfferedOn || defaultOfferedDays);
	self.enrollmentDaysMin = new ReactiveVar(Template.instance().data?.requiredEnrollmentMin || 1);
	self.enrollmentDaysMax = new ReactiveVar(Template.instance().data?.requiredEnrollmentMax || (openWeekends ? 7 : 5));
	self.definitionType = new ReactiveVar(currentType);
	self.showAmount = new ReactiveVar(!SCALED_PLAN_FREQUENCIES.includes(Template.instance().data.frequency));
	self.showDailyAmount = new ReactiveVar(false);
	self.frequency = new ReactiveVar(Template.instance().data.frequency);
	self.amount = new ReactiveVar(Template.instance().data.amount);
});
Template.billingAdminAddPlanModal.onRendered(function() {
	const instance = this;

	// Initialize datepicker
	$("#defaultExpirationPlan").datepicker({
		autoclose: true,
		todayHighlight: true
	});

	// Initialize select2 for designations
	$("#designationsSelect").select2({
		multiple: true,
	});

	// Initialize select2 for programOfferedOn and handle change event
	$("#programOfferedOn").select2({
		multiple: true,
	}).on("change", () => {
		const isScaledPlan = SCALED_PLAN_FREQUENCIES.includes(instance.frequency.get());
		const element = $("#programOfferedOn");
		const valid = isScaledPlan ? validateInput(element.val(), 'offeredDays', this) : true;
		if (valid) {
			instance.offeredDays.set(element.val());
		} else {
			// Reset the value to the previous state
			element.val(instance.offeredDays.get()).trigger("change");
			// Close the select list
			element.select2().trigger("select2:close");
		}
	});
});




Template.billingAdminAddPlanModal.events({
	"click input[name=type]": (e, i) => {
		if(e.target.value === ITEM_TYPE)  {
			$("#selectFrequency").prop("disabled", true);
			$("#selectCategory").prop("disabled", true);
		} else if(e.target.value == PUNCH_CARD_TYPE) {
			$("#selectFrequency").prop("disabled", true);
			$("#selectCategory").prop("disabled", false);
		} else {
			$("#selectFrequency").prop("disabled", false);
			$("#selectCategory").prop("disabled", false);
			$("#selectProgram").prop("disabled", false);
		}
	},
	"change input[name='type']": (e, i) => {
		setTimeout(() => {
			$("#defaultExpirationPlan").datepicker({ autoclose: true, todayHighlight: true });
		}, 400);
		i.definitionType.set( $(e.currentTarget).val() );
	},
	'change #selectFrequency': (e, i) => {
		const previousFrequency = i.frequency.get();
		// If moving from a non-scaled to a scaled, set the max enrollment days to the length of the offered days to keep from locking the form upon validation.
		if (!SCALED_PLAN_FREQUENCIES.includes(previousFrequency) && SCALED_PLAN_FREQUENCIES.includes(e.target.value)) {
			if (i.offeredDays.get().length < i.enrollmentDaysMax.get()) {
				i.enrollmentDaysMax.set(i.offeredDays.get().length);
			}
		}

		Template.instance().showDailyAmount.set(e.target.value === BillingFrequencies.DAILY_CHARGED_MONTHLY || e.target.value === BillingFrequencies.WEEKLY_SCHEDULED_DAILY);
		i.showAmount.set(!SCALED_PLAN_FREQUENCIES.includes(e.target.value));
		i.frequency.set(e.target.value);
	},
	'input #txtAmount': (e, i) => {
		i.amount.set(e.target.value);
	},
	'input #requiredEnrollmentMin': (e, i) => {
		const valid = validateInput(e.target.value, 'enrollmentDaysMin', i);
		if (valid) {
			i.enrollmentDaysMin.set(e.target.value);
		} else {
			e.target.value = i.enrollmentDaysMin.get();
		}
	},
	'input #requiredEnrollmentMax': (e, i) => {
		const valid = validateInput(e.target.value, 'enrollmentDaysMax', i);
		if (valid) {
			i.enrollmentDaysMax.set(e.target.value);
		} else {
			e.target.value = i.enrollmentDaysMax.get();
		}
	}
});
Template.billingAdminAddPlanModal.helpers({
	showRegFeeConfig() {
		return Template.instance().org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	},
	showDesignations() {
		return Template.instance().org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW) && Template.instance().designationOptions.get().length;
	},
	regFeeExemptChecked() {
		return Template.instance().data.regFeeExempt ? 'checked' : '';
	},
	siblingDiscountExemptChecked() {
		return Template.instance().data.siblingDiscountExempt ? 'checked': '';
	},
	designationOptions() {
		return Template.instance().designationOptions.get();
	},
	designations() {
		return Template.instance().data.designations || [];
	},
	availableBillingFrequencies() {
		return Orgs.current().availableBillingFrequencies();
	},
	availablePrograms() {
		return (Orgs.current() && Orgs.current().availablePrograms());
	},
	itemSelected() {
		return Template.instance().definitionType.get() === ITEM_TYPE;
	},
	isAmountVisible() {
		return Template.instance().showAmount.get();
	},
	getScaledAmount(index) {
		return Template.instance().data.scaledAmounts ? Template.instance().data.scaledAmounts[index] : 0;
	},
	isShowBundleOption() {
		return Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.PLAN_BUNDLES);
	},
	amountText() {
		return Template.instance().showDailyAmount.get() ? 'Daily Amount' : 'Amount';
	},
	showDetails() {
		return Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW) &&
			(Template.instance().definitionType.get() === PLAN_TYPE || Template.instance().definitionType.get() === ITEM_TYPE)
	},
	bundleType() {
		return PLAN_BUNDLE_TYPE;
	},
	isBundleSelected() {
		return Template.instance().definitionType.get() === PLAN_BUNDLE_TYPE;
	},
	data() {
		return Template.instance().data;
	},
	isShowPunchCard() {
		return Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.PUNCH_CARDS)
	},
	punchCardSelected() {
		return Template.instance().definitionType.get() === PUNCH_CARD_TYPE
	},
	showFrequency() {
		return Template.instance().definitionType.get() === ITEM_TYPE || Template.instance().definitionType.get() === PUNCH_CARD_TYPE;
	},
	selectedFrequency() {
		return Template.instance().frequency;
	},
	planAmount() {
		return Template.instance().amount;
	},
	showCategory() {
		return Template.instance().definitionType.get() === ITEM_TYPE;
	},
	showDropInDailyRate() {
		return Template.instance().definitionType.get() === ITEM_TYPE && (Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.PUNCH_CARDS))
	},
	definitionType() {
		return Template.instance().definitionType.get();
	},
	isScaledPlan() {
		return Template.instance().definitionType.get() === PLAN_TYPE && SCALED_PLAN_FREQUENCIES.includes(Template.instance().frequency.get());
	},
	showProgramOfferedOn() {
		return Template.instance().definitionType.get() === PLAN_TYPE && !PLANS_CHARGED_BY_CHECKINS.includes(Template.instance().frequency.get());
	},
	availableDays() {
		const org = Orgs.current();
		return org?.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED)
			? ["saturday", "monday", "tuesday", "wednesday", "thursday", "friday", "sunday"]
			: ["monday", "tuesday", "wednesday", "thursday", "friday"];
	},
	availableDaysCount() {
		const org = Orgs.current();
		return org?.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED) ? 7 : 5;
	},
	offeredDays() {
		return Template.instance().offeredDays.get();
	},
	formatDay(day) {
		return day.capitalizeFirstLetter();
	},
	requiredEnrollmentMin() {
		return Template.instance().enrollmentDaysMin.get();
	},
	requiredEnrollmentMax() {
		return Template.instance().enrollmentDaysMax.get();
	},
	hasRegistrationFlow() {
        return Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
    },
	orgsFieldOpts() {
		return {
			selectedIds: Template.instance().data.offeredAtOrgIds || [],
		};
	},
});

Template.billingAdminChargeMultiple.helpers({
	targets() {
		if (!Meteor.user() || !Meteor.user().fetchPerson()) return;
		const currentPerson = Meteor.user().fetchPerson();

		const results = Session.get(BILLING_ADMIN_PLANS_PEOPLE);

		Groups.find({}, {sort:{name:-1}}).fetch().forEach( (g) => { results.unshift( {_id:"group:"+g._id, type:"group", label: "All " + g.name + " enrollees"})});
		results.unshift(
			{_id:"group:allenrollees", type:"group", label: "All Enrollees"}
		);
		return results;
	},
	"availableDiscountTypes": () => {
		return Orgs.current() && _.sortBy(Orgs.current().availableDiscountTypes(),  (d) => d.description );
	}
});
Template.billingAdminChargeMultiple.onRendered(() => {
	$("#targets").selectize({
		render: {
			option: formatTarget,
			item: formatTaggedTarget
		},
		plugins: ['remove_button']
	});
});

function formatTarget (item, escape) {
	const recipientType = item.text.split("|")[1], recipientName = item.text.split("|")[0];
	return "<div><span class='name-label'>" + escape(recipientName) + "</span><span class='name-caption'>" + escape(recipientType)+ "</span></div>";
}

function formatTaggedTarget (item, escape) {
	const recipientName = item.text.split("|")[0];
	return "<div class='item'><span>" + escape(recipientName) + "</span></div>";
}
const validateInput = (value, type, instance) => {

	if(!value) {
		mpSwal.fire({icon: "error", title: "Please enter a value"});
		return false;
	}

	switch (type) {
		case 'enrollmentDaysMin':
			if (parseInt(value) > parseInt(instance.enrollmentDaysMax.get())) {
				mpSwal.fire({
					icon: "error",
					title: "Minimum enrollment days cannot be greater than maximum enrollment days"
				});
				return false;
			} else if(parseInt(value) > instance.offeredDays.get().length) {
				mpSwal.fire({icon: "error", title: "Minimum enrollment days cannot exceed the number of days offered"});
				return false;
			}
			break;
		case 'enrollmentDaysMax':
			if (parseInt(value) < parseInt(instance.enrollmentDaysMin.get())) {
				mpSwal.fire({
					icon: "error",
					title: "Maximum enrollment days cannot be less than minimum enrollment days"
				});
				return false;
			} else if (parseInt(value) > instance.offeredDays.get().length) {
				mpSwal.fire({icon: "error", title: "Maximum enrollment days cannot exceed the number of days offered"});
				return false;
			}
			break;
		case 'offeredDays':
			if (value.length < instance.enrollmentDaysMin.get()) {
				mpSwal.fire({
					icon: "error",
					title: 'You cannot offer fewer total days than your minimum days for enrollment. Please adjust your minimum days for enrollment first.'
				});
				return false;
			} else if (value.length < instance.enrollmentDaysMax.get()) {
				mpSwal.fire({
					icon: "error",
					title: 'You cannot offer fewer total days than your maximum days for enrollment. Please adjust your maximum days for enrollment first.'
				});
				return false;
			}
			break;
	}
	return true;
}
const addUpdateOfferedDays = (formFieldData) => {
	if (formFieldData.type === PLAN_TYPE && !PLANS_CHARGED_BY_CHECKINS.includes(formFieldData.frequency)) {
		formFieldData.programOfferedOn = MiscUtils.getMultiselectValues(document.getElementById('programOfferedOn'));
		// Scaled plan only properties.
		if (SCALED_PLAN_FREQUENCIES.includes(formFieldData.frequency)) {
			formFieldData.requiredEnrollmentMin = parseInt(document.getElementById('requiredEnrollmentMin').value);
			formFieldData.requiredEnrollmentMax = parseInt(document.getElementById('requiredEnrollmentMax').value);
		}
	} else { // Not a plan type or a plan charged by checkins
		if (formFieldData.programOfferedOn) {
			delete formFieldData.programOfferedOn;
		}

		if (formFieldData.requiredEnrollmentMin) {
			delete formFieldData.requiredEnrollmentMin;
		}

		if (formFieldData.requiredEnrollmentMax) {
			delete formFieldData.requiredEnrollmentMax;
		}
	}
}

Template.billingAdminPlans.onDestroyed(function() {
	delete Session.keys[BILLING_ADMIN_PLANS_PEOPLE];
});