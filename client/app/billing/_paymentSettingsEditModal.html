<template name="paymentSettingsEditModal">
    <form id="paymentSettingsForm">
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Manual Payment Type</label>
            <div class="col-lg-6 col-md-9 col-sm-12">
                <select id="manualPayTypesSelect" name="manualPaymentTypes[]" size=1 multiple class="form-control">
                    {{#each getManualPayTypes}}
                        <option {{ getIsSelected value 'payTypes' }} value="{{value}}">{{text}}</option>
                    {{/each}}
                </select>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Payment Method On File</label>
            <div class="col-lg-6 col-md-9 col-sm-12">
                <select id="paymentMethodSelect" name="paymentMethod" size=1 class="form-control">
                    <option {{ getIsSelected true 'paymentMethod' }} value="required">Required - Family members must keep at least one payment method on file at all times.</option>
                    <option disabled="{{ isOptionalPaymentMethodDisabled }}" {{ getIsSelected false 'paymentMethod' }} value="optional">Optional - Family members are not required to keep a payment method on file.</option>
                </select>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Autopay Enrollment</label>
            <div class="col-lg-6 col-md-9 col-sm-12">
                <select id="autopayEnrollmentSelect" name="autopayEnrollment" size=1 class="form-control">
                    <option {{ getIsSelected true 'autopayEnrollment' }} value="required">Required - Family members must remain enrolled in autopay once initially enrolled.</option>
                    <option {{ getIsSelected false 'autopayEnrollment' }} value="optional">Optional - Family members are not required to enroll in autopay.</option>
                </select>
            </div>
        </div>
    </form>
</template>