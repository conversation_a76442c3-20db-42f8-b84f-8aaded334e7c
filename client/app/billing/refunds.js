import './refunds.html';
import '../reports/reportOrgsField'

Template.refunds.onCreated(function() {
	var self = this;
	self.refundData = new ReactiveVar();
	self.showOrgs = new ReactiveVar();
});

Template.refunds.onRendered(function() {
	$('#refundsStartDate').datepicker({autoclose:true});
	$('#refundsEndDate').datepicker({autoclose:true});
	var self = this;
	callGetRefunds(self, moment().add(-30, "days").format("MM/DD/YYYY"), moment().format("MM/DD/YYYY"), false);
});

function callGetRefunds(instance, passedStartDate, passedEndDate, passedShowPendingOnly) {
	const startDate = passedStartDate || $("#refundsStartDate").val(),
		endDate = passedEndDate || $("#refundsEndDate").val(),
		showPendingOnly = passedShowPendingOnly || $("#showPendingOnly").prop("checked"),
		refundType = $("select[name=type]").val(),
		orgIds = $("#reportOrgs").val();

	$("#btnUpdate").html("Updating...").prop("disabled", true);
	
	Meteor.callAsync("billingRefundsReport", {
		startDate,
		endDate,
		showPendingOnly,
		refundType,
		orgIds
	})
	.then((result) => {
		$("#btnUpdate").html("Update").prop("disabled", false);
		instance.refundData.set(result);
		instance.showOrgs.set((orgIds||[]).length >= 1);
	})
	.catch((error) => {
		$("#btnUpdate").html("Update").prop("disabled", false);
		instance.refundData.set(undefined);
		instance.showOrgs.set((orgIds||[]).length >= 1);
	});
}

Template.refunds.helpers({
	formattedStartDate() {
		return moment().add(-30, "days").format("MM/DD/YYYY");
	},
	formattedEndDate() {
		return moment().format("MM/DD/YYYY");
	},
	refunds() {
		return Template.instance().refundData.get();
	},
	showOrg() {
		return Template.instance().showOrgs.get();
	}
});

Template.refunds.events({
	"click #btnUpdate"(e,i) {
		callGetRefunds(i);
	},
	"click .btnViewInvoice"(e, i) {
		window.open("/billing/invoices/" + this.invoiceId, "_blank");
	}
});