import { Orgs } from '../../../lib/collections/orgs';
import { PaymentSettingsClientService } from '../../services/billing/PaymentSettingsClientService';
import { showModal } from '../main';
import './_billingAdminPaymentSettings.html';
import './_paymentSettingsEditModal';
import '../simpleModal/simpleModal';

Template.billingAdminPaymentSettings.onCreated(function() {
    const org = Orgs.current();
    this.paymentMethodRequired = new ReactiveVar(org.billing?.paymentMethodRequired || false);
    this.autopayEnrollmentRequired = new ReactiveVar(org.billing?.autopayEnrollmentRequired || false);
});

Template.billingAdminPaymentSettings.helpers({
    showEditOptions() {
      return true;
    },
    getPaymentMethodOptionValue() {
        return PaymentSettingsClientService.paymentMethodOptionValue(Template.instance().paymentMethodRequired.get());
    },
    getAutopayEnrollmentOptionValue() {
        return PaymentSettingsClientService.autopayEnrollmentOptionValue(Template.instance().autopayEnrollmentRequired.get());
    },
});

Template.billingAdminPaymentSettings.events({
    "click #paymentSettingsEdit": (event, instance) => {
        showModal("simpleModal", {
            title:"Billing Options",
            template: "paymentSettingsEditModal",
            nonScrollable: true,
            multiValues: true,
            onSave: (e, i, formFieldData) => {
                const { excludedManualPayTypes, paymentMethodRequired, autopayEnrollmentRequired } = PaymentSettingsClientService.getFormValuesFromOnSaveData(formFieldData);
                Meteor.call("updateOrgPaymentSettings", Orgs.current()._id, paymentMethodRequired, autopayEnrollmentRequired, excludedManualPayTypes, (error) => {
                    if (error) {
                       mpSwal.fire('Error', error.reason, 'error').then(() => {
                           $("#simpleModal").modal('hide');
                       });
                    } else {
                        instance.paymentMethodRequired.set(paymentMethodRequired);
                        instance.autopayEnrollmentRequired.set(autopayEnrollmentRequired);
                        mpSwal.fire('Success', 'Billing settings updated', 'success').then(() => {
                            $("#simpleModal").modal('hide');
                        });
                    }
                });
            }
        }, "#simpleModal");
    },
});