<template name="deposits">
	<div class="container">
		<div class="card card-custom">
			<div class="card-header flex-wrap border-0 pt-6 pb-0">
				<div class="card-title">
					<h3 class="card-label">Manage Deposits
					<span class="text-muted pt-2 font-size-sm d-block">Find payments and manage deposits.</span></h3>
					
				</div>
				<div class="card-toolbar">
					<!--<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>-->
					
				</div>
				
			</div>
			<div class="row ml-6 mt-6">
				<div class="col-12">
					<ul class="nav nav-pills">
						<li class="nav-item">
						  <a class="nav-link {{#if trueIfEq currentView "payments"}}active{{/if}} nav-link-deposits-view" data-view="payments" href="/billing/admin/bank-deposits"  data-cy="open-payments-tab" >Open Payments</a>
						</li>
						<li class="nav-item">
						  <a class="nav-link {{#if trueIfEq currentView "deposits"}}active{{/if}} nav-link-deposits-view" data-view="deposits" href="#" data-cy="past-deposits-tab">Past Deposits</a>
						</li>
					</ul>
				</div>
			</div>
			{{#if trueIfEq currentView "payments"}}
				{{> depositsPayments}}
			{{else}}
				{{> depositsList}}
			{{/if}}
		</div>
	</div>
</template>
