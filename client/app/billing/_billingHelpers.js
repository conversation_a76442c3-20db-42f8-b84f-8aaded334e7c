import { Orgs } from "../../../lib/collections/orgs";
import { Invoice } from "../../../lib/collections/invoices";
import { Relationships } from "../../../lib/collections/relationships";
import { getPeopleData } from "../../services/peopleMeteorService";
import { showModal } from "../main";
import './_adjustLineItemModal';
import './_billingCredit';
import './_billingPayment';
import './_billingRefund';
import _ from '../../../lib/util/underscore';
import { processPermissions } from "../../../lib/permissions";
import '../simpleModal/simpleModal';
import './_billingVoidCreditLine.html';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { AvailableActionTypes, AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import { HistoryAuditChangeTypes, HistoryAuditRecordTypes } from "../../../lib/constants/historyAuditConstants";
import { Log } from "../../../lib/util/log";

export class BillingHelpers {
	static showPayNowModal(personId, person, invoiceId, invoice ) {
		invoice = new Invoice(invoice);
		const availableBankAccount = person.connectedBankAccount() && person.connectedBankAccount().status === "verified";
		const availableCreditCard = person.connectedCreditCard();
		const currentOrg =  Orgs.current();
		const paymentSource = currentOrg.billing.cashnetEnabled ? "cashnet" : "stripe";

		if (!availableBankAccount && !availableCreditCard && paymentSource !== "cashnet") {
			mpSwal.fire("No available payment methods", "You currently do not have any active payment methods. If you added a bank account, please make sure to complete the verification process before attempting to pay.", "error");
			return;
		}

		showModal("simpleModal", {
			title: "Pay Invoice Now",
			template: "billingPaymentPayNow",
			actionButtonLabel: "Pay Now",
			data: {
				disableBankAccount: !availableBankAccount,
				disableCreditCard: !availableCreditCard,
				paymentAmount: invoice.amountDueForFamilyMember(person._id),
				invoiceNumber: invoice.invoiceNumber,
				showServiceChargeNotice: currentOrg.billing.passthroughFees,
				paymentSource,
				currentOrg
			},
			onSave: (e, i, formFieldData) => {
				if (paymentSource === "cashnet") {
					if (parseFloat(formFieldData.payment_amount) > invoice.openAmount) {
						return mpSwal.fire("Error", "You cannot specify a payment amount greater than the invoice amount.", "error");
					}
					$("#simpleModal").modal("hide");

					const amountFormatted = numeral(formFieldData.payment_amount).format('0.00'),
						signoutUrl = "http://localhost:3000/billing/cashnet/signout",
						altPostUrl = "https://tendlymr-staging.herokuapp.com/billing/cashnet/transact",
						cashnetUrl = (Orgs.current().billing.cashnetUrl || "https://train.cashnet.com/UCR_ECStest") + "?itemcode=ECS-BAL&amount=" + amountFormatted +
							"&ref1type=INVOICEID&ref1val=" + invoiceId +
							"&ref2type=PAYERID&ref2val=" + personId +
							"&ref3type=INVOICENUMBER&ref3val=" + invoice.invoiceNumber + (
								window.location.href.includes("://localhost") ? "&alt_otpsso_url=" + encodeURIComponent( altPostUrl) + "&signouturl=" + encodeURIComponent(signoutUrl) : ""
							);

					window.open(cashnetUrl, "_blank");
				} else {
					formFieldData.personId = personId;
					formFieldData.invoiceId = invoiceId;

					// This is a way to emulate chargeback in Adyen by passing "chargeback" as holder name
					// To emulate chargeback use the following command in the browser console before submitting the pay now form:
					// window.paymentHolderName = 'chargeback';
					const holderName = window.paymentHolderName;
					if (holderName) {
						formFieldData.holderName = holderName;
						console.info(`Using custom holder name "${holderName}"`)
						window.paymentHolderName = null;
					}

					const amountDue = invoice.amountDueForFamilyMember(person._id);
					const payInvoice = function () {
						Meteor.callAsync("payInvoice", formFieldData)
						.then((result)=>{
							$("#simpleModal").modal("hide");
						})
						.catch((error)=>{
							$(e.target).html('Submit').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
					};
					if (parseFloat(formFieldData.payment_amount) <= invoice.openAmount && parseFloat(formFieldData.payment_amount) <= amountDue) {
						payInvoice();
					} else {
						if (Orgs.current().hasCustomization("billing/preventOverpayment/enabled")) {
							$(e.target).html('Submit').prop("disabled", false);
							return mpSwal.fire("Error", "You cannot specify an amount greater than what is due for this invoice.");
						}
						mpSwal.fire({
							title: "Confirm Overpayment",
							text: "You are choosing to pay more than the open amount of the invoice that you owe. Any remaining amount over " + numeral(amountDue || invoice.openAmount).format('$0.00') + " will be applied to a credit memo on your account.",
							type: "warning",
							showCancelButton: true,
							confirmButtonText: "Confirm Overpayment"
						}).then(result => {
							if (result.value) {
								formFieldData.overridePaymentLimit = true;
								payInvoice();
							} else
								$(e.target).html('Submit').prop("disabled", false);

						});
					}
				}
			}
		});
	}

	static async showCreditModal(invoiceId, invoice, payerPersonId, existingCreditIndex, callback = null) {
		invoice = new Invoice(invoice);
		const existingCredit = parseInt(existingCreditIndex) >= 0 && invoice.credits[existingCreditIndex];

		if (existingCredit && existingCredit.creditReason === "manual_payment") {
			existingCredit.creditReason += "_" + existingCredit.creditManualPaymentMethod + "_fpid_" + existingCredit.paidBy;
		}
		
		const creditReasons= [
			{ reasonId: "", reasonDescription: ""}
		];

		const excludedManualPayTypes = _.deep(Orgs.current(), "billing.excludedManualPayTypes") || [];
		const userHasPayrollEditPermission = processPermissions({
			assertions: [
				{ context: AvailablePermissions.BILLING_PAYMENTS, action: AvailableActionTypes.EDIT },
				{ context: AvailablePermissions.BILLING_PAYMENTS_CREDIT_PAYROLL_DEDUCTION, action: AvailableActionTypes.EDIT }
			],
			evaluator: (person) => person.type === USER_TYPES.ADMIN
		})

		if (!payerPersonId) {
			const familyPayerIds = await Relationships.find({targetId: invoice.personId, relationshipType:"family"}).mapAsync(r => r.personId);
			const familyPeople = await getPeopleData({_id: {"$in": familyPayerIds}});

			_.each(familyPeople, (fp) => {
				if (!excludedManualPayTypes.includes('cash')) {
					creditReasons.push({
						reasonId: "manual_payment_cash_fpid_" + fp._id,
						reasonDescription: "Manual Payment - Cash - " + fp.firstName + " " + fp.lastName
					});
				}

				if (!excludedManualPayTypes.includes('check')) {
					creditReasons.push({
						reasonId: "manual_payment_check_fpid_" + fp._id,
						reasonDescription: "Manual Payment - Check - " + fp.firstName + " " + fp.lastName
					});
				}

				if (!excludedManualPayTypes.includes('credit_card')) {
					creditReasons.push({
						reasonId: "manual_payment_card_fpid_" + fp._id,
						reasonDescription: "Manual Payment - Credit Card - " + fp.firstName + " " + fp.lastName
					});
				}

				if (!excludedManualPayTypes.includes('ach')) {
					creditReasons.push({
						reasonId: "manual_payment_ach_fpid_" + fp._id,
						reasonDescription: "Manual Payment - ACH - " + fp.firstName + " " + fp.lastName
					});
				}

				if (!excludedManualPayTypes.includes('payroll_deduction') && userHasPayrollEditPermission) {
					creditReasons.push({
						reasonId: "payroll_deduction",
						reasonDescription: "Payroll Deduction - " + fp.firstName + " " + fp.lastName
					});
				}

				creditReasons.push({ reasonId: "security_deposit_refund_fpid_" + fp._id, reasonDescription: "Security Deposit Refund - " + fp.firstName + " " + fp.lastName});
			});
		} else {
			if (!excludedManualPayTypes.includes('cash')) {
				creditReasons.push({
					reasonId: "manual_payment_cash_fpid_" + payerPersonId,
					reasonDescription: "Manual Payment - Cash"
				});
			}

			if (!excludedManualPayTypes.includes('check')) {
				creditReasons.push({
					reasonId: "manual_payment_check_fpid_" + payerPersonId,
					reasonDescription: "Manual Payment - Check"
				});
			}

			if (!excludedManualPayTypes.includes('credit_card')) {
				creditReasons.push({
					reasonId: "manual_payment_card_fpid_" + payerPersonId,
					reasonDescription: "Manual Payment - Credit Card"
				});
			}

			if (!excludedManualPayTypes.includes('ach')) {
				creditReasons.push({
					reasonId: "manual_payment_ach_fpid_" + payerPersonId,
					reasonDescription: "Manual Payment - ACH"
				});
			}

			if (!excludedManualPayTypes.includes('payroll_deduction') && userHasPayrollEditPermission) {
				creditReasons.push({
					reasonId: "payroll_deduction",
					reasonDescription: "Payroll Deduction"
				});
			}

			creditReasons.push({ reasonId: "security_deposit_refund_fpid_" + payerPersonId, reasonDescription: "Security Deposit Refund"});
		}

		_.each(Orgs.current().availablePayerSources(), (p) => {
			creditReasons.push({ reasonId: "reimbursable_" + p.type, reasonDescription: "Payer: " + p.description });
		});

		const userHasWriteOffEditPermission = processPermissions({
			assertions: [
				{ context: AvailablePermissions.BILLING_PAYMENTS, action: AvailableActionTypes.EDIT },
				{ context: AvailablePermissions.BILLING_PAYMENTS_CREDIT_BAD_DEBT, action: AvailableActionTypes.EDIT}
			],
			evaluator: (person) => person.type === USER_TYPES.ADMIN
		});

		if (userHasWriteOffEditPermission) {
				creditReasons.push({ reasonId:"agency_write_off", reasonDescription: "Write Off - Agency"});
				creditReasons.push({ reasonId:"bad_debt", reasonDescription: "Write Off - Bad Debt"});
				creditReasons.push({ reasonId:"collections_write_off", reasonDescription: "Write Off - Collections"});
		}
	
		creditReasons.push({ reasonId:"other", reasonDescription: "Other Credit"});

		showModal("simpleModal", {
			title: existingCredit ? "Modify Invoice Credit" : "Credit Invoice",
			template: "billingCredit",
			data: {
				invoice: invoice,
				creditReasons: creditReasons,
				existingCredit: existingCredit
			},
			onSave: async (e, i, formFieldData) => {
				formFieldData.invoiceId = invoiceId;
			
				if (parseInt(existingCreditIndex) >= 0) {
					formFieldData.existingCreditIndex = existingCreditIndex;
				}
			
				// Only perform these checks for "other" credit type with a line item
				if (formFieldData.credit_reason === "other" && formFieldData.line_item) {
					const lineItemIndex = parseInt(formFieldData.line_item);
					const lineItem = invoice.lineItems[lineItemIndex];
					
					// Calculate total amount already credited to this line item
					const amountCreditedToLineItem = invoice.credits
						?.filter(c => c.creditLineItemIndex === lineItemIndex)
						.reduce((memo, c) => memo + c.amount, 0) ?? 0;
					
					// Calculate if this credit would fully credit the line item
					const newCreditAmount = parseFloat(formFieldData.credit_amount);
					const wouldBeFullyCredited = (amountCreditedToLineItem + newCreditAmount) === lineItem.amount;
					
					if (wouldBeFullyCredited && lineItem?.originalItem?._id) {
						try {
							// Check for reservations before proceeding
							Meteor.callAsync("getReservationsForInvoiceWithItemId", invoiceId, lineItem?.originalItem?._id, invoice.personId).then((reservations) => {
								const meteorUser = Meteor.user()
								const currentPerson = meteorUser && meteorUser.fetchPerson();
								const currentOrg =  Orgs.current();
								const uniqueScheduleTypesSet = [...new Set(reservations.map(reservation => reservation.scheduleType))];
								const scheduleTypes = currentOrg.getScheduleTypes();
								const matchedScheduleTypes = uniqueScheduleTypesSet
									.map(typeId => scheduleTypes.find(type => type._id === typeId))
									.filter(scheduleType => scheduleType);
								const scheduleTypeNamesList = matchedScheduleTypes
									.map((scheduleType, index) => `${index + 1}. ${scheduleType.type}`)
									.join('\n');
								const scheduleTypeReservationsMap = _.groupBy(reservations, 'scheduleType');
								if (reservations && reservations.length > 0) {
									// Show confirmation modal for reservations
									mpSwal.fire({
										title: 'Would you like to remove the child from the following schedule(s)?',
										text: scheduleTypeNamesList,
										showCancelButton: true,
										showCloseButton: true,
										confirmButtonText: 'Adjust Invoice and Remove Schedules',
										cancelButtonText: 'Adjust Invoice Only',
										confirmButtonColor: 'var(--primary)',
										cancelButtonColor: 'var(--primary)',
										focusConfirm: false,
										focusCancel: false
									}).then(async (result) => {
										let resultMessage = ''
										if (result.value) {
											// When you click 'Adjust Invoice and Remove Schedules'
											await Meteor.callAsync('deleteReservationsByInvoiceAndBillingChargeId', invoiceId, invoice.personId, lineItem?.originalItem?._id)
											.then((res)=>{
												resultMessage += "Schedules removed successfully\n";
											})
											.catch((error)=>{
												resultMessage += "Error removing schedules: " + error.reason || error.message + "\n";
											});
						
											for (const matchedScheduleType of matchedScheduleTypes) {
												const reservations = scheduleTypeReservationsMap[matchedScheduleType._id].map(reservation => reservation._id);
												
												const historyOptions = {
													personId: invoice.personId,
													orgId: currentOrg._id,
													previousState: { selectedPerson: invoice.personId, orgId: currentOrg._id, _id: reservations },
													currentState: { selectedPerson: invoice.personId, orgId: currentOrg._id, _id: reservations },
													callbackString: HistoryAuditRecordTypes.SCHEDULE,
													changeType: HistoryAuditChangeTypes.DELETE,
													performedByUser: currentPerson,
													performedByName: `${currentPerson.firstName} ${currentPerson.lastName}`,
													details: `${matchedScheduleType.type} was removed by invoice adjustment`
												};
												await Meteor.callAsync('logHistory', historyOptions).then((res) => {
													Log.info('History record created successfully:', res);
												}).catch((error) => {
													Log.info('Error creating history record for invoice adjustment', error);
												});
											}
											this.creditInvoice(formFieldData, callback, e, 'Invoice credited successfully\nSchedules removed successfully');
										} else if (result.dismiss === 'cancel') {
											this.creditInvoice(formFieldData, callback, e, 'Invoice credited successfully');
										} else {
											$(e.target).html('Submit').prop("disabled", false);
										}
									});
								} else {
									this.creditInvoice(formFieldData, callback, e, 'Invoice credited successfully');
								}
							});
						} catch (error) {
							Log.error("Error checking reservations:", error);
						}
					} else {
						this.creditInvoice(formFieldData, callback, e, 'Invoice credited successfully');
					}
				} else {
					this.creditInvoice(formFieldData, callback, e, 'Invoice credited successfully');
				}
			}
		});
	}

	static async creditInvoice(formFieldData, callback, event, modalMessage) {
		try {
			await Meteor.callAsync("creditInvoice", formFieldData);
			
			if (callback && typeof callback === "function") {
				callback();
			}
			
			$("#simpleModal").modal("hide");
			if (modalMessage) {
				mpSwal.fire(modalMessage);
			}
		} catch (error) {
			$(event.target).html('Submit').prop("disabled", false);
			mpSwal.fire("Error", error.reason || error.message, "error");
		}
	}

	static showRefundModal(invoiceId, invoice, callback = null) {
		invoice = new Invoice(invoice);
		const charges = invoice.getRefundableCharges().charges;
		if (!charges.length) {
			mpSwal.fire("Error", "There are no refundable charges on this invoice.", "error");
			return;
		}

		showModal("simpleModal", {
			title: "Refund Charge",
			template: "billingRefund",
			data: {
				invoice,
				charges,
			},
			onSave: (e, i, formFieldData) => {
				const refundPunchCardCheckbox = $("#refundPunchCard");
				formFieldData.invoiceId = invoiceId;
				const selectedCharge = charges.find(charge => charge.id === formFieldData.charge_id);

				if (!selectedCharge) {
					mpSwal.fire("Error", "Charge selected for refund was not found", "error");
					$("#btnSave").html('Save').prop("disabled", false);
					return;
				}

				if (parseFloat(formFieldData.refund_amount) > selectedCharge.refundableAmount) {
					if (selectedCharge.refundableAmount !== selectedCharge.originalAmount) {
						const refundedAmount = selectedCharge.originalAmount - selectedCharge.refundableAmount;
						mpSwal.fire("Error", `Refund amount cannot be greater than the refundable amount. A previous refund has been requested for this charge in the amount of $${refundedAmount}, which leaves a refundable amount of $${selectedCharge.refundableAmount}`, "error");
					} else {
						mpSwal.fire("Error", "Refund amount cannot be greater than the charge amount.", "error");
					}
					$("#btnSave").html('Save').prop("disabled", false);
					return;
				}

				if (refundPunchCardCheckbox) {
					formFieldData.refundPunchCard = refundPunchCardCheckbox.prop("checked");
					Meteor.callAsync("refundPunchCard", formFieldData)
					.then((result)=>{
						Meteor.callAsync("refundCharge", formFieldData)
						.then((result)=> {

							if (callback && typeof callback === "function") {
								callback();
							}

							$("#simpleModal").modal("hide");
						})
						.catch((error)=>{
							$("#btnSave").html('Save').prop("disabled", false);
							mpSwal.fire("Error", error.reason || error.message, "error");
						});
					})
					.catch((error)=>{
						$("#btnSave").html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason || error.message, "error");
					});
				} else {
					Meteor.callAsync("refundCharge", formFieldData)
					.then((result)=> {
						if (callback && typeof callback === "function") {
							callback();
						}

						$("#simpleModal").modal("hide");
					})
					.catch((error)=>{
						$("#btnSave").html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason || error.message, "error");
					});
				}
			}
		});
	}

	static showVoidModal(invoiceId, reservations, selectedPersonId, callback = null) {
		const meteorUser = Meteor.user()
		const currentPerson = meteorUser && meteorUser.fetchPerson();
		const currentOrg =  Orgs.current();
		const uniqueScheduleTypesSet = [...new Set(reservations.map(reservation => reservation.scheduleType))];
		const scheduleTypes = currentOrg.getScheduleTypes();
		const matchedScheduleTypes = uniqueScheduleTypesSet
			.map(typeId => scheduleTypes.find(type => type._id === typeId))
			.filter(scheduleType => scheduleType);
		const scheduleTypeNamesList = matchedScheduleTypes
			.map((scheduleType, index) => `${index + 1}. ${scheduleType.type}`)
			.join('\n');
		const scheduleTypeReservationsMap = _.groupBy(reservations, 'scheduleType');

		if (reservations && reservations.length > 0) {
			mpSwal.fire({
				title: 'Would you like to remove the child from the following schedule(s)?',
				text: scheduleTypeNamesList,
				showCancelButton: true,
				showCloseButton: true,
				confirmButtonText: 'Adjust Invoice and Remove Schedules',
				cancelButtonText: 'Adjust Invoice Only',
				confirmButtonColor: 'var(--primary)',
				cancelButtonColor: 'var(--primary)',
				focusConfirm: false,
				focusCancel: false
			}).then(async (result) => {
				let resultMessage = ''
				if (result.value) {
					// When you click 'Adjust Invoice and Remove Schedules'
					await Meteor.callAsync('deleteReservationsByInvoiceId', invoiceId, selectedPersonId)
					.then((res)=>{
						resultMessage += "Schedules removed successfully\n";
					})
					.catch((error)=>{
						resultMessage += "Error removing schedules: " + error.reason || error.message + "\n";
					});

					for (const matchedScheduleType of matchedScheduleTypes) {
						const reservations = scheduleTypeReservationsMap[matchedScheduleType._id].map(reservation => reservation._id);
						
						const historyOptions = {
							personId: selectedPersonId,
							orgId: currentOrg._id,
							previousState: { selectedPerson: selectedPersonId, orgId: currentOrg._id, _id: reservations },
							currentState: { selectedPerson: selectedPersonId, orgId: currentOrg._id, _id: reservations },
							callbackString: HistoryAuditRecordTypes.SCHEDULE,
							changeType: HistoryAuditChangeTypes.DELETE,
							performedByUser: currentPerson,
							performedByName: `${currentPerson.firstName} ${currentPerson.lastName}`,
							details: `${matchedScheduleType.type} was removed by invoice adjustment`
						};
						await Meteor.callAsync('logHistory', historyOptions).then((res) => {
							Log.info('History record created successfully:', res);
						}).catch((error) => {
							Log.info('Error creating history record for invoice adjustment', error);
						});
					}
					await Meteor.callAsync("voidInvoice", {invoiceId})
					.then((res)=>{
						resultMessage += "Invoice successfully voided\n";
						if (callback && typeof callback === "function") {
							callback();
						}
					})
					.catch((error)=>{
						resultMessage += "Error voiding invoice: " + error.reason || error.message + "\n";
					});
					mpSwal.fire(resultMessage);
				} else if (result.dismiss === 'cancel') {
					await Meteor.callAsync("voidInvoice", {invoiceId})
					.then((res)=>{
						resultMessage += "Invoice successfully voided\n";
						if (callback && typeof callback === "function") {
							callback();
						}
					})
					.catch((error)=>{
						resultMessage += "Error voiding invoice: " + error.reason || error.message + "\n";
					});
					mpSwal.fire(resultMessage);
				}
			});
		} else {
			mpSwal.fire({  
				title: "Are you sure?",
				text: "This will void the invoice and set the amount open to $0.",
				showCancelButton: true,   
				confirmButtonText: "Yes, void it!"
			}).then(async result => {   
				if (result.value) {
					await Meteor.callAsync("voidInvoice", {invoiceId})
					.then((result)=>{
						if (callback && typeof callback === "function") {
							callback();
						}
						mpSwal.fire("Invoice successfully voided");
					})
					.catch((error)=>{
						mpSwal.fire("Error voiding invoice", error.reason, "error");
					});
				}
			});
		}
	}

	static showVoidCreditLineModal(invoiceId, creditIndex, callback = null) {
		Meteor.call("getInvoiceById", invoiceId, (err, invoice) => {
			if (err) {
				console.error("Error fetching invoice:", err);
				mpSwal.fire("Error", err.reason, "error");
				return;
			}
	
			const creditEntry = invoice.credits[creditIndex];
	
			showModal("simpleModal", {
				title: "Void Credit / Payment",
				template: "billingVoidCreditLine",
				data: {
					invoice: invoice,
					creditEntry
				},
				onSave: (e, i, formFieldData) => {
					formFieldData.invoiceId = invoiceId;
					formFieldData.existingCreditIndex = creditIndex;
	
					Meteor.callAsync("voidCreditLine", formFieldData)
					.then((result) => {
						if (callback && typeof callback === "function") {
							callback();
						}
						$("#simpleModal").modal("hide");
					})
					.catch((error) => {
						$(e.target).html('Submit').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				}
			});
		});
	}

	static showManualRefundCreditLineModal(invoiceId, creditIndex, callback = null) {
		mpSwal.fire( {
			title: "Refund Manual Payment",
			text: "This will mark the payment as refunded and increase the balance due on the invoice. Please enter any notes regarding the refund:",
			input: "text",
			showCancelButton: true,   
			confirmButtonText: "Refund Payment",
			inputPlaceholder: "Enter notes here"}).then(result => {   
				
				if (result.isConfirmed) {
					const formFieldData = {
						invoiceId: invoiceId,
						existingCreditIndex: creditIndex,
						notes: result.value
					};
					
					Meteor.callAsync("manualRefundCreditLine", formFieldData)
					.then((result)=>{
						if (callback && typeof callback === "function") {
							callback();
						}

						$("#simpleModal").modal("hide");
					})
					.catch((error)=>{
						$(e.target).html('Submit').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				}
			});
		
	}

	static showReverseLineItemModal(options, callback = null) {
		mpSwal.fire( {
			title: "Reverse Reallocation",
			text: "Notice: You are about to reverse this allocation back to its original status. This may result in open family or payer balances. Are you sure you want to continue?",
			input: "text",
			showCancelButton: true,
			confirmButtonText: "Continue",
			inputPlaceholder: "Enter notes here"}).then(result => {
			if (result.isConfirmed) {
				const formFieldData = {
					invoice: options.invoice,
					existingCreditIndex: options.creditIndex,
					existingAllocationIndex: options.allocationIndex,
					createdInvoice: options.createdInvoice,
					notes: result.value
				};
				Meteor.callAsync("manualReverseReallocation", formFieldData)
				.then((result)=>{
					if (callback && typeof callback === "function") {
						callback();
					}
					$("#simpleModal").modal("hide");
				})
				.catch((error)=>{
					$(e.target).html('Submit').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});

	}

	static showAdjustLineItemModal(options, callback = null) {
		showModal("adjustLineItemModal", {
			invoice: options.invoice,
			person: options.person,
			creditIndex: options.creditIndex,
			onSave: (e, i, formFieldData) => {
				const creditAmount = parseFloat(options.invoice.credits[options.creditIndex]?.amount);
				const selectedType = formFieldData.find(obj => obj.name === 'assignmentType').value;
				const adjustOptions = {
					invoice: options.invoice,
					creditIndex: options.creditIndex,
					selectedInvoice: null,
					invoiceAmount: 0,
					memoAmount: 0,
				};

				if (selectedType === 'invoice') {
					const selectedAmount = parseFloat(formFieldData.find(obj => obj.name === 'creditAmount').value);
					const selectedInvoice = formFieldData.find(obj => obj.name === 'selectedInvoice').value;
					const invoiceAmount = selectedInvoice.openAmount;

					adjustOptions.selectedInvoice = selectedInvoice;

					if (creditAmount < selectedAmount) {
						mpSwal.fire("Error", "The amount to apply cannot be greater than the credit amount", "error");
						return;
					} else if (invoiceAmount < selectedAmount) {
						mpSwal.fire({
							text: "Please note that the adjusted amount exceeds the open balance on the selected invoice; the rest will be applied to a credit memo",
							showCancelButton: true,
							confirmButtonText: "Proceed",
						}).then(result => {
							if (!result.isConfirmed) {
								return;
							}
							// pay partial amount to invoice, generate credit memo path
							adjustOptions.invoiceAmount = invoiceAmount;
							adjustOptions.memoAmount = selectedAmount - invoiceAmount;
							Meteor.callAsync("adjustCreditLine", adjustOptions)
							.then((result)=>{
								handleAdjustCreditLineResult(undefined, callback);
							})
							.catch((error)=>{
								handleAdjustCreditLineResult(error)
							});
						});
					} else {
						// pay full amount to invoice path
						adjustOptions.invoiceAmount = selectedAmount;
						Meteor.callAsync("adjustCreditLine", adjustOptions)
						.then((result)=>{
							handleAdjustCreditLineResult(undefined, callback);
						})
						.catch((error)=>{
							handleAdjustCreditLineResult(error)
						});
					}
				} else {
					// generate credit memo path
					adjustOptions.memoAmount = creditAmount;
					Meteor.callAsync("adjustCreditLine", adjustOptions)
					.then((result)=>{
						handleAdjustCreditLineResult(undefined, callback);
					})
					.catch((error)=>{
						handleAdjustCreditLineResult(error)
					});
				}
			}
		});

		function handleAdjustCreditLineResult(error, callback) {
			if (error) {
				mpSwal.fire("Error", error.reason, "error");
			} else {
				if (callback && typeof callback === "function") {
					callback();
				}

				$("#adjustLineItemModal").modal("hide");
			}
		}
	}
};
