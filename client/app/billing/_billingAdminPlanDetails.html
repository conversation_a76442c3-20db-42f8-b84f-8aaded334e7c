<template name="billingAdminPlanDetails">
    <div class="form-group row">
         <h2 class="col-xl-3 col-lg-3 text-right">Details</h2>
    </div>
    <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Date Type</label>
        <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
            <select data-cy="add-plan-date-type" class="form-control" id="dateTypeSelect" name="date_type">
                <option value=""></option>
                {{#if trueIfEq definitionType 'item'}}
                    <option value="dateRange" {{selectedIfEqual dateTypeSelected 'dateRange'}}>Date Range</option>
                    <option value="recurring" {{selectedIfEqual dateTypeSelected 'recurring'}}>Recurring</option>
                    <option value="individualDates" {{selectedIfEqual dateTypeSelected 'individualDates'}}>Individual Date(s)</option>
                {{/if}}
                {{#if trueIfEq definitionType 'plan'}}
                    <option value="timePeriod" {{selectedIfEqual dateTypeSelected 'timePeriod'}}>Time Period</option>
                    {{# if canShowSelectiveWeeksOption }}
                        <option value="timePeriodSelectiveWeeks" {{selectedIfEqual dateTypeSelected 'timePeriodSelectiveWeeks'}}>
                            Time Period with Selective Weeks
                        </option>
                    {{/ if }}
                {{/if}}
            </select>
        </div>
    </div>
    <div style="{{#unless trueIfEq dateTypeSelected 'dateRange'}}display:none;{{/unless}}">
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Service Date(s)</label>
                <div class="col-lg-2 col-md-3 pr-0">
                    <input id="serviceStartDate" class="form-control form-control-solid" type="text" name="service_start_date" value="{{formatDate detailsInfo.serviceStartDate 'MM/DD/YYYY'}}">
                </div>
                <div class="mx-0 col-1 text-center align-self-center">
                    <span class="font-size-h2">&mdash;</span>
                </div>
                <div class="col-lg-2 col-md-3 pl-0">
                    <input id="serviceEndDate" class="form-control form-control-solid" type="text" name="service_end_date" value="{{formatDate detailsInfo.serviceEndDate 'MM/DD/YYYY'}}">
                </div>
            </div>
        </div>
    <div style="{{#unless trueIfEq dateTypeSelected 'recurring'}}display:none;{{/unless}}">
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Start Date</label>
                <div class="col-lg-2 col-md-3 pr-0">
                    <input id="recurringStartDate" data-cy="recurring-start-date" class="form-control form-control-solid" type="text" name="recurring_start_date" value="{{formatDate detailsInfo.recurringStartDate 'MM/DD/YYYY'}}">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
                <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
                    <label>
                        Repeats every
                        <input type="text" class="form-control form-control-solid" data-cy="recurring_frequency" name="recurring_frequency" style="width:100px;display:inline" value="{{detailsInfo.recurringFrequency}}">
                        week(s) for
                        <input type="text" class="form-control form-control-solid" data-cy="recurring_occurrences" name="recurring_occurrences" style="width:100px;display:inline" value="{{detailsInfo.recurringOccurrences}}">
                        occurrences
                    </label>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
                <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
                    <div class="checkbox-inline">
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_sun" name="recursSun" {{checkedForDay 'sun'}}>
                            <span></span>
                            Sun
                        </label>
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_mon" name="recursMon" {{checkedForDay 'mon'}}>
                            <span></span>
                            Mon
                        </label>
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_tue" name="recursTue" {{checkedForDay 'tue'}}>
                            <span></span>
                            Tue
                        </label>
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_wed" name="recursWed" {{checkedForDay 'wed'}}>
                            <span></span>
                            Wed
                        </label>
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_thu" name="recursThu" {{checkedForDay 'thu'}}>
                            <span></span>
                            Thu
                         </label>
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_fri" name="recursFri" {{checkedForDay 'fri'}}>
                            <span></span>
                            Fri
                        </label>
                        <label class="checkbox checkbox-primary">
                            <input type="checkbox" data-cy="recurring_sat" name="recursSat" {{checkedForDay 'sat'}}>
                            <span></span>
                            Sat
                        </label>
                </div>
                </div>
            </div>
        </div>
    <div style="{{#unless trueIfEq dateTypeSelected 'individualDates'}}display:none;{{/unless}}">
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Service Date(s)</label>
            <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
                {{#if rows}}
                    {{#each (repeat rows)}}
                        {{#let rowIndex=@index}}
                            <div class="form-group row">
                                {{#each date in (columns rowIndex)}}
                                    <div class="col-3 pr-0">
                                        <input data-cy="service-datepicker" class="form-control form-control-solid individual-service-datepicker" type="text"
                                               data-id="{{date.index}}" value="{{formatDate date.value 'MM/DD/YYYY'}}">
                                    </div>
                                    <div class="col-1 pl-0 text-center align-self-center">
                                        <button class="btn btn-icon btn-clean btn-sm btn-remove-individual-service-date" data-id="{{date.index}}">
                                            <span class="fad-regular fad-primary fad fa-times"></span>
                                        </button>
                                    </div>
                                {{/each}}
                            </div>
                        {{/let}}
                    {{/each}}
                {{/if}}
                <button data-cy="add-days-btn" class="btn btn-primary font-weight-bolder" id="btnAddDays">
                    <i class="fad fa-swap-opacity fa-plus mr-2"></i>Add Day(s)
                </button>
            </div>
        </div>
    </div>
    {{#if showTimePeriodSelect}}
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Service Date(s)</label>
            <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
                <select data-cy="add-plan-service-dates" class="form-control" id="timePeriodsSelect" name="time_period">
                    <option value=""></option>
                    {{#each availableTimePeriods}}
                        <option value="{{_id}}" {{selectedIfEqual detailsInfo.timePeriod _id}}>{{name}} ({{formatDate startDate "MM/DD/YYYY"}} - {{formatDate endDate "MM/DD/YYYY"}})</option>
                    {{/each}}
                </select>
            </div>
        </div>
    {{/if}}

    {{#if showWeekendCheckboxes}}
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right">Do not create schedules on</label>
            <div class="col-lg-6 col-md-9 col-sm-12">
                <div class="checkbox-list">
                    <label class="checkbox">
                        <input type="checkbox" name="excludeSaturdays" id="excludeSaturdays" checked="{{detailsInfo.excludeSaturdays}}">
                        <span></span>
                        Saturdays
                    </label>
                    <label class="checkbox">
                        <input type="checkbox" name="excludeSundays" id="excludeSundays" checked="{{detailsInfo.excludeSundays}}">
                        <span></span>
                        Sundays
                    </label>
                </div>
            </div>
        </div>
    {{/if}}

    {{# if showTimePeriodWeekSelections }}
        <div class="form-group row">
            <div class="col-xl-3 col-lg-3 text-right col-form-label"></div>
            <div class="col-6">
                <div class="row">
                    <div class="col-3 text-left"><strong>Amount</strong></div>
                    <div class="col-3 text-left"><strong>Dates</strong></div>
                    <div class="col-6 text-center"></div>
                </div>
            </div>
        </div>
        {{# each selectiveWeek in selectiveWeeks }}
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Week {{ incremented @index }}</label>
                <div class="col-6">
                    <div class="row">
                        <div class="col-3">
                            <input class="form-control form-control-solid selective-week-amount" type="text"
                                   placeholder="Amount" pattern="[0-9]+([\.][0-9]+)?" data-amount-id="{{ @index }}"
                                   value="{{ formatNumber (getPlanAmount @index) "0.00"}}" required>
                        </div>
                        <div class="col-3">
                            <input class="form-control form-control-solid selective-week-datepicker-start" type="text"
                                   data-id="{{ @index }}"
                                   value="{{ formatDate (selectiveWeekStart selectiveWeek) 'MM/DD/YYYY' }}">
                        </div>
                        <div class="col mx-0 text-center align-self-center">
                            <span class="font-size-h2">&mdash;</span>
                        </div>
                        <div class="col-3">
                            <input class="form-control form-control-solid selective-week-datepicker-end" type="text"
                                   data-id="{{ @index }}"
                                   value="{{ formatDate (selectiveWeekEnd selectiveWeek) 'MM/DD/YYYY' }}">
                        </div>
                        <div class="col text-center align-self-center">
                            {{# if canDeleteSelectiveWeek }}
                                <button class="btn btn-icon btn-clean btn-sm btn-remove-selective-week-date"
                                        data-id="{{ @index }}">
                                    <span class="fad-regular fad-primary fad fa-times"></span>
                                </button>
                            {{/ if }}
                        </div>
                    </div>
                </div>
            </div>
        {{/ each }}
        {{# if canAddSelectiveWeek }}
            <div class="form-group row">
                <div class="offset-3">
                    <button class="btn btn-primary font-weight-bolder ml-4" id="btnAddSelectiveWeek">
                        <i class="fad fa-swap-opacity fa-plus mr-2"></i>Add Week
                    </button>
                </div>
            </div>
        {{/ if }}
    {{/ if }}

    <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Times</label>
        <div class="col-lg-2 col-md-3 pr-0">
            <input data-cy="add-plan-start-time" id="startTime" class="form-control form-control-solid" type="time" name="start_time" value="{{formatTime detailsInfo.startTime}}">
        </div>
        <div class="mx-0 col-1 text-center align-self-center">
            <span class="font-size-h2">&mdash;</span>
        </div>
        <div class="col-lg-2 col-md-3 pl-0">
            <input data-cy="add-plan-end-time" id="endTime" class="form-control form-control-solid" type="time" name="end_time" value="{{formatTime detailsInfo.endTime}}">
        </div>
    </div>
    <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Registration Window</label>
        <div class="col-lg-2 col-md-3 pr-0">
            <input data-cy="add-plan-reg-start-date" id="regStartDate" class="form-control form-control-solid" type="text" name="reg_start_date" value="{{formatDate detailsInfo.regStartDate 'MM/DD/YYYY'}}">
        </div>
        <div class="mx-0 col-1 text-center align-self-center">
            <span class="font-size-h2">&mdash;</span>
        </div>
        <div class="col-lg-2 col-md-3 pl-0">
            <input data-cy="add-plan-reg-end-date" id="regEndDate" class="form-control form-control-solid" type="text" name="reg_end_date" value="{{formatDate detailsInfo.regEndDate 'MM/DD/YYYY'}}">
        </div>
    </div>
    <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Eligible Grades</label>
        <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
            <select data-cy="add-plan-grades" multiple class="form-control form-control-lg" id="gradesMultiselect" name="grades[]" size="1">
                 {{#each grade in grades}}
                    <option value="{{grade}}" {{selectedIfContains detailsInfo.grades grade}}>{{grade}}</option>
                {{/each}}
            </select>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Link to Schedule</label>
        <div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
            <select data-cy="add-plan-schedule-type" class="form-control" id="scheduleType" name="schedule_type">
                <option value=""></option>
                {{#each scheduleTypes}}
                    <option value="{{_id}}" {{selectedIfEqual detailsInfo.scheduleType _id}}>{{type}}</option>
                {{/each}}
            </select>
        </div>
    </div>
</template>