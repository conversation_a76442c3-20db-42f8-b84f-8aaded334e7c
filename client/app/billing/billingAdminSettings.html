<template name="billingAdminSettings">
    <div class="box box-solid content-box descriptive-content">
        <div class="box-body">
            <h3>Status</h3>
            <div class="row">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Billing Status
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    <span style="color:{{billingInfo.messageColor}}">{{billingInfo.billingStatus.message}}</span><br/>
                    {{#if showDisableButton}}
                        <div class="btn btn-primary font-weight-bolder mt-2" id="btnDisableBilling">Disable All Billing
                            Features
                        </div>
                        <br/>
                    {{/if}}
                    <br/>
                    {{#if billingInfo.suspendInvoicing}}
                        <span style="font-weight:bold">Invoicing suspended
                            {{#if billingInfo.suspendInvoicingUntilDate}}
                                until {{formatDate billingInfo.suspendInvoicingUntilDate "M/D/YYYY"}}
                            {{else}}
                                indefinitely
                            {{/if}}
							</span><br/>
                        <div class="btn btn-primary font-weight-bolder mt-2" id="btnResumeInvoicing">Resume
                            Invoicing
                        </div>
                    {{else}}
                        You may suspend all invoices from being generated indefinitely or for a period of time.<br/>
                        <div class="btn btn-primary font-weight-bolder mt-2" id="btnSuspendInvoicing">Suspend
                            Invoicing
                        </div>
                    {{/if}}
                </div>
            </div>
            <div class="separator separator-dashed my-8"></div>
            <div class="row mb-2">
                <div class="col-8">
                    <h3>Registration Fees and Time Periods</h3>
                </div>
            </div>
            {{> _billingRegFee }}
            <div class="row mt-4 mb-4">
                <div class="col-2 offset-1">
                    <h4>Time Periods</h4>
                </div>
                <div class="col-7">
                    Create time periods for collecting registration fees
                </div>
                <div class="col-1">
                    <button class="btn btn-primary" id="btnConfigureTimePeriods" data-cy="configure-time-period">
                        Configure
                    </button>
                </div>
            </div>
            {{#if showTimePeriodsConfig }}
            <div class="row">
                <div class="offset-2 col-7">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>Name</th>
                            <th>Start</th>
                            <th>End</th>
                            <th class="d-flex justify-content-end">
                                {{# if showEditOptions }}
                                    <button class="btn btn-primary" id="btnAddNewTimePeriod" data-cy="add-new-time-period">
                                        <i class="fad fa-plus fa-swap-opacity text-white"></i>
                                        Add
                                    </button>
                                {{/if}}
                            </th>
                        </tr>
                        </thead>
                        <tbody data-cy="time-period-table">
                        {{# each period in availableTimePeriods }}
                            <tr data-cy="time-period" data-id="{{ period._id }}" class="timePeriodDisplayRow">
                                <td data-cy="time-period-name">{{period.name}}</td>
                                <td data-cy="time-period-start-date">{{formatDate period.startDate 'MM/DD/YYYY'}}</td>
                                <td data-cy="time-period-end-date">{{formatDate period.endDate 'MM/DD/YYYY'}}</td>
                                <td class="d-flex justify-content-end">{{# if showEditOptions }}
                                    <div class="d-flex flex-row">
                                        <a data-cy="edit-time-period" href="#" class="btnEditTimePeriod" data-id="{{ period._id }}"
                                           style="margin-right:10px"
                                        >
                                            <i class="fad fad-primary fa-pencil"></i>
                                        </a>
                                    </div>
                                {{/if}}
                                </td>
                            </tr>
                            <tr data-cy="edit-period-{{ period._id }}" data-id="{{ period._id }}" class="timePeriodEditRow" style="display: none">
                                <td colspan="12">
                                    <form class="frmTimePeriodEdit" data-id="{{ period._id }}">
                                        <div class="row">
                                            <div class="col-3">
                                                <input data-cy="period-name-edit" 
                                                        type="text"
                                                        class="form-control txtTimePeriodName"
                                                        value="{{period.name}}"
                                                >
                                            </div>
                                            <div class="col-3">
                                                <input data-cy="period-start-edit"
                                                        type="date"
                                                        class="form-control dateTimePeriodStart"
                                                        value="{{formatDateToString period.startDate}}"
                                                >
                                            </div>
                                            <div class="col-3">
                                                <input data-cy="period-end-edit"
                                                        type="date"
                                                        class="form-control dateTimePeriodEnd"
                                                        value="{{formatDateToString period.endDate}}"
                                                >
                                            </div>
                                            <div class="d-flex justify-content-end col-3">
                                                <button data-cy="save-edit" class="btn btn-primary font-weight-bolder btn-sm btnSaveEditTimePeriod mr-2"
                                                        data-id="{{ period._id }}"
                                                >
                                                    Save
                                                </button>
                                                <button class="btn btn-secondary font-weight-bolder btn-sm btnCancelEditTimePeriod"
                                                        data-id="{{ period._id }}"
                                                >
                                                    Cancel
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </td>
                            </tr>
                        {{/each}}
                        </tbody>
                    </table>
                    <div id="rowAddTimePeriod" class="row ml-5" style="display:none">
                        <form id="frmAddTimePeriod">
                            <div>
                                <div class="row">
                                    <div class="col-4">
                                        <input data-cy="period-name-input"
                                                type="text"
                                                id="txtTimePeriodName"
                                                class="form-control"
                                                placeholder="Name"
                                        >
                                    </div>
                                    <div class="col-4">
                                        <input data-cy="period-start-input"
                                                type="date"
                                                id="dateTimePeriodStart"
                                                class="form-control"
                                                placeholder="Start Date"
                                        >
                                    </div>
                                    <div class="col-4">
                                        <input data-cy="period-end-input"
                                                type="date"
                                                id="dateTimePeriodEnd"
                                                class="form-control"
                                                placeholder="End Date"
                                        >
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-2 mt-4">
                                <button data-cy="save-time-period" class="btn btn-primary font-weight-bolder btn-sm" id="btnSaveTimePeriod"
                                        data-id="{{ period._id }}">Save
                                </button>
                                <button class="btn btn-secondary font-weight-bolder btn-sm" id="btnCancelAddTimePeriod"
                                        data-id="{{ period._id }}">Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {{/if}}
            <div class="separator separator-dashed my-8"></div>
            <div class="row">
                <div class="col-xl-3 col-lg-3">
                    <h3>Legal Entity</h3>
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#if showEditOptions}}
                        <div class="btn btn-primary font-weight-bolder" id="btnEditLegalEntity"><i
                                class="fad fa-pen-square text-white mr-2"></i>Edit
                        </div>
                    {{/if}}
                </div>
            </div>
            <div class="row mt-6">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Legal Entity Name
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.legalEntity.business_name}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Tax ID Number
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#if billingInfo.legalEntity.business_tax_id}}
                        On file
                    {{/if}}<br/>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Legal Entity Address
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.legalEntity.address}}<br/>
                    {{billingInfo.legalEntity.city}}
                    , {{billingInfo.legalEntity.state}} {{billingInfo.legalEntity.zipcode}}<br/>
                </div>
            </div>
            <div class="separator separator-dashed my-8"></div>
            <div class="row">
                <div class="col-xl-3 col-lg-3">
                    <h3>Schedule</h3>
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#if showEditOptions}}
                        <div class="btn btn-primary font-weight-bolder btnEditSchedule"><i
                                class="fad fa-pen-square text-white mr-2"></i>Edit
                        </div>
                    {{/if}}
                </div>
            </div>
            <div class="row mt-6">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Generate Invoices
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.generateWhenLabel}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Weekly Invoicing Schedule
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.generateWeeklyDescription}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Monthly Invoicing Schedule
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.generateMonthlyDescription}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Bi-weekly Seed Date
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.generateBiWeeklyDate}}
                    {{#if billingInfo.nextBiWeeklyDate}}
                        <br/>(Next run date: {{formatDate billingInfo.nextBiWeeklyDate "M/D/YYYY"}})
                    {{/if}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Grace Period (Days)
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.gracePeriodDays}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Allow Payments Before Due Date
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{billingInfo.allowPaymentsBeforeDueDate}}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Missed Invoice Interval
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{ billingInfo.missedInvoiceInterval }}
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Automatic Late Fees
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#if billingInfo.automaticLateFees.active}}
                        Active<br/>
                        Item: {{billingInfo.automaticLateFees.lateFeeItem}}<br/>
                        Days Late to Assess: {{billingInfo.automaticLateFees.lateFeeDays}}<br/>
                    {{else}}
                        Inactive<br/>
                    {{/if}}
                </div>
            </div>
            <!--
				<div class="row mt-4">
					<div class="col-xl-3 col-lg-3 text-right font-size-h6">
						Late fee
					</div>
					<div class="col-lg-6 col-md-9 col-sm-12">
						{{formatCurrency billingInfo.lateFee}}<br/>
					</div>
				</div>
			-->
            <div class="separator separator-dashed my-8"></div>
           {{> billingAdminDiscountsAndPayers}}
            <div class="separator separator-dashed my-8"></div>
            {{# if hasCustomization getRegistrationFlowCustomizationName }}
                <div class="row">
                    <div class="col-xl-3 col-lg-3">
                        <h3>Plans and Items Settings</h3>
                    </div>
                </div>
                <div class="row mt-6">
                    <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                        Programs
                    </div>
                    <div class="col-lg-6 col-md-9 col-sm-12">
                        <div data-cy="manage-programs" class="btn btn-primary font-weight-bolder" id="btnShowPrograms">Manage Programs</div>
                        <div id="programsPanel" style="display:none;padding: 10px;">
                            <div data-cy="hide-programs" class="btn btn-primary font-weight-bolder" id="btnHidePrograms">Hide Programs</div>
                            <table class="table mt-4">
                                <tbody>
                                <tr>
                                    <th>Name</th>
                                    <th>Status</th>
                                    <th>Requires 7 Days Advance Notice?</th>
                                    <th></th>
                                </tr>
                                {{# each program in availablePrograms }}
                                    <tr data-cy="program-data" data-id="{{ program._id }}" class="programDisplayRow">
                                        <td data-cy="program-name">{{ program.name }}</td>
                                        <td data-cy="program-status">{{ getProgramStatus program.isActive }}</td>
                                        <td>{{ getProgramRequiresAdvanceNotice program.isRequiredAdvanceNotice }}</td>
                                        <td>{{# if showEditOptions }}
                                            <div class="d-flex flex-row">
                                                <a data-cy="edit-program" href="#" class="btnEditProgram" data-id="{{ program._id }}"
                                                   style="margin-right:10px"><i
                                                        class="fad fad-primary fa-pencil"></i></a>
                                            </div>
                                        {{/if}}
                                        </td>
                                    </tr>
                                    <tr data-cy="edit-program-data-{{ program._id }}" style="display:none" data-id="{{ program._id }}" class="programEditRow">
                                        <td colspan=10>
                                            <form class="frmProgramEdit" data-id="{{ program._id }}">
                                                <div class="col-xs-5">
                                                    <input data-cy="program-name-edit" type="text" class="txtProgramName form-control"
                                                           placeholder="Name" value="{{ program.name }}">
                                                </div>
                                                <div class="col-xs-3 mt-2">
                                                    <select data-cy="program-status-edit" class="selectProgramStatus form-control">
                                                        <option value="active" {{ selectedIfEqual program.isActive
                                                                                                  true }}>Active
                                                        </option>
                                                        <option value="inactive" {{ selectedIfEqual program.isActive
                                                                                                    false }}>Inactive
                                                        </option>
                                                    </select>
                                                </div>
                                                <div class="col-xs-2 mt-2">
                                                    <div class="checkbox-inline">
                                                        <label class="checkbox checkbox-primary">
                                                            <input data-cy="check-requires-advance-notice-edit" type="checkbox"
                                                                   class="chkProgramRequiresAdvanceNotice"
                                                                   checked={{ program.isRequiredAdvanceNotice }} />
                                                            <span></span>
                                                            Requires 7 Days Advance Notice?
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-xs-2 mt-2">
                                                    <button data-cy="save-edit-program" class="btn btn-primary font-weight-bolder btn-sm btnSaveEditProgram mr-2"
                                                            data-id="{{ program._id }}">Save
                                                    </button>
                                                    <button class="btn btn-secondary font-weight-bolder btn-sm btnCancelEditProgram"
                                                            data-id="{{ program._id }}">Cancel
                                                    </button>
                                                </div>
                                            </form>
                                        </td>
                                    </tr>
                                {{/each}}
                                </tbody>
                            </table>
                            {{# if showEditOptions }}
                                <div data-cy="add-new-program" class="btn btn-primary font-weight-bolder" id="btnAddNewProgram">
                                    <i class="fad fa-plus fa-swap-opacity text-white mr-2"></i>Add New Program
                                </div>
                            {{/ if }}
                            <div id="rowAddProgram" class="row ml-4" style="display:none">
                                <form id="frmAddProgram">
                                    <div class="col-xs-5">
                                        <input data-cy="add-program-name" type="text" id="txtProgramName" class="form-control" placeholder="Name"/>
                                    </div>
                                    <div class="col-xs-3 mt-2">
                                        <select data-cy="add-program-status" id="selectProgramStatus" class="form-control">
                                            <option value="active">Active</option>
                                            <option value="inactive" selected>Inactive</option>
                                        </select>
                                    </div>
                                    <div class="col-xs-2 mt-2">
                                        <div class="checkbox-inline">
                                            <label class="checkbox checkbox-primary">
                                                <input data-cy="chk-requires-advance-notice" type="checkbox" id="chkProgramRequiresAdvanceNotice"/>
                                                <span></span>
                                                Requires 7 Days Advance Notice?
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 mt-4">
                                        <button data-cy="save-new-program" class="btn btn-primary font-weight-bolder btn-sm" id="btnSaveProgram">
                                            Save
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="separator separator-dashed my-8"></div>
            {{/ if }}
            {{> billingAdminPaymentSettings}}
            <div class="separator separator-dashed my-8"></div>
            <div class="row">
                <div class="col-xl-3 col-lg-3">
                    <h3>Options</h3>
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#if showEditOptions}}
                        <div class="btn btn-primary font-weight-bolder btnEditOptions"><i
                                class="fad fa-pen-square text-white mr-2"></i>Edit
                        </div>
                    {{/if}}
                </div>
            </div>
            <div class="row mt-6">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Discount Calculation
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#unless billingInfo.toplinePercentDiscounts}}
                        Percent discounts calculated after subtracting all dollar discounts (default).
                    {{else}}
                        Percent discounts calculated against invoice total.
                    {{/unless}}
                </div>
            </div>
            <div class="row mt-6">
                <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                    Deposit Reports
                </div>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    {{#unless billingInfo.allowDepositReportEdit}}
                        Modification to Bank Deposit Reports are disabled.
                    {{else}}
                        Modification to Bank Deposit Reports are enabled.
                    {{/unless}}
                </div>
            </div>
            {{#if availableBillingMaps}}
                <div class="row mt-6">
                    <div class="col-xl-3 col-lg-3 text-right font-size-h6">
                        Ledger Billing Maps
                    </div>
                    <div class="col-lg-6 col-md-9 col-sm-12">
                        <div class="btn btn-primary font-weight-bolder" id="btnShowBillingMaps">Manage Billing Maps
                        </div>
                        <div id="billingMapsPanel" style="display:none;padding: 10px;">
                            <div class="btn btn-primary font-weight-bolder" id="btnHideBillingMaps">Hide BillingMaps
                            </div>
                            <table class="table mt-4">
                                <tbody>
                                <tr>
                                    <th>Description</th>
                                    <th>Ledger Account</th>
                                    <th></th>
                                </tr>
                                {{#each availableBillingMaps}}
                                    <tr>
                                        <td>{{description}}</td>
                                        <td>{{accountName}}</td>
                                        <td><a href="#" class="edit-field btnEditBillingMap" data-id="{{code}}"><i
                                                class="fad fad-primary fa-pencil mr-3"></i>Edit</a></td>
                                    </tr>
                                {{/each}}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {{/if}}
				<div class="row mt-6">
                    {{#if showRunInvoices}}
                        <div class="col-sm-4">
							<div class="btn btn-primary font-weight-bolder" id="btnRunInvoices">Run Invoices Now</div>
                        </div>
                        <div class="col-sm-4">
                            <div class="btn btn-primary font-weight-bolder" id="btnRunAutopay">Run Auto Pay Now</div>
                        </div>
                        <!-- delete after BUGS-2915 passes QA -->
                        <div class="col-sm-4">
                            <div class="btn btn-primary font-weight-bolder" id="btnRunPrecomputeForOrg">Run Precompute Now</div>
                        </div>
                    {{/if}}
				</div>
			</div>
		</div>
	</template>
	
	<template name="billingSettingsLegalEntity">
		<form id="frmBankAccount">
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Business Legal Name</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="business_name" value="{{business_name}}" required>
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Tax ID Number</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="business_tax_id" value="{{business_tax_id}}" >
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Address</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="address" value="{{address}}" required>
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">City</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="city" value="{{city}}" required>
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">State</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="state" value="{{state}}" required>
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Zipcode</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="zipcode" value="{{zipcode}}" required>
				</div>
			</div>
		</form>
	</template>
	
	<template name="billingSettingsModalBankAccount">
		<form id="frmBankAccount">
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Routing/ABA Number</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="routing_number" required>
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Bank Account Number</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="account_number" required>
				</div>
			</div>
		</form>
	</template>
	
	<template name="billingSettingsModalOptions">
		<form id="frmOptions">
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Discount Calculation</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<select name="discount_calculation" size=1 class="form-control">
						<option value="afterDollars" {{selectedIfEqual discountCalculation "afterDollars"}}>Percent discounts applied after all dollar discounts</option>
						<option value="topline"  {{selectedIfEqual discountCalculation "topline"}}>Percent discounts calculated against invoice total</option>
					</select>
				</div>
			</div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Deposit Report</label>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    <select name="allow_deposit_report_edit" size=1 class="form-control">
                        <option value="false" {{selectedIfEqual allowDepositReportEdit false}}>Disable Modifications to Bank Deposit Report</option>
                        <option value="true" {{selectedIfEqual allowDepositReportEdit true}}>Allow Modifications to Bank Deposit Report</option>
                    </select>
                </div>
            </div>
            <div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Registration Discount Types</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<select id="registrationDiscountTypes" name="registration_discount_types[]" size=1 multiple class="form-control">
                        <option {{selectedIfContains discountsAllowed "coupon_codes"}} value="coupon_codes"> Coupon Codes </option>
                        <option {{selectedIfContains discountsAllowed "subsidy_approval_process"}} value="subsidy_approval_process"> Subsidy Approval Process </option>
                        <option {{selectedIfContains discountsAllowed "district_employee_approval_process"}} value="district_employee_approval_process"> District Employee Approval Process </option>
                        {{#if showEmployeeId}}
                            <option {{selectedIfContains discountsAllowed "employee_id"}} value="employee_id"> Employee ID </option>
                        {{/if}}
					</select>
				</div>
			</div>
		</form>
	</template>
	
	<template name="billingSettingsModalSchedule">
		<form id="frmSchedule">
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Generate Invoices</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<select name="generate_when" size=1 class="form-control">
						<option value="advance" {{selectedIfEqual generateWhen "advance"}}>In Advance</option>
						<option value="arrears"  {{selectedIfEqual generateWhen "arrears"}}>In Arrears</option>
					</select>
				</div>
			</div>
			<div class="form-group row" style="border-top:1px solid #ccc;padding-top:16px">
				<label class="col-xl-3 col-lg-3 text-right col-form-label"><b>Weekly Schedule</b></label>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Invoicing Day of Week</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<select name="generate_day" class="form-control">
						<option value="sunday" {{selectedIfEqual generateDay "sunday"}}>Sunday</option>
						<option value="monday" {{selectedIfEqual generateDay "monday"}}>Monday</option>
						<option value="tuesday" {{selectedIfEqual generateDay "tuesday"}}>Tuesday</option>
						<option value="wednesday" {{selectedIfEqual generateDay "wednesday"}}>Wednesday</option>
						<option value="thursday" {{selectedIfEqual generateDay "thursday"}}>Thursday</option>
						<option value="friday" {{selectedIfEqual generateDay "friday"}}>Friday</option>
						<option value="saturday" {{selectedIfEqual generateDay "saturday"}}>Saturday</option>
					</select>
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label pt-0">Weekly Plan Due Date</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<div class="form-check">
						<input class="form-check-input" type="radio" name="weekly_plan_due" id="weekly_grace" value="graceperiod" {{checkedIfEq useWeeklyPlanDue "graceperiod"}}>
						<label class="form-check-label" for="weekly_grace">
						  Use Grace Period
						</label>
					</div>
					<div class="form-check">
						<input class="form-check-input" type="radio" name="weekly_plan_due" id="weekly_fixed" value="fixed" {{checkedIfEq useWeeklyPlanDue "fixed"}}>
						<label class="form-check-label" for="weekly_fixed">
							Fixed Day of Week (next occurring):
						</label>
						<select class="form-control" name="weekly_plan_due_day">
							<option value="sunday" {{selectedIfEqual weeklyPlanDueDay "sunday"}}>Sunday</option>
							<option value="monday" {{selectedIfEqual weeklyPlanDueDay "monday"}}>Monday</option>
							<option value="tuesday" {{selectedIfEqual weeklyPlanDueDay "tuesday"}}>Tuesday</option>
							<option value="wednesday" {{selectedIfEqual weeklyPlanDueDay "wednesday"}}>Wednesday</option>
							<option value="thursday" {{selectedIfEqual weeklyPlanDueDay "thursday"}}>Thursday</option>
							<option value="friday" {{selectedIfEqual weeklyPlanDueDay "friday"}}>Friday</option>
							<option value="saturday" {{selectedIfEqual weeklyPlanDueDay "saturday"}}>Saturday</option>
						</select>
					</div>
				</div>
			</div>
			<div class="form-group row" style="border-top:1px solid #ccc;padding-top:16px">
				<label class="col-xl-3 col-lg-3 text-right col-form-label"><b>Monthly Schedule</b></label>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Invoicing Day of Month</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="number" class="form-control" name="generate_monthday" value="{{generateMonthDay}}" required min="0" max="31">
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label pt-0">Monthly Plan Due Date</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<div class="form-check">
						<input class="form-check-input" type="radio" name="monthly_plan_due" id="monthly_grace" value="graceperiod" {{checkedIfEq useMonthlyPlanDue "graceperiod"}}>
						<label class="form-check-label" for="exampleRadios1">
						  Use Grace Period
						</label>
					</div>
					<div class="form-check">
						<input class="form-check-input" type="radio" name="monthly_plan_due" id="monthly_fixed" value="fixed" {{checkedIfEq useMonthlyPlanDue "fixed"}}>
						<label class="form-check-label" for="exampleRadios1">
							Fixed Day of Month (next occurring):
						</label>
						<input type="number" class="form-control" name="monthly_plan_due_day" value="{{monthlyPlanDueDay}}" min="0" max="31">
					</div>
				</div>
			</div>
			
			<div class="form-group row" style="border-top:1px solid #ccc;padding-top:16px">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Bi-weekly Seed Date {{generateBiWeeklyDateFormatted}}</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="text" class="form-control" name="generate_biweeklydate" id="generateBiWeeklyDateField" value="{{generateBiWeeklyDateFormatted}}" >
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Grace Period (in days)</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input type="number" class="form-control" name="grace_period_days" value="{{gracePeriodDays}}" required min="0" max="365">
				</div>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Allow Payments Before Due Date</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<select class="form-control" name="allow_payments_before_due_date">
						<option value="yes" >Yes</option>
						<option value="no" {{selectedIfEqual disablePaymentsBeforeDueDate true}}>No</option>
					</select>
				</div>
			</div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Missed Invoice Interval</label>
                <div class="col-lg-6 col-md-9 col-sm-12">
                    <input type="number" class="form-control" name="missed_invoice_interval" value="{{ missedInvoiceInterval }}" required min="0" max="365">
                </div>
            </div>

			<div class="form-group row" style="border-top:1px solid #ccc;padding-top:16px">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Automatic Late Fee</label>
				<div class="col-lg-6 col-md-9 col-sm-12 pt-3">
					<div class="checkbox-list">
						<label class="checkbox checkbox-primary">
							<input type="checkbox" class="form-check-input" name="automatic_late_fee" id="automatic_late_fee" {{checkedIfEq assessLateFee true}}>
							<span></span>
							Assess late fee automatically with item:
						</label>
					</div>
					<select class="form-control mt-3" name="late_fee_item">
						<option value=""></option>
						{{#each item in availableItems}}
						<option value="{{item._id}}" {{selectedIfEqual assessLateFeeItemId item._id}}>{{item.description}}</option>
						{{/each}}
					</select>
					<label class="pt-3">Days Late to Assess Fee:</label>
					<input type="number" class="form-control pt-3" name="late_fee_days" value="{{assessLateFeeDays}}" min="0" max="365">
					
				</div>
            </div>

            {{#if hasCustomization getRegistrationFlowCustomizationName }}
                {{> _dropInFeeBuffer}}
            {{/if}}

		</form>
	</template>
