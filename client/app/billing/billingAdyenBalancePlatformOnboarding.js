import {AsYouType} from "libphonenumber-js";
import {BillingAdyenBalancePlatformUtils} from "../../../lib/util/billingAdyenBalancePlatformUtils";
import './billingAdyenBalancePlatformOnboarding.html';

/*
 * It formats the error message to be displayed in the UI.
 * For example, "postalCode is invalid" will be formatted to "Postal code is invalid".
 */
const formatErrorMessage = (errorMessage) => {
	// Use regex to match camelCase words and insert spaces, then capitalize the first letter.
	return errorMessage.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^[a-z]/, char => char.toUpperCase());
}

Template.billingAdyenBalancePlatformOnboarding.events({
	"submit #frmAdyenBalancePlatformOnboarding"(event, instance) {
		event.preventDefault();
		const initialFormData = instance.balancePlatformOnboardingState.get()?.initialFormData;

		const options = {
			legalName: $("input[name=company-legal-name]")?.val()?.trim(),
			phone: (initialFormData.phone ?? $("input[name=company-phone-number]")?.val())?.trim()?.replaceAll(' ', ''),
			webAddress: (initialFormData.webAddress ?? $("input[name=company-web-address]")?.val())?.trim(),
			address: {
				line1: $("input[name=company-address-line-1]")?.val()?.trim(),
				line2: $("input[name=company-address-line-2]")?.val()?.trim(),
				city: $("input[name=company-city]")?.val()?.trim(),
				state: $("select[name=company-state]")?.val(),
				postalCode: $("input[name=company-postal-code]")?.val()?.trim(),
			}
		}

		const formErrors = BillingAdyenBalancePlatformUtils.validateStartBillingAdyenBalancePlatformOnboardingInputData(
			options,
			instance.balancePlatformOnboardingState.get()?.isAdyenClassicPlatformAccount
		);
		const errorKeys = Object.keys(formErrors);
		if (errorKeys.length > 0) {
			mpSwal.fire("Error", `
				<div class="text-left">
					<div class="mb-1">
						<strong>Please correct the following error${errorKeys.length > 1 ? 's' : ''}:</strong>
					</div>
					<ul>
						${errorKeys.map(key => `<li>${formErrors[key]}</li>`).join("")}
					</ul>
				</div>
			`, "error");
			return;
		}

		const submitButton = $('#frmAdyenBalancePlatformOnboarding button[type=submit]');
		submitButton.prop("disabled", true);
		submitButton.html('<i class="fad fa-spinner-third fa-spin w-20px h20px p-0 mr-1 align-middle" style="font-size: 20px"></i> Submitting...');

		Meteor.callAsync("startBillingAdyenBalancePlatformOnboarding", options).then((result) => {
			submitButton.prop("disabled", false);
			submitButton.html("Submit");

			if (!result.destinationUrl) {
				mpSwal.fire("Error", "No onboarding portal link was available. Please try again later.", "error");
				return;
			}

			Meteor.callAsync("getBillingAdyenBalancePlatformOnboardingStatus").then((result) => {
				instance.balancePlatformOnboardingState.set(result);
			}).catch((error) => {
				if (error) {
					console.error("Failed to get balance platform onboarding status", error);
				}
			});

			// We are redirecting user to the hosted onboarding page in the same tab
			// as opening in a new tab would cause some supported browsers to block the popup
			window.location.href = result.destinationUrl;
		}).catch((error) => {
			submitButton.prop("disabled", false);
			submitButton.html("Submit");

			if (error && error.error === "invalid-web-address") {
				mpSwal.fire("Error", "Invalid web address", "error");
				return;
			}

			if (error && error.error === "invalid-data" && error.details?.length > 0) {
				mpSwal.fire("Error", `
					<div class="text-left">
						<div class="mb-1">
							<strong>Please correct the following error${error.details.length > 1 ? 's' : ''}:</strong>
						</div>
						<ul>
							${error.details.map(field => `<li>${field.message ? formatErrorMessage(field.message) :  'Unknown'}</li>`).join("")}
						</ul>
					</div>
				`, "error")
				return;
			}

			if (error && error.error === "contact-support-for-migration") {
				mpSwal.fire("Error", "Please contact the support team for migration to the Balance Platform", "error");
				return;
			}

			if (error) {
				console.error("Failed to start balance platform onboarding", error);
				mpSwal.fire("Error", "There was an issue starting balance platform onboarding", "error");
				return;
			}
		});
	},

	"click #btnOpenBalancePlatformOnboarding"(event, instance) {
		event.preventDefault();

		$('#btnOpenBalancePlatformOnboarding').prop("disabled", true);

		Meteor.callAsync("resumeBillingAdyenBalancePlatformOnboarding").then((result) => {
			$('#btnOpenBalancePlatformOnboarding').prop("disabled", false);

			if (!result) {
				mpSwal.fire("Error", "No balance platform onboarding portal link was available. Please try again later.", "error");
				return;
			}

			// We are redirecting user to the hosted onboarding page in the same tab
			// as opening in a new tab would cause some supported browsers to block the popup
			window.location.href = result.destinationUrl;
		}).catch((error) => {
			if (error) {
				console.error("Failed to resume balance platform onboarding", error);
				mpSwal.fire("Error", "There was an issue generating the link to the balance platform onboarding portal.", "error");
				return;
			}
		});
	},

	'input #phoneNumber': function (e, i) {
		if (e.target.value.length > 5) {
			// avoid deletion issues
			e.target.value = new AsYouType('US').input(e.target.value);
		}
	},
});

Template.billingAdyenBalancePlatformOnboarding.onCreated(function() {
	this.balancePlatformOnboardingState = new ReactiveVar(null);

	const refreshOnboardingState = () => {
		Meteor.callAsync("getBillingAdyenBalancePlatformOnboardingStatus").then((result) => {
			this.balancePlatformOnboardingState.set(result);
		}).catch((error) => {
			if (error) {
				console.error("Failed to get balance platform onboarding status", error);
				mpSwal.fire("Error", "There was an issue getting the balance platform onboarding status", "error");
				return;
			}
		});
	}
	refreshOnboardingState()

	setInterval(refreshOnboardingState, 10000);
});

Template.billingAdyenBalancePlatformOnboarding.helpers({
	balancePlatformOnboardingState() {
		return Template.instance().balancePlatformOnboardingState.get();
	}
});
