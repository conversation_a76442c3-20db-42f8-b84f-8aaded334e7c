import moment from "moment-timezone";
import './_registrationReviewModal';
import './recentEnrollments.html';
import { showModal } from "../main";
import { Orgs } from "../../../lib/collections/orgs";
import '../reports/reportOrgsField';

Template.recentEnrollments.onCreated(function() {
	var self = this;
	self.registrationData = new ReactiveVar([]);
	self.enrollmentData = new ReactiveVar([]);
});

Template.recentEnrollments.onRendered(function() {
	$('#scheduleStartDate').datepicker({autoclose:true});
	$('#scheduleEndDate').datepicker({autoclose:true});
	var self = this;
	const timezone = Orgs.current().getTimezone();
	callGetEnrollments(self, moment.tz(timezone).subtract(30, 'days').format('MM/DD/YYYY'), moment.tz(timezone).format('MM/DD/YYYY'));
});

function callGetEnrollments(instance, passedStartDate, passedEndDate) {
	const startDate = passedStartDate || $("#scheduleStartDate").val(),
		endDate = passedEndDate || $("#scheduleEndDate").val(),
		showResolvedRegistrations = $("#chkShowResolved").prop("checked"),
		showUnpaidOnly = $("#chkShowUnpaid").prop("checked"),
		orgIds = $("#reportOrgs").val();

	$("#btnUpdate").text("Updating...");
	Meteor.callAsync("getNewEnrollments", {
		startDate,
		endDate,
		showResolvedRegistrations,
		showUnpaidOnly,
		orgIds,
		showOnHoldRegistrations: true
	})
	.then((result) => {
		$("#btnUpdate").text("Update");
		instance.enrollmentData.set(conditionResults(result.enrolledPeople, instance));
	})
	.catch((error)=>{
		$("#btnUpdate").text("Update");
	});
}

Template.recentEnrollments.helpers({
	formattedStartDate() {
		return moment.tz(Orgs.current().getTimezone()).subtract(30, 'days').format("MM/DD/YYYY");
	},
	formattedEndDate() {
		return moment.tz(Orgs.current().getTimezone()).format("MM/DD/YYYY");
	},
	registrations() {
		return Template.instance().enrollmentData.get();
	},
	isShowResolvedActive() {
		return $("#chkShowResolved").prop("checked");
	},
	orgsFieldOpts() {
		return {overrideNonSelectedText:"All Orgs"};
	},
	isPendingRegistration(reg) {
		return reg.regId;
	},
	getSubsidyName(reg) {
		if (reg.agencyIdentifier) {
			return reg.agencyIdentifier
		}else{
			const planData = Template.instance().registrationData.get().find(r => r._id === reg.regId).data.plans;
			for (const plan of planData[0]) {
				if(!plan.allocations.length){
					continue;
				}
				for (const allocation of plan.allocations) {
					if(allocation.allocationType === 'reimbursable' || allocation.allocationType === 'reimbursable-with-copay') {
						return allocation.allocationDescription.split(':')[1].trim();
					}
				}
			}
		}
	},
	isCrossSiteRegistration(reg) {
		const currentOrg = Orgs.current();
		return reg.regId && currentOrg._id !== reg.orgId;
	}
});

Template.recentEnrollments.events({
	"click #btnUpdate"(e,i) {
		callGetEnrollments(i);
	},
	"change #chkShowResolved"(e, i) {
		callGetEnrollments(i);
	},
	"change #chkShowUnpaid"(e, i) {
		callGetEnrollments(i);
	},
	"click .go-to-registrant"(e, i) {
		e.preventDefault();
		const destination = $(e.currentTarget).attr("href"),
			registrantOrgId = $(e.currentTarget).data("org-id");

		const org = Orgs.current();
		if (org._id != registrantOrgId) {
			Meteor.callAsync('adminSwitchOrg', registrantOrgId)
			.then((result) => {
				location.replace("/loading?setLoc=redirect&desturl=" + encodeURIComponent(destination));
			})
			.catch((error) => {
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			window.open(destination, "_blank");
		}
	},
	"click .btnMarkResolved"(e, instance) {
    const personId = $(e.currentTarget).attr("data-id");
		mpSwal.fire({
			title: "Are you sure?",
			text: "This will mark the registration as resolved.",
			icon: "warning",
			showCancelButton: true,
			closeOnConfirm: false
		}).then(result => {
			if (result.value) {
				Meteor.callAsync("resolveEnrollment", {personId})
				.then((response) => {
					mpToastr("Registration marked resolved.");
					callGetEnrollments(instance);
				})
				.catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnReview"(e, instance) {
		e.preventDefault();
		const registrationId = $(e.currentTarget).attr("data-id");
		showModal("registrationReviewModal", {registrationData: instance.registrationData.get().find(rd => rd._id === registrationId), enrollmentData: instance.enrollmentData});
	},
});

const conditionResults = function (enrolledPeople, instance) {
	if(!enrolledPeople || !enrolledPeople.length || !instance) {
		return;
	}
	const timezone = Orgs.current().getTimezone();
    const results = [];
    const registrationData = [];
    for (const people of enrolledPeople) {
		if(!people) {
			continue;
		}

        if(!people.data) {
			people.childName = people.firstName + ' ' + people.lastName;
			people.childBirthday = people.birthday;
            results.push(people);
            continue;
        }

        for (const child of people.data.children) {
			if (!child) {
				continue;
			}
			child.childIdx = people.data.children.indexOf(child);
            results.push({
				firstName: people.data.contacts?.length > 0 ? (people.data.contacts?.find(contact => contact.primaryCaregiver === 'Yes')?.firstName || people.data.contacts[0].firstName) : '',
				lastName: people.data.contacts?.length > 0 ? (people.data.contacts?.find(contact => contact.primaryCaregiver === 'Yes')?.lastName || people.data.contacts[0].lastName) : '',
                childName: child.firstName + ' ' + child.lastName,
                childBirthday: child.birthday,
                orgName: people.orgName,
				orgId: people.orgId,
				createdAt: people.addedStamp,
				agencyIdentifier: child.agencyIdentifier,
				subsidyCode: child.subsidyCode,
				regId: people._id,
				couponCodes: people.data.couponCodes ?? []
            })
        }
		registrationData.push(people);
    }
    instance.registrationData.set(registrationData);
	results.sort((a,b) => {
		return b.createdAt - a.createdAt;
	})
    return results;
}
