import { moment } from 'meteor/momentjs:moment';

/**
 * Initialize a date range picker with the specified options
 * @param {Object} options - Configuration options for the date range picker
 * @param {jQuery} options.element - The jQuery element to initialize the picker on
 * @param {Object} options.instance - The template instance
 * @param {moment} options.startDate - Start date for the picker (moment object)
 * @param {moment} options.endDate - End date for the picker (moment object)
 * @param {Object} options.ranges - Predefined date ranges
 * @param {Function} options.onApply - Callback when dates are applied
 * @param {string} options.startDateField - Name of the field in instance to store start date
 * @param {string} options.endDateField - Name of the field in instance to store end date
 * @param {Function} options.onApplyCallback - Additional callback after applying dates
 */
export function initializeDateRangePicker(options) {
    const {
        element,
        instance,
        startDate,
        endDate,
        ranges,
        startDateField = 'startDate',
        endDateField = 'endDate',
        onApplyCallback
    } = options;
    
    // Default ranges if none provided
    const defaultRanges = {
        'Current Month': [moment().startOf("month"), moment().endOf("month")],
        'Last Month': [moment().subtract(1, "months").startOf("month"), moment().subtract(1, "months").endOf("month")],
        'Last 3 Months': [moment().subtract(3, "months").startOf("month"), moment().endOf("month")],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'Year to Date': [moment().startOf("year"), moment().endOf("month")]
    };
    
    // Remove any existing date range picker
    if (element.data('daterangepicker')) {
        element.data('daterangepicker').remove();
    }
    
    // Initialize the date range picker
    element.daterangepicker({
        startDate: startDate,
        endDate: endDate,
        opens: 'right',
        ranges: ranges || defaultRanges,
        autoUpdateInput: false,
        autoApply: false
    }).on('apply.daterangepicker', function(ev, picker) {
        if (isValidDate(picker.startDate) && isValidDate(picker.endDate)) {
            // Update the instance date properties
            instance[startDateField] = picker.startDate;
            instance[endDateField] = picker.endDate;
            
            // Update the input value
            $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
            
            // Call the additional callback if provided
            if (typeof onApplyCallback === 'function') {
                onApplyCallback(instance);
            }
        } else {
            mpSwal.fire({
                icon: 'warning',
                text: 'Please enter valid date values.',
                confirmButtonText: 'OK',
            });
        }
    }).on('cancel.daterangepicker', function(ev, picker) {
        $(this).val(instance[startDateField].format('MM/DD/YYYY') + ' - ' + instance[endDateField].format('MM/DD/YYYY'));
    });
    
    // Set initial value
    element.val(startDate.format('MM/DD/YYYY') + ' - ' + endDate.format('MM/DD/YYYY'));
}

/**
 * Helper function to validate if a moment object has a valid date
 * @param {moment} momentObj - The moment object to validate
 * @returns {boolean} - Whether the date is valid
 */
export function isValidDate(momentObj) {
    return momentObj && momentObj.isValid();
} 