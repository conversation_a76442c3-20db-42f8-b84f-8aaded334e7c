import { BillingFrequencies } from '../../../lib/constants/billingConstants';

const { PLAN_TYPE } = require("../../../lib/constants/billingConstants");
import moment from "moment-timezone";
import { AvailableCustomizations } from '../../../lib/customizations';
import { People } from '../../../lib/collections/people';
import { Log } from '../../../lib/util/log';
import { BillingUtils } from '../../../lib/util/billingUtils';
import $ from 'jquery';
import { Orgs } from '../../../lib/collections/orgs';
import './_billingAdminPlanDetails.html';

Template.billingAdminPlanDetails.created = function() {
    const details = Template.instance().data.details ?? {};
	const individualDatesValue = (details.individualDates ?? []).sort((a,b) => BillingUtils.dateComparatorWithNullsLast(a, b));
	this.individualDates = new ReactiveVar(individualDatesValue);
	this.selectiveWeeks = new ReactiveVar(details.selectiveWeeks ?? []);
	let dateType = details.dateType ?? '';
	if (dateType === 'timePeriod' && details.selectiveWeeks?.length) {
		dateType = 'timePeriodSelectiveWeeks';
	}
	this.dateTypeSelection = new ReactiveVar(dateType);
	this.selectedTimePeriod = new ReactiveVar(Template.instance().data.details?.timePeriod);
	this.gradeOptions = new ReactiveVar([]);
	this.type = new ReactiveVar(Template.instance().data.type);
	this.frequency = Template.instance().data.frequency;
	this.amount = Template.instance().data.amount;
	this.org = Orgs.current();

	const gradesResults = People.getProfileFieldWithNameForType('person', 'studentGrade');
	if (gradesResults) {
		this.gradeOptions.set(gradesResults.values ?? []);
	}
	
	const todayDate = moment.tz(this.org.getTimezone()).startOf("day").valueOf();
	this.availableTimePeriods = this.org && this.org.billing.timePeriods ?
		this.org.billing.timePeriods.filter((t) => todayDate < t.endDate).sort((a, b) => {
			return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
		})
		: [];
};

Template.billingAdminPlanDetails.rendered = function () {
	const self = this;

	$('#serviceStartDate').datepicker({
		autoclose:true,
		todayHighlight: true
	}).on('changeDate', (ev) => {
		const minDate = new Date(ev.date.valueOf());
		minDate.setDate(minDate.getDate() + 1)
		$('#serviceEndDate').datepicker('setStartDate', minDate);
		ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
	});
	// Start date should always before end date
	$('#serviceEndDate').datepicker({
		autoclose:true,
		todayHighlight: true
	}).on('changeDate', (ev) => {
		const maxDate = new Date(ev.date.valueOf());
		maxDate.setDate(maxDate.getDate() - 1)
		$('#serviceStartDate').datepicker('setEndDate', maxDate);
		ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
	});
	$('#recurringStartDate').datepicker({
		autoclose:true,
		todayHighlight: true
	}).on('changeDate', (ev) => {
		ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
	});
	const formattedDates = this.individualDates.get().map(date => date && moment.tz(date, self.org.getTimezone()).format('MM/DD/YYYY'));
	this.individualDates.get().forEach((date, index) => {
		$(`.individual-service-datepicker[data-id=${index}]`).datepicker({
			autoclose:true,
			todayHighlight: true,
			format:'mm/dd/yyyy',
			datesDisabled: formattedDates
		}).on('changeDate', (ev) => {
			ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
		});
	})
	$("#gradesMultiselect").select2({
        multiple: true,
    })
	$('#regStartDate').datepicker({
		autoclose:true,
		todayHighlight: true
	}).on('changeDate', (ev) => {
		const minDate = new Date(ev.date.valueOf());
		minDate.setDate(minDate.getDate() + 1)
		$('#regEndDate').datepicker('setStartDate', minDate);
		ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
	});
	// Start date should always before end date
	$('#regEndDate').datepicker({
		autoclose:true,
		todayHighlight: true
	}).on('changeDate', (ev) => {
		const maxDate = new Date(ev.date.valueOf());
		maxDate.setDate(maxDate.getDate() - 1)
		$('#regStartDate').datepicker('setEndDate', maxDate);
		ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
	});

	// Selective weeks
	const org = self.org;
	const selectedTimePeriod = Template.instance().availableTimePeriods.find(tp => tp._id === Template.instance().selectedTimePeriod.get());
	if(selectedTimePeriod){
		const minMonday = selectedTimePeriod?.startDate ?
			moment.tz(selectedTimePeriod.startDate, org.getTimezone()) :
			undefined;
			minMonday.add(1, 'week').startOf('week');
			minMonday.add(1, 'day')
			const sd = minMonday.clone().format('MM/DD/YYYY');
		
		const endDate = selectedTimePeriod?.endDate ?
			moment.tz(selectedTimePeriod.endDate, org.getTimezone()) :
			undefined;

		$(`.selective-week-datepicker-start`).datepicker({
			autoclose: true,
			todayHighlight: true,
			format: 'mm/dd/yyyy',
			startDate: moment(sd, 'MM/DD/YYYY').format('MM/DD/YYYY'),
			endDate: endDate.format('MM/DD/YYYY'),
		}).on('changeDate', (ev) => {
			BillingUtils.selectiveStartDateChange(ev,self,endDate,org)
		});
		$(`.selective-week-datepicker-end`).datepicker({
			autoclose: true,
			todayHighlight: true,
			format: 'mm/dd/yyyy',
			startDate: moment(sd, 'MM/DD/YYYY').format('MM/DD/YYYY'),
			endDate: endDate.format('MM/DD/YYYY')
		}).on('changeDate', (ev) => {
			const insideDates = self.selectiveWeeks.get();
			const startDate = insideDates[ev.target.dataset.id][0];
			insideDates.splice(ev.target.dataset.id, 1, [startDate, ev.target.value]);
			self.selectiveWeeks.set(insideDates);
			ev.target.dispatchEvent(new Event('input', { 'bubbles': true }));
		});
		self.selectiveWeeks.get().forEach((dates, dateIndex) => {
			setTimeout(() => {
				const endDateDatePicker = $(`.selective-week-datepicker-end[data-id=${ dateIndex }]`);
				endDateDatePicker.prop("disabled", false);
				if (endDateDatePicker) {
					endDateDatePicker.datepicker('setStartDate', moment.tz(dates[0], 'MM/DD/YYYY', self.org.getTimezone()).format('MM/DD/YYYY'));
					endDateDatePicker.datepicker('setEndDate', moment.tz(dates[0], 'MM/DD/YYYY', self.org.getTimezone()).endOf('week').format('MM/DD/YYYY'));
				}
				const startDateDatePicker = $(`.selective-week-datepicker-start[data-id=${ dateIndex }]`);
				startDateDatePicker.prop("disabled", false);
				if (moment.tz(startDateDatePicker.datepicker("getDate"), self.org.getTimezone()).startOf('week') < moment.tz(self.org.getTimezone()).startOf('week')) {
					// Disable Datepicker based on condition
					startDateDatePicker.datepicker("destroy").prop("disabled", true);
				}
				else{
					if(dateIndex > 0){
						const sd = moment.tz(self.selectiveWeeks.get()[dateIndex-1][0], 'MM/DD/YYYY', self.org.getTimezone()).add(1, 'week').startOf('week') < moment.tz(self.org.getTimezone()).startOf('week') ? moment.tz(self.org.getTimezone()).startOf('week') : moment.tz(self.selectiveWeeks.get()[dateIndex-1][0], 'MM/DD/YYYY', self.org.getTimezone()).add(1, 'week').startOf('week')
						startDateDatePicker.datepicker('setStartDate', sd.format('MM/DD/YYYY'));
					}
					else{
						startDateDatePicker.datepicker('setStartDate', moment.tz(self.org.getTimezone()).startOf('week').format('MM/DD/YYYY'));
					}
					if(self.selectiveWeeks.get()[dateIndex+1]){
						startDateDatePicker.datepicker('setEndDate', moment.tz(self.selectiveWeeks.get()[dateIndex+1][0], 'MM/DD/YYYY', self.org.getTimezone()).subtract(1, 'week').endOf('week').format('MM/DD/YYYY'));
					}
				}
				if (moment.tz(endDateDatePicker.datepicker("getDate"), self.org.getTimezone()).startOf('week') < moment.tz(self.org.getTimezone()).startOf('week')) {
					// Disable Datepicker based on condition
					endDateDatePicker.datepicker("destroy").prop("disabled", true);
				}
			}, 200);
		});
	}
};

Template.billingAdminPlanDetails.helpers({
    'dateTypeSelected'() {
		return Template.instance().dateTypeSelection.get();
	},
	'rows'() {
		const length = Template.instance().individualDates.get().length;
		return Math.ceil(length / 3);
	},
	'columns'(rowIndex) {
		const serviceDates = Template.instance().individualDates.get();
		const cols = [];
		for (let i = 0; i < serviceDates.length; i++) {
			if (i >= rowIndex * 3 && i < rowIndex * 3 + 3) {
				cols.push({value: serviceDates[i], index: i});
			}
		}
		return cols;
	},
	'individualDates'() {
		return Template.instance().individualDates.get();
	},
	selectiveWeeks() {
		return Template.instance().selectiveWeeks.get();
	},
	selectiveWeekStart(selectiveWeek) {
		return selectiveWeek[0];
	},
	selectiveWeekEnd(selectiveWeek) {
		return selectiveWeek[1];
	},
	'grades'() {
		return Template.instance().gradeOptions.get();
	},
	'scheduleTypes'() {
		return _.sortBy(Template.instance().org.getScheduleTypes(), "type");
	},
	'detailsInfo'() {
		return Template.instance().data.details ?? {};
	},
	'checkedForDay'(day) {
		try {
			const details = Template.instance().data.details ?? {};
			if (!details.recurringDays || !Array.isArray(details.recurringDays)) {
				return "";
			}
			return details.recurringDays.includes(day) ? "checked" : "";
		} catch (error) {
			console.error("Error in checkedForDay helper:", error);
			return "";
		}
	},
	'definitionType'() {
		return Template.instance().data.type;
	},
	'availableTimePeriods'() {
		return Template.instance().availableTimePeriods;
	},
	canShowSelectiveWeeksOption() {
		return [BillingFrequencies.WEEKLY, BillingFrequencies.WEEKLY_SCHEDULED_DAILY].includes(Template.instance().frequency.get()) &&
			Template.instance().org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	},
	'showTimePeriodSelect'() {
		return Template.instance().data.type === PLAN_TYPE && ['timePeriod', 'timePeriodSelectiveWeeks'].includes(Template.instance().dateTypeSelection.get());
	},
	'showWeekendCheckboxes'() {
		return Template.instance().data.type === 'item' &&
			['dateRange'].includes(Template.instance().dateTypeSelection.get()) &&
			Template.instance().org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);
	},
	showTimePeriodWeekSelections() {
		return Template.instance().data.type === PLAN_TYPE && ['timePeriodSelectiveWeeks'].includes(Template.instance().dateTypeSelection.get()) &&
			Template.instance().selectedTimePeriod.get();
	},
	canAddSelectiveWeek() {
		const selectedTimePeriod = Template.instance().availableTimePeriods.find(tp => tp._id === Template.instance().selectedTimePeriod.get());
		const latestMonday = getMaxMonday(selectedTimePeriod, Template.instance());
		latestMonday.add(11, 'days');
		const endDate = selectedTimePeriod?.endDate ?
			moment.tz(selectedTimePeriod.endDate, Template.instance().org.getTimezone()).valueOf() :
			undefined;
		return (Template.instance().selectiveWeeks.get() ?? []).length < 52 &&
			latestMonday.valueOf() <= endDate;
	},
	canDeleteSelectiveWeek() {
		return (Template.instance().selectiveWeeks.get() ?? []).length > 1;
	},
	getPlanAmount(index) {
		const defaultAmount = Template.instance().amount.get();
		const details = Template.instance().data.details ?? {};
		if (details.selectiveWeekAmounts && details.selectiveWeekAmounts[index]) {
			return details.selectiveWeekAmounts[index];
		} else {
			return defaultAmount;
		}
	}
});

Template.billingAdminPlanDetails.events({
    'change #dateTypeSelect' (e, i) {
		const newVal = $('#dateTypeSelect').val();
		i.dateTypeSelection.set(newVal);
	},
	'change #timePeriodsSelect'(e, i) {
		i.selectedTimePeriod.set(document.getElementById('timePeriodsSelect')?.value);
		i.selectiveWeeks.set([]);
	},
	'click #btnAddDays' (e, i) {
		e.preventDefault();
		const dates = i.individualDates.get();
		dates.push(null);
		dates.sort((a,b) => BillingUtils.dateComparatorWithNullsLast(a,b));
		i.individualDates.set(dates);
		setTimeout(() => {
			$(`.individual-service-datepicker[data-id=${dates.length - 1}]`).datepicker({
				autoclose:true,
				todayHighlight: true,
				format:'mm/dd/yyyy',
				datesDisabled: dates.map(date => date && moment.tz(date, i.org.getTimezone()).format('MM/DD/YYYY'))
			}).on('changeDate', (ev) => {
				ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
			});
		}, 18);
	},
	'click .btn-remove-individual-service-date' (e, i) {
		e.preventDefault();
		const index = $(e.currentTarget).attr("data-id");
		const dates = i.individualDates.get();
		dates.splice(index, 1);
		dates.sort((a,b) => BillingUtils.dateComparatorWithNullsLast(a,b));
		i.individualDates.set(dates);
		dates.forEach((date, index) => {
			setTimeout(() => {
				$(`.individual-service-datepicker[data-id=${ index }]`).datepicker('setDatesDisabled', dates.map(date => date && moment.tz(date, i.org.getTimezone()).format('MM/DD/YYYY')));
			}, 200);
		});
	},
	'input .individual-service-datepicker': debounce(function (e, i) {
		const index = $(e.currentTarget).attr("data-id");
		let val = $(e.currentTarget).val();
		const dates = i.individualDates.get();
		const timezone = i.org.getTimezone();

		// Normalize the date
		const normalizedDate = BillingUtils.reformatToMMDDYYYY(val);

		// Validate the normalized date
		if (normalizedDate && moment.tz(normalizedDate, 'MM/DD/YYYY', true, timezone).isValid()) {
			// Update the date at the specified index
			dates[index] = moment.tz(normalizedDate, 'MM/DD/YYYY', timezone).valueOf();

			// Update unique dates, sort, and set them
			const uniqueDates = BillingUtils.sortDatesWithNullsLast(BillingUtils.updateUniqueDates(dates));
			i.individualDates.set(uniqueDates);

			// Update the datepicker fields and disable dates
			updateDatepickerFields(uniqueDates, timezone);
		} else if (val !== '') {
			// Allow user to continue typing if the input is not valid yet
		} else {
			// Handle empty input by clearing the date
			dates[index] = null;
			i.individualDates.set(dates);
			$(e.currentTarget).val('');

			// Update the datepicker fields
			updateDatepickerFields(dates, timezone);
		}
	}, 300),
	'click #btnAddSelectiveWeek'(e, i) {
		e.preventDefault();
		const selectedTimePeriod = i.availableTimePeriods.find(tp => tp._id === i.selectedTimePeriod.get());
		const minMonday = getMaxMonday(selectedTimePeriod, i);
		minMonday.add(1, 'week').startOf('week');
		minMonday.add(1, 'day')
		const startDate = minMonday.clone().format('MM/DD/YYYY');
		let sd = moment.tz(i.org.getTimezone())
		if(moment.tz(selectedTimePeriod.startDate, i.org.getTimezone()) < moment.tz(i.org.getTimezone())){
			sd = moment.tz(i.org.getTimezone()).format('MM/DD/YYYY')
		}
		else{
			sd = moment.tz(selectedTimePeriod.startDate, i.org.getTimezone()).format('MM/DD/YYYY')
		}
		const endDate = selectedTimePeriod?.endDate ?
			moment.tz(selectedTimePeriod.endDate, i.org.getTimezone()) :
			undefined;
		const defaultEnd = minMonday.clone().add(4, 'days');

		const dates = i.selectiveWeeks.get();
		dates.push([startDate, defaultEnd.format('MM/DD/YYYY')]);
		i.selectiveWeeks.set(dates);
		BillingUtils.adjustSurroundingWeeksStartEndDate(i, endDate)

		setTimeout(() => {
			dates.forEach((dateRange, dateIndex) => {
				$(`.selective-week-datepicker-start[data-id=${ dateIndex }]`).datepicker({
					autoclose: true,
					todayHighlight: true,
					format: 'mm/dd/yyyy',
					startDate: moment(sd, 'MM/DD/YYYY').format('MM/DD/YYYY'),
					endDate: endDate.format('MM/DD/YYYY'),
				}).on('changeDate', (ev) => {
					BillingUtils.selectiveStartDateChange(ev,i,endDate,i.org)
				});
				$(`.selective-week-datepicker-end[data-id=${ dateIndex }]`).datepicker({
					autoclose: true,
					todayHighlight: true,
					format: 'mm/dd/yyyy',
					startDate: minMonday.clone().format('MM/DD/YYYY'),
					endDate:endDate < minMonday.clone().endOf('week') ? endDate.format('MM/DD/YYYY') : minMonday.clone().endOf('week').format('MM/DD/YYYY'),
				}).on('changeDate', (ev) => {
				const insideDates = i.selectiveWeeks.get();
				const startDate = insideDates[ev.target.dataset.id][0];
				insideDates.splice(ev.target.dataset.id, 1, [startDate, ev.target.value]);
				i.selectiveWeeks.set(insideDates);
				ev.target.dispatchEvent(new Event('input', { 'bubbles': true }));
			});
		});
		}, 200);
	},
	'click .btn-remove-selective-week-date'(e, i) {
		e.preventDefault();
		const index = $(e.currentTarget).attr("data-id");
		const dates = i.selectiveWeeks.get();
		dates.splice(index, 1);
		i.selectiveWeeks.set(dates);
		const selectedTimePeriod = i.availableTimePeriods.find(tp => tp._id === i.selectedTimePeriod.get());
		const endDate = selectedTimePeriod?.endDate ?
			moment.tz(selectedTimePeriod.endDate, i.org.getTimezone()) :
			undefined;
		BillingUtils.adjustSurroundingWeeksStartEndDate(i, endDate)
	},
	'keydown .selective-week-datepicker-start, keydown .selective-week-datepicker-end': (evt) => {
		const code = evt.which ?? evt.keyCode;
		// Only allow tab
		return code === 9;
	},
});

/**
 * Get the max Monday date from the selective weeks.
 *
 * @param { * } selectedTimePeriod
 * @param instance
 * @returns { moment }
 */
function getMaxMonday(selectedTimePeriod, instance) {
	const timezoneStartDate = moment.tz(selectedTimePeriod?.startDate, instance.org.getTimezone());//.format('MM/DD/YYYY');
	const timezoneEndDate = moment.tz(selectedTimePeriod?.endDate, instance.org.getTimezone());//.format('MM/DD/YYYY');

	let maxDate = new moment.tz(instance.org.getTimezone());
	if (timezoneStartDate.valueOf() > maxDate.valueOf()) {
		maxDate = timezoneStartDate;
	}

	if (maxDate.day() !== 1) {
		let todayDay = maxDate.day();
		const previousMondayDate = maxDate.day() - todayDay + (todayDay === 0 ? -6 : 1);
		maxDate.day(previousMondayDate);
	} else {
		maxDate.subtract(7, 'days');
	}
	const mondays = instance.selectiveWeeks.get().map(dates => dates[0]);
	for (const monday of mondays) {
		const date = new moment.tz(monday, 'MM/DD/YYYY', instance.org.getTimezone());
		if (date.valueOf() > maxDate.valueOf() && date.valueOf() < timezoneEndDate.valueOf()) {
			maxDate = date;
		}
	}
	return maxDate;
}

function updateDatepickerFields(dates, timezone) {
	dates.forEach((date, idx) => {
		const datePickerElement = $(`.individual-service-datepicker[data-id=${idx}]`);
		const formattedDate = date ? moment.tz(date, timezone).format('MM/DD/YYYY') : '';
		datePickerElement.val(formattedDate);

		// Disable selected dates in the datepicker
		datePickerElement.datepicker('setDatesDisabled', dates.map(d => d ? moment.tz(d, timezone).format('MM/DD/YYYY') : ''));
	});
}

function debounce(func, wait) {
	let timeout;
	return function (...args) {
		const context = this;
		clearTimeout(timeout);
		timeout = setTimeout(() => func.apply(context, args), wait);
	};
}
