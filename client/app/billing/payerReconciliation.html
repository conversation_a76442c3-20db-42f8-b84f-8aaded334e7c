<template name="payerReconciliation">
	<style type="text/css">
		.draft-modal .swal2-input-label {
			margin-top: 20px !important;
			display: inline-block;
			width: 24% !important;
			text-align: right !important;
			padding-right: 1em;
		}
		.draft-modal #swal2-input {
			margin-top: 20px !important;
			width: 74% !important;
			max-width: 74% !important;
			min-width: 74% !important;
			display: inline-block !important;
		}
		.draft-modal .swal2-popup {
			width: 50em !important;
			display: block !important;
		}
	</style>
	<div class="container" id="payer-reconciliation-window">
		<div class="card gutter-b">
			<div class="card-header">
				<div class="invoice-box-section {{#if recipients}}payer-reconcile-results{{/if}}" id="payer-reconciliation-box">
					<div class="payer-reconciliation-flex">
						<h3 class="box-title ml-4">Payer Reconciliation</h3>
						<br/>
						
						<div class="row ml-0 mt-3 mb-4">
							<div class="col-12">
								<ul class="nav nav-pills">
									<li class="nav-item">
										<a class="nav-link {{#if trueIfEq currentView "open"}}active{{/if}} nav-link-reconciliation-view" data-view="open" href="#">Open Reconciliation</a>
									</li>
									<li class="nav-item">
										<a class="nav-link {{#if trueIfEq currentView "saved"}}active{{/if}} nav-link-reconciliation-view" data-view="saved" href="#">Saved Reconciliations</a>
									</li>
								</ul>
							</div>
						</div>

						{{#if trueIfEq currentView "open"}}
						<!-- Open Reconciliation Tab Content -->
						<div class="pl-3">
							<p class="mb-3">Select a payer and date range below for filtering the transactions.</p>
							<div class="p-3 mb-3" style="border:1px solid #ccc;border-radius:6px;">
								<label class="mb-2">Filter Options</label>
								<div class="row">
									<div class="col-md-3">
										<label>Payer:</label>
										<select name="payer" class="form-control">
											<option value=""></option>
											{{#each payer in payers}}
											<option value="{{payer.type}}">{{payer.description}}</option>
											{{/each}}
										</select><br/>
										<div class="checkbox-inline">
											<label class="checkbox"><input type="checkbox" value="admin" name="show-paid" ><span></span> Display previously paid items</label>
										</div>
									</div>
									<div class="col-md-4">
										<label>Date Range:</label>
										<input type="text" id="open-reconciliation-date-range" name="invoices-date-range" class="form-control">
									</div>
								</div>
								<div class="row">
									<div class="col-md-3">
										<button class="btn btn-primary" id="btn-update-filters">Update</button>
									</div>
									{{#if hasDraft}}
									<div class="col-md-3">
										<button class="btn btn-primary" id="btn-load-draft">Load Saved Draft</button>
									</div>
									{{/if}}
									{{#if canDeleteDraft}}
									<div class="col-md-3">
										<button class="btn btn-secondary" id="btn-delete-draft">Delete Saved Draft</button>
									</div>
									{{/if}}
								</div>
							</div>
							
							{{#if recipients}}
							<div class="table-holder payer-reconcile-table mt-3">
								<div class="action-links mb-2">
									<a href="#" id="expand-all">Expand All</a> | 
									<a href="#" id="collapse-all">Collapse All</a> | 
									<a href="#" id="select-all">Select All</a> | 
									<a href="#" id="deselect-all">Deselect All</a> |
								</div>
								<table class="table">
									{{# if showNonFamilyFundingLine }}
										<tr class="child-header">
											<td colspan="3" class="recipient-name">{{ getSelectedPayerName }} Non-Family Funds</td>
											<td colspan="3" style="text-align:right">Amount to apply:</td>
											<td>
												<div class="form-inline pull-right">
													<input type="number" step="0.01" name="invoice-amount" class="form-control" style='text-align:right' value="0" />
												</div>
											</td>
										</tr>
										<tr class="overpayment-info-row">
											<td colspan="6" style="text-align: right; vertical-align: middle;">Apply Overpayment: </td>
											<td>
												<div class="form-inline pull-right">
													<select class="overpayment-destination form-control non-family-funding-destination">
														<option value="unapplied-cash-account">Unapplied Cash</option>
														<option value="agency-refund-account">Agency Refund</option>
													</select>
												</div>
											</td>
										</tr>
									{{/ if }}
									{{#each recipient in recipients}}
									<tr class='child-header' data-person-id="{{recipient.personId}}">
										<td>
											<a href="#" class="person-collapse" data-person-id="{{recipient.personId}}">
												<i class="fa fa-caret-down {{#unless personOpen recipient.personId}}d-none{{/unless}}"></i>
												<i class="fa fa-caret-right {{#if personOpen recipient.personId}}d-none{{/if}} "></i>
											</a> 
										</td>
										<td style="text-align:center">
											<input type="checkbox" name="selection-person" data-person-id="{{recipient.personId}}">
										</td>
										<td>
											<span class="recipient-name">{{recipient.name}}</span>
										</td>
										<td style="text-align:right"> {{#if recipient.hasDays}}<b>Days:</b> {{recipient.totalDays}} {{/if}} </td>
										<td style="text-align:right"> <b>{{recipient.totalCopayCount}} Family Copay(s):</b> {{formatCurrency recipient.totalCopays}} </td>
										<td style="text-align:right"> <b>Original Payer Balance:</b> {{formatCurrency recipient.totalOriginalAmount}} </td>
										<td style="text-align:right"> <b>Open Payer Balance:</b> {{formatCurrency recipient.totalPayerOpenAmount}} </td>
									</tr>
									<tbody data-person-id="{{recipient.personId}}" class="payer-body-section d-none">
										{{#each li in recipient.matchedLineItems}}
											<tr class='invoice-header'>
												<td></td>
												<td style="text-align:center"><input type="checkbox" name="selection-invoice" data-person-id="{{recipient.personId}}" data-invoice-id="{{li.invoiceId}}" data-invoice-only-amount="{{#if li.invoiceOnly}}{{li.totalPayerOpenAmount}}{{/if}}"></td>
												<td><a href="/billing/invoices/{{li.invoiceId}}" target="_blank">Invoice {{li.invoiceNumber}}</a> {{li.description}}</td>
												<td>{{li.coversPeriodDesc}}</td>
												<td style="text-align:right">Family Copay: {{formatCurrency li.copayAmount}}</td>
												<td style="text-align:right">Original Payer Balance: {{formatCurrency li.totalOriginalAmount}}</td>
												<td style="text-align:right">Open Payer Balance: {{formatCurrency li.totalPayerOpenAmount}}</td>

											</tr>
											{{#each di in li.discountLineItemDays}}
												<tr class='detail-item'>
													<td></td>
													<td style="text-align:center">{{#if di.open}}<input type="checkbox" name="selections" data-date="{{di.coveredDate}}" data-invoice-id="{{li.invoiceId}}" data-lineitem-id="{{li._id}}" data-amount="{{di.openAmount}}" data-person-id="{{recipient.personId}}" value="{{li.invoiceId}}|{{li._id}}|{{di.coveredDate}}">{{/if}}</td>
													<td style="padding-left:10px">{{di.coveredDate}}</td>
													<td></td>
													<td></td>
													<td style="text-align:right">{{formatCurrency di.amount}}</td>
													<td style="text-align:right">{{formatCurrency di.openAmount}}</td>
												</tr>
											{{/each}}
											<tr class='apply-summary'>
												<td colspan=6 style="text-align:right">Amount to apply:</td>
												<td ><div class="form-inline pull-right">
													<input
															type="number" step="0.01"
															name="invoice-amount"
															data-person-id="{{recipient.personId}}"
															data-lineitem-id="{{li._id}}"
															data-invoice-id="{{li.invoiceId}}"
															data-invoice-only-amount="{{#if li.invoiceOnly}}{{li.totalPayerOpenAmount}}{{/if}}"
															class="form-control" style='text-align:right'
															value="0"
													>
												</div></td>

											</tr>
											<tr style="display:none" class="overpayment-info-row" data-invoice-id="{{li.invoiceId}}">
												<td colspan=6 style="text-align:right">Apply Overpayment: </td>
												<td class="form-inline pull-right">
													
													<select class="overpayment-destination form-control" data-person-id="{{recipient.personId}}" data-lineitem-id="{{li._id}}" data-invoice-id="{{li.invoiceId}}">
														<option value="adjustment-account">Adjustment Account</option>
														<option value="agency-refund-account">Agency Refund Account</option>
														<option value="payer-credit-memo">Credit Memo - Payer</option>
														{{#each familyPayer in (availableFamilyPayers recipient.personId)}}
														<option value="family-credit-memo-{{familyPayer._id}}">Credit Memo - {{familyPayer.name}}</option>
														{{/each}}
														<option value="unapplied-cash-account">Unapplied Cash Account</option>
													</select>
												</td>
											</tr>
										{{/each}}
										<tr class='child-summary'>
											<td colspan=6 style="text-align:right">Amount to apply for {{recipient.name}}:</td>
											<td class="child-amount" data-person-id="{{recipient.personId}}" style="text-align:right"></td>
										</tr>
									</tbody>
									{{/each}}
								</table>
							</div>
							{{ totalsRunner }}
							{{else if requestReceived}}
							<p>No results match your current filters.</p>
							{{else if requestMade}}
								{{> loading}}
							{{/if}}
							
							<div class="payer-action-footer row {{# if recipients }}d-flex{{/ if }} align-items-center mx-0 px-0">
								{{# if hasCustomization getNonFamilyFundingCustomizationName }}
									<div class="col-3 text-left">
										<a href="#" class="add-non-family-funds">Receive Non-Family Funds</a>
									</div>
								{{/ if }}
								<div class="col-5 {{# unless hasCustomization getNonFamilyFundingCustomizationName }} offset-3 {{/ unless }} text-center">
									<span id="total-item-count">0</span> item(s) selected totaling <span id="total-item-amount">$0.00</span>
								</div>
								<div class="col-4 text-right pr-0">
									<button disabled={{cantSaveDraft}} class="btn btn-secondary" id="btn-save-draft">Save Draft</button>
									<button class="btn btn-primary" id="btn-submit-payments">Submit Now</button>
								</div>
							</div>
						</div>
						{{else}}
						<!-- Saved Reconciliations Tab Content -->
						{{> savedReconciliations}}
						{{/if}}
					</div>

					{{#if and reportData (trueIfEq currentView "open")}}
					<div class="payer-report-data">
						<button class="btn btn-default pull-right" id="btn-export">Export</button>
						<h3>Payer Reconciliation Report</h3>
						<h4>Payer: {{reportData.payer}}</h4>
						<h4>Batch Label: {{reportData.batchLabel}}</h4>
						<h4>Date Range: {{reportData.dateRange}}</h4>
						<br/>
						<table class="table" id="dvReportData">
							<tr>
								<th>Name</th>
								<th style="text-align:center">Invoice #</th>
								<th>Covered Day(s)</th>
								<th style="text-align:right"># Day(s)</th>
								<th style="text-align:right">Amount</th>
							</tr>
						{{#each item in reportData.report}}
							<tr>
								<td>{{ getReportPersonName item }}</td>
								<td style="text-align:center">{{item.invoiceNumber}}</td>
								<td>{{item.coveredDaysLabel}}</td>
								<td style="text-align:right">{{item.coveredDaysCount}}</td>
								<td style="text-align:right">{{formatCurrency item.amount}}</td>
							</tr>
						{{/each}}
							<tr class="report-summary-row">
								<td>Total for batch</td>
								<td></td>
								<td></td>
								<td style="text-align:right">{{reportData.totalNumberDays}}</td>
								<td style="text-align:right">{{formatCurrency reportData.totalAmount}}</td>
							</tr>
						</table>
					</div>
					{{/if}}
				</div>
			</div>
		</div>
	</div>
</template>