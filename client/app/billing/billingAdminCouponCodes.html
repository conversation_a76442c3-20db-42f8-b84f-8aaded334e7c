<template name="billingAdminCouponCodes">
  <div class="box box-solid content-box descriptive-content">
    <div class="box-body">
      <div data-cy="add-coupon-btn" class="btn btn-primary font-weight-bolder pull-right add-coupon" id="btnAddCoupon"><i class="fad fa-plus fa-swap-opacity text-white"></i> Add</div>
      <div class="row">
        <div class="col-md-3">
          <div class="input-group" style="border:1px solid #cccccc" id="coupon-search-group">
            <input data-cy="search-by-code" type="name" placeholder="Search by code" id="search-text" class="form-control" autocomplete="off" style="border:none">
            <span class="input-group-btn">
                <button data-cy="search-code-btn-clear" type="submit" name="search" class="btn btn-flat" id='search-btn-clear'><i class="fa fa-search"></i><i class="fa fa-times hidden"></i></button>
            </span>
          </div>
        </div>
        <div class="col-md-3 form-group">
          <div class="checkbox-inline">
            <label class="checkbox"><input data-cy="show-archived-coupons" type="checkbox" id="showArchivedCoupons"><span></span> Show archived coupons</label>
          </div>
        </div>
      </div>
      <br/>
      <table data-cy="coupon-table" class="table table-striped billing-table">
        <tr>
          <th>Coupon Code</th>
          <th>Description</th>
          <th>Amount</th>
          <th>Reg. Start</th>
          <th>Reg. End</th>
          <th></th>
        </tr>
        {{#each couponCodes}}
        <tr>
          <td data-cy="coupon-code" class="text-uppercase">{{code}}</td>
          <td data-cy="coupon-description">
            {{description}}
            {{#if archived}}<span style="color:#ff0000">(Archived)</span>{{/if}}
            {{#if isExpired}}<span style="color:#ff0000">(Expired)</span>{{/if}}
          </td>
          <td data-cy="coupon-amount">
            {{#if trueIfEq amountType "dollars"}}{{formatCurrency amount}} {{else}} {{amount}} % {{/if}}
            {{#if isSuspended}}(Suspended through {{formatDate suspendUntil "MM/DD/YYYY"}}){{/if}}</td>
          <td data-cy="coupon-start-date">{{formatDate regStartDate "MM/DD/YYYY"}} </td>
          <td data-cy="coupon-end-date">{{formatDate regEndDate "MM/DD/YYYY"}} </td>
          <td>
            <div class="dropdown">
              <div class="btn btn-primary font-weight-bolder btn-text-white" id="dropdown-{{_id}}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                Actions<i class="fad-regular fad fa-angle-down ml-4" style="color:#fff"></i>
              </div>
              <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdown-{{_id}}">
                <span data-cy="edit-coupon-button-{{_id}}" class="dropdown-item clickable-row edit-coupon" data-id="{{_id}}">Edit</span>
                {{#unless isSuspended}}
                <span data-cy="suspend-coupon-button-{{_id}}" class="dropdown-item clickable-row suspend-coupon" data-id="{{_id}}">Suspend</span>
                {{else}}
                <span class="dropdown-item clickable-row cancel-coupon-suspension" data-id="{{_id}}">Cancel Suspension</span>
                {{/unless}}
                {{#unless archived}}
                <span data-cy="archive-coupon-button-{{_id}}" class="dropdown-item clickable-row archive-coupon" data-id="{{_id}}">Archive</span>
                {{else}}
                <span class="dropdown-item clickable-row unarchive-coupon" data-id="{{_id}}">Un-archive</span>
                {{/unless}}
                {{#if showDelete}}
                <span class="dropdown-item clickable-row delete-coupon" data-id="{{_id}}">Delete</span>
                {{/if}}
              </div>
            </div>

          </td>
        </tr>
        {{/each}}
      </table>
    </div>
  </div>

</template>

<template name="billingAdminAddEditCouponModal">

  <form id="frmAddPlan">
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Coupon Code*</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input data-cy="add-coupon-code" type="text" class="form-control" name="code" value="{{code}}">
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Description</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input data-cy="add-coupon-desc" type="text" class="form-control" name="description"  value="{{description}}">
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Amount*</label>
      <div class="col-lg-4 col-md-6 col-sm-8">
        <input data-cy="add-coupon-amount" type="text" name="amount" class="form-control" id="txtAmount" value="{{formatNumber amount amountFormat}}">
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4">
        <select data-cy="add-coupon-amount-type" class="form-control" id="selectAmountType" name="amount_type">
          <option value="dollars" {{selectedIfEqual amountType "dollars"}}>Dollars</option>
          <option value="percent" {{selectedIfEqual amountType "percent"}}>Percent</option>
        </select>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Registration Start Date*</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input type="text" id="regStartDate" name="registration_start_date" class="form-control" value="{{formatDate regStartDate 'MM/DD/YYYY'}}" >
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Registration End Date</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input data-cy="add-reg-end-date" type="text" id="regEndDate" name="registration_end_date" class="form-control" value="{{formatDate regEndDate 'MM/DD/YYYY'}}" >
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Coupon Savings Expiration Date</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input data-cy="add-coupon-exp-date" type="text" id="expirationDate" name="expiration_date" class="form-control" value="{{formatDate expirationDate 'MM/DD/YYYY'}}" >
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Max # of Registrations Using This Coupon:</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input data-cy="add-max-number-registration" type="text" id="maxNumberOfRegistrations" name="max_number_of_registrations" class="form-control" value="{{maxNumberOfRegistrations}}" >
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Can Coupon Be Used With Other Coupons?*</label>
      <div class="col-lg-9 col-xl-6">
        <select data-cy="add-used-with-others" class="form-control" id="usedWithOtherCouponsSelect" name="used_with_other_coupons">
          <option value="yes" {{selectedIfEqual usedWithOtherCoupons true}}>Yes</option>
          <option value="no" {{selectedIfEqual usedWithOtherCoupons false}}>No</option>
        </select>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Can Coupon Be Used With Discounts?*</label>
      <div class="col-lg-9 col-xl-6">
        <select data-cy="add-used-with-discounts" class="form-control" id="usedWithOtherDiscountsSelect" name="used_with_discounts">
          <option value="yes" {{selectedIfEqual usedWithDiscounts true}}>Yes</option>
          <option value="no" {{selectedIfEqual usedWithDiscounts false}}>No</option>
        </select>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Restrict Coupon Usage to Specific Time Periods?</label>
      <div class="col-lg-9 col-xl-6">
        <select data-cy="add-restricted-for-time-periods" class="form-control" id="restrictForTimePeriodsSelect" name="restrict_for_time_periods">
          <option value="yes" {{selectedIfEqual restrictForTimePeriods "yes"}}>Yes</option>
          <option value="no" {{selectedIfEqual restrictForTimePeriods "no"}}>No</option>
        </select>
      </div>
    </div>
    <div class="form-group row" style="{{#unless isRestrictedForTimePeriods}}display:none;{{/unless}}">
        <label class="col-4 text-right col-form-label">Time Periods</label>
        <div class="col-lg-9 col-xl-6">
          <div class="checkbox-inline">
            <label class="checkbox checkbox-primary">
              <input type="checkbox" class="form-check-input" id="allTimePeriodsCheckbox">
              <span></span>
              All Selected
            </label>
          </div>
          <select multiple class="form-control form-control-lg form-control-solid" id="timePeriodsMultiselect" name="time_periods[]" size="1">
            {{#each availableTimePeriods}}
              <option value="{{_id}}" {{selectedIfContains currentTimePeriods _id}}>{{name}} ({{formatDate startDate "MM/DD/YYYY"}} - {{formatDate endDate "MM/DD/YYYY"}})</option>
            {{/each}}
          </select>
        </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Restrict Coupon Usage to Specific Billing Plans?</label>
      <div class="col-lg-9 col-xl-6">
        <select data-cy="add-restrict-billing-plan" class="form-control" id="restrictForBillingPlansSelect" name="restrict_for_billing_plans">
          <option value="yes" {{selectedIfEqual restrictForBillingPlans "yes"}}>Yes</option>
          <option value="no" {{selectedIfEqual restrictForBillingPlans "no"}}>No</option>
        </select>
      </div>
    </div>
    <div class="form-group row" style="{{#unless isRestrictedForBillingPlans}}display:none;{{/unless}}">
      <label class="col-4 text-right col-form-label">Billing Plans</label>
      <div class="col-lg-9 col-xl-6">
        <div class="checkbox-inline">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" class="form-check-input" id="allBillingPlansCheckbox">
            <span></span>
            All Selected
          </label>
        </div>
        <select data-cy="add-coupon-billing-plan" multiple class="form-control form-control-lg form-control-solid" id="billingPlansMultiselect" name="billing_plans[]" size="1">
          {{#each availableBillingPlans}}
          <option value="{{_id}}" {{selectedIfContains currentBillingPlans _id}}>{{description}} - {{formatCurrency amount}} {{getFrequencyLabel frequency}}</option>
          {{/each}}
        </select>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Restrict Coupon Usage to Specific One Time Charges?</label>
      <div class="col-lg-9 col-xl-6">
        <select class="form-control" id="restrictForOneTimeChargesSelect" name="restrict_for_one_time_charges">
          <option value="yes" {{selectedIfEqual restrictForOneTimeCharges "yes"}}>Yes</option>
          <option value="no" {{selectedIfEqual restrictForOneTimeCharges "no"}}>No</option>
        </select>
      </div>
    </div>
    <div class="form-group row" style="{{#unless isRestrictedForOneTimeCharges}}display:none;{{/unless}}">
      <label class="col-4 text-right col-form-label">One Time Charges</label>
      <div class="col-lg-9 col-xl-6">
        <div class="checkbox-inline">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" class="form-check-input" id="allOneTimeChargesCheckbox">
            <span></span>
            All Selected
          </label>
        </div>
        <select multiple class="form-control form-control-lg form-control-solid" id="oneTimeChargesMultiselect" name="one_time_charges[]" size="1">
          {{#each availableOneTimeCharges}}
          <option value="{{_id}}" {{selectedIfContains currentOneTimeCharges _id}}>{{description}} - {{formatCurrency amount}}</option>
          {{/each}}
        </select>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Is This Coupon Code a Single Installment Coupon?</label>
      <div class="col-lg-9 col-xl-6">
        <select class="form-control" id="isSingleInstallmentCouponSelect" name="is_single_installment_coupon">
          <option value="yes" {{selectedIfEqual isSingleInstallmentCoupon "yes"}}>Yes</option>
          <option value="no" {{selectedIfEqual isSingleInstallmentCoupon "no"}}>No</option>
        </select>
      </div>
    </div>
  <div class="form-group row">
      <label class="col-4 text-right col-form-label">Ledger Code</label>
      <div class="col-lg-6 col-md-9 col-sm-12">
        <input data-cy="add-ledger-code" type="text" class="form-control" name="ledger_code"  value="{{ledgerCode}}">
      </div>
    </div>
    <div class="form-group row">
      <label class="col-4 text-right col-form-label">Can This Coupon Code Be Used With Bundles</label>
      <div class="col-lg-9 col-xl-6">
        <select data-cy="add-use-in-bundles" class="form-control" id="useCouponInBundles" name="use_coupon_in_bundles">
          <option value="yes-all" {{selectedIfEqual useCouponInBundles "yes-all"}}>Yes - apply to all plans in bundle</option>
          <option value="yes-most" {{selectedIfEqual useCouponInBundles "yes-most"}}>Yes - apply to most expensive plan in bundle</option>
          <option value="yes-least" {{selectedIfEqual useCouponInBundles "yes-least"}}>Yes - apply to least expensive plan in bundle</option>
          <option value="no" {{selectedIfEqual useCouponInBundles "no"}}>No</option>
        </select>
      </div>
    </div>
  </form>

</template>
