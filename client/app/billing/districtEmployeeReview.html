<template name="districtEmployeeReview">
    <div class="container">
        <div class="card card-custom">
            <div class="card-header flex-wrap border-0 pt-6 pb-0">
                <div class="card-title">
                    <h3 class="card-label">District Employee Registration Review
                        <span class="text-muted pt-2 font-size-sm d-block">Find and manage recent registrations</span>
                    </h3>
                </div>
                <div class="card-toolbar">
                    <div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
                        Update
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="box box-solid content-box descriptive-content">
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Start Date</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control pull-right" id="scheduleStartDate" value="{{formattedStartDate}}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>End Date</label>
                                    <div class="input-group">
                                        <input type="text"
                                               class="form-control pull-right"
                                               id="scheduleEndDate"
                                               value="{{formattedEndDate}}"
                                        >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Org(s):</label><br/>
                                    {{> reportOrgsField opts=orgsFieldOpts}}
                                </div>
                            </div>
                        </div>
                        <br/>
                        <table class="table table-striped billing-table">
                            <tr>
                                <th>Person</th>
                                <th>Child's Name</th>
                                <th>Child's Birthday</th>
                                <th>Org</th>
                                <th>Date Created</th>
                                <th>District Employee Email</th>
                                <th>Action</th>
                            </tr>
                            {{#each r in registrations}}
                                <tr>
                                    <td data-cy="tutor-name">{{r.firstName}} {{r.lastName}}</td>
                                    <td data-cy="child-name">{{r.childName}}</td>
                                    <td data-cy="birthday">{{formatDate r.childBirthday "MM/DD/YYYY"}}</td>
                                    <td data-cy="org-name">{{r.orgName}}</td>
                                    <td data-cy="date-created">{{formatDate r.createdAt "MM/DD/YYYY"}}</td>
                                    <td data-cy="district-email">{{r.districtEmail}}</td>
                                    <td>
                                        <div class="dropdown">
                                            <button data-cy="action-btn" class="btn btn-secondary btn-sm dropdown-toggle" type="button"
                                                    id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                Action
                                            </button>
                                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                <a data-cy="review" class="dropdown-item btnReview" data-id="{{r.regId}}"
                                                   href="#">Review</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {{/each}}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>