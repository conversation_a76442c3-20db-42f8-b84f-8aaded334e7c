import './_adjustLineItemModal.html';

Template.adjustLineItemModal.onCreated( function() {
    this.selectedType = new ReactiveVar('credit');
    this.openInvoices = new ReactiveVar([]);
    Meteor.callAsync('allOpenInvoicesForParent', this.data.person)
    .then((res)=>{
        const openInvoices = res.filter(invoice => invoice._id !== this.data.invoice._id);
        this.openInvoices.set(openInvoices);
    })
    .catch((err)=>{
        throw new Meteor.Error(err.error, err.message);
    });
});

Template.adjustLineItemModal.helpers({
    selected: function () {
        return Template.instance().selectedType.get();
    },
    invoices: function () {
        return Template.instance().openInvoices.get();
    },
    saveDisabled: function () {
        return Template.instance().selectedType.get() === 'invoice' && !Template.instance().openInvoices.get().length;
    }
});

Template.adjustLineItemModal.events({
    'click .radio-list': function (event, template) {
        const target = $(event.target);
        if (target.is('input')) {
            const selected = target.val();
            template.selectedType.set(selected);
        }
    },
    'click #saveButton': function (event, template) {
        handleSubmit(event, template);
    },
    'submit #adjustLineItemForm': function (event, template) {
        handleSubmit(event, template);
    }
});

const handleSubmit = function(e, template) {
    e.preventDefault();
    const formFieldData = $('#adjustLineItemForm').serializeArray();
    if (template.selectedType.get() === 'invoice') {
        const inputValue = formFieldData.find(obj => obj.name === 'creditAmount').value;
        if(inputValue < 0) {
            mpSwal.fire('Error', 'Credit amount must not be negative', 'error');
            return;
        }
        const selectedInvoiceId = formFieldData.find(obj => obj.name === 'invoiceSelect').value;
        formFieldData.push({
            name: 'selectedInvoice',
            value: template.openInvoices.get().find((invoice) => invoice._id === selectedInvoiceId)
        });
    }
    template.data.onSave(e, template, formFieldData);
}