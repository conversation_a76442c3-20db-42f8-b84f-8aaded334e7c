import './_recentManualPayments.html';

Template.recentManualPayments.helpers({
	"recentManualPaymentsItem"() {
		return Template.instance().recentManualPaymentsItem.get();
	},
	"section"() {
		return Template.instance().section.get();
	},
	"dashboardData"() {
		return Template.instance().dashboardData;
	},
	"recentManualPaymentsDataAvailable"() {
		Template.instance().recentManualPaymentsItem.set(Template.instance().data?.sections[0]?.items[0]);
		Template.instance().section.set(Template.instance().data?.sections[0]);
		Template.instance().dashboardData.set(Template.instance().data);
		return Template.instance().data ? true : false;
	}
});

Template.recentManualPayments.onCreated(function () {
	this.recentManualPaymentsItem = new ReactiveVar(null);
	this.section = new ReactiveVar(null);
	this.dashboardData = new ReactiveVar(null);
});
