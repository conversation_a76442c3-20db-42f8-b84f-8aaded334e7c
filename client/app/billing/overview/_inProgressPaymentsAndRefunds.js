import './_inProgressPaymentsAndRefunds.html';

Template.inProgressPaymentsAndRefunds.helpers({
    "inProgressPaymentsAndRefundsItem"(){
		return Template.instance().inProgressPaymentsAndRefundsItem.get();
	},
	"section"(){
		return Template.instance().section.get();
	},
	"dashboardData"(){
		return Template.instance().dashboardData;
	},
	"parentDataAvailable"(){
		Template.instance().dashboardData.set(Template.instance().data);
		Template.instance().section.set(Template.instance().data?.sections[0]);
		Template.instance().inProgressPaymentsAndRefundsItem.set(Template.instance().data?.sections[0]?.items[0]);
		return Template.instance().data ? true : false;
	}
});

Template.inProgressPaymentsAndRefunds.onCreated(function() {
	this.dashboardData = new ReactiveVar(null);
	this.section = new ReactiveVar(null);
	this.inProgressPaymentsAndRefundsItem = new ReactiveVar(null);
});