import './_aging.html';

Template.aging.helpers({
	"agingItem"() {
		return Template.instance().agingItem.get();
	},
	"dashBoardData"() {
		return Template.instance().dashBoardData;
	},
	"section"() {
		return Template.instance().section.get();
	},
	"agingDataAvailable"() {
		Template.instance().agingItem.set(Template.instance().data?.sections[0]?.items[0]);
		Template.instance().dashBoardData.set(Template.instance().data);
		Template.instance().section.set(Template.instance().data?.sections[0])
		return Template.instance().data ? true : false;
	}
});

Template.aging.onCreated(function () {
	this.agingItem = new ReactiveVar(null);
	this.dashBoardData = new ReactiveVar(null);
	this.section = new ReactiveVar(null);
});