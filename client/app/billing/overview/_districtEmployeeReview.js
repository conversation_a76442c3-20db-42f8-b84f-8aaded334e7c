import './_districtEmployeeReview.html';

Template.districtEmployeeReviewWidget.helpers({
    "districtEmployeeReviewItem"(){
		return Template.instance().districtEmployeeReviewItem.get();
	},
	"section"(){
		return Template.instance().section.get();
	},
	"dashboardData"(){
		return Template.instance().dashboardData;
	},
	"districtEmployeeReviewData"(){
		Template.instance().dashboardData.set(Template.instance().data);
		Template.instance().section.set(Template.instance().data?.sections[0]);
		Template.instance().districtEmployeeReviewItem.set(Template.instance().data?.sections[0]?.items[0]);
		return Template.instance().data ? true : false;
	}
});

Template.districtEmployeeReviewWidget.onCreated(function() {
	this.dashboardData = new ReactiveVar(null);
	this.section = new ReactiveVar(null);
	this.districtEmployeeReviewItem = new ReactiveVar(null);
});