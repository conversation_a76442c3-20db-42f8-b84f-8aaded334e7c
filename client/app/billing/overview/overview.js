import { Meteor } from 'meteor/meteor';

export async function getRevenuePerformanceWidgetData(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getRevenuePerformanceData", options)
        .then((result) => {
            setTimeout(() => {
                instance.revenuePerformanceData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getAmountInvoiced(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getAmountInvoiced", options)
        .then((result) => {
            setTimeout(() => {
                instance.amountInvoicedData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

 export async function getNextAutoInvoicing(instance) {
	const options = {
		name: instance.data.name
	};
	return new Promise(async (resolve, reject) => {
		Meteor.callAsync("getNextAutoInvoicing", options)
        .then((result) => {
            setTimeout(() => {
                instance.nextAutoInvoicingData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
	});
}

export async function getAging(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getAging", options)
        .then((result) => {
            setTimeout(() => {
                instance.agingData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getPayerStatus(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getPayerStatus", options)
        .then((result) => {
            setTimeout(() => {
                instance.payerStatusData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getPastDueAccounts(instance, screen="billing") {
    const options = {
        name: instance.data.name,
        screen: screen
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getPastDueAccounts", options)
        .then((result) => {
            setTimeout(() => {
                instance.pastDueAccountsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}
export async function getRecentManualPayments(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getRecentManualPayments", options)
        .then((result) => {
            setTimeout(() => {
                instance.recentManualPaymentsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getACHReturns(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getACHReturns", options)
        .then((result) => {
            setTimeout(() => {
                instance.achReturnsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getCreditCardRefusal(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getCreditCardRefusal", options)
        .then((result) => {
            setTimeout(() => {
                instance.creditCardRefusalData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getRecentRefunds(instance) {
    currentUser = Meteor.user();
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getRecentRefunds", options)
        .then((result) => {
            setTimeout(() => {
                instance.recentRefundsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getRegistrationEmployee(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getRegistrationStatusCall", options)
        .then((result) => {
            setTimeout(() => {
                instance.registrationStatusData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getDistrictEmployeeReview(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getDistrictEmployeeReview", options)
        .then((result) => {
            setTimeout(() => {
                instance.districtEmployeeData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}

export async function getUpcomingWithdrawals(instance) {
	const options = {
		name: instance.data.name
	};
	return new Promise(async (resolve, reject) => {
		Meteor.callAsync("getUpcomingWithdrawals", options)
        .then((result) => {
            setTimeout(() => {
                instance.upcomingWithdrawalsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
	});
}

export async function getUpcomingBillingAdjustments(instance) {
	const options = {
		name: instance.data.name
	};
	return new Promise(async (resolve, reject) => {
		Meteor.callAsync("getUpcomingBillingAdjustments", options)
        .then((result) => {
            setTimeout(() => {
                instance.upcomingBillingAdjustmentsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
	});
}

export async function getInProgressPaymentsAndRefunds(instance) {
    const options = {
        name: instance.data.name
    };
    return new Promise(async (resolve, reject) => {
        Meteor.callAsync("getInProgressPaymentsAndRefunds", options)
        .then((result) => {
            setTimeout(() => {
                instance.inProgressPaymentsAndRefundsData.set(result);
                resolve(true);
            }, 500);
        })
        .catch((error) => {
            setTimeout(() => {
                reject(error);
            }, 500);
        });
    });
}
