import { Orgs } from '../../../lib/collections/orgs';
import '../reports/reportOrgsField';
import './recentEnrollmentsAirSlate.html';

Template.recentEnrollmentsAirSlate.onCreated(function() {
	var self = this;
	self.enrollmentData = new ReactiveVar({});
});

Template.recentEnrollmentsAirSlate.onRendered(function() {
	$('#scheduleStartDate').datepicker({autoclose:true});
	$('#scheduleEndDate').datepicker({autoclose:true});
	var self = this;
	callGetEnrollments(self, moment().add(-30, 'days').format("MM/DD/YYYY"), moment().format("MM/DD/YYYY"));
});

function callGetEnrollments(instance, passedStartDate, passedEndDate,) {
	const startDate = passedStartDate || $("#scheduleStartDate").val(),
		endDate = passedEndDate || $("#scheduleEndDate").val(),
		showResolvedRegistrations = $("#chkShowResolved").prop("checked"),
		showUnpaidOnly = $("#chkShowUnpaid").prop("checked"),
		orgIds = $("#reportOrgs").val();

	$("#btnUpdate").text("Updating...");
	Meteor.callAsync("getNewEnrollments", {
		startDate,
		endDate,
		showResolvedRegistrations,
		showUnpaidOnly,
		orgIds
	})
	.then((result) => {
		$("#btnUpdate").text("Update");
		instance.enrollmentData.set(result);
	})
	.catch((error)=>{
		$("#btnUpdate").text("Update");
		instance.enrollmentData.set(undefined);
	});
}

Template.recentEnrollmentsAirSlate.helpers({
	formattedStartDate() {
		return moment().add(-30, 'days').format("MM/DD/YYYY");
	},
	formattedEndDate() {
		return moment().format("MM/DD/YYYY");
	},
	registrations() {
		const allData = Template.instance().enrollmentData.get();
		return allData?.enrolledPeople;
	},
	isShowResolvedActive() {
		return $("#chkShowResolved").prop("checked");
	},
	orgsFieldOpts() {
		return {overrideNonSelectedText:"All Orgs"};
	}
});

Template.recentEnrollmentsAirSlate.events({
	"click #btnUpdate"(e,i) {
		callGetEnrollments(i);
	},
	"change #chkShowResolved"(e, i) {
		callGetEnrollments(i);
	},
	"click .go-to-registrant"(e, i) {
		e.preventDefault();
		const destination = $(e.currentTarget).attr("href"),
			registrantOrgId = $(e.currentTarget).data("org-id");
		
		const org = Orgs.current();
		if (org._id != registrantOrgId) {
			Meteor.callAsync('adminSwitchOrg', registrantOrgId)
			.then((result) => {
				location.replace("/loading?setLoc=redirect&desturl=" + encodeURIComponent(destination));
			})
			.catch((error) => {
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			window.open(destination, "_blank");
		}
	},
	"click .btnMarkResolved"(e, instance) {
    const personId = $(e.currentTarget).attr("data-id");
		mpSwal.fire({
			title: "Are you sure?",
			text: "This will mark the registration as resolved.",
			icon: "warning",
			showCancelButton: true,
			closeOnConfirm: false
		}).then(result => {
			if (result.value) {
				Meteor.callAsync("resolveEnrollment", {personId})
				.then((response) => {
					mpToastr("Registration marked resolved.");
					callGetEnrollments(instance);
				})
				.catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	}
});