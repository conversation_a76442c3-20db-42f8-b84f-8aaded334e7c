<template name="receiptDetailModal">
	<div id="receiptDetailModal" class="modal">
		<div class="modal-dialog modal-dialog-scrollable modal-xl">
			<div class="modal-content">
			
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
						<h4 class="modal-title">Receipt</h4>
				</div>
				<div class="modal-body">
					<b>Invoice Number:</b> {{invoice.invoiceNumber}}<br/>
					<b>Invoice Date:</b> {{invoice.invoiceDate}}<br/>
					<b>Invoice For:</b> {{targetPerson.lastName}}, {{targetPerson.firstName}}<br/>
					<b>Original Amount:</b> {{formatCurrency invoice.originalAmount}}<br/>
					<br/>
					<b>Payment Details:</b><br/>
					
					{{#each payments}}
						<div style="background-color:#eee;margin-bottom:5px;margin-top:5px;padding:5px">
							
							<b>Payment Amount:</b> {{formatCurrency amount}}<br/>
							{{#if serviceCharge}}
							<b>Service Charge:</b> {{formatCurrency serviceCharge}}<br/>
							{{/if}}
							<b>Payment Date:</b> {{formatDate createdAt "M/DD/YYYY"}}<br/>
							<b>Paid By:</b> {{paidByDesc}}<br/>
							<b>Payment Method:</b> {{paymentMethodDescription}}<br/>
							{{#if stripeInfo}}
							<b>Confirmation #:</b> {{stripeInfo.id}}<br/>
							{{/if}}

							{{#if cashnetInfo}}
							<b>Confirmation #:</b> {{cashnetInfo.tx}}<br/>
							{{/if}}

						</div>
					{{/each}}
					
				</div>
				<div class="modal-footer">
					
					<button class="btn btn-primary font-weight-bolder btn-md" id="btnClose">Close</button>

				</div>

			</div>
		</div>
	</div>
</template>
