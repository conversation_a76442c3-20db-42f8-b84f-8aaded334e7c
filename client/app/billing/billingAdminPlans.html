<template name="billingAdminPlans">
	<div class="box box-solid content-box descriptive-content">
		<div class="box-body">
			{{#if showAddButton}}
				<div data-cy="add-plan-btn" class="btn btn-primary font-weight-bolder pull-right" id="btnAddPlan"><i class="fad fa-plus fa-swap-opacity text-white"></i> Add</div>
			{{/if}}
			<div class="row">
				<div class="col-md-3">
					<div class="input-group" style="border:1px solid #cccccc" id="plan-search-group">
						<input data-cy="search-by-name" type="name" placeholder="Search by name" id="search-text" class="form-control" autocomplete="off" style="border:none">
						<span class="input-group-btn">
							<button data-cy="search-btn-clear" type="submit" name="search" class="btn btn-flat" id='search-btn-clear'><i class="fa fa-search"></i><i class="fa fa-times hidden"></i></button>
						</span>
					</div>
				</div>
				<div class="col-md-3 form-group">
					<div class="checkbox-inline">
						<label class="checkbox"><input data-cy="show-archived-plans" type="checkbox" id="showArchived"><span></span> Show archived plans</label>
					</div>
				</div>
			</div>
			<br/>
			<table data-cy="billing-table" class="table table-striped billing-table">
				<tr>
					<th>Type</th>
					<th>Description</th>
					<th>Details</th>
					<th>Category</th>
					{{#if showLedgerAccountName}}
					<th style="text-align:center">Ledger Account</th>
					{{/if}}
					{{#if hasRegistrationFlow }}
						<th style="text-align:center">Expiration Date</th>
					{{/if}}
					<th></th>
				</tr>
				{{#each plans}}
				<tr>
					<td data-cy="type">{{formatPlanType}}</td>
					<td data-cy="plan-desc">{{ description }}
						{{#if archived}}<span style="color:#ff0000">Archived</span>{{/if}}
						{{#if isExpired}}<span style="color:#ff0000">Expired</span>{{/if}}</td>
					<td data-cy="plan-frequency">{{# if isNotBundle }}{{formatCurrency amount}}{{/ if }} {{getFrequencyLabel frequency}} {{#if isSuspended}}(Suspended through {{formatDate suspendUntil "M/D/YYYY"}}){{/if}}</td>
					<td data-cy="plan-category">{{category.capitalizeFirstLetter}}</td>
					{{#if showLedgerAccountName}}
					<td style="text-align:center" data-cy="plan-ledger-account">{{ledgerAccountName}}</td>
					{{/if}}
					{{#if hasRegistrationFlow }}
						<td style="text-align:center" data-cy="plan-expiration-date">
							{{#if expires}}
								{{formatExpires expires}}
                        	{{/if}}
						</td>
					{{/if}}
					<td>
						{{#if showActions}}
							<div class="dropdown">
								<div class="btn btn-primary font-weight-bolder btn-text-white" id="dropdown-{{_id}}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
									Actions<i class="fad-regular fad fa-angle-down ml-4" style="color:#fff"></i>
								</div>
								<div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdown-{{_id}}">
									{{#if showEditActions }}
									<span class="dropdown-item clickable-row edit-plan" data-cy="edit-button-{{_id}}" data-id="{{_id}}">Edit</span>                                        {{# if isNotBundle }}
                                            {{#unless isSuspended}}
                                                <span class="dropdown-item clickable-row suspend-plan" data-cy="suspend-button-{{_id}}" data-id="{{_id}}">Suspend</span>
                                            {{else}}
                                                <span class="dropdown-item clickable-row cancel-suspension" data-id="{{_id}}">Cancel Suspension</span>
                                            {{/unless}}
                                        {{/ if }}
										{{#unless archived}}
											<span class="dropdown-item clickable-row archive-plan" data-cy="archive-button-{{_id}}" data-id="{{_id}}">Archive</span>
										{{else}}
											<span class="dropdown-item clickable-row unarchive-plan" data-id="{{_id}}">Un-archive</span>
											{{#if showDelete}}
													<span class="dropdown-item clickable-row delete-plan" data-id="{{_id}}">Delete</span>
											{{/if}}
										{{/unless}}
										{{#if showPropagateSettings}}
											<span class="dropdown-item clickable-row propagate-plan" data-cy="propagate-button-{{_id}}" data-id="{{_id}}">Propagate</span>
										{{/if}}
									{{/if}}
									{{#if showChargeMultiple}}
										<span class="dropdown-item clickable-row batch-charge" data-id="{{_id}}">Charge multiple...</span>
									{{/if}}
								</div>
							</div>
						{{/if}}
					</td>
				</tr>
				{{/each}}
			</table>
		</div>
	</div>
</template>

<template name="billingAdminAddPlanModal">
	<form id="frmAddPlan">
		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right">Type</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				<div class="radio-list">
					<label class="radio radio-primary">
						<input type="radio" name="type" value="plan" {{checkedIfEq type "plan"}}>
						<span></span>
						Plan
					</label>
					<label class="radio radio-primary">
						<input data-cy="check-item" type="radio" name="type" value="item" {{checkedIfEq type "item"}}>
						<span></span>
						Item
					</label>
					{{#if isShowBundleOption}}
						<label class="radio radio-primary">
							<input data-cy="check-bundle" type="radio" name="type" value="{{bundleType}}" {{checkedIfEq type bundleType}}>
							<span></span>
							Bundle
						</label>
					{{/if}}
					{{#if isShowPunchCard}}
						<label class="radio radio-primary">
							<input data-cy="check-punch-card" type="radio" name="type" value="punchcard" {{checkedIfEq type "punchcard"}}>
							<span></span>
							Punch Card
						</label>
					{{/if}}
				</div>
			</div>
		</div>
		{{#unless isBundleSelected}}
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input data-cy="add-plan-description" type="text" class="form-control" name="description" required value="{{description}}">
				</div>
			</div>
		{{/unless}}
		{{#if hasRegistrationFlow}}
			{{> billingProgramDetailEditor data=data}}
		{{/if}}
		{{#if isBundleSelected}}
			{{> billingAdminPlanBundle data=data}}
		{{else}}
			{{#unless showCategory}}
				<div class="form-group row">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Category</label>
					<div class="col-lg-6 col-md-9 col-sm-12">
						<select data-cy="add-plan-category" class="form-control" id="selectCategory" name="category">
							<option value="" {{selectedIfEqual category ""}}></option>
							<option value="tuition" {{selectedIfEqual category "tuition"}}>Tuition</option>
						</select>
					</div>
				</div>
			{{/unless}}
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Program</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<select data-cy="add-plan-program" class="form-control" id="selectProgram" name="program">
						<option></option>
						{{#each option in availablePrograms}}
							<option value="{{option._id}}" {{selectedIfEqual program option._id}}>{{option.name}}</option>
						{{/each}}
					</select>
				</div>
			</div>

			{{#unless showFrequency}}
				<div class="form-group row">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Frequency</label>
					<div class="col-lg-6 col-md-9 col-sm-12">
						<select data-cy="add-plan-frequency" class="form-control" id="selectFrequency" name="frequency" required>
							<option></option>
							{{#each option in availableBillingFrequencies}}
								<option value="{{option.type}}" {{selectedIfEqual frequency option.type}}>{{option.description}}</option>
							{{/each}}
						</select>
					</div>
				</div>
					<div class="form-group row" style="{{#unless showProgramOfferedOn}}display:none;{{/unless}}">
						<label class="col-xl-3 col-lg-3 text-right col-form-label">Program Offered On</label>
						<div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
							<select multiple class="form-control form-control-lg" id="programOfferedOn" name="programOfferedOn" size="1">
								{{#each day in availableDays}}
									<option value="{{day}}" {{selectedIfContains offeredDays day}}>{{formatDay day}}</option>
								{{/each}}
							</select>
						</div>
					</div>

					<div class="form-group row" style="{{#unless isScaledPlan}}display:none;{{/unless}}">
						<label class="col-xl-3 col-lg-3 text-right col-form-label" for="requiredEnrollment">Required Enrollment</label>
						<div class="col-lg-6 col-md-9 col-sm-12">
							<div class="d-flex no-wrap">
								<select id="requiredEnrollmentMin" class="form-control form-control-solid col-2">
									{{#each number in (numbersBetween 1 availableDaysCount)}}
										<option value="{{number}}" {{selectedIfEqual requiredEnrollmentMin number}}>{{number}}</option>
									{{/each}}
								</select>
								<div class="mx-0 col-1 text-center align-self-center">
									<span class="font-size-h2">&mdash;</span>
								</div>
								<select id="requiredEnrollmentMax" class="form-control form-control-solid col-2">
									{{#each number in (numbersBetween 1 availableDaysCount)}}
										<option value="{{number}}" {{selectedIfEqual requiredEnrollmentMax number}}>{{number}}</option>
									{{/each}}
								</select>
								<div class="col-auto align-self-center">
									<span>days per week</span>
								</div>
							</div>
						</div>
					</div>
				<div id="validateMinMax" class="invalid-feedback text-center" style="display: none">
					Maximum enrollment must be less than or equal to the number of days this program is offered. Please adjust the Program Offered Field to increase the maximum enrollment days allowed.
				</div>
			{{/unless}}

			{{#if punchCardSelected}}
				<div class="form-group row">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Number of Days</label>
					<div class="col-lg-6 col-md-9 col-sm-12">
						<input data-cy="punch-card-number-of-days" type="number" min="1" name="numberOfDays" class="form-control" id="dayAmount" value="{{numberOfDays}}" pattern="[0-9]+([\.][0-9]+)?" required>
					</div>
				</div>
			{{/if}}

			<div class="form-group row" {{hiddenIfEqual isAmountVisible false}}>
				<label class="col-xl-3 col-lg-3 text-right col-form-label">{{amountText}}</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input data-cy="add-plan-amount" type="text" name="amount" class="form-control" id="txtAmount" pattern="[0-9]+([\.][0-9]+)?" value="{{formatNumber amount "0.00"}}" required>
				</div>
			</div>

			{{#each number in (numbersBetween 0 (decremented requiredEnrollmentMax))}}
				<div class="form-group row" {{hiddenIfEqual isAmountVisible true}}>
					<label class="col-xl-3 col-lg-3 text-right col-form-label">{{incremented number}} Day{{#if number}}s{{/if}}/Wk Amount</label>
					<div class="col-lg-6 col-md-9 col-sm-12">
						<input data-cy="scaled-amount" type="text" name="scaledAmount_{{number}}" class="form-control" pattern="[0-9]+([\.][0-9]+)?" value="{{formatNumber (getScaledAmount number) "0.00"}}" required>
					</div>
				</div>
			{{/each}}

			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Default Expiration</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
					<input data-cy="add-plan-expiration" type="text" id="defaultExpirationPlan" name="expires" class="form-control" value="{{formatDate expires 'MM/DD/YYYY'}}">
				</div>
			</div>

			{{#if showLedgerAccountName}}
				<div class="form-group row">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Ledger Account</label>
					<div class="col-lg-6 col-md-9 col-sm-12">
						<input data-cy="add-plan-ledger-account" type="text" name="ledgerAccountName" class="form-control" value="{{ledgerAccountName}}">
					</div>
				</div>
			{{/if}}

			{{#if itemSelected}}
				<div class="form-group row">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Refundable Deposit</label>
					<label class="col-lg-6 col-md-9 col-sm-12 checkbox">
						<input data-cy="refundable-deposit" type="checkbox" class="form-control" name="refundableDeposit" checked="{{#if refundableDeposit}}checked{{/if}}">
						<span></span>
						&nbsp;<br/>
					</label>
				</div>
			{{/if}}

			{{#if showDropInDailyRate}}
				<div class="form-group row">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Drop In Daily Rate</label>
					<label class="col-lg-6 col-md-9 col-sm-12 checkbox">
						<input type="checkbox" class="form-control" name="dropInDailyRate" checked="{{#if dropInDailyRate}}checked{{/if}}">
						<span></span>
						&nbsp;<br/>
					</label>
				</div>
			{{/if}}
		{{/if}}

		{{#if showDetails}}
			{{> billingAdminPlanDetails details=data.details type=definitionType frequency=selectedFrequency amount=planAmount}}
		{{/if}}

		{{#if showDesignations}}
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label" for="designationsSelect">Restrict to the following designation(s)</label>
				<div class="col-xl-6 col-lg-6 col-md-9 col-sm-12">
					<select multiple class="form-control form-control-lg" id="designationsSelect" name="designations[]" size="1">
						{{#each designation in designationOptions}}
							<option value="{{designation}}" {{selectedIfContains designations designation}}>{{designation}}</option>
						{{/each}}
					</select>
				</div>
			</div>
		{{/if}}

		{{#if showRegFeeConfig}}
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label" for="regFeeExempt">Exempt from Registration Fee</label>
				<label class="col-lg-6 col-md-9 col-sm-12 checkbox">
					<input data-cy="add-plan-reg-free-exempt" id="regFeeExempt" class="form-control" type="checkbox" name="regFeeExempt" {{regFeeExemptChecked}}>
					<span></span>
					<br/>
				</label>
			</div>
			<div class="form-group row">
				<label class="col-xl-3 col-lg-3 text-right col-form-label" for="siblingDiscountExempt">Exempt from Sibling Discount</label>
				<label class="col-lg-6 col-md-9 col-sm-12 checkbox">
					<input data-cy="add-plan-sibling-discount-exempt" id="siblingDiscountExempt" class="form-control" type="checkbox" name="siblingDiscountExempt" {{siblingDiscountExemptChecked}}>
					<span></span>
					<br/>
				</label>
			</div>
		{{/if}}

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Offered to additional locations</label>
			<div class="col-lg-6 col-md-9 col-sm-12">
				{{> reportOrgsField opts=orgsFieldOpts }}
			</div>
		</div>
	</form>
</template>

<template name="billingAdminChargeMultiple">
		<form id="frmAddPlan">
			<div class="row">
				<div class="col-sm-12">
					<label>Item:</label><br/>
					{{planInfo.description}}
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<label>Charge To:</label><br/>
					<select id="targets" multiple="multiple" style="width:100%">
						{{#each targets}}
						<option value="{{_id}}">{{label}}|{{type}}</option>
						{{/each}}
					</select>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 form-group">
					<label>Price:</label>
					<input type="text" class="form-control" name="price"  pattern="[0-9]+(\.[0-9][0-9]?)?" value="{{planInfo.amount}}">
				</div>
			</div>
	
			<div class="row">
				<div class="col-sm-12 form-group">
					<label>Discount reason (if applicable):</label>
					<select name="discount_reason" class="form-control">
						<option value=""></option>
						{{#each availableDiscountTypes}}
							<option value="{{type}}">{{description}}</option>
						{{/each}}
					</select>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 form-group">
					<label>Quantity:</label>
					<input type="text" class="form-control" name="quantity"  pattern="[0-9]+?" value="1">
				</div>
			</div>
	
			<div class="row">
				<div class="col-sm-12">
					<label>Note:</label><br/>
					<input type="text" name="notes" class="form-control">
				</div>
			</div>
		</form>
	</template>
