import './_depositsList';
import './deposits.html';
import './_depositsPayments';

Template.deposits.onCreated(function() {
	var self = this;
	self.currentView = new ReactiveVar("payments");
});

Template.deposits.onRendered(function() {
	var self = this;
});


Template.deposits.helpers({
	currentView() {
		return Template.instance().currentView.get();
	}
});

Template.deposits.events({
	"click .nav-link-deposits-view"(e,i) {
		if ($(e.currentTarget).data("view") === 'deposits') {
			e.preventDefault();
		}
		i.currentView.set( $(e.currentTarget).data("view"));
	}
});

