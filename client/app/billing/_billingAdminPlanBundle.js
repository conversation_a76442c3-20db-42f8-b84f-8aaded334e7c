import { Orgs } from '../../../lib/collections/orgs';
import { SCALED_PLAN_FREQUENCIES } from '../../../lib/constants/billingConstants';
import './_billingAdminPlanBundle.html';

function getScaledPlans() {
    return (Orgs.current() && (Orgs.current().availableBillingPlans() ?? [])).filter(p => SCALED_PLAN_FREQUENCIES.includes(p.frequency));
}

Template.billingAdminPlanBundle.onCreated( function() {
    const plans = Template.instance().data.data.plans ?? [];
    let showPricing = false;
    let plan1 = null;
    let plan2 = null;
    this.scaledPlans = getScaledPlans();
    if (plans.length > 1 && plans[0] !== plans[1]) {
        plan1 = this.scaledPlans.find(p => p._id === plans[0]);
        plan2 = this.scaledPlans.find(p => p._id === plans[1]);
        if (plan1?.frequency !== plan2?.frequency) {
            plan1 = null;
            plan2 = null;
        } else {
            showPricing = true;
        }
    }
    this.showPricing = new ReactiveVar(showPricing);
    this.plan1 = new ReactiveVar(plan1);
    this.plan2 = new ReactiveVar(plan2);
});

Template.billingAdminPlanBundle.helpers({
    getScaledAmount(rowIndex, columnIndex) {
        return Template.instance().data.data.scaledAmounts ? Template.instance().data.data.scaledAmounts[rowIndex][columnIndex] : 0;
    },
    getPlans(index) {
        const scaledPlans = Template.instance().scaledPlans;
        const planProp = 'plan' + (index + 1);
        const otherPlanProp = 'plan' + (index === 0 ? 2 : 1);
        const thisPlanFrequency = Template.instance()[planProp].get()?.frequency;
        const otherPlanFrequency = Template.instance()[otherPlanProp].get()?.frequency;
        const selectedFrequency = thisPlanFrequency ?? otherPlanFrequency;
        // If no selected plans or this plan is the only one chosen so far, show all scaled plans
        if (!selectedFrequency || (thisPlanFrequency && !otherPlanFrequency)) {
            return scaledPlans;
        }
        // Show only scaled plans that match the current frequency
        return scaledPlans.filter(p => p.frequency === selectedFrequency);
    },
    getPlanDescription(index) {
        if (index === 0) {
            return Template.instance().plan1.get()?.description;
        } else if (index === 1) {
            return Template.instance().plan2.get()?.description;
        }
    },
    getPlanIdAtIndex(index) {
        if (index === 0) {
            return Template.instance().plan1.get()?._id;
        } else if (index === 1) {
            return Template.instance().plan2.get()?._id;
        }
    },
    showPricing() {
        return Template.instance().showPricing.get();
    },
    getMaxValues() {
        const plan1 = Template.instance().plan1.get();
        const plan2 = Template.instance().plan2.get();

        const plan1Max = plan1?.requiredEnrollmentMax ?? 5;
        const plan2Max = plan2?.requiredEnrollmentMax ?? 5;
        const plan1Min = plan1?.requiredEnrollmentMin ?? 1;
        const plan2Min = plan2?.requiredEnrollmentMin ?? 1;

        const maxDays = Math.max(plan1Max - 1, plan2Max - 1, 0);  // Subtract 1 for zero-indexing
        const maxColumns = Math.max(plan1Max, plan2Max, 1) + 1;    // Add 1 for grid label spacing

        return {
            maxDays,
            maxColumns,
            plan1Min,
            plan1Max,
            plan2Min,
            plan2Max
        };
    }
});

Template.billingAdminPlanBundle.events({
    'change .bundle-plan': () => {
        const uniquePlansSet = new Set();
        const plans = document.querySelectorAll('.bundle-plan');
        for (let i = 0; i < plans.length; i++) {
            if (plans[i].value) {
                uniquePlansSet.add(plans[i].value);
            }
            const plan = Template.instance().scaledPlans.find(p => p._id === plans[i].value);
            if (i === 0) {
                Template.instance().plan1.set(plan || null);
            } else if (i === 1) {
                Template.instance().plan2.set(plan || null);
            }
        }
        Template.instance().showPricing.set(uniquePlansSet.size === 2);
    }
});