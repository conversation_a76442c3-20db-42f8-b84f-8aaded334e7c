<template name="invoiceDetail">
{{#with invoiceData}}		
	<div class="container">
		<div class="card gutter-b">
			<div class="card-header">
				
				<div class="btn-group pull-right" role="group">
					<button data-cy="invoice-actions-btn" id="btnGroupActions" type="button" class="btn btn-primary font-weight-bolder dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Actions</button>
					
				
					<div class="dropdown-menu" role="menu" aria-labelledby="btnGroupActions">
						{{#if showResendInvoice}}
						<li><a data-cy="re-send-invoice-btn" href="#" class="dropdown-item" id="resendInvoiceLink">Re-send Invoice</a></li>
						{{/if}}
						<li class="divider"></li>
						{{#if showCreditInvoice}}
						<li><a data-cy="credit-inovice-btn" href="#"  class="dropdown-item" id="creditInvoiceLink">Credit Invoice</a></li>
						{{/if}}
						{{#if showVoidInvoice}}
						<li><a data-cy="void-invoice-btn" href="#"  class="dropdown-item" id="voidInvoiceLink">Void Invoice</a></li>
						{{/if}}
						{{#if showIssueRefund}}
						<li><a data-cy="issue-refund-btn" href="#"  class="dropdown-item" id="issueRefundLink">Issue Refund</a></li>
						{{/if}}
					</div>
				</div>

				<h3 class="card-title">Invoice</h3>
			</div>
			<div class="card-body">
				<div class="row vertical-align">
					<div class="col-sm-4">
						<div class="invoice-item-label">Invoice For:</div>
						<a href="/people/{{targetPerson._id}}">{{targetPerson.lastName}}, {{targetPerson.firstName}}</a><br/>

						<div class="invoice-item-label">Invoice #:</div>
						{{invoiceNumber}}<br/>

						
					</div>
					<div class="col-sm-4">
						<div class="invoice-item-label">Invoice Date:</div>
						{{invoiceDate}}<br/>

						<div class="invoice-item-label">Due Date:</div>
						{{formatDate dueDate "M/DD/YYYY"}}<br/>

					</div>
					<div class="col-sm-4">
						<div class="invoice-item-label">Original Amount:</div>
						{{formatCurrency originalAmountWithPayers}}<br/>

						<div class="invoice-item-label">Amount Due:</div>
						<b>
							{{#if lt openAmount 0}}
								{{formatCurrency 0}}
							{{else}}
								{{formatCurrency openAmount}}
							{{/if}}
						</b><br/>

					</div>
				</div>
			</div>
		</div>

		<div class="card gutter-b">
			<div class="card-header">
				<h3 class="card-title">Line Items</h3>
			</div>
			<div class="card-body">
					
				<table class="table">
				{{#each lineItem in lineItems}}
					{{#with lineItem}}
						{{#if trueIfEq type "item"}}
							<tr>
								<td>
									{{originalItem.description}}{{#if notes}}<br/> - {{notes}}{{/if}} 
								</td>
								<td>
									Qty: {{quantity}}
								</td>
								<td>
                                    {{formatCurrency price}}
								</td>
								<td style="text-align:right">
									{{formatCurrency amount}}
								</td>
							</tr>
						{{else}}
							<tr>
								<td>
									{{description}} - {{getFrequencyLabel frequency}}<br/>
									{{#if coversPeriodDesc}} - {{coversPeriodDesc}}{{/if}}
									{{#if coveredDays}}: {{join coveredDays ', '}}{{/if}}
								</td>
								<td></td> 
								<td style="text-align:right">
									{{formatCurrency amount}}
								</td>
								<td></td>
							</tr>
                        {{/if}}
                        {{#if appliedDiscounts}}
								{{#each appliedDiscounts}}
									{{#let discountIndex=@index }}
										<tr>
											<td>
												- {{#if trueIfEq originalAllocation.discountType "coupon"}}{{originalAllocation.code}} Coupon Code{{else}}{{#if trueIfEq lineItem.type "item"}}{{originalAllocation.description}}{{else}}{{originalAllocation.allocationDescription}}{{/if}}{{/if}}
                                                {{#unless trueIfEq lineItem.type "item"}}
                                                    {{#if showModifyDiscount}}
                                                        {{#unless XXXcoveredDays}}<a href="#" class="invoice-modify-discount px-3" data-id="{{lineItem._id}}" data-index="{{@index}}">modify &gt;</a>{{/unless}}
                                                        <a href="#" class="invoice-void-discount px-3" data-id="{{lineItem._id}}" data-index="{{@index}}">void &gt;</a>
                                                        {{/if}}
                                                        {{#if showReallocatePayers}} <a href="#" class="invoice-reallocate-discount px-3" data-id="{{lineItem._id}}" data-index="{{@index}}">reallocate &gt;</a> {{/if}}
                                                        {{#if voidedAt}}<i style="color:var(--primary)">voided</i>{{/if}}<br/>
                                                        <ul>
                                                        {{#each coveredDay in coveredDays}}
                                                            <li>{{coveredDay}} {{#if showModifyDiscount}}( <a href="#" class="invoice-remove-lineitem-covered-day" data-day="{{coveredDay}}" data-allocation-id="{{discountIndex}}" data-lineitem-id="{{lineItem._id}}">remove</a> ){{/if}}</li>
                                                        {{/each}}
                                                        </ul>
                                                        {{#if coveredDays}}
                                                            <a href="#" class="invoice-add-lineitem-covered-day" data-lineitem-id="{{lineItem._id}}" data-allocation-id="{{discountIndex}}" style="margin-left:20px">+ Add reimbursable day</a>
                                                        {{/if}}
                                                {{/unless}}
											</td>
											<td></td>
											<td valign=top style="text-align:right">({{formatCurrency amount}})</td>
											<td></td>
										</tr>
									{{/let}}
								{{/each}}
							{{/if}}
                        {{#unless trueIfEq type "item"}}
							<tr>
								<td>
                                        {{#if showAddDiscount}}
                                        <a href="#" class="invoice-add-discount" data-id="{{lineItem._id}}" data-type="discount">+ Add discount</a>
                                        {{/if}}
                                        {{#if showAddReimbursement}}
                                        <a href="#" class="invoice-add-discount" data-id="{{lineItem._id}}" data-type="reimbursable" style="margin-left:20px">+ Add reimbursement</a>
                                        {{/if}}

								</td>
								<td></td>
								<td></td>
								<td></td>
							</tr>
                        {{/unless}}

							<tr>
								<td colspan=3>{{#if trueIfEq type "item"}}Item{{else}}Plan{{/if}} Total:</td>
								<td style="text-align:right">{{formatCurrency (getTotalAmount this)}}</td>
							</tr>
					{{/with}}
				{{/each}}
					<tr>
						<td colspan=3><b>Invoice Total:</b></td>
						<td style="text-align:right"><b>{{formatCurrency getInvoicedAmountMinusDiscounts}}</b></td>
					</tr>
					<tr>
						<td colspan=3><b>Invoice Total (Family):</b></td>
						<td style="text-align:right">
							<b>
								{{#unless voided}}
									{{#if lt originalAmount 0}}
										{{formatCurrency 0}}
									{{else}}
										{{formatCurrency originalAmount}}
									{{/if}}
									{{else}}
										{{formatCurrency 0}}
								{{/unless}}
							</b>
						</td>
					</tr>
				</table>
			
			</div>
		</div>

		<div class="card gutter-b">					
			<div class="card-header">
				{{#if showEditInvoiceNotes}}
					<button class="btn btn-primary pull-right" id="btnEditInvoiceNotes">Edit</button>
				{{/if}}
				<h3 class="card-title" style="{{#if showEditInvoiceNotes}}padding-top:10px{{/if}}">Invoice Notes</h3>
			</div>
			<div class="card-body">
					{{#if invoiceNotes}}
						<div style="white-space: pre-wrap;">{{invoiceNotes}}</div>

					{{else}}
						<p style="text-align:center">No invoices notes at this time.</p>
					{{/if}}
			</div>
		</div>

		<div class="card gutter-b">
			<div class="card-header">
				<h3 class="card-title">Invoice Ledger</h3>
			</div>
			<div class="card-body">
				<table class="table">
					<tr style="border-bottom:1px solid #ccc">
						<th>Date</th>
						<th>Description</th>
						<th style="text-align:right">Debit</th>
						<th style="text-align:right">Credit</th>
						<th style="text-align:right">Balance</th>
					</tr>
					{{#each lineItemDetail}}
						<tr>
							<td style="vertical-align:top">{{formatDate date "MM/DD/YYYY"}}</td>
							<td style="vertical-align:top">
								{{description}}
								{{#if showVoid}}
									<a data-cy="void-line-item-btn" href="#" class="btnVoidLineItem" data-id="{{i}}" style="margin-right: 1rem">void</a>
								{{/if}}
								{{#if showModify}}
									<a data-cy="modify-line-item-btn" href="#" class="btnModifyLineItem" data-id="{{i}}" style="margin-right: 1rem">modify</a>
								{{/if}}
								{{#if showManualRefund}}
									<a data-cy="manual-refund-line-item-btn" href="#" class="btnManualRefundLineItem" data-id="{{i}}" style="margin-right: 1rem">refund</a>
								{{/if}}
								{{#if showReverse}}
									<a data-cy="reverse-line-item-btn" href="#" class="btnReverseLineItem" style="margin-right: 1rem">reverse</a>
								{{/if}}
								{{#if showAdjust}}
									<a data-cy="adjust-line-item-btn" href="#" class="btnAdjustLineItem">adjust payment</a>
								{{/if}}
								{{#if voidedReason}}
									<br/>Voided: {{voidedReason}}{{#if voidedNote}} - {{voidedNote}}{{/if}}
								{{/if}}
								{{#if voidedAt}}
									<br/>Voided: {{formatDate voidedAt "MM/DD/YYYY"}}
								{{/if}}
								{{#if refundedAt}}
									<br/>Refunded: {{formatDate refundedAt "MM/DD/YYYY"}}{{#if refundedNote}} - {{refundedNote}}{{/if}}
								{{/if}}
								{{#if reversedAt}}
									<br/>Reversed: {{formatDate reversedAt "MM/DD/YYYY"}}{{#if reversedNote}} - {{reversedNote}}{{/if}}
								{{/if}}
								{{#each adjustments}}
									<br/>Adjusted: {{formatDate adjustedAt "MM/DD/YYYY"}}{{#if adjustedNote}} - {{adjustedNote}}{{/if}}
								{{/each}}
							</td>
							<td style="vertical-align:top;text-align:right">{{#if debitAmount}}{{formatCurrency debitAmount}}{{/if}}</td>
							<td style="vertical-align:top;text-align:right">{{#if creditAmount}}{{formatCurrency creditAmount}}{{/if}}</td>
							<td style="vertical-align:top;text-align:right">{{formatCurrency balance}}</td>
						</tr>
					{{/each}}
				</table>
			</div>
		</div>


		<div class="card gutter-b">					
			<div class="card-header">
				<h3 class="card-title">Open Invoice Balances</h3>
			</div>
			<div class="card-body">

				<table class="table">
					<tr style="border-bottom:1px solid #ccc">
						<th>Source</th>
						<th style="text-align:right">Allocated Amount</th>
						<th style="text-align:right">Paid Amount</th>
						<th style="text-align:right">Open Amt</th>
					</tr>
					{{#each openBalances}}
					<tr>
						<td >{{source}}</td>
						<td style="text-align:right">
							{{#if lt allocatedAmount 0}}
								{{formatCurrency 0}}
							{{else}}
								{{formatCurrency allocatedAmount}}
							{{/if}}
						</td>
						<td style="text-align:right">{{formatCurrency paidAmount}}</td>
						<td style="text-align:right">
							{{#if lt openAmount 0}}
								{{formatCurrency 0}}
							{{else}}
								{{formatCurrency openAmount}}
							{{/if}}
						</td>
					</tr>
					{{/each}}
				</table>
			</div>
			
		</div>
	</div>
{{/with}}
</template>

<template name="billingModifyDiscountModal">
<form id="frmModifyDiscount">
<div class="row">
<div class="col-xs-12">
	
	<b>Description:</b><br/>
	{{currentLabel}}<br/>
	
	<b>Current Amount:</b><br/>
	{{formatCurrency currentAmount}}<br/>
	<hr/>
	<b>New Amount:</b><br/>
	<input type="text" name="newAmount" class="form-control" required pattern="[0-9]+([\.][0-9]+)?" value="{{formatNumber currentAmount '0.00'}}"><br/>
	
</div>
</div>
</form>
</template>

<template name="billingAddDiscountModal">
<form id="frmAddDiscount">
<div class="row">
<div class="col-xs-12">

	{{#if trueIfEq discountType "discount"}}
	<div class="row">
		<div class="col-sm-12 form-group">
			<label>Discount Type:</label>
			<select name="allocation_discount_type" class="form-control">
				<option value=""></option>
				{{#each availableDiscountTypes}}
				<option value="{{type}}">{{description}}</option>
				{{/each}}
			</select>
			(Note: percent-based discounts cannot currently be added to existing invoices)
		</div>
	</div>
	{{/if}}

	{{#if trueIfEq discountType "reimbursable"}}
	<div class="row">
		<div class="col-sm-12 form-group">
			<label>Other Payer Type:</label>
			<select name="allocation_reimbursement_type" class="form-control">
				<option value=""></option>
				{{#each availableReimbursementTypes}}
				<option value="{{type}}">{{description}}</option>
				{{/each}}
			</select>
		</div>
	</div>
	{{/if}}

	<div class="row">
		<div class="col-sm-12 form-group">
			<label>Amount:</label>
			<input type="text" class="form-control" name="discount_amount"  pattern="[0-9]+(\.[0-9][0-9]?)?" value="0.00"><br/>
		</div>
	</div>
</div>
</div>
</form>

</template>

<template name="billingReallocatePayerModal">
	<form id="frmModifyDiscount">
	<div class="row">
		<div class="col-12">
		
			<div class="row d-flex align-items-center ">
				<div class="col-3 text-right">
					<label class="mb-0">Reallocate From:</label>
				</div>
				<div class="col-9">
					<!--{{allocateFromDescription}} {{formatCurrency allocateFromAmount}}-->
					<select name="reallocate_from" class="form-control">
						{{#each allocations}}
						<option value="{{index}}">{{originalAllocation.allocationDescription}} {{formatCurrency amount}}</option>
						{{/each}}
						<option value="family">Family {{formatCurrency familyShare}}</option>
					</select>
				</div>
			</div>
			<div class="row pt-6 d-flex align-items-center">
				<div class="col-3 text-right">
					<label>Reallocate To:</label>
				</div>
				<div class="col-9">
					<select name="reallocate_to" class="form-control">
						<option value="family">Family {{formatCurrency familyShare}}</option>
						<option value="agency">Write Off - Agency</option>
						<option value="bad-debt">Write Off - Bad Debt</option>
						<option value="collections">Write Off - Collections</option>
						{{#each allocations}}
						<option value="{{index}}">{{originalAllocation.allocationDescription}} {{formatCurrency amount}}</option>
						{{/each}}
					</select>
				</div>
			</div>
			<div class="row pt-6 d-flex align-items-center">
				<div class="col-3 text-right">
					<label>Amount:</label>
				</div>
				<div class="col-9">
					<input name="amount" class="form-control" value="0.00">
				</div>
			</div>
			<div class="row pt-6 d-flex align-items-center">
				<div class="col-3 text-right">
					<label>Invoice Due Date:</label>
				</div>
				<div class="col-4">
					
					<input name="invoice-due-date" class="form-control" type="date">
					
				</div>
			</div>
			<div class="row d-flex align-items-center">
				<div class="col-3 text-right">
					<label></label>
				</div>
				<div class="col-9">
					For family reallocations, a new invoice will be issued. Specify a new due date or leave blank to use default grace period.		
				</div>
			</div>
			<div class="row pt-6 d-flex align-items-center">
				<div class="col-3 text-right">
					<label>Invoice Description:</label>
				</div>
				<div class="col-9">
					<textarea name="invoice-description" class="form-control">{{reallocationDescription}}</textarea>
				</div>
			</div>
		</div>
	</div>
	</form>
</template>