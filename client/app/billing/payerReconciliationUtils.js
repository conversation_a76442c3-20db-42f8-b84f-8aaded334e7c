import { initializeDateRangePicker, isValidDate } from './dateRangePickerComponent';

export function loadSavedReconciliations(instance) {
    if (instance.loadingSavedReconciliations && instance.loadingSavedReconciliations.get()) {
        return;
    }
   
    const startDate = instance.savedStartDate.format("YYYY-MM-DD"),
        endDate = instance.savedEndDate.format("YYYY-MM-DD");
    
    instance.loadingSavedReconciliations.set(true);
    instance.requestSavedReceived.set(false);
    instance.savedReconciliations.set(null);
    
    Meteor.callAsync("getPayerReconciliations", {
        startDate,
        endDate
    })
    .then((result) => {
        instance.requestSavedReceived.set(true);
        instance.loadingSavedReconciliations.set(false);
        instance.savedReconciliations.set(result);
    })
    .catch((error) => {
        instance.requestSavedReceived.set(true);
        instance.loadingSavedReconciliations.set(false);
        mpSwal.fire({
            icon: 'error',
            text: 'Error retrieving saved reconciliations. Please try again.',
            confirmButtonText: 'OK',
        });
    });
}

export function loadSavedReconciliationsDatePicker(instance) {
    const $input = $("#saved-reconciliations-date-range");
    
    if (!$input.length) {
        console.error("Saved reconciliations date picker not found");
        return;
    }
    
    if (!instance.savedStartDate) {
        instance.savedStartDate = moment().startOf("month");
    }
    if (!instance.savedEndDate) {
        instance.savedEndDate = moment().endOf("month");
    }
    
    initializeDateRangePicker({
        element: $input,
        instance: instance,
        startDate: instance.savedStartDate,
        endDate: instance.savedEndDate,
        startDateField: 'savedStartDate',
        endDateField: 'savedEndDate',
        onApplyCallback: loadSavedReconciliations
    });
} 