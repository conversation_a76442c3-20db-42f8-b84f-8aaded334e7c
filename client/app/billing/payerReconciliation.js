import { SavedDraftType } from "../../../lib/constants/draftConstants";
import { DraftUtils } from "../../../lib/util/draftUtils";
import { cloneDeep } from 'lodash';
import { PayerUtils } from "../../../lib/util/payerUtils";

import { hasPermissionToEdit, processSave } from "./_depositsPayments";
import { AvailableCustomizations } from '../../../lib/customizations';
import { getPeopleData } from "../../services/peopleMeteorService";
import { showModal } from "../main";
import './payerReconciliation.html';
import './payerReconciliationConfirmation.html';
import './savedReconciliations.html';
import './savedReconciliations.js';
import { Orgs } from "../../../lib/collections/orgs";
import { Relationships } from "../../../lib/collections/relationships";
import Swal from 'sweetalert2/dist/sweetalert2';
import { remapDownloadUrl } from '../../../mpweb';
import '../simpleModal/simpleModal';
import { loadSavedReconciliations, loadSavedReconciliationsDatePicker } from './payerReconciliationUtils';
import { initializeDateRangePicker, isValidDate } from './dateRangePickerComponent';

let draftObject = {};
Template.payerReconciliation.onCreated(function () {
	var self = this;
	self.requestMade = new ReactiveVar();
	self.requestReceived = new ReactiveVar();
	self.transactions = new ReactiveVar();
	self.allocations = new ReactiveVar([]);
	self.reportData = new ReactiveVar();
	self.savedDraft = null;
	self.existingDraft = null;
	self.canSave = new ReactiveVar(false);
	self.loadedDraftId = new ReactiveVar(null);
	self.savedDrafts = new ReactiveVar([]);
	self.currentView = new ReactiveVar("open");
	self.showNonFamilyFundingLine = new ReactiveVar(false);
	refreshDrafts(self);
	self.startDate = moment().startOf("month");
	self.endDate = moment().endOf("month");
	clearSession();
	allowRouting = false;
	self.familyMembersList = new ReactiveVar([]);

	routePromptFunc = () => {
		savePrompt(self, true);
	}
	self.autorun(() => {
		if (self.transactions.get()) {
			let familyMemberIds = [];
			self.transactions.get().map((recipient) => {
				Relationships.find({ targetId: recipient.personId, relationshipType: "family" }).map(r => {
					familyMemberIds.push(r.personId);
				});
			});

			familyMemberIds = [...new Set(familyMemberIds)];

			getPeopleData(
				{ "_id": { "$in": familyMemberIds } },
				{ fields: { _id: 1, lastName: 1, firstName: 1 } })
				.then(res => {
					self.familyMembersList.set(res);
				})
				.catch(err => {
					console.log(err);
				});
		}
	});
});

const refreshDrafts = (instance) => {
	Meteor.callAsync('retrieveDrafts', SavedDraftType.PAYER_RECONCILIATION)
	.then((result) => {
		instance.savedDrafts.set(result);
	})
	.catch((error) => {
		instance.savedDrafts.set(undefined);
	});
}

function clearSession() {
	Meteor.callAsync('clearPayerSession');
}

const savePrompt = (instance, fromRoute = false) => {
	if (!instance.canSave.get() || _.isEqual(instance.existingDraft, draftObject)) {
		if (fromRoute) {
			clearSession();
			instance.canSave.set(false);
			allowRouting = true;
			routePromptFunc = null;
			originalRouteFunc.apply(null, originalRouteArgs);
		}
		return;
	}
	const defaultDraftName = draftObject.payerName + ', ' +
		new moment(draftObject.startDate).format('MM/DD/YY') + ' - ' +
		new moment(draftObject.endDate).format('MM/DD/YY');
	mpSwal.fire({
		html: '<h4>Save payer reconciliation entries as draft?</h4>',
		showCancelButton: true,
		input: 'text',
		inputLabel: 'Draft Name',
		inputValue: defaultDraftName,
		confirmButtonText: 'Save as draft',
		cancelButtonText: 'Don\'t Save',
		customClass: {
			container: 'draft-modal',
			confirmButton: 'btn btn-primary font-weight-bolder',
			cancelButton: 'btn btn-secondary font-weight-bolder',
		},
		didOpen: function () {
			Swal.getConfirmButton().disabled = false;
			Swal.getInput().addEventListener('keyup', (e) => { Swal.getConfirmButton().disabled = !e.target.value });
			Swal.getInput().addEventListener('change', (e) => { Swal.getConfirmButton().disabled = !e.target.value });
		}
	}).then(result => {
		if (result.isConfirmed && result.value) {
			Meteor.callAsync('saveDraft', SavedDraftType.PAYER_RECONCILIATION, draftObject, result.value)
			.then((result) => {
				instance.loadedDraftId.set(result);
				refreshDrafts(instance);
			})
			.catch((error) => {
				instance.loadedDraftId.set(undefined);
				refreshDrafts(instance);
			});
			instance.existingDraft = draftObject;
			instance.canSave.set(false);
		}
		if (fromRoute) {
			clearSession();
			allowRouting = true;
			routePromptFunc = null;
			originalRouteFunc.apply(null, originalRouteArgs);
		}
	});
}

Template.payerReconciliation.onRendered(function() {
	const self = this;
	self.autorun(() => {
		const $openInput = $('#open-reconciliation-date-range');
		if ($openInput.length > 0) {
			initializeDateRangePicker({
				element: $openInput,
				instance: self,
				startDate: self.startDate,
				endDate: self.endDate,
				ranges: {
					'Today': [moment(), moment()],
					'Last 30 Days': [moment().subtract(29, 'days'), moment()],
					'Last 60 Days': [moment().subtract(59, 'days'), moment()],
					'Last 120 Days': [moment().subtract(119, 'days'), moment()],
					'Last 365 Days': [moment().subtract(364, 'days'), moment()],
				}
			});
		}
		
		if (self.currentView.get() === "saved") {
			const $savedTab = self.$(`.savedReconciliations`);
			if ($savedTab.length) {
				const savedTemplate = Blaze.getView(document.querySelector('.savedReconciliations'))._templateInstance;
				if (savedTemplate) {
					const $savedInput = $("#saved-reconciliations-date-range");
					if ($savedInput.length) {
						initializeDateRangePicker({
							element: $savedInput,
							instance: savedTemplate,
							startDate: savedTemplate.savedStartDate,
							endDate: savedTemplate.savedEndDate,
							startDateField: 'savedStartDate',
							endDateField: 'savedEndDate',
							onApplyCallback: (instance) => loadSavedReconciliations(instance)
						});
					}
				}
			}
		}
	});
});

Template.payerReconciliation.helpers({
	payers() {
		return Orgs.current() && Orgs.current().availablePayerSources(true);
	},
	cantSaveDraft() {
		return !Template.instance().canSave.get();
	},
	canDeleteDraft() {
		return !!Template.instance().loadedDraftId.get();
	},
	hasDraft() {
		return Template.instance().savedDrafts.get()?.length > 0;
	},
	requestMade() {
		return Template.instance().requestMade.get();
	},
	requestReceived() {
		return Template.instance().requestReceived.get();
	},
	recipients() {
		return _.sortBy(Template.instance().transactions.get(), r => r.name);
	},
	reportData() {
		return Template.instance().reportData.get();
	},
	currentView() {
		return Template.instance().currentView.get();
	},
	trueIfEq(a, b) {
		return a === b;
	},
	and() {
		return Array.from(arguments).slice(0, -1).every(Boolean);
	},
	availableFamilyPayers(personId) {
		const payers = [];
		Relationships.find({ targetId: personId, relationshipType: "family" }).map(r => {
			Template.instance().familyMembersList.get()
				.filter(f => r.personId === f._id)
				.map((p) => {
					payers.push({
						_id: p._id,
						name: p.firstName + ' ' + p.lastName
					});
				});
		});
		return payers;
	},
	totalsRunner() {
		const instance = Template.instance();
		setTimeout(function () {
			recalculateTotals(instance);
		}.bind(instance));
	},
	personOpen(personId) {
		return Boolean(Template.instance().savedDraft && Template.instance().savedDraft.invoiceAmounts &&
			Template.instance().savedDraft.invoiceAmounts.find((obj) => obj.personId === personId && Number(obj.amt) > 0));
	},
	getNonFamilyFundingCustomizationName() {
		return AvailableCustomizations.NON_FAMILY_FUNDS_ENABLED;
	},
	showNonFamilyFundingLine() {
		return Template.instance().showNonFamilyFundingLine.get();
	},
	getSelectedPayerName() {
		const payerSelect = document.querySelector('select[name=payer]');
		return (payerSelect?.selectedOptions ?? [])[0].text ?? '';
	},
	getReportPersonName(item) {
		if (item.isNonFamilyFunding) {
			const payerSelect = document.querySelector('select[name=payer]');
			return ((payerSelect?.selectedOptions ?? [])[0].text ?? '') + ' Non-Family Funds';
		}
		return item.personName;
	}
});

const recalculateTotalsDebounced = debounce(recalculateTotals, 300);
const recalculatePersonTotalDebounced = debounce(recalculatePersonTotal, 300);
const recalculatePersonInvoiceDebounced = debounce(recalculatePersonInvoice, 300);

function updateResults(instance) {
	const startDate = instance.startDate.format("MM/DD/YYYY"),
		endDate = instance.endDate.format("MM/DD/YYYY"),
		sDate = instance.startDate.format('YYYY-MM-DD'),
		eDate = instance.endDate.format('YYYY-MM-DD'),
		payer = $("select[name=payer]").val(),
		payerName = $('select[name=payer] :selected').text(),
		hidePaidItems = !($("input[name=show-paid]").prop("checked")),
		options = {
			startDate, endDate, payer, hidePaidItems
		}
	instance.requestMade.set(true);
	instance.requestReceived.set(false);
	instance.reportData.set(null);
	instance.transactions.set(null);
	instance.savedDraft = null;
	instance.showNonFamilyFundingLine.set(false);
	Meteor.callAsync('checkConflictingSessions', { payer, startDate: sDate, endDate: eDate })
	.then((results) => {

		if (results && results.length) {
			const text = PayerUtils.getSessionLabel(results[0], payerName);
			instance.requestMade.set(false);
			mpSwal.fire({
				icon: 'warning',
				text,
				confirmButtonText: 'OK',
			});
		} else {

			Meteor.callAsync("billingPayerReconciliationTransactions", options)
			.then((result) => {
				instance.requestReceived.set(true);
				console.log("result", result);
				Meteor.callAsync('startPayerSession', { payer, startDate: sDate, endDate: eDate });
				instance.transactions.set(result);
				recalculateTotals(instance);
			})
			.catch((error) => {
				instance.requestReceived.set(true);
				mpSwal.fire("Error", error.reason, "error");
			});
		}
	});
}

function debounce(func, wait) {
	let timeout;
	return function executedFunction(...args) {
		const later = () => {
			clearTimeout(timeout);
			func(...args);
		};
		clearTimeout(timeout);
		timeout = setTimeout(later, wait);
	};
}

window.addEventListener('beforeunload', () => {
	clearSession();
	return null;
});
Template.payerReconciliation.events({
	'beforeunload window': () => {
		clearSession();
	},
	"click #btn-load-draft": (e, i) => {
		const options = Object.fromEntries(
			i.savedDrafts.get().map(draft => {
				return [draft._id, DraftUtils.getDraftLabel(draft)];
			})
		);
		mpSwal.fire({
			html: '<h4>Load Saved Draft</h4>',
			showCancelButton: true,
			input: 'select',
			inputLabel: 'Draft Name',
			customClass: {
				container: 'draft-modal',
				confirmButton: 'btn btn-primary font-weight-bolder',
				cancelButton: 'btn btn-secondary font-weight-bolder',
			},
			inputOptions: options,
			confirmButtonText: 'Open',
			cancelButtonText: 'Cancel'
		}).then(result => {
			if (result.isConfirmed && result.value) {
				const draft = i.savedDrafts.get().find(draft => draft._id === result.value);
				loadDraft(draft.draftObject, i);
				i.loadedDraftId.set(result.value);
			}
		});
	},
	"click #btn-save-draft": (e, i) => {
		savePrompt(i)
	},
	"click #btn-delete-draft": (e, i) => {
		Meteor.callAsync('deleteDraft', i.loadedDraftId.get())
		.finally(() => {
			refreshDrafts(i);
			i.existingDraft = null;
			recalculateTotals(i);
			i.loadedDraftId.set(null);
		});
	},
	"click #btn-update-filters": (e, i) => {
		updateResults(i);
	},
	"click input[name='selections']": (e, i) => {
		const target = e.currentTarget;
		personId = $(target).data("person-id"),
			invoiceId = $(target).data("invoice-id");

		recalculatePersonInvoice(personId, invoiceId);
		recalculateTotals(i);
	},
	"change input[name='invoice-amount']": (e, i) => {
		const target = e.currentTarget;
		personId = $(target).data("person-id"),
			invoiceId = $(target).data("invoice-id"),
			amount = $(target).val();

		recalculatePersonTotal(personId);
		recalculateTotals(i);
		const data = i.transactions.get(),
			matchedTransaction = data.find(p => p.personId == personId)?.matchedLineItems?.find(li => li.invoiceId == invoiceId);
		console.log("updated total", amount, data, matchedTransaction);
		if (!isNaN(amount) && amount > matchedTransaction?.totalPayerOpenAmount)
			$(`tr.overpayment-info-row[data-invoice-id=${invoiceId}]`).show();
		recalculateTotals(i);
	},
	'change .overpayment-destination': (e, i) => {
		recalculateTotals(i);
	},
	"click input[name='selection-person']": (e, i) => {
		const target = e.currentTarget,
			checkState = $(target).prop("checked"),
			personId = $(target).data("person-id");

		$("input[name='selection-invoice'][data-person-id='" + personId + "']").each(function () {
			const invoiceId = $(this).data("invoice-id");
			$(this).prop("checked", checkState);
			$("input[name='selections'][data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']").prop('checked', checkState);
			recalculatePersonInvoice(personId, invoiceId);
		});


		recalculateTotals(i);
	},
	"click input[name='selection-invoice']": (e, i) => {
		const target = e.currentTarget,
			checkState = $(target).prop("checked"),
			personId = $(target).data("person-id"),
			invoiceId = $(target).data("invoice-id");

		$("input[name='selections'][data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']").prop('checked', checkState);
		recalculatePersonInvoice(personId, invoiceId);
		recalculateTotals(i);
	},
	"click #btn-submit-payments": (e, i) => {
		let allocations = [], itemCount = 0, totalAmount = 0.0;
		const payer = $("select[name=payer]").val();
		$("input[name='invoice-amount']").each(function (item) {
			const invoiceItem = $(this),
				invoiceId = invoiceItem.data("invoice-id"),
				personId = invoiceItem.data("person-id"),
				lineItemId = invoiceItem.data("lineitem-id"),
				invoiceOnlyAmount = invoiceItem.data("invoice-only-amount"),
				amount = invoiceItem.val();
			if (amount > 0) {
				const daySelections = $("input[name='selections'][data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']:checked").map(function (x) { return $(this).data("date"); }).get();
				let overpaymentDestination = undefined;
				if (!invoiceId) {
					overpaymentDestination = document.querySelector('.non-family-funding-destination').value;
				} else {
					overpaymentDestination = $("select.overpayment-destination[data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']").val();
				}

				const allocation = {
					personId,
					invoiceId,
					lineItemId,
					amount,
					overpaymentDestination
				};
				if (invoiceOnlyAmount) {
					allocation.invoiceOnlyAmount = invoiceOnlyAmount;
					itemCount += 1;
				} else if (!invoiceId) {
					++itemCount;
				} else {
					allocation.daySelections = daySelections;
					itemCount += daySelections.length;
				}
				allocations.push(allocation);
				Template.instance().allocations.set(allocations);
				totalAmount += parseFloat(amount);
			}
		});

		const modalDescription = "You are submitting " + itemCount + " payment(s) totaling " + numeral(totalAmount).format("$0.00");
		showModal("simpleModal", {
			title: "Confirm Payment(s)",
			template: "payerReconciliationConfirmation",
			size: "medium",
			justifyFooter: 'center',
			data: {
				description: modalDescription,
				totalAmount,
				actionButtonLabel: "Yes, confirm payment(s)",
				cancelButtonLabel: "Cancel",
				modalsize: "md"
			},
			onSave: async (e, modalInstance, formFieldData) => {
				const checkNumberValue = formFieldData.checkNumber || document.getElementById("checkNumber").value;
				const fileInput = document.getElementById("supportingDocumentation");
				if (!checkNumberValue) {
					mpSwal.fire("Check Number field is required");
					return;
				}
				else {
					if (fileInput && fileInput.files.length > 0) {
						const file = fileInput.files[0];
						const allowedTypes = [
							'application/pdf',
							'application/msword',
							'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
							'image/jpeg',
							'image/png',
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
						];
						if (!allowedTypes.includes(file.type)) {
							mpSwal.fire("Unsupported file format. Please upload a PDF, JPG, PNG, DOC, DOCX, XLS, or XLSX.");
							return;
						}
					}
					$("#simpleModal").modal("hide");
					var uploadFile = fileInput.files[0];
					
					const processPayment = (supportingDocument) => {
						Meteor.callAsync("creditPayerMultiple", {
							allocations,
							payer,
							startDate: i.startDate.format("MM/DD/YYYY"),
							endDate: i.endDate.format("MM/DD/YYYY"),
							checkNumber: checkNumberValue,
							supportingDocument: supportingDocument
						})
						.then((result) => {
							mpSwal.close();
							$(e.currentTarget).prop("disabled", false).text("Apply");
							i.transactions.set(null);
							i.requestMade.set(false);
							i.showNonFamilyFundingLine.set(false);
							if (!Orgs.current().billing?.billingMaps?.undepositedFunds?.accountName) {
								mpSwal.fire("Payment(s) applied successfully");
							} else {
								mpSwal.fire({
									title: "Payment(s) applied successfully.\nWould you like to create a deposit?",
									showConfirmButton: true,
									showCancelButton: true,
									cancelButtonText: 'Continue without deposit',
									confirmButtonText: 'Create Deposit'
								}).then(result => {
									if (result.isConfirmed) {
										const currentDate = new Date()
										currentDate.setHours(0, 0, 0, 0);
										const startOfDayTimestamp = currentDate.getTime();
										currentDate.setHours(23, 59, 59, 999);
										const endOfDayTimestamp = currentDate.getTime();
										Meteor.callAsync("billingUndepositedPayments", {
											startOfDayTimestamp,
											endOfDayTimestamp
										})
										.then((result) => {
											const availableTransactions = result.transactions
											const filteredTransactions = availableTransactions.filter(transaction => {
												const hasMatchingAllocation = allocations.some(allocation => {
													return allocation.invoiceId === transaction.invoiceId;
												});
												return hasMatchingAllocation;
											})
											let depositInfo
											const items = filteredTransactions;
											showModal("simpleModal", {
												title: "Create/Update Deposit",
												template: "billingUpsertDeposit",
												data: {
													itemCount: items.length,
													itemAmount: numeral(_.reduce(items, (memo, item) => { return memo + item.creditAmount; }, 0)).format("$0,000.00"),
													memo: depositInfo && depositInfo.memo
												},
												onSave: (e, formInstance, formFieldData) => {
													if ((!hasPermissionToEdit() || !Orgs.current().requireRoles()) && !Orgs.current().billing.allowDepositReportEdit) {
														mpSwal.fire({
															title: 'Confirm creation of Bank Deposit. This cannot be altered once saved.',
															showCancelButton: true,
															confirmButtonText: 'Confirm',
														}).then(result => {
															if (result.isConfirmed) {
																processSave(items, depositInfo, formFieldData, e, i);
															} else {
																callGetDeposits(i);
															}
														});
													} else {
														processSave(items, depositInfo, formFieldData, e, i);
													}
												}
												});
										})
										.catch((err) => {
											console.log(err)
										});
									}
								})
							}
							i.reportData.set(result);
							i.canSave.set(false);
							clearSession();
							draftObject = {};
							if (i.loadedDraftId.get()) {
								Meteor.callAsync('deleteDraft', i.loadedDraftId.get())
								.finally(() => {
									refreshDrafts(i);
									i.loadedDraftId.set(null);
								});
							}
						})
						.catch((err) => {
							mpSwal.close();
							$(e.currentTarget).prop("disabled", false).text("Apply");
							mpSwal.fire("Error: " + err.reason);
						});
					};
					
					if (uploadFile) {
						const metaContext = {tokenId: tokenString()};
						const uploader = new Slingshot.Upload("mySupportingDocumentUploads", metaContext);
						
						uploader.send(uploadFile, function (error, downloadUrl) {
							if (error) {
								mpSwal.close();
								mpSwal.fire("Error uploading document", error, "error");
							} else {
								downloadUrl = remapDownloadUrl(downloadUrl);
								console.log("download = " + downloadUrl);
								const mediaType = uploadFile.type;
								const supportingDocument = {
									fileName: uploadFile.name,
									fileType: mediaType,
									documentUrl: downloadUrl,
								};
								
								processPayment(supportingDocument);
							}
						});
					} else {
						processPayment(null);
					}
				}
			}
		});
	},
	"click .person-collapse": (e, i) => {
		const personId = $(e.currentTarget).data("person-id"),
			target = $("tbody[data-person-id='" + personId + "']");
		if (target.hasClass("d-none")) {
			$(e.currentTarget).children("i.fa-caret-down").removeClass("d-none");
			$(e.currentTarget).children("i.fa-caret-right").addClass("d-none");
		} else {
			$(e.currentTarget).children("i.fa-caret-down").addClass("d-none");
			$(e.currentTarget).children("i.fa-caret-right").removeClass("d-none");
		}
		target.toggleClass("d-none");
		return false;
	},
	"click #expand-all": (e, i) => {
		e.preventDefault();
		$(".person-collapse .fa-caret-down").removeClass("d-none");
		$(".person-collapse .fa-caret-right").addClass("d-none");
		$("tbody").removeClass("d-none");
		return false;
	},
	"click #collapse-all": (e, i) => {
		e.preventDefault();
		$(".person-collapse .fa-caret-down").addClass("d-none");
		$(".person-collapse .fa-caret-right").removeClass("d-none");
		$("tbody").addClass("d-none");
		return false;
	},
	"click #select-all": (e, i) => {
		e.preventDefault();
		$("input[name='selection-invoice']").each(function () {
			const invoiceId = $(this).data("invoice-id"), personId = $(this).data("person-id");
			$("input[name='selection-person']").prop("checked", true);
			$(this).prop("checked", true);
			$("input[name='selections'][data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']").prop('checked', true);
			recalculatePersonInvoice(personId, invoiceId);
		});

		recalculateTotals(i);
	},
	"click #deselect-all": (e, i) => {
		e.preventDefault();
		$("input[name='selection-invoice']").each(function () {
			const invoiceId = $(this).data("invoice-id"), personId = $(this).data("person-id");
			$("input[name='selection-person']").prop("checked", false);
			$(this).prop("checked", false);
			$("input[name='selections'][data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']").prop('checked', false);
			recalculatePersonInvoice(personId, invoiceId);
		});

		recalculateTotals(i);
	},
	"click #btn-export": (e, i) => {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvReportData'), outputFile]);
	},
	"click .add-non-family-funds": (e, i) => {
		i.showNonFamilyFundingLine.set(true);
		recalculateTotals(i);
	},
	'click .nav-link-reconciliation-view': function(e, i) {
		const view = $(e.currentTarget).data('view');
		i.currentView.set(view);
		
		if (view === "saved") {
			i.reportData.set(null);
		}
		
		setTimeout(() => {
			if (view === "open") {
				const $openInput = $('#open-reconciliation-date-range');
				if ($openInput.length > 0) {
					loadDateRangePicker(i);
				}
			} else if (view === "saved") {
				const savedTemplate = Template.instance().$(`.savedReconciliations`).length ? 
					Blaze.getView(document.querySelector('.savedReconciliations'))._templateInstance : null;
				
				if (savedTemplate) {
					const $savedInput = $("#saved-reconciliations-date-range");
					if ($savedInput.length) {
						initializeDateRangePicker({
							element: $savedInput,
							instance: savedTemplate,
							startDate: savedTemplate.savedStartDate,
							endDate: savedTemplate.savedEndDate,
							startDateField: 'savedStartDate',
							endDateField: 'savedEndDate',
							onApplyCallback: loadSavedReconciliations
						});
					}
					
					loadSavedReconciliations(savedTemplate);
				}
			}
		}, 100);
	},
	"click #btn-update-saved-filters": function(e, i) {
		loadSavedReconciliations(i);
	},
	"click .download-document": function(e, i) {
		e.preventDefault();
		const documentId = $(e.currentTarget).data('document-id');
		const fileName = $(e.currentTarget).data('file-name');
		const fileType = $(e.currentTarget).data('file-type');
		const documentUrl = $(e.currentTarget).data('document-url');
		
		if (!documentUrl) return;
		
		setTimeout(() => {
			window.open(documentUrl, '_blank');
		}, 500);
	}
});

function loadDraft(draft, instance) {
	instance.existingDraft = cloneDeep(draft);
	if (draft.payer) {
		const startDate = new moment(draft.startDate).format("MM/DD/YYYY");
		const endDate = new moment(draft.endDate).format("MM/DD/YYYY");
		const payer = draft.payer;
		const hidePaidItems = draft.hidePaidItems;
		const options = { startDate, endDate, payer, hidePaidItems };
		Meteor.callAsync("billingPayerReconciliationTransactions", options)
		.then((result) => {
			instance.requestReceived.set(true);
			instance.transactions.set(result);
			instance.reportData.set(null);
			instance.savedDraft = draft;
			setTimeout(() => {
				console.log({ draft });
				document.querySelectorAll('.payer-reconcile-table input[name="selection-person"]').forEach(node => {
					node.checked = draft.personBoxes?.includes(node.dataset.personId);
				});
				document.querySelectorAll('.payer-reconcile-table input[name="selection-invoice"]').forEach(node => {
					node.checked = draft.invoiceBoxes?.includes(node.dataset.invoiceId);
				});
				document.querySelectorAll('.payer-reconcile-table input[name="invoice-amount"]').forEach(node => {
					let amt = 0;
					const lineItemId = node.dataset.lineItemId;
					const invoiceId = node.dataset.invoiceId;
					const amtObj = draft.invoiceAmounts.find(obj => (lineItemId && lineItemId === obj.lId) || (!lineItemId && invoiceId === obj.invoiceId));
					if (amtObj) {
						amt = Number(amtObj.amt);
					}
					node.value = amt;
				});
				document.querySelectorAll('.payer-reconcile-table .overpayment-info-row').forEach(node => {
					const invoiceId = node.dataset.invoiceId;
					const overpay = draft.overpays?.find(obj => obj.invoiceId === invoiceId);
					if (!overpay) {
						node.style.display = 'none';
						return;
					}
					node.style.removeProperty('display');
					node.querySelector('.overpayment-destination').value = overpay.destination;
				});
				document.querySelectorAll('.payer-reconcile-table .child-amount').forEach(node => {
					const personId = node.dataset.personId;
					const amt = draft.invoiceAmounts.filter(obj => obj.personId === personId)
						.map(amtObj => Number(amtObj.amt))
						.reduce((a, b) => a + b, 0);
					node.innerText = numeral(amt).format('$0.00');
				});
				document.querySelectorAll('.payer-reconcile-table .person-collapse').forEach(node => {
					const personId = node.dataset.personId;
					const hasValues = draft.invoiceAmounts.filter(obj => obj.personId === personId && Number(obj.amt) > 0).length > 0;
					if (hasValues) {
						node.querySelector('.fa-caret-down').classList.remove('d-none');
						node.querySelector('.fa-caret-right').classList.add('d-none');
					} else {
						node.querySelector('.fa-caret-down').classList.add('d-none');
						node.querySelector('.fa-caret-right').classList.remove('d-none');
					}
					const section = document.querySelector('.payer-reconcile-table .payer-body-section[data-person-id="' + personId + '"]');
					if (section) {
						if (hasValues) {
							section.classList.remove('d-none');
						} else {
							section.classList.add('d-none');
						}
					}
				});
			}, 100)
		});
		instance.startDate = new moment(draft.startDate);
		loadDateRangePicker(instance);
		$("select[name=payer]").val(draft.payer);
		$("input[name=show-paid]").prop("checked", !draft.hidePaidItems);
	}
}

function loadDateRangePicker(instance) {
	$('input[name="invoices-date-range"]').daterangepicker(
		{
			startDate: instance.startDate,
			endDate: instance.endDate,
			ranges: {
				'Today': [moment(), moment()],
				'Last 30 Days': [moment().subtract(29, 'days'), moment()],
				'Last 60 Days': [moment().subtract(59, 'days'), moment()],
				'Last 120 Days': [moment().subtract(119, 'days'), moment()],
				'Last 365 Days': [moment().subtract(364, 'days'), moment()],
			}
		}
	).on('apply.daterangepicker', function (ev, picker) {
		instance.startDate = picker.startDate;
		instance.endDate = picker.endDate;
	});
}

function recalculatePersonInvoice(personId, invoiceId) {
	let amount = 0.0;

	$("input[name='selections'][data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']:checked").each(function (item) {
		amount += $(this).data('amount');
	});
	$("input[data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "'][data-invoice-only-amount]:checked").each(function (item) {
		amount += $(this).data('invoice-only-amount');
	});
	$("input[data-person-id='" + personId + "'][data-invoice-id='" + invoiceId + "']").val(numeral(amount).format("0.00"));
	recalculatePersonTotal(personId);
}

function recalculatePersonTotal(personId) {
	let totalAmount = 0.0;

	$("input[data-person-id='" + personId + "'][name='invoice-amount']").each(function (item) {
		totalAmount += parseFloat($(this).val());
	});

	$(".child-amount[data-person-id='" + personId + "']").text(numeral(totalAmount).format("$0.00"));
}

function recalculateTotals(instance) {
	let amount = 0.0;
	let itemCount = $("input[name='selections']:checked").length + $("input[name='selection-invoice'][data-invoice-only-amount]:checked").length;
	$("input[name='invoice-amount']").each(function (item) {
		amount += parseFloat($(this).val());
	});
	if (instance.showNonFamilyFundingLine.get()) {
		itemCount += 1;
	}

	$("#total-item-amount").text(numeral(amount).format("$0.00"));
	$("#total-item-count").text(itemCount);
	instance.canSave.set(false);
	if (amount > 0) {
		draftObject = {
			startDate: instance.startDate.format('YYYY-MM-DD'),
			endDate: instance.endDate.format('YYYY-MM-DD'),
			payer: $("select[name=payer]").val(),
			payerName: $("select[name=payer] :selected").text(),
			hidePaidItems: !($("input[name=show-paid]").prop("checked")),
		}
		const personBoxes = document.querySelectorAll('input[name="selection-person"]:checked');
		draftObject.personBoxes = [];
		for (const box of personBoxes) {
			draftObject.personBoxes.push(box.dataset.personId);
		}
		const invoiceBoxes = document.querySelectorAll('input[name="selection-invoice"]:checked');
		draftObject.invoiceBoxes = [];
		for (const box of invoiceBoxes) {
			draftObject.invoiceBoxes.push(box.dataset.invoiceId);
		}
		draftObject.invoiceAmounts = [];
		const invoiceAmounts = document.querySelectorAll('input[name="invoice-amount"]');
		for (const invAmount of invoiceAmounts) {
			draftObject.invoiceAmounts.push({
				personId: invAmount.dataset.personId,
				invoiceId: invAmount.dataset.invoiceId,
				lid: invAmount.dataset.lineitemId ?? null,
				amt: invAmount.value,
			})
		}
		draftObject.overpays = [];
		const overpays = document.querySelectorAll('.overpayment-info-row');
		for (const overpayRow of overpays) {
			if (overpayRow.style.display === 'none') {
				continue;
			}
			draftObject.overpays.push({
				invoiceId: overpayRow.dataset.invoiceId,
				destination: overpayRow.querySelector('.overpayment-destination').value
			});
		}
		if (!_.isEqual(draftObject, instance.existingDraft)) {
			instance.canSave.set(true);
		}
	}
}

var tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};
