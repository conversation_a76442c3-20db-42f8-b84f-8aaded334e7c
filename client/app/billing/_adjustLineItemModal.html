<template name="adjustLineItemModal">
    <div id="adjustLineItemModal" class="modal fade">
        <div class="modal-dialog modal-sm" style="width: 100%;height: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Adjust Payment</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <form id="adjustLineItemForm">
                        <div class="radio-list">
                            <label class="radio radio-primary">
                                <input data-cy="apply-to-a-credit-memo" type="radio" name="assignmentType" value="credit" {{checkedIfEq selected 'credit'}}>
                                <span></span>
                                Apply to a credit memo
                            </label>
                            <label class="radio radio-primary">
                                <input data-cy="apply-to-an-invoice" type="radio" name="assignmentType" value="invoice" {{checkedIfEq selected 'invoice'}}>
                                <span></span>
                                Apply to an invoice
                            </label>
                        </div>
                        {{#if trueIfEq selected 'invoice'}}
                            <div class="row mt-4 form-group invoice-select">
                                {{#if invoices}}
                                    <div class="col-6">
                                        <label for="invoiceSelect">
                                            Invoice
                                        </label>
                                        <select
                                                class="form-control rounded select-single"
                                                id="invoiceSelect"
                                                name="invoiceSelect"
                                        >
                                            {{#each invoices}}
                                                <option value="{{_id}}">{{invoiceNumber}}</option>
                                            {{/each}}
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="creditAmount">
                                            Amount
                                        </label>
                                        <input
                                                id="creditAmount"
                                                name="creditAmount"
                                                type="text"
                                                class="form-control rounded"
                                                pattern="[0-9]+(\.[0-9][0-9]?)?"
                                                value="0.00"
                                        />
                                    </div>
                                {{else}}
                                    <div class="col-12 text-center" style="color: var(--danger)">
                                        No invoices found with open amounts
                                    </div>
                                {{/if}}
                            </div>
                        {{/if}}
                    </form>
                </div>
                <div class="modal-footer d.flex justify-content-center align-content-center">
                    <button data-cy="adjust-payment-save-btn" type="button" class="btn btn-primary font-weight-bolder mr-2" id="saveButton" disabled="{{saveDisabled}}">Save</button>
                    <button data-cy="adjust-payment-cancel-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>