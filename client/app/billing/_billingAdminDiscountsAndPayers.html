<template name="billingAdminDiscountsAndPayers">
    <div class="row">
        <div class="col-xl-3 col-lg-3">
            <h3>Discounts and Payers</h3>
        </div>
    </div>
    <div class="row mt-6">
        <div class="col-xl-3 col-lg-3 text-right font-size-h6">
            Discounts
        </div>
        <div class="col-lg-6 col-md-9 col-sm-12">
            <div data-cy="manage-discounts" class="btn btn-primary font-weight-bolder" id="btnShowDiscounts">Manage Discounts</div>
            <div id="discountsPanel" style="display:none;padding: 10px;">
                <div class="btn btn-primary font-weight-bolder" id="btnHideDiscounts">Hide Discounts</div>
                <table class="table mt-4">
                    <tbody>
                    <tr>
                        <th>Code</th>
                        <th>Description</th>
                        {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                            <th>Ledger</th>
                        {{/if}}
                        <th style="text-align:center">Expires</th>
                        {{#if hasCustomization "billing/plans/allowSingleDiscount"}}
                            <th style="text-align:center">Overrides</th>
                        {{/if}}
                        <th>Default</th>
                        <th>Archived</th>
                        <th></th>
                    </tr>
                    {{#each availableDiscounts}}
                        <tr data-cy="discount-data" data-id="{{type}}" class="discountDisplayRow">
                            <td data-cy="discount-code">{{type}}</td>
                            <td data-cy="discount-description">{{description}}</td>
                            {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                                <td data-cy="discount-ledger">{{ledgerAccountName}}</td>
                            {{/if}}
                            <td data-cy="discount-expires" style="text-align:center">{{#if expiresWithGracePeriod}}X{{/if}}</td>
                            {{#if hasCustomization "billing/plans/allowSingleDiscount"}}
                                <td style="text-align:center">{{#if overrideSingleDiscount}}X{{/if}}</td>
                            {{/if}}
                            <td data-cy="discount-default">{{#if amount}}{{#if trueIfEq amountType "dollars"}}{{formatCurrency
                                    amount}}{{else}}{{formatNumber amount "0.00"}}%{{/if}}{{/if}}</td>
                            <td data-cy="discount-archived">{{#if archived}}X{{/if}}</td>
                            <td>{{#if showEditOptions}}
                                <div class="d-flex flex-row">
                                    <a data-cy="edit-discount" href="#" class="btnEditDiscount" data-id="{{type}}"
                                       style="margin-right:10px"><i class="fad fad-primary fa-pencil"></i></a>
                                    <!--<a href="#" class="btnRemoveDiscount" data-id="{{type}}"><i class="fad fad-primary fa-times"></i></a>-->
                                    <a data-cy="archived-discount" href="#" class="btnArchiveDiscount" data-id="{{type}}"><i
                                            class="fad fad-primary fa-archive"></i></a>
                                </div>
                            {{/if}}
                            </td>
                        </tr>
                        <tr data-cy="discount-edit-row-{{type}}" style="display:none" data-id="{{type}}" class="discountEditRow">
                            <td colspan=10>
                                <form class="frmDiscountEdit" data-id="{{type}}">
                                    <div class="col-xs-6">
                                        Discount Code: {{type}}<br/>
                                        <br/>
                                        <input data-cy="edit-discount-desc" type="text" class="txtDiscountDescription form-control"
                                               placeholder="Description" value="{{description}}"><br/>
                                        {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                                            Ledger:<br/>
                                            <input data-cy="edit-discount-ledger" type="text" class="txtDiscountLedgerAccountName form-control"
                                                   placeholder="Ledger Account"
                                                   value="{{ledgerAccountName}}"><br/>
                                        {{/if}}
                                        <div class="checkbox-inline mb-4">
                                            <label class="checkbox checkbox-primary">
                                                <input data-cy="edit-chk-expires" type="checkbox" class="chkExpiresWithGracePeriod"
                                                       checked={{expiresWithGracePeriod}}>
                                                <span></span>
                                                Expires at end of grace period
                                            </label>
                                            {{#if hasCustomization "billing/plans/allowSingleDiscount"}}
                                                <label class="checkbox checkbox-primary">
                                                    <input type="checkbox" class="chkOverrideSingleDiscountRule"
                                                           checked={{overrideSingleDiscount}}>
                                                    <span></span>
                                                    Override single discount rule
                                                </label>
                                            {{/if}}
                                        </div>
                                    </div>
                                    <div class="col-xs-4"><input data-cy="edit-discount-amount" type="text"
                                                                 class="txtDiscountAmount form-control"
                                                                 placeholder="Amount" value="{{amount}}"><br/>
                                        <select data-cy="edit-discount-amount-type" class="selectDiscountAmountType form-control">
                                            <option value="dollars" {{selectedIfEqual amountType "dollars"}}>
                                                Dollars
                                            </option>
                                            <option value="percent" {{selectedIfEqual amountType "percent"}}>
                                                Percent
                                            </option>
                                        </select>
                                    </div>
                                    <div class="col-xs-2 mt-2">
                                        <button data-cy="save-edit-discount" class="btn btn-primary font-weight-bolder btn-sm btnSaveEditDiscount mr-2"
                                                data-id="{{type}}">Save
                                        </button>
                                        <button class="btn btn-secondary font-weight-bolder btn-sm btnCancelEditDiscount"
                                                data-id="{{type}}">Cancel
                                        </button>
                                    </div>
                                </form>
                            </td>
                        </tr>
                    {{/each}}
                    </tbody>
                </table>
                {{#if showEditOptions}}
                    <div data-cy="add-new-discount" class="btn btn-primary font-weight-bolder" id="btnAddNewDiscount"><i
                            class="fad fa-plus fa-swap-opacity text-white mr-2"></i>Add New Discount
                    </div>
                {{/if}}
                <div id="rowAddDiscount" class="row ml-4" style="display:none">
                    <form id="frmAddDiscount">
                        <div class="col-xs-6">
                            <input data-cy="discount-code-input" type="text" id="txtDiscountType" class="form-control"
                                   placeholder="Discount Code"><br/>
                            <input data-cy="discount-desc-input" type="text" id="txtDiscountDescription" class="form-control"
                                   placeholder="Description"><br/>
                            {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                                Ledger:<br/>
                                <input data-cy="discount-ledger-input" type="text" id="txtDiscountLedgerAccountName" class="form-control"
                                       placeholder="Ledger Account">
                            {{/if}}
                            <div class="checkbox-inline my-4">
                                <label class="checkbox checkbox-primary">
                                    <input data-cy="chk-expires" type="checkbox" id="chkExpiresWithGracePeriod">
                                    <span></span>
                                    Expires at end of grace period
                                </label>
                                {{#if hasCustomization "billing/plans/allowSingleDiscount"}}
                                    <label class="checkbox checkbox-primary">
                                        <input type="checkbox" id="chkOverrideSingleDiscountRule">
                                        <span></span>
                                        Override single discount rule
                                    </label>
                                {{/if}}
                            </div>
                        </div>
                        <div class="col-xs-4"><input data-cy="discount-amount-input" type="text" id="txtDiscountAmount" class="form-control"
                                                     placeholder="Amount"><br/>
                            <select data-cy="discount-amount-type" id="selectDiscountAmountType" class="form-control">
                                <option value="dollars">Dollars</option>
                                <option value="percent">Percent</option>
                            </select>
                        </div>
                        <div class="col-xs-1 mt-4">
                            <button data-cy="save-discount" class="btn btn-primary font-weight-bolder btn-sm" id="btnSaveDiscount">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-8">
        <div class="col-xl-3 col-lg-3 text-right font-size-h6">
            Payers
        </div>
        <div class="col-lg-6 col-md-9 col-sm-12">
            <div data-cy="manage-payers" class="btn btn-primary font-weight-bolder" id="btnShowPayers">Manage Payers</div>
            <div id="payersPanel" style="display:none;padding: 10px;">
                <div data-cy="hide-payers" class="btn btn-primary font-weight-bolder" id="btnHidePayers">Hide Payers</div>
                <table class="table mt-4">
                    <tbody>
                    <tr>
                        <th>Code</th>
                        <th>Description</th>
                        {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                            <th>Ledger Account</th>
                        {{/if}}
                        {{#if hasCustomization "billing/configuration/payerCashPostingLedger"}}
                            <th>Cash/Posting Ledger Account</th>
                        {{/if}}
                        <th>Archived</th>
                        <th></th>
                    </tr>
                    {{#each availablePayers}}
                        <tr data-cy="payer-data" data-id="{{type}}" class="payerDisplayRow">
                            <td data-cy="payer-code">{{type}}</td>
                            <td data-cy="payer-description">{{description}}</td>
                            {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                                <td data-cy="payer-ledger-account">{{ledgerAccountName}}</td>
                            {{/if}}
                            {{#if hasCustomization "billing/configuration/payerCashPostingLedger"}}
                                <td data-cy="payer-cash-ledger-account">{{cashLedgerAccountName}}</td>
                            {{/if}}
                            <td data-cy="payer-archived">{{#if archived}}X{{/if}}</td>
                            <td>
                                {{#if showEditOptions}}
                                    <div class="d-flex flex-row">
                                        <a data-cy="edit-payer-btn" href="#" class="btnEditPayer" data-id="{{type}}"
                                           style="margin-right:10px" title="Edit"><i
                                                class="fad fad-primary fa-pencil"></i></a>
                                        <!--<a href="#" class="btnRemovePayer" data-id="{{type}}"><i class="fad fad-primary fa-times"></i></a>-->
                                        <a data-cy="archive-payer-btn" href="#" class="btnArchivePayer" data-id="{{type}}"
                                           title="Archive"><i class="fad fad-primary fa-archive"></i></a>
                                    </div>
                                {{/if}}
                            </td>
                        </tr>
                        <tr data-cy="payer-edit-row-{{type}}" data-id="{{type}}" class="payerEditRow" style="display:none">
                            <td colspan=3>
                                <form class="frmPayerEdit" data-id="{{type}}">
                                    <div class="col-xs-3">Payer Code: {{type}}</div>
                                    <div class="col-xs-4 mt-2">
                                        <input data-cy="edit-payer-desc"
                                                type="text"
                                                class="txtPayerDescription form-control"
                                                placeholder="Description"
                                                value="{{description}}"
                                        >
                                    </div>
                                    {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                                        <div class="col-xs-3 mt-2">
                                            <input data-cy="edit-payer-ledger-account" type="text"
                                                   class="txtPayerLedgerAccountName form-control"
                                                   placeholder="Ledger Account"
                                                   value="{{ledgerAccountName}}"
                                            >
                                        </div>
                                    {{/if}}
                                    {{#if hasCustomization "billing/configuration/payerCashPostingLedger"}}
                                        <div class="col-xs-3 mt-2">
                                            <input data-cy="edit-payer-cash-ledger-account"
                                                    type="text"
                                                    class="txtCashLedgerAccountName form-control"
                                                    placeholder="Cash/Posting Ledger Account"
                                                    value="{{cashLedgerAccountName}}"
                                            >
                                        </div>
                                    {{/if}}
                                    <div class="col-xs-2 mt-4">
                                        <button data-cy="save-edit-payer" class="btn btn-primary font-weight-bolder mr-2 btn-sm btnSaveEditPayer"
                                                data-id="{{type}}">Save
                                        </button>
                                        <button class="btn btn-secondary font-weight-bolder btn-sm btnCancelEditPayer"
                                                data-id="{{type}}">Cancel
                                        </button>
                                    </div>
                                </form>
                            </td>
                        </tr>
                    {{/each}}
                    </tbody>
                </table>
                {{#if showEditOptions}}
                    <div data-cy="add-new-payer" class="btn btn-primary font-weight-bolder" id="btnAddNewPayer">
                        <i class="fad fa-plus fa-swap-opacity text-white mr-2"></i>
                        Add New Payer
                    </div>
                {{/if}}
                <div id="rowAddPayer" class="row ml-4" style="display:none">
                    <form id="frmAddPayer">
                        <div class="col-xs-3">
                            <input data-cy="payer-code-input"
                                    type="text"
                                    id="txtPayerType"
                                    class="form-control"
                                    placeholder="Payer Code"
                            >
                        </div>
                        <div class="col-xs-4 mt-2">
                            <input data-cy="payer-desc-input"
                                    type="text"
                                    id="txtPayerDescription"
                                    class="form-control"
                                    placeholder="Description"
                            >
                        </div>
                        {{#if hasCustomization "billing/requireLedgerAccountName/enabled"}}
                            <div class="col-xs-3 mt-2">
                                <input data-cy="payer-ledger-input"
                                        type="text"
                                        id="txtPayerLedgerAccountName"
                                        class="form-control"
                                        placeholder="Ledger Account"
                                >
                            </div>
                        {{/if}}
                        {{#if hasCustomization "billing/configuration/payerCashPostingLedger"}}
                            <div class="col-xs-3 mt-2">
                                <input data-cy="payer-cash-ledger-input"
                                        type="text"
                                        id="txtCashLedgerAccountName"
                                        class="form-control"
                                        placeholder="Cash/Posting Ledger Account"
                                >
                            </div>
                        {{/if}}
                        <div class="col-xs-2 mt-4">
                            <button data-cy="save-payer"
                                    class="btn btn-primary font-weight-bolder btn-sm"
                                    id="btnSavePayer"
                            >
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>