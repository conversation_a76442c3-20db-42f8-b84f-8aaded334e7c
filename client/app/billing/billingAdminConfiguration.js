import { Orgs } from "../../../lib/collections/orgs";
import { AvailableCustomizations } from "../../../lib/customizations";
import './billingAdminConfiguration.html';
import './billingAdminPlans';
import './billingAdminCouponCodes';
import './billingAdminSettings';

Template.billingAdminConfiguration.helpers({
    canSeeCouponCodes() {
        const org = Orgs.current();
        return (org && org.hasCustomization(AvailableCustomizations.COUPON_CODES))
    }
});
