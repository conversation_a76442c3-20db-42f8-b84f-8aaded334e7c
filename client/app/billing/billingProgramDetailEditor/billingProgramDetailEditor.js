import { Template } from 'meteor/templating';
import './billingProgramDetailEditor.html';

Template.billingProgramDetailEditor.onCreated(function() {
  this.programDetails = Template.instance().data.data.programDetails;
});
Template.billingProgramDetailEditor.onRendered(function() {
  if (!Promise.allSettled) {
      Promise.allSettled = function(promises) {
          return Promise.all(promises.map(p => Promise.resolve(p).then(
              value => ({
                  status: 'fulfilled',
                  value
              }),
              reason => ({
                  status: 'rejected',
                  reason
              })
          )));
      };
  }

  tinymce.init({
      selector: '#programDetailEditor',
      plugins: 'lists link code advlist inlinecss fontsize',
      toolbar: 'fontsize bold italic forecolor backcolor | bullist numlist loweralpha | link | code',
      height: '100%',
      width: '100%',
      resize: false,
      autoresize: true,
      font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 18pt 24pt 30pt 36pt 48pt 60pt 72pt 96pt',
      advlist_bullet_styles: 'default',
      advlist_number_styles: 'default,lower-alpha',
      statusbar: false,
      setup: (editor) => {
        editor.on('init', () => {
          if (this.programDetails) {
            editor.setContent(this.programDetails);
          } else {
            editor.setContent('');
          }
        });
      }
  });
});

Template.billingProgramDetailEditor.onDestroyed(function() {
  tinymce.remove();
});