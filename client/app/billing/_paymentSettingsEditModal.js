import { Orgs } from '../../../lib/collections/orgs';
import { PaymentSettingsClientService } from '../../services/billing/PaymentSettingsClientService';
import './_paymentSettingsEditModal.html';

Template.paymentSettingsEditModal.onCreated(function () {
    const org = Orgs.current();
    this.paymentSettingsService = new PaymentSettingsClientService(org);
});

Template.paymentSettingsEditModal.rendered = function() {
    $("#manualPayTypesSelect").selectize({
        placeholder: 'Choose which types of payments can be manually applied at this site.',
        plugins: ['remove_button']
    });
}

Template.paymentSettingsEditModal.helpers({
    getManualPayTypes() {
        return Template.instance().paymentSettingsService.getManualPayTypes();
    },

    getIsSelected(type, source) {
        return Template.instance().paymentSettingsService.isSelected(type, source) ? 'selected' : '';
    },

    isOptionalPaymentMethodDisabled() {
        return Template.instance().paymentSettingsService.isOptionalPaymentMethodDisabled();
    }
});

Template.paymentSettingsEditModal.events({
    'input #autopayEnrollmentSelect'(event, template) {
        template.paymentSettingsService.validateAutopayEnrollmentChange(event.target.value === 'required');
    },

    'input #paymentMethodSelect'(event, template) {
        template.paymentSettingsService.validatePaymentMethodChange(event.target.value === 'required');
    },
});