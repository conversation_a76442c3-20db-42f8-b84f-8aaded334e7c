import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Invoices } from '../../../lib/collections/invoices';
import './invoiceDetail.html';
import { processPermissions } from '../../../lib/permissions';
import { Orgs } from '../../../lib/collections/orgs';
import { Relationships } from '../../../lib/collections/relationships';
import { showModal } from '../main';
import { BillingHelpers } from './_billingHelpers';
import { InvoiceUpdateService } from '../../../lib/invoiceUpdateService';
import '../simpleModal/simpleModal';
import { InvoiceDetailClientService } from '../../services/billing/InvoiceDetailClientService';
import { InvoiceUtils } from '../../../lib/util/invoiceUtils';

Template.invoiceDetail.onCreated(async function () {
	const invoiceId = FlowRouter.getParam("id");
	this.invoiceService = new ReactiveVar(null);
	this.currentOrg = Orgs.current();

	this.autorun(() => {
		this.subscribe("theInvoices", { invoiceId });
	});

	try {
		const service = await InvoiceDetailClientService.create(invoiceId, this.currentOrg);
		await service.initOpenBalances(this.currentOrg);
		this.invoiceService.set(service);

	} catch (e) {
		mpSwal.fire('Error', e.message || e.reason, 'error');
	}
});

Template.invoiceDetail.helpers({
	invoiceData() {
		return Template.instance().invoiceService.get()?.invoiceData() ?? {};
	},

	targetPerson() {
		return Template.instance().invoiceService.get()?.targetPerson();
	},

	showVoid() {
		return Template.instance().invoiceService.get()?.showVoid(this);
	},

	showManualRefund() {
		return Template.instance().invoiceService.get()?.showManualRefund(this);
	},

	showReverse() {
		return Template.instance().invoiceService.get()?.showReverse(this);
	},

	showModify() {
		const currentOrg = Template.instance().currentOrg;
		return Template.instance().invoiceService.get()?.showModify(currentOrg, this);
	},

	showAdjust() {
		return Template.instance().invoiceService.get()?.showAdjust(this);
	},

	showResendInvoice() {
		return Template.instance().invoiceService.get()?.showResendInvoice();
	},

	showReallocatePayers() {
		return Template.instance().invoiceService.get()?.showReallocatePayers(this);
	},

	showCreditInvoice() {
		return Template.instance().invoiceService.get()?.showCreditInvoice();
	},

	showEditInvoiceNotes() {
		return Template.instance().invoiceService.get()?.showEditInvoiceNotes();
	},

	showVoidInvoice() {
		return Template.instance().invoiceService.get()?.showVoidInvoice();
	},

	showIssueRefund() {
		return Template.instance().invoiceService.get()?.showIssueRefund();
	},

	showAddDiscount() {
		const currentOrg = Template.instance().currentOrg;
		return Template.instance().invoiceService.get()?.showAddDiscount(currentOrg);
	},

	showAddReimbursement() {
		const currentOrg = Template.instance().currentOrg;
		return Template.instance().invoiceService.get()?.showAddDiscount(currentOrg);
	},

	showModifyDiscount() {
		const currentOrg = Template.instance().currentOrg;
		return Template.instance().invoiceService.get()?.showModifyDiscount(currentOrg, this);
	},

	openBalances() {
		return Template.instance().invoiceService.get()?.getOpenBalances();
	},

	getInvoicedAmountMinusDiscounts() {
		return Template.instance().invoiceService.get()?.getInvoicedAmountMinusDiscounts(this);
	},
	getTotalAmount(lineItem){
		return InvoiceUtils.getPlanAmountTotal(lineItem);
	}
});

Template.invoiceDetail.events({
	"click .invoice-modify-discount"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.invoiceModifyDiscount(event);
	},

	"click .invoice-void-discount"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.invoiceVoidDiscount(event);
	},

	"click .invoice-add-discount"(event, instance) {
		event.preventDefault();
		const currentOrg = instance.currentOrg;
		instance.invoiceService.get()?.invoiceAddDiscount(event, currentOrg);
	},

	"click .invoice-reallocate-discount"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.invoiceReallocateDiscount(event);
	},

	"click .invoice-remove-lineitem-covered-day"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.invoiceRemoveReimbursableDay(event);
	},

	"click .invoice-add-lineitem-covered-day"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.invoiceAddReimbursableDay(event);
	},

	"click #resendInvoiceLink"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.resendInvoice();
	},

	async "click #creditInvoiceLink"(event, instance) {
		event.preventDefault();
		await instance.invoiceService.get()?.creditInvoice();
	},

	"click #voidInvoiceLink"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.voidInvoice();
	},

	"click #issueRefundLink"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.issueRefund();
	},

	"click .btnVoidLineItem"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.voidLineItem(event);
	},

	async "click .btnModifyLineItem"(event, instance) {
		event.preventDefault();
		await instance.invoiceService.get()?.modifyLineItem(event)
	},

	"click .btnManualRefundLineItem"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.manualRefundLineItem(event);
	},

	"click .btnReverseLineItem"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.reverseLineItem(this);
	},

	"click .btnAdjustLineItem"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.adjustLineItem(this);
	},

	"click #btnEditInvoiceNotes"(event, instance) {
		event.preventDefault();
		instance.invoiceService.get()?.editInvoiceNotes();
	}

});

Template.billingAddDiscountModal.helpers({
	"availableDiscountTypes": () => {
		return Orgs.current().availableDiscountTypes();
	},
	"availableReimbursementTypes": () => {
		return Orgs.current().availablePayerSources();
	}
});


