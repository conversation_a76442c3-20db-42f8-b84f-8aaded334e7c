import { Orgs } from '../../../lib/collections/orgs';
import { AvailableCustomizations } from '../../../lib/customizations';
import { processPermissions } from '../../../lib/permissions';
import './_billingAdminDiscountsAndPayers.html';

Template.billingAdminDiscountsAndPayers.helpers({
    showEditOptions() {
        return processPermissions({
            assertions: [{ context: "billing/configuration/system", action: "edit"}],
            evaluator: (person) => person.type=="admin"
        })
    },
    availableDiscounts() {
        return (Orgs.current() && Orgs.current().availableDiscountTypes(true));
    },
    availablePayers() {
        return (Orgs.current() && Orgs.current().availablePayerSources(true));
    },
});

Template.billingAdminDiscountsAndPayers.events({
    "click #btnShowDiscounts": () => {
        $("#discountsPanel").show();
        $("#btnShowDiscounts").hide();
    },
    "click #btnHideDiscounts": () => {
        $("#discountsPanel").hide();
        $("#btnShowDiscounts").show();
        $("#btnAddNewDiscount").show();
        $("#rowAddDiscount").hide();
    },
    "click #btnAddNewDiscount": () => {
        $("#btnAddNewDiscount").hide();
        $("#rowAddDiscount").show();
        $("#frmAddDiscount")[0].reset();
    },
    "click .btnEditDiscount": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id");
        $(".discountEditRow[data-id='" + discountType + "']").show();
        $(".discountDisplayRow[data-id='" + discountType + "']").hide();
    },
    "click #btnSaveDiscount": (e) => {
        e.preventDefault();
        Meteor.callAsync("insertDiscountType", {
            type: $("#txtDiscountType").val(),
            description: $("#txtDiscountDescription").val(),
            amount: parseFloat($("#txtDiscountAmount").val()),
            amountType: $("#selectDiscountAmountType").val(),
            expiresWithGracePeriod: $("#chkExpiresWithGracePeriod").prop("checked"),
            overrideSingleDiscount: $("#chkOverrideSingleDiscountRule").prop("checked"),
            ledgerAccountName: $("#txtDiscountLedgerAccountName").val()
        })
        .then((result)=>{
            $("#btnAddNewDiscount").show();
            $("#rowAddDiscount").hide();
        })
        .catch((error)=>{
            mpSwal.fire(error.reason);
        });
    },
    "click .btnSaveEditDiscount": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id");
        Meteor.callAsync("updateDiscountType", {
            type: discountType,
            description: $(".frmDiscountEdit[data-id='" + discountType + "'] .txtDiscountDescription").val(),
            amount: parseFloat($(".frmDiscountEdit[data-id='" + discountType + "'] .txtDiscountAmount").val()),
            amountType: $(".frmDiscountEdit[data-id='" + discountType + "'] .selectDiscountAmountType").val(),
            expiresWithGracePeriod: $(".frmDiscountEdit[data-id='" + discountType + "'] .chkExpiresWithGracePeriod").prop("checked"),
            overrideSingleDiscount: $(".frmDiscountEdit[data-id='" + discountType + "'] .chkOverrideSingleDiscountRule").prop("checked"),
            ledgerAccountName: $(".frmDiscountEdit[data-id='" + discountType + "'] .txtDiscountLedgerAccountName").val()
        })
        .then((result)=>{
            $(".discountEditRow[data-id='" + discountType + "']").hide();
            $(".discountDisplayRow[data-id='" + discountType + "']").show();
        })
        .catch((error)=>{
            mpSwal.fire(error.reason);
        });
    },
    "click .btnCancelEditDiscount": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id");
        $(".discountEditRow[data-id='" + discountType + "']").hide();
        $(".discountDisplayRow[data-id='" + discountType + "']").show();
    },
    "click .btnRemoveDiscount": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id");
        mpSwal.fire({
            title: "Are you sure?",
            text: "Removing the requested discount type will cause it to no longer appear in the app or in reports. This cannot be undone.",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, remove type"
        }).then(async (result) => {
            if (result.value) {
                await Meteor.callAsync("removeDiscountType", discountType);
            }
        });
    },
    "click .btnArchiveDiscount": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id"),
            currentType = _.find(Orgs.current().availableDiscountTypes(true), ct => ct.type === discountType),
            messageText = currentType.archived ? "This discount will appear as a selection in billing."
                : "Archiving the requested discount type will cause it to no longer appear as a selection in billing.",
            confirmButtonText = currentType.archived ? "Yes, unarchive discount" : "Yes, archive discount";

        mpSwal.fire({
            title: "Are you sure?",
            text: messageText,
            type: "warning",
            showCancelButton: true,
            confirmButtonText
        }).then(async (result) => {
            if (result.value) {
                await Meteor.callAsync("archiveDiscountType", discountType);
            }
        });
    },
    "click #btnShowPayers": () => {
        $("#payersPanel").show();
        $("#btnShowPayers").hide();
    },
    "click #btnHidePayers": () => {
        $("#payersPanel").hide();
        $("#btnShowPayers").show();
        $("#btnAddNewPayer").show();
        $("#rowAddPayer").hide();
    },
    "click #btnAddNewPayer": () => {
        $("#btnAddNewPayer").hide();
        $("#rowAddPayer").show();
        $("#frmAddPayer")[0].reset();
    },
    "click .btnEditPayer": (e) => {
        e.preventDefault();
        const payerType = $(e.currentTarget).data("id");
        $(".payerEditRow[data-id='" + payerType + "']").show();
        $(".payerDisplayRow[data-id='" + payerType + "']").hide();
    },
    "click .btnCancelEditPayer": (e) => {
        e.preventDefault();
        const payerType = $(e.currentTarget).data("id");
        $(".payerEditRow[data-id='" + payerType + "']").hide();
        $(".payerDisplayRow[data-id='" + payerType + "']").show();
    },
    "click .btnSaveEditPayer": async (e) => {
        e.preventDefault();
        const payerType = $(e.currentTarget).data("id");
        const options = {
            type: payerType,
            description: $(".frmPayerEdit[data-id='" + payerType + "'] .txtPayerDescription").val(),
            ledgerAccountName: $(".frmPayerEdit[data-id='" + payerType + "'] .txtPayerLedgerAccountName").val()
        };
        if (Orgs.current().hasCustomization(AvailableCustomizations.PAYER_CASH_LEDGER)) {
            options.cashLedgerAccountName = $(".frmPayerEdit[data-id='" + payerType + "'] .txtCashLedgerAccountName").val();
        }
        await Meteor.callAsync("updatePayerType", options)
        .then((result)=>{
            $(".payerEditRow[data-id='" + payerType + "']").hide();
            $(".payerDisplayRow[data-id='" + payerType + "']").show();
        })
        .catch( (error)=>{
            mpSwal.fire(error.reason);
        });
    },
    "click #btnSavePayer": (e) => {
        e.preventDefault();
        const payerType = $("#txtPayerType").val();
        if (!isAlphaNumeric(payerType)) {
            return mpSwal.fire("Error", "Payer types can only contain letters, numbers, and spaces");
        }
        const options = {
            type: payerType,
            description: $("#txtPayerDescription").val(),
            ledgerAccountName: $("#txtPayerLedgerAccountName").val()
        };
        if (Orgs.current().hasCustomization(AvailableCustomizations.PAYER_CASH_LEDGER)) {
            options.cashLedgerAccountName = $("#txtCashLedgerAccountName").val();
        }
        Meteor.callAsync("insertPayerType", options)
        .then((result)=>{
            $("#btnAddNewPayer").show();
            $("#rowAddPayer").hide();
        })
        .catch(()=>{
            mpSwal.fire(error.reason);
        });
    },
    "click .btnRemovePayer": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id");
        mpSwal.fire({
            title: "Are you sure?",
            text: "Removing the requested payer type will cause it to no longer appear in the app or in reports. This cannot be undone.",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, remove type"
        }).then(async (result) => {
            if (result.value) {
                await Meteor.callAsync("removePayerType", discountType);
            }
        });
    },
    "click .btnArchivePayer": (e) => {
        e.preventDefault();
        const discountType = $(e.currentTarget).data("id").toString();
        const currentType = _.find(Orgs.current().availablePayerSources(true), ct => ct.type === discountType);
        const confirmButtonText = currentType.archived ? "Yes, unarchive type" : "Yes, archive type";
        const messageText = currentType.archived ? "This payer will appear as a selection in billing."
                : "Archiving the requested payer type will cause it to no longer appear as a selection in billing.";

        mpSwal.fire({
            title: "Are you sure?",
            text: messageText,
            type: "warning",
            showCancelButton: true,
            confirmButtonText
        }).then(async (result) => {
            if (result.value) {
                await Meteor.callAsync("archivePayerType", discountType);
            }
        });
    },
});