<template name="billingCredit">
	<form id="frmCredit">
        <div class="row">
            <div class="col-sm-12">
                <label>Invoice Number: </label>
                {{invoice.invoiceNumber}}<br/>
                <label>Original Amount: </label>
                {{formatCurrency invoice.originalAmount}}<br/>
                <label>Open Amount: </label>
                {{formatCurrency invoice.openAmount}}<br/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 form-group">
                <label>Credit Amount</label>
                <input data-cy="credit-amount-input" type="text" class="form-control" name="credit_amount" required pattern="[0-9]+([\.][0-9]+)?"
                       value="{{formatNumber existingCredit.amount '0.00'}}"/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 form-group">
                <label>Credit Reason</label>
                <select data-cy="credit-reason-select" class="form-control" name="credit_reason" required>
                    {{#each creditReasons}}
                        <option value="{{reasonId}}" selected={{trueIfEq reasonId
                                                                         ../existingCredit.creditReason }}>{{reasonDescription}}</option>
                    {{/each}}
                </select>
            </div>
        </div>
        {{#if showLineItem}}

            <div class="row">
                <div class="col-sm-12 form-group">
                    <label>Line Item</label>
                    <select data-cy="credit-line-item-select" class="form-control" name="line_item" required>
                        <option value=""></option>
                        {{#each invoice.lineItems}}
                            <option value="{{@index}}" selected={{trueIfEq @index
                                                                           ../existingCredit.creditLineItemIndex }}>{{lineItemDescription
                                    @index}}</option>
                        {{/each}}
                    </select>
                </div>
            </div>
        {{/if}}

        {{#if showCheckNumber}}
            <div class="row">
                <div class="col-sm-12 form-group">
                    <label>Check Number</label>
                    <input data-cy="check-number-input" type="number" class="form-control" name="check_number" required
                           value="{{existingCredit.checkNumber}}"/>
                </div>
            </div>
        {{/if}}
        <div class="row">
            <div class="col-sm-12 form-group">
                <label>Note</label>
                <input data-cy="credit-note-input" type="text" class="form-control" name="credit_note" value="{{existingCredit.creditNote}}"/>
            </div>
        </div>
    </form>
</template>