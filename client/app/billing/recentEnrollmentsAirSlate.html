<template name="recentEnrollmentsAirSlate">
	<div class="container">
	  <div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
		  <div class="card-title">
			<h3 class="card-label">Registration Status
			  <span class="text-muted pt-2 font-size-sm d-block">Find and manage recent registrations.</span>
			</h3>
		  </div>
		  <div class="card-toolbar">
			<!--<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
						  <i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					  </div>-->
			<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
			  Update
			</div>
		  </div>
		</div>
		<div class="card-body">
		  <div class="box box-solid content-box descriptive-content">
			<div class="box-body">
  
			  <div class="row">
				<div class="col-md-3">
				  <div class="form-group">
					<label>Start Date</label>
					<div class="input-group">
  
					  <input type="text" class="form-control pull-right" id="scheduleStartDate"
						value="{{formattedStartDate}}">
					</div>
				  </div>
				</div>
				<div class="col-md-3">
				  <div class="form-group">
					<label>End Date</label>
					<div class="input-group">
  
					  <input type="text" class="form-control pull-right" id="scheduleEndDate"
						value="{{formattedEndDate}}">
					</div>
				  </div>
				</div>
				<div class="col-md-3">
					<div class="form-group">
						<label>Org(s):</label><br/>
						{{> reportOrgsField opts=orgsFieldOpts}}
					</div>
				</div>
				<div class="col-md-3">
				  <label>Options:</label><br/>
				  <div class="col-sm-12 form-group">
					<div class="checkbox-list">
					  <label class="checkbox checkbox-primary"><input type="checkbox" id="chkShowResolved"><span></span>Show Resolved Changes</label>
					  <label class="checkbox checkbox-primary"><input type="checkbox" id="chkShowUnpaid"><span></span>Show Unpaid Only</label>
					</div>
				  </div>	
				</div>
			  </div>
			  <br />
			  <table class="table table-striped billing-table">
				<tr>
				  <th>Person</th>
				  <th>Org</th>
				  <th>Date Created</th>
				  <th>Date Added to Waitlist</th>
				  <th>Action</th>
				</tr>
				{{#each r in registrations}}
				  <tr>
					<td><a href="/people/{{r._id}}" data-org-id="{{r.orgId}}" class="go-to-registrant">{{r.firstName}} {{r.lastName}}</a></td>
					<td>{{r.orgName}}</td>
					<td>{{formatDate r.createdAt "MM/DD/YYYY"}}</td>
					<td>{{formatDate r.waitlistAddedDate "MM/DD/YYYY"}}</td>
					<td>
					{{#unless isShowResolvedActive}}
					  
						<div class="dropdown">
						  <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" id="dropdownMenuButton"
							data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							Action
						  </button>
						  <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
							<a class="dropdown-item btnMarkResolved" data-id="{{r._id}}" href="#">Mark resolved</a>
						  </div>
						</div>
					  
					{{/unless}}
					</td>
				  </tr>
				{{/each}}
			  </table>
			</div>
		  </div>
		</div>
	  </div>
	</div>
  </template>