<template name="billingAdyenBalancePlatformOnboarding">
    <div class="container-fluid" id="dashboard">
        <div class="row">
            <div class="col-12 font-weight-mormal font-size-lg">
                {{#if balancePlatformOnboardingState}}
                    {{#if (trueIfEq balancePlatformOnboardingState.status "not-started")}}
                        {{#if balancePlatformOnboardingState.isAdyenClassicPlatformAccount}}
                            <h2>Upgrade to the New Payment Processing Platform</h2>
                            <hr noshade/>
                            We're excited to offer you the opportunity to migrate to our new payment processing platform. This will enable you to start processing payments seamlessly. Please enter necessary data and click "Submit" to begin the transition.
                            <br/>
                            <br/>
                        {{else}}
                            <h2>Getting Started with Payment Processing</h2>
                            <hr noshade/>
                            Welcome to MomentPath billing and payments. As part of federal Know Your Customer rules and laws, we are required to collect certain items of
                            information from you before we can begin processing payments. This information is processed through a trusted third-party for verification
                            and setup of your merchant account. At any time, please contact MomentPath if you have any questions or concerns about this process.<br/>
                            <br/>
                        {{/if}}

                        <div class="row">
                            <div class="col-8 offset-2">
                                <form id="frmAdyenBalancePlatformOnboarding">
                                    <h4>Step One: Basic Information</h4>
                                    <br/>
                                    {{#unless balancePlatformOnboardingState.isAdyenClassicPlatformAccount}}
                                        <label>Company Legal Name:</label>
                                        <input type="text" name="company-legal-name" class="form-control">
                                        <br/>
                                    {{/unless}}

                                    <label>Company Phone:</label>
                                    {{#if balancePlatformOnboardingState.initialFormData.phone}}
                                        <div>{{balancePlatformOnboardingState.initialFormData.phone}}</div>
                                    {{else}}
                                        <input type="text" name="company-phone-number" class="form-control" id="phoneNumber">
                                    {{/if}}
                                    <br/>

                                    <label>Company Website:</label>
                                    {{#if balancePlatformOnboardingState.initialFormData.webAddress}}
                                        <div>{{balancePlatformOnboardingState.initialFormData.webAddress}}</div>
                                    {{else}}
                                        <input type="text" name="company-web-address" class="form-control">
                                    {{/if}}
                                    <br/>

                                    {{#unless balancePlatformOnboardingState.isAdyenClassicPlatformAccount}}
                                    <b>Company Address</b><br/>
                                    <label>Address Line 1:</label>
                                    <input type="text" name="company-address-line-1" class="form-control">
                                    <br/>

                                    <label>Address Line 2:</label>
                                    <input type="text" name="company-address-line-2" class="form-control">
                                    <br/>

                                    <label>City:</label>
                                    <input type="text" name="company-city" class="form-control">
                                    <br/>

                                    <label>State:</label>
                                    <select name="company-state" class="form-control">
                                        <option value=""></option>
                                        <option value="AL">Alabama</option>
                                        <option value="AK">Alaska</option>
                                        <option value="AZ">Arizona</option>
                                        <option value="AR">Arkansas</option>
                                        <option value="CA">California</option>
                                        <option value="CO">Colorado</option>
                                        <option value="CT">Connecticut</option>
                                        <option value="DE">Delaware</option>
                                        <option value="FL">Florida</option>
                                        <option value="GA">Georgia</option>
                                        <option value="HI">Hawaii</option>
                                        <option value="ID">Idaho</option>
                                        <option value="IL">Illinois</option>
                                        <option value="IN">Indiana</option>
                                        <option value="IA">Iowa</option>
                                        <option value="KS">Kansas</option>
                                        <option value="KY">Kentucky</option>
                                        <option value="LA">Louisiana</option>
                                        <option value="ME">Maine</option>
                                        <option value="MD">Maryland</option>
                                        <option value="MA">Massachusetts</option>
                                        <option value="MI">Michigan</option>
                                        <option value="MN">Minnesota</option>
                                        <option value="MS">Mississippi</option>
                                        <option value="MO">Missouri</option>
                                        <option value="MT">Montana</option>
                                        <option value="NE">Nebraska</option>
                                        <option value="NV">Nevada</option>
                                        <option value="NH">New Hampshire</option>
                                        <option value="NJ">New Jersey</option>
                                        <option value="NM">New Mexico</option>
                                        <option value="NY">New York</option>
                                        <option value="NC">North Carolina</option>
                                        <option value="ND">North Dakota</option>
                                        <option value="OH">Ohio</option>
                                        <option value="OK">Oklahoma</option>
                                        <option value="OR">Oregon</option>
                                        <option value="PA">Pennsylvania</option>
                                        <option value="RI">Rhode Island</option>
                                        <option value="SC">South Carolina</option>
                                        <option value="SD">South Dakota</option>
                                        <option value="TN">Tennessee</option>
                                        <option value="TX">Texas</option>
                                        <option value="UT">Utah</option>
                                        <option value="VT">Vermont</option>
                                        <option value="VA">Virginia</option>
                                        <option value="WA">Washington</option>
                                        <option value="WV">West Virginia</option>
                                        <option value="WI">Wisconsin</option>
                                        <option value="WY">Wyoming</option>
                                    </select>
                                    <br/>

                                    <label>Postal Code:</label>
                                    <input type="text" name="company-postal-code" class="form-control">
                                    <br/>
                                    {{/unless}}

                                    By clicking "submit", I agree that this information above is correct. I also agree that I am the owner or an individual authorized
                                    to sign on behalf of the business named above.<br/>
                                    <br/>
                                    <button type="submit" name="Submit" class="btn btn-primary btn-block font-size-h5 mb-2 font-weight-bolder">
                                        Submit
                                    </button>
                                </form>
                            </div>
                        </div>
                    {{else}}
                        {{> billingAdyenBalancePlatformOnboardingStatus balancePlatformOnboardingState=balancePlatformOnboardingState}}
                    {{/if}}
                {{else}}
                    {{>loading}}
                {{/if}}
            </div>
        </div>
    </div>
</template>

<template name="billingAdyenBalancePlatformOnboardingStatus">
    <h2>Payment Processing Setup and Management</h2>
    <hr noshade/>
    {{#if (trueIfEq balancePlatformOnboardingState.capabilitiesStatus "pending")}}
        Your payment processing onboarding is pending verification. We are currently verifying your information, which may take some time.
        Please check back later, or contact MomentPath support if you have any questions.<br/>
    {{else if (trueIfEq balancePlatformOnboardingState.capabilitiesStatus "invalid")}}
        There was an issue with the verification of your onboarding information. Please review the details and correct any errors to proceed with payment processing.<br/>
    {{else if (trueIfEq balancePlatformOnboardingState.capabilitiesStatus "valid")}}
        Your onboarding process is complete and has been successfully verified. You are now ready to start processing payments with MomentPath.<br/>
    {{/if}}
    <br/>
    <b>Account Status:</b>
    {{#if (trueIfEq balancePlatformOnboardingState.status "active")}}
        <i class="fad fa-check-circle text-success"></i>
    {{else}}
        <i class="fad fa-exclamation-triangle text-danger"></i>
    {{/if}}
    {{balancePlatformOnboardingState.statusDescription}}<br/>

    <b>Processing Status:</b>
    {{#if (trueIfEq balancePlatformOnboardingState.capabilitiesStatus "pending")}}
        <i class="fad fa-spinner-third fa-spin text-bright-blue"></i>
    {{else if (trueIfEq balancePlatformOnboardingState.capabilitiesStatus "invalid")}}
        <i class="fad fa-exclamation-triangle text-danger"></i>
    {{else if (trueIfEq balancePlatformOnboardingState.capabilitiesStatus "valid")}}
        <i class="fad fa-check-circle text-success"></i>
    {{/if}}
    {{balancePlatformOnboardingState.capabilitiesStatusDescription}}<br/>

    {{#if (trueIfEq balancePlatformOnboardingState.status "active")}}
        <br/>
        You are able to edit your onboarding details at any time by clicking the button below. If you have any questions or concerns, please contact MomentPath support.<br/>
        <br/>
        <a href="#" id="btnOpenBalancePlatformOnboarding" class="btn btn-lg btn-primary font-weight-boldest mt-1">Visit Onboarding Portal</a>
    {{/if}}
</template>
