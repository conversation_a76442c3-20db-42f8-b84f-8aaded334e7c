import moment from "moment-timezone";
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AvailableCustomizations } from "../../../lib/customizations";
import { InvoiceUtils } from "../../../lib/util/invoiceUtils";
import { Log } from "../../../lib/util/log";
import './_billingPayment.html';
import { Orgs } from "../../../lib/collections/orgs";

Template.billingConfigureAutoPay.onCreated(function() {
	this.accountType = new ReactiveVar('');
	this.platformFeesData = new ReactiveVar({ peopleRelationships: [], hasRecurringCharges: false });

	this.updatePlatformFees = () => {
		const instance = Template.instance();
		const accountType = instance.accountType.get() || $('select[name="account_type"]').val();

		if (!accountType || accountType === '') {
			instance.platformFeesData.set({ peopleRelationships: [], hasRecurringCharges: false });
			return;
		}

		Meteor.callAsync('allOpenInvoicesForParent', FlowRouter.getParam('_id'))
		.then((result) => {
			const resultsWithMerchantFee = addMerchantFeeToResult(accountType, result, Orgs.current().billing.paymentFees);
			const uniqueResultsWithMerchantFee = removeDuplicates(resultsWithMerchantFee);
			instance.platformFeesData.set({ peopleRelationships: uniqueResultsWithMerchantFee, hasRecurringCharges: uniqueResultsWithMerchantFee.length > 0 });
		})
		.catch((error) => {
			Log.error('Error fetching platform fees:', error);
		});
	};
});

Template.billingConfigureAutoPay.onRendered(function() {
	this.accountType.set($('select[name="account_type"]').val());
	this.updatePlatformFees();
});

Template.billingConfigureAutoPay.events({
	"click input[name='specific_amount']": function() {
		$("input[name=payment_amount_type][value='specific']").prop('checked', true);
	},
	'change select[name="account_type"]'(event, instance) {
		const accountType = $(event.currentTarget).val();
		instance.accountType.set(accountType);
		instance.updatePlatformFees();
	}
});

Template.billingConfigureAutoPay.helpers({
	"showPlatformFees": () => {
		return Orgs.current().hasCustomization(AvailableCustomizations.BILLING_PLATFORM_FEES);
	},
	"getPlatformFees": () => {
		return Template.instance().platformFeesData.get().peopleRelationships;
	}
});

Template.billingPaymentCreditCard.helpers({
	"expireYears": () => {
		const curYear = new moment().year();
		return _(15).times( (x) => { return x + curYear;})
	},
	"passedInfo": () => { 
		return Template.instance().data;
	}
});

const addMerchantFeeToResult = (accountType, result, orgPaymentFees) => {
	return result.map(invoice => {
		const merchantFee = InvoiceUtils.calculateMerchantFee(
			accountType,
			{ calculatedAmount: invoice.openAmount },
			orgPaymentFees
		).toFixed(2);

		return { ...invoice, merchantFee };
	})
}

const removeDuplicates = (result) => {
	return result.filter((invoice, index, self) =>
			index === self.findIndex((t) =>
				t.merchantFee === invoice.merchantFee &&
				t.openAmount === invoice.openAmount &&
				t.personName === invoice.personName
			)
	);
}