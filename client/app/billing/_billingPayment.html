
<template name="billingPaymentCreditCard">
	<form id="frmPayment">
		<div class="row">
			<div class="col-sm-12 form-group">
				{{#if providerAdyen}}
					<div id="customCard-container">
						<label>
							<span>Card number:</span>
							<span class="customCard-field" data-cse="encryptedCardNumber"></span>
						</label>
						<label>
							<span>Expiration date:</span>
							<span class="customCard-field"  data-cse="encryptedExpiryDate"></span>
						</label>
						<label>
							<span>CVV/CVC:</span>
							<span class="customCard-field"  data-cse="encryptedSecurityCode"></span>
						</label>
					</div>
				{{else}}
					<label for="card-element">Credit or debit card</label>
					<div id="card-element">

					</div>
					<div id="card-errors" role="alert"></div>

				{{/if}}
			
			</div>
		</div>
		
	</form>
</template>

<template name="billingPaymentBankAccount">
	<form id="frmPayment">
		{{#if providerAdyen}}
		<b>Note:</b> Only US-based checking accounts are supported at this time. All other accounts (including savings) will result in a returned payment.<br/>
		<br/>
		<div id="ach-component-container"></div>
		<div class="checkbox-list">
			<label class="checkbox checkbox-primary">
			  <input type="checkbox" class="form-check-input" name="is-checking" >
			  <span></span>
			  I certify that the above account is a US-based checking account and understand that all other account types will result in a returned payment.
			</label>
		</div>
		{{else}}
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Routing Number</label>
				<input type="text" class="form-control" name="routing_number" required />
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Account Number</label>
				<input type="text" class="form-control" name="account_number" required />
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Verify Account Number</label>
				<input type="text" class="form-control" name="account_number_verify" required />
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Account Holder Name</label>
				<input type="text" class="form-control" name="account_holder_name" required />
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Account Type</label>
				<select class="form-control" name="account_holder_type" required>
					<option value="individual">Individual</option>
					<option value="company">Company</option>
				</select>
			</div>
		</div>
		{{/if}}
	</form>
</template>

<template name="billingPaymentVerifyBankAccount">
	<form id="frmPayment">
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Deposit Amount 1 (in cents)</label>
				<input type="text" class="form-control" name="deposit_amount_1" required maxlength="2" pattern="\d{1,2}"/>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Deposit Amount 2 (in cents)</label>
				<input type="text" class="form-control" name="deposit_amount_2" required maxlength="2" pattern="\d{1,2}"/>
			</div>
		</div>
	</form>
</template>

<template name="billingPaymentPayNow">
	<form id="frmPayment">
		<div class="row">
			<div class="col-sm-12">
				<label>Amount Due:</label>
				{{formatCurrency paymentAmount}}<br/>
				<label>Invoice Number:</label>
				{{invoiceNumber}}<br/>
			</div>
		</div>
		<br/>

		<div class="row">
			<div class="col-sm-12">
				<label>Payment Source</label>
				<br/>
				{{#if trueIfEq paymentSource "cashnet"}}
					Your transaction will be processed through Cashnet after you confirm the payment amount.
				{{else}}
					Choose account type this payment (bank accounts must be verified before making payment):<br/>
					<select name="account_type" size=1 required class="form-control">
						<option value="bank_account" disabled={{disableBankAccount}}>Bank Account</option>
						<option value="card" disabled={{disableCreditCard}}>Credit Card</option>
					</select>
				{{/if}}
			</div>
		</div>
		<br/>
		
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Payment Amount</label>
				<input type="text" class="form-control" name="payment_amount" required pattern="^\d*(\.\d{2}$)?">
			</div>
		</div>
		{{#if showServiceChargeNotice}}
		<div class="row">
			<div class="col-sm-12">
				{{> feeVerbiage paymentAmount=paymentAmount currentOrg=currentOrg}}
			</div>
		</div>
		{{/if}}
		<div class="row">
			<div class="col-sm-12">
				{{#if trueIfEq paymentSource "cashnet"}}
					You will be redirected to the Cashnet checkout site to complete payment once you approve the amount.<br/>
				{{else}}
					I understand that when I click the "Pay Now" link that I will immediately be charged for the full amount of
					the invoice as listed above.<br/>
				{{/if}}
			</div>
		</div>
	</form>
</template>

<template name="billingConfigureAutoPay">
	<form id="frmPayment">
		<div class="row">
			<div class="col-sm-12">
				Choose account type for enabling automatic payment (bank accounts must be verified before enabling automatic payment):<br/>
				<br/>
				<select name="account_type" size=1 required class="form-control">
					<option value="bank_account" disabled={{disableBankAccount}}>Bank Account</option>
					<option value="card" disabled={{disableCreditCard}}>Credit Card</option>
				</select>
				<br/>
				<label>Payment Amount:</label><br/>
				<input type="radio" name="payment_amount_type" value="full" checked> <span class="ml-0.5">Full Amount Due </span> <br/>
				<div class="form-inline">
					<input type="radio" name="payment_amount_type" value="specific"> <span class="ml-1">Specific Amount:</span>
					<span class="ml-2">
						<input type="text" class="form-control" name="specific_amount" pattern="^\d*(\.\d{2}$)?">
					</span>
				</div>

				<p class="mb-4 mt-4">
					I understand that by enabling the selected account for automatic payment that I will be automatically charged
					by each invoice's due date for the full amount due.
				</p>
			</div>
		</div>
		{{#if showServiceChargeNotice}}
		<div class="row">
			<div class="col-sm-12">
				{{#if showPlatformFees}}
					<b>Platform fees: </b> To cover costs associated with live customer support, communications related to billing, invoice generation and record-keeping, reporting, etc.
					{{#if getPlatformFees}}
						a platform fee will be added to the invoice total as follows:
						<ul class="mt-1">
							{{#each getPlatformFees}}
								<li>${{merchantFee}} will be added to every ${{openAmount}} invoice for {{personName}}</li>
							{{/each}}
						</ul>
					{{else}}
						a platform fee will be added to the invoice.
					{{/if}}
				{{else}}
					{{> feeVerbiage currentOrg=currentOrg}}
				{{/if}}
			</div>
		</div>
		{{/if}}
	</form>
</template>
