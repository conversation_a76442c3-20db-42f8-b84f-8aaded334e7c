import { FlowRouter } from "meteor/ostrio:flow-router-extra";
import {Log} from "../../../../lib/util/log";
import './billingReportSummary.html';
import '../../reports/reportOrgsField';
import '../../reports/reportQueueTemplate';
import { reportQueuedSwal } from "../../reports/queueReportsUtil";

function formatItemGroupKey(key) {
	const words = key.split(' ');
	return words.map((word) => {
		return word[0].toUpperCase() + word.substring(1);
	}).join(' ');
}

function getClassificationsList() {
	return [
		'Plan Charges',
		'Item Charges',
		'Credits',
		'Discounts',
		'Item Discounts',
		'Voided Plan Charges',
		'Voided Item Charges',
		'Voided Credits',
		'Voided Discounts',
		'Voided Item Discounts',
		'Payments',
		'Credit Memos',
		'Refunded',
		'Voided Payments',
		'Settlements',
		'Settlement Fees',
		'Manual Deposits',
		'Security Deposits Applied',
		'Security Deposits Refunded',
		'Credit Memos Refunded',
		'Credit Memos Voided',
		'Allocation Changes',
		'Refunded Manual Payments',
		'Chargeback Fees'
	];
}

Template.billingReportSummaryReport.onCreated( function () {
	this.classifications = ReactiveVar([]);
	this.results = ReactiveVar();
	this.orgIds = ReactiveVar([]);
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	this.isUpdating = new ReactiveVar(false);
	this.orgNames = new ReactiveVar('');
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId })
		.then((response) => {
			this.savedMode.set(true);
			const reportArgs = response.args,
				startDate = reportArgs.startDate,
				endDate = reportArgs.endDate,
				includeLinkedDetails = reportArgs.includeLinkedDetails ? 'Yes' : 'No';
			this.orgIds.set(reportArgs.orgIds);
			this.classifications.set(reportArgs.classifications);
			this.results.set(response.data);
			let header = 'Date: ' + startDate + '<br>';
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgIds })
			.then((resp)=>{
				header += 'Orgs: ' + resp + '<br>';
			})
			.catch((e)=>{
				header += 'Orgs: ' + undefined + '<br>';
			})
			.finally(()=>{
				header += 'Classification(s): ' + (reportArgs.classifications.length ? reportArgs.classifications.join(', ') : 'All') + '<br>';
				header += 'Filter by date start: ' + startDate + '<br>';
				header += 'Filter by date end: ' + endDate + '<br>';
				header += 'Include linked details: ' + includeLinkedDetails + '<br>';
				if (reportArgs.periodStartDate || reportArgs.periodStartDateEnd) {
					header += 'Filter by period start date begining: ' + reportArgs.periodStartDate + '<br>';
					header += 'Filter by period start date end: ' + reportArgs.periodStartDateEnd + '<br>';
				}
				this.savedHeader.set(header);
			});
		});
	}
});

Template.billingReportSummaryReport.onRendered( function () {
	const self = this;
	$(".input-group.date").datepicker({
		zIndexOffset:9999
	});

	const onChange = function () {
		self.classifications.set($('#summaryClassifications').val());
	}
	$("#summaryClassifications").multiselect({
		includeSelectAllOption: true,
		enableFiltering: true,
		nonSelectedText: 'All',
		numberDisplayed: 1,
		onChange: onChange,
		onSelectAll: onChange,
		onDeselectAll: onChange
	});
});
Template.billingReportSummaryReport.events({
	"click #btnUpdate": async function(e,i) {
		const options = {};
		var self = Template.instance();
		options.startDate = $("input[name=start_date]").val();
		options.endDate = $("input[name=end_date]").val();
		options.familyId = $("select[name=familyId]").val();
		options.includeLinkedDetails = $("input[name=include_transactions]").prop("checked");
		options.orgIds = $('#reportOrgs').val();
		if ($("input[name=filter_period_start]").prop("checked")) {
			options.periodStartDate = $("input[name=period_start_date]").val();
			options.periodStartDateEnd = $("input[name=period_start_date_end]").val();
		}
		options.classifications = $('#summaryClassifications').val();
		if (i.queueMode.get()) {
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'billingAdminSummaryReport',
				reportName: 'Summary',
				reportArgs: options,
				userId: Meteor.userId(),
				reportRoute: FlowRouter.current().path.slice(1)
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "billingSummary"
			});
			reportQueuedSwal();
			return;
		}
		i.isUpdating.set(true);
		$("#btnUpdate").prop('disabled', true);
		Meteor.callAsync("billingAdminSummaryReport", options)
		.then((result) => {
			i.isUpdating.set(false);
			$('#btnUpdate').prop('disabled', false);
			self.results.set(result);
		})
		.catch((error) => {
			i.isUpdating.set(false);
			$('#btnUpdate').prop('disabled', false);
			mpSwal.fire("Error", error.reason, "error");
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "billingSummary"
		});
		Meteor.callAsync('retrieveOrgNames', { orgs: options.orgIds })
		.then((result)=>{
			self.orgNames.set(result);
		})
		.catch((error)=>{
			Log.error("Error fetching org names at summary report:", error);
		});
	},
	"click #btnExport": async function() {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvData'), outputFile]);
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "billingSummary"
		});
	},
	"click .view-detail": function(e, i) {
		console.log("This", this);
		const targetType = $(e.target).data("target-type"),
		target = $(e.target).data("target");
		if (targetType == "invoice")
			window.open("/billing/invoices/" + target, "_blank");
		else if (targetType == "person")
			window.open("/people/" + target + "#billing", "_blank");
	},
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	}
});

Template.billingReportSummaryReport.helpers({
	defaultDates(option) {
		return option=="start" ? new moment().subtract(1, "month").format("MM/DD/YYYY") : new moment().format("MM/DD/YYYY");
	},
	itemGroups() {
		const results = Template.instance().results.get();
		console.log("Results:", results);
		const displayedClassifications = Template.instance().classifications.get();
		if (!results) {
			return;
		}
		const filteredResults = Object.keys(results)
			.filter(classification => !displayedClassifications.length || displayedClassifications.includes(classification))
			.reduce((obj, key) => {
				obj[key] = results[key];
				return obj;
			}, {});
		return _.map(filteredResults, (obj, key) => {
			obj.groupLabel = key;
			return obj;
		});
	},
	parsedItemGroup (itemGroup) {
		const currentClassifications = Template.instance().classifications.get(),
			isExpanded = currentClassifications.length > 0 && currentClassifications.length < getClassificationsList().length
		return _.map(itemGroup, (obj, key) => {
			obj.label = Template.instance().orgIds.get().length > 1 ? formatItemGroupKey(key) : key;
			obj.textMoreClass = isExpanded ? 'd-none' : '';
			obj.textLessClass = isExpanded ? '' : 'd-none';
			return obj;
		});
	},
	parsedOrgDetails (itemGroup) {
		const currentClassifications = Template.instance().classifications.get(),
			state = (currentClassifications.length > 0 && currentClassifications.length < getClassificationsList().length) ? 'open' : 'closed';
		return _.map(itemGroup, (obj, key) => {
			obj.state = state;
			obj.caretDownClass = state === 'open' ? '' : 'd-none';
			obj.caretRightClass = state === 'open' ? 'd-none' : '';
			obj.hidden = state === 'open' ? '' : 'hidden';
			return obj;
		});
	},
	orgNames: function() {
		return Template.instance().orgNames.get();
	},
	orgsFieldOpts () {
		const self = Template.instance();
		return {
			onChange() {
				const orgIds = $('#reportOrgs').val();
				self.orgIds.set(orgIds);
				const checkbox = $('[name="include_transactions"]').eq(0);
				if (orgIds && orgIds.length > 1) {
					checkbox.prop('checked', false);
					checkbox.prop('disabled', true);
				} else {
					checkbox.prop('disabled', false);
				}
			},
			numberDisplayed: 1
		}
	},
	classifications () {
		return getClassificationsList();
	},
	itemRollup (groupLabel, itemLabel) {
		if (Template.instance().orgIds.get().length < 2) {
			return {};
		}
		const currentClassifications = Template.instance().classifications.get(),
			state = (currentClassifications.length > 0 && currentClassifications.length < getClassificationsList().length) ? 'open' : 'closed';
		return {
			'data-state': state,
			'data-display-id': `${groupLabel}-${itemLabel}`,
		}
	},
	itemRollupHasChildren (quantity) {
		return Template.instance().orgIds.get().length > 1 && quantity > 0;
	},
	orgRollupIndent (depth) {
		return 'rollup-indent-' + ((depth ?? 0) + 2);
	},
	orgDisplayId (displayId, groupLabel, itemLabel) {
		return `${groupLabel}-${itemLabel}` + (displayId ? `-${displayId}` : '');
	},
	orgDisplayParentId (displayParentId, groupLabel, itemLabel) {
		return `${groupLabel}-${itemLabel}` + (displayParentId ? `-${displayParentId}` : '');
	},
	updateLabel() {
		return Template.instance().isUpdating.get() ? '<i class="fa fa-spinner fa-spin"></i> Updating' : (Template.instance().queueMode.get() ? 'Queue' : 'Update');
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	}
});
