<template name="billingReportPayoutDetail">
	
	<div class="row" id="reportLoadingBox">
		<i class="fa fa-spinner fa-spin fa-4x"></i> <br/>Loading Report
	</div>
	<span id="reportContent" style="display:none">
		<h3>Payout Details</h3>
		<div class="box box-solid content-box">
			<div class="box-body">
				<h4>Overview</h4>
				<div class="row">
					<div class="col-md-12">
						<table class="table">
							<tbody>
								<tr>
									<td>Date paid</td>
									<td>{{formatDate transferData.available_on "MM/DD/YYYY"}}</td>
								</tr>
								<tr>
									<td>Amount</td>
									<td>{{formatCurrency transferData.net}}</td>
								</tr>
								<tr>
									<td>Fee</td>
									<td>{{formatCurrency transferData.fee}}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<br/>
				<h4>Summary</h4>
				<div class="row">
					<div class="col-md-12">
						<table class="table table-striped">
							<tbody>
								<tr>
									<th></th>
									<th class="text-right">Count</th>
									<th class="text-right">Gross</th>
									<th class="text-right">Fees</th>
									<th class="text-right">Total</th>
								</tr>
								<tr>
									<td>Charges</td>
									<td align=right>{{summary.charges.count}}</td>
									<td align=right>{{formatCurrency summary.charges.gross}}</td>
									<td align=right>{{formatCurrency summary.charges.fees}}</td>
									<td align=right>{{formatCurrency summary.charges.total}}</td>
								</tr>
								<tr>
									<td>Refunds</td>
									<td align=right>{{summary.refunds.count}}</td>
									<td align=right>{{formatCurrency summary.refunds.gross}}</td>
									<td align=right>{{formatCurrency summary.refunds.fees}}</td>
									<td align=right>{{formatCurrency summary.refunds.total}}</td>
								</tr>
								<tr>
									<td>Adjustments</td>
									<td align=right>{{summary.adjustments.count}}</td>
									<td align=right>{{formatCurrency summary.adjustments.gross}}</td>
									<td align=right>{{formatCurrency summary.adjustments.fees}}</td>
									<td align=right>{{formatCurrency summary.adjustments.total}}</td>
								</tr>
								{{#if summary.topUps}}
								<tr>
									<td>Top Ups</td>
									<td align=right>{{summary.topUps.count}}</td>
									<td align=right>{{formatCurrency summary.topUps.gross}}</td>
									<td align=right>{{formatCurrency summary.topUps.fees}}</td>
									<td align=right>{{formatCurrency summary.topUps.total}}</td>
								</tr>
								{{/if}}
								<tr>
									<td colspan=4>Payouts</td>
									<td align=right>{{formatCurrency summary.payoutsTotal.total}}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<br/>
				<h4>Transactions</h4>
				<div class="row">
					<div class="col-md-12">
						<table class="table table-striped">
							<tbody>
								<tr>
									<th>Type</th>
									<th class="text-right">Gross</th>
									<th class="text-right">Fee</th>
									<th class="text-right">Total</th>
									<th>Details</th>
									<th>Date</th>
									
								</tr>

								{{#each transactions}}
								<tr>
									<td>{{cleanType type}}</td>
									<td align=right>{{formatCurrency amount}}</td>
									<td align=right>{{formatCurrency fee}}</td>
									<td align=right>{{formatCurrency net}}</td>
									<td>{{#if description}}{{description}}<br/>{{/if}}
										{{#if subDescription}}{{subDescription}}<br/>{{/if}}
										{{#if source.metadata.paidBy}}{{source.metadata.paidBy}}<br/>{{/if}}{{#if source.id}}Ref #: {{source.id}}{{/if}}
										{{#if destinationType}}<a href="#" class="btn-view-destination" data-type="{{destinationType}}" data-id="{{id}}">View {{destinationType}}</a>{{/if}}
									</td>
									<td>{{formatDate arrival_date "MM/DD/YYYY"}}</td>
								</tr>
								{{/each}}

							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</span>
</template>
