<template name="billingReportPlanEnrollments">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Plan Enrollments Report
					<span class="text-muted pt-2 font-size-sm d-block">View people enrolled by plan.</span>
				</h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if familyBalances}}
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				{{/if}}
				{{#unless isSavedMode}}
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					{{{updateLabel}}}
				</div>
				{{/unless}}
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<div class="row">
				{{#if isSavedMode}}
				<div class="row font-weight-bolder text-dark">
					<div class="col">
						{{{ savedHeader }}}
					</div>
				</div>
				{{/if}}
				{{#unless isSavedMode}}
				<div class="col-md-3">

					<div class="form-group" style="margin-left:10px">
						<label class="control-label">Group:</label><br />
						<select class="form-control" name="group-id">
							<option value=""></option>
							{{#each availableGroups}}
							<option value="{{_id}}">{{name}}</option>
							{{/each}}
						</select>
					</div>
				</div>

				<div class="col-md-3">
					<div class="form-group" style="margin-left:10px">
						<label class="control-label">Plan:</label><br />
						<select class="form-control" name="plan-id">
							<option value=""></option>
							<option value="none">Not enrolled</option>
							{{#each availablePlans}}
							<option value="{{_id}}">{{description}}</option>
							{{/each}}
						</select>
					</div>

				</div>

				<!-- Add Orgs(id) When the select is "Not Enrolled" is "active" -->
				<div class="col-md-3" id="not-enrolled" style="display:none;">
					<div class="form-group">
						<label>Org(s):</label>
						<div class="input-group">
							{{> reportOrgsField }}
						</div>
					</div>
				</div>
				<div class="col-md-3 pt-8">
					<div class="checkbox-list">
						<label class="checkbox checkbox-primary">
							<input type="checkbox" id="groupByPerson">
							<span></span>
							Group By {{translationForEntityType "person"}} with Schedule
						</label>
					</div>
				</div>
				{{> reportQueueCheckbox }}
				{{/unless}}
			</div>
			{{#if plans}}
				{{#if groupByPerson}}
					<div class="row">
						<div class="col-md-12">

							<h3 class='text-center'>Plan Enrollments Report</h3>
							<h4 class='text-center'>Org: {{orgName}}</h4>

							<table class="table " style="overflow-x:auto;display:block">
								<tbody>
									<tr>
										<th>Person</th>
										<th>Age</th>
										<th>Group</th>
										<th>Last Invoice Date</th>
										<th>Last Invoice Amount</th>
										<th>Schedule</th>
										<th>Plan Name</th>
										<th style="text-align:right">Enrollment Date</th>
										<th style="text-align:right">Suspension Date</th>
										<th style="text-align:right">Amount</th>
									</tr>
									{{#each obj in plans}}
									<tr>
										<td><a href="/people/{{obj.person._id}}#billing"
												target="_blank">{{obj.person.lastName}}, {{obj.person.firstName}}</a></td>
										<td>{{obj.age}}</td>
										<td>{{obj.person.defaultGroupName}}</td>
										<td style="text-align:right">{{#if obj.person.lastInvoice}}{{formatDate
											obj.person.lastInvoice.createdAt "M/D/YYYY"}}{{/if}}</td>
										<td style="text-align:right">{{#if obj.person.lastInvoice}}{{formatCurrency
											obj.person.lastInvoice.originalAmount}}{{/if}}</td>
										<td>{{#each schedule in obj.personSchedules}} {{{formatSchedule schedule}}} {{/each}}
										</td>
										<td></td>
										<td style="text-align:right"></td>
										<td style="text-align:right"></td>
										<td style="text-align:right"></td>
									</tr>
									{{#each plan in obj.personPlans}}
									<tr>
										<td></td>
										<td></td>
										<td></td>
										<td></td>
										<td></td>
										<td></td>
										<td>{{getPersonPlanNameForGroup plan}}</td>
										<td style="text-align:right">{{formatDate plan.enrollmentDate "M/D/YYYY"}}</td>
										<td style="text-align:right">{{formatDate obj.person.suspendedUntil "M/D/YYYY"}}</td>
										<td style="text-align:right">{{#if shouldShowAmount plan}}{{formatCurrency
											(getPersonPlanTotalForGroup plan)}}{{/if}}</td>
									</tr>
									{{/each}}
									{{/each}}
								</tbody>
							</table>
							{{#if noResults}}
							<div style="text-align:center">No results to display.</div>
							{{/if}}
						</div>
					</div>
				{{else}}
					<div class="row">
					<div class="col-md-12">

						<h3 class='text-center'>Plan Enrollments Report</h3>
						<h4 class='text-center'>Org: {{orgName}}</h4>
						<table class="table ">
							<tbody>
								<tr>
									<th>Plan Name</th>
									<th>Person</th>
									<th>Age</th>
									<th>Group</th>
									<th>Last Invoice Date</th>
									<th>Last Invoice Amount</th>
									<th style="text-align:right">Enrollment Date</th>
									<th style="text-align:right">Suspension Date</th>
									<th style="text-align:right">Amount</th>
									<th>Third Party Payer Amount</th>
									<th>Family Splits</th>
									<th>Discounts</th>
								</tr>
								{{#each plan in plans}}
								<tr>
									<td colspan=12
										style="font-weight:bold;border-top:1px solid #000;border-bottom:1px solid #999">
										{{plan.description}} -- Total Enrolled: {{plan.enrolledPeoplePlans.length}}
									</td>
								</tr>
								{{#each personPlan in (sortPeoplePlans plan.enrolledPeoplePlans)}}
								<tr>
									<td></td>
									<td><a href="/people/{{personPlan.person._id}}#billing"
											target="_blank">{{personPlan.person.lastName}},
											{{personPlan.person.firstName}}</a></td>
									<td>{{personPlan.age}}</td>
									<td>{{personPlan.person.defaultGroupName}}</td>
									<td style="text-align:right">{{#if personPlan.person.lastInvoice}}{{formatDate
										personPlan.person.lastInvoice.createdAt "M/D/YYYY"}}{{/if}}</td>
									<td style="text-align:right">{{#if personPlan.person.lastInvoice}}{{formatCurrency
										personPlan.person.lastInvoice.originalAmount}}{{/if}}</td>
									<td style="text-align:right">{{formatDate personPlan.currentPlan.enrollmentDate
										"M/D/YYYY"}}
									</td>
									<td style="text-align:right">{{formatDate personPlan.person.suspendedUntil "M/D/YYYY"}}
									</td>
									<td style="text-align:right">{{#if shouldShowAmount plan}}{{formatCurrency
										(getPersonPlanTotal personPlan plan)}}{{/if}}
									</td>
									<td style="text-align:center">{{getThirdPartyPayer personPlan plan}}</td>
									<td>{{getFamilySplits personPlan }}</td>
									<td>{{formatCurrency (getDiscounts personPlan plan)}}</td>
								</tr>
								{{/each}}
								<tr>
									<td colspan=12 style="border-top:1px solid #999">
										Total {{plan.description}}
									</td>
									<td style="text-align:right;border-top:1px solid #999">
										{{#if shouldShowAmount plan}}{{formatCurrency (planTotal plan)}}{{/if}}
									</td>
								</tr>
								{{/each}}
							</tbody>
						</table>
						{{#if noResults}}
						<div style="text-align:center">No results to display.</div>
						{{/if}}
					</div>
					</div>
				{{/if}}
			{{/if}}
			{{#if noEnrolledMultipleOrgs}}
				<div class="row">
					<div class="col-md-12">
						<h3 class='text-center'>Plan Enrollments Report</h3>
					</div>
					
						
							<div class="col-md-12">
								<table class="table ">
									<tbody>
										<tr>
											<th>Plan Name</th>
											<th>Person</th>
											<th>Age</th>
											<th>Group</th>
											<th>Last Invoice Date</th>
											<th>Last Invoice Amount</th>
											<th style="text-align:right">Enrollment Date</th>
											<th style="text-align:right">Suspension Date</th>
											<th style="text-align:right">Amount</th>
											<th>Third Party Payer Amount</th>
											<th>Family Splits</th>
											<th>Discounts</th>
										</tr>
									</tbody>
										{{#each noEnrolledMultipleOrgs}}
											{{#let noEnrolledMultipleOrg=this OrgIndex=@index}}
												<tbody
														data-parent-id="{{ noEnrolledMultipleOrg.displayParentId }}"
														data-state="closed"
														data-display-id="{{ noEnrolledMultipleOrg.displayId }}"
														{{ rollupHidden noEnrolledMultipleOrg.displayParentId }}
												>
													<tr class="{{ rollupIndent noEnrolledMultipleOrg.displayDepth }}">
														<td colspan=12
															style="font-weight:bold;border-top:1px solid #000;border-bottom:1px solid #999">
															{{#if rollupHasChildren noEnrolledMultipleOrg.displayChildrenCount }}
															<a href="javascript:void(0)" class="rollup-control" data-control-id="{{noEnrolledMultipleOrg.displayId}}" onclick="rollupToggle(this)">
																<i class="fa fa-caret-down d-none"></i>
																<i class="fa fa-caret-right "></i>
															</a>
															{{/if}}
															{{noEnrolledMultipleOrg.Name}} -- Total Not Enrolled: {{ totalNot(noEnrolledMultipleOrg) }}
															{{#if checkAmount noEnrolledMultipleOrg.enrollments.enrolledPeoplePlans}} - <a class="ORGC-Show" data-id="{{OrgIndex}}" data-org-id="{{ noEnrolledMultipleOrg.displayId }}" data-org-name="{{ noEnrolledMultipleOrg.Name }}" href="">See More Info</a> {{/if}}
														</td>
													</tr>

													{{#if noEnrolledMultipleOrg.enrollments.enrolledPeoplePlans }}
													{{#each personPlan in (sortPeoplePlans noEnrolledMultipleOrg.enrollments.enrolledPeoplePlans)}}
													<tr style="display:none;" class="ORGC-{{OrgIndex}} {{rollupIndent noEnrolledMultipleOrg }}">
														<td> </td>
														<td><a href="" class="no-enrolled-person-link" data-person-id="{{personPlan.person._id}}" data-org-id="{{ noEnrolledMultipleOrg.displayId }}">{{personPlan.person.lastName}},
																{{personPlan.person.firstName}}</a></td>
														<td>{{personPlan.age}}</td>
														<td>{{personPlan.person.defaultGroupName}}</td>
														<td style="text-align:right">{{#if personPlan.person.lastInvoice}}{{formatDate
															personPlan.person.lastInvoice.createdAt "M/D/YYYY"}}{{/if}}</td>
														<td style="text-align:right">{{#if personPlan.person.lastInvoice}}{{formatCurrency
															personPlan.person.lastInvoice.originalAmount}}{{/if}}</td>
														<td style="text-align:right">{{formatDate personPlan.currentPlan.enrollmentDate
															"M/D/YYYY"}}
														</td>
														<td style="text-align:right">{{formatDate personPlan.person.suspendedUntil "M/D/YYYY"}}
														</td>
														<td style="text-align:right">{{#if shouldShowAmount noEnrolledMultipleOrg.enrollments}}{{formatCurrency
															(getPersonPlanTotal personPlan noEnrolledMultipleOrg.enrollments)}}{{/if}}
														</td>
														{{#if personPlan.person.currentPlan.allocations}}
															<td style="text-align:center">{{getThirdPartyPayer personPlan noEnrolledMultipleOrg.enrollments}}</td>
															<td>{{getFamilySplits personPlan }}</td>
															<td>{{formatCurrency (getDiscounts personPlan noEnrolledMultipleOrg.enrollments)}}</td>
														{{else}}
															<td></td>
															<td></td>
															<td></td>
														{{/if}}
													</tr>
													{{/each}}
														
													{{/if }}
												</tbody>
											{{/let}}
										{{/each}}						
								</table>
							</div>
					</div>
			{{/if}}
		</div>
	</div>

</template>