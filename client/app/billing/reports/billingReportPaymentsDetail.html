<template name="billingReportPaymentsDetail">

    <div class="card card-custom">
        <div class="card-header flex-wrap border-0 pt-6 pb-0">
            <div class="card-title">
                <h3 class="card-label">Invoice Credits Detail Report
                    <span class="text-muted pt-2 font-size-sm d-block">View and filter payments and credits applied to invoices by date range.</span>
                </h3>
            </div>
            <div class="card-toolbar">
                <!--begin::Button-->
                {{#if transactions}}
                    <div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
                        <i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
                    </div>
                {{/if}}
                <div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
                    Update
                </div>
                <!--end::Button-->
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">

                    <div class="form-group" style="margin-left:10px">
                        <label class="control-label">Date Start:</label>
                        <div class="input-group date">
                            <input data-cy="start-date-input" type="text" class="form-control" name="start_date" value="{{defaultDates 'start'}}">
                            <div class="input-group-addon">
                                <span class="glyphicon glyphicon-th"></span>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-md-3">

                    <div class="form-group" style="margin-left:10px">
                        <label class="control-label">Date End:</label>
                        <div class="input-group date">
                            <input data-cy="end-date-input" type="text" class="form-control" name="end_date" value="{{defaultDates 'end'}}">
                            <div class="input-group-addon">
                                <span class="glyphicon glyphicon-th"></span>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-md-4">
                    <form>
                        <label data-cy="payment-type" class="control-label">Payment Type:</label><br/>
                        <select data-cy="payment-type-select" name="payment-type" class="form-control" multiple="multiple">
                            <option value="manual_cash">Manual Payment - Cash</option>
                            <option value="manual_check">Manual Payment - Check</option>
                            <option value="manual_card">Manual Payment - Credit Card</option>
                            <option value="manual_ach">Manual Payment - ACH</option>
                            {{#each payerSources}}
                                <option value="payer_{{type}}">Payer - {{description}}</option>
                            {{/each}}
                            <option value="payment_ach">Payment - ACH</option>
                            <option value="payment_card">Payment - Credit Card</option>
                            <option value="payment_creditmemo">Payment - Credit Memo</option>
                            <option value="security_deposit_auto">Security Deposits (system)</option>
                            <option value="other">Other</option>
                        </select>
                    </form>
                </div>
            </div>
            <div class="row">
                    <div class="col-md-3">
                        <div class="form-group" style="margin-left:10px">
                            <input data-cy="search-entries" name="search-entries" type="text" class="form-control" placeholder="Search">
                        </div>
                    </div>
                <div class="col-md-3">
                    <div class="checkbox-list">
                        <label class="checkbox checkbox-primary">
                            <input data-cy="include-payouts-check" type="checkbox" class="form-check-input" id="chkIncludePayouts">
                            <span></span>
                            Include Payouts (longer load time)
                        </label>
                    </div>
                </div>
            </div>

            <br/>
            {{#if transactions}}
                <div class="row">
                    <div class="col-md-12">

                        <h3 class='text-center'>Invoice Credits Detail Report</h3>
                        <h4 class='text-center'>Org: {{orgName}}</h4>

                        <table class="table table-striped" id="dvData">
                            <tbody>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>For</th>
                                <th>Invoice #</th>
                                {{#if showCheckNumber}}
                                    <th>Check #</th>
                                {{/if}}
                                <th>Period</th>
                                <th style="text-align:right">Amount</th>
                                {{#if showPayoutData}}
                                    <th>Payout</th>
                                {{/if}}
                            </tr>

                            {{#each transactions}}
                                <tr>
                                    <td data-cy="date-invoice-credit">{{formatDate createdAt "MM/DD/YYYY"}}</td>
                                    <td data-cy="type-invoice-credit">{{type}}</td>
                                    <td data-cy="description-invoice-credit">{{description}}</td>
                                    <td data-cy="for-invoice-credit"><a href="/people/{{personId}}" target="_blank">{{personName}}</a></td>
                                    <td data-cy="invoice-number"><a href="/billing/invoices/{{invoiceId}}" target="_blank">{{invoiceNumber}}</a>
                                    </td>
                                    {{#if showCheckNumber}}
                                        <td data-cy="check-number-invoice">{{checkNumber}}</td>
                                    {{/if}}
                                    <td data-cy="period-inovice-credit">{{coversPeriodDescription}}</td>
                                    <td data-cy="invoice-credit-amount" style="text-align:right">{{formatCurrency amount}}</td>
                                    {{#if showPayoutData}}
                                        <td>{{#if payoutInfo}}
                                            <a href="/billing/admin/reports/payoutDetail?provider=&payoutId={{payoutInfo.payoutId}}&refId={{payoutInfo.refId}}#reports"
                                            target="_blank">{{payoutInfo.payoutDate}}</a>
                                        {{/if}}
                                        </td>
                                    {{/if}}
                                </tr>
                            {{/each}}

                            {{#if transactions}}
                                <tr>
                                    <td colspan=5>Total</td>
                                    <td data-cy="total-amount-invoice-credit" style="text-align:right">{{formatCurrency totalAmount}}</td>
                                </tr>
                            {{/if}}
                            </tbody>
                        </table>
                    </div>
                </div>
            {{/if}}
		</div>
	</div>

</template>
