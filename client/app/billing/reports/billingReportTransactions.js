import { isMoment } from "moment";
import './billingReportTransactions.html';
import { Orgs } from "../../../../lib/collections/orgs";
import _ from '../../../../lib/util/underscore';

Template.billingReportTransactions.onCreated( function () {
	this.transactionsData = ReactiveVar();
});

Template.billingReportTransactions.onRendered( function () {
	$(".input-group.date").datepicker({
		zIndexOffset:9999
	});
});
Template.billingReportTransactions.events({
	"click #btnUpdate": async function(e,i) {
		$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Updating').prop("disabled", true);
		options = {};
		var self = Template.instance();
		options.startDate = $("input[name=start_date]").val();
		options.endDate = $("input[name=end_date]").val();
		options.type = $("select[name=type]").val();
		options.stripeLegacy = $("#chkStripeLegacy").prop("checked");
		Meteor.callAsync("billingTransactionReport", options)
		.then((result) => {
			$(e.target).html('Update').prop("disabled", false);
			self.transactionsData.set(result);
		})
		.catch((error) => {
			$(e.target).html('Update').prop("disabled", false);
			mpSwal.fire("Error", error.reason, "error");
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "billingTransactions"
		});
	},
	"click #btnExport": async function() {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvData'), outputFile]);
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "billingTransactions"
		});
	}
});

Template.billingReportTransactions.helpers({
	todayDate() {
		return new moment().format("MM/DD/YYYY");
	},
	transactions() {
		const transactionData = Template.instance().transactionsData.get();
		if (transactionData) return transactionData.data;
	},
	totalAmount() {
		const transactionData = Template.instance().transactionsData.get();
		console.log("output", transactionData);
		if (transactionData) return transactionData.totalAmount;
	},
	transactionsEnabled() {
		return Orgs.current() &&  ( _.deep(Orgs.current(), "billing.stripeAccountId") ||
			_.deep(Orgs.current(), "billing.adyenInfo") || 
			_.deep(Orgs.current(), "billing.cashnetEnabled") );
	},
	orgName() {
		return Orgs.current() && Orgs.current().name;
	},
	stripeLegacyEnabled() {
		return Orgs.current() && (_.deep(Orgs.current(), "billing.stripeAccountIdLegacy"));
	}
});
