import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../../lib/collections/people';
import './billingReportChargesAttendance.html';
import { Orgs } from '../../../../lib/collections/orgs';
import '../../reports/reportOrgsField';
import '../../reports/reportQueueTemplate';
import { reportQueuedSwal } from '../../reports/queueReportsUtil';

Template.billingReportChargesAttendance.created = function() {
	const instance = this;
	this.reportData = new ReactiveVar();
	instance.dataCompleted = new ReactiveVar(false);
	instance.queueMode = new ReactiveVar(false);
	instance.savedMode = new ReactiveVar(false);
	instance.savedHeader = new ReactiveVar();
	instance.isUpdating = new ReactiveVar(false);
	instance.showOrgColumn = new ReactiveVar(false);
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId })
		.then((response) => {
			this.savedMode.set(true);
			this.reportData.set(response.data);
			this.dataCompleted.set(true);
			const reportArgs = response.args;
			let header = '';
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgIds })
			.then((resp) => {
				header = 'Orgs: ' + resp + '<br>';
			})
			.catch((error) => {
				header = 'Orgs: ' + undefined + '<br>';
			})
			.finally(()=>{
				this.showOrgColumn.set(reportArgs.orgIds?.length > 1);
				this.savedHeader.set(header);
			});
		});
	}
}

Template.billingReportChargesAttendance.events({
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
	"click #btnUpdate": async function(e, i) {
		const orgIds = $("#reportOrgs").val();
		const params = { orgIds };

		i.dataCompleted.set(false);
		if (i.queueMode.get()) {
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'chargesAttendance',
				reportName: 'Billing Plans and Attendance',
				reportArgs: params,
				userId: Meteor.userId(),
				reportRoute: FlowRouter.current().path.slice(1)
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "chargesAttendance"
			});
			reportQueuedSwal();
			return;
		}

		i.isUpdating.set(true);
		$('#btnUpdate').prop('disabled', true);
		Meteor.callAsync('chargesAttendance', params)
		.then(async (response) => {
			i.isUpdating.set(false);
			i.showOrgColumn.set(orgIds?.length > 1);
			$(e.target).prop('disabled', false);
			i.reportData.set(response);
			i.dataCompleted.set(true);
			await Meteor.callAsync('trackClientActivity', {
				label: "report-created",
				reportType: "chargesAttendance"
			});
		})
		.catch(async (err) => {
			i.isUpdating.set(false);
			i.showOrgColumn.set(orgIds?.length > 1);
			$(e.target).prop('disabled', false);
			i.reportData.set(undefined);
			mpSwal.fire(err.reason);
			i.dataCompleted.set(true);
			await Meteor.callAsync('trackClientActivity', {
				label: "report-created",
				reportType: "chargesAttendance"
			});
		});
	},
	"click .destination-link": function (e, i) {
		e.preventDefault();
		const element = $(event.target);
		const personId = element.attr('data-person-id');
		const orgId = element.attr('data-org-id');
		const childTab = element.hasClass('billing') ? '#billing' : '';
		if (orgId !== Orgs.current()._id) {
			const orgName = element.attr('data-org-name');
			mpSwal.fire({
				title: 'Warning',
				icon: 'warning',
				html: `<p>Viewing this child will switch YOU to being logged into ${orgName}, <span class="font-italic">and you will not see the change to your site in the upper right until you refresh this browser page.</span></i></p>`,
				showCancelButton: true,
				confirmButtonText: 'Proceed',
				cancelButtonText: 'Cancel'
			}).then ( result => {
				if (result.value) {
					Meteor.callAsync('adminSwitchOrg', orgId)
					.then((result) => {
						window.open(`/people/${personId}${childTab}`, '_blank');
					})
					.catch((error) => {	
						mpSwal.fire('Error', error.reason, 'error');
					});
				}
			});
		} else {
			window.open(`/people/${personId}${childTab}`, '_blank');
		}
	}
});

Template.billingReportChargesAttendance.helpers({
	dateRangeLabel() {
		return Template.instance().dateRangeLabel.get();
	},
	totals() {
		const data = Template.instance().reportData.get();
		return data && data.totals;
	},
	people() {
		return People.find({type:{"$in": ["staff", "admin"]}}, {sort:{lastName:1, firstName:1}});
	},
  	isHavingData(table) {
		const data = Template.instance().reportData.get()[table];
		return data && data.length > 0;
  	},
  	canRender() {
    	return !!Template.instance().reportData.get();
  	},
  	table(dataSet){
    	return Template.instance().reportData.get()[dataSet];
  	},
	hasChildAttended(child) {
		return !!child.checkedInOutTime;
	},
	updateLabel() {
		return Template.instance().isUpdating.get() ? '<i class="fa fa-spinner fa-spin"></i> Updating' : (Template.instance().queueMode.get() ? 'Queue' : 'Update');
	},
	isQueueMode() {
		return Template.instance().queueMode.get();
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
	dataCompleted: function() {
		return Template.instance().dataCompleted.get();
	},
	showOrgColumn() {
		return Template.instance().showOrgColumn.get();
	},
	getNameColumnWidth() {
		 return Template.instance().showOrgColumn.get() ? '30%' : '50%';
	}
});
