import { isMoment } from "moment";
import './billingReportPaymentsDetail.html'
import { Orgs } from "../../../../lib/collections/orgs";
import '../_invoiceLedgerModal';
import { showModal } from "../../main";

Template.billingReportPaymentsDetail.onCreated( function () {
	this.transactionsData = ReactiveVar();
	this.showCheckNumber = ReactiveVar(false);
});

Template.billingReportPaymentsDetail.onRendered( function () {
	$(".input-group.date").datepicker({
		zIndexOffset:9999
	});
	$("select[name=payment-type]").multiselect({
		nonSelectedText: 'All'
	});
});
Template.billingReportPaymentsDetail.events({
	"click #btnUpdate": async function(e,i) {
		$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Updating').prop("disabled", true);
		options = {};
		var self = Template.instance();
		options.startDate = $("input[name=start_date]").val();
		options.endDate = $("input[name=end_date]").val();
		options.paymentType = $("select[name=payment-type]").val();
		options.includePayouts = $("#chkIncludePayouts").prop('checked');
		const query = $("input[name=search-entries]").val();
		options.query = query ? query : null;

		Meteor.callAsync("billingPaymentsDetailReport", options)
		.then((result) => {
			$(e.target).html('Update').prop("disabled", false);
			self.showCheckNumber.set(false);
			for (const entry of result.entries) {
				if (entry.checkNumber) {
					self.showCheckNumber.set(true);
				}
			}
			self.transactionsData.set(result);
		})
		.catch((error) => {
			$(e.target).html('Update').prop("disabled", false);
			mpSwal.fire("Error", error.reason, "error");
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "billingPaymentsDetail"
		});
	},
	"click .btnViewLedger": function(e,i) {
		e.preventDefault();
		const invoiceId = this._id;
		showModal("invoiceLedgerModal", {invoiceId: invoiceId});
	},
	"click #btnExport": async function () {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvData'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "billingPaymentsDetail"
		});
	},
});

Template.billingReportPaymentsDetail.helpers({
	defaultDates(option) {
		return option=="start" ? new moment().subtract(1, "month").format("MM/DD/YYYY") : new moment().format("MM/DD/YYYY");
	},
	transactions() {
		const transactionData = Template.instance().transactionsData.get();
		if (transactionData) return transactionData.entries;
	},
	totalAmount() {
		const transactionData = Template.instance().transactionsData.get();
		return (transactionData && transactionData.totalAmount) || 0.0;
	},
	payerSources() {
		return Orgs.current().availablePayerSources(true);
	},
	orgName() {
		return Orgs.current() && Orgs.current().name;
	},
	showPayoutData() {
		const o = Orgs.current(), billingStatus = o && o.billingStatus();
		return billingStatus && billingStatus.status == "active";
	},
	showCheckNumber() {
		return Template.instance().showCheckNumber.get();
	}
});
