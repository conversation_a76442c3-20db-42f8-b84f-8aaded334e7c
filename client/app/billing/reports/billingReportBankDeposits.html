<template name="billingReportBankDeposits">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Bank Deposits Reconciliation Report
				<span class="text-muted pt-2 font-size-sm d-block">View and filter deposit entries for a particular date range.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if entries}}
					<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>				
				{{/if}}
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="row">

				<div class="col-md-3">
					
						<div class="form-group" style="margin-left:10px">
							<label>Date Range</label>
							<input data-cy="date-range-input" type="text" name="invoices-date-range" class="form-control">
						</div>
					
				</div>

				<div class="col-md-3">
					
						
					
				</div>
			</div>
			
			
			<br/>
			{{#if entries}}
			<div class="row">
				<div class="col-md-12">
					
					<h3 class='text-center'>Bank Deposits Reconciliation Report</h3>
					<h4 class='text-center'>Org: {{orgName}}</h4>
					<h4 class='text-center'>Date Range: {{formattedDateRange}}</h4>
					
					<table class="table {{#unless includeTransactions}}table-striped{{/unless}}" id="dvData">
						<tbody>
							<tr>
								<th>Date</th>
								<th>Type</th>
								<th>Comments/Notes</th>
								<th class="text-right">Amount</th>
								
							</tr>

							{{#each detail in entries}}

							
								<tr>
									
									<td>{{detail.date}}</td>
									<td>{{detail.itemTypeDescription}} {{detail.itemTypeDescriptionDetail}}</td>
									<td>{{detail.description}} </td>
									<td class="text-right">{{formatCurrency detail.amount}}</td>
									
								</tr>
										
									
							{{/each}}

							
							<tr>
								<td style="font-weight:bold">Total</td>
								<td></td>
								<td></td>
								
								<td style="text-align:right;font-weight:bold">{{formatCurrency (totalAmount "debit")}}</td>
								
							</tr>
							
						</tbody>
					</table>
				</div>
			</div>

			{{else}}
				{{#if loaded}}
					<p>There are no ledger entries matching your filter criteria. Please note this report requires ledger account mappings to be setup before it will generate results.</p>
				{{/if}}
			{{/if}}
		</div>
	</div>
</template>
