<template name="billingReportAging">
	<!--begin::Card-->
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Aging Report
				<span class="text-muted pt-2 font-size-sm d-block">View current and past due balances, arranged by age buckets.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if invoicePeople}}
					<div data-cy="export-csv-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>
				{{/if}}
				{{#unless isSavedMode}}
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					{{{updateLabel}}}
				</div>
				{{/unless}}
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<!--begin: Search Form-->
			<!--begin::Search Form-->
			<div class="mb-7">
			<form id="invoiceSearch">
				{{#if isSavedMode}}
				<div class="row font-weight-bolder text-dark">
					<div class="col">
						{{{ savedHeader }}}
					</div>
				</div>
				{{/if}}
				{{#unless isSavedMode}}
				<div class="row">
					<div class="col-sm-4">
						<div class="form-group">
							<label class="control-label">Filter by name:</label><br/>
							<input type="text" class="form-control" name="search_by_name">
						</div>
					</div>

					<div class="col-sm-4">
						<div class="form-group">
							<label class="control-label">Group by:</label><br/>
							<select data-cy="group-by-filter" name="filter_grouping" class="form-control">
								<option value="family">Family</option>
								<option value="individual">Individual</option>
							</select>
						</div>
					</div>
					<div class="col-sm-4">
						<label class="control-label">Date:</label><br/>
						<div class="input-group date">
							<input data-cy="calendar" type="text" class="form-control" name="date" value="{{todayDate}}">
							<div class="input-group-addon">
								<span class="glyphicon glyphicon-th"></span>
							</div>
						</div>
					</div>					
				</div>
				<div class="row">
					<div class="col-sm-4">
						<label class="control-label">Balance Type:</label><br/>
						<select data-cy="balance-type-filter" name="filter_balance_type" class="form-control" id="filter_balance_type">
							<option value="family">Family</option>
							<option value="payer">Payer</option>
							<option value="both">Both</option>
						</select>
					</div>
					<div class="col-sm-4">
						<div class="form-group">
							<label class="control-label">Filter by balance:</label><br/>
							<select data-cy="range-days-filter" name="filter_balance" class="form-control" id="filter_balance">
								<option value="">All</option>
								<option value="0-6">0-6</option>
								<option value="7-13">7-13</option>
								<option value="14-20">14-20</option>
								<option value="21-27">21-27</option>
								<option value="over28">Over 28</option>
							</select>
						</div>
					</div>
					<div class="col-sm-4">
						<div class="form-group">
							<label>Org(s):</label><br/>
							{{> reportOrgsField }}
						</div>
					</div>
				</div>
					<div class="col">
						<div class = "row">
							<div class="ml-4">
								{{> reportQueueCheckbox }}
							</div>
						</div>
						<div class="row">
							<div class="ml-4">
								<div class="col-auto d-flex justify-content-center px-0">
									<div class="d-flex">
										<div class="checkbox-list d-flex justify-content-center">
									<label class="checkbox checkbox-primary">
										<input type="checkbox" class="form-check-input" id="chkUsePeriodDate" >
										<span> </span>
										Use Posting Date to Age
									</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							{{#if showIncludeWaitList}}
								<div class="ml-4 mt-7">
										<div class="d-flex">
											<div class="checkbox-list d-flex justify-content-center">
												<label class="checkbox checkbox-primary">
												<input type="checkbox" class="form-check-input" id="chkIncludeWaitList" checked>
													<span> </span>
														Include Wait List
												</label>
											</div>
										</div>
								</div>
								{{/if}}
							</div>
					</div>
				{{/unless}}
			</form>
			<div class="row" style="margin-top:12px">
				<div class="col-md-12">
					{{#if invoicePeople}}
					<h3 class='text-center'>Aging Report</h3>
					<h4 class='text-center'>Org: {{reportOrgName}}</h4>
					
					<table data-cy="aging-report-table" class="table " id="dvData">
						<tbody>
							<tr style="border-bottom: 1px solid #666">
								<th>Name</th>
								{{#if showOrgName}}<th>Org</th>{{/if}}
								{{#if showPayerColumn}}<th>Payer</th>{{/if}}
								<th>Invoice #</th>
								<th>Notes</th>
								<th>Due Date</th>
								<th style="text-align:right">Original Amt</th>
								<th style="text-align:right">Open Amt</th>
								{{#if showFamilyDateRanges}}
									<th style="text-align:right">Current</th>
									<th style="text-align:right">0 to 6</th>
									<th style="text-align:right">7 to 13</th>
									<th style="text-align:right">14 to 20</th>
									<th style="text-align:right">21 to 27</th>
									<th style="text-align:right">Over 28</th>
								{{/if}}
								{{#if showPayerDateRanges}}
									<th style="text-align:right">Current</th>
									<th style="text-align:right">1 to 30</th>
									<th style="text-align:right">31 to 60</th>
									<th style="text-align:right">61 to 90</th>
									<th style="text-align:right">91 to 120</th>
                                    <th style="text-align:right">Over 121</th>
								{{/if}}
							</tr>
							
							{{#each centerTotals}}
								<tr
									class="bottom-border-only {{ rollupClass isRollup }} {{ rollupIndent displayDepth}}"
									data-parent-id="{{ displayParentId }}"
									data-state="closed"
									data-display-id="{{ displayId }}"
									{{ rollupHidden displayParentId }}
								>
									<td style="font-weight:600px">
										{{#if rollupHasChildren displayChildrenCount }}
										<a href="javascript:void(0)" class="rollup-control" data-control-id="{{displayId}}" onclick="rollupToggle(this)">
											<i class="fa fa-caret-down d-none"></i>
											<i class="fa fa-caret-right "></i>
										</a>
										{{/if}}
										<b>Invoice Total for {{name}}</b>
									</td>
									{{#if showOrgName}}<td></td>{{/if}}
									{{#if showPayerColumn}}<td></td>{{/if}}
									<td></td>
									<td></td>
									<td></td>
									<td data-cy="total-original-amount" style="text-align:right">{{formatCurrency totalNetOriginalAmount}}</td>
									<td data-cy="total-open-amount" style="text-align:right">{{formatCurrency totalOpenAmount}}</td>
									{{#if showFamilyDateRanges}}
										<td data-cy="total-current-amount" style="text-align:right">{{formatCurrency totalCurrentAmount}}</td>
										<td data-cy="total-first-range"    style="text-align:right">{{formatCurrency totalZeroToSix}}</td>
										<td data-cy="total-second-range"   style="text-align:right">{{formatCurrency totalSevenToThirteen}}</td>
										<td data-cy="total-third-range"    style="text-align:right">{{formatCurrency totalFourteenToTwenty}}</td>
										<td data-cy="total-fourth-range"   style="text-align:right">{{formatCurrency totalTwentyOneToTwentySeven}}</td>
										<td data-cy="total-fifth-range"    style="text-align:right">{{formatCurrency totalOverTwentyEight}}</td>
									{{/if}}
									{{#if showPayerDateRanges}}
										<td style="text-align:right">{{formatCurrency totalCurrentAmount}}</td>
										<td style="text-align:right">{{formatCurrency totalOneToThirty}}</td>
										<td style="text-align:right">{{formatCurrency totalThirtyOneToSixty}}</td>
										<td style="text-align:right">{{formatCurrency totalSixtyOneToNinety}}</td>
										<td style="text-align:right">{{formatCurrency totalNinetyOneToOneTwenty}}</td>
										<td style="text-align:right">{{formatCurrency totalOverOneTwenty}}</td>
									{{/if}}
								</tr>
							{{/each}}
							
							{{#each invoicePeople}}
							{{#if trueIfEq filterGrouping "family"}}
							<tr class="no-border">
								<td><b>Family: {{familyName}}</b></td>
								{{#if showOrgName}}<td><b>{{orgName}}</b></td>{{/if}}
							</tr>
							{{/if}}
								{{#each groupedInvoices}}
										<tr class="no-border">
											<td ><b><a href="/people/{{personId}}#billing" target="_blank">{{personName}} </a> {{#if personInActive}}<span style="color:#ff0000">(i)</span>{{/if}}
											 </b> {{#if waitListed}}<span style="color: #FF0000">(W)</span>{{/if}}
											{{#if showOrgName}}<td><b>{{orgName}}</b></td>{{/if}}</td>
										</tr>
									{{#each invoices}}
										{{#if isCredit}}
										<tr class="no-border">
											<td></td>
											{{#if showOrgName}}<td></td>{{/if}}
											{{#if showPayerColumn}}<td>{{payerName}}</td>{{/if}}
											<td>{{#if isCurrentOrg}}<a href="/people/{{paidByPersonId}}#billing" target="_blank">{{type}}</a>{{else}}{{type}}{{/if}}</td>
											<td></td>
											<td>{{formatDate createdAt "MM/DD/YYYY"}}</td>
											<td style="text-align:right">{{formatCurrency originalAmount}}</td>
											<td style="text-align:right">{{formatCurrency balancePointInTime}}</td>
											{{#if showFamilyDateRanges}}
												<td style="text-align:right">{{#if trueIfEq amountBucket "current"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "0-6"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "7-13"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "14-20"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "21-27"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "over28"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
											{{/if}}
											{{#if showPayerDateRanges}}
												<td style="text-align:right">{{#if trueIfEq amountBucket "current"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "1-30"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "31-60"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "61-90"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "91-120"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
                                                <td style="text-align:right">{{#if trueIfEq amountBucket "over121"}}{{formatCurrency
                                                        balancePointInTime}}{{/if}}</td>
											{{/if}}
										</tr>
										{{else}}
										<tr class="no-border">
											<td></td>
											{{#if showOrgName}}<td></td>{{/if}}
											{{#if showPayerColumn}}<td>{{payerName}}</td>{{/if}}
											<td>{{#if isCurrentOrg}}<a href="/billing/invoices/{{_id}}" target="_blank">{{invoiceNumber}}</a> <a href="#" class="btnViewLedger" target="_blank">(quick)</a>{{else}}{{invoiceNumber}}{{/if}}</td>
											<td>{{invoiceNotes}}</td>
											<td>{{formatDate dueDate "MM/DD/YYYY"}}</td>
											<td data-cy="original-amount-item" style="text-align:right">{{formatCurrency originalAmount}}</td>
											<td data-cy="open-amount-item" style="text-align:right">{{formatCurrency balancePointInTime}}</td>
											{{#if showFamilyDateRanges}}
												<td data-cy="current-amount-item"  style="text-align:right">{{#if trueIfEq amountBucket "current"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td data-cy="first-range-item"  style="text-align:right">{{#if trueIfEq amountBucket "0-6"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td data-cy="second-range-item" style="text-align:right">{{#if trueIfEq amountBucket "7-13"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td data-cy="third-range-item"  style="text-align:right">{{#if trueIfEq amountBucket "14-20"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td data-cy="fourth-range-item" style="text-align:right">{{#if trueIfEq amountBucket "21-27"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td data-cy="fifth-range-item"  style="text-align:right">{{#if trueIfEq amountBucket "over28"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
											{{/if}}
											{{#if showPayerDateRanges}}
												<td style="text-align:right">{{#if trueIfEq amountBucket "current"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "1-30"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "31-60"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "61-90"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
												<td style="text-align:right">{{#if trueIfEq amountBucket "91-120"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
                                                <td style="text-align:right">{{#if trueIfEq amountBucket "over121"}}{{formatCurrency
                                                        balancePointInTime}}{{/if}}</td>
											{{/if}}
										</tr>
										{{/if}}
									{{/each}}
								<tr class="bottom-border-only">
									<td style="font-weight:600px">Total {{personName}}</td>
									{{#if showOrgName}}<td></td>{{/if}}
									{{#if showPayerColumn}}<td></td>{{/if}}
									<td></td>
									<td></td>
									<td></td>
									<td ><div class="total-column">{{formatCurrency totalOriginalAmount}}</div></td>
									<td ><div class="total-column">{{formatCurrency totalOpenAmount}}</div></td>
									{{#if showFamilyDateRanges}}
										<td ><div class="total-column">{{formatCurrency totalCurrentAmount}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalZeroToSix}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalSevenToThirteen}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalFourteenToTwenty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalTwentyOneToTwentySeven}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalOverTwentyEight}}</div></td>
									{{/if}}
									{{#if showPayerDateRanges}}
										<td ><div class="total-column">{{formatCurrency totalCurrentAmount}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalOneToThirty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalThirtyOneToSixty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalSixtyOneToNinety}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNinetyOneToOneTwenty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalOverOneTwenty}}</div></td>
									{{/if}}
								</tr>
								{{/each}}
								{{#each familyCredits}}
								<tr class="no-border">
									<td style="padding-left:25px">{{paidByPersonName}} - credit memo</td>
									{{#if showOrgName}}<td></td>{{/if}}
									{{#if showPayerColumn}}<td>{{payerName}}</td>{{/if}}
									<td>{{#if isCurrentOrg}}<a href="/people/{{paidByPersonId}}#billing" target="_blank">{{type}}</a>{{else}}{{type}}{{/if}}</td>
									<td></td>
									<td>{{formatDate createdAt "MM/DD/YYYY"}}</td>
									<td style="text-align:right">{{formatCurrency originalAmount}}</td>
									<td style="text-align:right">{{formatCurrency balancePointInTime}}</td>
									{{#if showFamilyDateRanges}}
										<td style="text-align:right">{{#if trueIfEq amountBucket "current"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "0-6"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "7-13"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "14-20"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "21-27"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "over28"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
									{{/if}}
									{{#if showPayerDateRanges}}
										<td style="text-align:right">{{#if trueIfEq amountBucket "current"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "1-30"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "31-60"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "61-90"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
										<td style="text-align:right">{{#if trueIfEq amountBucket "91-120"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
                                        <td style="text-align:right">{{#if trueIfEq amountBucket "over121"}}{{formatCurrency balancePointInTime}}{{/if}}</td>
									{{/if}}
								</tr>
								{{/each}}
							{{#if trueIfEq filterGrouping "family"}}
								<tr class="bottom-border-only no-border">
									<td style="font-weight:600px"><b>Credit Total {{familyName}}</b></td>
									{{#if showOrgName}}<td></td>{{/if}}
									{{#if showPayerColumn}}<td></td>{{/if}}
									<td></td>
									<td></td>
									<td></td>
									<td ><div data-cy="credit-total-amount" class="total-column">{{formatCurrency totalCreditOriginalAmount}}</div></td>
									<td ><div data-cy="credit-open-amount" class="total-column">{{formatCurrency totalCreditOpenAmount}}</div></td>
									{{#if showFamilyDateRanges}}
										<td ><div data-cy="credit-current-amount" class="total-column">{{formatCurrency totalCreditCurrentAmount}}</div></td>
										<td ><div data-cy="credit-first-range"    class="total-column">{{formatCurrency totalCreditZeroToSix}}</div></td>
										<td ><div data-cy="credit-second-range"   class="total-column">{{formatCurrency totalCreditSevenToThirteen}}</div></td>
										<td ><div data-cy="credit-third-range"    class="total-column">{{formatCurrency totalCreditFourteenToTwenty}}</div></td>
										<td ><div data-cy="credit-fourth-range"   class="total-column">{{formatCurrency totalCreditTwentyOneToTwentySeven}}</div></td>
										<td ><div data-cy="credit-fifth-range"    class="total-column">{{formatCurrency totalCreditOverTwentyEight}}</div></td>
									{{/if}}
									{{#if showPayerDateRanges}}
										<td ><div class="total-column">{{formatCurrency totalCreditCurrentAmount}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalCreditOneToThirty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalCreditThirtyOneToSixty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalCreditSixtyOneToNinety}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalCreditNinetyOneToOneTwenty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalCreditOverOneTwenty}}</div></td>
									{{/if}}
								</tr>
								<tr class="bottom-border-only no-border">
									<td style="font-weight:600px"><b>Invoice Total {{familyName}}</b></td>
									{{#if showOrgName}}<td></td>{{/if}}
									{{#if showPayerColumn}}<td></td>{{/if}}
									<td></td>
									<td></td>
									<td></td>
									<td ><div data-cy="invoice-total-amount" class="total-column">{{formatCurrency totalOriginalAmount}}</div></td>
									<td ><div data-cy="invoice-open-amount" class="total-column">{{formatCurrency totalOpenAmount}}</div></td>
									{{#if showFamilyDateRanges}}
										<td ><div data-cy="invoice-current-amount" class="total-column">{{formatCurrency totalCurrentAmount}}</div></td>
										<td ><div data-cy="invoice-first-range"    class="total-column">{{formatCurrency totalZeroToSix}}</div></td>
										<td ><div data-cy="invoice-second-range"   class="total-column">{{formatCurrency totalSevenToThirteen}}</div></td>
										<td ><div data-cy="invoice-third-range"    class="total-column">{{formatCurrency totalFourteenToTwenty}}</div></td>
										<td ><div data-cy="invoice-fourth-range"   class="total-column">{{formatCurrency totalTwentyOneToTwentySeven}}</div></td>
										<td ><div data-cy="invoice-fifth-range"    class="total-column">{{formatCurrency totalOverTwentyEight}}</div></td>
									{{/if}}
									{{#if showPayerDateRanges}}
										<td ><div class="total-column">{{formatCurrency totalCurrentAmount}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalOneToThirty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalThirtyOneToSixty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalSixtyOneToNinety}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNinetyOneToOneTwenty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalOverOneTwenty}}</div></td>
									{{/if}}
								</tr>
								<tr class="bottom-border-only no-border">
									<td style="font-weight:600px"><b>Net Total {{familyName}}</b></td>
									{{#if showOrgName}}<td></td>{{/if}}
									{{#if showPayerColumn}}<td></td>{{/if}}
									<td></td>
									<td></td>
									<td></td>
									<td ><div data-cy="net-original-amount" class="total-column">{{formatCurrency totalNetOriginalAmount}}</div></td>
									<td ><div data-cy="net-open-amount"  class="total-column">{{formatCurrency totalNetOpenAmount}}</div></td>
									{{#if showFamilyDateRanges}}
										<td ><div data-cy="net-current-amount" class="total-column">{{formatCurrency totalNetCurrentAmount}}</div></td>
										<td ><div data-cy="net-first-range"   class="total-column">{{formatCurrency totalNetZeroToSix}}</div></td>
										<td ><div data-cy="net-second-range"   class="total-column">{{formatCurrency totalNetSevenToThirteen}}</div></td>
										<td ><div data-cy="net-third-range"    class="total-column">{{formatCurrency totalNetFourteenToTwenty}}</div></td>
										<td ><div data-cy="net-fourth-range"   class="total-column">{{formatCurrency totalNetTwentyOneToTwentySeven}}</div></td>
										<td ><div data-cy="net-fifth-range"    class="total-column">{{formatCurrency totalNetOverTwentyEight}}</div></td>
									{{/if}}
									{{#if showPayerDateRanges}}
										<td ><div class="total-column">{{formatCurrency totalNetCurrentAmount}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNetOneToThirty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNetThirtyOneToSixty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNetSixtyOneToNinety}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNetNinetyOneToOneTwenty}}</div></td>
										<td ><div class="total-column">{{formatCurrency totalNetOverOneTwenty}}</div></td>
									{{/if}}
								</tr>
							{{/if}}
							{{/each}}
						</tbody>
					</table>

					{{else}}
						{{#if dataCompleted}}
						<p> No results matched your current search criteria.</p>
						{{/if}}
					{{/if}}
				</div>
			</div>
		</div>
	</div>
	</div>
</template>