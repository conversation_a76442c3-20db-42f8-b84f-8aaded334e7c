import { isMoment } from "moment";
import {AvailableCustomizations} from "../../../../lib/customizations";
import {FlowRouter} from 'meteor/ostrio:flow-router-extra';
import './billingReportPayouts.html';
import { Orgs } from "../../../../lib/collections/orgs";
import _ from '../../../../lib/util/underscore';
import '../../reports/reportOrgsField';
import '../../reports/reportQueueTemplate';
import { reportQueuedSwal } from "../../reports/queueReportsUtil";

Template.billingReportPayouts.onCreated( function () {
	this.payoutsData = ReactiveVar();
	this.payoutsDetail = ReactiveVar();
	this.csvData = ReactiveVar();
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	const queueId = FlowRouter.getQueryParam('queueId');

	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', {queueId})
		.then((response) => {
			this.savedMode.set(true);
			const reportArgs = response.args;
			let header;
			Meteor.callAsync('retrieveOrgNames', {orgs: reportArgs.orgIds})
			.then((resp) => {
				header = 'Orgs: ' + resp + '<br>';
			})
			.catch((e) => {
				header = 'Orgs: ' + undefined + '<br>';
			})
			.finally(()=>{
				header += 'Start Date: ' + reportArgs.startDate + '<br>';
				header += 'End Date: ' + reportArgs.endDate + '<br>';
				if (reportArgs.stripeLegacy) {
					header += 'Stripe Legacy: ' + reportArgs.stripeLegacy + '<br>';
				}
				this.savedHeader.set(header);
			});
			this.payoutsData.set(response.data);
		});
	}

});

Template.billingReportPayouts.onRendered( function () {
	$(".input-group.date").datepicker({
		zIndexOffset:9999
	});
});
Template.billingReportPayouts.events({
	"click #chkQueue": function (e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
	"click #btnUpdate": async function (e, i) {
		$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Updating').prop("disabled", true);
		options = {};
		var self = Template.instance();
		options.startDate = $("input[name=start_date]").val();
		options.endDate = $("input[name=end_date]").val();
		options.stripeLegacy = $("#chkStripeLegacy").prop("checked");
		options.orgIds = $("#reportOrgs").val();

		if (i.queueMode.get()) {
			$("#btnUpdate").text("Update").prop('disabled', false);
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'billingPayoutsReport',
				reportName: 'Payouts Report',
				reportArgs: options,
				userId: Meteor.userId(),
				reportRoute: 'billing/admin/reports/payouts'
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "billingPayoutsReport"
			});
			reportQueuedSwal();
			return;
		}


		Meteor.callAsync("billingPayoutsReport", options)
		.then((result) => {
			$(e.target).html('Update').prop("disabled", false);
			self.payoutsData.set(result);
		})
		.catch((error) => {
			$(e.target).html('Update').prop("disabled", false);
			mpSwal.fire("Error", error.reason, "error");
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "billingPayouts"
		});
	},
	"click .btnViewDetail": function (e, i) {
		const destination = $(e.currentTarget).attr("href");
		const targetOrgId = $(e.currentTarget).data("org-id");
		e.preventDefault();

		if (Orgs.current()._id !== targetOrgId) {
			Meteor.callAsync('adminSwitchOrg', targetOrgId)
			.then((result) => {
				location.replace(destination);
			})
			.catch((error) => {
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			window.open(destination);
		}
	},
	"click #btnExport": async function (e) {
		var outputFile = 'export.csv'
		if (Orgs.current().hasCustomization(AvailableCustomizations.QUICKBOOKS_REVENUE_EXPORT)) {
			$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Exporting').prop("disabled", true);
			let customizedTable = $('<table>');

			let headers = ['Organization', 'Name', 'Season', 'Program', 'Program Type', 'Schedule Name', 'Account Email', 'Transaction Date', 'Payer Email', 'Confirmation', 'Transaction Type', 'Transaction Category', 'Transaction ID', 'Payment Method', 'Payment Handler', 'Memo', 'Payment', 'Deposit', 'List Price', 'Discount', 'Discount Name', 'Discount', 'Discount Name', 'Discount', 'Discount Name', 'Fees', 'Fee Name', 'Start Date', 'End Date', 'Program Start', 'Program End'];
			let headerRow = $('<tr>');
			headers.forEach(function (header) {
				let th = $('<th>').text(header);
				headerRow.append(th);
			});
			customizedTable.append(headerRow);

			const payouts = Template.instance().payoutsData.get();
			const payoutsData = payouts.data;
			const self = Template.instance();
			const payoutDetails = [];
			payoutsData.forEach((payout) => {
				const options = {
					orgId: payout.orgId,
					payoutId: payout.id,
					payoutReference: payout.ref
				}
				Meteor.callAsync('billingPayoutDetailReport', options)
				.then((result) => {
					payoutDetails.push(result.data);
					if (payoutDetails.length === payoutsData.length) {
						self.payoutsDetail.set(payoutDetails);
						const payoutsDetail = self.payoutsDetail.get();
						Meteor.callAsync('quickBooksPayoutsReport', payoutsDetail)
						.then(async (result) => {
							self.csvData.set(result);
							const data = self.csvData.get();
							data.forEach(function (rowData) {
								let row = $('<tr>');
								rowData.forEach(function (cellData) {
									let td = $('<td>').text(cellData);
									row.append(td);
								});
								customizedTable.append(row);
							});
							$(e.target).html('Export CSV').prop("disabled", false);
							exportTableToCSV.apply(this, [customizedTable, 'quickBooksExport.csv']);
							await Meteor.callAsync('trackClientActivity', {
								label: "report-exported",
								reportType: "billingPayouts"
							});
						})
						.catch((error)=> {
							console.log(error, 'error')
						});
					}
				})
				.catch((error)=>{
					console.log(error, 'error')
				});
			})
		} else {
			exportTableToCSV.apply(this, [$('#dvData'), outputFile]);
			await Meteor.callAsync('trackClientActivity', {
				label: "report-exported",
				reportType: "billingPayouts"
			});	
		}
	}
});

Template.billingReportPayouts.helpers({
	todayDate() {
		return new moment().format("MM/DD/YYYY");
	},
	transactions() {
		const payoutsData = Template.instance().payoutsData.get();
		if (payoutsData) return payoutsData.data;
	},
	totalAmount() {
		const payoutsData = Template.instance().payoutsData.get();
		if (payoutsData) return payoutsData.totalAmount;
	},
	payoutsEnabled() {
		return Orgs.current() && (_.deep(Orgs.current(), "billing.stripeAccountId") || _.deep(Orgs.current(), "billing.adyenInfo"));
	},
	orgName() {
		return Orgs.current() && Orgs.current().name;
	},
	stripeLegacyEnabled() {
		return Orgs.current() && (_.deep(Orgs.current(), "billing.stripeAccountIdLegacy"));
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
	getViewDetailLink(payoutId, payoutRef, type) {
		let url = `/billing/admin/reports/payoutDetail?payoutId=${payoutId}&refId=${payoutRef}`;

		if (type) {
			url += `&type=${type}`;
		}

		url += '#reports';

		return url;
	}
});
