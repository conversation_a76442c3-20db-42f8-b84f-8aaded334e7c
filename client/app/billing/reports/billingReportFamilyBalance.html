<template name="billingReportFamilyBalance">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Family Balance Report
				<span class="text-muted pt-2 font-size-sm d-block">View transactions and current balances for families based on a date range.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if familyBalances}}
					<div data-cy="fb-export-csv-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>
				{{/if}}
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="family-balance-update-btn">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="row">
				<div class="col-md-3">

						<div class="form-group" style="margin-left:10px">
							<label class="control-label">Date Start:</label><br/>
							<div class="input-group date">
								<input type="text" class="form-control" name="start_date" value="{{defaultDates 'start'}}" data-cy="family-balance-start-date-inp">
								<div class="input-group-addon">
									<span class="glyphicon glyphicon-th"></span>
								</div>
							</div>
						</div>

				</div>
				<div class="col-md-3">

						<div class="form-group" style="margin-left:10px">
							<label class="control-label">Date End:</label><br/>
							<div class="input-group date">
								<input type="text" class="form-control" name="end_date" value="{{defaultDates 'end'}}">
								<div class="input-group-addon">
									<span class="glyphicon glyphicon-th"></span>
								</div>
							</div>
						</div>

				</div>
				<div class="col-md-6">


							<label class="control-label">Family:</label><br/>
							<select name="familyId" class="form-control">
								<option value=""></option>
								{{#each familyMember in familyMembers}}
								<option value="{{familyMember.id}}">{{familyMember.name}}</option>
								{{/each}}
							</select>


				</div>
			</div>
			<div class="row">
				<div class="col-md-3">
					<div class="form-group" style="margin-left:10px">
						<label class="control-label">Sort:</label><br/>
						<select name="sort" class="form-control">
							<option value="">Name (Last, First)</option>
							<option value="endingBalance">Total Balance (desc)</option>
						</select>
					</div>
				</div>
				<div class="col-sm-3" style="margin-left:10px">
					<div class="form-group">
						<label class="control-label">Balance Type:</label><br/>
						<select name="balance_type" class="form-control" id="balance_type">
							<option value="">All Balances</option>
							<option value="pastDue">Past Due Balances</option>
							<option value="prepaid">Prepaid Balances</option>
						</select>
					</div>
				</div>
			</div>

			<br/>
			{{#if familyBalances}}
			<div class="container d-flex flex-row justify-content-end mb-4">
				<div class="d-flex flex-row ml-6 justify-content-center align-items-center">
				  <span class="page-item-set" style="cursor:pointer;" data-action="previous" data-cy="family-balance-pagination-prev-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
				  <span class="font-size-h2" data-cy="fb-pagination-records-count">{{pageStart}}-{{pageEnd}} of {{pageTotal}}</span>
				  <span class="page-item-set" style="cursor:pointer;" data-action="next" data-cy="family-balance-pagination-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">

					<h3 class='text-center'>Family Balance Report</h3>
					<h4 class='text-center'>Org: {{orgName}}</h4>

					<table class="table " id="dvData">
						<tbody>
							<tr>
								<th>Date/Time</th>
								<th>Name</th>
								<th>Description</th>
								<th style="text-align:right">Original Amount</th>
								<th style="text-align:right">Paid Amount</th>
								<th style="text-align:right">Other Credits</th>
								<th style="text-align:right">Open Amount</th>
								<th style="text-align:right">Balance</th>
							</tr>
							{{#each family in familyBalances}}
								<tr data-id="{{family.familyPersonId}}" class="fb-expandable {{#unless allCollapsed}}expanded{{/unless}}">
									<th colspan=7>
										{{#if allCollapsed}}
										<a href="#" class="fb-expander" data-id="{{family.familyPersonId}}">
											<i class="fa fa-chevron-right"></i><i class="fa fa-chevron-down"></i></a>{{/if}} {{family.familyPersonName}}
											{{#if family.familyPersonInActive}}<span style="color:#ff0000"> (i)</span>{{/if}}
									</th>
									<th style="text-align:right" class="fb-collapsed-balance"><span>{{formatCurrency family.endingBalance}}</span></th>
								</tr>
								{{#each family.entries}}
								<tr class="fb-collapsable {{#if allCollapsed}}collapsed{{/if}}" data-id="{{family.familyPersonId}}">
									<td>{{invoiceDate}}</td>
									<td><a href="/people/{{personId}}#billing" target="_blank">{{personName}}</a> {{#if personInActive}}<span style="color:#ff0000">(i)</span>{{/if}}</td>
									<td>Invoice <a href="/billing/invoices/{{_id}}" target="_blank">{{invoiceNumber}}</a> (<a href="#" class="btnViewLedger">view ledger</a>)</td>
									<td style="text-align:right">{{formatCurrency originalAmount}}</td>
									<td style="text-align:right">{{formatCurrency familyMemberPaidAmount}}</td>
									<td style="text-align:right">{{formatCurrency otherCreditsAmount}}</td>
									<td style="text-align:right">{{formatCurrency familyMemberOpenAmount}}</td>
									<td style="text-align:right">{{formatCurrency runningTotal}}</td>
								</tr>
								{{/each}}

								{{#each family.openCreditMemos}}
								<tr class="fb-collapsable {{#if allCollapsed}}collapsed{{/if}}" data-id="{{family.familyPersonId}}">
									<td>{{formatDate createdAt "M/DD/YYYY"}}</td>
									<td></td>
									<td>Credit Memo {{creditMemoDescription}}</td>
									<td style="text-align:right">{{formatCurrency originalAmount}}</td>
									<td style="text-align:right"></td>
									<td style="text-align:right"></td>
									<td style="text-align:right">{{formatCurrency openAmount}}</td>
									<td style="text-align:right">{{formatCurrency runningTotal}}</td>
								</tr>
								{{/each}}

								{{#if family.entries}}
								<tr style="background-color:#fafafa" class="fb-collapsable {{#if allCollapsed}}collapsed{{/if}}" data-id="{{family.familyPersonId}}">
									<td colspan=7>Ending Balance - {{family.familyPersonName}}</td>
									<td style="text-align:right">{{formatCurrency family.endingBalance}}</td>

								</tr>
								{{/if}}
							{{/each}}
						</tbody>
					</table>
				</div>
			</div>
			{{/if}}
		</div>
	</div>

</template>
