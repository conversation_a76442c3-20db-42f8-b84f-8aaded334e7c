<template name="billingAdminConfiguration">
	<div class="container">
		<div class="card card-custom">
			<div class="card-header card-header-tabs-line">
				<div class="card-toolbar">
					<ul class="nav nav-tabs nav-tabs-space-lg nav-tabs-line nav-tabs-bold nav-tabs-line-3x" role="tablist">
						<li class="nav-item mr-3">
							<a data-cy="plans-items-tab" class="nav-link active" data-toggle="tab" href="#tab_plans">
								<span class="nav-icon"><i class="fad fa-usd-square mr-2"></i></span>
								<span class="nav-text font-weight-bold">Plans And Items</span>
							</a>
						</li>
						{{# if canSeeCouponCodes}}
						<li class="nav-item mr-3">
							<a data-cy="coupon-tab" class="nav-link" data-toggle="tab" href="#tab_coupon_codes">
								<span class="nav-icon fa-stack mr-3" style="font-size:0.75em;">
									<i class="fad fa-money-check-alt fa-stack-2x"></i>
									<i class="fad fa-pen fa-stack-1x ml-3 mt-1"></i>
								</span>
								<span class="nav-text font-weight-bold">Coupon Codes</span>
							</a>
						</li>
						{{/if}}
						<li class="nav-item mr-3">
							<a data-cy="settings-tab" class="nav-link" data-toggle="tab" href="#tab_settings">
								<span class="nav-icon"><i class="fad fa-cog mr-2"></i></span>
								<span class="nav-text font-weight-bold">Settings</span>
							</a>
						</li>
						
					</ul>
				</div>
			</div>
			<div class="card-body">
				<div class="tab-content pt-5">
					<div class="tab-pane active" id="tab_plans" role="tabpanel">
						{{> billingAdminPlans}}
					</div>
					{{# if canSeeCouponCodes}}
					<div class="tab-pane" id="tab_coupon_codes" role="tabpanel">
						{{> billingAdminCouponCodes}}
					</div>
					{{/if}}
					<div class="tab-pane" id="tab_settings" role="tabpanel">
						{{> billingAdminSettings}}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
