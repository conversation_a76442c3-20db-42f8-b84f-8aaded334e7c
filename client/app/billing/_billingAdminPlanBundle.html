<template name="billingAdminPlanBundle">
    {{# each number in (numbersBetween 0 1) }}
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Plan {{ incremented number }}</label>
            <div class="col-lg-6 col-md-9 col-sm-12">
                <select data-cy="bundle-plan-{{number}}" class="form-control bundle-plan" name="plan_{{ number }}">
                    <option value=""></option>
                    {{# each plan in getPlans number }}
                        <option value="{{ plan._id }}" {{selectedIfEqual (getPlanIdAtIndex number) plan._id}}>{{ plan.description }}</option>
                    {{/ each }}
                </select>
            </div>
        </div>
    {{/ each }}
    {{# if showPricing }}
        <div class="row mb-3">
            <div class="col text-center">Bundled Amounts</div>
        </div>
        <div class="row mb-3">
            <div class="col-9 offset-2 text-center">
                <div class="row">
                    <div class="col-10 offset-2">{{ getPlanDescription 1 }}</div>
                </div>
            </div>
        </div>
        {{# with getMaxValues }}
            <div class="row mb-4">
                <div class="col-9 offset-2">
                    <div class="dynamic-grid" style="grid-template-columns: repeat({{ maxColumns }}, 1fr);">
                        <div class="dynamic-grid-label"></div>
                        {{# each columnIndex in (numbersBetween 0 maxDays) }}
                            <div class="d-flex justify-content-center">
                                {{ incremented columnIndex }} Day{{# if columnIndex }}s{{/ if }}/Wk
                            </div>
                        {{/ each }}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-2 pb-7 d-flex align-items-center justify-content-end">
                    {{ getPlanDescription 0 }}
                </div>
                <div class="col-9">
                    {{# each rowIndex in (numbersBetween 0 maxDays) }}
                        <div class="dynamic-grid form-group" style="grid-template-columns: repeat({{ maxColumns }}, 1fr);">
                            <div class="dynamic-grid-label d-flex align-items-center justify-content-end">
                                {{ incremented rowIndex }} Day{{# if rowIndex }}s{{/ if }}/Wk
                            </div>
                            {{# each columnIndex in (numbersBetween 0 maxDays) }}
                                <div class="input-wrapper">
                                    {{# if (or (lt (incremented rowIndex) plan1Min) (gt (incremented rowIndex) plan1Max) (lt (incremented columnIndex) plan2Min) (gt (incremented columnIndex) plan2Max)) }}
                                        <input data-cy="scaled-amount-{{rowIndex}}-{{columnIndex}}"
                                               type="text"
                                               class="form-control"
                                               name="scaledAmount_{{ rowIndex }}_{{ columnIndex }}"
                                               value="N/A"
                                               disabled
                                        >
                                    {{ else }}
                                        <input data-cy="scaled-amount-{{rowIndex}}-{{columnIndex}}"
                                               type="text"
                                               class="form-control"
                                               name="scaledAmount_{{ rowIndex }}_{{ columnIndex }}"
                                               pattern="[0-9]+([\.][0-9]+)?"
                                               value="{{ formatNumber (getScaledAmount rowIndex columnIndex) '0.00' }}"
                                               required
                                        >
                                    {{/ if }}
                                </div>
                            {{/ each }}
                        </div>
                    {{/ each }}
                </div>
            </div>
        {{/ with }}
    {{/ if }}
</template>