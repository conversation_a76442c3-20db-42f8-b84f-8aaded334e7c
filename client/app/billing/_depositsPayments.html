<template name="depositsPayments">
	<div class="card-body">
		<div class="box box-solid content-box descriptive-content">
			<div class="box-body">
				<div class="row">
					<div class="col-12">
						<b>Filter Undeposited Payments</b>
					</div>
				</div>
				<div class="d-flex flex-row">
					<div class="pr-5 w-25">
						<div class="form-group">
							<label>Date Range</label>
							<input type="text" name="invoices-date-range" class="form-control" data-cy="deposits-date-range">
						</div>
					</div>
					<div class="pr-5">
						<label>Payment Types</label><br/>
						<select name="payment-types" multiple>
							<option value="ach">ACH</option>
							<option value="cash">Cash</option>
							<option value="check">Check</option>
							<option value="card">Credit Card</option>
						</select>
					</div>
					<div class="pr-5">
						<label>&nbsp;</label><br/>
						<button class="btn btn-secondary" id="update-filters" data-cy="mbd-report-update-btn">Update</button>
					</div>
				</div>
				<br/>
				{{#if depositInfo}}
                    {{#if canEditDeposits}}
				        <b>Editing Existing Deposit</b><br/>
                    {{else}}
                        <b>Viewing Existing Deposit</b><br/>
                    {{/if}}
				Deposit Date: {{depositInfo.depositDate}}<br/>
				Original Amount: {{formatCurrency depositInfo.depositTotal}}<br/>
				Memo: {{depositInfo.memo}}<br/>
				<br/>
				{{/if}}
				<table class="table table-striped billing-table" data-cy="deposite-manage-report-table">
					<tr>
                        <th>
                            <label class="checkbox">
                                <input type="checkbox" class="select-all" data-cy="select-all-deposits"> <span></span>
                            </label>
                        </th>
						<th>Date</th>
						<th>Person</th>
						<th>Paid By</th>
						<th>Invoice</th>
						<th>Method</th>
						<th style="text-align:right">Amount</th>
						<th>Note</th>
					</tr>
					{{#each deposits}}
					<tr>
                    <td><label class="checkbox"><input data-cy="deposit-checkbox" type="checkbox" class="select-item" data-invoice="{{invoiceId}}" data-item="{{creditIndex}}" checked={{checkedIfExists depositId}}><span></span></label></td>
						<td data-cy="deposits-payment-table-date">{{formatDate creditDate "M/DD/YYYY"}}</td>
						<td>
                            {{# if fundId }}
                                {{ personName }}
                            {{ else }}
                                <a href="/people/{{personId}}#billing" target="_blank" data-cy="child-name">{{personName}}</a>
                            {{/ if }}
                        </td>
						<td>
							{{# if payerId }}
								<a href="/people/{{payerId}}#billing" target="_blank" data-cy="payer-by-name">{{paidByName}}</a>
							{{ else }}
								{{ paidByName }}
							{{/ if }}
						</td>
<!--						<td>{{paidByName}}</td>-->
						<td>{{#if invoiceNumber}}<a href="/billing/invoices/{{invoiceId}}" target="_blank">{{invoiceNumber}}</a>{{/if}}</td>
						<td data-cy="payment-method-types">{{paymentMethod}}</td>
						<td style="text-align:right" data-cy="deposits-report-amount">{{formatCurrency creditAmount}}</td>
						<td>{{note}}</td>
					</tr>
					{{/each}}
				</table>
			</div>
		</div>
	</div>
	
	<div class="card-footer deposit-actions d-flex flex-row align-items-center">
		{{#if selectedItems}}
            <div class="h5" data-cy="deposits-total-cell">
                {{selectedItems.count}} item(s) totaling {{selectedItems.amount}} selected.
            </div>
            {{#if canEditDeposits}}
                <div class="ml-auto">
                    <button data-cy="update-create-deposit-btn" class="btn btn-primary" id="btn-upsert-deposit">{{#if depositInfo}}Update{{else}}Create{{/if}} Deposit</button>
                </div>
            {{else}}
                {{#unless depositInfo}}
                <div class="ml-auto">
                    <button data-cy="create-deposit-btn" class="btn btn-primary" id="btn-upsert-deposit">Create Deposit</button>
                </div>
                {{/unless}}
            {{/if}}
		{{else}}
			<div class="col-12 text-center">
				Select items above to group into a deposit batch
			</div>
		{{/if}}
	</div>

</template>

<template name="billingUpsertDeposit">
	<form id="frmCredit">
		<div class="row">
			<div class="col-12 h5">
				{{itemCount}} item(s) selected totaling {{itemAmount}}<br/>
			</div>
		</div>
		<!--<div class="row">
			<div class="col-12 form-group">
				<label>Deposit Date</label>
				<input type="date" class="form-control" name="depositDate" value="{{depositDate}}" required/>
			</div>
		</div>-->
		<div class="row">
			<div class="col-12 form-group">
				<label>Memo</label>
				<textarea class="form-control" name="memo" >{{memo}}</textarea>
			</div>
		</div>
	</form>
</template>