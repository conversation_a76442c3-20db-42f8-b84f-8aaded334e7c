<template name="refunds">
	<div class="container">
		<div class="card card-custom">
			<div class="card-header flex-wrap border-0 pt-6 pb-0">
				<div class="card-title">
					<h3 class="card-label">View Refunds
					<span class="text-muted pt-2 font-size-sm d-block">Find refunds and view activity.</span></h3>
				</div>
				<div class="card-toolbar">
					<!--<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>-->
					<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
						Update
					</div>
				</div>
			</div>
			<div class="card-body">
				<div class="box box-solid content-box descriptive-content">
					<div class="box-body">
						
						<div class="row">
							<div class="col-md-3">
								<div class="form-group">
									<label>Start Date</label>
									<div class="input-group">
										
										<input type="text" class="form-control pull-right" id="refundsStartDate" value="{{formattedStartDate}}">
									</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="form-group">
									<label>End Date</label>
									<div class="input-group">
										
										<input type="text" class="form-control pull-right" id="refundsEndDate" value="{{formattedEndDate}}">
									</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="form-group">
									<label>Type</label>
									<div class="input-group">
										<select name="type" class="form-control">
											<option value=""></option>
											<option value="manualCreditMemo">Credit Memos</option>
											<option value="onlinePayment">Payments</option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="form-group">
									<label>Org(s):</label>
									<div class="input-group">
										{{> reportOrgsField }}
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-3 form-group">
								<div class="checkbox-inline">
									<label class="checkbox"><input type="checkbox" id="showPendingOnly"><span></span> Show pending only</label>
								</div>
							</div>
						</div>
						<br/>
						<table class="table table-striped billing-table">
							<tr>
								<th>Type</th>
								<th >Date</th>
								<th>Person</th>
								<th>Invoice</th>
								
								<th style="text-align:right">Amount</th>
								<th>Status</th>
								{{#if showOrg}}
								<th>Org</th>
								{{/if}}
							</tr>
							{{#each refunds}}
							<tr>
								<td>{{typeLabel}}</td>
								<td>{{formatDate refundDate "M/DD/YYYY"}}</td>
								<td><a href="/people/{{personId}}#billing" target="_blank">{{personName}}</a></td>
								<td><a href="/billing/invoices/{{invoiceId}}" target="_blank">{{invoiceNumber}}</a></td>
								
								<td style="text-align:right">{{formatCurrency refundAmount}}</td>
								<td>{{status}}</td>
								{{#if showOrg}}
								<td>{{orgName}}</td>
								{{/if}}
							</tr>
							{{/each}}
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>