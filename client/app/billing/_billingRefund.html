<template name="billingRefund">
	<form id="frmRefund">
		<div class="row">
			<div class="col-sm-12">
				<label>Invoice Number: </label>
				{{invoice.invoiceNumber}}<br/>
				<label>Original Amount: </label>
				{{formatCurrency invoice.originalAmount}}<br/>
				<label>Open Amount: </label>
				{{formatCurrency invoice.openAmount}}<br/>
				<label>Total Refundable Amount:</label>
				{{formatCurrency invoice.getRefundableAmount}}
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Refundable Charges</label>
				<select class="form-control" name="charge_id" required>
					{{#each charges}}
					<option value="{{id}}">{{desc}}</option>
					{{/each}}
				</select>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Refund Amount</label>
				<input type="text" class="form-control" name="refund_amount" required pattern="[0-9]+([\.][0-9]+)?"/>
			</div>
		</div>
		{{#if showPunchCardCheckBox invoice}}
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>This refund includes a punch card refund</label>
				<label class="checkbox checkbox-primary">
					<input type="checkbox" class="form-check-input" name="refundPunchCard" id="refundPunchCard">
					<span class="ml-2"></span>
				</label>
			</div>
		</div>
		{{/if}}
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Refund Reason</label>
				<select class="form-control" name="refund_reason" required>
					<option value=""></option>
					<option value="billing_error">Billing Error</option>
					<option value="service_issue">Service Issue</option>
					<option value="other">Other</option>
				</select>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12 form-group">
				<label>Note</label>
				<input type="text" class="form-control" name="refund_note" />
			</div>
		</div>
	</form>
</template>