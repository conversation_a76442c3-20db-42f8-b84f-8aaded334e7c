<template name="queuedPayments">
	<div class="container">
		<div class="card card-custom">
			<div class="card-header flex-wrap border-0 pt-6 pb-0">
				<div class="card-title">
					<h3 class="card-label">Manage Batch Payments
					<span class="text-muted pt-2 font-size-sm d-block">Manage batch payments and create payment files.</span></h3>
					
				</div>
				<div class="card-toolbar">
					<!--<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>-->
					
				</div>
				
			</div>
			<div class="card-body">
				<div class="box box-solid content-box descriptive-content">
					<div class="box-body">
						{{#if batchData}}
							<div class="row">
								<div class="col-12">
									<b>Available Transactions</b>
								</div>
							</div>
							
							<p>There are currently {{batchData.transactionCount}} transactions totaling {{formatCurrency batchData.transactionTotal}} in this org queued for payment file generation.</p>
							<button class="btn btn-primary" id="btnGenerateFile">Generate File</button>
							<hr />
							<div class="row">
								<div class="col-12">
									<b>Recent Generated Files</b>
								</div>
							</div>
							
							<table class="table table-striped billing-table">
								<tr>
									<th>Date</th>
									<th style="text-align:right">Amount</th>
									<th style="text-align:right">Count</th>
									<th>Name</th>
								</tr>
								{{#each batchData.recentBatches}}
								<tr>
									<td>{{formatDate createdAt "MM/DD/YYYY"}}</td>
									<td style="text-align:right">{{#if sumTotalInCents}}{{formatCurrency (sumTotalInDollars sumTotalInCents)}}{{/if}}</td>
									<td style="text-align:right">{{itemCount}}</td>
									<td>
										{{fileKey}}
									</td>
								</tr>
								{{/each}}
							</table>
						{{else}}
							{{> loading}}
						{{/if}}
					</div>

				</div>
			</div>
		</div>
	</div>
</template>

<template name="queuedPaymentsGenerateFileModal">
	<form id="formGenerateFile">
		
		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Company Routing</label>
			<div class="col-lg-9 col-xl-6">
				<input type="text" class="form-control form-control-lg" name="company-routing" value="{{companyRouting}}">
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Company Account</label>
			<div class="col-lg-9 col-xl-6">
				<input type="text" class="form-control form-control-lg" name="company-account" value="{{companyAccountNumber}}">
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Account Name</label>
			<div class="col-lg-9 col-xl-6">
				<input type="text" class="form-control form-control-lg" name="account-name" value="{{companyBankName}}">
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Company Identifier</label>
			<div class="col-lg-9 col-xl-6">
				<input type="text" class="form-control form-control-lg" name="company-identifier" value="{{companyIdentifier}}">
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Company Name</label>
			<div class="col-lg-9 col-xl-6">
				<input type="text" class="form-control form-control-lg" name="company-name" value="{{companyName}}">
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Save Above Data</label>
			<div class="col-lg-9 col-xl-6">
				<div class="checkbox-list">
					<label class="checkbox checkbox-primary">
						<input type="checkbox" class="form-check-input" name="save-data" >
						<span></span>
						Store this data to be pre-populated for next time
					</label>
				</div>
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Payment Date</label>
			<div class="col-lg-9 col-xl-6">
				<input type="date" class="form-control form-control-lg" name="payment-date">
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Org(s):</label><br/>
			<div class="col-lg-9 col-xl-6">
				{{> reportOrgsField }}
			</div>
		</div>

		<div class="form-group row">
			<label class="col-xl-3 col-lg-3 text-right col-form-label">Login Code</label>
			<div class="col-lg-6 col-xl-3">
				<input type="text" class="form-control form-control-lg" name="auth-code" >
				
			</div>
			<div class="col-lg-3 col-xl-3">
				<button class="btn btn-secondary" id="btn-send-auth-code">Send Email</button>
				{{emailStatus}}
			</div>
		</div>

    </form>
	
</template>
