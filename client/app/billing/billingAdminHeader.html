<template name="billingAdminHeader">
  <ul class="menu-nav">
    {{#if showOverview}}
    <li class="menu-item {{isActivePath regex='\\/billing\\/admin$|overview$' class='menu-item-active' }}" aria-haspopup="true">
      <a href="/billing/admin?tab=overview" class="menu-link">
        <span class="menu-text">Overview</span>
      </a>
    </li>
    <li class="menu-item {{isActivePath path='/billing/admin/reports' class='menu-item-active' }}" aria-haspopup="true">
      <a href="/billing/admin/reports" class="menu-link">
        <span class="menu-text">Reports</span>
      </a>
    </li>
    {{/if}}
    <li class="menu-item {{isActivePath path='/billing/admin/configuration' class='menu-item-active' }}" aria-haspopup="true">
      <a href="/billing/admin/configuration" class="menu-link">
        <span class="menu-text" data-cy="billing-admin-configuration-button">Configuration</span>
      </a>
    </li>
  </ul>
</template>
