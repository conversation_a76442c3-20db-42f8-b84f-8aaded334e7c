import { Orgs } from '../../../lib/collections/orgs';
import './_dropInFeeBuffer.html';

Template._dropInFeeBuffer.onCreated(function() {
})

Template._dropInFeeBuffer.helpers({
    "prefillValue"() {
       return Orgs.current().billing?.scheduling?.dropInBuffer || ''
    }
})

Template._dropInFeeBuffer.events({
    'input #lateFeeInput': function (event, template) {
        const lateFeeInput = template.find('#lateFeeInput');
        let currentValue = lateFeeInput.value !== '' ? parseInt(lateFeeInput.value, 10) : 0;
        const maxBufferMinutes = 720;

        // Check if the value is below the minimum
        if (currentValue < 0) {
            currentValue = 0;
        }

        // Check if the value is above the maximum
        if (currentValue > maxBufferMinutes) {
            currentValue = maxBufferMinutes;
        }

        // Update the input value
        lateFeeInput.value = currentValue;
    },

});