<template name="billingAdminReports">
	<div class="container">
		{{#if displayReportName}}
			{{> Template.dynamic template=displayReportName}}
			
		{{else}}
		<!--being::Row (for searching)-->
		{{#if searchIsEnabled}}
		<div class="row mb-8">
			<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6 offset-xl-4 offset-lg-3 offset-md-3 offset-sm-3">
				<input type="text" class="form-control" placeholder="Search for a report..." id="searchReports">
			</div>
		</div>
		{{/if}}
		<!--end::Row-->
		<!--begin::Row-->
		<div class="row">
			<!--begin::Col-->
			{{#each report in availableReports}}
			<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
				<!--begin::Card-->
				<div class="card card-custom gutter-b card-stretch">
					<!--begin::Body-->
					<div class="d-flex flex-column card-body pt-4 justify-content-between">
						<!--begin::User-->
						<div class="d-flex align-items-center mb-7">
							<!--begin::Pic-->
							<div class="flex-shrink-0 mr-4">
								
								<i class="fa fa-chart-bar fa-lg"></i>
							</div>
							<!--end::Pic-->
							<!--begin::Title-->
							<div class="d-flex flex-column">
								<a href="/billing/admin/reports/{{report.name}}" class="text-dark font-weight-bold text-hover-primary font-size-h4 mb-0">{{report.title}}</a>
								<span class="text-muted font-weight-bold">{{report.subtitle}}</span>
							</div>
							<!--end::Title-->
						</div>
						<!--end::User-->
						<!--begin::Desc-->
						<p class="mb-7">{{report.description}}</p>
						<!--end::Desc-->
						<!--begin::Info
						<div class="mb-7">
							<div class="d-flex justify-content-between align-items-center">
								<span class="text-dark-75 font-weight-bolder mr-2">Budget:</span>
								<a href="#" class="text-muted text-hover-primary">$249,500</a>
							</div>
							<div class="d-flex justify-content-between align-items-cente my-1">
								<span class="text-dark-75 font-weight-bolder mr-2">Expences:</span>
								<a href="#" class="text-muted text-hover-primary">$76,810</a>
							</div>
							<div class="d-flex justify-content-between align-items-center">
								<span class="text-dark-75 font-weight-bolder mr-2">Due Date:</span>
								<span class="text-muted font-weight-bold">21.05.2016</span>
							</div>
						</div>
						end::Info-->
						<a href="/billing/admin/reports/{{report.name}}" class="btn btn-block btn-sm btn-light-primary font-weight-bolder text-uppercase py-4" data-cy="report-button-{{report.name}}">View {{report.title}} Report</a>
					</div>
					<!--end::Body-->
				</div>
				<!--end:: Card-->
			</div>
			{{/each}}
			<!--end::Col-->
		</div>
		{{/if}}
	</div>
</template>
