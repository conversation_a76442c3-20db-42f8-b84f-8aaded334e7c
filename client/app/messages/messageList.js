import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { Relationships } from '../../../lib/collections/relationships';
import _ from '../../../lib/util/underscore';
import './messageList.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../lib/util/log';
import moment from 'moment';

const DISPLAY_MAX = 20;
const DEBOUNCE_TIME = 400;

Template.messageList.helpers({
	threads() {
		return Template.instance().messageThreads.get();
	},
	messageView() {
		return Session.get("messageTargetFolder");
	},
	activeSearch() {
		return (Template.instance().searchTerm.get() || "").length > 0;
	},
	showAdministrativeVisibility() {
		return (Orgs.current() && Orgs.current().hasCustomization("messages/administrativeVisibility/enabled")
			&& Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type=="admin");
	},
	indexPosition() {
		const pos = Template.instance().indexPosition.get();
		const totalCount = Template.instance().totalMessages.get();
		return (totalCount > 0) ? pos + 1 : 0;
	},
	lastDisplayPosition() {
		const pos = Template.instance().indexPosition.get();
		const totalCount = Template.instance().totalMessages.get();
		if (totalCount == 0) return 0;
		if (totalCount < pos + DISPLAY_MAX) {
			return totalCount;
		} else {
			const threads = Template.instance().messageThreads.get();
			return pos + threads.length;
		}
	},
	threadCount() {
		return Template.instance().totalMessages.get();
	},
	isListLoading() {
		return Template.instance().isLoading.get();
	},
	getLoadingClass() {
		return 'loading-overlay-fixed-centered';
	}
});
Template.messageList.events({
	"click #btnSend": (e,i) => {
		e.preventDefault();
		Meteor.callAsync("insertMessage", {
			recipients: $("#recipients").val(),
			subject: $("input[name='subject']").val(),
			message: $("textarea[name='message']").val()
		})
		.then((result)=>{
			mpSwal.fire("Message sent!");
			$("#compose-box").hide();
			$("#btnNewMessage").show();
		})
		.catch((error)=>{
			mpSwal.fire(error.reason);
		});
	},
	"click .thread-box": (e, i) => {
		const threadId = $(e.currentTarget).data("id");
		Router.go("/messages/" + threadId);
	},
	"click #btnNewMessage": (e, i) => {
		e.preventDefault();
		$("#frmNewMessage")[0].reset();
		$("#recipients")[0].selectize.clear();
		$("#compose-box").show();
		$("#btnNewMessage").hide();

	},
	"click #btnCancel": (e,i) => {
		e.preventDefault();
		$("#compose-box").hide();
		$("#btnNewMessage").show();
	},
	"click #btnGroupMessageView": (e,i) => {
		e.preventDefault();
		Template.instance().messageView.set($(e.target).data('type'));
	},
	"input input[name='search-term']": _.debounce((e, i) => {
		const searchTerm = $(e.currentTarget).val();
		i.searchTerm.set(searchTerm);
	}, DEBOUNCE_TIME),
	"click .list-item": (e,i) => {
		if (_.contains(e.target.parentNode.classList, "checkbox")) return;
		const messageId = $(e.currentTarget).data("message-id");
		FlowRouter.go('messages', { id: messageId });
	},
	"click #btn-messagelist-previous": (e,i) => {
		const pos = i.indexPosition.get();
		i.indexPosition.set( pos - DISPLAY_MAX < 0 ? 0 : pos - DISPLAY_MAX);
	},
	"click #btn-messagelist-next": (e,i) => {
		const pos = i.indexPosition.get();
		const totalCount = i.totalMessages.get();
		i.indexPosition.set( pos + DISPLAY_MAX >= totalCount ? pos : pos + DISPLAY_MAX);
	},
	"click #checkbox-select-all": (e,i) => {
		const ct = $(e.currentTarget),
			status = ct.prop("checked");
		$(".message-select-checkbox").prop("checked", status);
	},
	"click .message-select-checkbox":(e,i) => {
		$("#checkbox-select-all").prop("checked", false);
	},
	"click #btn-archive-all": (e,i) => {
		const messageIds = $(".message-select-checkbox:checked").map(function() { return $(this).data("message-id")}).get();
		if (messageIds.length > 0) {
			Meteor.callAsync("archiveMessage", {messageIds})
			.catch((error)=>{
				mpSwal.fire("Error", error.reason, "error")
			})
			.finally(()=>{
				$(".message-select-checkbox").prop("checked", false);
				$("#checkbox-select-all").prop("checked", false);
				getThreads(i, { page: parseInt(i.indexPosition.get() / DISPLAY_MAX), limit: DISPLAY_MAX });
				i.totalMessages.set(i.totalMessages.get() - messageIds.length);
			});
		}
	}
});
Template.messageList.onCreated(function() {
	this.searchTerm = new ReactiveVar("");
	const self = this;
	this.indexPosition = new ReactiveVar(0);
	this.currentFolderType = new ReactiveVar("inbox");
	this.totalMessages = new ReactiveVar(0);
	this.messageThreads = new ReactiveVar([]);
	this.searchDebounce = new ReactiveVar(new moment().subtract(DEBOUNCE_TIME, 'milliseconds'));
	this.isLoading = new ReactiveVar(false);
	let currentPage = parseInt(self.indexPosition.get() / DISPLAY_MAX);
	let initialLoad = true;

	self.autorun( function() {
		var currentFolder = Session.get("messageTargetFolder") || "inbox";
		currentPage = parseInt(self.indexPosition.get() / DISPLAY_MAX);
		const searchTerm = self.searchTerm.get();

		const now = new moment();
		const duration = moment.duration(now.diff(self.searchDebounce.get()));
		if (duration.as('milliseconds') < 300 && !initialLoad) {
			return;
		}

		if (currentFolder !== self.currentFolderType.get()) {
			// since I'm in an auto run and want to reset reactive vars - I'm
			// returning at the end of this since it will rerun and I don't want to double subscribe
			self.currentFolderType.set(currentFolder);
			self.indexPosition.set(0);
			return;
		}

		if (currentFolder === "admin") {
			Meteor.callAsync("getMessageCenterCount", { admin: true, searchTerm: searchTerm })
			.then((res)=>{
				self.totalMessages.set(res.total);
			})
			.catch((err)=>{
				Log.error(err);
			});
		} else if (currentFolder === "sent") {
			Meteor.callAsync("getMessageCenterCount", { searchTerm: searchTerm })
			.then((res)=>{
				self.totalMessages.set(res.personMessages);
			})
			.catch((err)=>{
				Log.error(err);
			});
		} else {
			const options = { searchTerm: searchTerm };
			if (currentFolder === 'inbox') {
				options.excludeArchived = true;
			}
			Meteor.callAsync("getMessageCenterCount", options)
			.then((res)=>{
				self.totalMessages.set(res.total);
			})
			.catch((err)=>{
				Log.error(err);
			});
		}

		getThreads(self, { page: currentPage, limit: DISPLAY_MAX });
		self.searchDebounce.set(new moment());
		initialLoad = false;
	});

	$("#kt_inbox_compose").on('hidden.bs.modal', function () {
		getThreads(self, { page: currentPage, limit: DISPLAY_MAX });
	});
});

function getThreads(instance, pagination) {
	if (!Meteor.user() || !Meteor.user().fetchPerson()) return;
	const currentUser = Meteor.user(), currentPerson = currentUser && currentUser.fetchPerson();
	let query = {
		orgId: Orgs.current()._id
	};

	let qualifiedRecipients = [currentUser.personId];
	qualifiedRecipients = qualifiedRecipients.concat(
		_.map(
		Relationships.find({orgId: currentUser.orgId, personId: currentUser.personId, relationshipType:"family"}).fetch(),
		(r) => { return r.targetId;}
		)
	);

	const messageView = Session.get("messageTargetFolder");
	switch( messageView ) {
		case "inbox":
			query["markedArchived"] = {"$ne": currentPerson._id};
			query["$or"] = [ {personId: currentUser.personId}, {currentRecipients: {"$in": qualifiedRecipients}} ];
			break;
		case "sent":
			query["personId"] = currentPerson._id;
			break;
		case "all":
			query["$or"] = [ {personId: currentUser.personId}, {currentRecipients: {"$in": qualifiedRecipients}} ];
			break;
	}
	const queryOptions = {};
	queryOptions.offset = pagination?.page * pagination?.limit;
	queryOptions.limit = pagination?.limit;

	instance.isLoading.set(true);
	Meteor.callAsync('getMessages', { query, queryOptions, searchTerm: instance.searchTerm.get() })
	.then((res)=>{
		instance.messageThreads.set(res);
		instance.isLoading.set(false);
	})
	.catch((err)=>{
		console.log('err', err);
		instance.isLoading.set(false);
	});
}
