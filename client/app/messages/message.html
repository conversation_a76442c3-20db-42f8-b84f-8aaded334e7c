<template name="message">
	{{#with thread}}
		<!--begin::View-->
		<div class="flex-row-fluid ml-lg-8" id="kt_inbox_view">
			<!--begin::Card-->
			<div class="card card-custom card-stretch">
			<!--begin::Header-->
			<div class="card-header align-items-center flex-wrap justify-content-between py-5 h-auto">
				<!--begin::Left-->
				<div class="d-flex align-items-center my-2">
					<a data-cy="btn-messages-back" href="#" class="btn btn-clean btn-icon btn-sm mr-6" id="btn-messages-back" data-inbox="back">
						<i class="flaticon2-left-arrow-1"></i>
					</a>
					<a data-cy="btn-archive-message" href="#" class="btn btn-default btn-icon btn-sm mr-2" id="btnArchive" data-toggle="tooltip" title="" data-original-title="Archive">
						<i class="fad fa-archive"></i>
					</a>
				</div>
				<!--end::Left-->
				<!--begin::Right
				<div class="d-flex align-items-center justify-content-end text-right my-2">
					<span class="text-muted font-weight-bold mr-4" data-toggle="dropdown">1 - 50 of 235</span>
					<span class="btn btn-default btn-icon btn-sm mr-2" data-toggle="tooltip" title="" data-original-title="Previose message">
						<i class="ki ki-bold-arrow-back icon-sm"></i>
					</span>
					<span class="btn btn-default btn-icon btn-sm" data-toggle="tooltip" title="" data-original-title="Next message">
						<i class="ki ki-bold-arrow-next icon-sm"></i>
					</span>
				</div>
				end::Right-->
			</div>
			<!--end::Header-->
			<!--begin::Body-->
			<div class="card-body p-0">
				<!--begin::Header-->
				<div class="d-flex align-items-center justify-content-between flex-wrap card-spacer-x py-0">
					<!--begin::Title-->
					<div data-cy="message-title-open" class="d-flex align-items-center mr-2 py-2">
                        <div class="font-weight-bold font-size-h3 mr-3">{{ threadTitle }}</div>
                        {{# if isInbox }}
                            <span class="label label-light-primary font-weight-bold label-inline mr-2">inbox</span>
						{{/if}}
						<!--
						<span class="label label-light-danger font-weight-bold label-inline">important</span>
						-->
					</div>
					<!--end::Title-->
					
				<!--end::Header-->
				</div>
				<!--begin::SubTitle-->
				<div data-cy="message-subtitle-open" class="mb-3 card-spacer-x">
					<span class="font-weight-bold text-muted">
                        {{# if recipientIncluded }}
                            with {{ threadRecipients }}
                        {{ else }}
                            {{ fullRecipientDescription }}
                        {{/ if }}
					</span>
				</div>
				<!--end::SubTitle-->
				<!--begin::Messages-->
				<div class="mb-3">
					{{#each threadItems}}
					<div class="cursor-pointer shadow-xs toggle-on" data-inbox="message">
						<!--begin::Message Heading-->
						<div class="d-flex card-spacer-x py-6 flex-column flex-md-row flex-lg-column flex-xxl-row justify-content-between">
							<div class="d-flex align-items-center">
								<span class="symbol symbol-50 mr-4">
									<span class="symbol-label" style="background-image: url('/metronic/theme/html/demo13/dist/assets/media/users/100_13.jpg')"></span>
								</span>
								<div class="d-flex flex-column flex-grow-1 flex-wrap mr-2">
									<div class="d-flex">
										<a data-cy="sender-name" href="#" class="font-size-lg font-weight-bolder text-dark-75 text-hover-primary mr-2">{{senderName}}</a>
										<div class="font-weight-bold text-muted"><span class="label label-success label-dot mr-2"></span></div>
									</div>
									<div class="d-flex flex-column">
										<div class="toggle-off-item">
											<span class="font-weight-bold text-muted"> </span>
										</div>
									</div>
								</div>
							</div>
							
							<div data-cy="message-date" class="d-flex my-2 my-xxl-0 align-items-md-center align-items-lg-start align-items-xxl-center flex-column flex-md-row flex-lg-column flex-xxl-row">
								<div class="font-weight-bold text-muted mx-2">{{formatDate createdAt "MM/DD/YYYY h:mm a"}}</div>
								
							</div>
						</div>
						<!--end::Message Heading-->
						<div data-cy="full-message" class="card-spacer-x py-3 toggle-off-item message-body">
                            <p>{{{ getMessageContent message }}}</p>
						</div>
					</div>
					{{/each}}
				</div>
				<!--end::Messages-->
				<!--begin::Reply-->
				<div class="card-spacer mb-3" id="kt_inbox_reply">
				<div class="card card-custom shadow-sm">
					<div class="card-body p-0">
					<!--begin::Form-->
					<form id="kt_inbox_reply_form">
						<!--begin::Body-->
						<div class="d-block">
						    <!--begin::To-->
                            <div class="d-flex align-items-center border-bottom inbox-to px-8 min-h-50px">
                                <div class="text-dark-50 w-75px">To:</div>
                                <div class="d-flex align-items-center flex-grow-1">
                                    Thread recipient
                                </div>
                                <div class="ml-2">
                                    {{# if showIncludeAllFamilyCheckbox }}
                                        <label class="checkbox checkbox-primary" style="{{ checkboxCursor }}">
                                            <input type="checkbox" id="includeAllFamily" {{ checkedIfEq includeAllFamily true }} disabled="{{ includeAllFamily }}" />
                                            <span></span>
                                            &nbsp;&nbsp;Include all family members&nbsp;&nbsp;
                                        </label>
                                    {{/ if }}
                                </div>
                            </div>
                            <!--end::To-->

                            <!--begin::Message-->
                            <div id="kt_inbox_reply_editor" class="border-0 ql-container ql-snow">
                                <textarea data-cy="reply-message" class="form-control border-0" name="reply-message" placeholder="Type reply here..." rows="3"></textarea>
                            </div>
                            <!--end::Message-->
						
						</div>
						<!--end::Body-->
						<!--begin::Footer-->
						<div class="d-flex align-items-center justify-content-between py-5 pl-8 pr-5 border-top">
						<!--begin::Actions-->
						<div class="d-flex align-items-center mr-3">
							<!--begin::Send-->
							<div class="btn-group mr-4">
								
								<a data-cy="send-reply-btn" href="#" class="btn btn-primary font-weight-bold px-6" id="btnReplySend">Send</a>
								
							
							</div>
							<!--end::Send-->
							
						</div>
						<!--end::Actions-->
						
						</div>
						<!--end::Footer-->
					</form>
					<!--end::Form-->
					</div>
				</div>
				</div>
				<!--end::Reply-->
			</div>
			<!--end::Body-->
			</div>
			<!--end::Card-->
		</div>
		<!--end::View-->
	{{/with}}
</template>
