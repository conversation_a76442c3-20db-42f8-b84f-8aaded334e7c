<template name="_groupConfigurationModal">
  <!-- Modal-->
  <div class="modal fade" id="_groupConfigurationModal" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">{{group.name}} Configuration</h5>
          <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
            <span class="fad-regular fad-primary fad fa-times"></span>
          </div>
        </div>
        <div class="modal-body bg-gray-100">
          <!--begin::Form-->
          <form>
            <div class="card-body">
              <div class="form-group row">
                <label class="col-2 col-form-label">Group Name</label>
                <div class="col-10">
                  <input class="form-control" type="text" value="{{group.name}}" id="group-name-input"/>
                </div>
              </div>
              <div class="form-group row">
                <label for="group-capacity-input" class="col-2 col-form-label">Max Capacity</label>
                <div class="col-10">
                  <input class="form-control" type="text" value="{{group.capacity}}" id="group-capacity-input"/>
                </div>
              </div>
              <div class="form-group row">
                <label data-cy="group-preferred-capacity" for="group-preferred-capacity" class="col-2 col-form-label">Preferred Capacity</label>
                <div class="col-10">
                  <input class="form-control" type="text" value="{{group.preferredCapacity}}" id="group-preferred-capacity"/>
                </div>
              </div>
              
              {{#if showActivitiesAgeGroup}}
                <div class="form-group row">
                  <label for="group-activities-age-group" class="col-2 col-form-label">Activities Age Group</label>
                  <div class="col-10">
                    <select class="form-control" id="group-activities-age-group">
                      {{#each value in availableActivityAgeGroups}}
                        <option value="{{value}}" {{selectedIfEqual group.activitiesAgeGroup value}}>{{value}}</option>
                      {{/each}}
                    </select>
                  </div>
                </div>
              {{/if}}
              
              <div class="form-group row">
                <label for="group-age-group" class="col-2 col-form-label">Age Group</label>
                <div class="col-10 row" id="group-age-group">
                  <div class="col-4">
                    <label for="group-age-group-begin">Age Group Begin</label>
                    <select class="form-control" id="group-age-group-begin">
                      {{#each val in ageValues}}
                        <option value="{{val}}" {{selectedIfEqual group.ageGroup.begin val}}>{{val}}</option>
                      {{/each}}
                    </select>
                  </div>
                  <div class="col-4">
                    <label for="group-age-group-end">Age Group End</label>
                    <select class="form-control" id="group-age-group-end">
                      {{#each val in ageValues}}
                        <option value="{{val}}" {{selectedIfEqual group.ageGroup.end val}}>{{val}}</option>
                      {{/each}}
                    </select>
                  </div>
                  <div class="col-4">
                    <label for="group-age-group-type">Age Group Range Type</label>
                    <select class="form-control" id="group-age-group-type">
                      <option value="months" {{selectedIfEqual group.ageGroup.type "months"}}>Months</option>
                      <option value="years" {{selectedIfEqual group.ageGroup.type "years"}}>Years</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="form-group row">
                <label for="group-enrollment-goal" class="col-4 col-form-label">Enrollment Goal (1 month out)</label>
                <div class="col-8">
                  <input class="form-control" type="text" value="{{group.enrollmentGoal}}" id="group-enrollment-goal"/>
                </div>
              </div>
              <div class="form-group row">
                <label for="group-ratio" class="col-4 col-form-label">{{ getEntityTypeStaff }} to {{ getEntityTypePeople }} Ratio (1:x)</label>
                <div class="col-8">
                  <input data-cy="group-ratio" class="form-control" type="text" value="{{group.ratio}}" id="group-ratio"/>
                </div>
              </div>
              <div class="form-group row">
                <label for="group-sleep-check-interval" class="col-4 col-form-label">Sleep Check Interval (in minutes)</label>
                <div class="col-8">
                  <input class="form-control" type="text" value="{{group.sleepCheckInterval}}" id="group-sleep-check-interval"/>
                </div>
              </div>
              
              {{#if hasCustomization "report/classList/enabled"}}
                <div class="form-group row">
                  <label for="group-include-class-list" class="col-4 col-form-label">Designate as Classroom</label>
                  <div class="col-2">
                    <label class="checkbox checkbox-rounded checkbox-primary col-form-label">
                      <input data-cy="group-include-class-list" type="checkbox" id="group-include-class-list" {{checkedIfEq true group.includeClassList}}/>
                      <span></span>
                    </label>
                  </div>
                </div>
              {{/if}}
              
              {{#if showChildcareOptions}}
                <div class="form-group row">
                  <label for="group-type-infant" class="col-4 col-form-label">Infant Group</label>
                  <div class="col-1">
                    <label class="checkbox checkbox-rounded checkbox-primary col-form-label">
                      <input type="checkbox" id="group-type-infant" {{checkedIfEq true group.typeInfant}}/>
                      <span></span>
                    </label>
                  </div>
                </div>
                <div class="form-group row">
                  <label for="group-type-infant" class="col-4 col-form-label">Group Type</label>
                  <div class="col-6">
                    <select class="form-control" id="group-type">
                      <option value=""></option>
                      {{#each val in groupTypes}}
                        <option value="{{val}}" {{selectedIfEqual group.getGroupType val}}>{{val}}</option>
                      {{/each}}
                    </select>
                  </div>
                </div>
              {{/if}}
              
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button data-cy="save-group-configuration" type="button" class="btn btn-primary font-weight-bolder mr-2" id="group-configuration-save-button">Save</button>
          <button data-cy="cancel-group-configuration" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>
