import { orgLanguageTranformationUtil } from "../../../lib/util/orgLanguageTranformationUtil";
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Orgs } from "../../../lib/collections/orgs.js";
import { processPermissions } from '../../../lib/permissions';
import { isCheckinRequired } from "../../../lib/momentHelpers.js";
import { showModal } from "../main";
import './_groupListRow.html';
import { offlinePreventCheck, setupNewMomentSession } from "../../../mpweb.js";
import './_groupBillingPlan.js';
import './_groupSuggestionRules.js';
import './_groupConfigurationModal.js';
import '../moments/momentFormModal.js';
import './_groupActivityModal.js';
import './_rosterModal.js';

Template._groupListRow.events({
  'click #billingPlan': function(event, template) {
    
    showModal("_groupBillingPlan", {groupId: this._id}, "#_groupBillingPlan");
  },
  'click #rules': function(event, template) {
    showModal("_groupSuggestionRules", {groupId: this._id}, "#_groupSuggestionRules");
  },
  'click #configuration': function(event, template) {
    showModal("_groupConfigurationModal", {groupId: this._id}, "#_groupConfigurationModal");
  },
  "click #new-moment-link": function(event, template) {
    if(isCheckinRequired()){
      mpSwal.fire("Error", "Staff and admins must be checked in to post moments. Please visit your profile to checkin.", "error");
    } else {
      setupNewMomentSession();
      Session.set("activeMomentParentSource", "group");
      Session.set("activeMomentParentSourceId", this._id);
      //setting this allows for observations without a selected curriculum
      Session.set("portfolioStandardGroupFilter", this._id);
      showModal("momentFormModal", {shouldFade: true}, "#momentFormModal");
    }
  },
  'click #group-activity-btn': function(event, template) {
    Session.set("activeMomentParentSource", "group");
    Session.set("activeMomentParentSourceId", this._id);
    //setting this allows for observations without a selected curriculum
    Session.set("portfolioStandardGroupFilter", this._id);
    Template.instance().thePeopleRosterSubscription = Meteor.subscribe("thePeopleRoster", { groupId: this._id });
    showModal("_groupActivityModal", {groupId: this._id}, "#_groupActivityModal");
  },
  'click #group-roster-btn': function(event, template) {
    // subscribe peopleByGroup here to show in roster
    Template.instance().thePeopleRosterSubscription = Meteor.subscribe("thePeopleRoster", { groupId: this._id });
    showModal("_rosterModal", {groupId: this._id}, "#_rosterModal")
  },
  "click #delete-group": function(event, template) {
    if (offlinePreventCheck()) return false;
    var groupId = this._id;
    return mpSwal.fire({  
      title: "Are you sure?",   
      text: "You will not be able to recover this group once deleted!",   
      icon: "warning",   
      showCancelButton: true,   
      confirmButtonText: "Yes, delete it!"
    }).then( async result => {
      if (result.value) {
        await Meteor.callAsync("deleteGroup", groupId);
      }
    });
  },
  'click #generate-peek-of-the-week': function(event, template) {
    event.preventDefault();
    Meteor.callAsync('generatePeekHtml', { groupId: this._id })
      .then((res) => {
        var wnd = window.open("Peek Of The Week", "", "_blank");
        wnd.document.write(res);
      })
      .catch((err) => {
        console.error(err);
      });
  },
	'click .newGroupLink': function(event, template) {
		if (offlinePreventCheck()) return false;
		$("#newGroupFormModal").modal();
	}
});

Template._groupListRow.onRendered( function() {

});

Template._groupListRow.onCreated( function() {
	var self = this;
  this.thePeopleRosterSubscription = null;
});

Template._groupListRow.onDestroyed(function () {
  // Destroy the subscription when not included in autorun; this will prevent document projections from persisting across publications
  if (this.thePeopleRosterSubscription) {
    this.thePeopleRosterSubscription.stop();
  }
});

Template._groupListRow.helpers({
  "showBillingInfo": function() {
    const org = Orgs.current();
    return org && org.billing && org.billing.enabled;
  },
  'checkedInCount': function() {
    return this.counts?.checkedInCount;
  },
  'staffCheckedInCount': function() {
    return this.counts?.staffCheckedInCount;
  },
  'absentCount': function() {
    return this.counts?.absentCount;
  },
  'totalPeopleCount': function() {
    return this.counts?.totalPeopleCount;
  },
  'remainingCount': function() {
    return this.counts?.remainingCount;

  },
  'groupInitials': function() {
    var initials = "";
    if (this.name && this.name.length > 0) initials += this.name[0];
    if (this.name && this.name.length > 1) initials += this.name[1];
    return initials.toUpperCase();
  },
  "showConfigurationTab": function() {
    return processPermissions({
      assertions: [{ context: "groups", action: "edit"}],
      evaluator: (person) => person.type=="admin"
    });
  },
	'capacityPercentage': function() {
    return this.counts?.capacityPercentage;

	},
	'roomCount': function() {
    return this.counts?.roomCount;

	},
  'isMomentPostable': function() {
    return processPermissions({
      assertions: [{ context: "moments", action: "edit" }],
      evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
    });
  },
  'getEntityTypePeople': function () {	
		return orgLanguageTranformationUtil.getEntityType('people').toLowerCase();
	}
});
