<template name="_groupAddForm">
  <form class="px-8 py-8" id="add-group-form">
    <div class="form-group">
      <label for="add-group-name">Group Name</label>
      <input data-cy="add-group-name" type="text" class="form-control" id="add-group-name" placeholder="Name">
    </div>
    <div class="form-group">
      <label for="add-group-capacity">Capacity</label>
      <input data-cy="add-group-capacity" type="text" class="form-control" id="add-group-capacity" placeholder="Capacity">
    </div>
    <div class="form-group row">
      <label class="col-4 col-form-label">Infant Group</label>
      <label class="col-8 col-form-label checkbox checkbox-primary">
          <input data-cy="infant-group" type="checkbox" id="add-group-infant" />
          <span></span>
      </label>
    </div>
    {{#if getShowAlert}}
      <div class="alert alert-custom alert-notice alert-light-danger fade show" id="checkin-alert" role="alert">
        <div class="alert-icon"><i class="fad fa-exclamation-triangle"></i></div>
        <div class="alert-text">{{getActiveAlertText}}</div>
        <div class="alert-close">
          <button type="button" class="close" data-dismiss="alert" aria-label="Close" id="group-add-close-alert">
            <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
          </button>
        </div>
      </div>
    {{/if}}
    <div data-cy="save-create-another" class="group-save-btn btn btn-primary font-weight-bolder mr-2 save-and-create">Save & Create Another</div>
    <div data-cy="save-group" class="group-save-btn btn btn-primary font-weight-bolder">Save</div>
  </form>
</template>
