import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Orgs } from "../../../lib/collections/orgs.js";
import { Groups} from '../../../lib/collections/groups.js';
import { hideModal } from "../main";
import _ from '../../../lib/util/underscore';
import './_groupBillingPlan.html';
import './_scheduleTypeBillingPlanLinking.js';
Template._groupBillingPlan.onCreated( function() {
  this.scheduleTypeRows = new ReactiveVar({});
})

Template._groupBillingPlan.helpers({
  "group": function() {
    var groupId = Template.instance().data.groupId;
    var group = Groups.findOne({_id: groupId});
    return group;
  },
  "availableBillingPlans": function() {
    var groupId = Template.instance().data.groupId;
    var group = Groups.findOne({_id: groupId});
    
    var billingPlans = [{_id: "n/a", description: "None"}];
    if (group.defaultBillingPlanId) billingPlans = [];
    
    _.each(Orgs.current().billing.plansAndItems, (i) => { if (i.type == "plan" && (!i.archived || i._id == group.defaultBillingPlanId)) billingPlans.push({ _id: i._id, description: i.description }) });
    return _.sortBy(billingPlans, "description");
  },
  scheduleTypeLinkingData() {
    const template = Template.instance();
    return {
      onRowsChange: (rows) => {
        template.scheduleTypeRows.set(rows);
      }
    };
  },
  groupId() {
    return Template.instance().data.groupId;
  }
});

Template._groupBillingPlan.events({
	"click #group-billing-plan-save-button": (event, template) => {
    event.preventDefault();
    var groupId = Template.instance().data.groupId;
    const rows = template.scheduleTypeRows.get();
    const billingId = $("#group-billing-info").val();
    if (billingId == "n/a") return mpSwal.fire("Error", "You must select a Billing Plan", "error");
    const isValid = validateScheduleData(rows);

    if (!isValid) {
      mpSwal.fire({
        title: "Error",
        text: "Missing schedule type or billing plans",
        icon: "error"
      })
    } else {
      mpSwal.fire({
        title: "Replace Existing Plans?",
        text: "Do you want to replace all existing billing plans for this group?",
        showCancelButton: true,
        cancelButtonText: "No",
        showCloseButton: true,
        confirmButtonText: "Yes",
      }).then(result => {
        if (result.value || (result.dismiss && result.dismiss == "cancel")) {
          const replaceExisting = result.value;
          Meteor.callAsync("updateGroupBillingPlan", { groupId: groupId, planId: billingId, replaceExisting: replaceExisting })
            .then((result) => {
              mpSwal.fire("Success", "Group Default Billing Plan Saved", "success");
              hideModal("#_groupBillingPlan");
            })
            .catch((error) => {
              mpSwal.fire("Error", error.reason, "error");
            });

          Meteor.callAsync('setScheduleTypeLinkingDataForGroup', rows, groupId)
            .then((result) => {
              // Handle success if needed
            })
            .catch((error) => {
              mpSwal.fire("Error", error.reason, "error");
            });
        }
      })
    }
	}
});

function validateScheduleData(data) {
  return !Object.values(data).some(row =>
    !row.scheduleType || row.billingPlans.length === 0
  );
}