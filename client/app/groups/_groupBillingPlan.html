<template name="_groupBillingPlan">
  <!-- Modal-->
  <div class="modal fade" id="_groupBillingPlan" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document" style="max-width: 80vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 data-cy="default-billing-plan" class="modal-title" id="exampleModalLabel">{{group.name}} Default Billing Plan</h5>
          <div data-cy="close-modal" class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
            <span class="fad-regular fad-primary fad fa-times"></span>
          </div>
        </div>
        <div class="modal-body bg-gray-100">
          <!--begin::Form-->
          <form>
            <div class="card-body">          
              <div class="form-group row">
                <label for="group-billing-info" class="col-4 col-form-label">Plan to use in forecasting reports for ideal revenue</label>
                <div class="col-8">
                  <select class="form-control" id="group-billing-info">
                    {{#each availableBillingPlans}}
                      <option value="{{_id}}" {{selectedIfEqual _id group.defaultBillingPlanId}}>{{description}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            </div>
          </form>
          {{> _scheduleTypeBillingPlanLinking data=scheduleTypeLinkingData groupId=groupId}}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary font-weight-bolder mr-2" id="group-billing-plan-save-button" data-cy="group-billing-plan-save-button">Save</button>
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal" data-cy="close-group-billing-plan">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>
