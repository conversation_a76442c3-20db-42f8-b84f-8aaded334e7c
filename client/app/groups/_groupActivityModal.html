<template name="_groupActivityModal">
  <!-- Modal-->
  <div class="modal fade" id="_groupActivityModal" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 data-cy="group-activity-modal" class="modal-title" id="exampleModalLabel">{{group.name}} Activity</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-primary font-weight-bolder btn-text-white mr-4" id="filterMomentsLink" data-toggle="collapse" data-target="#filterMomentsCollapse" aria-expanded="false" aria-controls="filterMomentsCollapse">
              <i class="fad-regular fad fa-filter fa-swap-opacity mr-2" style="color:#fff"></i>Filter
            </div>
            <div data-cy="close-activity-modal" class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div data-cy="group-activity-content" class="modal-body bg-gray-100">
          {{ > momentList source="group" _id=group._id hideFilter="true"}}
        </div>
        <div class="modal-footer">
          <button data-cy="close-group-activity-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>
