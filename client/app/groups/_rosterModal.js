import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Groups } from '../../../lib/collections/groups.js';
import { Orgs } from "../../../lib/collections/orgs.js";
import { Reservations } from "../../../lib/collections/reservations";
import { People, Person } from "../../../lib/collections/people";
import _ from '../../../lib/util/underscore';
import './_rosterModal.html';
import '../people/_peopleListCard.js';
import '../people/_peopleListRow.js';
import Swal from 'sweetalert2/dist/sweetalert2';

const moment = require('moment-timezone');
const { Session } = require('meteor/session');
function resetClassForSelectedPeople() {
  var selectedPeople = $(".mp-selected-card").map(function (x) { return $(this).data("id") }).get();
  _.each(selectedPeople, (p) => {
    $(`#${p}-list-card`).removeClass("mp-selected-card");
    $(`#${p}-list-card`).removeClass("bg-success-o-80");
  })
}

function defaultFilter(people, groupId) {
  return _.filter(people, function (p) { return (p.checkInGroupId == groupId) })
    .concat(_.filter(people, function (p) { return (!p.checkedIn) }))
    .concat(_.filter(people, function (p) { return (p.checkedIn && p.checkInGroupId != groupId && p.defaultGroupId == groupId) }));
}
// Helper function to parse time strings into Date objects
function parseTime(timeString) {
  return moment(timeString, 'h:mm a');
}
const computeRatioGroups = function () {

  const todayStamp = new moment().startOf('day').valueOf(), currentUser = Meteor.user();
  if (!currentUser) return;

  const groups = [],
    dayOfWeek = new moment().format("ddd").toLowerCase(),
    startOfDay = new moment().startOf('day').valueOf(),
    endOfDay = new moment().endOf('day').valueOf(),
    canceledReservationPeopleIds = Reservations.find({ scheduledDate: { "$gte": startOfDay, "$lt": endOfDay }, cancellationReason: { $exists: true } }, { fields: { selectedPerson: 1 } }).fetch().map(p => p.selectedPerson),
    allReservationPeopleIds = Reservations.find({ scheduledDate: { "$lte": startOfDay }, $and: [{ $or: [{ scheduledEndDate: { "$gte": startOfDay } }, { scheduledEndDate: { $in: [null, false] } }] }, { $or: [{ recurringDays: dayOfWeek }, { recurringFrequency: { $exists: false } }] }] }, { fields: { selectedPerson: 1 } }).fetch().filter(r => !_.contains(canceledReservationPeopleIds, r.selectedPerson)).map(p => p.selectedPerson);

  let maxCapacity = 0, maxCheckins = 0, maxCount;
  Groups.find({ orgId: currentUser.orgId }, { sort: { sortOrder: 1, name: 1 } }).forEach(function (item) {

    const entry = { name: item.name, capacity: item.capacity, groupId: item._id, ratio: item.ratio };
    if (item.capacity && parseInt(item.capacity) > maxCapacity) maxCapacity = parseInt(item.capacity);

    const peopleInRoom = People.find({ checkedIn: true, checkInGroupId: item._id, type: 'person' });
    const roomCount = peopleInRoom.count();
    entry.checkinCount = roomCount;
    if (roomCount > maxCheckins) maxCheckins = roomCount;

    var staffRoomCount = People.find({ checkedIn: true, checkInGroupId: item._id, type: { "$in": ['admin', 'staff'] } }).count();
    entry.staffCount = staffRoomCount;

    var absencePeopleCount = People.find({ _id: { $nin: canceledReservationPeopleIds }, inActive: { $ne: true }, checkedIn: { $ne: true }, defaultGroupId: item._id, type: 'person', 'familyCheckIn.absent': true, 'familyCheckIn.checkInTime': { $gte: todayStamp } }).count(),
      canceledGroupReservationCount = People.find({ inActive: { $ne: true }, defaultGroupId: item._id, type: 'person', _id: { $in: canceledReservationPeopleIds } }).count(),
      absenceCount = absencePeopleCount + canceledGroupReservationCount;
    entry.absenceCount = absenceCount;

    var remainingCountQuery = { _id: { $in: allReservationPeopleIds }, inActive: { $ne: true }, checkedIn: { $ne: true }, defaultGroupId: item._id, type: 'person', $or: [{ 'familyCheckIn.absent': { $ne: true } }, { 'familyCheckIn.checkInTime': { $lt: todayStamp } }] };
    var remainingCount = People.find(remainingCountQuery).count();
    entry.remainingCount = remainingCount;
    const reservationCount = (_.intersection(peopleInRoom.map(pir => pir._id), allReservationPeopleIds)).length;
    const checkinNoSchedule = roomCount - reservationCount;

    if (checkinNoSchedule > 0) entry.noScheduleMessage = `${checkinNoSchedule} check-in(s) with no schedule`;
    groups.push(entry);
  });
  maxCount = maxCapacity;
  if (maxCheckins > maxCapacity) maxCount = maxCheckins;
  if (maxCount < 10) maxCount = 10;

  groups.forEach((group) => {
    group.inCompliance = true;
    if (group.ratio) {
      const goldenRatio = 1 / parseInt(group.ratio);
      const currentRatio = parseInt(group.staffCount) / parseInt(group.checkinCount);
      if (group.checkinCount > 0 && currentRatio < goldenRatio) group.inCompliance = false;
    }
    group.maxCount = maxCount;
    group.checkinLength = parseInt(group.checkinCount / maxCount * 100);
    if (group.capacity && group.capacity > 0) {
      const adjustment = maxCount - group.capacity;
      group.capacityLength = parseInt((group.capacity - adjustment - group.checkinCount) / maxCount * 100);
    }
    group.staffLength = parseInt(group.staffCount / maxCount * 100);
  });
  return groups;
}

function _hydratePeople(people) {
  // TODO: Improve this. There are performance considerations and just general implementation
  // improvements that could be made here.

  let providerMax = 0;
  let familyMax = 0;
  const timezone = Orgs.current().getTimezone();
  const startDateNum = new moment.tz(timezone).startOf('day').valueOf();
  const endDateNum = new moment.tz(timezone).endOf('day').valueOf();
  const dayMap = { "sat": "Sa", "sun": "Su", "mon": "M", "tue": "T", "wed": "W", "thu": "R", "fri": "F" };
  _.each(people, function (person, index) {
    if ((person.type === "person" || person.type === 'staff') && !person.inActive) {
      const pData = person.engagementData();

      if (pData.providerCount > providerMax) {
        providerMax = pData.providerCount;
      }

      if (pData.familyCount > familyMax) {
        familyMax = pData.familyCount;
      }

      if (pData.providerCount > 0 || pData.familyCount > 0) {
        person.hasEngagement = true;
        person.engagementProviderCount = pData.providerCount;
        person.engagementFamilyCount = pData.familyCount;
      }

      const todayReservations = Reservations.findWithRecurrence({ startDateValue: startDateNum, endDateValue: endDateNum, query: { selectedPerson: person._id } });
      const canceledReservations = Reservations.findWithRecurrence({
        startDateValue: startDateNum,
        endDateValue: endDateNum,
        query: {
          selectedPerson: person._id,
          cancellationReason: { $exists: true }
        }
      });

      // Add consideration for mobile cancellations BUGS-2171
      const mobileCancellation = person.familyCheckIn?.absent && person.familyCheckIn.checkInTime >= new moment().startOf('day').valueOf();
      const isAbsentToday = (canceledReservations.length > 0 || mobileCancellation);
      person.isScheduledToday = todayReservations.length > 0;
      person.isAbsentToday = !person.checkedIn && isAbsentToday;
      if (isAbsentToday) {
        person.absentComment = mobileCancellation ? '' : canceledReservations[0].cancellationComments;
        person.absentReason = mobileCancellation ? person.familyCheckIn?.absentReason :canceledReservations[0].cancellationReason;
      }
      const recurringDays = [],
        todayStamp = new moment().startOf("day").valueOf(),
        personRecurringReservations = Reservations.find({
          recurringFrequency: { "$ne": null },
          selectedPerson: person._id,
          "$and": [
            { "$or": [{ "scheduledDate": { "$eq": null } }, { "scheduledDate": { "$lte": todayStamp } }] },
            { "$or": [{ "scheduledEndDate": { "$eq": null } }, { "scheduledEndDate": { "$gt": todayStamp } }] }
          ]
        }).fetch();
      _.each(personRecurringReservations, reservation => {

        if (reservation.recurringDays) {
          _.each(reservation.recurringDays, (d) => recurringDays.push(dayMap[d]))
        }
      });

      person.recurringDays = recurringDays.join(",");

    }
    person.rank = index + 1;
  });
  _.each(people, function (person) {
    if (person.hasEngagement) {
      person.engagementProviderMax = providerMax;
      person.engagementFamilyMax = familyMax;
      person.engagementTotalCount = (providerMax > 0 ? person.engagementProviderCount / providerMax : 0) +
        (familyMax > 0 ? person.engagementFamilyCount / familyMax : 0);
    }
  });
  return people
}

Template._rosterModal.onCreated(function () {

  var self = this;

  self.recentMove = new ReactiveVar(false);
  self.recentNameToFace = new ReactiveVar(false);

  var groupId = Template.instance().data.groupId;

  Meteor.callAsync('getRecentMoveMomentByGroupId', groupId)
    .then((result) => {
      self.recentMove.set(result);
    })

  Meteor.callAsync('getRecentNameToFaceMomentByGroupId', groupId)
    .then((result) => {
      self.recentNameToFace.set(result);
    });
});

Template._rosterModal.helpers({
  "onlyAbsentPeople": function () {
    const value = Template.instance().onlyAbsentPeople.get();
    return Template.instance().onlyAbsentPeople.get();
  },
  "scheduleTypes": function() {
    return _.sortBy(Orgs.current().getScheduleTypes(), "type");
  },
  'gridViewActive': function () {
    return Template.instance().gridViewActive.get();
  },
  'currentSort': function () {
    const sortField = Session.get("groupRosterSort")?.field || Template.instance().sortField.get();
    const sortDirection = Session.get("groupRosterSort")?.direction || Template.instance().sortDirection.get();
    return `${sortField}|${sortDirection}`;
  },
  "group": function () {
    var groupId = Template.instance().data.groupId;
    var group = Groups.findOne({ _id: groupId });
    return group;
  },
  "showSleepCheck": function () {
    return Template.instance().selectMode.get() && Orgs.current() && Orgs.current().isChildCare();
  },
  "showMultipleCheckin": function () {
    const org = Orgs.current();
    return org && org.hasCustomization("people/multipleCheckin/enabled") && !Template.instance().nameToFace.get();
  },
  "showGridView": function () {
    return Template.instance().nameToFace.get() || Template.instance().selectMode.get() || Template.instance().gridViewActive.get();
  },
  "multipleSelectMode": function () {
    return Template.instance().selectMode.get();
  },
  "nameToFacePeople": function () {
    return Template.instance().nameToFacePeople.get();
  },
  "nameToFaceSelected": function () {
    return Template.instance().nameToFace.get();
  },
  "nameToFacePrompt": function () {
    var groupId = Template.instance().data.groupId;
    const group = Groups.findOne({ _id: groupId })
    if (!group) {
      return false;
    }
    if (!group.timestamps) {
      return false;
    }
    const timestamps = group.timestamps;

    if (timestamps.moveMomentTimestamp && !timestamps.nameToFaceMomentTimestamp) {
      return true;
    }


    if (timestamps.moveMomentTimestamp && timestamps.nameToFaceMomentTimestamp) {
      return timestamps.moveMomentTimestamp.timestamp >= timestamps.nameToFaceMomentTimestamp.timestamp;
    }
    return false;
  },
  "scheduleTypeChecked": function(scheduleTypeId) {
    var selectedScheduleTypes = Template.instance().selectedScheduleTypes.get();
    return selectedScheduleTypes.includes(scheduleTypeId);
  },
  "retrievePeople": function (options) {
    const showScheduled = options?.hash?.scheduled;
    const showAbsent = options?.hash?.absent;
    const currentOrg = Orgs.current();
    if (!currentOrg) {
      return [];
    }
    const selectedScheduleTypes = Template.instance().selectedScheduleTypes.get();
    const groupId = Template.instance().data.groupId;
    const conditions = { "type": { "$not": "family" } };
    //TODO: Template Filtering
    conditions["orgId"] = currentOrg._id;

    const filterInactive = Template.instance().filterInactive.get();
    if (!filterInactive) {
      conditions["inActive"] = { $ne: true };
    }


    if (Template.instance().nameToFace.get()) {
      conditions["checkedIn"] = true;
      conditions["checkInGroupId"] = groupId;
      conditions["type"] = "person";
    } else {
      conditions["$or"] = [{ checkInGroupId: groupId }, { defaultGroupId: groupId }];
    }

    const sortField = Template.instance().sortField.get(), sortDirection = Template.instance().sortDirection.get();
    let sort;

    if (sortField === "firstName") {
      sort = { firstName: sortDirection === "asc" ? 1 : -1, lastName: 1 };
    } else if (sortField === "engagement") {
      sort = {};
    } else if (sortField === "type") {
      sort = { type: sortDirection === "asc" ? 1 : -1, lastName: 1 };
    } else if (sortField === "lastName") {
      sort = { lastName: sortDirection === "asc" ? 1 : -1, firstName: 1 };
    } else if (sortField === "sType") {
      sort = {};
    } else {
      sort = { lastName: 1, firstName: 1 };
    }

    let people = People.find(conditions, { sort: sort }).fetch();

    people = _hydratePeople(people);

    if (selectedScheduleTypes && selectedScheduleTypes.length > 0) {
      const todayStamp = new moment().startOf("day").valueOf();
      const peopleWithMatchingScheduleType = people.filter(p =>
          Reservations.find({
            selectedPerson: p._id,
            scheduleType: { $in: selectedScheduleTypes },
            "$and": [
              { "$or": [{ "scheduledDate": { "$eq": null } }, { "scheduledDate": { "$lte": todayStamp } }] },
              { "$or": [{ "scheduledEndDate": { "$eq": null } }, { "scheduledEndDate": { "$gt": todayStamp } }] }
            ],
          }).count() > 0
      );
      people = peopleWithMatchingScheduleType;
    }

    people = _.filter(people, (p) => showScheduled ? p.isScheduledToday : !p.isScheduledToday);
    people = _.filter(people, (p) => showAbsent ? p.isAbsentToday : !p.isAbsentToday);

    if (sortField === "engagement") {
      return _.sortBy(people, function (person) { return (person.engagementTotalCount || 0) * (sortDirection === "asc" ? 1 : -1); });
    } else if (sortField === "default") {
      const user = Meteor.user();
      const person = user && user.fetchPerson();
      if (person.type === "admin") {
        let staff = _.filter(people, function (person) { return (person.type === "staff" || person.type === "admin") && person.checkedIn });
        staff = _.sortBy(staff, 'lastName');
        let otherPeople = _.reject(people, function (person) { return (person.type === "staff" || person.type === "admin") && person.checkedIn });
        return defaultFilter(staff.concat(otherPeople), groupId)
      }
      return defaultFilter(people, groupId);
    } else if (sortField === "sType") {
      return _.sortBy(people, (person) => {
        const reservation = Reservations.findOne({ selectedPerson: person._id }, { sort: { scheduledTime: 1 } });
        if (reservation) {
          const scheduledTime = parseTime(reservation.scheduledTime);
          return scheduledTime.isValid() ? scheduledTime.valueOf() : Infinity;
        }
        return Infinity;
      });
    } else {
      return people;
    }
  },
  "retrieveAbsentPeople": function () {

    // This is a more efficient implementation of retrievePeopleV1.
    // The modal is blocking with a slow / long query so it's important
    // to make it as fast as possible.

    // A note ... some of the roster modal functionality may be missing.
    // One thing I noticed was "Engagement" was missing.
    // It's likely worth having that load in asynchonously to not block the modal,
    // so leaving that and other functionality out for now. -Cohoat
    const currentOrg = Orgs.current();
    const timezone = currentOrg.getTimezone();
    const startOfDay = new moment.tz(timezone).startOf('day').valueOf();
    const endOfDay = new moment.tz(timezone).endOf('day').valueOf();

    const cancelledReservations = Reservations.findWithRecurrence({
      startDateValue: startOfDay,
      endDateValue: endOfDay,
      query: {
        cancellationReason: { $exists: true }
      }
    })

    const canceledReservationPeopleIds = cancelledReservations.map(p => p.selectedPerson);

    // Sort by lastName:
    // Add consideration for mobile cancellations BUGS-2171
    const peopleQuery = {
      $or: [
        {
          _id: { $in: canceledReservationPeopleIds },
          orgId: currentOrg._id,
          type: 'person',
          inActive: {$ne: true},
        },
        {
          _id: { $nin: canceledReservationPeopleIds },
          orgId: currentOrg._id,
          inActive: {$ne: true},
          checkedIn: {$ne: true},
          type: 'person',
          'familyCheckIn.absent': true,
          'familyCheckIn.checkInTime': {$gte: startOfDay}
        }
      ]
    }

    const people = People.find(peopleQuery, {
      sort: { lastName: 1 }
    }).fetch().map(person => {
      const reservation = cancelledReservations.find(r => r.selectedPerson === person._id);
      person.isAbsentToday = true;
      person.absentComment = reservation ? reservation.cancellationComments : '';
      person.absentReason = reservation ? reservation.cancellationReason : person.familyCheckIn?.absentReason;
      return person;
    })

    return _hydratePeople(people);

  },
  'getEntityTypePeople': function () {	
		return orgLanguageTranformationUtil.getEntityType('people');
	},
});

Template._rosterModal.events({
  "change #showScheduledCheckbox": function (event, template) {
    template.showScheduled.set(event.currentTarget.checked);
  },
  "change .schedule-type-checkbox": function (event, template) {
    const selectedScheduleTypes = $(".schedule-type-checkbox:checked").map(function () {
      return $(this).val();
    }).get();
    template.selectedScheduleTypes.set(selectedScheduleTypes);
    Session.set("selectedScheduleTypes", selectedScheduleTypes);
  },
  "change #allCheckbox": function (event, template) {
    const allChecked = event.currentTarget.checked;
    const checkboxes = $(".schedule-type-checkbox");

    if (allChecked) {
      checkboxes.prop("checked", true);
      const selectedScheduleTypes = checkboxes.map(function () {
        return $(this).val();
      }).get();
      template.selectedScheduleTypes.set(selectedScheduleTypes)
      Session.set("selectedScheduleTypes", selectedScheduleTypes);
    } else {
      const selectedScheduleTypes = [];
      checkboxes.prop("checked", false);
      template.selectedScheduleTypes.set(selectedScheduleTypes);
      Session.set("selectedScheduleTypes", selectedScheduleTypes);
    }
  },
  "click #checkIncludeInactive": function (event, template) {
    template.filterInactive.set($(event.currentTarget).is(':checked'));
  },
  "change #sortButton": function (event, template) {
    var sortButtonSelection = $("#sortButton").val().split("|");
    if (sortButtonSelection.length > 0) {
      template.sortField.set(sortButtonSelection[0]);
      template.sortDirection.set(sortButtonSelection[1]);
      Session.set("groupRosterSort", {
        field: sortButtonSelection[0],
        direction: sortButtonSelection[1]
      });
    }
  },
  "click #select-multiple": function (event, template) {
    var current = template.selectMode.get();
    resetClassForSelectedPeople();

    template.selectMode.set(!current);
    template.nameToFace.set(false);
    template.gridViewActive.set(true);
    template.nameToFacePeople.set([]);
  },
  "click #name-to-face": function (event, template) {
    var current = template.nameToFace.get();
    resetClassForSelectedPeople();

    template.nameToFace.set(!current);
    template.selectMode.set(false);
    template.gridViewActive.set(true);
    var filterGroupId = template.data.groupId;
    if (!current == true) {
      Meteor.callAsync("nameToFacePeopleFilter", { filterGroupId })
        .then((result) => {
          Meteor.callAsync("getPeopleByQueryAndOptions",
            { _id: { $in: result.peopleIds } },
            {})
            .then((resPeopleList) => {
              const ntfPeople = resPeopleList.map(item => {
                return new Person(item); // Convert to collection document to get access to Person extended methods.
              });
              template.nameToFacePeople.set(ntfPeople);
            }).catch((err) => {
              console.log("Error while fetching invoiced person -", err);
            });
        }).catch((err) => {
          mpSwal.fire("Error", err.reason, "error");
        });
    } else {
      template.nameToFacePeople.set([]);
    }
  },
  "click #toggle-view": function (event, template) {
    template.gridViewActive.set(!template.gridViewActive.get());
    template.nameToFace.set(false);
    template.selectMode.set(false);
    template.nameToFacePeople.set([]);
  },
  "click #move-to": function (event, template) {
    var selectedPeople = $(".mp-selected-card").map(function (x) { return $(this).data("id") }).get();
    let groupOptions = {};
    Groups.find({}, { sort: { name: 1 } }).fetch().forEach((g) => { groupOptions[g._id] = g.name });

    mpSwal.fire({
      title: "Move To",
      text: "Choose a group to move selected people",
      input: "select",
      inputOptions: groupOptions,
      showCancelButton: true,
      confirmButtonText: "Move"
    }).then(async result => {
      if (result.value) {
        const switchData = {
          peopleIds: selectedPeople,
          groupId: result.value
        }
        await Meteor.callAsync("switchGroup", switchData);
        resetClassForSelectedPeople()
        template.selectMode.set(false);
        Meteor.callAsync('getRecentMoveMomentByGroupId', switchData.groupId)
          .then((result) => {
            template.recentMove.set(result);
          });
      }
    });
  },
  "click #sleep-check": function (event, template) {
    var selectedPeople = $(".mp-selected-card").map(function (x) { return $(this).data("id") }).get();
    if (!selectedPeople || selectedPeople.length == 0) return mpSwal.fire("You must select at least one person for a sleep check.");
    const org = Orgs.current();
    let outputMessage = "";
    mpSwal.fire({
      title: "Sleep Check",
      text: "A sleep check will be recorded for " + selectedPeople.length + " people.",
      input: "select",
      inputOptions: { "none": "", "Back": "Back", "Left Side": "Left Side", "Right Side": "Right Side", "Tummy": "Tummy" },
      showCancelButton: true,
      confirmButtonText: "Record Sleep Check"
    }).then(result => {
      if (result.value) {
        var opts = {};
        if (result.value != "none") opts.sleepPosition = result.value

        const callSleepCheck = function () {
          Meteor.callAsync("sleepCheck", { selectedPeople, opts })
            .then((result) => {
              resetClassForSelectedPeople()
              template.selectMode.set(false);
              if (result.successfulSleepChecks.length > 0) {
                outputMessage = outputMessage + "<br/>Sleep checks recorded for: " +
                  _.map(result.successfulSleepChecks, (p) => p.firstName + " " + p.lastName).join(", ");
              }

              if (result.sleepNotFound.length > 0) {
                outputMessage = outputMessage + "<br/>Sleep moments not found for: " +
                  _.map(result.sleepNotFound, (p) => p.firstName + " " + p.lastName).join(", ");
              }

              mpSwal.fire("Sleep Check Results", outputMessage, "success");
            })
            .catch((error) => {
              mpSwal.fire("Error with Sleep Check", error.reason, "error");
            });
        }

        if (org.hasCustomization("moments/sleep/distressedCheck")) {
          mpSwal.fire({
            title: "Signs of Distress",
            input: "select",
            inputOptions: { "No": "No", "Yes": "Yes" },
            showCancelButton: false,
            confirmButtonText: "Save"
          }).then(r2 => {
            opts.distressedSleep = r2.value;
            callSleepCheck();
          })
        } else {
          callSleepCheck();
        }

      }
    });

  },
  "click #check-in": function (event, template) {
    var selectedPeople = $(".mp-selected-card").map(function (x) { return $(this).data("id") }).get();
    Meteor.callAsync("checkInMultiple", { selectedPeople })
      .then((result) => {
        resetClassForSelectedPeople()
        mpSwal.fire("Success", "Checked in " + result.checkInCount, "success");
        template.selectMode.set(false);
      })
      .catch((error) => {
        mpSwal.fire("Error", error.reason, "error");
      });
  },
  "click #check-out": function (e, template) {
    var selectedPeople = $(".mp-selected-card").map(function (x) { return $(this).data("id") }).get();
    Meteor.callAsync("checkOutMultiple", { selectedPeople })
    .then((result)=>{
      resetClassForSelectedPeople()
      mpSwal.fire("Success", "Checked out " + result.checkInCount, "success");
      template.selectMode.set(false);
    })
    .catch((error)=>{
      mpSwal.fire("Error", error.reason, "error");
    });
  },
  "click #save-name-to-face": function (event, template) {
    var selectedPeople = $(".mp-selected-card").map(function (x) { return $(this).data("id") }).get();
    var filterGroupId = Template.instance().data.groupId;
    var completedById = Meteor.user().fetchPerson()._id;
    let inComplete = Orgs.current() && Orgs.current().hasCustomization("moments/incompleteFTF/enabled")
    inComplete = !inComplete;
    var moment_type = ""

    // inputOptions can be an object or Promise
    if (inComplete && selectedPeople.length < template.nameToFacePeople.get().length) {
      mpSwal.fire({
        title: 'Choose a value',
        html: `
        <div class="radio-list">
          <label class="radio radio-primary">
            <input type="radio" id="swal-input" name="assignment-type" value="Potty" >
            <span></span>
            Bathroom
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="assignment-type" value="Split">
            <span></span>
            Split
          </label>
          <label class="radio radio-primary">
            <input type="radio" name="assignment-type" value="Extracurricular" >
            <span></span>
            Extracurricular
          </label>
          <div style="display:flex">
            <label style="width:auto;" class="radio radio-primary form-group">
              <input type="radio" name="assignment-type" value="Other" >
              <span></span>
              Other:
            </label>
          <input class="form-control" placeholder="Write the reason" style="width:auto;position: relative; opacity: inherit;" type="text" name="otherVal" value="" >
          </div>
        </div>
        `,
        showDenyButton: true,
        confirmButtonText: 'Save',
        denyButtonText: `Don't save`,
        focusConfirm: false,
        preConfirm: () => {
          if ($("input[name='assignment-type']:checked").val()) {
            if ($("input[name='assignment-type']:checked").val() == "Other") {
              if (!$("input[name='otherVal']").val()) {
                Swal.showValidationMessage('Is necessary choose one option.')
              } else
                return $("input[name='otherVal']").val()
            } else
              return $("input[name='assignment-type']:checked").val()

          } else {
            Swal.showValidationMessage('Is necessary choose one option.')
          }
        },
      }).then(function (result) {
        if (result.isConfirmed) {
          moment_type = result.value

          Meteor.callAsync("nameToFaceCheckConfirmation", { selectedPeople, completedById, filterGroupId, inComplete, moment_type })
            .then((result) => {
              resetClassForSelectedPeople()
              template.nameToFace.set(false);
              template.nameToFacePeople.set([]);
              template.selectMode.set(false);
              mpSwal.fire("Success", "Name to Face saved", "success");
            })
            .catch((error) => {
              mpSwal.fire("Error", error.reason, "error");
            });

          Meteor.callAsync('getRecentNameToFaceMomentByGroupId', filterGroupId)
            .then((result) => {
              template.recentNameToFace.set(result);
            });
        }
      })
    } else {

      Meteor.callAsync("nameToFaceCheckConfirmation", { selectedPeople, completedById, filterGroupId, inComplete, moment_type })
        .then((result) => {
          resetClassForSelectedPeople()
          template.nameToFace.set(false);
          template.nameToFacePeople.set([]);
          template.selectMode.set(false);
          mpSwal.fire("Success", "Name to Face saved", "success");
        })
        .catch((error) => {
          mpSwal.fire("Error", error.reason, "error")
        });

      Meteor.callAsync('getRecentNameToFaceMomentByGroupId', filterGroupId)
        .then((result) => {
          template.recentNameToFace.set(result);
        });
    }
  },
});

Template._rosterModal.onCreated(function () {
  const self = this;
  this.selectMode = new ReactiveVar(false);
  this.nameToFace = new ReactiveVar(false);
  this.sortField = new ReactiveVar("default");
  this.sortDirection = new ReactiveVar("asc");
  this.filterInactive = new ReactiveVar(false);
  this.gridViewActive = new ReactiveVar(false);
  this.nameToFacePeople = new ReactiveVar([]);
  this.onlyAbsentPeople = new ReactiveVar(
    this.data.onlyAbsentPeople || false
  );
  this.selectedScheduleTypes = new ReactiveVar(Session.get("selectedScheduleTypes") || []);
  this.showScheduled = new ReactiveVar(true);
})