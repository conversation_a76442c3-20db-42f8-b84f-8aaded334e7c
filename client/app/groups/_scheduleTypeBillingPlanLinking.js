import { ReactiveDict } from 'meteor/reactive-dict';
import moment from 'moment-timezone';
import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';
import './_scheduleTypeBillingPlanLinking.html';
import '../bundles/multiselect.css';

Template._scheduleTypeBillingPlanLinking.onCreated(function() {
  this.rows = new ReactiveDict();

  const groupId = this.data.groupId;
  const group = Groups.findOne(groupId);
  const todayDate = moment.tz(Orgs.current().getTimezone()).startOf("day").valueOf();

  if (group && group.scheduleTypeToPlans && Object.keys(group.scheduleTypeToPlans).length > 0) {
    // Convert scheduleTypeToPlans format to rows format
    Object.entries(group.scheduleTypeToPlans).forEach(([scheduleTypeId, planIds]) => {
      const rowId = Random.id();

      // Convert the array of plan IDs to the billingPlans format we need
      const billingPlans = planIds.map(planId => {
        const plan = Orgs.current().availableBillingPlans().find(p => p._id === planId);
        return {
          _id: planId,
          type: plan?.description
        };
      });

      this.rows.set(rowId, {
        scheduleType: scheduleTypeId,
        billingPlans: billingPlans
      });
    });
  } else {
    // If no existing data, set default empty row
    const initialId = Random.id();
    this.rows.setDefault(initialId, {
      scheduleType: '',
      billingPlans: []
    });
  }

  this.scheduleTypes = new ReactiveVar(Orgs.current().getScheduleTypes()
    .map(type => ({
      _id: type._id,
      type: type.type,
    })));

  this.billingPlans = new ReactiveVar(
    Orgs.current()
      .availableBillingPlans()
      .filter(plan => !plan.suspendUntil || plan.suspendUntil < todayDate)   // Don't show suspended billing plans
      .map(plan => ({
        description: plan.description,
        _id: plan._id
      }))
  );

  this.autorun(() => {
    const rows = this.rows.all();
    if (this.data.data && this.data.data.onRowsChange) {
      this.data.data.onRowsChange(rows);
    }
  });
});
Template._scheduleTypeBillingPlanLinking.helpers({
  initializeMultiselect(rowId) {
    const instance = Template.instance();

    Tracker.afterFlush(() => {
      const $select = $(`#billingPlanMultiSelect-${rowId}`);

      // First destroy any existing multiselect on this element
      if ($select.hasClass('multiselect-initialized')) {
        $select.multiselect('destroy');
      }

      if ($select.length) {
        const row = instance.rows.get(rowId);

        $select.multiselect({
          includeSelectAllOption: true,
          buttonWidth: '100%',
          maxHeight: 200,
          enableFiltering: true,
          nonSelectedText: 'Select Billing Plans',
          onChange: handleSelectionChange(instance, rowId),
          onSelectAll: handleSelectionChange(instance, rowId),
          onDeselectAll: handleSelectionChange(instance, rowId),
          templates: {
            button: '<button type="button" class="multiselect dropdown-toggle custom-select" data-cy="multiselect-btn" data-toggle="dropdown" title="Select Billing Plans" style="width: 100%;"><span class="multiselect-selected-text"></span></button>'
          }
        });

        // Mark this select as initialized
        $select.addClass('multiselect-initialized');

        $select.multiselect('deselectAll', false);
        $select.multiselect('refresh');

        // Set initial values if they exist
        const selectedValues = row?.billingPlans?.map(plan => plan._id) || [];
        if (selectedValues.length) {
          $select.multiselect('select', selectedValues);
        }
      }
    });
  },

  rows() {
    const rowsObj = Template.instance().rows.all();
    return Object.entries(rowsObj).map(([id, data]) => ({
      id,
      ...data
    }));
  },
  getScheduleTypes() {
    return Template.instance().scheduleTypes.get();
  },
  disabledIfSelected(_id) {
    const rows = Template.instance().rows.all();
    const selectedTypes = [...new Set(Object.values(rows)
      .map(row => row.scheduleType)
      .filter(type => type && type.length > 0))];
    return selectedTypes.includes(_id) ? 'disabled': '';
  },
  billingPlans() {
    return Template.instance().billingPlans.get();
  },
  isSelected(scheduleTypeId, scheduleType) {
    return scheduleTypeId === scheduleType ? 'selected' : '';
  }
});

Template._scheduleTypeBillingPlanLinking.events({
  'click [data-action="delete-row"]'(event, template) {
    event.preventDefault();
    event.stopPropagation();

    const rowId = event.currentTarget.getAttribute('data-row-id');

    // Destroy only this specific multiselect
    const $select = $(`#billingPlanMultiSelect-${rowId}`);
    if ($select.hasClass('multiselect-initialized')) {
      $select.multiselect('destroy');
      $select.removeClass('multiselect-initialized');
    }

    // Remove from reactive dict
    template.rows.delete(rowId);
  },

  'change .schedule-type'(event, template) {
    const rowId = event.target.dataset.rowId;
    const row = template.rows.get(rowId);
    const update = {
      ...row,
      scheduleType: event.target.value
    };
    template.rows.set(rowId, update);
  },

  'click #addScheduleType'(event, template) {
    const newId = Random.id();
    template.rows.set(newId, {
      scheduleType: '',
      billingPlans: []
    });
  }
});

function handleSelectionChange(instance, rowId) {
  return function() {
    const selectedValues = $(this.$select).val() || [];
    const billingPlans = selectedValues.map(selectedId => {
      const plan = instance.billingPlans.get().find(plan => plan._id === selectedId);
      return {
        _id: plan._id,
        description: plan.description
      };
    });

    const row = instance.rows.get(rowId);
    const update = {
      ...row,
      billingPlans
    };
    instance.rows.set(rowId, update);
  }
}