import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './dashboardHeader.html';
import { Orgs, Org } from '../../../lib/collections/orgs';
import { User } from '../../../lib/collections/users';
import { processPermissions } from '../../../lib/permissions';

Template.dashboardHeader.helpers({
  "canSeeMySite": function() {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (person && person.type == "admin") return true;
    return false;
  },
  "canSeeExpressDriveUp": function() {
    const org = Orgs.current();
    if (!org) return false;

    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person) return false;
    if (org.hasCustomization("people/expressDriveUpBlock/enabled")) return false;
    return person.type == "admin" && (org.hasCustomization("people/familyCheckin/enabled") || org.hasCustomization("people/expressDriveUp/enabled"));
  },
  "canSeeDataExplorer"() {
    return Orgs.current() && (Orgs.current().hasCustomization("billing/enabled") || Orgs.current().registrationGuided ) &&
					processPermissions({
						assertions: [{ context: "billing/reports", action: "read"}],
						evaluator: (person) => person.type=="admin"
					}) && processPermissions({
                              assertions: [{ context: "billing/invoices", action: "read"}], 
                              evaluator: (person) => person.type=="admin"
                        })
  }
});
