<template name="mediaGallery">
  <div class="d-flex flex-column justify-content-center">
    <div class="card card-custom mx-8 mb-8">
      <div class="card-header flex-wrap border-0 pb-0">
        <div class="card-title">
          <h3 data-cy="media-gallery-page-label" class="card-label">Media Gallery</h3>
        </div>
        <div class="card-toolbar">
          <div class="col-md-3">
              <div class="form-group">
              <label data-cy="start-date-label">Start Date</label>
              <div class="input-group">
                <input data-cy="start-date" type="text" class="form-control pull-right" id="momentsStartDate" value="{{formattedStartDate}}">
              </div>
            </div>
          </div>
          <div class="col-md-3">
              <div class="form-group">
              <label data-cy="end-date-label">End Date</label>
              <div class="input-group">
                <input data-cy="end-date" type="text" class="form-control pull-right" id="momentsEndDate" value="{{formattedEndDate}}">
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label data-cy="media-type-label">Media Type</label>
              <div class="input-group">
                <select data-cy="select-media-type" class="form-control" id="mediaType">
                  <option value="internal">Internal</option>
                  <option value="external">External</option>
                </select>
              </div>
            </div>
          </div>
          <div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
            Update
          </div>
        </div>
      </div>
      <div class="card-body no-padding">
        <ul data-cy="img-validation" style="display:flex;flex-wrap: wrap;list-style-type: none;">
        {{#each media in results}}
          <li style="height: 40vh;flex-grow: 1;margin-top:4px;margin-bottom:4px;margin-left:8px;margin-right:8px;">
            <a href="{{getOriginal media}}" target="_blank">
              <img src="{{media.getTimelinePhoto}}" alt="Photo" style="max-height: 100%;min-width: 100%;object-fit: cover;vertical-align: bottom;" onload="showme(this);" onerror="imgError(this);" loading="lazy" data-hasvideo="{{media.isVideo}}">

              <!-- <img src="/w3images/lights.jpg" alt="Lights" style="width:100%"> -->
            </a>
          </li>
        {{/each}}
          <li style="flex-grow: 10;"></li>
        </ul>
      </div>
    </div>
  </div>
</template>
