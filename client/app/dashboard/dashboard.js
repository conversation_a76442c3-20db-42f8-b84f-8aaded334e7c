import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './dashboard.html';
import '../reports/reportOrgsField';
import './dashboardBuilder/_dashboardLayout';
import '../reports/reportOrgsField';

Template.dashboard.events({
  'click #kt_login_forgot': function(e) {
    $('#kt_login').removeClass('login-forgot-on');
    $('#kt_login').removeClass('login-signin-on');
    $('#kt_login').removeClass('login-signup-on');

    $('#kt_login').addClass('login-forgot-on');
  },
  'click #kt_login_forgot_cancel': function(e) {
    $('#kt_login').removeClass('login-forgot-on');
    $('#kt_login').removeClass('login-signin-on');
    $('#kt_login').removeClass('login-signup-on');

    $('#kt_login').addClass('login-signin-on');
  },
	'submit #login-form': function (e,t) {
		e.preventDefault();
		$("#login-button").prop('disabled', true).text("Logging in...");

		var email = t.find('#login-email').value,
		password = t.find('#login-password').value;
		if (!Meteor.status().connected)
			mpSwal.fire({
				title:"No Internet connection detected",
				text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
			});
		
		Meteor.callAsync("checkLoginInfo", email)
		.then((loginInfo)=>{
			Meteor.loginWithPassword(email, password, function(err) {
				$("#login-button").prop('disabled', false).text("Login");
				if (err) {
					console.log("error logging in:" + err);
					
					return mpSwal.fire({
						title: "Email or password incorrect",
						text: "Please try again",
						timer: 1700,
						showConfirmButton: false,
						type: "error"
					});
					
				}
				else {
					$("#login-button").prop('disabled', false).text("Login");
					return FlowRouter.go('dashboard');
					
				}
			});				
		})
		.catch((err)=>{
			console.log(err);
			$("#login-button").prop('disabled', false).text("Login");
		})

	}
});

Template.dashboard.onCreated( function () {
	var self = this;
	self.currentOrgs = new ReactiveVar();
});

Template.dashboard.helpers({
	"showLoginCreated": function() {
		return FlowRouter.getQueryParam('loginCreated') == "yes" ? true : false;
	},
	"greetingMessage": function() {
		//Good afternoon, Shannon.
		const hourOfDay = new Date().getHours(),
			currentPerson = Meteor.user() && Meteor.user().fetchPerson();
		let message = "Good ";
		if (hourOfDay < 12)
			message+= " morning, ";
		else if (hourOfDay < 18)
			message += " afternoon, ";
		else
			message += " evening, ";
		message += (currentPerson && currentPerson.firstName) + ".";
		return message;
	},
	"orgsFieldOpts": function() {
		const instance = Template.instance();
		return {
			onChange(options) {  instance.currentOrgs.set(options);}
		}
	},
	"currentOrgs": function() {
		return Template.instance().currentOrgs;
	}
});