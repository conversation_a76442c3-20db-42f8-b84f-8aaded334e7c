import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './mediaGallery.html';

Template.mediaGallery.created = function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportEndDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.mediaType = new ReactiveVar("internal");
  this.results = new ReactiveVar([]);
};

Template.mediaGallery.rendered = function() {
	$('#momentsStartDate').datepicker({autoclose:true});
	$('#momentsEndDate').datepicker({autoclose:true});
}

Template.mediaGallery.events({
	"click #btnUpdate": async function(event, instance) {
		event.preventDefault();
		$("#btnUpdate").text("Updating...").prop('disabled', true);

		instance.reportStartDate.set($("#momentsStartDate").val());
		instance.reportEndDate.set($("#momentsEndDate").val());
		instance.mediaType.set($("#mediaType").val());

		Meteor.callAsync("mediaGallery", {
			startDate: instance.reportStartDate.get(),
			endDate: instance.reportEndDate.get(),
			mediaType: instance.mediaType.get(),
		})
		.then((response) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			instance.results.set(response.results);
		})
		.catch((error) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			alert(error);
		});

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "media-gallery"
		});
	},
});

Template.mediaGallery.helpers({
  "getOriginal": function(media) {
    return media.originalPhoto || media.getTimelinePhoto;
  },
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formattedEndDate": function(){
		return Template.instance().reportEndDate.get();
	},
	"results": function() {
		return Template.instance().results.get();
	},
});
