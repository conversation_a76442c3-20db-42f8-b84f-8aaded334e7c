import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import './mediaReview.html';
import { MediaReviews } from '../../../lib/collections/mediaReview';
import _ from '../../../lib/util/underscore';
import '../../lib/js/mpweb';
import { showModal } from '../main';
import '../moments/partials/_videoPlayerModal';
import { Groups } from '../../../lib/collections/groups';
import { processPermissions } from '../../../lib/permissions';

const MEDIA_LIMIT = 10;

Template.mediaReview.onCreated( function() {
	var self = this;
	self.allItems = new ReactiveVar([]);
	self.page = new ReactiveVar(0);
	self.totalResults = new ReactiveVar(0);
});

Template.mediaReview.onRendered( function() {
	refreshResults(Template.instance());

});

Template.mediaReview.helpers({
  'getItemCountStart': function() {
    let itemCount = Template.instance().page.get() * MEDIA_LIMIT;
    const totalItems = Template.instance().totalResults.get();
    if (itemCount > totalItems) {
      return totalItems
    }
    return itemCount;
  },
  'getItemCountEnd': function() {
    return Template.instance().totalResults.get();
  },
  "mediaReviews": function() {
    return Template.instance().allItems.get();
  },
  "isMedia": function() {
		return (this && this.mediaFileType && _.contains(["image","video"],this.mediaFileType));
  },
  "getGroupName": function(groupId) {
    const group = Groups.findOne(groupId);
    return group && group.name;
  },
  "getActive": function(index) {
    return (index == 0) ? "active" : "";
  },
  "hasMoreThanOne": function(media) {
    const res = media && media.attachedMedia()
    return (res) ? res.length > 1 : false;
  },
  "waitForTimeLinePhoto": function(path) {
    setTimeout(() => {
      console.log('waiting...')
    }, 5000);
    return path;
  }
});

Template.mediaReview.events({
  "click .page-item-set": function (event, template) {
    event.preventDefault();
    const action = $(event.currentTarget).attr("data-action");
    const currentPage = template.page.get();
    if (action == "add") {
      template.page.set(currentPage + 1)
    } else if (action == "subtract" && currentPage > 0) {
      template.page.set(currentPage - 1);
    }
    refreshResults(template);
  },
  "click .media-approve": function (e, i) {
    if (!processPermissions({
      evaluator: (thisPerson) => thisPerson.type == "admin"
    })) return false;

    const mediaId = $(e.currentTarget).attr("data-id");
    Meteor.callAsync("approveMedia", mediaId)
    .catch((error)=>{
      mpSwal.fire("Error", error.reason, "error");
    });
  },
  "click .media-reject": function (e, i) {
    if (!processPermissions({
      evaluator: (thisPerson) => thisPerson.type == "admin"
    })) return false;

    const mediaId = $(e.currentTarget).attr("data-id");
    Meteor.callAsync("rejectMedia", mediaId)
    .catch(()=>{
      mpSwal.fire("Error", error.reason, "error");
    });
  },
  "click .carousel-indicators-top": function (e, i) {
    const momentId = $(e.currentTarget).attr("data-id");
    window.open("/moments/" + momentId, "_blank");
  },
  "click .showMediaLink": function (e) {
    e.preventDefault();
    var videoId = $(e.currentTarget).data("id");
    var tokenId = $(e.currentTarget).data("mediatoken");
    var currentMoment = MediaReviews.findOne(videoId);

    var currentMedia = _.find(currentMoment.attachedMedia(), function (m) {
      return m.mediaToken == tokenId
    });

    if (currentMedia && currentMedia.isVideo) {
      pathString = currentMedia.pathForVideoFormat("mp4") + "|" + currentMedia.pathForVideoFormat("webm");
      Session.set("videoPlayerLocation", pathString);
      showModal("_videoPlayerModal", {}, "#_videoPlayerModal");
    }
  },
  "click .carousel-control-next": function (e, i) {
    e.preventDefault();

  },
  "click .carousel-control-prev": function (e, i) {
    e.preventDefault();
  }
})

function refreshResults(instance, resetPage = false) {
	if (resetPage) instance.page.set(0);	
	const skip = instance.page.get() * MEDIA_LIMIT;

	const allItems = MediaReviews.find({}, {limit: MEDIA_LIMIT, skip })
	instance.totalResults.set(MediaReviews.find({}).count());
	instance.allItems.set(allItems);

}
