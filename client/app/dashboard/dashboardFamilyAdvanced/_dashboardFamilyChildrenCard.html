<template name='dashboardFamilyChildrenCard'>
	<!-- BEGIN children card -->
	<div class="card card-body card-custom mb-6" data-cy="children-widget">
		<!-- BEGIN children card header -->
		<div class="d-flex align-items-center justify-content-between">
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2m0 2a8 8 0 1 0 0 16a8 8 0 0 0 0-16m0 7q3 0 5 1a5 5 0 0 1-10 0q2-1 5-1M8.5 7a2.5 2.5 0 0 1 2.45 2h-4.9A2.5 2.5 0 0 1 8.5 7m7 0a2.5 2.5 0 0 1 2.45 2h-4.9a2.5 2.5 0 0 1 2.45-2"/></svg>
			</div>
			<div class="pl-5 font-size-h3 font-weight-bold" data-cy="children-widget-title">
				Children
			</div>
			<div class="ml-auto">
				{{#if showAddChildButton}}
					<button type="button" class="btn btn-outline-primary" id="addChildLink" data-cy="add-child-button">
						<i class="fa fa-plus"></i> Add
					</button>
				{{/if}}
			</div>
		</div>
		<!-- END children card header -->
		
		{{#if children}}
			<!-- BEGIN children list -->
			{{#each child in children }}
				<div class="d-flex mt-6" data-cy="child-card">
					<div class="flex-shrink-0 mt-2" style="width:100px">
						<a href="/people/{{child._id}}" data-cy="child-avatar-link">
							{{#unless child.hasAvatar}}
								<div class="d-flex people-card-user-img people-card-user-small align-items-center justify-content-center"
										style="background-color:{{getAvatarBackground child.personInitials}}">
									<span class="initials">{{child.personInitials}}</span>
								</div>
							{{else}}
								<div class="people-card-user-img people-card-user-small mb-4">
								<img class="people-card-center-cropped people-card-user-small" style="display:none;" src="{{child.getAvatarUrl}}" onload="showme(this);" onerror="imgError(this);" alt="User profile picture">
								<i class="icon-2x fad fa-spinner fa-spin timelinePhotoSpinner text-primary" style="display:none;margin-top:20px;"></i>
								</div>
							{{/unless}}
						</a>
					</div>
					<div class="flex-grow-1">
						<div class="font-size-h3 font-weight-bold">
							<a href="/people/{{child._id}}" data-cy="child-name-link" style="color:unset">{{child.firstName}} {{child.lastName}}</a>
						</div>
						<div class="font-size-h6">
							{{child.orgName}}
						</div>
						<div>
							{{#each program in child.currentPrograms}}
								<svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" style="vertical-align: middle;"><path fill="#BDBDBD" d="M7 1v2H3a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h7.755A8 8 0 0 1 22 9.755V4a1 1 0 0 0-1-1h-4V1h-2v2H9V1zm16 15a6 6 0 1 1-12 0a6 6 0 0 1 12 0m-7-4v4.414l2.293 2.293l1.414-1.414L18 15.586V12z"/></svg>
								<span class="font-weight-bolder">{{scheduleTypeNameFor program.scheduleType}}</span>
								{{mapDaysToText program.recurringDays}}<br/>
							{{/each}}
							<svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 24 24" style="vertical-align: middle;"><path fill="#BDBDBD" d="M23 19h-1V9h-4V6.586l-6-6l-6 6V9H2v10H1v2h22zM6 19H4v-8h2zm12-8h2v8h-2zm-7 1h2v7h-2z"/></svg>
							<a href="/people/{{child._id}}#programs" data-cy="child-programs-link"> Registered in {{child.enrolledPrograms.length}} program(s)</a> &gt;
						</div>
					</div>
				</div>
				<div class="d-flex mt-6">
						<a data-cy="add-programs-button" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center" class="bg-wl-lighter-primary fg-wl-primary mr-8" href="/people/{{child._id}}#programs" alt="Add New Programs" data-toggle="tooltip" data-placement="top" title="Add New Programs">
							<svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 24 24" alt="Add New Programs"><path fill="currentColor" d="M7 1v2H3a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h7.755A8 8 0 0 1 22 9.755V4a1 1 0 0 0-1-1h-4V1h-2v2H9V1zm16 15a6 6 0 1 1-12 0a6 6 0 0 1 12 0m-7-4v4.414l2.293 2.293l1.414-1.414L18 15.586V12z"/></svg>
						</a>
					
						<a data-cy="express-drive-up-button" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center" class="bg-wl-lighter-primary fg-wl-primary mr-8 child-widget-express-drive-up-btn" href="#" alt="Express Drive Up" data-toggle="tooltip" data-placement="top" title="Express Drive Up" data-child-id="{{child._id}}">
							<svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 24 24" alt="Express Drive Up"><path fill="currentColor" d="M19 20H5v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-9l2.513-6.702A2 2 0 0 1 6.386 4h11.228a2 2 0 0 1 1.873 1.298L22 12v9a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1zM4.136 12h15.728l-2.25-6H6.386zM6.5 17a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m11 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3"/></svg>
						</a>
				</div>
			{{/each}}	 
			<!-- END children list -->
		{{else}}
			<p class="font-size-h4 mt-12" data-cy="children-widget-empty-state-message">It looks like you need to register a child at this location! Tap the "Add +" button above to get started.</p>
		{{/if}}
	</div>
	<!-- END children card -->
</template>