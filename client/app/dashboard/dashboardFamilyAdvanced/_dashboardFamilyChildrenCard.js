import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { People } from '../../../../lib/collections/people';
import { Orgs } from '../../../../lib/collections/orgs';
import { Relationships } from '../../../../lib/collections/relationships';
import { showModal, hideModal } from "../../../../client/app/main";
import { EnrollmentUtils } from '../../../../lib/util/enrollmentUtils';
import { RegistrationDesignationUtils } from '../../../../lib/registrationDesignationUtils';
import { AvailableCustomizations } from '../../../../lib/customizations';
import "./_dashboardFamilyExpressDriveUp";
import './_dashboardFamilyChildrenCard.html';


Template.dashboardFamilyChildrenCard.onCreated(function() {
	const instance = this;
	instance.childrenWithPrograms = new ReactiveVar([]);

	instance.autorun(async () => {
		const user = Meteor.user();
		if (!user) return;

		instance.subscribe('familyChildren', user.personId);

		const relationshipIds = Relationships.find({ personId: user.personId, relationshipType: 'family' }).map(r => r.targetId);
		const activeChildren = People.find({ _id: { $in: relationshipIds } }).fetch().map(p => {
			p.orgName = Orgs.current().name;
			return p;
		});

		for (const child of activeChildren) {
			const { _id, defaultGroupId } = child;
			const { reservations, selectiveWeekPlans } = await EnrollmentUtils.buildEnrolledPrograms(_id, defaultGroupId);

			child.enrolledPrograms = reservations;
			child.currentPrograms = reservations.filter(r => r.scheduledDate <= moment().startOf('day').valueOf());
			child.enrolledSelectiveWeekPlans = selectiveWeekPlans;
		}

		const sortedChildren = _.sortBy(activeChildren, c => c.lastName + ',' + c.firstName);
		instance.childrenWithPrograms.set(sortedChildren);
	});
});

Template.dashboardFamilyChildrenCard.helpers({
	children() {
		return Template.instance().childrenWithPrograms.get();
	},
	scheduleTypeNameFor(id) {
        const scheduleTypeDefinition = Orgs.current().getScheduleTypes().find(st => st._id === id);
        return scheduleTypeDefinition && scheduleTypeDefinition.type;
    },
	mapDaysToText(days) {
		return days && days.length > 0 && ("-- " + days.map(d => moment(d, 'dddd').format('ddd')).join(', '));
	},
	showAddChildButton() {
		return Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	}
});

Template.dashboardFamilyChildrenCard.events({
	'click #addChildLink': function(event, template) {
		console.log("clicked");
		event.preventDefault();
		const currentOrg = Orgs.current();
		const orgId = currentOrg._id;
		const currentPerson = Meteor.user().fetchPerson();
		const queryParams = { orgId: orgId, personId: currentPerson._id };
		const personDesignation = RegistrationDesignationUtils.getPersonDesignation(currentPerson);
		if (personDesignation) {
			queryParams.designation = personDesignation;
		}
		const queryString = Object.keys(queryParams)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
			.join('&');
		let baseUrl = (currentOrg && currentOrg.whiteLabel && currentOrg.whiteLabel.ROOT_URL) || Meteor.absoluteUrl().replace(/\/+$/, "");
		const url = `${baseUrl}/registration?${queryString}`;
		window.location.href=url;
	},
	'click .child-widget-express-drive-up-btn': async function(event, template) {
		const childId = event.currentTarget.getAttribute('data-child-id');
		const currentPerson = People.findOne(childId);
		const childName = currentPerson.firstName + ' ' + currentPerson.lastName;
		Session.set("activeMomentType", "pinCodeCheckinForm");
		showModal("simpleModal", {
            title: "Express Drive-Up For " + childName,
            template: "dashboardFamilyExpressDriveUp",
            hideCancelButton: false,
            actionButtonLabel: 'Save',
            justifyFooter: 'center',
            data: {
                _id: childId,
            },
            onSave: (saveEvent, i, formFieldData) => {
				const driveUpType = $("input[name='express-drive-up-type']").val();
				if (driveUpType==="arriving") {
					const attendingValue = $("#expressDriveUpAttendingToday").val();
					const attending = attendingValue === 'Yes';
					const checkInData = {
						personId: currentPerson._id,
						checkinFormFields: {}
					}
					if (attending) {
						let pinCodeFormFields = {};
						let requiredError = false;
						let requiredMessage = "";
						checkInData["checkinFormFields"]["attending"] = true;	
						checkInData["checkinFormFields"]["dropOffTimeEstimate"] = $("input[name='arrival-time-estimate']").val();
						_.each(Orgs.current().pinCodeCheckinFields(), function (ff) {
							switch (ff.fieldType) {
								case "text":
								case "string":
								case "customerDefinedList":
								case "select":
									pinCodeFormFields[ff.dataId] = $("#dashboardFamilyExpressDriveUpForm #momentField" + ff.dataId).val();
									break;
								case "buttons":
									pinCodeFormFields[ff.dataId] = $("#dashboardFamilyExpressDriveUpForm #momentField" + ff.dataId + " button.active").data("value");
									break;
								case "timePicker":
									const timeMoment = new moment($("#dashboardFamilyExpressDriveUpForm #momentField" + ff.dataId).val(), "HH:mm");
									if (timeMoment.isValid()) pinCodeFormFields[ff.dataId] = timeMoment.format("h:mm a");
									break;
							}

							const testStr = pinCodeFormFields[ff.dataId] ? pinCodeFormFields[ff.dataId] : "";
							if (ff.required && currentPerson && currentPerson.checkedIn !== true && testStr.trim().length === 0) {
								requiredError = true;
								requiredMessage += `${requiredMessage != "" ? ', ': ''}${ff.label}`;
							}

							if (!ff.hideComment) {
								pinCodeFormFields["comment"] = $("#dashboardFamilyExpressDriveUpForm #comment").val();
							}
						});
						
						if (requiredError) {
							$(".modal-body").scrollTop(0);
							$(saveEvent.target).html('Save').prop("disabled", false);
							$("#dashboardFamilyExpressDriveUpForm #momentFieldError").html("Please complete the following fields: " + requiredMessage);
						} else {
							checkInData["checkinFormFields"] = { ...pinCodeFormFields, ...checkInData["checkinFormFields"] };
						}
					} else { // not attending
						const notAttendingSelection = attendingValue.replace('No - ', '');	
						checkInData["checkinFormFields"]["attending"] = false;
						checkInData["checkinFormFields"]["attendingAbsenceReason"] = notAttendingSelection;
						checkInData["checkinFormFields"]["attendingAbsenceComment"] = $("input[name='reason-not-attending']").val();
					}

					Meteor.callAsync("familyCheckin", checkInData).then(result => {
						$("#simpleModal").modal('hide');
					}).catch(error => {
						$(saveEvent.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				} else { //departing
					const departureTimeChoice = $("input[name='arrival-time-estimate']").val();
					if (departureTimeChoice === "none") {
						Meteor.callAsync("expressDriveUpCancel", { personId: currentPerson._id }).then(result => {
							$("#simpleModal").modal('hide');
						}).catch(error => {
							$(saveEvent.target).html('Save').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
					} else {
						const checkOutData = {
							personId: currentPerson._id,
							pickUp: true,
							minutes: departureTimeChoice
						};
							
						Meteor.callAsync("expressDriveUpTimeEstimate", checkOutData).then(result => {
							$("#simpleModal").modal('hide');
						}).catch(error => {
							$(saveEvent.target).html('Save').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
					}
				}
			}
		});
	}
});