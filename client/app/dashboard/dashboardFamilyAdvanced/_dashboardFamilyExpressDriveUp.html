<template name="dashboardFamilyExpressDriveUp">
	<form id="dashboardFamilyExpressDriveUpForm">
		{{#if isArriving}}
			<input type="hidden" name="express-drive-up-type" value="arriving">
			<div id="momentFieldError" class="text-size-h6 text-danger pb-6"></div>
			<div class="form-group row align-items-center">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Attending today?</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
				
					<select class="form-control" id="expressDriveUpAttendingToday">
						<option value="Yes">Yes</option>
						<option value="No - sick">No - sick</option>
						<option value="No - vacation">No - vacation</option>
						<option value="No - other">No - other</option>
					</select>
				
				</div>
			</div>
			{{#if attending}}
				<div id="momentForm" data-id="{{childId}}">
					<div class="form-group row align-items-center">
						<label class="col-xl-3 col-lg-3 text-right col-form-label">Estimated arrival time</label>
						<div class="col-lg-6 col-md-9 col-sm-12">
						
							<div class="btn-group" role="group" aria-label="Estimated arrival time">
								<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '~10 minutes away'}}" data-value="~10 minutes away">~10 minutes away</button>
								<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '~20 minutes away'}}" data-value="~20 minutes away">~20 minutes away</button>
								<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '~30 minutes away'}}" data-value="~30 minutes away">~30 minutes away</button>
								<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '30+ minutes away'}}" data-value="30+ minutes away">30+ minutes away</button>
								<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival null}}" data-value="Remove arrival time">None</button>
							</div>
							<input type="hidden" name="arrival-time-estimate" value="">
						</div>
					</div>
					{{> dynamicMomentForm dynamicFieldValues=familyDataToDynamicFields}}
				</div>
			{{else}}
				<div class="form-group row align-items-center">
					<label class="col-xl-3 col-lg-3 text-right col-form-label">Reason not attending</label>
					<div class="col-lg-6 col-md-9 col-sm-12">
						<input type="text" class="form-control" id="expressDriveUpReasonNotAttending" name="reason-not-attending" value="">
					</div>
				</div>
			{{/if}}

		{{else}}
			<input type="hidden" name="express-drive-up-type" value="departing">
			<div class="form-group row align-items-center">
				<label class="col-xl-3 col-lg-3 text-right col-form-label">Estimated arrival time</label>
				<div class="col-lg-6 col-md-9 col-sm-12">
				
					<div class="btn-group" role="group" aria-label="Estimated arrival time">
						<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '10'}}" data-value="10">~10 minutes away</button>
						<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '20'}}" data-value="20">~20 minutes away</button>
						<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '30'}}" data-value="30">~30 minutes away</button>
						<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival '40'}}" data-value="45">30+ minutes away</button>
						<button type="button" class="btn btn-outline-secondary estimated-arrival-btn {{activeIfEq currentEstimatedArrival 'none'}}" data-value="none">None</button>
					</div>
					<input type="hidden" name="arrival-time-estimate" value="">
				</div>
			</div>
		{{/if}}
	</form>
</template>