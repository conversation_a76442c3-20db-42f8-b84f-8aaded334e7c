<template name="dashboardFamilyDocuments">

	{{#if showDocumentsCard}}
	<!-- BEGIN documents card -->
	<div class="card card-body card-custom mb-6">
		<!-- BEGIN documents card header -->
		<div class="d-flex align-items-center justify-content-between">
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#000" d="M4 4a2 2 0 0 1 2-2h8a1 1 0 0 1 .707.293l5 5A1 1 0 0 1 20 8v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm13.586 4L14 4.414V8zM12 4H6v16h12V10h-5a1 1 0 0 1-1-1zm-4 9a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1"/></svg>
			</div>
			<div class="pl-5 font-size-h3 font-weight-bold">
				Documents
			</div>
			<div class="ml-auto">
				
			</div>
		</div>
		<!-- END documents card header -->

		<!-- BEGIN documents list -->
		{{#each child in children}}
		{{#if hasDocuments child}}
		
			
			{{> _documents child=child.person collapseLayout=true}}
			
		
		{{/if}}
		{{/each}}
		
		{{#if noDocuments}}
		<div class="pt-6">
        	<div class="font-size-h3 font-weight-bold" style="color:unset">
				You’re all caught up! To review completed documents, please visit your child’s profile.
			</div>
		</div>
		{{/if}}
		
		
		<!-- END documents list-->

	</div>
	<!-- END documents card -->
	{{/if}}

</template>