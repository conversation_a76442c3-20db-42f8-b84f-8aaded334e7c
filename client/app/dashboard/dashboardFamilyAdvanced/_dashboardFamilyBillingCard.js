import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_dashboardFamilyBillingCard.html';

Template.dashboardFamilyBillingCard.helpers({
	autopayStatus() {
		const person = Meteor.user().fetchPerson();
		const billingStatus = person.billingStatus();
		return {
			HAS_AMOUNT_DUE: billingStatus.hasAutoPay && billingStatus.autoPayAmountFuture > 0,
			NO_PAYMENT_DUE: billingStatus.hasAutoPay && billingStatus.autoPayAmountFuture < 0.01,
			AUTOPAY_NOT_SETUP: !billingStatus.hasAutoPay
		};
	},
	currentPerson() {
		return Meteor.user().fetchPerson();
	}
});