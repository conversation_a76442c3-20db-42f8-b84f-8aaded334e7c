import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import "./_dashboardFamilyChildrenCard";
import "./_dashboardFamilyWMGCard";
import "./_dashboardFamilyDocuments";
import "./_dashboardFamilyBillingCard";
import "./_dashboardFamilyDiscoverProgramsCard";
import './dashboardFamilyAdvanced.html';

import { RegistrationDesignationUtils } from "../../../../lib/registrationDesignationUtils";

Template.dashboardFamilyAdvanced.onCreated(function() {
	this.initialDataLoading = new ReactiveVar(true);
	this.autorun(() => {
		if (this.subscriptionsReady() && this.initialDataLoading.get()) {
			this.initialDataLoading.set(false);
		}
	});
});

Template.dashboardFamilyAdvanced.helpers({
	'dashboardDataLoading'() {
		return Template.instance().initialDataLoading.get();
	},
	
});

Template.dashboardFamilyAdvanced.events({
	
});