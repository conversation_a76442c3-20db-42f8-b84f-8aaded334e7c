import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './dashboardFamilyAdvancedHeader.html';

Template.dashboardFamilyAdvancedHeader.onCreated(async function () {
	const self = this;
	this.memberships = new ReactiveVar();
	Meteor.callAsync('getSwitchableMemberships').then((res) => {
		if (res && res.length > 0) {
			self.memberships.set(res);
		}
	}).catch((err) => {
		console.error('Error fetching switchable memberships:', err);
	});
});

Template.dashboardFamilyAdvancedHeader.helpers({
	memberships() {
		return Template.instance().memberships.get();
	},
	showSwitchableDropdown() {
		return (Template.instance().memberships.get() || []).length > 0;
	}
});

Template.dashboardFamilyAdvancedHeader.events({
	"click .switch-membership-entry"(event, instance) {
		const membershipId = event.currentTarget.getAttribute("data-value");
		Meteor.callAsync('switchUserMembership', membershipId).then((result) => {
			location.replace("/loading?setLoc=home");
		}).catch((error) => {
			mpSwal.fire("Error", error.reason, "error");
		});

	}
});