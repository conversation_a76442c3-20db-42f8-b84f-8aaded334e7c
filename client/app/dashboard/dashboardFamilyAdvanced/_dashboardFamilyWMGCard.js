import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_dashboardFamilyWMGCard.html';

Template.dashboardFamilyWMGCard.helpers({
	getWmgLabel() {
		return Session.get("wmgLabel");
	}
});

Template.dashboardFamilyWMGCard.events({
	"click .wmgButton": function(e, i) {
		$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Cuteness Loading').prop("disabled", true);
		Meteor.callAsync("getWmgToken")
		.then((result)=>{
			$(e.target).html(Session.get("wmgLabel")).prop("disabled", false);
			const url = "https://presence.internal.watchmegrow.com/v1/embed/player";
			const form = document.createElement("form");
			form.setAttribute("method", "post");
			form.setAttribute("action", url);
			form.setAttribute("target", "view");
			const hiddenField = document.createElement("input"); 
			hiddenField.setAttribute("type", "hidden");
			hiddenField.setAttribute("name", "jwt");
			hiddenField.setAttribute("value", result);
			form.appendChild(hiddenField);
			document.body.appendChild(form);
			window.open('', 'view');
			form.submit();
		})
		.catch((error)=>{
			$(e.target).html(Session.get("wmgLabel")).prop("disabled", false);
			mpSwal.fire("Error", error.reason, "error");
		})
	}
})