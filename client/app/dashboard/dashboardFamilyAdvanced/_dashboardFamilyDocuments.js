import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Person } from '../../../../lib/collections/people';
import { Orgs } from '../../../../lib/collections/orgs';
import './_dashboardFamilyDocuments.html';

Template.dashboardFamilyDocuments.onCreated(function() {
	this.children = new ReactiveVar([]);
	
	let children = []
	const familyPerson = Meteor.user().fetchPerson()
	Meteor.callAsync("getFamilyPeople", familyPerson._id).then((result) => {
		
		const children = [];
		result?.familyPeople?.forEach((fp) => {
			if (fp.relationshipType === 'family' && !children.find((c) => c.person._id === fp.person._id)) {
				const thisPerson = new Person(fp.person);
				children.push( { 
					person: thisPerson,
					hasDocuments: showDocs(thisPerson)
				} );
			}
		});
		
		this.children.set(children);
	}).catch((error) => {
		console.log("Error", error);
	});
	
});

Template.dashboardFamilyDocuments.helpers({
	
	children() {
		const children = Template.instance().children.get();
		if (children) {
			return children;
		}
	},
	showDocumentsCard() {
		const children = Template.instance().children.get();
		return true; //return children && children.find((c) => c.hasDocuments);
	},
	hasDocuments(child) {
		return showDocs(child);
	},
	noDocuments() {
		const children = Template.instance().children.get();
		const userPerson = Meteor.user().fetchPerson();
		const anyDocs = children.filter((child) => {
			const childPerson = child.person;
			const currentDocumentItems = childPerson.documentRepositoryItems(userPerson, false) || [];
			console.log("childPerson name", childPerson.firstName, "currentDocumentItems", currentDocumentItems);
			return currentDocumentItems.length > 0;
		});
		console.log("anyDocs", anyDocs);
		return anyDocs.length == 0;
	}
});

function checkStringsIncluded(activeDocs, childDocs) {
	for (let i = 0; i < activeDocs.length; i++) {
		if (!childDocs.includes(activeDocs[i])) {
			return true;
		}
	}
	return false;
}

function showDocs(child) {
	const docs = Orgs.current().documentDefinitions || [];
	const activeDocs = docs.filter((doc) => !doc.deletedAt && !doc.hideNotUploaded && doc.assignmentType === "all").map((doc) => doc._id);
	const individualDocs = docs.filter((doc) => !doc.deletedAt && !doc.hideNotUploaded && doc.assignmentType === "individuals").map((doc) => doc._id);
	const groupDocs = docs.filter((doc) => !doc.deletedAt && !doc.hideNotUploaded && doc.assignmentType === "groups");
	if (groupDocs.length > 0 && child.defaultGroupId) {
		groupDocs.forEach((doc) => {
		if (doc.selectedGroupIds.includes(child.defaultGroupId)) {
			activeDocs.push(doc._id)
		}	
		})
	}
	const childDocs = child.documentItems ? Object.keys(child.documentItems) : [];
	const assignedDocs = child.documentAssignments ?? [];
	const filteredIndivDocs = childDocs.length > 0 ? individualDocs.filter((doc) => assignedDocs.includes(doc)) : individualDocs;
	if (filteredIndivDocs.length > 0) {
		activeDocs.push(...filteredIndivDocs);
	}
	return checkStringsIncluded(activeDocs, childDocs);
}