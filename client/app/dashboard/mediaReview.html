
<template name="mediaReview">
  <div class="d-flex flex-column justify-content-center">
    <div class="card card-custom mx-8 mb-8">
      <div class="card-header flex-wrap border-0 pb-0">
        <div class="card-title">
          <h3 class="card-label" data-cy="media-review-card-h3">Media Review</h3>
        </div>
        <div class="d-flex flex-row ml-6 justify-content-center align-items-center">
          <span class="page-item-set" style="cursor:pointer;" data-action="subtract" data-cy="prev-pagination-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
          <span class="font-size-h2" data-cy="pagination-item-count">{{getItemCountStart}} - {{getItemCountEnd}}</span>
          <span class="page-item-set" style="cursor:pointer;" data-action="add" data-cy="next-pagination-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
        </div>
      </div>
      <div class="card-body table-responsive no-padding">
        <table class="table" data-cy="media-review-table">
          <tbody>
            {{#each media in mediaReviews}}
              <tr>
                <td style="width:40%" >
                  <div id="{{media.momentId}}" class="carousel slide" data-interval="false">
                    <div class="carousel-indicators-top" style=" border-radius:30px; background-color: #fff; cursor: pointer;" data-id="{{media.momentId}}">
                      <div class="btn btn-primary font-weight-bolder min-w-175px mr-2" data-cy="moment-details-btn">
                        <i class="fad-regular fad fa-link mr-2" ></i>Moment Details
                      </div>
                    </div>
                    <div class="carousel-indicators-bottom">
                      <div id="mediaApprove" class="btn btn-success font-weight-bolder min-w-175px media-approve mr-2" style="z-index: 15;" data-id="{{media._id}}" data-cy="approve-all-btn">
                        <i class="fad-regular fad fa-user-check fa-swap-opacity mr-2" ></i>Approve All
                      </div>
                      <div id="mediaReject" class="btn btn-danger font-weight-bolder min-w-175px media-reject ml-2" style="z-index: 15;"  data-id="{{media._id}}" data-cy="reject-all-btn">
                        <i class="fad-regular fad fa-user-check fa-swap-opacity mr-2" ></i>Reject All
                      </div>
                    </div>
                    <div class="carousel-inner">
                      {{#each media.attachedMedia}}
                        <div class="carousel-item {{getActive @index}}">
                          {{#if isMedia}}
                            <div class="image-wrap d-block justify-content-center">
                              <img src="{{getTimelinePhoto}}" class="showMediaLink d-block {{#if trueIfEq isVideo true}}video cursor-pointer{{/if}}" alt="{{#if trueIfEq isVideo true}}Video{{else}}Photo{{/if}}" style="display:none;border-radius:16px;margin-left: auto;margin-right: auto;" onload="showme(this);" onerror="imgError(this);" data-id="{{media._id}}" data-mediatoken="{{mediaToken}}" data-hasvideo="{{isVideo}}">
                            </div>
                            <i class="icon-2x fad fa-spinner fa-spin timelinePhotoSpinner"></i>
                          {{else}}
                            <div class="font-weight-bolder d-block mt-2 text-center" style="margin-left: auto;margin-right: auto;min-height:96px;" onload="showme(this);" data-id="{{media._id}}">
                                <b><i class="text-bright-blue fad fa-paperclip mr-2"></i> Attachment:</b> <a href="{{getTimelinePhoto}}" target="_blank">{{#if fileName}}{{fileName}}{{else}}View{{/if}}</a><br/>
                            </div>
                          {{/if}}
                        </div>
                      {{/each}}
                    </div>
                    {{#if hasMoreThanOne media}}
                      <a class="carousel-control-prev" href="#{{media.momentId}}" role="button" data-slide="prev">
                        <span class="fad fad-primary fa-chevron-left icon-2x" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                      </a>
                      <a class="carousel-control-next"  href="#{{media.momentId}}" role="button" data-slide="next">
                        <span class="fad fad-primary fa-chevron-right icon-2x" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                      </a>
                    {{/if}}
                  </div>
                </td>
              </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
