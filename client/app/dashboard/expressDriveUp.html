<template name="expressDriveUp">
  <div class="d-flex flex-column justify-content-center">
    <div class="card card-custom mx-8 mb-8">
      <div class="card-header flex-wrap border-0 pb-0">
        <div class="card-title">
          <h3 class="card-label" data-cy="arrived-report-title">Arrived</h3>
        </div>
        <div class="card-toolbar">
          <div class="btn btn-primary font-weight-bolder btn-text-white" id="checkInOutAllHere" data-cy="check-in-out-btn">
            Check In/Out All
          </div>
        </div>
      </div>
      <div class="card-body table-responsive no-padding">
        <table class="table table-hover" data-cy="arrived-report-table">
          <tbody>
            <tr>
              <th style="width:20%">Name</th>
              <th style="width:20%">Arrival Time</th>
              <th style="width:20%">Group</th>
              <th style="width:20%">Action</th>
            </tr>

            {{#each person in peopleHere}}
              <tr>
                <td style="width:20%" data-cy="person-arrival-full-name"><a href="/people/{{person._id}}">{{person.firstName}} {{person.lastName}}</a></td>
                <td style="width:20%" data-cy="arrival-time">{{getArrivalTime person}}</td>
                <td style="width:20%" data-cy="group-arrival" >{{getGroupName person}}</td>
                <td style="width:20%">
                  <div class="btn {{getCheckButtonColor person}} font-weight-bolder min-w-175px express-check-in-out-btn" data-id="{{person._id}}" data-cy="waiting-for-approval">
                    <i class="fad-regular fad {{getCheckButtonIcon person}} fa-swap-opacity mr-2" ></i>{{getCheckButtonText person}}
                  </div>
                </td>
              </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
    </div>
    <div class="card card-custom mx-8 mb-8">
      <div class="card-header flex-wrap border-0 pb-0">
        <div class="card-title">
          <h3 class="card-label" data-cy="on-the-way-report-title">On the way</h3>
        </div>
      </div>
      <div class="card-body table-responsive no-padding">
        <table class="table table-hover" data-cy="on-the-way-report-table">
          <tbody>
            <tr>
              <th style="width:20%">Name</th>
              <th style="width:20%">Estimated Time</th>
              <th style="width:20%">Group</th>
              <th style="width:20%">Action</th>
            </tr>

            {{#each person in arrivals}}
              <tr>
                <td style="width:20%" data-cy="person-on-the-way-full-name"><a href="/people/{{person._id}}">{{person.firstName}} {{person.lastName}}</a></td>
                <td style="width:20%" data-cy="estimated-time">{{getEstimatedTime person}}</td>
                <td style="width:20%" data-cy="group-on-the-way">{{getGroupName person}}</td>
                <td style="width:20%" data-cy="waiting-for-approval-col">
                  <div class="btn {{getCheckButtonColor person}} font-weight-bolder min-w-175px express-check-in-out-btn" data-id="{{person._id}}" data-cy="waiting-for-approval">
                    <i class="fad-regular fad {{getCheckButtonIcon person}} fa-swap-opacity mr-2"></i>{{getCheckButtonText person}}
                  </div>
                </td>
              </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
