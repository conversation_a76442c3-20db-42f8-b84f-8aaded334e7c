import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { PeopleUtils } from '../../../../../lib/util/peopleUtils';
import { getPeopleData } from '../../../../services/peopleMeteorService';
import './_widgetActivity.html';
import { Moments } from '../../../../../lib/collections/moments';
import { Groups } from '../../../../../lib/collections/groups';
import '../../../../layout/loading';

Template.widgetActivity.onCreated( function() {
	this.latestActivities = new ReactiveVar([]);
	this.autorun(() => {
		const outputMoments = [];
		const startOfDay = new moment().startOf('day').valueOf()
		const moments = Moments.find({},{limit:10, sort:{sortStamp:-1}})
		const taggedPeopleIds = moments.fetch().flatMap((m)=>{
			return [m.owner,m.taggedPeople]
		})
		const peopleIds = [... new Set(taggedPeopleIds.flat())]
		getPeopleData({_id:{$in:peopleIds}}).then((peopleRes) => {
			if (peopleRes.length) {
				moments.map((currentMoment)=>{
					if (currentMoment.sortStamp < startOfDay) {
						currentMoment.timeDescription = new moment(currentMoment.sortStamp).format("M/D/YY");
					} else {
						currentMoment.timeDescription = new moment(currentMoment.sortStamp).format("h:mm a");
					}
					momentOwner =  peopleRes[peopleRes.findIndex(p => p._id === currentMoment.owner)];
					const momentOwnerName = PeopleUtils.getNamePreferredLast(momentOwner);
					if (currentMoment.momentType === "checkin" && momentOwner) {
						currentMoment.momentDescription = `<a href='/people/${ momentOwner._id }#activity'> ${ momentOwnerName }</a> was checked in`;
						if (currentMoment.checkInGroupId) {
							const g = Groups.findOne(currentMoment.checkInGroupId, { fields: { name: 1 } });
							if (g) {
								currentMoment.momentDescription+= " to " + g.name;
							}
						}
						currentMoment.iconColor = "text-success";
					} else if (currentMoment.momentType === "checkout" && momentOwner) {
						currentMoment.momentDescription = `<a href='/people/${ momentOwner._id }#activity'> ${ momentOwnerName }</a> was checked out`;
					} else if (currentMoment.momentType === "checkout" && momentOwner) {
						currentMoment.momentDescription = `<a href='/people/${momentOwner._id}#activity'> ${momentOwner.firstName} ${momentOwner.lastName}</a> was checked out`;
						if (currentMoment.checkOutGroupId) {
							const g = Groups.findOne(currentMoment.checkOutGroupId, { fields: { name: 1 } });
							if (g) {
								currentMoment.momentDescription+= " of " + g.name;
							}
						}
						currentMoment.iconColor = "text-danger";
					} else if (currentMoment.momentType === "move" && momentOwner) {
						currentMoment.momentDescription = `<a href='/people/${ momentOwner._id }#activity'> ${ momentOwnerName }</a> was moved`;
						if (currentMoment.checkOutGroupId) {
							const g = Groups.findOne(currentMoment.checkOutGroupId, { fields: { name: 1 } });
							if (g) {
								currentMoment.momentDescription+= " from " + g.name;
							}
						}
						if (currentMoment.checkInGroupId) {
							const g = Groups.findOne(currentMoment.checkInGroupId, { fields: { name: 1 } });
							if (g) {
								currentMoment.momentDescription+= " to " + g.name;
							}
						}
						currentMoment.iconColor = "text-danger";
					} else {
						const momentPoster = currentMoment.attributionName || "";
						currentMoment.momentDescription = momentPoster + ` posted a <a href='/moments/${currentMoment._id}'>${currentMoment.momentTypePretty} moment</a>`;
						if (currentMoment.taggedPeople.length === 1) {
							// const targetPerson = await getPeopleById(currentMoment.taggedPeople[0]);
							const targetPerson = peopleRes[peopleRes.findIndex(p => p._id === currentMoment.taggedPeople[0])];
							const targetPersonName = PeopleUtils.getNamePreferredLast(targetPerson);
							currentMoment.momentDescription += ` for <a href='/people/${ targetPerson._id }#activity'>${ targetPersonName }</a>`;
						} else {
							currentMoment.momentDescription += " for " + currentMoment.taggedPeople.length + " people"
						}
						currentMoment.iconColor = "text-warning";
					}
					outputMoments.push(currentMoment);
				})
				this.latestActivities.set(outputMoments)
			}
		}).catch(err => {
			console.log(err);
		});
	})
});

Template.widgetActivity.helpers({
	checkLoading:function(){
		return Session.get("isLoading");
	},
	items(){
		return Template.instance().latestActivities.get()
	}
});
