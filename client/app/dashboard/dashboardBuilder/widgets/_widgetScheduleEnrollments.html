<template name="widgetScheduleEnrollments">
	<style>
		.table td,th {
			border: none !important;
		}
		.wfilter {
			max-width: 50% !important;
		}
		.title {
			font-size: 16px !important;
		}
		.container {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px; /* Optional: for rounded corners */
        }

        .button {
            padding: 10px 20px;
            background-color: var(--primary);
			color: white;
			width: 50%;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

	</style>
	<div class="box box-default">
		{{#if isLoading}}
			<div class="card card-custom gutter-b">
				{{> loading fontSize="50px"}}
			</div>
		{{else}}
			<div data-cy="widget-schedule-enrollments" class="card card-custom gutter-b">
				<!--begin::Header-->
				<div class="card-header align-items-center border-0 mt-4">
					<h3 class="card-title align-items-start flex-column">
						<span data-cy="this-weeks-schedule" class="font-weight-bolder text-dark">This Week's Schedule</span>
						<span data-cy="this-weeks-schedule-desc" class="text-muted mt-3 font-weight-bold font-size-sm">Children schedules/enrollment cap</span>
					</h3>
					<!--filter::section-->
					<div class="wfilter col text-center mt-5">
						<div style="background-color: #f8f9fa; padding: 10px;border-radius: 5px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);" class="grey-box">
							<span data-cy="filter-value" id="filter-value">{{getRangeFilter}}</span>
							<span data-cy="filter-btn" id="btnFilter" style="cursor:pointer;float: right;"><i class="fad fad-primary fa-calendar-day icon-2x"></i></span>
						</div>
					</div>
					<!--filter::section-->
				</div>
				<!--end::Header-->
				{{#if isContentLoading}}
				<div class="card card-custom gutter-b">
					{{> loading fontSize="50px"}}
				</div>
				{{else}}
				<!--begin::Body-->
				<div data-cy="available-groups" class="card-body pt-4">
					<table class="table mt-4">
						<tbody>
						<tr>
							<th></th>
							{{#each getScheduleTypesDaysOfWeek}}
								<th data-cy="weekday-heading">
									<span data-cy="day-name">{{this}}</span>
									{{#with getHolidayInfo this}}
										<i class="fad-primary fad fa-calendar-day text-info ml-2"
										   data-toggle="tooltip"
										   data-html="true"
										   title="Holiday: {{name}}<br>Operating Schedule Types:<ul>{{#each permittedScheduleTypeNames}}<li>{{this}}</li>{{else}}<li>None</li>{{/each}}</ul>">
										</i>
									{{/with}}
								</th>
							{{/each}}
						</tr>
						{{#each scheduleTypes}}
						<tr>
							<td>
								<span data-cy="schedule-name" class="title room-enrollments-label">{{title}}</span>
							</td>
							{{#each stats}}
								{{#if showScheduleTypeDaysLabel abbr}}
									<td>
										<span data-cy="enrolled-list" class="{{showWarning coming cap}}">{{coming}}/{{cap}}</span>
									</td>
								{{/if}}
							{{/each}}
						</tr>
						{{/each}}
						</tbody>
					</table>
					{{#if showMore}}
					<div class="container">
						<button data-cy="show-more" class="button" id="btnShowMoreScheduleType">Show More</button>
					</div>
					{{/if}}
				</div>
				{{/if}}
			</div>
		{{/if}}
	</div>
</template>
