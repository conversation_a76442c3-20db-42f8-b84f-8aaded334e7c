import { Orgs } from '../../../../../lib/collections/orgs.js';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Tracker } from 'meteor/tracker';
import './_widgetScheduleEnrollments.html';
import '../../../../layout/loading.js';
import {
	ScheduleEnrollmentsWidget
} from '../../../../services/dashboard/dashboardBuilder/widgets/ScheduleEnrollmentsWidget';

Template.widgetScheduleEnrollments.onCreated(function(){
	this.isLoading = new ReactiveVar(Template.instance().data?.isLoading);
	this.widgetData = new ReactiveVar();
	this.isContentLoading = new ReactiveVar(false);
	const org = Orgs.current();

	this.scheduleEnrollmentsWidget = new ScheduleEnrollmentsWidget(org);

	// Reactively track changes to parent data
	this.autorun(() => {
		const parentIsLoading = Template.currentData()?.isLoading;
		const parentWidgetData = Template.currentData()?.widgetData;

		if (parentIsLoading !== undefined) {
			this.scheduleEnrollmentsWidget.isLoading.set(parentIsLoading);
		}

		if (parentWidgetData !== undefined) {
			this.scheduleEnrollmentsWidget.widgetData.set(parentWidgetData);
		}
	});

	// Initialize date picker after loading finishes
	this.autorun(() => {
		if (!this.scheduleEnrollmentsWidget.getIsLoading()) {
			Tracker.afterFlush(() => {
				this.scheduleEnrollmentsWidget.initializeDatePicker();
			});
		}
	});
});

Template.widgetScheduleEnrollments.onRendered(function () {
	this.autorun(() => {
		const widgetData = this.scheduleEnrollmentsWidget.widgetData.get(); // reactively track

		if (widgetData) {
			Tracker.afterFlush(() => {
				// Clean up any existing tooltips first
				$('[data-toggle="tooltip"]').tooltip('dispose');
				// Rebind tooltips on updated DOM
				$('[data-toggle="tooltip"]').tooltip({ html: true });
			});
		}
	});
});

Template.widgetScheduleEnrollments.events({
	'click #btnShowMoreScheduleType': function (e, i) {
		i.scheduleEnrollmentsWidget.showMoreClicked.set(true);
	},
});

Template.widgetScheduleEnrollments.helpers({
	scheduleTypes() {
		return Template.instance().scheduleEnrollmentsWidget.getScheduleTypes();
	},
	getScheduleTypesDaysOfWeek() {
		return Template.instance().scheduleEnrollmentsWidget.getWeekDays();
	},
	showScheduleTypeDaysLabel(abbr) {
		return Template.instance().scheduleEnrollmentsWidget.showScheduleTypeDaysLabel(abbr);
	},
	isLoading() {
		return Template.instance().scheduleEnrollmentsWidget.getIsLoading();
	},
	showWarning(coming, cap) {
		return Template.instance().scheduleEnrollmentsWidget.getShowWarning(coming, cap);
	},
	getRangeFilter() {
		return Template.instance().scheduleEnrollmentsWidget.getRangeFilter();
	},
	isContentLoading() {
		return Template.instance().scheduleEnrollmentsWidget.getIsContentLoading();
	},
	showMore() {
		return Template.instance().scheduleEnrollmentsWidget.showMore();
	},
	getHolidayInfo(abbr) {
		return Template.instance().scheduleEnrollmentsWidget.getHolidayInfo(abbr);
	}
});




