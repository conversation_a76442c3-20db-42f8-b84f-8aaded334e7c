import { Template } from 'meteor/templating';
import _ from 'underscore';
import './_widgetMediaRequirement.html';
import '../_dashboardItem.js';

Template.widgetMediaRequirement.helpers({
	getItem() {
		const people = Template.instance().data?.widgetData || [];

		const formattedRows = _.map(people, person => {
			const label = `${person.name}`;
			return {
				label,
				value: `-${person.deficit}`,
				link: `/people/${person._id}#profile`
			}
		})
		const subtitle = (formattedRows && formattedRows.length > 0) ? "" : "No missing required media at this time";
		const isLoading = Template.instance().data?.isLoading

		return {
			style: "list",
			title: "Media Requirements",
			subTitle: subtitle,
			isLoading: isLoading,
			dataItems: _.first(formattedRows, 5),
			footer: (formattedRows && formattedRows.length > 5),
			modalAction: {
				btnText: "Show All",
				title: "All Media Requirements",
				items: formattedRows
			},
		}
	}
})
