<template name="widgetActivity">
	<div class="card card-custom gutter-b widget-activity">
		{{#if checkLoading}}
			{{> loading fontSize="50px"}}
		{{else}}
		<!--begin::Header-->
		<div class="card-header align-items-center border-0 mt-4">
			<h3 class="card-title align-items-start flex-column">
				<span data-cy="latest-activity" class="font-weight-bolder text-dark">Latest Activity</span>
				<span class="text-muted mt-3 font-weight-bold font-size-sm">since 1 minute ago...</span>
			</h3>
		</div>
		<!--end::Header-->
		<!--begin::Body-->
		<div class="card-body pt-4">
			{{#if Template.subscriptionsReady}}
			<!--begin::Timeline-->
			<div class="timeline timeline-6 mt-3">
				{{#each item in items}}
				<!--begin::Item-->
				<div class="timeline-item align-items-start fade-in">
					<!--begin::Label-->
					<div data-cy="timeline-date" class="timeline-label font-weight-bolder text-dark-75 font-size-lg">{{item.timeDescription}}</div>
					<!--end::Label-->
					<!--begin::Badge-->
					<div class="timeline-badge">
						<i data-cy="timeline-badge-color" class="fa fa-genderless {{item.iconColor}} icon-xl"></i>
					</div>
					<!--end::Badge-->
					<!--begin::Text-->
					<div data-cy="timeline-content" class="font-weight-mormal font-size-lg timeline-content text-muted pl-3">{{{item.momentDescription}}}</div>
					<!--end::Text-->
				</div>
				<!--end::Item-->
				{{/each}}
				
				
			</div>
			<!--end::Timeline-->
			{{/if}}
		</div>
		<!--end: Card Body-->
		{{/if}}
	</div>
</template>
