import ApexCharts from 'apexcharts';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import './_widgetActivations.html';
import '../../../../layout/loading';

function drawActivationsChart(activationsData) {
	if (activationsData) {
		const primaryColor = getComputedStyle(document.documentElement).getPropertyValue("--primary");
		const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue("--people-ratio-staff-template-color");
	
		const uninvitedFamilies = activationsData["totalUninvitedFamilies"];
		const totalUnactivatedFamilyMembers = activationsData["totalUnactivatedFamilyMembers"],
			totalActivatedFamilyMembers = activationsData["totalActivatedFamilyMembers"];
		totalPeople = activationsData["totalPeople"];
	
		var options = {
			chart: {
				type: 'donut',
				height: 300,
				events: {
					dataPointSelection: function (event, chartContext, config) {
						FlowRouter.go("/activations#tnaf");
					}
				}
			},
			series: [totalActivatedFamilyMembers, totalUnactivatedFamilyMembers],
			labels: ["Activated Family Members", "Unactivated Family Members"],
			colors: [`${primaryColor}`.trim(), `${secondaryColor}`.trim()],
			responsive: [{
				breakpoint: 480,
				options: {
					chart: {
						//width: 120
					}
	
				}
			}],
			legend: {
				position: "bottom"
			}
		}
	
		var docloc = document.querySelector("#activationsChart"), chart = new ApexCharts(docloc, options);
		if (docloc) chart.render();
	}
}

Template.widgetActivations.helpers({
	"activationsData": () => {
		return Template.instance().data;
	},
	"parentDataAvailable"() {
		drawActivationsChart(Template.instance().data);
		return Template.instance().data ? true : false;
	}
});


