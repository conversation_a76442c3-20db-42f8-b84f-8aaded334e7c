<template name="widgetActivations">
  <div class="box box-default">
    <div class="card card-custom gutter-b">
      <!--begin::Header-->
      {{#if parentDataAvailable}}
      <div class="card-header align-items-center border-0 mt-4">
        <h3 class="card-title align-items-start flex-column">
          <span data-cy="activations-pie-chart" class="font-weight-bolder text-dark">Activations</span>
          <!--<span class="text-muted mt-3 font-weight-bold font-size-sm"></span>-->
        </h3>
      </div>
      {{/if}}
      <!--end::Header-->
      <!--begin::Body-->
      <div class="card-body pt-4">
        <div class="d-flex flex-column">
          <div id="activationsChart"></div>
          {{#if activationsData}}
            <div class="table-responsive">
              <table class="table table-borderless table-vertical-center">
                <thead>
                  <tr>
                    <th class="p-0" style="min-width: 150px"></th>
                    <th class="p-0" style="min-width: 40px"></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="pl-0">
                      <a data-cy="total-activated-family-members-label" href='/activations#tnaf' class="text-dark font-weight-bolder text-hover-primary mb-1 font-size-lg">Total Activated Family Members</a>
                    </td>
                    <td class="text-right pr-0">
                      <span data-cy="total-activated-family-members" class="text-dark-75 font-weight-bolder font-size-lg">{{activationsData.totalActivatedFamilyMembers}}</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="pl-0">
                      <a data-cy="total-unactivated-family-members-label" href='/activations#tufm' class="text-dark font-weight-bolder text-hover-primary mb-1 font-size-lg">Total Unactivated Family Members</a>
                    </td>
                    <td class="text-right pr-0">
                      <span data-cy="total-unactivated-family-members" class="text-dark-75 font-weight-bolder font-size-lg">{{activationsData.totalUnactivatedFamilyMembers}}</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="pl-0">
                      <a data-cy="total-family-members-without-an-invite-label" href='/activations#tfmi' class="text-dark font-weight-bolder text-hover-primary mb-1 font-size-lg">Total Family Members without an Invite</a>
                    </td>
                    <td class="text-right pr-0">
                      <span data-cy="total-family-members-without-an-invite" class="text-dark-75 font-weight-bolder font-size-lg">{{activationsData.totalUninvitedFamilyMembers}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          {{else}}
            {{> loading fontSize="50px"}}
          {{/if}}
        </div>
      </div>
    </div>
  </div>
</template>
