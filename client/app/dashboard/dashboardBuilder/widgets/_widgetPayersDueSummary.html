<template name="widgetPayersDueSummary">
	{{#if reportBuckets}}
		<div id="payers-due-chart">
		{{#each item in reportBuckets}}
			<div data-cy="payer-status-label" class="payers-due-chart-row {{#if shouldHide @index}}d-none{{/if}}">
				{{item.payerLabel}}<br/>
				<div class="payers-due-chart-label-holder">
					<div class="payers-due-chart-label-item over30">
						<span data-cy="payer-status-over-30-days" class="chart-amount">{{formatCurrency item.over30}}</span><br/>
						Over 30 days
					</div>
					<div class="payers-due-chart-label-item under30">
						<span data-cy="payer-status-under-30-days" class="chart-amount">{{formatCurrency item.under30}}</span><br/>
						Under 30 days
					</div>
				</div>
				<div style="clear:both"></div>
				<div class="payers-due-chart-bar-holder">
					<div class="payers-due-chart-bar over30" style="width:{{item.over30Width}}%">
							
					</div>

					<div class="payers-due-chart-bar under30" style="width:{{item.under30Width}}%">

					</div>
				</div>
				<div style="clear:both"></div>
			</div>
		{{/each}}
		{{#if showIsMore}}
		<div class="text-center"><a href="#" id="btnExpandPayersDueSummary">More</a></div>
		{{/if}}
		</div>

	{{else}}
		<p style="text-align:center;font-size:18px">
			There are no outstanding reimbursable amounts at this time.
		</p>
	{{/if}}

</template>