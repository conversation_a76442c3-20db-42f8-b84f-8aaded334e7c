import { Template } from 'meteor/templating';
import { orgLanguageTranformationUtil } from '../../../../../lib/util/orgLanguageTranformationUtil';
import { Counts } from '../../../../../lib/collections/counts';
import { Orgs } from '../../../../../lib/collections/orgs';
import { showModal } from '../../../main';
import './_widgetCheckinsStat.html';
import '../../../../layout/loading';
import $ from 'jquery';
import '../../../groups/_rosterModal';

Template.widgetCheckinsStat.events({
	'click #absent-people-count': function (e, i) {
		showModal("_rosterModal", {
			onlyAbsentPeople: true,
		}, "#_rosterModal")
	}
})

Template.widgetCheckinsStat.onCreated(function () {
	this.autorun(() => {
		this.subscribe("amountInvoiced");
	})
	/* this.hasChildcareCrm = new ReactiveVar(false);
	if (Orgs.current()) {
		Meteor.callAsync('hasChildcareCrm', Orgs.current()._id).then((result) => {
			this.hasChildcareCrm.set(result);
		}).catch((err) => {
			console.error('Error checking childcare CRM:', err);
		});
	} */
});

Template.widgetCheckinsStat.onRendered(function () {
	$('[data-toggle="tooltip"]').tooltip();
})

Template.widgetCheckinsStat.helpers({
	'checkLoading': function () {
		return Session.get("isLoading");
	},
	'checkedInPeopleCount': function () {
		const org = Orgs.current();
		return Counts.findOne({ orgId: org._id }).checkedInPeopleCount;
	},
	'checkedInStaffCount': function () {
		const org = Orgs.current();
		return Counts.findOne({ orgId: org._id }).checkedInStaffCount;
	},
	'enrollmentCount': function () {
		const org = Orgs.current();
		if (!org) {
			return '';
		}
		// return StatusService.countEnrollments(Orgs.current()._id, Template.instance().hasChildcareCrm.get());
		// Don't filter based on CRM status, for now

		return Counts.findOne({ orgId: org._id }).enrollmentCount;
	},
	'showCapacityWidget': function () {
		return Orgs.current() && Orgs.current().hasCustomization("customer/CDS/customFTECalculations");
	},
	'grossCapacity': function () {
		const org = Orgs.current();
		return Counts.findOne({ orgId: org._id }).grossCapacity;
	},
	'netCapacity': function () {
		const org = Orgs.current();
		return Counts.findOne({ orgId: org._id }).netCapacity;
	},
	'absentPeopleCount': function () {
		const org = Orgs.current();
		return Counts.findOne({ orgId: org._id }).absentPeopleCount;
	},
	'getEntityTypePeople': function () {	
		return orgLanguageTranformationUtil.getEntityType('people');
	},
});
