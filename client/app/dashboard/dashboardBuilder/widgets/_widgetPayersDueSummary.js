import { Template } from 'meteor/templating';
import { Orgs } from '../../../../../lib/collections/orgs.js';
import './_widgetPayersDueSummary.html';
import _ from 'underscore';

Template.widgetPayersDueSummary.helpers({
	reportBuckets() {
		console.log("data", Template.instance());
		const data = Template.instance().data.dataItems.buckets, output = [], allPayers = Orgs.current() && Orgs.current().availablePayerSources(true);
		if (!data) return;
		let maxUnder=0, maxOver=0;
		_.each(data, (buckets, payer) => {
			if (buckets["under30"] > maxUnder) maxUnder = buckets["under30"];
			if (buckets["over30"] > maxOver) maxOver = buckets["over30"];
		});
		_.each(data, (buckets, payer) => {
			const payerTotal = (buckets["under30"] || 0) + (buckets["over30"] || 0);
			const payerDetail = _.find(allPayers, p=> p.type == payer);
			if (payerTotal > 0) {
				output.push({
					payer,
					payerLabel: (payerDetail && payerDetail.description) || payer,
					payerTotal,
					under30: buckets["under30"],
					under30Width:  buckets["under30"] / payerTotal * 100,
					over30: buckets["over30"],
					over30Width: buckets["over30"] / payerTotal * 100,
				});
			}
		});
		
		return _.sortBy(output, cr=> cr.payerTotal * -1);
	},
	shouldHide(i) {
		return i>=4;
	},
	showIsMore() {
		const data = Template.instance().data.dataItems.buckets;
		return Object.keys(data).length >= 4;
	}
});

Template.widgetPayersDueSummary.events({
	"click #btnExpandPayersDueSummary"() {
		$("#btnExpandPayersDueSummary").addClass("d-none");
		$(".payers-due-chart-row").removeClass("d-none");
	}
});
