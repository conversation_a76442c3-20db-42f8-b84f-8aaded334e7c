import moment from "moment-timezone";
import { People } from "../../../../../lib/collections/people";
import { orgLanguageTranformationUtil } from "../../../../../lib/util/orgLanguageTranformationUtil";

import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { showModal } from "../../../main";
import './_widgetRatios.html';
import '../../../groups/_rosterModal';
import { CheckinRatios } from "../../../../../lib/collections/checkinRatios";

Template.widgetRatios.events({
	'click .group-roster-link': function (e, i) {
		var groupId = $(e.currentTarget).attr("data-groupid");
		Template.instance().thePeopleRosterSubscription = Meteor.subscribe("thePeopleRoster", { groupId: groupId });
		showModal("_rosterModal", { groupId }, "#_rosterModal")
	}
})
Template.widgetRatios.helpers({
	'checkLoading': function () {
		// Show loading only when the subscription is still loading
		return Template.instance().subscriptionLoading.get();
	},
	'getColorFromCompliance': function (inCompliance) {
		return (inCompliance) ? "#A6D800" : "#FF3B30";
	},
	// //This peace of the code is no more used
	// 'noGroupAssigned': function () {

	// 	const timezone = Orgs.current().getTimezone();
	// 	const startOfDay = new momentTz.tz(moment().startOf('day'), timezone).valueOf();
	// 	const endOfDay = new momentTz.tz(moment().endOf('day'), timezone).valueOf();

	// 	const cancelledReservationPeopleIds = Reservations.findWithRecurrence({
	// 		startDateValue: startOfDay,
	// 		endDateValue: endOfDay,
	// 		query: {
	// 			cancellationReason: { $exists: true }
	// 		}
	// 	}).map(p => p.selectedPerson);

	// 	const peopleQuery = {
	// 		_id: { $in: cancelledReservationPeopleIds },
	// 		type: 'person',
	// 		$or: [{
	// 			checkInGroupId: {
	// 				$exists: false
	// 			}
	// 		}, {
	// 			checkInGroupId: {
	// 				$in: [null, false]
	// 			}
	// 		}]
	// 	}

	// 	// TODO: Rest of these counts. These counts are for a special new block item
	// 	// at the end of the check-in ratios to make sure the counts all line up for
	// 	// at least the absent people (children) count. Will be clarifying the other count
	// 	// items (is it possible to have children / staff checked-in without a group?)

	// 	return {
	// 		childrenCount: 0,
	// 		staffCount: 0,
	// 		checkinLength: 0,
	// 		capacityLength: 0,
	// 		staffLength: 0,
	// 		absenceCount: People.find(peopleQuery).count(),
	// 		remainingCount: 0,
	// 	}
	// },
	"ratioGroups": () => {
		return Template.instance().ratioGroupsData.get();
	},
	'childrenAbsentCount': function (value) {
		return value === 0;
	},
	'getEntityTypePeople': function () {	
		return orgLanguageTranformationUtil.getEntityType('people').toLowerCase();
	}
});

Template.widgetRatios.onCreated(function () {
	this.thePeopleRosterSubscription = null;
	this.subscriptionLoading = new ReactiveVar(true);
	this.ratioGroupsData = new ReactiveVar(null);
	
	// Set up data reactivity
	this.autorun(() => {
		const data = CheckinRatios.findOne()?.data;
		if (data) {
			this.ratioGroupsData.set(data);
		}
	});
	
	// Subscribe to theCheckInRatio to make the CheckinRatios collection reactive
	this.autorun(() => {
		const user = Meteor.user();
		if (user) {
			this.subscriptionLoading.set(true);
			this.theCheckInRatioSubscription = Meteor.subscribe('theCheckInRatio', {}, {
				onReady: () => {
					this.subscriptionLoading.set(false);
				}
			});
		}
	});
});

Template.widgetRatios.onDestroyed(function () {
	// Destroy the subscription when not included in autorun; this will prevent document projections from persisting across publications
	if (this.thePeopleRosterSubscription) {
		this.thePeopleRosterSubscription.stop();
	}
	
	if (this.theCheckInRatioSubscription) {
		this.theCheckInRatioSubscription.stop();
	}
});
