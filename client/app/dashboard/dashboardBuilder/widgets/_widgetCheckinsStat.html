<template name="widgetCheckinsStat">
    <div class="card card-custom bg-white card-stretch flex-row">
        {{#if checkLoading}}
            {{> loading fontSize="50px"}}
        {{else}}
        <div class="card-body d-flex flex-row my-3">
            <div class="d-flex flex-column mr-16">
                <span class="font-size-h4 text-dark-75 font-weight-bolder mb-2">{{ getEntityTypePeople }}</span>
                <div class="d-flex flex-row align-items-center">
                    <i class="icon-2x fad fa-swap-opacity fa-user-check text-primary mr-8"></i>
                    <div class="d-flex flex-column mr-6">
                        <span data-cy="check-in-people-count" class="text-dark-75 font-size-h2 font-weight-bolder">{{checkedInPeopleCount}}</span>
                        <span class="text-dark-50">Checked In</span>
                    </div>
                    <div class="d-flex flex-column mr-6">
                        <span data-cy="enrollment-count" class="text-dark-75 font-size-h2 font-weight-bolder">{{enrollmentCount}}</span>
                        <span class="text-dark-50">Enrolled</span>
                    </div>
                    <div id="absent-people-count" class="d-flex flex-column" style="cursor:pointer">
                        <span data-cy="absent-people-count" class="font-size-h2 font-weight-bolder">{{absentPeopleCount}}</span>
                        <span class="text-primary">Absent</span>
                    </div>
                </div>
            </div>
            <div class="d-flex flex-column">
                <span class="font-size-h4 text-dark-75 font-weight-bolder mb-2">Staff</span>
                <div class="d-flex flex-row align-items-center">
                    <i class="icon-2x fad fa-swap-opacity fa-users text-primary mr-8"></i>
                    <div class="d-flex flex-column mr-6">
                        <span data-cy="check-in-staff-count" class="text-dark-75 font-size-h2 font-weight-bolder">{{checkedInStaffCount}}</span>
                        <span class="text-dark-50">Checked In</span>
                    </div>
                </div>
            </div>
            {{#if showCapacityWidget}}
                <div class="d-flex flex-column ml-10">
                    <div class="d-flex">
                        <span class="font-size-h4 text-dark-75 font-weight-bolder mb-2">Capacity</span>
                        <a
                                href="#"
                                data-toggle="tooltip"
                                data-placement="right"
                                data-html="true"
                                title="Gross Capacity is Maximum Tuition divided by Total Location Maximum Tuition. 100% is ideal.<br><br>Net Capacity is Tuition Billed divided by Total Location Maximum Tuition. 100% is ideal."
                        >
                            <i class="icon-2x fad fad-primary fa-info-circle ml-2"></i>
                        </a>
                    </div>
                    <div class="d-flex flex-row align-items-center">
                        <i class="icon-2x fad fa-bullseye-arrow text-primary mr-8"></i>
                        <div class="d-flex flex-column mr-6">
                            <span data-cy="gross-capacity" class="text-dark-75 font-size-h2 font-weight-bolder">{{grossCapacity}}</span>
                            <span class="text-dark-50">Gross</span>
                        </div>
                        <div class="d-flex flex-column mr-6">
                            <span data-cy="net-capacity" class="text-dark-75 font-size-h2 font-weight-bolder">{{netCapacity}}</span>
                            <span class="text-dark-50">Net</span>
                        </div>
                    </div>
                </div>
            {{/if}}
        </div>
        {{/if}}
    </div>
</template>
