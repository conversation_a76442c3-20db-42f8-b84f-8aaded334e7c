import moment from 'moment-timezone';
import { Orgs } from '../../../../../lib/collections/orgs';

export async function getActivations(instance) {
    return new Promise(async (resolve, reject) => {
        await Meteor.callAsync("getActivationsData")
            .then((result) => {
                setTimeout(() => {
                    instance.activationsData.set(result);
                    resolve(true);
                }, 500);
            })
            .catch((error) => {
                mpSwal.fire("Error", error.reason || error.message, "error");
                setTimeout(() => {
                    reject(error);
                }, 500);
            });
    });
}
export async function getDocumentsOutstanding(instance) {
    return new Promise(async (resolve, reject) => {
        await Meteor.callAsync("documentsDueSummary")
            .then((result) => {
                instance.documentsOutstandingIsloadingFlag.set(false);
                setTimeout(() => {
                    instance.documentsOutstandingData.set(result);
                    resolve(true);
                }, 500);
            })
            .catch((error) => {
                instance.documentsOutstandingIsloadingFlag.set(false);
                mpSwal.fire("Error", error.reason || error.message, "error");
                setTimeout(() => {
                    reject(error);
                }, 500);
            });
    });
}
export async function getMediaRequirements(instance) {
    return new Promise(async (resolve, reject) => {
        await Meteor.callAsync("getDashboardItem", { name: "mediaRequirements" })
            .then((result) => {
                instance.mediaRequirementsIsloadingFlag.set(false);
                setTimeout(() => {
                    instance.mediaRequirementsData.set(result);
                    resolve(true);
                }, 500);
            })
            .catch((error) => {
                instance.mediaRequirementsIsloadingFlag.set(false);
                mpSwal.fire("Error", error.reason || error.message, "error");
                setTimeout(() => {
                    reject(error);
                }, 500);
            })
            .finally(() => {
            });
    });
}
export async function getWeeksSchedule(instance, page, pageSize = 5) {
    return new Promise(async (resolve, reject) => {
        await Meteor.callAsync("classListGroupDays", { page, pageSize})
            .then((result) => {
                instance.weeksScheduleIsloadingFlag.set(false);
                setTimeout(() => {
                    instance.weeksScheduleData.set(result.groupData);
                    resolve(true);
                }, 500);
            })
            .catch((error) => {
                instance.weeksScheduleIsloadingFlag.set(false);
                mpSwal.fire("Error", error.reason || error.message, "error");
                setTimeout(() => {
                    reject(error);
                }, 500);
            })
            .finally(() => {
            });
    });
}

export async function getCheckInRatios(instance) {
    return new Promise(async (resolve, reject) => {
        await Meteor.callAsync("getDataWidgetRatios")
            .then((result) => {
                instance.checkInRatiosIsloadingFlag.set(false);
                setTimeout(() => {
                    instance.checkInRatiosData.set(result);
                    resolve(true);
                }, 500);
            })
            .catch((error) => {
                instance.checkInRatiosIsloadingFlag.set(false);
                mpSwal.fire("Error", error.reason || error.message, "error");
                setTimeout(() => {
                    reject(error);
                }, 500);
            });
    });
}
export async function getWeeksEnrollmentByType(instance) {
    return new Promise(async (resolve, reject) => {
        const newDate = new moment.tz(Orgs.current().getTimezone())
        const rangeStart = newDate.startOf('week').format("MM/DD/YYYY")
        const rangeEnd = newDate.endOf('week').format("MM/DD/YYYY")

        try{
            let result = await Meteor.callAsync("rangedEnrollmentData",{ rangeStart, rangeEnd });
            instance.weeksScheduleRegistrationFlowData.set(result);
            instance.weeksScheduleRegistrationFlowIsloadingFlag.set(false);
            resolve(true);
        }catch (error){
            mpSwal.fire("Error", error.reason || error.message, "error");
            setTimeout(() => {
                reject(error);
            }, 500);
        }
    });
}