import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_widgetActivityRealTime.html';
import { Moments } from '../../../../../lib/collections/moments';
import '../../../../layout/loading';

Template.widgetActivityRealTime.onRendered( function() {
	var instance = this;

	data = [];
	var t = document.getElementById("kt_stats_widget_7_chart");
	if (t) {
		countMoments();
		var e = {
			series:  [{
				name: "Activity",
				data: data.slice()
			}],
			chart: {
				type: "area",
				height: 150,
				toolbar: {
					show: !1
				},
				zoom: {
					enabled: !1
				},
				sparkline: {
					enabled: !0
				}
			},
			plotOptions: {},
			legend: {
				show: false,
				formatter: function(seriesName, opts) {
					return seriesName
			}
			},
			dataLabels: {
				enabled: true,
			},
			fill: {
				type: "solid",
				opacity: 1
			},
			stroke: {
				curve: "smooth",
				show: !0,
				width: 3,
				colors: [KTApp.getSettings().colors.theme.base.success]
			},
			xaxis: {
				type: 'numeric',
				axisBorder: {
					show: false
				},
				axisTicks: {
					show: false
				},
				labels: {
					show: false,
					style: {
						colors: KTApp.getSettings().colors.gray["gray-500"],
						fontSize: "12px",
						fontFamily: KTApp.getSettings()["font-family"]
					}
				},
				crosshairs: {
					show: false,
					position: "front",
					stroke: {
						color: KTApp.getSettings().colors.gray["gray-300"],
						width: 1,
						dashArray: 3
					}
				},
				tooltip: {
					enabled: true,
					formatter: void 0,
					offsetY: 0,
					style: {
						fontSize: "12px",
						fontFamily: KTApp.getSettings()["font-family"]
					}
				}
			},
			yaxis: {
				labels: {
					show: false,
					style: {
						colors: KTApp.getSettings().colors.gray["gray-500"],
						fontSize: "12px",
						fontFamily: KTApp.getSettings()["font-family"]
					}
				}
			},
			states: {
				normal: {
					filter: {
						type: "none",
						value: 0
					}
				},
				hover: {
					filter: {
						type: "none",
						value: 0
					}
				},
				active: {
					allowMultipleDataPointsSelection: !1,
					filter: {
						type: "none",
						value: 0
					}
				}
			},
			tooltip: {
				enabled: true,
				x: {
					show: false,
				},
				style: {
					fontSize: "12px",
					fontFamily: KTApp.getSettings()["font-family"]
				},
			},
			colors: [KTApp.getSettings().colors.theme.light.success],
			markers: {
				colors: [KTApp.getSettings().colors.theme.light.success],
				strokeColor: [KTApp.getSettings().colors.theme.base.success],
				strokeWidth: 3
			}
		};
		var chart = new ApexCharts(t, e);
		chart.render();
		this.run_every_sec = Meteor.setInterval(function () {
			countMoments();
		  
			chart.updateSeries([{
			  data: data
			}])
		}, 5000);
	}
});

Template.widgetActivityRealTime.onDestroyed(function () {
    clearInterval(this.run_every_sec);
});

let data = [];
var TICKINTERVAL = 360000;
let lastDate;

function resetData(){
	// Alternatively, you can also reset the data at certain intervals to prevent creating a huge series 
	data = data.slice(data.length - 10, data.length);
}

function countMoments() {
	const mc = Moments.find({}).count();
	if (mc == 0) return [];
	const startingValue = new Date().getTime();

	if (data.length == 0 ) {
		for (let i = 1; i <= 10; i++) {
			const currentValue = startingValue - ((10 - i) * TICKINTERVAL);
			data.push({
				x: new moment(currentValue).format("h:mm a"),
				y: momentsInInterval(currentValue)
			});
		}
		lastDate = startingValue;
	} else if (startingValue - lastDate > TICKINTERVAL) {
		data.push({
			x: new moment(startingValue).format("h:mm a"),
			y: momentsInInterval(startingValue)
		});
		lastDate = startingValue;
	} else {
		const item = _.last(data),
			currentCount = momentsInInterval(startingValue);
		item.y = currentCount;
	}
	
}
function momentsInInterval(currentValue) {
	const startRange = currentValue - TICKINTERVAL, endRange = currentValue;
	const c = Moments.find({sortStamp:{"$gte": startRange, "$lt": endRange}}).count();
	return c;
}