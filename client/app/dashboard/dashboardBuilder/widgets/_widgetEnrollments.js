import { Template } from 'meteor/templating';
import './_widgetEnrollments.html'
import { Orgs } from '../../../../../lib/collections/orgs';

Template.widgetEnrollments.onCreated(function(){
	this.widgetData = new ReactiveVar();
	this.groupCount = new ReactiveVar(6); // Set to page size + 1
	const org = Orgs.current();
	const timezone = org.getTimezone()
	this.timezone = new ReactiveVar(timezone);
	this.page = new ReactiveVar(1);
	this.pageSize = new ReactiveVar(5);
	this.isLoading = new ReactiveVar(Template.instance().data?.isLoading);
	this.isLoadingMoreGroups = new ReactiveVar(false);
});

Template.widgetEnrollments.events({
	'click #btnShowMoreGroups': async function (e, i) {
		i.isLoadingMoreGroups.set(true);
		const page = i.page.get() + 1;
		const pageSize = i.pageSize.get();
		await Meteor.callAsync("classListGroupDays", { page, pageSize })
            .then((result) => {
				i.isLoadingMoreGroups.set(false);
				if (!i.widgetData.get()) {
					i.widgetData.set(result.groupData);
					i.groupCount.set(result.pagination.totalCount)
					i.page.set(page);
					i.pageSize.set(pageSize);
				} else {
					const updatedGroupResults = i.widgetData.get().concat(result.groupData)
					i.widgetData.set(updatedGroupResults);
					i.groupCount.set(result.pagination.totalCount)
					i.page.set(page);
					i.pageSize.set(pageSize);
				}
            })
            .catch((error) => {
				i.isLoadingMoreGroups.set(false);
                mpSwal.fire("Error", error.reason || error.message, "error");
            })
	}
})

Template.widgetEnrollments.helpers({
	'getClassListDays': function() {
		return Template.instance().widgetData.get();
	},
	'getClassListDaysOfWeek': function() {
		return ['M', 'T', 'W', 'R', 'F'];
	},
	'showClassListDaysLabel': function(d) {
		return (d === "M") ? "visible" : "hidden";
	},
	'getClassListDayData': function(obj, val) {
		return obj[val];
	},
	'isLoading': function() {
		if (!Template.instance().data?.isLoading) {
			Template.instance().widgetData.set(Template.instance().data?.widgetData);
			Template.instance().isLoading.set(Template.instance().data?.isLoading);
		}
		return Template.instance().isLoading.get();
	},
	'isLoadingMoreGroups': function() {
		return Template.instance().isLoadingMoreGroups.get();
	},
	'showMore': function() {
		return Template.instance().groupCount.get() >= (Template.instance().page.get()) * Template.instance().pageSize.get();
	}
});