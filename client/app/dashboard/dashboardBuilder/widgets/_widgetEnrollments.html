<template name="widgetEnrollments">
	<div class="box box-default">
		{{#if isLoading}}
			<div class="card card-custom gutter-b">
				{{> loading fontSize="50px"}}
			</div>
		{{else}}
			<div data-cy="widget-enrollments-legacy" class="card card-custom gutter-b">
				<!--begin::Header-->
				<div class="card-header align-items-center border-0 mt-4">
					<h3 class="card-title align-items-start flex-column">
						<span data-cy="this-weeks-schedule-legacy" class="font-weight-bolder text-dark">This Week's Schedule</span>
						<!--<span class="text-muted mt-3 font-weight-bold font-size-sm"></span>-->
					</h3>
				</div>
				<!--end::Header-->
				<!--begin::Body-->
				<div data-cy="available-groups" class="card-body pt-4">
					<div style="display:flex;flex-direction:column">
						{{#each getClassListDays}}
							<div data-cy="available-group" class="room-enrollments-container">
								<span data-cy="group-name" class="room-enrollments-label">{{name}}</span>
								<span data-cy="capacity-ratio">{{capacity}} capacity  •  1:{{ratio}} ratio</span>
								<div style="display:flex;flex-direction:row;margin-top:8px;">
									{{#each value in getClassListDaysOfWeek}}
										<div style="display:flex;flex-direction:column;">
											<div style="display:flex;flex-direction:column;margin-bottom:12px;">
												<span style="font-weight:bold">{{value}}</span>
												<span data-cy="enrolled-list">{{getClassListDayData totals.totals value}} ({{getClassListDayData totals.partTimeTotals value}})</span>
												<span style="visibility:{{showClassListDaysLabel value}}">Enrolled</span>
											</div>
											<div style="display:flex;flex-direction:column;">
												<span data-cy="staff-list">{{getClassListDayData staff value}}</span>
												<span style="visibility:{{showClassListDaysLabel value}}">Staff Required</span>
											</div>
										</div>
									{{/each}}
								</div>
							</div>
						{{/each}}
					</div>
					{{#if isLoadingMoreGroups}}
						<div class="card card-custom gutter-b">
							{{> loading fontSize="50px"}}
						</div>
					{{else}}
					{{#if showMore}}
					<button data-cy="show-more-groups" class="btn-primary btn" id="btnShowMoreGroups">Show More</button>
					{{/if}}
					{{/if}}
				</div>
			</div>
		{{/if}}
	</div>
</template>