import {RenderApexCharts} from "/client/lib/renderApexCharts";
import { Template } from 'meteor/templating';
import './_forYouItem.html';

Template.forYouItem.onRendered( function() {
	const item = Template.instance().data.item;
	if (item.style == "chart")
		RenderApexCharts.initApexVerticalBar({elementId:item.title.replaceAll(" ","-")});
});

Template.forYouItem.helpers( {
	chartId() {
		const item = Template.instance().data.item;
		return item.title.replaceAll(" ","-");
	},
});