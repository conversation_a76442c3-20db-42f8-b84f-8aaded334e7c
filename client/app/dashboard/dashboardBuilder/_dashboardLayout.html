<template name='dashboardLayout'>
	<div class="overlay">
		{{#if equals parentData.name "billing"}}
			<!-- if name = billing then excecute below else go with old -->
			<!-- Revenue Section start -->
			<div class="row mt-12">
				<div class="col-9">
					<h3>Revenue</h3>
				</div>
			</div>
			<div class="row">
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> revenuePerformance revenuePerformanceData}}
				</div>
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> amountInvoiced amountInvoicedData}}
				</div>
			</div>
			<!-- Revenue Section end -->

			<!-- Collections Section start -->
			<div class="row mt-12">
				<div class="col-9">
					<h3>Collections</h3>
				</div>
			</div>
			<div class="row">
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> aging agingData}}
				</div>
				{{#if showPayerStatus}}
					<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
						{{> payerStatus payerStatusData}}
					</div>
				{{/if}}
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> pastDueAccounts pastDueAccountsData}}
				</div>
			</div>
			<!-- Collections Section end -->

			<!-- Payments Section start -->
			<div class="row mt-12">
				<div class="col-9">
					<h3>Payments</h3>
				</div>
			</div>
			<div class="row">
				{{#if showRecentManualPayments}}
					<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
						{{> recentManualPayments recentManualPaymentsData}}
					</div>
				{{/if}}
				{{#if showACHReturnsAndCreditCardRefusal}}
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> ACHReturns achReturnsData}}
				</div>
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> creditCardRefusal creditCardRefusalData}}
				</div>
				{{/if}}
				{{#if showRecentRefund}}
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> recentRefund recentRefundsData}}
				</div>
				{{/if}}
			</div>
			<!-- Payments Section end -->

			<!-- Registration Section start -->
			{{#if showRegistration}}
			<div class="row mt-12">
				<div class="col-9">
					<h3>Registration</h3>
				</div>
			</div>
			<div class="row">
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> registrationStatusWidget registrationStatusData}}
				</div>
				{{#if showDistricEmployeeReview}}
					<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
						{{> districtEmployeeReviewWidget districtEmployeeData}}
					</div>
				{{/if}}
			</div>
			{{/if}}
			<!-- Registration Section end -->

			<!-- What's Next Section start -->
			<div class="row mt-12">
				<div class="col-9">
					<h3>What's Next</h3>
				</div>
			</div>
			<div class="row">
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> upcomingWithdrawals upcomingWithdrawalsData}}
				</div>
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> upcomingBillingAdjustments upcomingBillingAdjustmentsData}}
				</div>
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> nextAutoInvoicing nextAutoInvoicingData}}
				</div>
				<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
					{{> inProgressPaymentsAndRefunds inProgressPaymentsAndRefundsData}}
				</div>
			</div>
			<!-- What's Next Section end -->
		{{else}}
			{{#if requery}}
				<div class="d-flex min-h-300px justify-content-center">
					{{> loading}}
				</div>
			{{else}}
				{{#if dashboardSections}}
					{{#each section in dashboardSections}}
						<div class="row mt-12">
							<div class="col-9">
								<h3>{{section.title}}</h3>
							</div>
							<div class="col-3 text-right">
								<!--<a href="{{section.link}}">{{section.linkDescription}} <i class="fa fa-arrow-right"></i></a>-->
							</div>
						</div>
						<div class="row">
							{{#each item in section.items}}
								<div class="col-md-4">
									{{> dashboardItem section=section item=item dashboardData=dashboardData handler=handler}}
								</div>
							{{/each}}
						</div>
					{{/each}}
				{{else}}
					<div class="d-flex min-h-300px justify-content-center">
						No Data
					</div>
				{{/if}}
			{{/if}}
		{{/if}}

	</div>
</template>