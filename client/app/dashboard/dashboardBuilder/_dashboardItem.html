<template name="dashboardItem">
	<div class="card card-custom {{#if stacked}}card-stretch-half{{else}}card-stretch{{/if}} {{#unless excludeGutter}}gutter-b{{/unless}}">
	{{#if currentItem.isLoading}}
      {{>loading fontSize="50px"}}
    {{else}}		
		<div class="card-body {{#if stretched}}d-flex flex-column p-0{{else}}p-5{{/if}}">

			{{#if requeryActive}}
				<div class="spinner spinner-primary"></div>
			{{else}}
				<div data-cy="overview-values" class="{{#if stretched}}flex-grow-1 p-5{{else}}p-0{{/if}}">
					{{#if currentItem.dropdownItems}}
					<div class="card-toolbar pull-right">
						<div data-cy="view-dropdown" class="dropdown dropdown-inline">
							<a href="#" class="btn btn-primary btn-sm font-weight-bolder dropdown-toggle px-5" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">{{currentItem.dropdownLabel}}</a>
							<div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
								<!--begin::Navigation-->
								<ul class="navi navi-hover">
									{{#each dropdownItem in currentItem.dropdownItems}}
									<li class="navi-item">
										<a data-cy="time-intervals" href="#" class="navi-link dashboard-item-dropdown-link" data-action="{{dropdownItem.value}}">
											<span class="navi-icon">
												<i class="flaticon2-calendar"></i>
											</span>
											<span class="navi-text">{{dropdownItem.label}}</span>
										</a>
									</li>
									{{/each}}
								</ul>
								<!--end::Navigation-->
							</div>
						</div>
					</div>
					{{/if}}

					<b data-cy="chart-details">{{currentItem.title}}</b><br/>
					
					{{#if showMetric}}
					<h1 class="">{{currentItem.metric}}</h1>
					<p>{{currentItem.subTitle}}</p>
					{{/if}}
				

				</div>
				
				{{#if isGraph}}
					<div class="flex-grow-1">
						<div id="{{chartId}}" style="height:150px;"></div>
					</div>
				{{/if}}
				{{#if isProgressBar}}
					<div class="font-weight-bold text-muted font-size-sm mt-10">
						<span class="text-dark-75 font-size-h2 font-weight-bolder mr-2">{{currentItem.metric}}</span>
					{{currentItem.metricLabel}}</div>
					<div class="progress progress-xs mt-7 bg-success-o-60">
						<div class="progress-bar bg-success" role="progressbar" style="width: {{currentItem.barWidth}}%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
					</div>
				{{/if}}
				{{#if isList}}
					<div class="table-responsive" id="{{chartId}}">
						<table class="table table-borderless table-vertical-center">
							<thead>
								<tr>
									<th class="p-0" style="min-width: 150px"></th>
									<th class="p-0" style="min-width: 40px"></th>
								</tr>
							</thead>
							<tbody>
								{{#each dataItem in currentItem.dataItems}}
								<tr>
									<td class="pl-0">
										<a data-cy="dashboard-list-data-item-label-{{dataItem.personId}}" href="{{#if dataItem.link}}{{dataItem.link}}{{else}}#{{/if}}" data-org-id="{{dataItem.linkOrgId}}" class="text-dark font-weight-bolder text-hover-primary mb-1 font-size-lg data-item-link">{{dataItem.label}}</a>
										{{#if dataItem.alertText}}<span style="color:#ff0000">{{dataItem.alertText}}</span>{{/if}}
									</td>
									<td class="text-right pr-0">
										<span data-cy="dashboard-list-data-item-value-{{dataItem.personId}}" class="text-dark-75 font-weight-bolder font-size-lg">{{dataItem.value}}</span>
									</td>
								</tr>
								{{/each}}
							</tbody>
						</table>
					</div>
				{{/if}}
			{{/if}}

			{{#if isFteData}}
				<div class="table-responsive">
					<table class="table table-borderless table-vertical-center">
						<thead>
							<tr>
								<th class="px-2"></th>
								<th class="px-2">FTE</th>
								<th class="px-2">Variance</th>
								<th class="px-2">FTE +1 month</th>
								<th class="px-2">FTE +1 goal</th>
							</tr>
						</thead>
						<tbody>
							{{#each dataItem in item.dataItems}}
								{{#each dataItem.totals}}
									<tr>
										<td data-cy="organization-fte" class='no-wrap' style="font-weight:bold;">{{name}}</td>
										<td data-cy="current-fte" style="font-weight:bold;">{{currentFte}}</td>
										<td data-cy="variance-fte" style="{{getVarianceTextClass preferredCapacity currentFte}}">{{#if hasNumberValue currentVariance}}{{currentVariance}}{{/if}}</td>
										<td data-cy="fte-plus" class='no-wrap'>{{currentFtePlusOne}}</td>
										<td data-cy="fte-goal" class='no-wrap'>{{enrollmentGoal}}</td>
									</tr>
								{{/each}}
								{{#each dataItem.groups}}
									<tr>
										<td data-cy="group-fte" class='no-wrap'>{{name}}</td>
										<td data-cy="group-current-fte" style="font-weight:bold;">{{currentFte}}</td>
										<td data-cy="group-variance-fte" style="{{getVarianceTextClass preferredCapacity currentFte}}">{{#if hasNumberValue currentVariance}}{{currentVariance}}{{/if}}</td>
										<td data-cy="group-fte-plus" class='no-wrap'>{{currentFtePlusOne}}</td>
										<td data-cy="group-fte-goal" class='no-wrap'>{{enrollmentGoal}}</td>
									</tr>
								{{/each}}
								<div class="separator separator-dashed my-2"></div>
							{{/each}}
						</tbody>
					</table>
				</div>
			{{/if}}

			{{#if isNotAvailable}}
			<div class="na-label">
				<div class="na-label-text">Collecting more data. Check back soon &#x1F680;</div>
			</div>
			{{/if}}

			{{#if item.widgetTemplate}}
				{{> Template.dynamic template=item.widgetTemplate data=item}}
			{{/if}}
		</div>
		{{#if currentItem.footer}}
			{{#if currentItem.modalAction}}
				<div class="card-footer py-3">
					<div class="d-flex flex-row justify-content-center align-items-center">
						<div class="btn btn-primary font-weight-bolder btn-text-white showModalAction">
							{{currentItem.modalAction.btnText}}
						</div>
					</div>
				</div>
			{{/if}}

			{{#if item.footerAction}}
				<div class="d-flex flex-row justify-content-center align-items-center">
					<a href="{{item.footerAction.destination}}" class="btn btn-primary font-weight-bolder btn-text-white footerAction">
						{{item.footerAction.btnText}}
					</a>
				</div>
			{{/if}}
			{{#if item.otherAction}}
				<div class="card-footer py-3">
					<div class="d-flex flex-row justify-content-center align-items-center">
						<a data-cy="footer-btn"  href="{{item.otherAction.destination}}" class="btn btn-primary font-weight-bolder btn-text-white showModalAction">
							{{item.otherAction.btnText}}
						</a>
					</div>
				</div>
			{{/if}}
		{{/if}}
	{{/if}}
	</div>
</template>
