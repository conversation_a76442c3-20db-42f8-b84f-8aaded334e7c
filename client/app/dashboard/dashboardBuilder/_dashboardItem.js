import {RenderApexCharts} from "/client/lib/renderApexCharts";
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Tracker } from 'meteor/tracker';
import './_dashboardItem.html';
import './_dashboardListModal.html'
import '../../../layout/loading';
import { showModal } from "../../main";
import { Orgs } from "../../../../lib/collections/orgs";
import '../../billing/overview/_pastDueAccounts';
import '../../simpleModal/simpleModal';

Template.dashboardItem.events({
	"click .showModalAction": function(e ,i) {
		if (i.data.item.modalAction) {
			showModal("simpleModal", {
				title: i.data.item.modalAction.title,
				hideSave: true,
				template: "_dashboardListModal",
				data: { items: i.data.item.modalAction.items },
			}, "#simpleModal");
		}
	},
	"click .dashboard-item-dropdown-link": function(e, i) {
		e.preventDefault();
		const selectedValue = $(e.currentTarget).data("action"),
			currentSectionTitle = i.data.section.title,
			item = i.data.item;
		i.data.handler.handleDropdownChange({
			sectionTitle:currentSectionTitle,
			itemId:item.itemId,
			selectedValue
		});
	},
	"click .data-item-link": function(event,i) {
		event.preventDefault();
		const destination = $(event.currentTarget).attr("href"),
			destinationOrgId = $(event.currentTarget).data("org-id"),
			currentOrg = Orgs.current();
			
		if (destinationOrgId && currentOrg._id != destinationOrgId) {
			Meteor.callAsync('adminSwitchOrg', destinationOrgId)
			.then((res)=>{
				location.replace("/loading?setLoc=redirect&desturl=" + encodeURIComponent(destination));
			})
			.catch((error)=>{
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			FlowRouter.go(destination);
		}
	}
});

Template.dashboardItem.helpers({
	hasNumberValue(val) {
		return !isNaN(val);
	},
	stretched(){
		const item = Template.instance().data.item;
		if (item && item.style=="fullGraph")
			return true;
	},
	isGraph() {
		const item = Template.instance().data.item;
		return item && item.style=="fullGraph";
	},
	isProgressBar() {
		const item = Template.instance().data.item;
		return item && item.style=="progressBar";
	},
	isList() {
		const item = Template.instance().data.item;
		return item && item.style=="list";
	},
	isFteData() {
		const item = Template.instance().data.item;
		return item && item.style == "fteDataTable"
	},
	chartId(){
		const item = Template.instance().data.item;
		return item.title.replaceAll(" ","-");
	},
	showMetric() {
		const item = Template.instance().data.item;
		return item && item.style!="progressBar";
	},
	isNotAvailable() {
		const item = Template.instance().data.item;
		return item && item.notAvailable;
	},
	requeryActive() {
		return false;
		const requeryActive = Template.instance().data.requery;
		return requeryActive && requeryActive.get();
	},
	chartId() {
		const item = Template.instance().data.item;
		return item.title.replaceAll(" ","-");
	},
	currentItem() {
		return Template.instance().data.item;
	},
	getVarianceTextClass(preferredCapacity, currentFte) {
		let cFte = parseFloat(currentFte);
		let pC = parseFloat(preferredCapacity);
		if (cFte < pC) return "color:red;"
		return "";
	}
});

Template.dashboardItem.onRendered( function() {
	let item = this.data.dashboardData, self = this;

	Tracker.autorun( function() {

		const dashboardData = self.data.dashboardData && self.data.dashboardData.get(),
			sectionTitle = self.data.section && self.data.section.title,
			currentSectionData = dashboardData && sectionTitle && _.find(dashboardData.sections, s => s.title == sectionTitle),
			currentItemTitle = self.data.item.title,
			currentItem = currentSectionData && _.find(currentSectionData.items, i => i.title == currentItemTitle);
		//console.log("dashboardData udpate", dashboardData);
		if (currentItem) refreshGraphs(currentItem);	
	});
});

function refreshGraphs(item) {
	if (item.style=="fullGraph" && item.graphStyle == "line") {
		RenderApexCharts.initApexLineChart({
			elementId: item.title.replaceAll(" ","-"),
			item
		});
	} else if (item.style=="fullGraph" && item.graphStyle == "bars") {
		RenderApexCharts.initApexBarChart({
			elementId: item.title.replaceAll(" ","-"),
			item
		});
	} else if (item.style=="fullGraph" && item.graphStyle == "pie") {
		RenderApexCharts.initApexPieChart({
			elementId: item.title.replaceAll(" ","-"),
			item
		});
	} else if (item.style=="fullGraph" && item.graphStyle == "verticalBars") {
		RenderApexCharts.initApexVerticalBar({
			elementId: item.title.replaceAll(" ","-"),
			item
		});
	} 
}
