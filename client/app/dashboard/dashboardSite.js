import {
	getActivations,
	getDocumentsOutstanding,
	getMediaRequirements,
	getWeeksSchedule,
	getWeeksEnrollmentByType
} from "./dashboardBuilder/widgets/mySite";
import  { AvailableCustomizations }  from "../../../lib/customizations";

import { getPastDueAccounts } from "../../app/billing/overview/overview.js";
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './dashboardSite.html';
import './dashboardBuilder/widgets/_widgetCheckinsStat.js';
import './dashboardBuilder/widgets/_widgetActivations.js';
import './dashboardBuilder/widgets/_widgetActivity.js';
import './dashboardBuilder/widgets/_widgetDocumentsDue.js';
import './dashboardBuilder/widgets/_widgetEnrollments.js';
import { Orgs } from "../../../lib/collections/orgs.js";

import './dashboardBuilder/widgets/_widgetExpressDriveUp.js';
import './dashboardBuilder/widgets/_widgetMediaRequirement.js'
import './dashboardBuilder/widgets/_widgetRatios.js';
import './dashboardBuilder/widgets/_widgetScheduleEnrollments.js';
import '../../layout/loading.js';
import { Users }from "../../../lib/collections/users.js";
import { processPermissions } from "../../../lib/permissions.js";
import '../../../client/app/billing/overview/_pastDueAccounts.js';

Template.dashboardSite.helpers({
	"hasExpressDriveUp"() {
		const org = Orgs.current();
		if (org && org.hasCustomization("people/expressDriveUpBlock/enabled")) return false;
		return org && (org.hasCustomization("people/familyCheckin/enabled") || org.hasCustomization("people/expressDriveUp/enabled"));
	},
	"hasPastdueAcct"() {
		const org = Orgs.current();
		if (org && org.hasCustomization("mySiteDashboard/ShowPastDueAccountsWidget")) return true;
	},
	"pastDueAccountsData"() {
		const pd = Template.instance().pastDueAccountsData.get();
		return pd;
	},

	'canSeePastDueAccountDollar': function() {
        return processPermissions({
            assertions: [{ context: "billing/reports", action: "write"}],
			evaluator: (person) => person.type=="admin"
        });
    },
	
	"locationName"() {
		return Orgs.current() && Orgs.current().name;
	},
	"showRoomEnrollments"() {
		const person = Meteor.user() && Meteor.user()?.fetchPerson();
		return Orgs.current()?.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW) && person && person.type == "admin";
	},
	"activationsData"() {
		return Template.instance().activationsData?.get();
	},
	"documentsOutstandingData"() {
		return Template.instance().documentsOutstandingData?.get();
	},
	"documentsOutstandingIsloadingFlag"() {
		return Template.instance().documentsOutstandingIsloadingFlag?.get();
	},
	"mediaRequirementsData"() {
		return Template.instance().mediaRequirementsData?.get();
	},
	"mediaRequirementsIsloadingFlag"() {
		return Template.instance().mediaRequirementsIsloadingFlag?.get();
	},
	"weeksScheduleData"() {
		return Template.instance().weeksScheduleData?.get();
	},
	"weeksScheduleIsloadingFlag"() {
		return Template.instance().weeksScheduleIsloadingFlag?.get();
	},
	"weeksScheduleRegistrationFlowData"() {
		return Template.instance().weeksScheduleRegistrationFlowData?.get();
	},
	"weeksScheduleRegistrationFlowIsloadingFlag"() {
		return Template.instance().weeksScheduleRegistrationFlowIsloadingFlag?.get();
	},
	"checkInRatiosData"() {
		return Template.instance().checkInRatiosData?.get();
	},
	"checkInRatiosIsloadingFlag"() {
		return Template.instance().checkInRatiosIsloadingFlag?.get();
	},
});

Template.dashboardSite.onCreated(function () {
	this.activationsData = new ReactiveVar(null);
	this.documentsOutstandingData = new ReactiveVar(null);
	this.documentsOutstandingIsloadingFlag = new ReactiveVar(true);
	this.mediaRequirementsData = new ReactiveVar(null);
	this.mediaRequirementsIsloadingFlag = new ReactiveVar(true);
	this.weeksScheduleData = new ReactiveVar(null);
	this.weeksScheduleIsloadingFlag = new ReactiveVar(true);
	this.weeksScheduleRegistrationFlowData = new ReactiveVar(null);
	this.weeksScheduleRegistrationFlowIsloadingFlag = new ReactiveVar(true);
	this.checkInRatiosData = new ReactiveVar(null);
	this.checkInRatiosIsloadingFlag = new ReactiveVar(true);
	this.pastDueAccountsData = new ReactiveVar([]);
	this.autorunFlag = 0;
	
	Meteor.defer(() => {
		const person = Meteor.user() && Meteor.user()?.fetchPerson();
		const promises = [
			getPastDueAccounts(this, "mySiteDashboard"),
			getActivations(this),
			getDocumentsOutstanding(this),
			getMediaRequirements(this)
		];
		if (Orgs.current()?.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW) && person && person.type == "admin") {
			promises.push(getWeeksEnrollmentByType(this));
		} else {
			promises.push(getWeeksSchedule(this, 0, 5));
		}
		Promise.all(promises)
		.catch((error) => {
			throw new Meteor.Error(error.error, error.reason);
		});
	})
 });

Template.dashboardSite.onDestroyed(function() {
	// No need to stop the subscription here as it's handled by the widget
});


