<!-- /imports/client/app/index/index.html -->
<template name="dashboard">
  <div class="container-fluid" id="dashboard">

    <div class="row">
      <div class="col-md-6">
        <h1 data-cy="greeting-message">{{greetingMessage}}</h1>
      </div>
      <div class="col-md-6">
        <div class="row">
          <div class="col-7"></div>
          <div data-cy="org-name" class="col-5 text-right">
            {{>reportOrgsField fullwidth=true opts=orgsFieldOpts}}
          </div>
          <!--
          <div class="col-5">
            <div class="dropdown">
              <button class="btn btn-secondary dropdown-toggle btn-block" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  Last 30 Days
              </button>
              <div class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                  <a class="dropdown-item" href="#">Last 30 Days</a>
                  
              </div>
            </div>
          </div>

          <div class="col">
            <button class="btn btn-light">
              <i class="fa fa-gear"></i>
            </button>
          </div>
        -->
        </div>
      </div>

    </div>

    {{> dashboardLayout name="primary" orgs=currentOrgs}}

  </div>
</template>
