import { People } from "../../../lib/collections/people";
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './expressDriveUp.html';
import '../moments/partials/_checkinFormModal'
import { showModal } from "../main";
import '../moments/partials/_checkoutFormModal';
import { processPermissions } from "../../../lib/permissions";
import { Groups } from "../../../lib/collections/groups";

const getPeopleHere = (user) => {
  const q = {
    orgId: user.orgId,
    inActive: { $ne: true },
    $or: [
      {"familyCheckIn.dropOffArrival": true},
      {"familyCheckOut.pickUpArrival": true}
    ]
  };

  return People.find(q, {
    sort: { lastName: 1, firstName: 1 },
    fields: {
      firstName: 1,
      lastName: 1,
      checkedIn: 1,
      familyCheckIn: 1,
      familyCheckOut: 1,
      checkInGroupId: 1,
      defaultGroupId: 1
    }
  });
}

Template.expressDriveUp.helpers({
  "peopleHere": function() {
    const user = Meteor.user();
    return getPeopleHere(user)
  },
  "arrivals": function() {
    const user = Meteor.user();
    const q = {
      orgId: user.orgId,
      inActive: { $ne: true },
      $or: [
        {
          "familyCheckIn.dropOffTimeEstimate": {$exists: true}
        },
        {
          "familyCheckOut.pickUpTimeEstimate": {$exists: true}
        }
      ]
    };

    return People.find(q, {
      sort: {"familyCheckIn.dropOffTimeEstimate": 1, "familyCheckIn.pickUpTimeEstimate": 1 },
      fields: {
        firstName: 1,
        lastName: 1,
        checkedIn: 1,
        familyCheckIn: 1,
        familyCheckOut: 1,
        checkInGroupId: 1,
        defaultGroupId: 1
      }
    });

  },
  "getEstimatedTime": function(person) {
    const time = person?.familyCheckIn?.dropOffTimeEstimate || person?.familyCheckOut?.pickUpTimeEstimate;
    return new moment(time).format("h:mm a");
  },
  "getArrivalTime": function(person) {
    const time = person?.familyCheckIn?.arrivalTime || person?.familyCheckOut?.arrivalTime;
    return new moment(time).format("h:mm a");
  },
  "getGroupName": function(person) {
    const gId = person.checkInGroupId || person.defaultGroupId;
    const group = Groups.findOne(gId);
    return group && group.name;
  },
  'getCheckButtonIcon': function(person) {
    if (person.checkedIn) return "fa-sign-out text-black";
    return "fa-user-clock text-white";
  },
  'getCheckButtonText': function(person) {
    if (person.checkedIn) return "Check Out";
    return "Waiting for Checkin";
  },
  'getCheckButtonColor': function (person) {
    if (person.checkedIn) return "btn-secondary btn-text-black";
    return "btn-info btn-text-white";
  },
});

Template.expressDriveUp.events({
  "click #checkInOutAllHere": function() {
    const user = Meteor.user();
    const arrivals = getPeopleHere(user).fetch();
    let checkins = arrivals.map(p => { return !p.checkedIn ? p._id : null});
    let checkouts = arrivals.map(p => { return p.checkedIn ? p._id : null});
    checkins = _.without(checkins, null);
    checkouts = _.without(checkouts, null);

    if (checkins.length > 0) {
      Meteor.callAsync("checkInMultiple", {selectedPeople: checkins})
      .then((result)=>{
        mpSwal.fire("Success", "Checked in " + result.checkInCount, "success");
      })
      .catch((error)=>{
        mpSwal.fire("Error", error.reason, "error");
      });
    }

    if (checkouts.length > 0) {
      Meteor.callAsync("checkOutMultiple", {selectedPeople: checkouts})
      .then((result)=>{
        mpSwal.fire("Success", "Checked out " + result.checkInCount, "success");
      })
      .catch((error)=>{
        mpSwal.fire("Error", error.reason, "error");
      });
    }

  },
  "click .express-check-in-out-btn": function(e, i) {
    if (!processPermissions({
      assertions: [{ context: "people/movement", action: "edit" }],
      evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
    })) return false;

    const personId = $(e.currentTarget).attr("data-id");
    const person = People.findOne(personId);
    if (!person) return;
    Session.set("currentId", personId);
    if (person.checkedIn) {
      showModal("_checkoutFormModal", {personId: person._id}, "#_checkoutFormModal")
    } else {
      Session.set("currentGroupId", person.defaultGroupId);
      showModal("_checkinFormModal", {personId: person._id}, "#_checkinFormModal")
    }
  }
})
