import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './dashboardFamily.html';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import { Person } from '../../../lib/collections/people';
import '../moments/momentList.js';
import '../people/_documents.js';

Template.dashboardFamily.onCreated(function() {
	this.children = new ReactiveVar([]);
	
	let children = []
	const familyPerson = Meteor.user().fetchPerson()
	Meteor.callAsync("getFamilyPeople", familyPerson._id).then((result) => {
		const children = result?.familyPeople?.map((fp) => {
			if (fp.relationshipType === 'family') {
				return new Person(fp.person);
			}
		});
		this.children.set(children);
	}).catch((error) => {
		console.log("Error", error);
	});
	
});

Template.dashboardFamily.helpers({
	currentPerson() {
		if (!Meteor.user()) return;
		return Meteor.user().fetchPerson();
	},
	getWmgLabel() {
		return Session.get("wmgLabel");
	},
	showDocs(child) {
		const orgs = Orgs.current();
		const docs = orgs.documentDefinitions;
		const activeDocs = docs.filter((doc) => !doc.deletedAt && !doc.hideNotUploaded && doc.assignmentType == "all").map((doc) => doc._id);
		const individualDocs = docs.filter((doc) => !doc.deletedAt && !doc.hideNotUploaded && doc.assignmentType == "individuals").map((doc) => doc._id);
		const groupDocs = docs.filter((doc) => !doc.deletedAt && !doc.hideNotUploaded && doc.assignmentType == "groups");
		if (groupDocs.length > 0 && child.defaultGroupId) {
			groupDocs.forEach((doc) => {
			if (doc.selectedGroupIds.includes(child.defaultGroupId)) {
				activeDocs.push(doc._id)
			}	
			})
		}
		const childDocs = child.documentItems ? Object.keys(child.documentItems) : [];
		const assignedDocs = child.documentAssignments ?? [];
		const filteredIndivDocs = childDocs.length > 0 ? individualDocs.filter((doc) => assignedDocs.includes(doc)) : individualDocs;
		if (filteredIndivDocs.length > 0) {
			activeDocs.push(...filteredIndivDocs);
		}
		return checkStringsIncluded(activeDocs, childDocs);
	},
	showBlock(title) {
		switch (title) {
			case "billing":
				return _.deep(Orgs.current(), "billing.enabled");
			// case "familyCheckIn":
			// 	return Orgs.current() && Orgs.current().hasCustomization("people/familyCheckin/enabled") &&
			// 		Meteor.user() && 
			// 		_.filter(Meteor.user().fetchPerson().availableFamilyCheckins(), (ci) => {
			// 			return !ci.familyCheckIn || ci.familyCheckIn.checkInTime < new moment().startOf("day").valueOf();
			// 		}).length > 0;
		}
	},
	children() {
		const children = Template.instance().children.get();
		if (children) {
			return children;
		}
	},
	documentRepositoryItems: function() {
		const person = this, docs = person && person.documentItems, userPerson = Meteor.user() && Meteor.user().fetchPerson(),
			showArchived = Template.instance().showArchived.get();
		return person && person.type=="person" && userPerson && Orgs.current() &&
			_.chain(Orgs.current().documentDefinitions)
				.filter( (dd) => {
					const doc = docs && docs[dd._id];
					return (showArchived || !dd.deletedAt) &&
						(!dd.hideNotUploaded || doc) &&
						(showArchived || !doc || !doc.archivedAt) &&
						(!_.contains(person.documentExemptions, dd._id)) &&
						( !dd.assignmentType ||
							dd.assignmentType == "all" ||
							(dd.assignmentType == "groups" && _.contains(dd.selectedGroupIds, person.defaultGroupId)) ||
							(dd.assignmentType == "individuals" && _.contains(person.documentAssignments, dd._id))
						);
				})
				.map((dd, i) => {
					const doc = docs && docs[dd._id];
					dd.showViewTemplate = dd.repositoryKey && true;
					dd.showUploadDocument = userPerson.type=="admin" || userPerson.type=="family";
					dd.showReviewDocument = doc;
					dd.showApproveRejectDocument = userPerson.type=="admin" && doc && !(doc.approvedAt || docs.rejectedAt);
					dd.showExemptDocument = userPerson.type == "admin" && (!dd.assignmentType || dd.assignmentType == "all" || dd.assignmentType == "groups");
					dd.templateId = dd._id;
					dd.documentId = doc && dd._id;
					dd.showUnassignDocument = userPerson.type == "admin" && _.contains(person.documentAssignments, dd._id) && !dd.documentId;
					dd.showArchiveDocument = doc && !doc.archivedAt;
					dd.isArchived = (doc && doc.archivedAt) || dd.deletedAt;
					dd.signatureRequired = (dd.templateOption == "signature");
					dd.acknowledgmentRequired = (dd.templateOption == "ack");

					if (doc && doc.templateOptionResult) {
						if (doc.templateOptionResult.action == "ack" && dd.acknowledgmentRequired) {
							dd.displayTemplateOptionResult = `Acknowledged by ${doc.templateOptionResult.personName} on ${new moment(doc.templateOptionResult.date).format("M/D/YYYY")}`;
						}
					}

					if (doc && doc.approvedAt)
						dd.status = "Approved " + new moment(doc.approvedAt).format("M/D/YYYY");
					else if (doc && doc.rejectedAt)
						dd.status = "Rejected " + new moment(doc.rejectedAt).format("M/D/YYYY");
					else if (doc && doc.createdAt)
						dd.status = "Uploaded " + new moment(doc.createdAt).format("M/D/YYYY");
					else
						dd.status = "Not uploaded";
					return dd;
				})
				.sortBy( (dd) => { return dd.section + "|" + dd.name;})
				.value();
	},
	fullName(firstName, lastName) {
		return `${firstName} ${lastName}`;
		
	}
	// availableCheckins() {
	// 	return Meteor.user() && Meteor.user().fetchPerson().availableFamilyCheckins();
	// }
});

Template.dashboardFamily.events({
	"click .wmgButton": function(e, i) {
		$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Cuteness Loading').prop("disabled", true);
		Meteor.callAsync("getWmgToken")
		.then((result)=>{
			$(e.target).html(Session.get("wmgLabel")).prop("disabled", false);
			var url = "https://presence.internal.watchmegrow.com/v1/embed/player";
			var form = document.createElement("form");
			form.setAttribute("method", "post");
			form.setAttribute("action", url);
			form.setAttribute("target", "view");
			var hiddenField = document.createElement("input"); 
			hiddenField.setAttribute("type", "hidden");
			hiddenField.setAttribute("name", "jwt");
			hiddenField.setAttribute("value", result);
			form.appendChild(hiddenField);
			document.body.appendChild(form);
			window.open('', 'view');
			form.submit();
		})
		.catch((error)=>{
			$(e.target).html(Session.get("wmgLabel")).prop("disabled", false);
			mpSwal.fire("Error", error.reason, "error");
		})
	}
})
function checkStringsIncluded(activeDocs, childDocs) {
	for (let i = 0; i < activeDocs.length; i++) {
		if (!childDocs.includes(activeDocs[i])) {
			return true;
		}
	}
	return false;
}
