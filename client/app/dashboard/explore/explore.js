import moment from "moment-timezone";
import { Template } from 'meteor/templating';
import './explore.html';
import { RevenueExplorer } from './revenueExplorer';
import '../../reports/reportOrgsField';

Template.explore.onCreated (function () {
	this.revenueExplorer = new RevenueExplorer();
});

Template.explore.onRendered(function() {
	const self = this;
	const revenueExplorer = self.revenueExplorer;

	// Tracker to reactively update graphs when dashboard data changes
	Tracker.autorun(() => {
		const currentItem = revenueExplorer.generateCurrentItem();
		if (currentItem) {
			revenueExplorer.refreshGraphs(currentItem);
		}
	});

	// Refresh data initially
	revenueExplorer.refreshData();

	// Initialize the date range picker
	const filters = revenueExplorer.getFilters();
	const timezone = revenueExplorer.getTimezone();
	const todayMoment = moment.tz(timezone);
	$('input[name="mp-date-range-picker"]').daterangepicker({
		startDate: moment.tz(filters.startDate, "YYYY-MM-DD", timezone),
		endDate: moment.tz(filters.endDate, "YYYY-MM-DD", timezone),
		ranges: {
			'Today': [todayMoment, todayMoment],
			'Last 30 Days': [todayMoment.clone().subtract(30, 'days'), todayMoment],
			'Last 60 Days': [todayMoment.clone().subtract(60, 'days'), todayMoment],
			'Last 120 Days': [todayMoment.clone().subtract(120, 'days'), todayMoment],
			'Last 365 Days': [todayMoment.clone().subtract(365, 'days'), todayMoment]
		}
	}).on('apply.daterangepicker', function(ev, picker) {
		// Update filters on date change using class method
		revenueExplorer.mutateFilters({
			startDate: picker.startDate.format('YYYY-MM-DD'),
			endDate: picker.endDate.format('YYYY-MM-DD')
		})
	});
});

Template.explore.helpers({
	dataRows() {
		const revenueExplorer = Template.instance().revenueExplorer;

		// Get the necessary data from the revenueExplorer instance
		const dashboardData = revenueExplorer.getDashboardData();
		const timespan = revenueExplorer.getFilters().bucket || "months";
		const timezone = revenueExplorer.getTimezone();

		// Ensure that dashboardData exists before proceeding
		return dashboardData && dashboardData.boundaries.map(boundary => {
			// Find the corresponding item for the boundary
			const item = dashboardData.summaryOverTime.find(i => i._id.toString() === boundary.toString());

			// Return the structured data row
			return {
				amount: item ? item.sum : 0,  // Default to 0 if no item is found
				originalItem: item,  // Keep a reference to the original item
				label: {
					"weeks": new moment.tz(boundary, timezone).format("M/D/YY"),
					"months": new moment.tz(boundary, timezone).format("MMM YYYY"),
					"years": new moment.tz(boundary, timezone).format("YYYY")
				}[timespan]  // Use the label based on the timespan (weeks, months, years)
			};
		});
	},

	orgsFieldOpts() {
		const revenueExplorer = Template.instance().revenueExplorer;

		return {
			onChange(options) {
				// Set the current organizations
				revenueExplorer.setCurrentOrgs(options);

				// Refresh the data after updating the organizations
				revenueExplorer.refreshData();
			}
		};
	},

	valueColumns() {
		const revenueExplorer = Template.instance().revenueExplorer;
		const dataFilters = revenueExplorer.getDataFilters();

		// Check if the revenue type is "Payer" or "Family"
		if (dataFilters?.revenueType === "Payer" || dataFilters?.revenueType === "Family") {
			// Return columns based on revenue calculation (gross or not)
			return dataFilters?.revenueCalculation === "gross" ?
				[
					{ label: "Gross Amount", value: "sum" },
					{ label: "Discount Amount", value: "discount" },
					{ label: "Net Amount", value: "net" }
				] :
				[{ label: "Amount", value: "sum" }];
		} else {
			// For other revenue types, return family and payer columns based on revenue calculation
			return dataFilters?.revenueCalculation === "gross" ?
				[
					{ label: "Family Amount", value: "family" },
					{ label: "Payer Amount", value: "payer" },
					{ label: "Gross Amount", value: "sum" },
					{ label: "Discount Amount", value: "discount" },
					{ label: "Net Amount", value: "net" }
				] :
				[
					{ label: "Family Amount", value: "family" },
					{ label: "Payer Amount", value: "payer" },
					{ label: "Amount", value: "sum" }
				];
		}
	},

	getValue(dataRow, valueColumn) {
		return dataRow?.originalItem?.[valueColumn.value];
	},

	dataExplanation() {
		const revenueExplorer = Template.instance().revenueExplorer;
		const dashboardData = revenueExplorer.getDashboardData();
		const dataFilters = revenueExplorer.getDataFilters();

		if (dashboardData) {
			let msg = "Results include all plan and item revenue from non-voided invoices during the time period specified. ";

			if (!dataFilters.dateType) {
				msg += "Period dates are used where applicable, otherwise revenue is allocated to the date an invoice was created. ";
			} else {
				msg += "Revenue is allocated to the date an invoice was created. ";
			}

			return msg;
		}
	}
});

Template.explore.events({
	"change select[name=bucket]"(e, i) {
		const selectedValue = $("select[name=bucket]").val();
		i.revenueExplorer.mutateFilters({bucket: selectedValue});
	},
	"change .explore-filter"(e, i) {
		const dest = $(e.currentTarget).data("filter-id");
		const selectedValue = $(e.currentTarget).val();
		const mutation = {};

		mutation[dest] = selectedValue;
		i.revenueExplorer.mutateFilters(mutation);
	},
	"click #btnExport"() {
		const outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dtResults'), outputFile]);
	}
});
