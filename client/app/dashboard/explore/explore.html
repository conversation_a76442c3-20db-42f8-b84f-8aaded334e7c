<template name="explore">
	<div class="container-fluid" id="explore">

		<div class="row">
		  <div class="col-md-6">
			<div class="h2"> Revenue Performance <br/> <small class="text-muted">Total revenue invoiced over time</small> </div>
			
		  </div>
		  <div class="col-md-6">
			<div class="row">
			  <div class="col-7"></div>
			  <div data-cy="report-orgs" class="col-5 text-right">
				{{>reportOrgsField fullwidth=true opts=orgsFieldOpts}}
			  </div>
			  
			</div>
		  </div>
	
		</div>
	
		<div class="row mt-12">

			<div class="col-12">
				<div class="flex-grow-1">
					<div class="card">
						<div class="card-body pb-3 pt-3">
							<div class="row d-flex flex-row align-items-center">
								<div class="p-6 text-right font-weight-bold">
									Revenue Source
								</div>
								<div class="px-6">
									<select data-cy="filter-revenue-source" class="form-control explore-filter" data-filter-id="revenueType" name="filter-revenue-type">
										<option value="">Both</option>
										<option value="Family">Family</option>
										<option value="Payer">Payer</option>
									</select>
								</div>
								<div class="px-6 text-right font-weight-bold">
									Revenue Type
								</div>
								<div class="px-6">
									<select data-cy="filter-revenue-type" class="form-control explore-filter" data-filter-id="revenueCalculation" name="filter-revenue-calculation">
										<option value="">Net</option>
										<option value="gross">Gross</option>
									</select>
								</div>
								<div class="px-6 text-right font-weight-bold">
									Date Filter
								</div>
								<div class="px-6">
									<select data-cy="filter-revenue-period" class="form-control explore-filter" data-filter-id="dateType" name="filter-date-type">
										<option value="">Period Start</option>
										<option value="create">Invoice Created</option>
									</select>
								</div>
							</div>
						</div>
					</div>

					<div class="card mt-8">
						<div class="card-body pb-3 pt-3">
							<div class="row">
								<div class="col-9"></div>
								<div class="col-1">
									<span style="font-weight:bold">Grouping</span><br/>
									<select data-cy="revenue-grouping" class="form-control" name=bucket>
										<option value="weeks">Weekly</option>
										<option value="months" selected>Monthly</option>
										<option value="years">Yearly</option>
									</select>
								</div>
								<div class="col-2">
									<span style="font-weight:bold">Date Range</span><br/>
									<div class="d-flex flex-row min-w-200px">
										<input data-cy="revenue-date-range" type="text" name="mp-date-range-picker" class="form-control">
									</div>
								</div>
							</div>
							<div id="explore-chart" style="height:350px;"></div>
						</div>
					</div>
					
					{{#if dataExplanation}}
					<div class="card mt-8">
						<div class="card-body pb-3 pt-3">
							<span class="font-weight-bold">Data Explanation:</span> {{dataExplanation}}
						</div>
					</div>
					{{/if}}

					{{#if dataRows}}
					<div class="card mt-8">
						<div class="card-body">
							<div class="text-right pb-3"><button data-cy="revenue-export-csv-btn" class="btn btn-secondary" id="btnExport">Export CSV</button></div>
							<table class="table table-bordered" id="dtResults">
								<thead>
									<tr>
										<th>Date</th>
										{{#each valueColumn in valueColumns}}
										<th class="text-right">{{valueColumn.label}}</th>
										{{/each}}
									</tr>
								</thead>
								<tbody>
									{{#each dataRow in dataRows}}
									<tr>
										<td data-cy="table-label">{{dataRow.label}}</td>
										{{#each valueColumn in valueColumns}}
										<td data-cy="table-value" class="text-right">{{formatCurrency (getValue dataRow valueColumn)}}</td>
										{{/each}}
									</tr>
									{{/each}}
								</tbody>
							</table>
						</div>
					</div>
					{{/if}}
				</div>
			</div>
		</div>
	
	  </div>
</template>