import moment from 'moment-timezone';
import { Log } from '../../../../lib/util/log';
import { RenderApexCharts } from '../../../lib/renderApexCharts';
import { Orgs } from '../../../../lib/collections/orgs';

export class RevenueExplorer {
    constructor(filters = null) {
        this.dashboardData = new ReactiveVar();
        this.timezone = Orgs.current().getTimezone();
        const defaultFilters = filters || this.getDefaultFilter();

        this.filters = new ReactiveVar(defaultFilters);
        this.currentOrgs = new ReactiveVar();
        this.dataFilters = new ReactiveVar({});
    }

    getTimezone() {
        return this.timezone;
    }

    setDashboardData(data) {
        this.dashboardData.set(data);
    }

    getDashboardData() {
        return this.dashboardData.get();
    }

    setFilters(filters) {
        if (filters && typeof filters === 'object') {
            this.filters.set(filters);
        } else {
            Log.error('Invalid filters provided');
        }
    }

    getFilters() {
        return this.filters.get();
    }

    setCurrentOrgs(orgs) {
        this.currentOrgs.set(orgs);
    }

    getCurrentOrgs() {
        return this.currentOrgs.get();
    }

    setDataFilters(filters) {
        this.dataFilters.set(filters);
    }

    getDataFilters() {
        return this.dataFilters.get();
    }

    getDefaultFilter() {
        return {
            endDate: new moment.tz(this.getTimezone()).format("YYYY-MM-DD"),
            startDate: new moment.tz(this.getTimezone()).add(-6, "months").format("YYYY-MM-DD"),
            bucket: "months"
        };
    }

    generateCurrentItem() {
        const dashboardData = this.getDashboardData();
        const filters = this.getFilters();
        const timezone = this.getTimezone();

        if (!dashboardData || !filters) {
            return null;
        }

        const latestItemBoundary = Math.max(dashboardData.boundaries);
        const latestItem = dashboardData.summaryOverTime.find(i => i._id.toString() === latestItemBoundary.toString()) || { sum: 0 };
        const timespan = filters.bucket || "months";
        const seriesLabels = filters.revenueType ? ["sum"] : ["payer", "family"];

        return {
            itemId: "revenuePerformance",
            style: "fullGraph",
            graphStyle: "bars",
            verticalBars: true,
            showLabels: true,
            isCurrency: true,
            title: "Revenue Performance",
            metric: numeral(latestItem.sum).format("$0,000"),
            subTitle: "This " + timespan.slice(0, -1), // Removes the last character
            dataSeries: seriesLabels.map(seriesLabel => ({
                name: seriesLabel,
                data: dashboardData.boundaries.map(boundary => {
                    const item = dashboardData.summaryOverTime.find(i => i._id.toString() === boundary.toString());
                    return {
                        amount: item ? item[seriesLabel] : 0,
                        label: ({
                            "weeks": new moment.tz(boundary, timezone).format("M/D/YY"),
                            "months": new moment.tz(boundary, timezone).format("MMM YYYY"),
                            "years": new moment.tz(boundary, timezone).format("YYYY")
                        })[timespan]
                    };
                })
            })),
            dropdownItems: [
                { label: "Weekly", value: "weeks" },
                { label: "Monthly", value: "months" },
                { label: "Yearly", value: "years" }
            ],
            dropdownLabel: "View"
        };
    }


    refreshData() {
        const filters = this.getFilters();
        const options = {
            name: "revenueExplorer",
            timespan: filters.bucket,
            startDate: filters.startDate,
            endDate: filters.endDate,
            orgIds: this.getCurrentOrgs(),
            timezone: this.getTimezone(),
            // topic-specific filters
            revenueType: filters.revenueType,
            dateType: filters.dateType,
            revenueCalculation: filters.revenueCalculation
        };

        Meteor.callAsync("getDashboardItem", options)
        .then((result)=>{
            this.setDashboardData(result);
        })
        .catch((error)=>{
            mpSwal.fire(error.error, error.reason || error.message, "error");
            return;
        });
    }

    mutateFilters(options) {
        const filters = this.getFilters();
        const updatedFilters = _.extend(filters, options);
        this.setFilters(updatedFilters);
        this.refreshData();
        this.setDataFilters(updatedFilters);
    }

    refreshGraphs(item) {
        if (item.style === "fullGraph" && item.graphStyle === "line") {
            RenderApexCharts.initApexLineChart({
                elementId: "explore-chart",
                item
            });
        } else if (item.style === "fullGraph" && item.graphStyle === "bars") {
            RenderApexCharts.initApexBarChart({
                elementId: "explore-chart",
                item
            });
        } else if (item.style === "fullGraph" && item.graphStyle === "pie") {
            RenderApexCharts.initApexPieChart({
                elementId: "explore-chart",
                item
            });
        } else if (item.style === "fullGraph" && item.graphStyle === "verticalBars") {
            RenderApexCharts.initApexVerticalBar({
                elementId: "explore-chart",
                item
            });
        }
    }
}