<template name="performanceMonitoring">
    <div class="performance-monitoring-container">
        <h1>Performance Monitoring</h1>

        <div class="tabs">
            <button class="tab-btn {{isActiveTab 'overview'}}" data-tab="overview">Overview</button>
            <button class="tab-btn {{isActiveTab 'methods'}}" data-tab="methods">Methods</button>
            <button class="tab-btn {{isActiveTab 'subscriptions'}}" data-tab="subscriptions">Subscriptions</button>
            <button class="tab-btn {{isActiveTab 'routes'}}" data-tab="routes">Routes</button>
            <button class="tab-btn {{isActiveTab 'critical'}}" data-tab="critical">Critical</button>
            <button class="tab-btn {{isActiveTab 'heavyQueries'}}" data-tab="heavyQueries">Heavy Queries</button>
        </div>

        {{#if isLoading}}
        <p>Loading metrics...</p>
        {{else}}
        <!-- Overview Tab -->
        {{#if isActiveTab 'overview'}}
        <div class="metrics-grid">
            <div class="metric-box">
                <div class="metric-title">Users</div>
                <div class="metric-value">{{metrics.userCount}}</div>
            </div>
            <div class="metric-box">
                <div class="metric-title">Active Sessions</div>
                <div class="metric-value">
                    <span>{{metrics.activeSessionsCount}}</span>
                </div>
            </div>
            <div class="metric-box">
                <div class="metric-title">Avg Response Time</div>
                <div class="metric-value">{{metrics.averageResponseTime}}ms</div>
            </div>
            <div class="metric-box">
                <div class="metric-title">Error Rate</div>
                <div class="metric-value">{{metrics.errorRate}}%</div>
            </div>
        </div>

        <h3>Critical Performance Issues</h3>
        {{#if hasCriticalPerformance}}
        <div class="critical-summary">
            <div class="critical-count">
                <span class="count-number">{{criticalMethodsCount}}</span>
                <span class="count-label">Slow Methods</span>
            </div>
            <div class="critical-count">
                <span class="count-number">{{criticalSubscriptionsCount}}</span>
                <span class="count-label">Slow Subscriptions</span>
            </div>
        </div>
        {{else}}
        <p class="no-issues">No critical performance issues detected.</p>
        {{/if}}
        {{/if}}

        <!-- Methods Tab -->
        {{#if isActiveTab 'methods'}}
        <div class="methods-header">
            <input type="text" id="methodSearch" placeholder="Search methods..." class="search-input">
            <div class="methods-filter">
                <label>
                    <input type="checkbox" id="showCriticalOnly" class="critical-checkbox">
                    Show critical methods only
                </label>
            </div>
        </div>

        {{#if methods.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Method Name</th>
                    <th>Call Count</th>
                    <th>Avg Time (ms)</th>
                    <th>Max Time (ms)</th>
                    <th>Memory Usage</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {{#each methods}}
                <tr class="{{methodRowClass this}}">
                    <td>{{name}}</td>
                    <td>{{count}}</td>
                    <td>{{avgTime}}</td>
                    <td>{{maxTime}}</td>
                    <td>{{formatSize memoryUsage}}</td>
                    <td>
                        {{#if isCriticalMethod this}}
                        <span class="status-critical">Critical</span>
                        {{else}}
                        {{#if isSlowMethod this}}
                        <span class="status-warning">Slow</span>
                        {{else}}
                        <span class="status-normal">Normal</span>
                        {{/if}}
                        {{/if}}
                    </td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p>No method calls recorded yet.</p>
        {{/if}}
        {{/if}}

        <!-- Subscriptions Tab -->
        {{#if isActiveTab 'subscriptions'}}
        <div class="subs-header">
            <input type="text" id="subscriptionSearch" placeholder="Search subscriptions..." class="search-input">
            <div class="subs-filter">
                <label>
                    <input type="checkbox" id="showCriticalSubsOnly" class="critical-checkbox">
                    Show critical subscriptions only
                </label>
            </div>
        </div>

        <!-- Active Users -->
        {{#if showingActiveUsers}}
        <div class="active-users-modal">
            <h3>Active Users ({{activeUsers.length}})</h3>
            <button id="closeActiveUsers">Close</button>
            {{#if activeUsers.length}}
            <table class="metrics-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Last Activity</th>
                        <th>Session Duration</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each user in activeUsers}}
                    <tr>
                        <td>{{user.username}}</td>
                        <td>{{formatDate user.lastActivity}}</td>
                        <td>{{formatDuration user.sessionDuration}}</td>
                        <td>{{user.ipAddress}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
            {{else}}
            <p>No active users found.</p>
            {{/if}}
        </div>
        {{/if}}

        {{#if subscriptions.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Subscription Name</th>
                    <th>Active Count</th>
                    <th>Ready Time (ms)</th>
                    <th>Data Size</th>
                    <th>Document Count</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {{#each subscriptions}}
                <tr class="{{subscriptionRowClass this}}">
                    <td>{{name}}</td>
                    <td>{{count}}</td>
                    <td>{{readyTime}}</td>
                    <td>{{formatSize dataSize}}</td>
                    <td>{{documentCount}}</td>
                    <td>
                        {{#if isCriticalSubscription this}}
                        <span class="status-critical">Critical</span>
                        {{else}}
                        {{#if isSlowSubscription this}}
                        <span class="status-warning">Slow</span>
                        {{else}}
                        <span class="status-normal">Normal</span>
                        {{/if}}
                        {{/if}}
                    </td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p>No subscription data recorded yet.</p>
        {{/if}}
        {{/if}}

        <!-- Routes Tab -->
        {{#if isActiveTab 'routes'}}
        {{#if routes.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Route Path</th>
                    <th>Visit Count</th>
                    <th>Avg Load Time (ms)</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {{#each routes}}
                <tr class="{{routeRowClass this}}">
                    <td>{{path}}</td>
                    <td>{{visitCount}}</td>
                    <td>{{avgLoadTime}}</td>
                    <td>
                        {{#if isSlowRoute this}}
                        <span class="status-warning">Slow</span>
                        {{else}}
                        <span class="status-normal">Normal</span>
                        {{/if}}
                    </td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p>No route data recorded yet.</p>
        {{/if}}
        {{/if}}

        <!-- Heavy Queries Tab -->
        {{#if isActiveTab 'heavyQueries'}}
        <div class="heavy-queries-header">
            <input type="text" id="querySearch" placeholder="Search queries..." class="search-input">
            <div class="queries-filter">
                <label>
                    <input type="checkbox" id="showHeavyOnly" class="heavy-checkbox">
                    Show heavy queries only
                </label>
            </div>
        </div>

        {{#if heavyQueries.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Query Name</th>
                    <th>Collection</th>
                    <th>Execution Count</th>
                    <th>Avg Time (ms)</th>
                    <th>Max Time (ms)</th>
                    <th>Documents Scanned</th>
                    <th>Memory Impact</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {{#each heavyQueries}}
                <tr class="{{queryRowClass this}}">
                    <td>{{name}}</td>
                    <td>{{collection}}</td>
                    <td>{{executionCount}}</td>
                    <td>{{avgTime}}</td>
                    <td>{{maxTime}}</td>
                    <td>{{documentsScanned}}</td>
                    <td>{{formatSize memoryImpact}}</td>
                    <td>
                        {{#if isHeavyQuery this}}
                        <span class="status-critical">Heavy</span>
                        {{else}}
                        {{#if isModerateQuery this}}
                        <span class="status-warning">Moderate</span>
                        {{else}}
                        <span class="status-normal">Normal</span>
                        {{/if}}
                        {{/if}}
                    </td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="query-optimization-suggestions">
            <h3>Optimization Suggestions</h3>
            {{#if optimizationSuggestions.length}}
            <ul>
                {{#each optimizationSuggestions}}
                <li class="suggestion-item">
                    <span class="suggestion-title">{{title}}</span>
                    <p class="suggestion-details">{{details}}</p>
                    <span class="suggestion-impact">Potential Impact: {{impact}}</span>
                </li>
                {{/each}}
            </ul>
            {{else}}
            <p>No optimization suggestions available at the moment.</p>
            {{/if}}
        </div>
        {{else}}
        <p>No heavy queries recorded yet.</p>
        {{/if}}
        {{/if}}

        <!-- Critical Tab -->
        {{#if isActiveTab 'critical'}}
        <div class="critical-header">
            <h2>System Health</h2>
            <button class="refresh-btn" id="refreshCritical">Refresh Now</button>
        </div>

        <div class="metrics-grid">
            <div class="metric-box {{#if isAboveThreshold criticalMetrics.memoryUsage 80}}critical{{/if}}">
                <div class="metric-title">Memory Usage</div>
                <div class="metric-value">{{criticalMetrics.memoryUsage}}%</div>
                <div class="metric-trend">{{{criticalMetrics.memoryTrend}}}</div>
            </div>
            <div class="metric-box {{#if isAboveThreshold criticalMetrics.cpuUsage 75}}critical{{/if}}">
                <div class="metric-title">CPU Usage</div>
                <div class="metric-value">{{criticalMetrics.cpuUsage}}%</div>
                <div class="metric-trend">{{{criticalMetrics.cpuTrend}}}</div>
            </div>
            <div class="metric-box {{#if isAboveThreshold criticalMetrics.processUptime 0 true}}{{else}}warning{{/if}}">
                <div class="metric-title">Process Uptime</div>
                <div class="metric-value">{{formatDuration criticalMetrics.processUptime}}</div>
            </div>
            <div class="metric-box {{criticalMetrics.clusterStatusClass}}">
                <div class="metric-title">Cluster Status</div>
                <div class="metric-value">{{criticalMetrics.clusterStatus}}</div>
            </div>
        </div>

        <h3>Active Issues</h3>
        {{#if criticalMetrics.activeIssues.length}}
        <table class="metrics-table issues-table">
            <thead>
                <tr>
                    <th>Issue Type</th>
                    <th>Severity</th>
                    <th>Details</th>
                    <th>Detected</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {{#each criticalMetrics.activeIssues}}
                <tr class="{{severity}}-issue">
                    <td>{{type}}</td>
                    <td><span class="severity-badge {{severity}}">{{severity}}</span></td>
                    <td>{{details}}</td>
                    <td>{{formatDate detected}}</td>
                    <td>{{status}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p class="no-issues">No active issues detected.</p>
        {{/if}}

        <h3>Memory Leaks</h3>
        {{#if criticalMetrics.memoryLeaks.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Component</th>
                    <th>Size</th>
                    <th>Growth Rate</th>
                    <th>First Detected</th>
                </tr>
            </thead>
            <tbody>
                {{#each criticalMetrics.memoryLeaks}}
                <tr class="{{#if isHighGrowth growthRate}}critical{{/if}}">
                    <td>{{component}}</td>
                    <td>{{formatSize size}}</td>
                    <td>{{growthRate}}%/hr</td>
                    <td>{{formatDate firstDetected}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p class="no-issues">No memory leaks detected.</p>
        {{/if}}

        <h3>Critical Methods</h3>
        {{#if criticalMethods.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Method Name</th>
                    <th>Call Count</th>
                    <th>Avg Time (ms)</th>
                    <th>Max Time (ms)</th>
                    <th>Memory Usage</th>
                </tr>
            </thead>
            <tbody>
                {{#each criticalMethods}}
                <tr class="critical-row">
                    <td>{{name}}</td>
                    <td>{{count}}</td>
                    <td>{{avgTime}}</td>
                    <td>{{maxTime}}</td>
                    <td>{{formatSize memoryUsage}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p class="no-issues">No critical methods detected.</p>
        {{/if}}

        <h3>Critical Subscriptions</h3>
        {{#if criticalSubscriptions.length}}
        <table class="metrics-table">
            <thead>
                <tr>
                    <th>Subscription Name</th>
                    <th>Active Count</th>
                    <th>Ready Time (ms)</th>
                    <th>Data Size</th>
                    <th>Document Count</th>
                </tr>
            </thead>
            <tbody>
                {{#each criticalSubscriptions}}
                <tr class="critical-row">
                    <td>{{name}}</td>
                    <td>{{count}}</td>
                    <td>{{readyTime}}</td>
                    <td>{{formatSize dataSize}}</td>
                    <td>{{documentCount}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <p class="no-issues">No critical subscriptions detected.</p>
        {{/if}}
        {{/if}}
        {{/if}}
    </div>
</template>