<template name="switchOrg">

  <div class="card card-custom mx-24">
    <!-- /.login-logo -->
    <div class="login-box-body">
      <h3 class="ml-16 mt-8">Switch Org</h3>
      <div class="d-flex flex-row justify-content-center align-items-center">
        <form action="" id="switchOrgForm" method="post">
          
          <div class="form-group has-feedback">
            <select id="orgsList" style="min-width: 450px">
              {{#each org in orgs}}
              <option value='{{org._id}}'>{{org.name}} {{#if doShowId}}{{org._id}}{{/if}}</option>
              {{/each}}
            </select>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="checkbox">
                  <label><input type="checkbox" id="showAll"> Show All</label>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="checkbox">
                <label><input type="checkbox" id="showId"> Show Org Id</label>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-4 mb-4">
              <button type="submit" class="btn btn-primary btn-block btn-flat" id="login-button">Switch</button>
            </div>
            <!-- /.col -->
          </div>
        </form>
      </div>

    </div>
    <!-- /.login-box-body -->
  </div>
  <!-- /.login-box -->

</template>
