import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './profileDefinitionEditor.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Orgs } from '../../../lib/collections/orgs';
import { showModal } from '../main';
import '../people/_addPersonModal';

var profileDefinitionEditor;

var processProfileType = function (paramType) {
   switch (paramType) {
    case "staff":
      return {prefix: "staffProfileFields", title: "Staff"};
    case "family":
      return {prefix: "familyProfileFields", title: "Family"};
    case "prospect":
      return {prefix: "inquiryProfileFields", title: "Inquiry"};
    case "pincodefields":
      return {prefix: "pinCodeCheckinFields", title: "Pin Code Fields"};
    case "deactivationReasons":
      return {prefix: "deactivationReasons", title: "Deactivation Reasons (Person/Family)"};
    case "deactivationReasonsStaff":
      return {prefix: "deactivationReasonsStaff", title: "Deactivation Reasons Staff"}
    default:
      return {prefix: "profileFields", title: "Person"};
   }
};

Template.profileDefinitionEditor.onCreated(function() {
	this.activeProfile = new ReactiveVar("");
});

Template.profileDefinitionEditor.rendered = function() {
	Tracker.autorun(function (e) {
		var curId = FlowRouter.current().params.id;
		var org = Orgs.findOne({_id: curId});

      var curProfileType = processProfileType(FlowRouter.current().queryParams.type);

		profileDefinitionEditor = AceEditor.instance("profileEditor", {theme: "dawn", mode: "json"});
		if(profileDefinitionEditor.loaded!==undefined){
			e.stop();	
			if (org && org.valueOverrides && org.valueOverrides[curProfileType.prefix]) {
        profileDefinitionEditor.insert(JSON.stringify(org.valueOverrides[curProfileType.prefix], null, '\t'));
      }
		}
	});
};

Template.profileDefinitionEditor.helpers({
	"title": function() {
		var curId = FlowRouter.current().params.id;
		var org = Orgs.findOne({_id: curId});
      var curProfileType = processProfileType(FlowRouter.current().queryParams.type);
		if (org) return org.name + " - " + curProfileType.title;	
	},
	"thisCarePlan": function() {
		return Template.instance().activeProfile.get();
	}
});

Template.profileDefinitionEditor.events({
	'click #previewLink': function() {
		var definitionData = JSON.parse(profileDefinitionEditor.getValue() || "");
		Session.set("currentEditPersonId", null);
      Session.set("editPersonModalPersonType", FlowRouter.current().queryParams.type);
      Session.set("editPersonPreviewProfileFields", definitionData);

      showModal("_addPersonModal", {}, "#_addPersonModal");
	},
	'click #saveLink': function() {
		var curId = FlowRouter.current().params.id;
      	var curProfileType = processProfileType(FlowRouter.current().queryParams.type);
		var definitionData = JSON.parse(profileDefinitionEditor.getValue());
		Meteor.callAsync("updateProfileDefinition", curId, curProfileType.prefix, definitionData).then((curId) => {
			mpSwal.fire("Save Successful!");
		}).catch((error) => {
			mpSwal.fire("Error during save: " + error);
		});
	},
	'click #cancelLink': function() {
		FlowRouter.go("/superadmin/customers/" + FlowRouter.current().params.id);
	}
});
