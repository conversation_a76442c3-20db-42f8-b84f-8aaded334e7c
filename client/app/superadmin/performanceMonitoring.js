import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './performanceMonitoring.html';
import './performanceMonitoring.css';

const THRESHOLDS = {
    METHOD_TIME_WARNING: 500,
    METHOD_TIME_CRITICAL: 2000,
    METHOD_MEMORY_WARNING: 10 * 1024 * 1024, // 10MB
    METHOD_MEMORY_CRITICAL: 50 * 1024 * 1024, // 50MB
    SUBSCRIPTION_TIME_WARNING: 1000,
    SUBSCRIPTION_TIME_CRITICAL: 5000,
    SUBSCRIPTION_SIZE_WARNING: 1 * 1024 * 1024, // 1MB
    SUBSCRIPTION_SIZE_CRITICAL: 10 * 1024 * 1024, // 10MB
    ROUTE_TIME_WARNING: 1000,
    QUERY_TIME_WARNING: 500,
    QUERY_TIME_CRITICAL: 2000,
    QUERY_MEMORY_WARNING: 5 * 1024 * 1024, // 5MB
    QUERY_MEMORY_CRITICAL: 20 * 1024 * 1024, // 20MB
    QUERY_DOCS_WARNING: 1000,
    QUERY_DOCS_CRITICAL: 10000
};


// Global variables for route tracking
let currentRoute = null;
let routeEntryTime = null;

Template.performanceMonitoring.onCreated(function () {
    const instance = this;

    // Initialize reactive variables
    instance.activeTab = new ReactiveVar('overview');
    instance.metrics = new ReactiveVar({
        userCount: 0,
        activeSessionsCount: 0,
        averageResponseTime: 0,
        errorRate: "0.00"
    });
    instance.methods = new ReactiveVar([]);
    instance.subscriptions = new ReactiveVar([]);
    instance.routes = new ReactiveVar([]);
    instance.criticalMetrics = new ReactiveVar(null);
    instance.isLoading = new ReactiveVar(true);
    instance.heavyQueries = new ReactiveVar([]);
    instance.optimizationSuggestions = new ReactiveVar([]);

    // Filter variables
    instance.showCriticalMethodsOnly = new ReactiveVar(false);
    instance.showCriticalSubsOnly = new ReactiveVar(false);
    instance.methodSearchTerm = new ReactiveVar('');
    instance.subscriptionSearchTerm = new ReactiveVar('');
    instance.querySearchTerm = new ReactiveVar('');

    instance.activeUsers = new ReactiveVar([]);
    instance.showingActiveUsers = new ReactiveVar(false);

    // Load initial overview metrics
    Meteor.call('getPerformanceMetrics', (error, result) => {
        if (error) {
            console.error('[Performance Monitor] Error fetching initial metrics:', error);
            instance.metrics.set({
                userCount: 0,
                activeSessionsCount: 0,
                averageResponseTime: 0,
                errorRate: "0.00",
                connectionMetrics: {
                    active: 0,
                    total: 0,
                    lastUpdate: Date.now()
                }
            });
        } else if (result) {
            instance.metrics.set(result);
        } else {
            console.warn('[Performance Monitor] No metrics data received');
            instance.metrics.set({
                userCount: 0,
                activeSessionsCount: 0,
                averageResponseTime: 0,
                errorRate: "0.00",
                connectionMetrics: {
                    active: 0,
                    total: 0,
                    lastUpdate: Date.now()
                }
            });
        }
        instance.isLoading.set(false);
    });

    // Load data when changing tabs
    instance.autorun(() => {
        const currentTab = instance.activeTab.get();

        // Set loading state when changing tabs
        instance.isLoading.set(true);

        if (currentTab === 'overview') {
            Meteor.call('getPerformanceMetrics', (error, result) => {
                if (!error) {
                    instance.metrics.set(result);
                } else {
                    console.error('[Performance Monitor] Error updating metrics:', error);
                }
                instance.isLoading.set(false);
            });
        } else if (currentTab === 'methods') {
            Meteor.call('getMethodMetrics', (error, result) => {
                if (!error) {
                    // Apply filtering if needed
                    let filteredMethods = result;
                    if (instance.showCriticalMethodsOnly.get()) {
                        filteredMethods = filteredMethods.filter(method =>
                            method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
                            (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL)
                        );
                    }

                    const searchTerm = instance.methodSearchTerm.get();
                    if (searchTerm) {
                        filteredMethods = filteredMethods.filter(method =>
                            method.name.toLowerCase().includes(searchTerm.toLowerCase())
                        );
                    }

                    instance.methods.set(filteredMethods);
                } else {
                    console.error('[Performance Monitor] Error fetching method metrics:', error);
                }
                instance.isLoading.set(false);
            });
        } else if (currentTab === 'subscriptions') {
            Meteor.call('getSubscriptionMetrics', (error, result) => {
                if (!error) {
                    // Apply filtering if needed
                    let filteredSubs = result;
                    if (instance.showCriticalSubsOnly.get()) {
                        filteredSubs = filteredSubs.filter(sub =>
                            sub.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
                            (sub.dataSize && sub.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)
                        );
                    }

                    const searchTerm = instance.subscriptionSearchTerm.get();
                    if (searchTerm) {
                        filteredSubs = filteredSubs.filter(sub =>
                            sub.name.toLowerCase().includes(searchTerm.toLowerCase())
                        );
                    }

                    instance.subscriptions.set(filteredSubs);
                } else {
                    console.error('[Performance Monitor] Error fetching subscription metrics:', error);
                }
                instance.isLoading.set(false);
            });
        } else if (currentTab === 'routes') {
            Meteor.call('getRouteMetrics', (error, result) => {
                if (!error) {
                    console.log('[Performance Monitor] Route metrics loaded:', result);
                    instance.routes.set(result);
                } else {
                    console.error('[Performance Monitor] Error fetching route metrics:', error);
                }
                instance.isLoading.set(false);
            });
        } else if (currentTab === 'critical') {
            Meteor.call('getCriticalMetrics', (error, result) => {
                if (!error) {
                    console.log('[Performance Monitor] Critical metrics loaded:', result);
                    instance.criticalMetrics.set(result);

                    // Load critical methods and subscriptions
                    Meteor.call('getCriticalMethods', (error, methods) => {
                        if (!error) {
                            instance.methods.set(methods);
                        }
                    });

                    Meteor.call('getCriticalSubscriptions', (error, subs) => {
                        if (!error) {
                            instance.subscriptions.set(subs);
                        }
                    });
                } else {
                    console.error('[Performance Monitor] Error fetching critical metrics:', error);
                }
                instance.isLoading.set(false);
            });
        } else if (currentTab === 'heavyQueries') {
            // Load heavy queries
            Meteor.call('getHeavyQueries', (error, queries) => {
                if (!error) {
                    instance.heavyQueries.set(queries);
                } else {
                    console.error('[Performance Monitor] Error fetching heavy queries:', error);
                }
                instance.isLoading.set(false);
            });

            // Load optimization suggestions
            Meteor.call('getQueryOptimizationSuggestions', (error, suggestions) => {
                if (!error) {
                    instance.optimizationSuggestions.set(suggestions);
                } else {
                    console.error('[Performance Monitor] Error fetching optimization suggestions:', error);
                }
            });
        }
    });

    // Set up auto-refresh every 30 seconds
    instance.refreshInterval = Meteor.setInterval(() => {
        const currentTab = instance.activeTab.get();

        if (currentTab === 'overview') {
            Meteor.call('getPerformanceMetrics', (error, result) => {
                if (!error) {
                    console.log('[Performance Monitor] Received updated metrics:', result);
                    instance.metrics.set(result);
                } else {
                    console.error('[Performance Monitor] Error updating metrics:', error);
                }
            });
        } else if (currentTab === 'routes') {
            Meteor.call('getRouteMetrics', (error, result) => {
                if (!error) instance.routes.set(result);
            });
        } else if (currentTab === 'critical') {
            Meteor.call('getCriticalMetrics', (error, result) => {
                if (!error) instance.criticalMetrics.set(result);
            });
        }
    }, 30000);

    // Add immediate refresh when tab changes to overview
    instance.autorun(() => {
        if (instance.activeTab.get() === 'overview') {
            Meteor.call('getPerformanceMetrics', (error, result) => {
                if (!error) {
                    console.log('[Performance Monitor] Received initial metrics:', result);
                    instance.metrics.set(result);
                } else {
                    console.error('[Performance Monitor] Error getting initial metrics:', error);
                }
            });
        }
    });
});

Template.performanceMonitoring.onDestroyed(function () {
    // Clean up interval
    Meteor.clearInterval(this.refreshInterval);
});

Template.performanceMonitoring.helpers({
    isLoading() {
        return Template.instance().isLoading.get();
    },

    JSON(obj) {
        return JSON.stringify(obj, null, 2);
    },
    activeUsers() {
        return Template.instance().activeUsers.get();
    },

    showingActiveUsers() {
        return Template.instance().showingActiveUsers.get();
    },
    metrics() {
        const metrics = Template.instance().metrics.get();
        if (!metrics || metrics.activeSessionsCount === 0) {
            console.warn('[Performance Monitor] Missing or incomplete metrics data');
        }

        return metrics || {
            userCount: 0,
            activeUserCount: 0,
            activeSessionsCount: 0,
            averageResponseTime: 0,
            errorRate: "0.00",
            connectionMetrics: {
                active: 0,
                total: 0,
                lastUpdate: Date.now()
            }
        };
    },

    methods() {
        return Template.instance().methods.get() || [];
    },

    subscriptions() {
        return Template.instance().subscriptions.get() || [];
    },

    routes() {
        return Template.instance().routes.get() || [];
    },

    criticalMetrics() {
        return Template.instance().criticalMetrics.get() || {
            memoryUsage: 0,
            memoryTrend: "stable",
            cpuUsage: 0,
            cpuTrend: "stable",
            processUptime: 0,
            clusterStatus: "Unknown",
            clusterStatusClass: "",
            activeIssues: [],
            memoryLeaks: [],
            awsResources: [],
            recentErrors: []
        };
    },

    criticalMethods() {
        const methods = Template.instance().methods.get() || [];
        return methods.filter(method =>
            method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
            (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL)
        );
    },

    criticalSubscriptions() {
        const subs = Template.instance().subscriptions.get() || [];
        return subs.filter(sub =>
            sub.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
            (sub.dataSize && sub.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)
        );
    },

    hasCriticalPerformance() {
        const instance = Template.instance();
        const methods = instance.methods.get() || [];
        const subscriptions = instance.subscriptions.get() || [];

        const criticalMethods = methods.filter(method =>
            method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
            (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL)
        );

        const criticalSubscriptions = subscriptions.filter(sub =>
            sub.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
            (sub.dataSize && sub.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)
        );

        return criticalMethods.length > 0 || criticalSubscriptions.length > 0;
    },

    criticalMethodsCount() {
        const methods = Template.instance().methods.get() || [];
        return methods.filter(method =>
            method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
            (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL)
        ).length;
    },

    criticalSubscriptionsCount() {
        const subs = Template.instance().subscriptions.get() || [];
        return subs.filter(sub =>
            sub.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
            (sub.dataSize && sub.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)
        ).length;
    },

    isActiveTab(tab) {
        return Template.instance().activeTab.get() === tab ? 'active' : '';
    },

    methodRowClass(method) {
        if (method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
            method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL) {
            return 'critical-row';
        } else if (method.avgTime >= THRESHOLDS.METHOD_TIME_WARNING) {
            return 'warning-row';
        }
        return '';
    },

    subscriptionRowClass(subscription) {
        if (subscription.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
            (subscription.dataSize && subscription.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)) {
            return 'critical-subscription';
        } else if (subscription.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_WARNING) {
            return 'slow-method';
        }
        return '';
    },

    routeRowClass(route) {
        return route.avgLoadTime >= THRESHOLDS.ROUTE_TIME_WARNING ? 'warning-row' : '';
    },

    isCriticalMethod(method) {
        return method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
            (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL);
    },

    isSlowMethod(method) {
        return method.avgTime >= THRESHOLDS.METHOD_TIME_WARNING &&
            method.avgTime < THRESHOLDS.METHOD_TIME_CRITICAL;
    },

    isCriticalSubscription(subscription) {
        return subscription.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
            (subscription.dataSize && subscription.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL);
    },

    isSlowSubscription(subscription) {
        return subscription.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_WARNING &&
            subscription.readyTime < THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL;
    },

    isSlowRoute(route) {
        return route.avgLoadTime >= THRESHOLDS.ROUTE_TIME_WARNING;
    },

    isAboveThreshold(value, threshold, invert) {
        if (invert) {
            return value <= threshold;
        }
        return value >= threshold;
    },

    isHighGrowth(growthRate) {
        return growthRate > 5; // 5% per hour is considered high growth
    },

    formatDuration(seconds) {
        if (!seconds) return "N/A";

        const days = Math.floor(seconds / (24 * 60 * 60));
        const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
        const minutes = Math.floor((seconds % (60 * 60)) / 60);

        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    },

    formatSize(bytes) {
        if (!bytes) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDate(timestamp) {
        if (!timestamp) return 'N/A';
        return new Date(timestamp).toLocaleString();
    },

    heavyQueries() {
        return Template.instance().heavyQueries.get() || [];
    },

    optimizationSuggestions() {
        return Template.instance().optimizationSuggestions.get() || [];
    },

    queryRowClass(query) {
        if (query.avgTime >= THRESHOLDS.QUERY_TIME_CRITICAL ||
            query.memoryImpact >= THRESHOLDS.QUERY_MEMORY_CRITICAL ||
            query.documentsScanned >= THRESHOLDS.QUERY_DOCS_CRITICAL) {
            return 'critical-row';
        } else if (query.avgTime >= THRESHOLDS.QUERY_TIME_WARNING) {
            return 'warning-row';
        }
        return '';
    },

    isHeavyQuery(query) {
        return query.avgTime >= THRESHOLDS.QUERY_TIME_CRITICAL ||
            query.memoryImpact >= THRESHOLDS.QUERY_MEMORY_CRITICAL ||
            query.documentsScanned >= THRESHOLDS.QUERY_DOCS_CRITICAL;
    },

    isModerateQuery(query) {
        return query.avgTime >= THRESHOLDS.QUERY_TIME_WARNING &&
            query.avgTime < THRESHOLDS.QUERY_TIME_CRITICAL;
    },

    // Update the formatSessionCount helper
    formatSessionCount(count) {
        if (count === 0) {
            return '0 (No active connections)';
        }
        const metrics = Template.instance().metrics.get();
        if (metrics && metrics.connectionMetrics) {
            const lastUpdate = new Date(metrics.connectionMetrics.lastUpdate);
            const timeAgo = Math.floor((Date.now() - lastUpdate) / 1000);
            return `${count} (Updated ${timeAgo}s ago)`;
        }
        return count.toString();
    },

    formatDataSize(bytes) {
        if (!bytes) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDocumentCount(count) {
        if (!count) return '0';
        return count.toLocaleString();
    },

    formatMemoryUsage(bytes) {
        if (!bytes) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});

Template.performanceMonitoring.events({
    'click .tab-btn'(event, instance) {
        const tab = event.currentTarget.dataset.tab;
        instance.activeTab.set(tab);
    },
    'click #showActiveUsers'(event, instance) {
        event.preventDefault();
        instance.showingActiveUsers.set(true);
        instance.isLoading.set(true);

        Meteor.call('getActiveUsers', (error, result) => {
            instance.isLoading.set(false);
            if (error) {
                console.error('[Performance Monitor] Error fetching active users:', error);
                mpSwal.fire("Error", "Failed to fetch active users. Please try again.", "error");
            } else {
                instance.activeUsers.set(result);
                const metrics = instance.metrics.get();
                metrics.activeSessionsCount = result.length;
                instance.metrics.set(metrics);
            }
        });
    },
    'click #closeActiveUsers'(event, instance) {
        event.preventDefault();
        instance.showingActiveUsers.set(false);
    },
    'input #methodSearch'(event, instance) {
        const searchTerm = event.target.value.toLowerCase();
        instance.methodSearchTerm.set(searchTerm);

        Meteor.call('getMethodMetrics', (error, result) => {
            if (error) return;

            let filtered = result;

            // Apply search filter
            if (searchTerm) {
                filtered = filtered.filter(method =>
                    method.name.toLowerCase().includes(searchTerm)
                );
            }

            // Apply critical-only filter if active
            if (instance.showCriticalMethodsOnly.get()) {
                filtered = filtered.filter(method =>
                    method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
                    (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL)
                );
            }

            instance.methods.set(filtered);
        });
    },

    'input #subscriptionSearch'(event, instance) {
        const searchTerm = event.target.value.toLowerCase();
        instance.subscriptionSearchTerm.set(searchTerm);

        Meteor.call('getSubscriptionMetrics', (error, result) => {
            if (error) return;

            let filtered = result;

            // Apply search filter
            if (searchTerm) {
                filtered = filtered.filter(sub =>
                    sub.name.toLowerCase().includes(searchTerm)
                );
            }

            // Apply critical-only filter if active
            if (instance.showCriticalSubsOnly.get()) {
                filtered = filtered.filter(sub =>
                    sub.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
                    (sub.dataSize && sub.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)
                );
            }

            instance.subscriptions.set(filtered);
        });
    },

    'change #showCriticalOnly'(event, instance) {
        instance.showCriticalMethodsOnly.set(event.target.checked);

        // Refetch and filter methods
        Meteor.call('getMethodMetrics', (error, result) => {
            if (error) return;

            let filtered = result;

            // Apply critical-only filter if checked
            if (event.target.checked) {
                filtered = filtered.filter(method =>
                    method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL ||
                    (method.memoryUsage && method.memoryUsage >= THRESHOLDS.METHOD_MEMORY_CRITICAL)
                );
            }

            // Also apply search filter if one exists
            const searchTerm = instance.methodSearchTerm.get();
            if (searchTerm) {
                filtered = filtered.filter(method =>
                    method.name.toLowerCase().includes(searchTerm)
                );
            }

            instance.methods.set(filtered);
        });
    },

    'change #showCriticalSubsOnly'(event, instance) {
        instance.showCriticalSubsOnly.set(event.target.checked);

        // Refetch and filter subscriptions
        Meteor.call('getSubscriptionMetrics', (error, result) => {
            if (error) return;

            let filtered = result;

            // Apply critical-only filter if checked
            if (event.target.checked) {
                filtered = filtered.filter(sub =>
                    sub.readyTime >= THRESHOLDS.SUBSCRIPTION_TIME_CRITICAL ||
                    (sub.dataSize && sub.dataSize >= THRESHOLDS.SUBSCRIPTION_SIZE_CRITICAL)
                );
            }

            // Also apply search filter if one exists
            const searchTerm = instance.subscriptionSearchTerm.get();
            if (searchTerm) {
                filtered = filtered.filter(sub =>
                    sub.name.toLowerCase().includes(searchTerm)
                );
            }

            instance.subscriptions.set(filtered);
        });
    },

    'click #refreshCritical'(event, instance) {
        instance.isLoading.set(true);

        Meteor.call('getCriticalMetrics', (error, result) => {
            if (!error) {
                instance.criticalMetrics.set(result);

                // Also refresh critical methods and subscriptions
                Meteor.call('getCriticalMethods', (error, methods) => {
                    if (!error) {
                        instance.methods.set(methods);
                    }
                });

                Meteor.call('getCriticalSubscriptions', (error, subs) => {
                    if (!error) {
                        instance.subscriptions.set(subs);
                    }
                });
            } else {
                console.error('[Performance Monitor] Error refreshing critical metrics:', error);
            }
            instance.isLoading.set(false);
        });
    },

    'input #querySearch'(event, instance) {
        const searchTerm = event.target.value.toLowerCase();
        instance.querySearchTerm.set(searchTerm);

        Meteor.call('getHeavyQueries', (error, queries) => {
            if (error) return;

            let filtered = queries;

            // Apply search filter
            if (searchTerm) {
                filtered = filtered.filter(query =>
                    query.name.toLowerCase().includes(searchTerm) ||
                    query.collection.toLowerCase().includes(searchTerm)
                );
            }

            instance.heavyQueries.set(filtered);
        });
    }
});