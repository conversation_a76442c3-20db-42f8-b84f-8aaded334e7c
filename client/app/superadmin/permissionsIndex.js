import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import './permissionsIndex.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.permissionsIndex.onCreated( function() {
	var self = this;
	self.roles = new ReactiveVar();
	Meteor.callAsync("getRoles", {}).then((result) => {
		self.roles.set(result);
	});
});

Template.permissionsIndex.helpers({
	"roles": function () {
		return Template.instance().roles.get();
	}
});

Template.permissionsIndex.events({
	"click #btnAddRole"() {
		mpSwal.fire({
			title: "New Role Identifier",
			text: "Please enter the identifier you would like to use for your new role (please only letters and numbers)", 
			input: "text",
			showCancelButton: true,
		}).then(result => {
			if (result.value) {
				
   				if(!result.value.match(/^[0-9a-zA-Z]+$/))
					return mpSwal.fire("Error", "Please use only numbers and letters for role identifiers.");
				Meteor.callAsync("insertRole", { name: result.value }).then((insertResult) => {
					console.log("result", insertResult);
					FlowRouter.go("/superadmin/roles/" + insertResult);
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});		
	}
});