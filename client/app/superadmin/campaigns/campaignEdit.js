import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import _ from '../../../../lib/util/underscore';
import { Orgs } from '../../../../lib/collections/orgs';
import './campaignEdit.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.campaignEdit.onCreated(function() {
	var self=this;
	self.selectedOrgs = new ReactiveVar([]);
	self.searchText = new ReactiveVar("");
	self.campaignData = new ReactiveVar();
	const campaignId = FlowRouter.getParam("id");
	if (campaignId && campaignId != "new") {
		Meteor.callAsync("getCampaign", { campaignId }).then((result) => {
			if (result && !result.campaignType) {
				result.campaignType = "simple-survey";
			}
			self.campaignData.set(result);
			self.selectedOrgs.set(result.orgIds);
		});
	} else {
		self.campaignData.set({
			campaignType: "simple-survey"
		})
	}
});

Template.campaignEdit.helpers({
	availableOrgs() {
		const query = {_id:{"$nin": Template.instance().selectedOrgs.get()}},
			st = Template.instance().searchText.get();
		if (st.trim() != "")
			query["$or"] = [
				{name: {"$regex": st, "$options": "i"}},
				{longName: {"$regex": st, "$options": "i"}}
			];
		return Orgs.find(query ,{fields:{name:1}, sort:{name:1}});
	},
	selectedOrgs() {
		return getSelectedOrgs(Template.instance());
	},
	selectedOrgsCount() {
		const selectedOrgs = getSelectedOrgs(Template.instance());
		return selectedOrgs.count();
	},
	campaignData() {
		return Template.instance().campaignData.get();
	},
	selectedTypesContain(passedType) {
		const selectedTypes = Template.instance().campaignData.get();
		return selectedTypes && _.contains(selectedTypes.targetTypes, passedType);
	}
});

function getSelectedOrgs(instance) {
	const query = {_id:{"$in": instance.selectedOrgs.get()}};
	return Orgs.find(query,{fields:{name:1}, sort:{name:1}});
}

Template.campaignEdit.events({
	"click #btnAddOrg"(e, i) {
		e.preventDefault();
		const orgIds = $(".chkAddOrg:checked").map(function(){
			return $(this).data("id");
		  }).get();
		const currentSelections = i.selectedOrgs.get();
		i.selectedOrgs.set(currentSelections.concat(orgIds));
		$("#chkSelectAll").prop("checked", false);
		$(".chkAddOrg").prop("checked", false);
	},
	"click .btnRemoveOrg"(e, i) {
		const selectedId = $(e.currentTarget).data("id");
		const currentSelections = i.selectedOrgs.get();
		i.selectedOrgs.set(currentSelections.filter( s => s != selectedId));
	},
	"keyup #txtSearch"(e, i) {
		const st = $("#txtSearch").val();
		i.searchText.set(st);
		$("#chkSelectAll").prop("checked", false);
		$(".chkAddOrg").prop("checked", false);
	},
	"click #chkSelectAll"(e, i) {
		const status = $("#chkSelectAll").prop("checked");
		$(".chkAddOrg").prop("checked", status);
	},
	"click #btnSave"(e, i) { 
		e.preventDefault();
		const campaignId = FlowRouter.getParam("id");
		const options = {
			title: $("input[name='campaign-title']").val(),
			announcement: $("input[name='announcement']").val(),
			resourceTitle: $("input[name='resource-title']").val(),
			resourceDescription: $("input[name='resource-description']").val(),
			resourceLink: $("input[name='resource-link']").val(),
			campaignType: $("input[name=campaign-type]:checked").val(),
			orgIds: i.selectedOrgs.get(),
			targetTypes: $("input[name='target-types']:checked").map(function() { return $(this).val();}).get(),
			active: $("input[name='campaign-active']").prop("checked")
		}
		console.log("options to call", options)
		if (campaignId && campaignId != "new") options["campaignId"] = campaignId;
		Meteor.callAsync("upsertCampaign", options).then((result) => {
			return mpSwal.fire("Success", "Campaign saved.", "success").then(() => {
				FlowRouter.go("/superadmin/campaigns");
			});
		}).catch((error) => {
			return mpSwal.fire("Error", error.reason, "error");
		});
	}
});