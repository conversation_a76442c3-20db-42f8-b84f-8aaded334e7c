import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './campaignResults.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.campaignResults.onCreated( function() {
	var self = this;
	self.campaignResultsData = new ReactiveVar();
	Meteor.callAsync("getCampaignResults", { campaignId: FlowRouter.getParam('id') }).then((result) => {
		self.campaignResultsData.set(result);
		console.log("result", result);
	});
});

Template.campaignResults.helpers(  {
	campaignResultsData() {
		return Template.instance().campaignResultsData.get();
	}
});

Template.campaignResults.events( {
	async "click #btnExport"() {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvData'), outputFile]);
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "billingAging"
		});
	}
});