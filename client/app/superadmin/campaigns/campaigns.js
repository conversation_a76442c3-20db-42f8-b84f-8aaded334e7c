import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './campaigns.html';
import './campaignEdit.js';
import './campaignResults.js';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.campaigns.helpers({
	activeCampaignsTemplate() {
		const id = FlowRouter.getParam('id'), action = FlowRouter.getParam('action');
		if (id == "new")
			return "campaignEdit";
		else if (id && action == "results")
			return "campaignResults";
		else if (id)
			return "campaignEdit";
		else 
			return "campaignList";
	}
});

Template.campaignList.onCreated( function() {
	var self = this;
	self.campaignsData = new ReactiveVar();
	Meteor.callAsync("getCampaigns", {}).then((result) => {
		console.log("Result", result);
		self.campaignsData.set(result);
	});
});

Template.campaignList.helpers({
	campaigns() {
		console.log("blah", Template.instance().campaignsData.get())
		return Template.instance().campaignsData.get();
	}
});