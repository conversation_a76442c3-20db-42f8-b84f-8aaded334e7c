import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './resetPins.html';
import $ from 'jquery';

Template.resetPins.created = function() {
	var self = this;
	self.orgList = new ReactiveVar();
	self.showAll = new ReactiveVar(false);
	Tracker.autorun( function() {
		self.orgList.set(null);
		Meteor.callAsync("superAdminList", {
			showAll: self.showAll.get(),
			includeParentOrg: false
		}).then((orgList) => {
			self.orgList.set(orgList);
		});
	});
};

Template.resetPins.rendered = function() {
	$("#orgsList").select2({
		selectionCssClass:"form-control form-control-lg form-control-solid"
	});
};
Template.resetPins.helpers({
	"orgs": function() {
		var orgs = Template.instance().orgList.get();
		
		return _.map(orgs, function(o){
			return { name: o.name + " (" + (o.userCount || 0) + ")", _id: o._id};
		});
	}
});

Template.resetPins.events({
	'submit form': function(event) {
		event.preventDefault();
		$("#resetButton").html("Working...");
		$("#resetButton").prop('disabled', true);
		
		Meteor.callAsync('resetPins', $("#orgsList").val()).then(() => {
			mpSwal.fire({ title: "PINs Updated", text: "PINs have been reset for all users in this organization." });
			$("#resetButton").html("Reset");
			$("#resetButton").prop('disabled', false);
		});
	}

});