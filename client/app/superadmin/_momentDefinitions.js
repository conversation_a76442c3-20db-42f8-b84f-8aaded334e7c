import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './momentDefinitions.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { MomentDefinitions } from '../../../lib/collections/momentDefinitions';

Template.momentDefinitions.events({
	"click #newMomentDefinition": function() {

		var value = prompt("Enter a name for this new moment type:");
		if (value) {
			Meteor.callAsync("insertMomentDefinition", value).then((curId) => {
				FlowRouter.go("/superadmin/moment-definitions/" + curId);
			});
		};
	}
});
Template.momentDefinitions.helpers({
	"momentDefinitions": function() {
		return MomentDefinitions.find({},{sort: {momentTypePretty: 1}});
	}
});
