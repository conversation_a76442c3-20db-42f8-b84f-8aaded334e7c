import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_newReleaseUpdateModal.html';
import { hideModal } from '../main';

Template._newReleaseUpdateModal.events({
  'submit form': function(e, i) {
    e.preventDefault();
    $("#newReleaseSave").prop('disabled', true);
    
    const options = {
      mobileRelease: $("#mobileRelease").prop("checked"),
      webRelease: $("#webRelease").prop("checked"),
      mobileLink: $("#mobileLink").val(),
      webLink: $("#webLink").val() 
    };
    
    Meteor.callAsync("createNewRelease", options).then((result) => {
      $("#newReleaseSave").prop('disabled', false);
      hideModal("#_newReleaseUpdateModal");
    }).catch((error) => {
      $("#newReleaseSave").prop('disabled', false);
      mpSwal.fire("Error", error.reason, "error");
    });
  }
})
