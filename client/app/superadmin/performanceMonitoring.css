.performance-monitoring-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 15px;
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 5px;
}

.tab-btn.active {
    font-weight: bold;
    border-bottom: 2px solid #2196F3;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-gap: 15px;
    margin-bottom: 20px;
}

.metric-box {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

.metric-box.critical {
    background-color: rgba(244, 67, 54, 0.1);
    border-left: 3px solid #F44336;
}

.metric-box.warning {
    background-color: rgba(255, 152, 0, 0.1);
    border-left: 3px solid #FF9800;
}

.metric-title {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
}

.metric-trend {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.metrics-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    margin-bottom: 30px;
}

.metrics-table th,
.metrics-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.metrics-table th {
    background: #f5f5f5;
}

/* Row highlighting */
.slow-method,
.warning-row {
    background-color: rgba(255, 152, 0, 0.1);
}

.critical-method,
.critical-subscription,
.critical-row {
    background-color: rgba(244, 67, 54, 0.1);
}

.critical-method td,
.critical-subscription td {
    font-weight: bold;
}

.search-input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Filter and search layout */
.methods-header,
.subs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.methods-header .search-input,
.subs-header .search-input {
    width: 70%;
    margin-bottom: 0;
}

.methods-filter,
.subs-filter {
    display: flex;
    align-items: center;
}

.critical-checkbox {
    margin-right: 5px;
}

/* Status indicators */
.status-normal,
.status-warning,
.status-critical {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    color: white;
}

.status-normal {
    background-color: #4CAF50;
}

.status-warning {
    background-color: #FF9800;
}

.status-critical {
    background-color: #F44336;
}

/* Critical tab specific styles */
.critical-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.refresh-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.refresh-btn:hover {
    background-color: #1976D2;
}

.severity-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    color: white;
    text-transform: uppercase;
}

.severity-badge.critical {
    background-color: #F44336;
}

.severity-badge.high {
    background-color: #FF5722;
}

.severity-badge.medium {
    background-color: #FF9800;
}

.severity-badge.low {
    background-color: #4CAF50;
}

.critical-issue {
    background-color: rgba(244, 67, 54, 0.1);
}

.high-issue {
    background-color: rgba(255, 87, 34, 0.1);
}

.medium-issue {
    background-color: rgba(255, 152, 0, 0.05);
}

.critical-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.critical-count {
    background-color: rgba(244, 67, 54, 0.1);
    border-left: 3px solid #F44336;
    padding: 10px 15px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.count-number {
    font-size: 24px;
    font-weight: bold;
    color: #F44336;
}

.count-label {
    font-size: 14px;
    color: #666;
}

.no-issues {
    color: #4CAF50;
    font-weight: bold;
}