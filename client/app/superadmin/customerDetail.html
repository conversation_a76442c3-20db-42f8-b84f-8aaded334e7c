<template name="customerDetail">

{{#with org}}
<div class="d-flex flex-row justify-content-between mx-8 mb-8">
  <a href="/superadmin/customers/" class="btn btn-default">&lt; Back to customer list</a>
  <h3>{{name}}</h3>
  <div class="d-flex flex-row">
    <a href="" class="btn btn-primary pull-right mr-4 font-weight-bolder" id="btnLoginAs">Login As...</a>
    <a href="" class="btn btn-primary pull-right mr-4 font-weight-bolder" id="btnSetCurriculumBank">Set Curriculum Bank</a>
	  {{#if hasCrm }}
		  <a href="" class="btn btn-primary pull-right mr-4 font-weight-bolder {{cantSync}}" id="btnInitiateCrmSync">Send Families to Enroll</a>
	  {{/if}}
	<a href="/superadmin/customers/{{_id}}/import" class="btn btn-primary pull-right mr-4 font-weight-bolder" id="btnImport">Import</a>
    <a href="" class="btn btn-danger pull-right font-weight-bolder" id="btnPropagateSettings">Propagate Settings</a>
  </div>
</div>


<div class="card card-custom card-collapsed mx-8 mb-8" data-card="true" id="boxCustomizations">
	<div class="card-header">
    <div class="card-title">
     <h3 class="card-label">Current Customizations</h3>
    </div>
		<div class="card-toolbar">
      <a data-cy="expand-customizations" href="" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="Toggle Card">
        <i class="fad fa-arrow-down fad-primary"></i>
      </a>
    </div>
	</div>
	<div class="card-body no-padding">
		<button data-cy="add-customization" type="button" class="btn btn-primary mb-4 font-weight-bolder" id="addCustomizationLink" style="margin-left:5px">Add Customization</button>
		<table class="table">
			<tbody>
				<tr>
					<th>Customization</th>
					<th>Status</th>
					<th>Action</th>
				</tr>
				
				<tr id="rowAddCustomization" style="display:none">
					
					<td><input data-cy="customization-input" type="text" class="form-control" name="customization-name"></td>
					<td><select name="customization-value" class="form-control">
						<option value="true">True</option>
						<option value="false">False</option>
						</select>
					</td>
					
					<td>
						<button data-cy="save-customization" id="btnSaveNewCustomization" class="btn btn-sm btn-primary">Save</button>
						<button id="btnCancelNewCustomization" class="btn btn-sm btn-default">Cancel</button>
					</td>
				</tr>
				
				{{#each customization in customizationList}}
				<tr>
					<td>{{customization.customizationName}}</td>
					<td>{{customization.customizationValue}}</td>
					<td><button data-cy="remove-customization" class="btn btn-sm btn-default btnRemoveCustomization" data-key="{{customization.customizationName}}">Remove</button></td>
				</tr>
				{{/each}}
			</tbody>
		</table>
	</div>
</div>

<div class="card card-custom mx-8 mb-8" data-card="true" id="customerEnabledCustomMoments">
  <div class="card-header">
    <div class="card-title">
     <h3 class="card-label">Enabled Custom Moments</h3>
    </div>
    <div class="card-toolbar">
      <a type="button" class="btn btn-primary mr-4 font-weight-bolder" id="addMomentTypeLink">Add</a>
      <a href="" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="Toggle Card">
        <i class="fad fa-arrow-down fad-primary"></i>
      </a>
    </div>
  </div>
	<div class="card-body no-padding">
		<table class="table">
			<tbody>
				<tr>
					<th>Moment Type</th>
					<th>Action</th>
				</tr>
				{{#each enabledMomentTypes}}
				<tr>
					<td>{{this}}</td>
					<td><a href="" data-momenttype="{{this}}" class="deleteMomentTypeLink">remove</a></td>
				</tr>
				{{/each}}
			</tbody>
		</table>
	</div>
</div>

<div class="card card-custom mx-8 mb-8" data-card="true" id="customerOtherInformation">
  <div class="card-header">
    <div class="card-title">
     <h3 class="card-label">Other Information</h3>
    </div>
    <div class="card-toolbar">
      <a href="" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="Toggle Card">
        <i class="fad fa-arrow-down fad-primary"></i>
      </a>
    </div>
  </div>
	<div class="card-body no-padding">
		<table class="table">
			<tbody>
				<tr>
					<td><b>Status</b></td>
					<td>{{#unless org.inactive}}Active{{else}}Inactive{{/unless}}</td>
					<td>
						{{#if org.inactive}}
							<a href="" id="btnSetActive">Reactivate</a>
						{{else}}
							<a href="" id="btnSetInactive">Deactivate</a>
						{{/if}}
					</td>
				</tr>
				<tr>

					<td><b>Long Name</b></td>
					<td>{{getLongName}}</td>
					<td><a href="" id="btnUpdateLongName">Manage</a></td>
				</tr>
				<tr>
					<td><b>Parent Org</b></td>
					<td>{{parentOrgName}}</td>
					<td><a href="" id="btn-manage-parent-org">Manage</a></td>
				</tr>
				<tr>
					<td><b>Enable as Master Org</b></td>
					<td>{{#if org.enableSwitchOrg}}Yes{{else}}No{{/if}}</td>
					<td><a href="" id="btn-enable-master-org" >Manage</a></td>
				</tr>
                {{#if org.enableSwitchOrg}}
                <tr>
					<td><b>Enable Brands</b></td>
					<td>{{hasBrands}}</td>
					<td><a href="" id="btn-enable-brands">Manage</a></td>
				</tr>
                {{/if}}
				<tr style='display:none' id="set-parent-org-row">
					<td><b>Set Parent Org</b></td>
					<td>
						<select id="orgsList" style="width:100%">
							<option value=""></option>
						{{#each availableMasterOrgs}}
							<option value='{{_id}}' selected={{trueIfEq org.parentOrgId _id}}>{{name}}</option>
						{{/each}}
						</select>
					</td>
					<td>
						<button class="btn btn-primary" id="btn-parent-org-save">Save</button>
						<button class="btn btn-default" id="btn-parent-org-cancel">Cancel</button>
					</td>
				</tr>
				<tr>
					<td><b>Custom Person Profile Fields</b></td>
					<td>{{#if hasDefined "valueOverrides.profileFields"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile">Manage</a></td>
				</tr>
				<tr>
					<td><b>Custom Inquiry Profile Fields</b></td>
					<td>{{#if hasDefined "valueOverrides.inquiryProfileFields"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile?type=prospect">Manage</a></td>
				</tr>
				<tr>
					<td><b>Custom Family Profile Fields</b></td>
					<td>{{#if hasDefined "valueOverrides.familyProfileFields"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile?type=family">Manage</a></td>
				</tr>
				<tr>
					<td><b>Custom Family/Person Deactivation Reasons</b></td>
					<td>{{#if hasDefined "valueOverrides.deactivationReasons"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile?type=deactivationReasons">Manage</a></td>
				</tr>
				<tr>
					<td><b>Custom Staff Profile Fields</b></td>
					<td>{{#if hasDefined "valueOverrides.staffProfileFields"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile?type=staff">Manage</a></td>
				</tr>
				<tr>
					<td><b>Custom Staff Deactivation Reasons</b></td>
					<td>{{#if hasDefined "valueOverrides.deactivationReasonsStaff"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile?type=deactivationReasonsStaff">Manage</a></td>
				</tr>
				<tr>
					<td><b>Enabled Curriculum Standards</b></td>
					<td>{{enabledCurriculumStandards}}</td>
					<td><a href="#" id="btnManageCurriculumStandards">Manage</a></td>
				</tr>
				<tr>
					<td><b>Enabled Permission Contexts</b></td>
					<td>{{#if hasDefined "valueOverrides.availablePermissionsContexts"}}
							<pre>{{enabledContexts}}</pre>
						{{else}}No{{/if}}</td>
                    <td><a href="#" id="btn-manage-contexts">Manage</a></td>
                </tr>
                <tr id="set-contexts-row" style="display:none">
                    <td><b>Set Permission Contexts</b></td>
                    <td><input type="checkbox" name="set-contexts" value="none" checked={{hasContext "none"}}> None (overrides all)<br/>
						{{#each context in availableContexts}}
						<input type="checkbox" name="set-contexts" value="{{context}}" checked={{hasContext context}}> {{context}}<br/>
						{{/each}}
					</td>
					<td>
						<button class="btn btn-primary" id="btn-contexts-save">Save</button>
						<button class="btn btn-default" id="btn-contexts-cancel">Cancel</button>
					</td>
				</tr>
				<tr>
					<td><b>Enabled Permission Roles</b></td>
					<td>{{#if hasDefined "valueOverrides.availablePermissionsRoles"}}
						<pre>{{enabledRoles}}</pre>
						{{else}}No{{/if}}</td>
					<td><a href="" id="btn-manage-roles">Manage</a></td>
				</tr>
				<tr id="set-roles-row" style="display:none">
					<td><b>Set Permission Roles</b></td>
					<td><input type="checkbox" name="set-roles" value="none" checked={{hasRole "none"}}> None (overrides all)<br/>
						{{#each role in availableRoles}}
						<input type="checkbox" name="set-roles" value="{{role.id}}" checked={{hasRole role.id}}> {{role.label}} ({{role.id}})<br/>
						{{/each}}
					</td>
					<td>
						<button class="btn btn-primary" id="btn-roles-save">Save</button>
						<button class="btn btn-default" id="btn-roles-cancel">Cancel</button>
					</td>
				</tr>
				<tr>
					<td><b>Pin Code Checkin Fields</b></td>
					<td>{{#if hasDefined "valueOverrides.pinCodeCheckinFields"}}Yes{{else}}No{{/if}}</td>
					<td><a href="/superadmin/customers/{{_id}}/profile?type=pincodefields">Manage</a></td>
				</tr>
				<tr>
					<td><b>Active Translation</b></td>
					<td>{{language}}</td>
					<td><a href="" id="btnChangeTranslation">Change</a></td>
				</tr>
				<tr>
					<td><b>Plan</b></td>
					<td><pre>{{jsonPrint planDetails}}</pre></td>
					<td><a href="" id="btnSetActivePlan">Set Active</a></td>
				</tr>
				<tr>
					<td><b>Stripe Customer ID</b></td>
					<td>{{org.stripeCustomerId}}</td>
					<td><a href="" id="btnSetStripeID">Set</a></td>
				</tr>
				<tr>
					<td><b>Billing / Payments Status</b></td>
					<td>{{org.billingStatus.message}}</td>
					<td>{{#if showModifyBillingStatus}} <a href="" id="btnSetBillingInvoiceOnly">Enable Invoice Only Billing</a>{{/if}}</td>
				</tr>
				<tr>
					<td><b>From Address</b></td>
					<td>{{org.fromAddress}}</td>
					<td><a href="" id="btnSetFromAddress">Set</a></td>
				</tr>
				<tr>
					<td><b>Chargeback/Account Invoice Email</b></td>
					<td>{{org.valueOverrides.chargebackInvoiceEmail}}</td>
					<td><a href="" id="btnSetChargebackInvoiceEmail">Set</a></td>
				</tr>
				<tr>
					<td><b>Quickbooks Audit Logs</b></td>
					<td></td>
					<td><a href="/superadmin/customers/{{_id}}/reports/qb-audit-log">View</a></td>
				</tr>
				<tr>
					<td><b>Prior Legal Entity / EIN Info</b></td>
					<td>
						{{#each entity in org.billing.legalEntityHistory}}
							{{entity.business_name}} 
							{{#if entity.startDate}} - Start: {{entity.startDate}}{{/if}} 
							{{#if entity.endDate}} - End: {{entity.endDate}}{{/if}} 
							- <a href="" class="btnRemoveHistoricalLegalEntity" data-idx="{{@index}}">Remove</a><br/>
						{{/each}}
					</td>
					<td><a href="" id="btnAddLegalEntity">Add</a></td>
				</tr>
				<tr>
					<td><b>Override Convenience Fee Language</b></td>
					<td>{{org.valueOverrides.alternateServiceChargeFeeDescription}}</td>
					<td><a href="" id="btnSetConvenienceFeeLanguage">Set</a></td>
				</tr>
				<tr>
					<td><b>Sage Location Code</b></td>
					<td>{{org.billing.sage.locationCode}}</td>
					<td><a href="" id="btnSetSageLocationCode">Set</a></td>
				</tr>
			</tbody>
		</table>
	</div>
</div>

<div class="card card-custom card-collapsed mx-8" data-card="true" id="customerWMG">
	<div class="card-header">
    <div class="card-title">
     <h3 class="card-label">WMG Integration Options</h3>
    </div>
		<div class="card-toolbar">
      <a href="" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="Toggle Card">
        <i class="fad fa-arrow-down fad-primary"></i>
      </a>
    </div>
	</div>
	<div class="card-body no-padding">
		<div class="d-flex flex-column my-8">
			<span>Steps to configure:</span>
			<span>1) Set the WMG Center ID (get from WMG team)</span>
			<span>1.a) run the engine.create API (experimental function - consult engineering)</span>
			<span>2) Run the Sync Center State (async)</span>
			<span>3) Configure Cameras (wait ~10 minutes after step 2)</span>
		</div>
		<div class="d-flex flex-row my-8">
			<div class="mr-4 font-weight-bolder">
				{{wmgCenterId}}
			</div>
			<div class="btn btn-primary font-weight-bolder" id="btnSetWmgCenterId">
				Set WMG Center ID
			</div>
		</div>
		<div class="d-flex flex-row my-8">
			<div class="btn btn-danger font-weight-bolder mr-4" id="btnSetWmgEngine">
				Create WMG Engine
			</div>
			<div class="btn btn-success font-weight-bolder mr-4" id="btnSetWmgState">
				Sync Center State to WMG
			</div>
			<div class="btn btn-success font-weight-bolder mr-4" id="btnSetWmgCameras">
				Configure Cameras
			</div>
			<div class="btn btn-success font-weight-bolder mr-4" id="btnDisplayGroupMappings">
				Show Groups for Camera Config
			</div>
		</div>
	</div>
</div>
{{/with}}
</template>

<template name="superadminAddLegalEntity">
	<form id="frmAddLegalEntity">	
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				Start Date:<br/>
			</label>
			<div class="col-10">
				<input type="date" class="form-control" name="start-date" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				End Date:<br/>
			</label>
			<div class="col-10">
				<input type="date" class="form-control" name="end-date" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				Business Name:<br/>
			</label>
			<div class="col-10">
				<input type="text" class="form-control" name="business-name" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				Tax ID/EIN:<br/>
			</label>
			<div class="col-10">
				<input type="text" class="form-control" name="business-tax-id" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				Address:<br/>
			</label>
			<div class="col-10">
				<input type="text" class="form-control" name="address" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				City:<br/>
			</label>
			<div class="col-10">
				<input type="text" class="form-control" name="city" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				State:<br/>
			</label>
			<div class="col-10">
				<input type="text" class="form-control" name="state" value=""><br/>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-2 col-form-label text-right">
				Zipcode:<br/>
			</label>
			<div class="col-10">
				<input type="text" class="form-control" name="zipcode" value=""><br/>
			</div>
		</div>
		
	</form>
</template>

<template name="superadminAddBrands">
        <div class="row col-12 mb-4">
            <div id="btnAddBrand" class="btn btn-primary font-weight-bolder ml-4"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add Brand</div>
        </div>
        <div class="row">
            <form id="frmAddBrand" style="display:none" class="row col-12 mb-6">
                <input type="hidden" name="brand-id">
                <div class="row col-12 mb-4">
                    <div class='col-8'>
                        <label>Name of Brand:</label>
                        <input type="text" class="form-control" id="AddBrand-Name" maxlength="36" minlength="3">
                    </div>
                </div>
                <div class="row col-4 pt-8">
                    <div class="btn btn-primary font-weight-bolder ml-4 mr-2 disabled" id="btnSaveAddBrand">Save</div>
                    <div class="btn btn-secondary font-weight-bolder" id="btnCancelAddBrand">Cancel</div>
                </div>
            </form>
            <table class="table">
                <tr style="border-bottom:1px solid #000">
                    <th>Brand Name</th>
                    <th></th>
                </tr>
                {{#each getBrands}}
                    <tr>
                        <td>
                            {{name}}
                        </td>
                        {{#if trueIfEq @index 0}}
                        <td></td>
                        {{else}}
                            <td>
                                <span id="btnEditBrand" class="text-primary" style="cursor:pointer;" data-id="{{_id}}">Edit</span> |
                                <span id="btnDeleteBrand" class="text-primary" style="cursor:pointer;" data-id="{{_id}}">Delete</span>
                            </td>
                        {{/if}}
                    </tr>
                {{/each}}
            </table>
            {{#if availableOrgs}}
            <div class="row col-4 pt-8">
                <div class="btn btn-primary font-weight-bolder ml-4" id="btnAssignSites">Assign Sites to Brands</div>
            </div>
            <form id="frmAssignSites" style="display:none" class="row col-12 mb-6">
                <table class="table">
                    <tr style="border-bottom:1px solid #000">
                        <th scope="col">Site</th>
                        {{#each getBrands}}
                        <th scope="col" class="text-center">{{name}}</th>
                        {{/each}}
                    </tr>
                    {{#each org in availableOrgs}}
                        <tr>
                            <td>{{org.name}}</td>
                                {{#each brand in getBrands}}
                                    <td class="text-center">
                                        <input type="radio" class="form-check-input" name="{{org._id}}" value="{{brand._id}}" checked="{{isChecked org brand._id}}">
                                    </td>
                                {{/each}}
                        </tr>
                    {{/each}}
                </table>
                <div class="row col-4 pt-8">
                    <div class="btn btn-primary font-weight-bolder ml-4 mr-2" id="btnSaveAssignSites">Save</div>
                    <div class="btn btn-secondary font-weight-bolder" id="btnCancelAssignSites">Cancel</div>
                </div>
            </form>
            {{/if}}
        </div>
</template>