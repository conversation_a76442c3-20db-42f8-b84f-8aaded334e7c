import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './manualMigrations.html';

Template.manualMigrations.created = function() {
    var self = this;
    self.migrations = new ReactiveVar();
    Meteor.callAsync("getManualMigrations").then((migrations) => {
        self.migrations.set(migrations);
      })
    self.running = new ReactiveVar(false);
};
Template.manualMigrations.helpers({
    migrations: function() {
        return Template.instance().migrations.get();
    },
    disabled: function() {
        return Template.instance().running.get() ? 'disabled' : '';
    }
});

Template.manualMigrations.events({
    'click .run-migration-button': async function(event, i) {
        event.preventDefault();
        const version = Number(event.target.dataset['version']);
        if (i.migrations.get().find(m => m.version === version).lastRun) {
            const confirm = await mpSwal.fire({
                title:"Migration Already Run",
                text:"This migration has already been run. Are you sure you want to run it again?",
                showCancelButton: true
            });
            if (!confirm.isConfirmed) {
                return;
            }
        }
        i.running.set(true);
        Meteor.callAsync('runManualMigration', version).then(async (result) => {
            i.running.set(false);
            if (!result) {
                mpSwal.fire({ title: "Error", text: "An error occurred while running the migration." });
                return;
            }
            return await Meteor.callAsync("getManualMigrations");
        }).then((migrations) => {
            if (migrations) {
                i.migrations.set(migrations);
                mpSwal.fire({ title: "Migration Complete", text: "Migration has been run." });
            }
        }).catch((err) => {
            i.running.set(false);
            mpSwal.fire({ title: "Error", text: "An error occurred while running the migration." });
        });
    }
})