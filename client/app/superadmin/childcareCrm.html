<template name="childcareCrm">
	<div class="d-flex flex-row justify-content-between mb-4 mx-8">
		<h3>Childcare CRM Integrations</h3>
		<button class="btn btn-primary pull-right" id="btnAddCrmIntegration"><i class="fa fa-plus"></i> New
			Integration</button>
	</div>

	<div class="card card-custom mx-8">
		<div class="box-body table-responsive no-padding">
			<table class="table table-hover">
				<tbody>
					<tr>
						<th>Name</th>
						<th class="text-center">Sync Status</th>
						<th class="text-center">Last Sync Date</th>
						<th class="text-center">Total Orgs Enabled</th>
						<th>Action</th>
					</tr>

					{{#each crmAccounts}}
						<tr>
							<td><a href="/superadmin/childcareCrm/{{_id}}">{{name}}</a></td>
							<td class="text-center">{{syncStatus}}</td>
							<td class="text-center">{{formatDateUnix lastSyncUpdate "MM/DD/YYYY HH:mm"}}</td>
							<td class="text-center">{{totalOrgsEnabled centers}}</td>
							<td>
								<a href="/superadmin/childcareCrm/{{_id}}">Edit</a> |
								<a class="sync-crm-data" style="cursor: pointer;" data-id="{{_id}}">Sync</a>
							</td>
						</tr>
					{{/each}}
				</tbody>
			</table>
		</div>
	</div>
</template>

<template name="newCrmIntegrationModal">
	<form id="formCrmIntegration">
		<div class="row">
			<div class="col-md-12">
				<label>Parent Org Name</label>
				<input type="text" class="form-control" name="name"><br />

				<label>CRM Service Username</label>
				<input type="text" class="form-control" name="username"><br />

				<label>CRM Service Password</label>
				<input type="text" class="form-control" name="password"><br />
			</div>
		</div>
	</form>
</template>