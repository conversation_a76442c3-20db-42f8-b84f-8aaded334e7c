import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './testZkTecoRetries.html';

Template.testZkTecoRetries.events({
    'click .submit-button': async function(e, i) {
        e.preventDefault();
        const code = Number(document.getElementById('codeInput').value);
        const maxRetries = Number(document.getElementById('maxRetriesInput').value);
        Meteor.callAsync('zkTecoRetryErrors', code, maxRetries).then((result) => {
            alert('Done');
        });
    },
    'click .submit-batch-button': async function(e, i) {
        e.preventDefault();
        Meteor.callAsync('zkTecoRetryBatches').then(() => {
            alert('Batch retry done');
        })
    },
})