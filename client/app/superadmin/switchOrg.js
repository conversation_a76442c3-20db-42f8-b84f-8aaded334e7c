import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './switchOrg.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import $ from 'jquery';

Template.switchOrg.created = function() {
	var self = this;
	self.orgList = new ReactiveVar();
	self.showAll = new ReactiveVar(false);
	self.showOrgId = new ReactiveVar(false);
	Tracker.autorun( function() {
		self.orgList.set(null);
		Meteor.callAsync("superAdminList", {
			showAll: self.showAll.get(),
			showOrgId: self.showOrgId.get(),
			includeParentOrg: false
		}).then((orgList) => {
			self.orgList.set(orgList);
		});
	});
};

Template.switchOrg.rendered = function() {
	$("#orgsList").select2({
		//selectionCssClass:"form-control form-control-lg form-control-solid"
	});
};
Template.switchOrg.helpers({
	"orgs": function() {
		var orgs = Template.instance().orgList.get();
		
		return _.map(orgs, function(o){
			return { name: o.name + " (" + (o.userCount || 0) + ")", _id: o._id};
		});
	},
	doShowId: function() {
		return Template.instance().showOrgId.get();
	}
});

Template.switchOrg.events({
	'submit form': function(event) {
		event.preventDefault();
	
		Meteor.callAsync('switchOrg', $("#orgsList").val()).then(() => {
			location.replace("/loading?setLoc=home");
		}).catch((err) => {
			location.replace("/loading?setLoc=home");
		});
	},
	"click #showAll": function(event) {
		Template.instance().showAll.set($("#showAll").is(":checked"));
	},
	"click #showId": function(event) {
		Template.instance().showOrgId.set($("#showId").is(":checked"));
	}

});