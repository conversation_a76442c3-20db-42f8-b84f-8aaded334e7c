import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Orgs } from '../../../lib/collections/orgs';
import './fixWithdrawnDetails.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.fixWithdrawnDetails.created = function () {
    this.childList = new ReactiveVar([]);
    this.prevList = new ReactiveVar([]);
    this.loading = new ReactiveVar(true);
    this.batchRunning = new ReactiveVar(false);
    this.batchDisabled = new ReactiveVar(true);
    this.orgId = FlowRouter.current().params.id;
    this.org = Orgs.findOne(this.orgId);
    this.baseUrl = new ReactiveVar('');
    Meteor.callAsync('getCrmUiBaseUrl').then((result) => {
        this.baseUrl.set(result);
    });
    Meteor.callAsync('getPreviouslyFixedChildren', this.orgId).then((result) => {
        this.prevList.set(result);
    });
    Meteor.callAsync('getProblemChildren', this.orgId).then((result) => {
        console.log({ result });
        this.childList.set(result);
        this.loading.set(false);
    });
}

Template.fixWithdrawnDetails.helpers({
    "org": function () {
        return Template.instance().org;
    },
    'childList': function() {
        return Template.instance().childList.get();
    },
    'prevList': function() {
        return Template.instance().prevList.get();
    },
    'uiUrl': function(crmObj) {
        return Template.instance().baseUrl.get() + '/main/families/' + crmObj.familyId;
    },
    'crmStatus': function(statusId) {
        return Template.instance().org.crmStatuses?.find(stat => stat.id === statusId)?.name;
    },
    'loading': function() {
        return Template.instance().loading.get();
    },
    'batchDisabled': function() {
        return Template.instance().batchDisabled.get() || Template.instance().batchRunning.get();
    }
});

Template.fixWithdrawnDetails.events({
    'click #allToggle': function(e, i) {
        document.querySelectorAll('.batch-one').forEach(node => {
            node.checked = e.target.checked;
        });
        checkBatch(i)
    },
    'click .batch-one': function(e, i) {
        checkBatch(i);
    },
    'click .fix-one': function(e, i) {
        e.preventDefault();
        console.log(e.target.dataset.id);
        prepRestore(e.target.dataset.id, i);
        checkBatch(i);
        processRestore(e.target.dataset.id, i);
    },
    'click #restoreBatch': async function (e, i) {
        e.preventDefault();
        if (i.batchRunning.get()) {
            return;
        }
        console.log('batching');
        i.batchRunning.set(true);
        const checkedNodes = document.querySelectorAll('.batch-one:checked');
        const batchIds = [];
        document.querySelectorAll('.batch-one:checked').forEach(node => {
            batchIds.push(node.dataset.id);
        })
        for (const id of batchIds) {
            prepRestore(id, i);
        }
        for (const id of batchIds) {
            await processRestore(id, i);
        }
        i.batchRunning.set(false);
    }
});

function checkBatch(i) {
    const selectedCount = document.querySelectorAll('.batch-one:checked').length;
    i.batchDisabled.set(selectedCount === 0);
}

function prepRestore(id, i) {
    const box = document.querySelector(`.batch-one[data-id="${id}"]`);
    box.remove();
    checkBatch(i);
    const btn = document.querySelector(`.fix-one[data-id="${id}"]`);
    btn.disabled = true;
    btn.innerText = 'Running...'
}

function processRestore(id, i) {
    const btn = document.querySelector(`.fix-one[data-id="${id}"]`);
    Meteor.callAsync('undoCrmWithdrawn', id).then((result) => {
        if (!result) {
            btn.innerText = 'Restore Failed';
        } else {
            i.childList.set(i.childList.get().filter(child => child._id !== id));
            Meteor.callAsync('getChildInfo', id).then((result) => {
                console.log({ err: null, result });
                if (result) {
                    const prevList = i.prevList.get();
                    prevList.push(result);
                    i.prevList.set(prevList);
                }
            });
        }
      })
      .catch((err) => {
        btn.innerText = 'Restore Failed';
      });
}
