<template name="superAdminFixLineItemsWithoutVoid">

<h3 class="mx-24"><a href="/superadmin">SuperAdmin</a> - <a href="/superadmin/invoice-management">Invoice Management</a> - Voided invoices with line items that are not voided</h3>

<div class="card card-custom mx-24">
	<div class="box-body table-responsive no-padding">
		<div class="d-flex flex-row p-5">
			<form action="" method="post">
				<div class="form-group">
					<select id="orgsList" style="min-width: 450px">
						<option value=''>(All Orgs)</option>
						{{#each availableOrgs}}
						<option value='{{_id}}'>{{name}}</option>
						{{/each}}
					</select>
				</div>
				<div class="row">
					<div class="col-xs-4 ml-4 mb-4">
						<button type="submit" class="btn btn-primary btn-block btn-flat" id="btn-find-invoices">Find Invoices</button>
					</div>
					{{#if currentParentOrg }}
					<div class="col-xs-4 ml-4 d-flex align-items-center justify-content-center">
						<div class="checkbox icheck">
							<label>
								<input type="checkbox" id="useParentOrg" name="useParentOrg"> Use Parent Org ({{currentParentOrg.name}})
							</label>
						</div>
					</div>
					{{/if}}
					{{#if getNumberOfSelectableChildren }}
					<div class="col-xs-4 ml-4 d-flex align-items-center justify-content-center">
						<div class="checkbox icheck">
							<label>
								<input type="checkbox" id="useChildrenOrgs" name="useChildrenOrgs"> Use Children Orgs ({{getNumberOfSelectableChildren}})
							</label>
						</div>
					</div>
					{{/if}}
				</div>
			</form>
		</div>
		{{#if haveQueried }}
			<div class="row">
				<div class="col-12">
					{{#if effectedInvoices.length}}
						<div>
							<h4 class="mx-6">
								Found {{effectedInvoices.length}} effected invoices:
							</h4>
						</div>
						<div>
							<div class="mx-6">
								{{#if getConfirmingFixAll}}
								<div class="alert alert-warning">
									Are you sure you want to fix all invoices? This is a permanent change to invoices and cannot be undone.
									<button class="btn btn-primary btn-sm btn-fix-all-invoices-confirm">Yes</button>
									<button class="btn btn-primary btn-sm btn-fix-all-invoices-cancel">No</button>
								</div>
								{{else}}
								<button class="btn btn-primary btn-sm btn-fix-all-invoices">Fix All</button>
								{{/if}}
								<button class="btn btn-primary btn-sm btn-export ml-2">Export</button>
							</div>
						</div>
						<div class="mt-4">
							<h4 class="mx-6">
								View Invoices:
							</h4>
						</div>
						<table class="table table-striped">
							<thead>
								<tr>
									<th>Invoice</th>
									<th>Voided At</th>
									<th>Description</th>
									<th>Line Items</th>
									<th>Invoice Ledger</th>
									<th>Actions</th>
								</tr>
							</thead>
							<tbody>
								{{#each effectedInvoices}}
									<tr>
										<td>
											<a 
												target="_blank"
												ref="noopener noreferrer"
												href="/superadmin/invoice-management/redirect/{{_id}}"
												class="text-dark-75 font-weight-bolder text-hover-primary mb-1 font-size-h5">
												#{{invoiceNumber}}
											</a>
											<br />
											{{invoiceDate}}
											<br />
											{{_id}}
										</td>
										<td>
											{{formatTimestamp voidedAt}}
										</td>
										<td>
											{{personName}}<br />
											<ul class="list-unstyled p-0 m-0">
												{{#each desc in lineItemDescriptions}}
													<li>{{desc}}</li>
												{{/each}}
											</ul>
										</td>
										<td>
											{{#if lineItems.length}}
												{{#each lineItem in lineItems}}
												<div>
													{{lineItem.description}} ({{getLineItemAllocationType lineItem}})
													Applied Discounts:
													<ul>
														{{#each appliedDiscount in lineItem.appliedDiscounts}}
															<li>{{appliedDiscount.source}} ({{appliedDiscount.type}}) - voided? {{# if appliedDiscount.voidedAt }}Yes{{else}}No{{/if}}</li>
														{{/each}}
													</ul>
												</div>
												{{/each}}
											{{else}}
												No line items found.
											{{/if}}
										</td>
										<td>
											{{#if lineItemDetail.length}}
												<ul>
													{{#each lineItem in lineItemDetail}}
													<li>
														{{#if lineItem.debitAmount}}
															Debit ${{lineItem.debitAmount}}
														{{else}}
															Credit ${{lineItem.creditAmount}}
														{{/if}}
													</li>
													{{/each}}
												</ul>
											{{else}}
												No invoice ledger found
											{{/if}}
										</td>
										<td>
											<btn class="btn btn-primary btn-sm btn-fix-invoice" data-invoice-id="{{_id}}">Fix</btn>
										</td>
									</tr>
								{{/each}}
							</tbody>
						</table>
					{{else}}
						<p class="pr-8 pb-8 pl-8">No effected invoices found.</p>
					{{/if}}
				</div>
			</div>
		{{/if}}
	</div>
</div>

</template>
