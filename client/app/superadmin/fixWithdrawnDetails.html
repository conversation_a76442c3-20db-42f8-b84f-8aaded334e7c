<template name="fixWithdrawnDetails">
  <div class="d-flex flex-row justify-content-between mb-4 mx-8">
    <h3>Fix Withdrawn Children: {{org.name}}</h3>
  </div>

  <div class="ml-8 mb-4">
    <a href="/superadmin/fix-withdrawn-statuses">Back to site list</a>
  </div>
  <div>
    <button class="btn btn-primary ml-8 mb-4" disabled="{{batchDisabled}}" id="restoreBatch">Restore All Checked</button>
  </div>
  <div class="card card-custom mx-8">
    <div class="box-body table-responsive no-padding">
        <table class="table table-hover">
          <tbody>
          <tr>
            <th><input type="checkbox" id="allToggle"/></th>
            <th>Name</th>
            <th>Enroll Link</th>
            <th>Last CRM Status Change From</th>
            <th>Last CRM Status Change To</th>
            <th>Current CRM Status</th>
            <th>Current Manage Status</th>
            <th>Restore</th>
          </tr>
          {{#if loading}}
          <tr><td>Loading...</td></tr>
          {{/if}}

          {{#each childList}}
          <tr>
            <td><input type="checkbox" class="batch-one" data-id="{{_id}}"/></td>
            <td><a href="/people/{{_id}}" target="_blank">{{firstName}} {{lastName}}</a></td>
            <td><a href="{{uiUrl childcareCrm}}" target="_blank">Enroll Link</a></td>
            <td>{{lastStatusInfo.from.name}}</td>
            <td>{{lastStatusInfo.to.name}}</td>
            <td>{{lastStatusInfo.current.name}}</td>
            <td>{{crmStatus crmStatusId}}</td>
            <td><button data-id="{{_id}}" class="btn btn-primary fix-one">Restore</button></td>
          </tr>
          {{/each}}
          </tbody>
        </table>
      <h4>Previously Fixed</h4>
      <table class="table table-hover">
        <tbody>
        <tr>
          <th>Name</th>
          <th>Enroll Link</th>
          <th>Current Manage Status</th>
        </tr>

        {{#each prevList}}
        <tr>
          <td><a href="/people/{{_id}}" target="_blank">{{firstName}} {{lastName}}</a></td>
          <td><a href="{{uiUrl childcareCrm}}" target="_blank">Enroll Link</a></td>
          <td>{{crmStatus crmStatusId}}</td>
        </tr>
        {{/each}}
        </tbody>
      </table>
      </div>
  </div>
</template>
