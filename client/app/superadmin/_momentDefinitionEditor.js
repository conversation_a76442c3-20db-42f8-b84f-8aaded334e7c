import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './momentDefinitionEditor.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { MomentDefinitions } from '../../../lib/collections/momentDefinitions';
import { setupNewMomentSession } from '../../../mpweb';
import { showModal } from '../main';
import '../moments/momentFormModal';
import { Tracker } from 'meteor/tracker';

var editor;

Template.momentDefinitionEditor.rendered = function() {
	Tracker.autorun(function (e) {
		editor = AceEditor.instance("momentEditor", {theme: "dawn", mode: "json"});
		if(editor.loaded!==undefined){
			e.stop();
			var curId = FlowRouter.current().params.id;
			var md = MomentDefinitions.findOne({_id: curId});
			if (md) {
				delete md._id;
				editor.insert(JSON.stringify(md, null, '\t'));
			}
		}
	});
	
};

Template.momentDefinitionEditor.helpers({
	"title": function() {
		var curId = FlowRouter.current().params.id;
		var md = MomentDefinitions.findOne({_id: curId});
		if (md) return md.momentTypePretty;
		
	}
});

Template.momentDefinitionEditor.events({
	'click #previewLink': function() {
		setupNewMomentSession();
		Session.set("momentPreviewContent", JSON.parse(editor.getValue()));
		Session.set("activeMomentType", "previewDynamicMoment");
		$(window).scrollTop(0);
		showModal("momentFormModal", { }, "#momentFormModal");
	},
	'click #saveLink': function() {
		var curId = FlowRouter.current().params.id;
		var definitionData = JSON.parse(editor.getValue());
		Meteor.callAsync("updateMomentDefinition", curId, definitionData).then((curId) => {
			mpSwal.fire("Save Successful!");
		}).catch((error) => {
			mpSwal.fire("Error during save: " + error);
		});
	},
	'click #cancelLink': function() {
		FlowRouter.go("/superadmin/moment-definitions");
	}
});
