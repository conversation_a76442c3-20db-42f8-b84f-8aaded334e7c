import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './childcareCrmDefinitionEditor.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ChildcareCrmAccounts } from '../../../lib/collections/childcareCrmAccounts';
import { Tracker } from 'meteor/tracker';

var editor;

Template.childcareCrmDefinitionEditor.rendered = function() {
	Tracker.autorun(function (e) {
		editor = AceEditor.instance("childcareCrmEditor", {theme: "dawn", mode: "json"});
		if(editor.loaded!==undefined){
			e.stop();
			var curId = FlowRouter.current().params.id;
			var crmAccount = ChildcareCrmAccounts.findOne({_id: curId});
			if (crmAccount) {
				delete crmAccount._id;
				editor.insert(JSON.stringify(crmAccount, null, '\t'));
			}
		}
	});
	
};

Template.childcareCrmDefinitionEditor.helpers({
	"title": function() {
		var curId = FlowRouter.current().params.id;
		var crmAccount = ChildcareCrmAccounts.findOne({_id: curId});
		if (crmAccount) return crmAccount.name;
	}
});

Template.childcareCrmDefinitionEditor.events({
	'click #saveLink': function() {
		var curId = FlowRouter.current().params.id;
		var definitionData = JSON.parse(editor.getValue());
		Meteor.callAsync("updateChildcareCrmDefinition", curId, definitionData).then((curId) => {
			mpSwal.fire("Save Successful!");
		}).catch((error) => {
			mpSwal.fire("Error during save: " + error);
		});
	},
	'click #cancelLink': function() {
		FlowRouter.go("/superadmin/childcareCrm");
	}
});
