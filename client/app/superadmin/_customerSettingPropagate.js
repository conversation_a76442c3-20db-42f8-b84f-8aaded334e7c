import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_customerSettingPropagate.html';
import { hideModal } from '../main';
import { Orgs } from '../../../lib/collections/orgs';

Template.customerSettingPropagateModal.events({
	'submit form': function(event, instance) {
		event.preventDefault();
		$("#customerSettingPropagateSave").prop('disabled', true);

		var toCustomerId = $("#toCustomerId").val();
		const customerId = Template.instance().data && Template.instance().data.customerId;

		Meteor.callAsync('propagateOrgSettings', { 
			fromOrg: customerId, 
			toOrg: toCustomerId 
		}).then((result) => {
			if (result.success) {
				mpSwal.fire("Success", result.message, "success");
			}
			$("#customerSettingPropagateSave").prop('disabled', false);
			hideModal("#_customerSettingPropagateModal");
		}).catch((error) => {
			$("#customerSettingPropagateSave").prop('disabled', false);
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});

Template.customerSettingPropagateModal.created = function() {

}

Template.customerSettingPropagateModal.rendered = function() {

};

Template.customerSettingPropagateModal.helpers({
	"customer": function() {
		const customerId = Template.instance().data && Template.instance().data.customerId,
			customer = Orgs.findOne({_id: customerId});

		return customer || {};
	}
});
