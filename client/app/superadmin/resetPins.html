<template name="resetPins">

    <div class="card card-custom mx-24">
      <!-- /.login-logo -->
      <div class="login-box-body">
        <h3 class="ml-16 mt-8">Update PINs</h3>
        <div class="ml-16 mt-8">
          <p>At the selected organization, all of the following will happen:</p>
          <ul>
            <li>All parents and staff will be assigned a randomly generated PIN</li>
            <li>All parents and staff will be sent an email with their PIN</li>
            <li>All parents and staff will be sent an SMS with their PIN</li>
          </ul>
          <p>Please be careful, and <b>please only click "Reset" once per organization.</b></p>
        </div>
        <div class="d-flex flex-row justify-content-center align-items-center">
          <form action="" id="resetPinsForm" method="post">
            
            <div class="form-group has-feedback">
              <select id="orgsList" style="min-width: 450px">
                {{#each orgs}}
                <option value='{{_id}}'>{{name}}</option>
                {{/each}}
              </select>
            </div>
            <div class="row">
              <div class="col-xs-4 mb-4">
                <button type="submit" class="btn btn-primary btn-block btn-flat" id="resetButton">Reset</button>
              </div>
              <!-- /.col -->
            </div>
          </form>
        </div>
  
      </div>
      <!-- /.login-box-body -->
    </div>
    <!-- /.login-box -->
  
  </template>
  