import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './superAdminReportsFailedAutoPay.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../../lib/util/log';

Template.superAdminReportsFailedAutoPay.onRendered ( function() {
    $(".input-group.date").datepicker({
        autoclose: true,
        todayHighlight: true,
        endDate: new moment().format('MM/DD/YYYY'),
        orientation: 'bottom'
    });
});

Template.superAdminReportsFailedAutoPay.events({
    'click #reportsLink': async function (e, i) {
        e.preventDefault();
        FlowRouter.go('superAdminReports');
    },
    'click #btnViewReport': async function (e, i) {
        const dateString = document.getElementById('date').value;
        const timezone = document.getElementById('timezone').value;

        if (!dateString || !timezone) {
            mpSwal.fire({
                text: 'Please select a date and timezone.',
                icon: 'error'
            });
            return;
        }
        Meteor.callAsync('viewFailedAutoPayReport', { dateString, timezone }).then((result) => {
            mpSwal.fire('Your report is being generated. You will receive an email when it is ready if there are any failed auto payments and you are on the distribution list.');
        }).catch((err) => {
            Log.error({ err });
            mpSwal.fire({
                text: 'There was an error generating the report.',
                icon: 'error'
            });
        });
    }
});
