import { Template } from 'meteor/templating';
import './superAdminReports.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.superAdminReports.helpers({
    getReports() {
        return [
            {
                name: 'Failed Auto Payments',
                description: 'View which invoices had auto pay failed to run.',
                routeName: 'superAdminReportsFailedAutoPay'
            },
            {
                name: 'Failed Invoicing',
                description: 'View children who failed to be invoiced correctly.',
                routeName: 'superAdminReportsFailedInvoicing'
            }
        ]
    },
});

Template.superAdminReports.events({
    'click #superadminLink': async function (e, i) {
        e.preventDefault();
        FlowRouter.go('superAdminIndex');
    },
    'click .report-link': async function (e, i) {
        e.preventDefault();
        const routeName = e.currentTarget.dataset.routeName;
        FlowRouter.go(routeName);
    }
});