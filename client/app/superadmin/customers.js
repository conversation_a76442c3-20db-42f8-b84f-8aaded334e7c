import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import _ from '../../../lib/util/underscore';
import './customers.html';
import { ReactiveVar } from 'meteor/reactive-var';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { hideModal, showModal } from '../main';
import '../simpleModal/simpleModal';
import moment from 'moment-timezone';

Template.customers.onCreated( function() {
	var self = this;
	self.customers = new ReactiveVar();
});

Template.customers.onRendered( function() {
	var self = this;
	reloadResults(self);
});

Template.customers.helpers({
	"customers": function () {
		//const query =FlowRouter.current().queryParams.showinactive ? {} : {inactive: {"$ne": true}};
		//return Orgs.find(query, {sort: {name: 1}});
		const customers = Template.instance().customers.get() || [];
		return (FlowRouter.current().queryParams.showinactive) ? customers : customers.filter(c => !c.inactive);
	},
	"showInactive": function() {
		return FlowRouter.current().queryParams.showinactive;
	},
	"lastEngagementDateInfo": function(infoType) {
		if (this.engagementCounts) {

			var lastDate = _.max(this.engagementCounts, function (ec) { return ec.dayTimestamp;});
			if (infoType =="date")
				return new moment(lastDate.dayTimestamp).format('MM/DD/YYYY');
			else
				return lastDate.providerCount + lastDate.familyCount;
		}
	}
});

Template.customers.events({
	"click #btnAddCustomer"(e, instance) {
		showModal("simpleModal", {
			title:"Create New Customer",
			template: "newCustomerModal",
			data: {},
			onSave: (e, i, formFieldData) => {
				formFieldData.internalProvision = true;		
				console.log (formFieldData);
				
				Meteor.callAsync("createAppAccount", formFieldData).then((orgId) => {
					hideModal("#simpleModal");

					mpSwal.fire({
						title: "Org Created",
						text: "Go there now?",
						showCancelButton: true,
						cancelButtonText: "No",
						showCloseButton: true,
						confirmButtonText: "Yes"
					}).then(swalResult => {
						if (swalResult.value) {
							FlowRouter.go("/superadmin/customers/" + orgId);
						} else {
							reloadResults(instance);
						}
					});
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
            }
        }, "#simpleModal");
    }
});

Template.newCustomerModal.onRendered(function () {
    // Disable the "Save" button by default
    $('#btnSave').prop('disabled', true);
});

Template.newCustomerModal.events({
    "change #timezone": function (e) {
        $('#btnSave').prop('disabled', !e.target.value);
    }
});

function reloadResults(instance) {
	Meteor.callAsync("superadminGetCustomers", {}).then((result) => {
		instance.customers.set(result);
	}).catch((error) => {
		mpSwal.fire("Error", "Issue loading customer list: " + error.reason, "error");
	});
}