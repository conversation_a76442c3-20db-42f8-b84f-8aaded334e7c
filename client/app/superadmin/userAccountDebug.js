import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import './userAccountDebug.html';

Template.userAccountDebug.onCreated(function() {
	this.users = new ReactiveVar([]);
  this.associatedPeople = new ReactiveVar([]);
  this.otherPeople = new ReactiveVar([]);
  this.userInvitations = new ReactiveVar([]);
});

Template.userAccountDebug.helpers({
  "stringifyMemberships": function(m) {
    if (m) return JSON.stringify(m);
  },
  "formatSentDate": function(d) {
    if (d) return new moment(d).format("MM/DD/YYYY HH:mm");
  },
  "userInvitations": function() {
    return Template.instance().userInvitations.get();
  },
  "users": function() {
    return Template.instance().users.get();
  },
  "otherPeople": function() {
    return Template.instance().otherPeople.get();
  },
  "associatedPeople": function() {
    return Template.instance().associatedPeople.get();
  },
  "displayBool": function(b) {
    return b ? "true" : "false";
  }
});

Template.userAccountDebug.events({
  'click #btnFind': function(e, i) {
    i.users.set([]);
    i.associatedPeople.set([]);
    i.otherPeople.set([]);
    i.userInvitations.set([]);
    var emailAddress = $("#emailAddress").val();
    if (!emailAddress) {
      mpSwal.fire("Error", "email address cannot be empty", "error");
      return;
    }
    
    Meteor.callAsync("superAdminFindByEmail", { emailAddress: emailAddress.trim() }).then((result) => {
      i.users.set(result.users);
      i.associatedPeople.set(result.associatedPeople);
      i.otherPeople.set(result.otherPeople);
      i.userInvitations.set(result.userInvitations);
    })
      .catch((err) => {
        mpSwal.fire("Error", err.reason, "error");
      });
  },
  'click .btnDeleteInvitation': function(e, i) {
    var invitationId = $(e.currentTarget).attr("data-id");    
    return mpSwal.fire({  
      title: "Delete Invitation",   
      text: "You will not be able to recover this invitation once deleted!",   
      type: "warning",   
      showCancelButton: true,   
      confirmButtonText: "Yes, delete it!"
    }).then( result => {   
      if (result.value) {
        Meteor.callAsync("superAdminDeleteInvitation", invitationId).then((result) => {
          $("#btnFind").click();
        }).catch((error) => {
          mpSwal.fire("Error", error.reason, "error");
        });
      }
    });
  },
  'click .btnDeletePersonEmailAddress': function(e, i) {
    var personId = $(e.currentTarget).attr("data-id");
    return mpSwal.fire({  
      title: "Delete Email Address",   
      text: "You will not be able to recover this email address once deleted!",   
      type: "warning",   
      showCancelButton: true,   
      confirmButtonText: "Yes, delete it!"
    }).then( result => {   
      if (result.value) {
        Meteor.callAsync("superAdminDeletePersonEmail", personId).then((result) => {
          $("#btnFind").click();
        }).catch((error) => {
          mpSwal.fire("Error", error.reason, "error");
        });
      }
    });
  }
});
