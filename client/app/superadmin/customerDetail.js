import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import _ from '../../../lib/util/underscore';
import './customerDetail.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ReactiveVar } from 'meteor/reactive-var';
import { AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import { Orgs } from '../../../lib/collections/orgs';
import { KTCard } from '../bundles/KTlibs';
import { showModal } from '../main';
import './_customerSettingPropagate';
import { Curriculums } from '../../../lib/collections/curriculum';
import '../simpleModal/simpleModal';

Template.customerDetail.onCreated(function() {
	var self = this;
	self.defaultRoles = new ReactiveVar();
	self.customers = new ReactiveVar();
	self.hasCrm = new ReactiveVar();
});
Template.customerDetail.onRendered(function() {
  var card = new KTCard('boxCustomizations');
  var card2 = new KTCard('customerEnabledCustomMoments');
  var card3 = new KTCard('customerOtherInformation');
	var card4 = new KTCard('customerWMG');
	var self = this;
	Meteor.callAsync("getRoles", {}).then((result) => {
		self.defaultRoles.set(result);
	}).catch((error) => {
		mpSwal.fire("Error", "Error fetching roles:", error);
	});
	Meteor.callAsync('hasChildcareCrm', FlowRouter.current().params.id).then((res) => {
		self.hasCrm.set(res);
	}).catch((err) => {
		mpSwal.fire("Error", "Error checking for childcare CRM:", err);
	});
	Meteor.callAsync("superadminGetCustomers", {}).then((result) => {
		self.customers.set(result);
	}).catch((error) => {
		mpSwal.fire("Error", "Issue loading customer list: " + error.reason, "error");
	});

})

Template.customerDetail.helpers({
	"org": function() {
		return Orgs.findOne({_id: FlowRouter.current().params.id});
	},
	hasCrm: function() {
		return Template.instance().hasCrm.get();
	},
	cantSync: function() {
		return !!(Orgs.findOne({_id: FlowRouter.current().params.id}).sendingToEnroll) ? 'disabled' : '';
	},
	"customizationList": function() {
		var customizations =  _.map(this.customizations, function(value, key) {
			return {customizationName: key, customizationValue: value.toString()};
		});
		return _.sortBy(customizations, "customizationName");
	},
	"enabledMomentTypes": function() {
		return this.enabledMomentTypes.sort();
	},
	"jsonPrint": function(jsonObject) {
		return JSON.stringify(jsonObject, null, "\t");
	},
	"hasDefined": function (pathKey) {
		return _.deep(this, pathKey);
	},
	"parentOrgName": function() {
		const parentOrgId = this.parentOrgId,
			customers = Template.instance().customers.get()
			o = parentOrgId && customers && customers.find(c => c._id == parentOrgId);
		return o && o.name;
	},
	"availableMasterOrgs": function() {
		const customers = Template.instance().customers.get() || [];
		return customers.filter(c => c.enableSwitchOrg);
	},
	"hasBrands": function() {
		return Orgs.findOne({_id: FlowRouter.current().params.id}).getBrands() ? 'Yes' : 'No';
	},
	"availableContexts": function() {
		return Object.values(AvailablePermissions);
	},
	"enabledContexts": function() {
		const currentRoles = _.deep(this, "valueOverrides.availablePermissionsContexts") || [];
		return currentRoles.join("\n");
	},
	"hasContext": function(context) {
		const currentContexts = _.deep(this, "valueOverrides.availablePermissionsContexts");
		let out = false;
		if (!currentContexts && context=="none") out=true;
		else if (currentContexts && _.contains(currentContexts, context))
			out= true;
		return out;
	},
	"availableRoles": function() {
		const mappedRoles = {},
			allRoles = Template.instance().defaultRoles.get();
		_.each(allRoles, role => { mappedRoles[role.name] = role});

		const roleDefs = _.deep(this,"valueOverrides.roleDefinitions") || mappedRoles,
			output = [];
		_.each(roleDefs, (roleDef, rn) => {
				roleDef.id = rn;
				output.push(roleDef);
		});
		return _.sortBy(output, o => o.label + "|" + o.id);
	},
	"enabledRoles": function() {
		const org = Orgs.current(),
			allRoles = Template.instance().defaultRoles.get(),
			mappedRoles = {};
		_.each(allRoles, role => { mappedRoles[role.name] = role});

		const roleNames = org && org.enabledRoles(),
			roleDefs = _.deep(org,"valueOverrides.roleDefinitions") || mappedRoles,
			output = [];

		roleNames.forEach( rn => {
			const rd = roleDefs[rn];
			if (rd) {
				rd.id = rn;
				output.push(rd);
			}
		});

		return output.map(role => role.label + " (" + role.id + ")").sort().join("\n");
	},
	"hasRole": function(role) {

		const currentRoles = _.deep(this, "valueOverrides.availablePermissionsRoles");
		let out = false;
		if (!currentRoles && role=="none") out=true;
		else if (currentRoles && _.contains(currentRoles, role))
			out= true;
		return out;
	},
	"showModifyBillingStatus": function() {
		const org = Orgs.findOne({_id: FlowRouter.current().params.id});
		return org.billingStatus().status == "inactive" || org.billingStatus().status == "client_disabled";
	},
	"enabledCurriculumStandards"() {
		return (this.curriculumStandard || []).join(", ");
	}
});
Template.customerDetail.events({
	'click #btnInitiateCrmSync': () => {
		mpSwal.fire({
			title: 'Initiate Enroll Sync?',
			text:'This will remove push Manage families to Enroll. Note that we do not trigger the reverse sync here.',
			type: 'info',
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("sendOrgToCrm", FlowRouter.current().params.id);
		})
	},
	"click #addCustomizationLink": () => {
		$("#rowAddCustomization").show();
	},
	"click #btnCancelNewCustomization": () => {
		$("input[name=customization-name]").val("");
		$("#rowAddCustomization").hide();
	},
	"click #addMomentTypeLink": async function() {
		var momentTypeName = prompt("Enter moment type to add:");

		if (momentTypeName && momentTypeName !="") {
			await Meteor.callAsync("addMomentTypeToCustomer", FlowRouter.current().params.id, momentTypeName);
		}
	},
	"click .deleteMomentTypeLink": async function(e) {
		var momentTypeName = $(e.currentTarget).data("momenttype");

		if (momentTypeName && momentTypeName !="") {
			await Meteor.callAsync("deleteMomentTypeFromCustomer", FlowRouter.current().params.id, momentTypeName);
		}
	},
	"click #btn-manage-parent-org": (e) => {
		e.preventDefault();
		$("#set-parent-org-row").show();
	},
	"click #btn-parent-org-save": async () => {
		const parentOrgId = $("#orgsList").val();
		await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, {
			area: "parentOrgId",
			value: parentOrgId
		});
		$("#set-parent-org-row").hide();
	},
	"click #btn-parent-org-cancel": () => {
		$("#set-parent-org-row").hide();
	},
	"click #btn-manage-contexts": (e) => {
		e.preventDefault();
		$("#set-contexts-row").show();
	},
	"click #btn-contexts-save": async () => {
		let contextsList = $("input[name='set-contexts']:checked").map(function () {
			return this.value;
		}).get();
		await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, {
			area: "valueOverrides.availablePermissionsContexts",
			value: contextsList
		});
		$("#set-contexts-row").hide();
	},
	"click #btn-contexts-cancel": () => {
		$("#set-contexts-row").hide();
	},
	"click #btn-manage-roles": (event) => {
		event.preventDefault();
		$("#set-roles-row").show();
	},
	"click #btn-roles-save": async () => {
		let rolesList = $("input[name='set-roles']:checked").map(function() { return this.value;}).get();
		await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, { area: "valueOverrides.availablePermissionsRoles", value: rolesList});
		$("#set-roles-row").hide();
	},
	"click #btn-roles-cancel": () => {
		$("#set-roles-row").hide();
	},
	"click #btnChangeTranslation": () => {
		mpSwal.fire({
			title: 'Choose Translation',
			input: 'select',
			inputOptions: {
				'': 'None',
				'translationsEnChildCare': 'Child',
				'translationsEnAdultCare': 'Adult',
				'translationsEnLightbridge': 'Lightbridge',
				'translationsEnMontessori': 'Montessori'
			},
			inputPlaceholder:'Select Translation',
			showCancelButton:true
		}).then( async (result) => {
				if (result.dismiss) return;
				if (result.value == "") result.value = null;
				await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, { area: "language", value: result.value});

		});
	},
	"click #btnSaveNewCustomization": async () => {
		const currentKey = $("input[name=customization-name]").val();
		const currentValue = $("select[name=customization-value]").val();
		await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, { area: "customizations", key: currentKey, value: currentValue});
		$("input[name=customization-name]").val("");
		$("#rowAddCustomization").hide();
	},
	"click #btn-enable-master-org": (e) => {
		e.preventDefault();
		mpSwal.fire({
			title: "Enable as master org?",
			showCancelButton: true,
			cancelButtonText: "No",
			confirmButtonText: "Yes"
		}).then ( async (result) => {
			await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, {area: "enableSwitchOrg", value: result && result.value});
		})
	},
	"click #btn-enable-brands": (e) => {
		e.preventDefault();
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		showModal("simpleModal", {
			title:"Add Brands",
			template: "superadminAddBrands",
			data: { currentOrg },
			hideSave: true,
			cancelButtonLabel: 'Close'
		});
	},
	"click .btnRemoveCustomization": (e,i) => {
		const currentKey = $(e.currentTarget).data("key");
		mpSwal.fire({
			title: 'Remove Customization?',
			text:'This will remove ' + currentKey,
			type: 'warning',
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("changeCustomerProperty", FlowRouter.current().params.id, { area: "customizations", key: currentKey});
		})
	},
	"click #btnSetStripeID": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Set Stripe ID',
			input: 'text',
			inputValue: currentOrg.stripeCustomerId || "",
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "stripeCustomerId", value: result.value});
		});
	},
	"click #btnSetFromAddress": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Set From Address',
			text: 'Please do not change this value unless the domain has already been setup with AWS. Example value (no quotes): "My Customer Name <<EMAIL>>" ',
			input: 'text',
			inputValue: currentOrg.fromAddress || "",
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "fromAddress", value: result.value});
		});
	},
	"click #btnSetActive": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Reactivate customer?',
			confirmButtonText: "Yes, reactivate",
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "inactive", value: false});
		});
	},
	"click #btnSetInactive": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Deactivate customer?',
			confirmButtonText: "Yes, deactivate",
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "inactive", value: true});
		});
	},
	"click #btnUpdateLongName": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Set Long Name',
			text: 'This will change how the org name appears in certain places within the app and email" ',
			input: 'text',
			inputValue: currentOrg.getLongName() || "",
			showCancelButton: true
		}).then( async (result) => {
			if (result.value)
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "longName", value: result.value});
		});
	},
	"click #btnSetChargebackInvoiceEmail": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Set Chargeback/Account Invoice Email',
			text: 'This override the email address for sending chargeback and other payment account notices ',
			input: 'text',
			inputValue: currentOrg?.valueOverrides?.chargebackInvoiceEmail || "",
			showCancelButton: true
		}).then( async (result) => {
			if (result.hasOwnProperty("value"))
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "valueOverrides.chargebackInvoiceEmail", value: result.value});
		});
	},
	"click #btnLoginAs": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		Meteor.callAsync('switchOrg', currentOrg._id)
		  .then(() => {
			mpSwal.fire("Org changed. You will need to sign in again.");
			FlowRouter.go("signOut");
		  })
		  .catch((error) => {
			console.error("Error switching org:", error);
		  });
	},
  "click #btnSetCurriculumBank": (e,i) => {
    const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
    mpSwal.fire({
      title: 'Set Curriculum Bank ID',
      text: 'This is the orgId that owns the curriculum bank',
      input: 'text',
      inputValue: currentOrg.curriculumBankId || "",
      showCancelButton: true
    }).then( async (result) => {
      if (result.value) await Meteor.callAsync("setCurriculumBank", { orgId: currentOrg._id, bankId: result.value });
    });
  },
	"click #btnPropagateSettings": (e) => {
		showModal("customerSettingPropagateModal", {customerId: FlowRouter.current().params.id}, "#_customerSettingPropagateModal");
	},
	"click #btnSetWmgCenterId": (e, i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
    mpSwal.fire({
      title: 'Set WMG Center ID',
      text: 'A value provided by WMG (most likely a v4 uuid)',
      input: 'text',
      inputValue: currentOrg.wmgCenterId || "",
      showCancelButton: true
    }).then( async (result) => {
      if (result.value) await Meteor.callAsync("setWmgCenterId", { orgId: currentOrg._id, wmgCenterId: result.value });
    });
	},
	"click #btnSetConvenienceFeeLanguage": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
		  	title: 'Set Convenience Fee Language',
		  	html: `<textarea rows=4 style="width: 100%; max-width: 100%;" id='convenienceFeeLanguage'>${currentOrg?.valueOverrides.alternateServiceChargeFeeDescription || ""}</textarea>`,
			showCancelButton: true
		}).then( async (result) => {
			const val = document.getElementById('convenienceFeeLanguage').value;

		  	if (result.value) await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "valueOverrides.alternateServiceChargeFeeDescription", value: val});
		});
	},
	"click #btnSetWmgState": (e, i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Sync Center State to WMG?',
			confirmButtonText: "Yes, sync",
			showCancelButton: true
		}).then( async (result) => {
			if (result.value) await Meteor.callAsync("syncWmgCenterState", {orgId: currentOrg._id});
		});
	},
	"click #btnSetWmgCameras": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire("Info", "a new tab will launch with WMG config options", "success");
		Meteor.callAsync("getWmgConfigToken", { orgId: currentOrg._id }).then((result) => {
			var form = document.createElement("form");
			form.setAttribute("method", "post");
			form.setAttribute("action", result.url);
			form.setAttribute("target", "view");
			var hiddenField = document.createElement("input");
			hiddenField.setAttribute("type", "hidden");
			hiddenField.setAttribute("name", "jwt");
			hiddenField.setAttribute("value", result.token);
			form.appendChild(hiddenField);
			document.body.appendChild(form);
			window.open('', 'view');
			form.submit();
		}).catch((err) => {
			mpSwal.fire("Error", err.reason, "error");
		});
	},
	"click #btnSetWmgEngine": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
      		title: 'Create WMG Engine',
			html: '<p>Experimental Function from Superadmin. Consult Engineering before use. Enter <a href="https://www.youtube.com/watch?v=i7pMz74EpPA" target="_blank">the magic word</a> to proceed</p>',
      		input: 'text',
			icon: 'warning',
      		showCancelButton: true
    }).then( (result) => {
      if (result.value == "please") {
		  Meteor.callAsync("createWmgEngine", { orgId: currentOrg._id }).then((result) => {
			  mpSwal.fire("Success", "API call success", "success");
		  }).catch((error) => {
			  mpSwal.fire("Error", error.reason, "error");
		  });
		}
    });
	},
	"click #btnDisplayGroupMappings": (e,i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		Meteor.callAsync("showGroupsForCameraConfig", currentOrg._id).then((result) => {
			mpSwal.fire({
				title: 'Group Info',
				html: `<div class="d-flex flex-column">${result.join('')}</div>`,
				icon: 'success',
			});
		}).catch((err) => {
			mpSwal.fire("Error", err.reason, "error");
		});
	},
	"click #btnAddLegalEntity": (e, i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		showModal("simpleModal", {
			title:"Add Legal Entity",
			template: "superadminAddLegalEntity",
			data: {   },
			onSave: (saveEvent, formInstance, formFieldData) => {
				formFieldData.orgId = currentOrg._id;
				Meteor.callAsync("superadminAddLegalEntity", {
					...formFieldData
				}).then((result) => {
					$("#simpleModal").modal("hide");
				}).catch((error) => {
					$(e.target).html('Submit').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnRemoveHistoricalLegalEntity": (e, i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id}),
			index = $(e.currentTarget).data("idx");
		mpSwal.fire({
			title: 'Remove this legal entity?',
			showCancelButton: true,
			cancelButtonText: "No",
			confirmButtonText: "Yes"
  		}).then( (result) => {
			if (result.value) {
				Meteor.callAsync("superadminRemoveLegalEntity", { orgId: currentOrg._id, index }).then((result) => {
					mpSwal.fire("Success", "Entity removed.", "success");
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click #btnSetBillingInvoiceOnly": (e, i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "billing.enabled", value: true }).then((result) => {
			mpSwal.fire("Success", "Invoice only mode enabled", "success");
		  }).catch((error) => {
			mpSwal.fire("Error", error.reason, "error");
		  });
	},
	"click #btnSetSageLocationCode": (e, i) => {
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id});
		mpSwal.fire({
			title: 'Set Sage Location Code',
			text: 'A value used for this location in Sage export',
			input: 'text',
			inputValue: currentOrg?.billing?.sage?.locationCode || "",
			showCancelButton: true
		  }).then( async (result) => {
			if (result.isConfirmed) {
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "billing.sage.locationCode", value: result.value });
			}
		  });
	},
	"click #btnManageCurriculumStandards"(e, i) {
		e.preventDefault();
		const currentOrg = Orgs.findOne({_id: FlowRouter.current().params.id}),
			currentStandards = currentOrg.curriculumStandard || [],
			availableStandards = Curriculums.availableStandardDefs();

		const standardsText = availableStandards.map(standardDef => `<option value="${standardDef}" ${currentStandards.includes(standardDef) ? "selected" : ""}>${standardDef}</option>`).join("\n") ;

		mpSwal.fire({
			title: "Manage Curriculum Standards",
			html: '<select name="curriculum-standards" multiple>'+
				standardsText +
				'</select>',
			focusConfirm: false,
			showCancelButton: true,
			confirmButtonText: "Save Changes",
			preConfirm: function () {
				return new Promise(function (resolve) {
					resolve([
						$("select[name='curriculum-standards']").val(),
					])
				});
			}
		}).then(async (result) => {
			if (result.value) {
				await Meteor.callAsync("changeCustomerProperty", currentOrg._id, { area: "curriculumStandard", value: result.value[0]});
			}
		})
	}
});

Template.superadminAddBrands.created = function() {
	this.brands = new ReactiveVar([{name: this.data.currentOrg.name}]);
    if(this.data.currentOrg.getBrands()) {
		this.brands.set([{name: this.data.currentOrg.name}, ...this.data.currentOrg.getBrands()]);
	}
	this.availableOrgs = new ReactiveVar([]);
	Meteor.callAsync('getSuperadminAvailableOrgs', this.data.currentOrg._id).then((res) => {
		this.availableOrgs.set(res);
	});
};

Template.superadminAddBrands.helpers({
	'getBrands': () => {
		return Template.instance().brands.get();
	},
	"availableOrgs": function() {
		return Template.instance().availableOrgs.get();
	},

	"isChecked": function(org, id) {
		if(!id && !org.selectedBrand) {
			return true;
		}
		else return id === org.selectedBrand;
	}
})

Template.superadminAddBrands.events({
	'input #AddBrand-Name': function(e) {
		const value = $(e.currentTarget).val().trim();
		if(value.length < 3) {
			$('#btnSaveAddBrand').addClass('disabled');
		}else{
			$('#btnSaveAddBrand').removeClass('disabled');
		}

	},

	'click #btnAddBrand': function(e) {
		e.preventDefault();
		$("#frmAddBrand").show();
		$("#btnAddBrand").hide();
		$("input[name=brand-id]").val("");
	},
	'click #btnCancelAddBrand': function(e) {
		e.preventDefault();
		$("#frmAddBrand").hide();
		$("#frmAddBrand")[0].reset();
		$("#btnAddBrand").show();
	},
	'click #btnSaveAddBrand': function(e, i) {
		e.preventDefault();
		const orgId = i.data.currentOrg._id;
		const brandName = $("#AddBrand-Name").val().trim();
		const existingId = $("input[name=brand-id]").val();

		if (_.isEmpty(brandName)) {
			return mpSwal.fire({icon:"error", title:"Cannot save empty field", text: "Please enter a brand name"});
		}

		if (brandName.length < 3) {
			return mpSwal.fire({icon:"error", title:"Cannot save", text: "Brand name must be longer than 3 characters"});
		}

		Meteor.callAsync("insertBrand", { existingId, brandName, orgId }).then((res) => {
			if (res) {
				let array = i.brands.get();
				if (array.map(brand => brand._id).includes(res._id)) {
					array.find(brand => brand._id === res._id).name = res.name;
				} else {
					array.push(res);
				}
				// sort new array by name except for the first item
				array = [...array.slice(0, 1), ..._.sortBy(array.slice(1), 'name')];
				i.brands.set(array);
			}
		});
		$("#frmAddBrand").hide();
		$("#frmAddBrand")[0].reset();
		$("#btnAddBrand").show();
	},
	'click #btnEditBrand': function(e) {
		e.preventDefault();

		$("input[name=brand-id]").val(this._id);
		$("#AddBrand-Name").val(this.name);

		$("#frmAddBrand").show();
		$("#btnAddBrand").hide();
	},
	'click #btnDeleteBrand': function(e, i) {
		const brandId = this._id;
		const orgId = i.data.currentOrg._id;

		mpSwal.fire({
			title: "Warning!",
			text: "If you delete this brand, all sites that are currently assigned to this brand will no longer be assigned to this brand. Are you sure you want to proceed?",
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: 'Yes',
			closeOnConfirm: false
		}).then(result => {
			if (result.value) {
				Meteor.callAsync("removeBrand", { brandId, orgId }).then((response) => {
					mpSwal.fire("Brand removed");
					i.brands.set(i.brands.get().filter(brand => brand._id !== brandId));
				});
			}
		});
	},
	"click #btnAssignSites": (e,i) => {
		e.preventDefault();
		$("#frmAssignSites").show();
		$("#btnAssignSites").hide();
	},

	"click #btnCancelAssignSites": (e, i) => {
		e.preventDefault();
		$("#frmAssignSites").hide();
		$("#btnAssignSites").show();
	},

	"click #btnSaveAssignSites": (e, i) => {
		e.preventDefault();
		const formData = $("#frmAssignSites").serializeArray();
		formData.forEach(data => {
			Meteor.callAsync('assignSites', { id: data.name, value: data.value }).then((result) => {
				console.log("Sites assigned successfully:", result);
			}).catch((err) => {
				mpSwal.fire('Error', err.reason, 'error');
			});
		})
		$("#frmAssignSites").hide();
		$("#btnAssignSites").show();
	}
});
