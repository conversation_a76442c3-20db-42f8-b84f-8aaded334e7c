<template name="userAccountDebug">
  <div class="card card-custom mx-24">
    <div class="form-group row mt-16">
      <label class="col-xl-3 col-lg-3 text-right col-form-label"><h5>Email Address (case sensitive)</h5></label>
      <div class="col-lg-3 col-md-6 col-sm-8">
        <input type="text" class="form-control" id="emailAddress">
      </div>
      <div class="col-lg-3 col-md-3 col-sm-4">
        <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnFind">
          Search
        </div>
      </div>
    </div>
    <div class="separator separator-dashed my-8"></div>
    <div class="row mx-24">
        <h3>Users</h3>
    </div>
    <div class="box-body no-padding">
    <table class="table table-hover mx-24" style="display:block;overflow-x:auto;padding-bottom:10px;">
      <tbody>
        <tr>
          <th class='no-wrap'>_id</th>
          <th class='no-wrap'>orgId</th>
          <th class='no-wrap'>orgName</th>
          <th class='no-wrap'>personId</th>
          <th class='no-wrap'>pending</th>
          <th class='no-wrap'>membership</th>
        </tr>
        
        {{#each u in users}}
          <tr>
            <td>{{u._id}}</td>
            <td class='no-wrap'>{{u.orgId}}</td>
            <td class='no-wrap'>{{u.orgName}}</td>
            <td class='no-wrap'>{{u.personId}}</td>
            <td class='no-wrap'>{{displayBool u.pending}}</td>
            <td class='no-wrap'>{{stringifyMemberships u.membership}}</td>
          </tr>
        {{/each}}
      </tbody>
    </table>
    </div>
    <div class="separator separator-dashed my-8"></div>
    <div class="row">
      <div class="row mx-24">
        <h3>Associated People</h3>
      </div>
    </div>
    <div class="box-body no-padding">
    <table class="table table-hover mx-24" style="display:block;overflow-x:auto;padding-bottom:10px;">
      <tbody>
        <tr>
          <th class='no-wrap'>name</th>
          <th class='no-wrap'>profileEmailAddress</th>
          <th class='no-wrap'>_id (personId)</th>
          <th class='no-wrap'>orgId</th>
          <th class='no-wrap'>orgName</th>
          <th class='no-wrap'>inActive</th>
          <th class='no-wrap'>ACTION</th>
        </tr>
        
        {{#each p in associatedPeople}}
          <tr>
            <td class='no-wrap'>{{p.firstName}} {{p.lastName}}</td>
            <td class='no-wrap'>{{p.profileEmailAddress}}</td>
            <td class='no-wrap'>{{p._id}}</td>
            <td class='no-wrap'>{{p.orgId}}</td>
            <td class='no-wrap'>{{p.orgName}}</td>
            <td class='no-wrap'>{{displayBool p.inActive}}</td>
            <td class='no-wrap'><a href="#" class="btnDeletePersonEmailAddress text-danger" data-id="{{p._id}}"><i class="fa fa-times" style="margin-left:12px;color:var(--danger);font-size:16px;"></i> Delete Email Address</a></td>
          </tr>
        {{/each}}
      </tbody>
    </table>
    </div>
    <div class="separator separator-dashed my-8"></div>
    <div class="row">
      <div class="row mx-24">
        <h3>Other People (have email address not tied to user)</h3>
      </div>
    </div>
    <div class="box-body no-padding">
    <table class="table table-hover mx-24" style="display:block;overflow-x:auto;padding-bottom:10px;">
      <tbody>
        <tr>
          <th class='no-wrap'>name</th>
          <th class='no-wrap'>profileEmailAddress</th>
          <th class='no-wrap'>_id (personId)</th>
          <th class='no-wrap'>orgId</th>
          <th class='no-wrap'>orgName</th>
          <th class='no-wrap'>inActive</th>
          <th class='no-wrap'>ACTION</th>
        </tr>
        
        {{#each p in otherPeople}}
          <tr>
            <td class='no-wrap'>{{p.firstName}} {{p.lastName}}</td>
            <td class='no-wrap'>{{p.profileEmailAddress}}</td>
            <td class='no-wrap'>{{p._id}}</td>
            <td class='no-wrap'>{{p.orgId}}</td>
            <td class='no-wrap'>{{p.orgName}}</td>
            <td class='no-wrap'>{{displayBool p.inActive}}</td>
            <td class='no-wrap'><a href="#" class="btnDeletePersonEmailAddress text-danger" data-id="{{p._id}}"><i class="fa fa-times" style="margin-left:12px;color:var(--danger);font-size:16px;"></i> Delete Email Address</a></td>
          </tr>
        {{/each}}
      </tbody>
    </table>
    </div>
    <div class="separator separator-dashed my-8"></div>
    <div class="row">
      <div class="row mx-24">
        <h3>User Invitations (across all people records & email address)</h3>
      </div>
    </div>
    <div class="box-body no-padding">
    <table class="table table-hover mx-24" style="display:block;overflow-x:auto;padding-bottom:10px;">
      <tbody>
        <tr>
          <th class='no-wrap'>_id</th>
          <th class='no-wrap'>email</th>
          <th class='no-wrap'>personId</th>
          <th class='no-wrap'>orgId</th>
          <th class='no-wrap'>orgName</th>
          <th class='no-wrap'>used</th>
          <th class='no-wrap'>last sent</th>
          <th class='no-wrap'>ACTION</th>
        </tr>
        
        {{#each ui in userInvitations}}
          <tr>
            <td class='no-wrap'>{{ui._id}}</td>
            <td class='no-wrap'>{{ui.email}}</td>
            <td class='no-wrap'>{{ui.personId}}</td>
            <td class='no-wrap'>{{ui.orgId}}</td>
            <td class='no-wrap'>{{ui.orgName}}</td>
            <td class='no-wrap'>{{displayBool ui.used}}</td>
            <td class='no-wrap'>{{formatSentDate ui.lastSentAt}}</td>
            <td class='no-wrap'><a href="#" class="btnDeleteInvitation text-danger" data-id="{{ui._id}}"><i class="fa fa-times" style="margin-left:12px;color:var(--danger);font-size:16px;"></i> Delete</a></td>
          </tr>
        {{/each}}
      </tbody>
    </table>
    </div>
  </div>
</template>
