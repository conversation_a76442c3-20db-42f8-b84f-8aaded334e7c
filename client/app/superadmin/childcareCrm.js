import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ChildcareCrmAccounts } from '../../../lib/collections/childcareCrmAccounts';
import _ from '../../../lib/util/underscore';
import './childcareCrm.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { hideModal, showModal } from '../main';
import '../simpleModal/simpleModal';

Template.childcareCrm.helpers({
	"crmAccounts": function () {
		return ChildcareCrmAccounts.find({}, {sort: {name: 1}});
	},
	"totalOrgsEnabled": function(centers) {
    let centersEnabled = 0;
		if (centers) {
      _.each(centers, (c) => {
        if (c.orgId) ++centersEnabled;
      });
		}
    
    return centersEnabled
	}
});

Template.childcareCrm.events({
	"click .sync-crm-data": function(event, template) {
		const curId = $(event.currentTarget).attr("data-id");
		mpSwal.fire("Sync Requested");
		Meteor.callAsync("syncChildcareCrmAccount", curId).then((result) => {
			console.log("Childcare CRM account synced successfully:", result);
		}).catch((err) => {
			mpSwal.fire("Error", err.reason, "error");
		});
	},
	"click #btnAddCrmIntegration": function() {
		showModal("simpleModal", {
			title:"Create New Childcare CRM Integration",
			template: "newCrmIntegrationModal",
			data: {},
			onSave: (e, i, formFieldData) => {
				
				Meteor.callAsync("createCrmAccount", formFieldData).then((crmAccountId) => {
					hideModal("#simpleModal");

					mpSwal.fire({
						title: "Crm Account Created",
						text: "Go there now?",
						showCancelButton: true,
						cancelButtonText: "No",
						showCloseButton: true,
						confirmButtonText: "Yes"
					}).then(swalResult => {
						if (swalResult.value) {
							FlowRouter.go("/superadmin/childcareCrm/" + crmAccountId);
						}
					});
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		}, "#simpleModal");
	}
});
