import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './backparseAdyen.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.backparseAdyen.onRendered ( function() {
    $(".input-group.date").datepicker({
        autoclose: true,
        todayHighlight: true,
        endDate: new moment().subtract(1, 'day').format('MM/DD/YYYY')
    });
});

Template.backparseAdyen.events({
    'click #fixesLink': async function (e, i) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #btnParseAdyen': async function (e, i) {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        if (!startDate || !endDate) {
            mpSwal.fire({
                text: 'Please select a start and end date. They can be the same date, if needed.',
                icon: 'error'
            });
            return;
        }
        if (moment(startDate).isAfter(moment(endDate))) {
            mpSwal.fire({
                text: 'The start date must be before the end date.',
                icon: 'error'
            });
            return;
        }
        const confirm = await mpSwal.fire({
            text: 'This will parse Adyen reports that haven\'t already been parsed between the given dates. It may take a while. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes, parse \'em!'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('adyenParseReports', { startDate, endDate }).then((result) => {
                mpSwal.fire('Parsing initiated.');
            });
        }
    }
});
