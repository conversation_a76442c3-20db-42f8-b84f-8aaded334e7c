import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './fixAdyenReports.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.fixAdyenReports.onRendered ( function() {
    $(".input-group.date").datepicker({
        autoclose: true,
        todayHighlight: true,
        endDate: new moment().subtract(1, 'day').format('MM/DD/YYYY')
    });
});

Template.fixAdyenReports.events({
    'click #fixesLink': async function (e, i) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #btnFixAdyenReports': async function (e, i) {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        if (!startDate || !endDate) {
            mpSwal.fire({
                text: 'Please select a start and end date. They can be the same date, if needed.',
                icon: 'error'
            });
            return;
        }
        if (moment(startDate).isAfter(moment(endDate))) {
            mpSwal.fire({
                text: 'The start date must be before the end date.',
                icon: 'error'
            });
            return;
        }
        const confirm = await mpSwal.fire({
            text: 'This will pull/replace Adyen reports between the given dates. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes, pull \'em!'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('adyenPullReports', { startDate, endDate }).then((result) => {
                console.log({ result });
                mpSwal.fire('Alright. A request has been made to Adyen to pull the reports. Good luck.');
            });
        }
    }
});
