import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './chargebackInvoicesTransferFunds.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.chargebackInvoicesTransferFunds.events({
    'click #fixesLink': async function (e) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #btnTransfer': async function (e, i) {
        const confirm = await mpSwal.fire({
            text: 'This will send a request to Adyen to transfer funds for all open chargeback invoices for all customers. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('runChargebackInvoicesTransferFunds', {}).then((result) => {
                console.log({ result });
                mpSwal.fire('Alright. Requests are being made to transfer funds for any open chargeback invoices. Any failures will be returned in an email to the billing errors email.');
            });
        }
    }
});
