import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './runDeferredEmailsNow.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../../lib/util/log';
import { Orgs } from '../../../../lib/collections/orgs';

Template.runDeferredEmailsNow.events({
    'click #fixesLink': async function (e) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #runDeferredEmailsNow': async function () {
        const confirm = await mpSwal.fire({
            text: 'This will run the nightly deferred emails for the current org only. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes'
        });

        if (confirm.isConfirmed) {
            Meteor.callAsync('runDeferredEmailsNow', Orgs.current()._id).then((response) => {
                Log.info('Deferred Emails job completed');
                mpSwal.fire('Success', 'Deferred Emails job completed.', 'success');
            }).catch((error) => {
                Log.error(`Error running deferred emails job: ${error.message || error.reason}`);
                mpSwal.fire('Error', `Error running deferred emails job: ${error.message || error.reason}`, 'error');
            });
        }
    }
});
