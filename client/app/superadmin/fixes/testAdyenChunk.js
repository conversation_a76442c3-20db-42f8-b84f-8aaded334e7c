import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './testAdyenChunk.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.testAdyenChunk.onRendered ( function() {
    $(".input-group.date").datepicker({
        autoclose: true,
        todayHighlight: true,
        endDate: new moment().subtract(1, 'day').format('MM/DD/YYYY')
    });
});

Template.testAdyenChunk.events({
    'click #btnChunkAdyen': async function (e, i) {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const chunkSize = Number(document.getElementById('chunkSize').value);

        if (!startDate || !endDate) {
            mpSwal.fire({
                text: 'Please select a start and end date. They can be the same date, if needed.',
                icon: 'error'
            });
            return;
        }
        if (chunkSize < 1) {
            mpSwal.fire({
                text: 'Please enter a chunk size greater than 0.',
                icon: 'error'
            });
            return;
        }
        if (moment(startDate).isAfter(moment(endDate))) {
            mpSwal.fire({
                text: 'The start date must be before the end date.',
                icon: 'error'
            });
            return;
        }
        const confirm = await mpSwal.fire({
            text: 'This will chunk Adyen reports that haven\'t already been chunked between the given dates. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes, chunk \'em!'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('adyenChunkReports', { startDate, endDate, chunkSize }).then((result) => {
                mpSwal.fire('Chunking initiated.');
            });
        }
    }
});
