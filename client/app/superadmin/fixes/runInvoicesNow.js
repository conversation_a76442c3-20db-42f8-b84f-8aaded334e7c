import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './runInvoicesNow.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../../lib/util/log';

Template.runInvoicesNow.helpers({

});

Template.runInvoicesNow.events({
    'click #fixesLink': async function (e) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #btnRunInvoicesNow': async function (e, i) {
        const confirm = await mpSwal.fire({
            text: 'This will run the full invoicing cron job now. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('reRunInvoicesCronJob').then((result) => {
                Log.info("Invoicing cron job ran successfully:", result);
            }).catch((err) => {
                Log.error('Error running invoicing cron job', err);
                mpSwal.fire(`Error running invoicing cron job: ${err.message || err.reason}`);
            });
        }
    }
});
