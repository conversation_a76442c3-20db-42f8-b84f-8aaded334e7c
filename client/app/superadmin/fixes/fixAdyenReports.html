<template name="fixAdyenReports">
    <div class="d-flex flex-row justify-content-between mb-4 mx-8">
        <a href="" id="fixesLink">Go Back</a>
    </div>
    <div class="d-flex flex-row justify-content-between mb-4 mx-8">
        <h3>Pull Adyen Reports</h3>
    </div>

    <div class="card card-custom mx-8">
        <div class="card-header flex-wrap border-0 pt-6 pb-0">
            <div class="card-title">
                <h3 class="card-label">Choose the dates to pull reports from Adyen</h3>
            </div>
        </div>
        <div class="card-body">
            <form id="adyen-reports-fix">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="control-label">Start Date:</label><br/>
                        <div class="input-group date row no-gutters" data-provide="datepicker">
                            <div class="col-11">
                                <input type="text" class="form-control" name="startDate" id="startDate">
                            </div>
                            <div class="input-group-addon col-1 ml-n6">
                                <span class="fa fa-calendar mt-4"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="control-label">End Date:</label><br/>
                        <div class="input-group date row no-gutters" data-provide="datepicker">
                            <div class="col-11">
                                <input type="text" class="form-control" name="endDate" id="endDate">
                            </div>
                            <div class="input-group-addon col-1 ml-n6">
                                <span class="fa fa-calendar mt-4"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="card-footer">
            <div class="row">
                <div class="col-12 text-center">
                    <button class="btn btn-primary font-weight-bolder" id="btnFixAdyenReports">Pull Reports</button>
                </div>
            </div>
        </div>
    </div>
</template>
