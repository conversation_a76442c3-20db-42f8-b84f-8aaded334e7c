import { Template } from "meteor/templating";
import "./syncOrg.html";

const syncResult = new ReactiveVar("");
const syncResultType = new ReactiveVar(null);
const allCenters = new ReactiveVar([]);
const isSyncing = new ReactiveVar(false);

Template.syncOrg.onCreated(async function () {
  syncResult.set("");
  syncResultType.set(null);
  isSyncing.set(false);

  try {
    const centers = await Meteor.callAsync("fetchAllOrg");
    allCenters.set(centers);
  } catch (error) {
    syncResult.set("❌ Failed to load organizations.");
    syncResultType.set("error");
  }
});

Template.syncOrg.helpers({
  allCenters() {
    return allCenters.get();
  },
  syncResult() {
    return syncResult.get();
  },
  syncResultType() {
    return syncResultType.get();
  },
  isSyncing() {
    return isSyncing.get();
  },
  shouldFade() {
    return true;
  },
});

Template.syncOrg.events({
  "click #syncSingleOrgButton": async function () {
    const orgId = document.getElementById("orgIdDropdown").value;
    if (!orgId) {
      syncResult.set("❗ Please select an Org to sync.");
      syncResultType.set("error");
      return;
    }

    try {
      isSyncing.set(true);
      syncResult.set("⏳ Syncing org...");
      syncResultType.set("info");

      const result = await Meteor.callAsync("syncChildStatuses", orgId);

      syncResult.set(`✅ Sync successful.\n${result}`);
      syncResultType.set("success");
    } catch (error) {
      syncResult.set(`❌ Sync failed.\n${error.message}`);
      syncResultType.set("error");
    } finally {
      isSyncing.set(false);
    }
  },

  "click #syncAllOrgsButton": async function () {
    try {
      isSyncing.set(true);
      syncResult.set("⏳ Syncing all orgs...");
      syncResultType.set("info");

      const result = await Meteor.callAsync("syncChildStatuses", null);

      syncResult.set(`✅ ${ result }`);
      syncResultType.set("success");
    } catch (error) {
      syncResult.set(`❌ Sync failed.\n${error.message}`);
      syncResultType.set("error");
    } finally {
      isSyncing.set(false);
    }
  },
});
