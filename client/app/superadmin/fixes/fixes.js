import { Template } from 'meteor/templating';
import './fixes.html';
import './syncOrg.js'
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { showModal } from '../../main.js';

Template.fixes.helpers({
    getFixes() {
        return [
            {
                name: 'Adyen Reports',
                description: 'Fixes Adyen reports that are missing or need to be replaced.',
                routeName: 'superAdminFixAdyenReports',
                buttonText: 'View'
            },
            {
                name: 'Parse Adyen Reports',
                description: 'Parse Adyen report data into new format.',
                routeName: 'superAdminParseAdyen',
                buttonText: 'View'
            },
            {
                name: 'Generate Chargeback Invoices',
                description: 'Generate chargeback invoices for all orgs with chargebacks.',
                routeName: 'superAdminFixGenerateChargebackInvoices',
                buttonText: 'View'
            },
            {
                name: 'Transfer Funds for Chargeback Invoices',
                description: 'Transfer funds for chargeback invoices for all orgs.',
                routeName: 'superAdminFixChargebackInvoicesTransferFunds',
                buttonText: 'View'
            },
            {
                name: 'Run Invoices Now',
                description: 'Run Invoicing Cron Job Now.',
                routeName: 'superAdminFixRunInvoicesNow',
                buttonText: 'View'
            },
            {
                name: 'Run Default Group Sync Now',
                description: 'Run Default Group Sync Cron Job Now.',
                routeName: 'superAdminFixRunDefaultGroupSyncNow',
                buttonText: 'View'
            },
            {
                name: 'Run Deferred Emails Job Now',
                description: 'Run Deferred Emails Job Now.',
                routeName: 'superAdminFixRunDeferredEmailsJobNow',
                buttonText: 'View'
            },
            {
                name: 'Sync Org Status',
                description: 'Sync Org Status.',
                routeName: 'syncOrg',
                buttonText: 'View'
            },
            {
                name: 'Run Precompute cron job',
                description: 'Run Precompute cron job.',
                routeName: 'cronprecompute',
                buttonText: 'View'
            },
            {
                name: 'Run Announcement Reminder',
                description: 'Runs the Announcement Reminder task for most recent announcement in the current org.',
                routeName: 'superAdminFixRunAnnouncementReminder',
                buttonText: 'Run Now'
            },
            {
                name: 'Run Precompute cron job for org in hierarchy',
                description: 'Runs the precompute cron job for the current org and all orgs in the hierarchy.',
                routeName: 'superAdminFixRunPrecomputeCronjobForOrgHierarchy',
                buttonText: 'Run Now'
            },
             {
                name: 'Run Kinderconnect Report',
                description: 'Runs the kinderconnect cronjob.',
                routeName: 'superAdminFixRunKinderConnect',
                buttonText: 'Run Now'
            }
        ]
    },
});

Template.fixes.events({
    'click #superadminLink': async function (e, i) {
        e.preventDefault();
        FlowRouter.go('superAdminIndex');
    },
    'click .fix-link': async function (e, i) {
        e.preventDefault();
        const routeName = e.currentTarget.dataset.routeName;
         if (routeName === 'cronprecompute') {
          Meteor.callAsync('triggerPrecomputeCronjob')
            .then((result) => {
              console.log('Results for precompute cron job:', result);
            })
            .catch((error) => {
              console.error('Error triggering precompute cron job:', error);
            });
        } else if (routeName === 'syncOrg') {
            showModal("syncOrg", { shouldFade: true }, "#syncModal");
        } else if (routeName === 'superAdminFixRunAnnouncementReminder') {
          Meteor.callAsync('runAnnouncementRemindersJob')
            .then((result) => {
              console.log('Results for Announcement reminder:', result);
            })
            .catch((error) => {
              console.error('Error triggering Announcement reminder:', error);
            });
            
        }else if(routeName === 'superAdminFixRunPrecomputeCronjobForOrgHierarchy'){
            Meteor.callAsync('triggerPrecomputeCronjobForOrgHierarchy')
            .then((result) => {
              console.log('Results for precompute cron job:', result);
            })
            .catch((error) => {
              console.error('Error triggering precompute cron job:', error);
            });
        } 
        else if(routeName === 'superAdminFixRunKinderConnect'){
            Meteor.callAsync('triggerKinderConnect')
            .then((result) => {
              console.log('Results for Kinderconnect report', result);
            })
            .catch((error) => {
              console.error('Error triggering kinder connect cron job:', error);
            });
        } 
        else  {
          FlowRouter.go(routeName);
        }
    }
});
