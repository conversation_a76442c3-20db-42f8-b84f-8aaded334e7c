<template name="permission">
	
	<div class="row mx-8">
		<div class="col-12">
			<a href="/superadmin/roles" id="btnIndex">&lt; Roles List</a><br/>
			<br/>
			<h3>Role: {{role.name}}</h3>
		</div>
	</div>
	
	<div class="card mx-8">

		<div class="card-body">
			<div class="row mb-12">
				<div class="col-6">
					<label class='col-form-label' style="font-weight:bold">Label:</label>
					<input type="text" class="form-control" name="role-description" value="{{role.label}}">

					<div class="form-check mt-6">
						<input class="form-check-input" type="checkbox" id="checkbox-passthrough" {{checkedIfEq role.passthroughWithoutMatchingRules true}}>
						<label class="form-check-label" for="inlineCheckbox1">Passthrough Without Matching Rules (Standard App Permissions)</label>
					</div>
					
					<div class="form-check mt-6">
						<input class="form-check-input" type="checkbox" id="checkbox-localassign" {{checkedIfEq role.localAdminCanAssign true}}>
						<label class="form-check-label" for="inlineCheckbox1">Local Admin Can Assign</label>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<h3 class="text-center">Assigned Contexts</h3>
					<table class="table">
						<tbody>
							<tr>
								<th>Context</th>
								<th>Action</th>
								<th></th>
							</tr>
		
							{{#each rule in currentRules}}
							<tr>
								<td>{{rule.context}}</td>
								<td>{{rule.action}}</td>
								<td><a href="#" class="btnRemoveContext" data-context="{{rule.context}}">Remove</a></td>
							</tr>
							{{/each}}
						</tbody>
					</table>
				</div>
				<div class="col-6">
					<h3 class="text-center">Available Contexts</h3>
					<select size="15" class="form-control" id="available-contexts" multiple>
						{{#each context in availableContexts}}
						<option value="{{context}}">{{context}}</option>
						{{/each}}
					</select>
					<div class="row mt-6">
						<div class="col-4 offset-2">
							<select class='form-control' id='context-action'>
								<option value="read">Read</option>
								<option value="edit">Write</option>
								<option value="deny">Deny</option>
							</select><br/>
						</div>
						<div class="col-6">
							<button id="btnAddContext" class="btn btn-primary">Add Context</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="row mt-6">
		<div class="col-12 text-center">
			<button class="btn btn-primary" id="btnSave">Save</button>
			<button class="btn btn-secondary" id="btnCancel">Cancel</button>
		</div>
	</div>
</template>