<template name="food">
  <div class="container d-flex flex-row justify-content-end mb-4">
    <div class="mr-4 d-flex flex-column justify-content-end">
      <label>Group:</label>
      <select data-cy="filter-by-group" class="form-control min-w-150px" id="filterGroup">
        <option value="">All</option>
        {{#each groups}}
          <option value="{{_id}}">{{name}}</option>
        {{/each}}
      </select>
    </div>
    <div class="mr-4 d-flex flex-column justify-content-end">
      <label>Start Date:</label>
      <input data-cy="start-date-filter" type="text" class="form-control" id="foodStartDate" value="{{formattedStartDate}}" >
    </div>
    <div class="{{#if userCanAddOrModifyFood}}mr-4{{/if}} d-flex flex-column justify-content-end">
      <label>End Date:</label>
      <input data-cy="end-date-filter" type="text" class="form-control" id="foodEndDate" value="{{formattedEndDate}}" >
    </div>
    {{#if userCanAddOrModifyFood}}
      <div class="d-flex flex-column justify-content-end">
        <div data-cy="add-new-food" class="btn btn-primary font-weight-bolder btn-text-white newFoodLink">
          <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add
        </div>
      </div>
    {{/if}}
  </div>
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#if showFoodFtux food}}
        <div class="card card-custom gutter-b">
          <div class="card-body">
            <div class="d-flex flex-column align-items-center justify-content-center">
              <div data-cy="no-food-message" class="font-size-h3 text-primary font-weight-bolder mb-4">
                Schedule Meals, Save Time.
              </div>
              <div class="mb-4">
                <i class="fad fad-primary icon-3x fa-utensils"></i>
              </div>
              <div class="font-weight-bold text-center mb-12">
                Scheduling food allows you to share menus, make data entry easier, and keep track of meals for reporting purposes.
              </div>
              <div class="d-flex flex-row justify-content-between">
                <div class="d-flex flex-column align-items-center justify-content-start mx-8">
                  <div class="font-size-h4 text-center font-weight-bolder mb-2">
                    1. Add Food Entries Here
                  </div>
                  <div class="font-weight-bold text-center mb-2">
                    Simply click the '+ Add' button to enter details of your scheduled food item
                  </div>
                  <div data-cy="add-food-btn-msg" class="btn btn-primary font-weight-bolder btn-text-white newFoodLink">
                    <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add
                  </div>
                </div>
                <div class="d-flex flex-column align-items-center justify-content-start mx-8">
                  <div class="font-size-h4 text-center font-weight-bolder mb-2">
                    2. Enter Food Moments
                  </div>
                  <div class="font-weight-bold text-center mb-2">
                    When you create a food moment and select a particular meal, the food entry will automatically populate in the moment detail.
                  </div>
                </div>
                <div class="d-flex flex-column align-items-center justify-content-start mx-8">
                  <div class="font-size-h4 text-center font-weight-bolder mb-2">
                    3. Share menus
                  </div>
                  <div class="font-weight-bold text-center mb-2">
                    Food entries are visible on the shared calendar to you, your staff, and families.
                  </div>
                  <div data-cy="visit-calendar-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnVisitCalendar">
                    Visit Calendar
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {{/if}}
      {{#each food}}
        <div class="card card-custom gutter-b" data-id="{{_id}}">
          <div class="card-body">
            <div class="row">
              <div class="d-flex flex-column col-4">
                <a data-cy="food-title" href="/food/{{idToEdit}}" class="font-weight-bolder font-size-h5 text-primary" data-id="{{idToEdit}}">{{meal}}</a>
                <span data-cy="food-description">{{processedDescription}}</span>
              </div>
              <div class="d-flex align-items-center col-3">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    {{#if recurringFrequency}}
                      <i class="icon-2x text-bright-blue fad fa-sync-alt"></i>
                    {{else}}
                      <i class="icon-2x text-bright-blue fad fa-calendar-check"></i>
                    {{/if}}
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Scheduled</span>
                    <span data-cy="food-date" class="font-weight-bolder font-size-h5">{{scheduledDateFormatted}}</span>
                  </div>
                </div>
              </div>
              <div class="d-flex align-items-center col-4">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-users"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Recipient(s)</span>
                    <span data-cy="food-recipients" class="font-weight-bolder font-size-h5">
                      {{#if sendToAll}}
                        All
                      {{else}}
                        {{#if findRecipientGroups}}
                          {{getRecipientGroupList findRecipientGroups}}
                        {{/if}}
                      {{/if}}
                    </span>
                  </div>
                </div>
              </div>
              {{#if userCanAddOrModifyFood}}
              <div class="d-flex justify-content-end align-items-center col-1">
                <div class="d-flex flex-row align-items-center justify-content-end">
                  <div class="dropdown" id="announcement-list-dropdown">
                    <div data-cy="dropdown-options" class="btn btn-icon btn-clean" data-toggle="dropdown" >
                        <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                    </div>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                      <span data-cy="edit-food" class="dropdown-item clickable-row editFoodLink" data-id="{{idToEdit}}" >Edit</span>
                      <span data-cy="delete-food" class="dropdown-item clickable-row deleteFoodLink" data-id="{{idToEdit}}" data-scheduled-date="{{scheduledDate}}">Delete</span>
                    </div>
                  </div>
                </div>
              </div>
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>
