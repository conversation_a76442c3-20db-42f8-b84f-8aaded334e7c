import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from 'moment-timezone';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Groups} from '../../../lib/collections/groups.js';
import _ from '../../../lib/util/underscore';
import { processPermissions } from '../../../lib/permissions';
import './foodFormModal.html';
import './foodDetail.html';
import { offlinePreventCheck } from '../../../mpweb.js';
import { showModal } from '../main.js';

Template.foodDetail.events({
	"click .editFoodLink": function() {
		if (offlinePreventCheck()) return false;
		showModal("foodFormModal", {foodId: this._id}, "#foodFormModal")
	},
	'click .deleteFoodLink': function(event) {
		if (offlinePreventCheck()) return false;
		var foodId = this._id;
		if (!this.recurringFrequency) {
			mpSwal.fire({  
				title: "Are you sure?",   
				text: "You will not be able to recover this food item once deleted!",   
				icon: "warning",   
				showCancelButton: true,   
				confirmButtonText: "Yes, delete it!"
			}).then(async (result) => {   
				if (result.value) await Meteor.callAsync("deleteFood", foodId);
			});
		} else {
			mpSwal.fire({
				title: "Confirm delete",
				text: "This is a recurring event. Do you want to delete the entire series?",
				icon: "warning",
				showCancelButton: true,
				showCloseButton: true,
				cancelButtonText: "Cancel",
				confirmButtonText: "Yes, Delete Series"
			}).then(async (result) => {
				if (result.value) {
					await Meteor.callAsync("deleteFood", foodId);
					FlowRouter.redirect("/food");
				}
			});
		}
	}
});

Template.foodDetail.helpers({
	"formattedDate": function() {
		if (this.recurringFrequency) {
			return "Repeats every " + this.recurringFrequency + " week(s) on " + this.recurringDays.join(", ");
		} 
		else
			return moment(this.scheduledDate).format("MM/DD/YYYY");
	},
	"formatCheckedInOutTime": function(d) {
		return d ? moment(d).format("MM/DD/YYYY h:mm a") : "" ;
	},
	"selectedGroupNames": function() {
		let groupNames = "";

		_.each(this.selectedGroups, (gId)=> { const group = Groups.findOne({_id: gId}); groupNames += (groupNames != "" ? ", " : "") + group.name;});
		return groupNames;
	},
	"name": function() {
		if (this && this.findSelectedPerson) return this.findSelectedPerson().firstName + " " + this.findSelectedPerson().lastName;
	},
	"recurringDescription": function() {
		if (this && this.recurringFrequency) {
			return "Yes";
		} else 
			return "No";
	},
	"showManageFood": function() {
		return processPermissions({
			assertions: [{ context: "food", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type=="staff"
		});
	}
});
