import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from 'moment-timezone';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Groups} from '../../../lib/collections/groups.js';
import { Foods } from '../../../lib/collections/food.js';
import { Orgs } from '../../../lib/collections/orgs.js';
import _ from '../../../lib/util/underscore';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from "../main";
import './foodFormModal.js';
import './food.html';
import { offlinePreventCheck } from '../../../mpweb.js';

Template.food.rendered = function() {
	var self = this;

	$("#foodStartDate").datepicker({ autoclose: true, todayHighlight: true }).on("changeDate", function(e) {
		self.filterStartDate.set(new moment(e.date).format("MM/DD/YYYY"));
	});

	$("#foodEndDate").datepicker({ autoclose: true, todayHighlight: true }).on("changeDate", function(e) {
		self.filterEndDate.set(new moment(e.date).format("MM/DD/YYYY"));
	})
}

Template.food.onCreated(function() {
	this.filterGroup = new ReactiveVar("");
	this.filterStartDate = new ReactiveVar(new moment().format("MM/DD/YYYY"));
	this.filterEndDate = new ReactiveVar(new moment().add(7, "days").format("MM/DD/YYYY"));
	var instance = this;

	instance.autorun(function() {
    const rangeStart = new moment(instance.filterStartDate.get(), "MM/DD/YYYY").format("MM/DD/YYYY"),
      rangeEnd = new moment(instance.filterEndDate.get(), "MM/DD/YYYY").format("MM/DD/YYYY");
      
		instance.subscribe('theFood', {startDate: rangeStart, endDate: rangeEnd});
	});
});

Template.food.events({
	'click .newFoodLink': function(event, template) {
		if (offlinePreventCheck()) return false;
    showModal("foodFormModal", {}, "#foodFormModal");
	},
	'click .editFoodLink': function(event) {
		if (offlinePreventCheck()) return false;
    showModal("foodFormModal", {foodId: $(event.target).attr("data-id")}, "#foodFormModal");
	},
	'click .deleteFoodLink': function(event) {
		if (offlinePreventCheck()) return false;
		var foodId = $(event.target).attr("data-id");
    console.log(this.originalId);
		if (!this.recurringFrequency) {
			mpSwal.fire({  
				title: "Are you sure?",   
				text: "You will not be able to recover this food item once deleted!",   
				icon: "warning",   
				showCancelButton: true,   
				confirmButtonText: "Yes, delete it!"
			}).then(async (result) => {   
				if (result.value) await Meteor.callAsync("deleteFood", foodId);
			});
		} else {
			mpSwal.fire({
				title: "Confirm delete",
				text: "This is a recurring event. Do you want to delete this instance or the series?",
				icon: "warning",
				showCancelButton: true,
				showCloseButton: true,
				cancelButtonText: "Delete Series",
				confirmButtonText: "Delete Instance"
			}).then(async (result) => {
				if (result.dismiss == "cancel") await Meteor.callAsync("deleteFood", this.originalId);
				if (result.value) await Meteor.callAsync("deleteFood", this.originalId, this.scheduledDate);
			});
		}
	},
	"change #filterGroup": function(event, template) {
		if (offlinePreventCheck()) return false;
        template.filterGroup.set( $("#filterGroup").val() );
	},
    "click .headlineLink": function(event) {
    	if (offlinePreventCheck()) return false;
    	Session.set("foodId",  $(event.target).data("id"));
    	$("#foodFormModal").modal();
	},
	"click #btnVisitCalendar": function() {
		FlowRouter.go("/calendar");
	}
});

Template.food.helpers({
	'userCanAddOrModifyFood': function() {
		return processPermissions({
			assertions: [{ context: "food", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type=="staff"
		});
	},
	'food': function() {
		let query = {};
		var filterGroup = Template.instance().filterGroup.get();
		if (filterGroup != "") query["selectedGroups"] = filterGroup;
		const org = Orgs.current();
		const endDateValue = new moment.tz(Template.instance().filterEndDate.get(), "MM/DD/YYYY", org.getTimezone()).endOf('day').valueOf();
		const foods = Foods.findWithRecurrence({
			startDate: new moment(Template.instance().filterStartDate.get(), "MM/DD/YYYY").format("MM/DD/YYYY"), 
			endDateValue,
			query: query, 
			options: {sort: {scheduledDate : 1}}
		});		
		//const foods = Template.instance().foods.get();
		let output =  _.sortBy(foods, (f) => {
			const sortKey= f.scheduledDate + "|" + _.indexOf(["Breakfast", "AM Snack", "Lunch", "PM Snack", "Late Snack", "Dinner"], f.meal);
			f.sortKey = sortKey;
			return sortKey;
		});
		
		return output;
	},
  'getRecipientGroupList': function(recipients) {
    const arr = [];
    _.each(recipients, (r) => {
      arr.push(r.name)
    });
    return arr.join(', ');
  },
	'showFoodFtux': function(food) {
		return (food.length == 0 || FlowRouter.current().path.includes("#ftux"));
	},
	'scheduledDateFormatted': function() {
		return moment(this.scheduledDate).format("YYYY-MM-DD");
	},
	"groups": function() {
         return Groups.find({}, {sort: {name: 1}});
	},
	"idToEdit": function() {
		return this.originalId || this._id;
	},
  "formattedStartDate": function() {
    return new Template.instance().filterStartDate.get();
  },
  "formattedEndDate": function() {
    return new Template.instance().filterEndDate.get();
  },
});
