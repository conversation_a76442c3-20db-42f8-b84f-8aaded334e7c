import { getPeopleData } from "../../services/peopleMeteorService";
import <PERSON> from 'papaparse';
import './reportAttendance.html';
import './reportOrgsField';
import { Groups } from "../../../lib/collections/groups";
import { Orgs } from "../../../lib/collections/orgs";
import _ from "../../../lib/util/underscore";

const dayMap = {"sun": "Su", "mon": "M", "tue": "Tu", "wed": "W", "thu": "Th", "fri": "F", "sat": "Sa"};
Template.reportAttendance.created = function() {
	var instance = this;
	const defaultSortType = (Meteor.user() && Meteor.user().uiOptions && Meteor.user().uiOptions.attendanceReportSort) || "attendeeFirstName";
	this.sortType = new ReactiveVar(defaultSortType);
	this.attendees = new ReactiveVar();
	this.peopleList = new ReactiveVar([]);
	getPeopleData({"type":{"$in":["person", "staff"]}},{sort: {lastName:1, firstName:1}, fields: {_id: 1, lastName:1, firstName:1, inActive: 1}}).then(res => {
		this.peopleList.set(res);
	}).catch(err => {
		console.log(err);
	});
}

Template.reportAttendance.rendered = function() {
	$('#attendanceStartDate').datepicker({autoclose:true});
	$('#attendanceEndDate').datepicker({autoclose:true});
}

Template.reportAttendance.events({
	"click #btnUpdate": async function() {
		$("#btnUpdate").text("Updating").prop('disabled', true);
		var params = {}, instance = Template.instance();
		params.reportStartDate = $("#attendanceStartDate").val();
		params.reportEndDate = $("#attendanceEndDate").val();
		params.personId = $("#filterPerson").val();
		params.groupId = $("#filterGroup").val();
		params.personType = $("#filterType").val();
		params.orgIds = $("#reportOrgs").val();
		const sortType = $("#sortType").val();
		params.sortType = sortType;
		instance.sortType.set(sortType);
		await Meteor.callAsync("setUiOption", "attendanceReportSort", sortType);
		Meteor.callAsync('attendanceReport', params).then((result) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			const data = JSON.parse(result);
			instance.attendees.set(data);
		}).catch((error) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			mpSwal.fire("Error", error.reason, "error");
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "attendance"
		});
	},
	"click #btnExportCsv": async function(e, i) {
		var csvContent = Papa.unparse(_.map(i.attendees.get(), function(o) {
			const elapsedTime = o.elapsedTime;
			let outLine = {
				"Attendee": o.name,
				"Date": o.date,
				"Payer": o.payer,
				"Check-In": o.cInTime || null,
				"Check-Out": o.cOutTime || null,
				"Transportation (Check-In)": o.trans,
				"Transportation (Check-Out)": o.cOutTrans,
				"Elapsed Time": elapsedTime,
				"Elapsed Time (units)": elapsedTime ? (elapsedTime * 60 / 15).toFixed(2) : "",
				"Staff Member": o.staffName,
				"Variance (mins)": o.variance,
				...(o.orgName && { Org: o.orgName }),
			};
			if (Orgs.current().hasCustomization("report/attendance/showCarePlanObjective"))
				outLine["Objective"] = o.carePlan;
			return outLine;
		}));
    	
    	DownloadFile(csvContent, "attendance.csv");
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "attendance"
		});
	}
});

Template.reportAttendance.helpers({
	"formattedStartDate": function(){
		return new moment().add(-1, "days").format("MM/DD/YYYY");
	},
	"formattedEndDate": function(){
		return new moment().format("MM/DD/YYYY");
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.sortStamp).format("hh:mm a");
	},
	"people": function() {
		return Template.instance().peopleList.get();
	},
	"groups": function() {
		return Groups.find({}, {sort: {name: 1}});
	},
	"attendees": function() {
		return _.sortBy(Template.instance().attendees.get(), Template.instance().sortType.get());
	},
	"currentSortType": function() {
		return (Meteor.user() && Meteor.user().uiOptions && Meteor.user().uiOptions.attendanceReportSort) || "attendeeFirstName";
	},
	"showStaffMember": function() {
		return Orgs.current().language == "translationsEnAdultCare";
	},
	"showElapsedTimeUnits": function() {
		return Orgs.current().language == "translationsEnAdultCare";
	},
	"showObjectiveField": function() {
		return Orgs.current().hasCustomization("report/attendance/showCarePlanObjective");
	},
	"showOrg": function() {
		const attendees = Template.instance().attendees.get();
		return attendees && attendees.length > 0 && attendees[0].orgName;
	},
	getElapsedTimeUnits(elapsedTime) {
		return elapsedTime ? (elapsedTime * 60 / 15).toFixed(2) : ""
	}
});
