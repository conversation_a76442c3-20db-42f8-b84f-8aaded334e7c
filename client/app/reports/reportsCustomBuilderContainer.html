<template name="reportsCustomBuilderContainer">

	{{#if reportId }}
		{{#if fieldsSelected }}
			<div class="container-fluid">
				{{> reportCustomReportFlex savedReport=savedReport reportId=reportId fields=fields filters=filters}}
			</div>
		{{/if}}
	{{else}}
	<div class="container-fluid" id="dashboard">
		<h1 class="mb-4">Step 1 - Select the type of data you want: </h1>
		<div class="row">
			<div class="col-lg-3">
				<div class="form-group">
					<label>Org(s):</label><br/>
					{{> reportOrgsField opts=orgsFieldOptions }}
				</div>
			</div>
		</div>
		
		<div class="row">
			<!--begin::Col-->
			{{#each report in availableReports}}
			<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
				<!--begin::Card-->
				<div class="card card-custom gutter-b card-stretch">
					<!--begin::Body-->
					<div class="d-flex flex-column card-body pt-4 justify-content-between">
						<!--begin::User-->
						<div class="d-flex align-items-center mb-7">
							<!--begin::Pic-->
							<div class="flex-shrink-0 mr-4">
								
								<i class="fa fa-chart-bar fa-lg"></i>
							</div>
							<!--end::Pic-->
							<!--begin::Title-->
							<div class="d-flex flex-column">
								<a href="/reports-custom-builder/{{report._id}}" class="text-dark font-weight-bold text-hover-primary font-size-h4 mb-0">{{report.name}}</a>
							</div>
							<!--end::Title-->
						</div>
						<!--end::User-->
						<!--begin::Desc-->
						<p class="mb-7">{{report.description}}</p>
						<!--end::Desc-->
						<a href="/reports-custom-builder/{{report._id}}" data-cy="custom-{{report.index}}" class="btn btn-block btn-sm btn-light-primary font-weight-bolder text-uppercase py-4">View Data</a>
					</div>
					<!--end::Body-->
				</div>
				<!--end:: Card-->
			</div>
			{{/each}}
			<!--end::Col-->
		</div>
	</div>
	{{/if}}

  </template>
