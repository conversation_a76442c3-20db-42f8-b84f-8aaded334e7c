<template name="reportClassList">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Enrollment Forecast Report
					<span class="text-muted pt-2 font-size-sm d-block">Center level details for FTE Enrollments.</span>
				</h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				{{#unless isSavedMode }}
					<div data-cy="enrollment-forecast-report-update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
						{{updateLabel}}
					</div>
				{{/unless}}
			</div>
		</div>
		<div class="card-body">
			<div class="box">

				<div class="box-body">
					{{#if isSavedMode}}
						<div class="row font-weight-bolder text-dark">
							<div class="col">
								{{{ savedHeader }}}
							</div>
						</div>
					{{/if}}
					{{#unless isSavedMode}}
						<div class="row">
							<div class="col-2">
								<div class="form-group">
									<label>Start Date</label>
									<div class="input-group">
										<input type="text" class="form-control pull-right" id="reportStartMonth"
											   value="{{formattedStartMonth}}">
									</div>
								</div>
							</div>
							<div class="col-2">
								<div class="form-group">
									<label>Group</label>
									<select class="form-control" id="selectedGroupId">
										<option value=""></option>
										{{#each availableGroups}}
											<option value="{{_id}}">{{name}}</option>
										{{/each}}
									</select>
								</div>
							</div>
							<div class="col-3">
								<div class="form-group">
									<label>Age Range (months)</label>
									<div class="d-flex flex-row align-items-center">
										<div class="mr-4">
											<input type="text" class="form-control" id="ageRangeStart">
										</div>
										<div class="mr-4 text-center align-middle">
											to
										</div>
										<div class="">
											<input type="text" class="form-control" id="ageRangeEnd">
										</div>
									</div>
								</div>
							</div>
							<div class="col-2 form-group mt-8">

								<div class="checkbox-list">
									<label class="checkbox">
										<input type="checkbox" value="includeChildDetails" id='includeChildDetails'>
										<span></span> Include Child Details
									</label>
								</div>
							</div>
							<div class="col-3 text-right">
								<b>Jump to:</b><br/>
								<a href="/reports/reportClassListSchedule" target="_blank">Capacity Planning Report</a>
							</div>
						</div>
						<div class="row">
							{{#if showOrgSelection}}
								<div class="col-2">
									<div class="form-group">
										<label>Select Orgs</label>
										{{> announcementOrgsField }}
									</div>
								</div>
									<div class="col-2 offset-2 form-group pt-4">
										{{> reportQueueCheckbox }}
									</div>
							{{/if}}
						</div>
					{{/unless}}
				</div>
				<div class="box-body no-padding" id="dvData">
					{{#unless isMultiSite}}
						{{#if groups}}
							<div class="row d-flex align-content-center justify-content-center">
								<h3>{{orgName}}</h3>
							</div>
							<table class="table  fix-head-table" id="moment-report-table" style="display:block;overflow-x:auto;">
					<tbody>
					
					{{#each groups}}
						{{#if showHeader @index}}
							<tr>
							{{#unless displayChildDetails}}
								<th class="text-center align-bottom">Group</th>
							{{/unless}}
						
								<th class="text-center align-bottom">Max Capacity</th>
								<th class="text-center align-bottom">Preferred Capacity</th>
								<th class="text-center align-bottom">Ratio</th>
								<th class="text-center align-bottom">Age Range</th>
								<th class="text-center align-bottom">FTE (current)</th>
								<th class="text-center align-bottom">Variance</th>
								<th class="text-center align-bottom">Occupancy Percentage</th>
								<th class="text-center align-bottom">FTE (+1 Month)</th>
								<th class="text-center align-bottom">Goal: FTE (+1 Month)</th>
								<th class="text-center align-bottom">Goal Percentage</th>
								<th class="text-center align-bottom">Recurring Tuition</th>
								<th class="text-center align-bottom">Recurring Discounts</th>
								<th class="text-center align-bottom">FTE (+2 Month)</th>
								<th class="text-center align-bottom">FTE (+3 Month)</th>
								<th class="text-center align-bottom">FTE (+4 Month)</th>
								<th class="text-center align-bottom">Loaded Monthly Payroll</th>
								<th class="text-center align-bottom">Payroll Ratio</th>
								<th></th>
							</tr>
						{{/if}}
					{{#if displayChildDetails}}						
						<tr style="border-top:2px solid #000">
							<td class='no-wrap' style="font-weight:bold;" colspan=50>{{name}}</td>
						</tr>
					{{/if}}
					<tr>
					  {{#unless displayChildDetails}}
						<td style="font-weight:bold;"><span class="text-nowrap">{{name}}</span></td>
					  {{/unless}}
					  <td  class="text-right">{{capacity}}</td>
					  <td  class="text-right">{{preferredCapacity}}</td>
											<td class="text-right">{{ratioLabel ratio}}</td>
											<td class="text-right">{{ageGroupLabel ageGroup}}</td>
											<td class="text-right" style="font-weight:bold;">{{currentFte}}</td>
											<td class="text-right" style="{{getVarianceTextClass preferredCapacity currentFte}}">{{#if hasNumberValue currentVariance}}{{currentVariance}}{{/if}}</td>
											<td class='no-wrap text-right'>{{#if hasNumberValue occupancyPercentage}}{{occupancyPercentage}}%{{/if}}</td>
											<td class='no-wrap text-right'>{{currentFtePlusOne}}</td>
											<td class='no-wrap text-right'>{{enrollmentGoal}}</td>
											<td class='no-wrap text-right'>{{enrollmentGoalPercentage}}%</td>
											<td class='no-wrap text-right'>{{formatCurrency memorizedRate}}</td>
											<td class='no-wrap text-right'>({{formatCurrency discounts}})</td>
											<td class='no-wrap text-right'>{{currentFtePlusTwo}}</td>
											<td class='no-wrap text-right'>{{currentFtePlusThree}}</td>
											<td class='no-wrap text-right'>{{currentFtePlusFour}}</td>
											<td class='no-wrap text-right'>{{formatCurrency monthlyLoadedPayroll}}</td>
											<td class='no-wrap text-right'>{{payrollRatio}}%</td>
					</tr>
										{{#if displayChildDetails}}
											{{#each childDetails}}
											<tr>
												<td colspan=2 class="text-right">{{firstName}} {{lastName}} {{#if inActive}}<span style="color:#ff0000"> (i)</span>{{/if}}</td>
												<td colspan=2 class="text-right">{{age}}</td>

												<td class="text-right">{{currentFte}}</td>
												<td colspan=2 class="text-right">
													{{#if withdrawalDate}}Withdraws: {{formatDate withdrawalDate "MM/DD/YYYY"}}{{/if}}
													{{#if enrollmentDate}}Starts: {{formatDate enrollmentDate "MM/DD/YYYY"}}{{/if}}
												</td>
												<td colspan=50>
													{{#if showScheduleLabel}}{{scheduleLabel}}{{/if}}
												</td>
											</tr>
											{{/each}}
										{{/if}}
					{{/each}}
										<tr>
											{{#unless displayChildDetails}}
												<td class='no-wrap' style='font-weight:bold'>Totals</td>
											{{/unless}}
											<td data-cy="enrollment-forecast-totals-max-capacity" class="text-right" >{{totals.maxCapacity}}</td>
											<td class="text-right">{{totals.preferredCapacity}}</td>
											<td class='no-wrap'></td>
											<td class='no-wrap'></td>
											<td class="text-right" style="font-weight:bold;">{{totals.currentFte}}</td>
											<td class="text-right" style="{{getVarianceTextClass totals.preferredCapacity totals.currentFte}}">{{totals.currentVariance}}</td>
											<td class='no-wrap text-right'>{{#if hasNumberValue totals.occupancyPercentage}}{{totals.occupancyPercentage}}%{{/if}}</td>
											<td class='no-wrap text-right'>{{totals.currentFtePlusOne}}</td>
											<td class='no-wrap'></td>
											<td class='no-wrap'></td>
											<td class='no-wrap text-right'>{{formatCurrency totals.memorizedRate}}</td>
											<td class='no-wrap text-right'>({{formatCurrency totals.discounts}})</td>
											<td class='no-wrap text-right'>{{totals.currentFtePlusTwo}}</td>
											<td class='no-wrap text-right'>{{totals.currentFtePlusThree}}</td>
											<td class='no-wrap text-right'>{{totals.currentFtePlusFour}}</td>
											<td class='no-wrap text-right'>{{formatCurrency totals.monthlyLoadedPayroll}}</td>
											<td class='no-wrap text-right'>{{totals.payrollRatio}}%</td>
										</tr>

					</tbody>
							</table>
						{{/if}}
					{{/unless}}
					{{#if isMultiSite}}
						<table class="table  fix-head-table" id="moment-report-table" style="display:block;overflow-x:auto;">
							<tbody>
							<tr>
								<th class="text-center align-bottom">Org</th>
								<th class="text-center align-bottom">Max Capacity</th>
								<th class="text-center align-bottom">Preferred Capacity</th>
								<th class="text-center align-bottom">FTE (current)</th>
								<th class="text-center align-bottom">Variance</th>
								<th class="text-center align-bottom">Occupancy Percentage</th>
								<th class="text-center align-bottom">FTE (+1 Month)</th>
								<th class="text-center align-bottom">Recurring Tuition</th>
								<th class="text-center align-bottom">Recurring Discounts</th>
								<th class="text-center align-bottom">FTE (+2 Month)</th>
								<th class="text-center align-bottom">FTE (+3 Month)</th>
								<th class="text-center align-bottom">FTE (+4 Month)</th>
								<th class="text-center align-bottom">Loaded Monthly Payroll</th>
								<th class="text-center align-bottom">Payroll Ratio</th>
								<th></th>
							</tr>
							{{#each orgs}}
							<tr>
								<td class='no-wrap' style='font-weight:bold'>{{orgName @index}}</td>
							{{#with totals @index}}
								<td class="text-right">{{maxCapacity}}</td>
								<td class="text-right">{{preferredCapacity}}</td>
								<td class="text-right" style="font-weight:bold;">{{currentFte }}</td>
								<td class="text-right" style="{{getVarianceTextClass totals.preferredCapacity
																					 totals.currentFte}}">{{currentVariance}}</td>
								<td class='no-wrap text-right'>{{#if hasNumberValue occupancyPercentage}}{{occupancyPercentage}}%{{/if}}</td>
								<td class='no-wrap text-right'>{{currentFtePlusOne}}</td>
								<td class='no-wrap text-right'>{{formatCurrency memorizedRate}}</td>
								<td class='no-wrap text-right'>({{formatCurrency discounts}})</td>
								<td class='no-wrap text-right'>{{currentFtePlusTwo}}</td>
								<td class='no-wrap text-right'>{{currentFtePlusThree}}</td>
								<td class='no-wrap text-right'>{{currentFtePlusFour}}</td>
								<td class='no-wrap text-right'>{{formatCurrency monthlyLoadedPayroll}}</td>
								<td class='no-wrap text-right'>{{payrollRatio}}%</td>
							{{/with}}
							</tr>
							{{/each}}
							</tbody>
						</table>
					{{/if}}
				</div>
			</div>
		</div>
	</div>
</template>
