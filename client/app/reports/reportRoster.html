<template name="reportRoster">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Contact Roster
				<span class="text-muted pt-2 font-size-sm d-block">A printable list of contacts for individuals.</span></h3>
			</div>
			{{#unless shouldHideHeaderForPrint}}
				<div class="card-toolbar">
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Print View
					</div>
					<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
						Update
					</div>
				</div>
			{{/unless}}
		</div>
		<div class="card-body">
			<div class="box">

				<div class="box-body roster-report {{#if singlePageMode}}page-break{{/if}}">
					  
					<div class="row" id="dvData">
						<div class="col-12">
							{{#if fullLayout}}
							<div class="row">
								<div class="col-sm-4">
									<label>Group:</label><br/>
									<select data-cy="select-group" id="selectedGroup" class='form-control'>
										<option value="">All</option>
										{{#each group in groups}}
										<option value="{{group._id}}">{{group.name}}</option>
										{{/each}}
									</select>
								</div>
								<div class="col-sm-8">
									<div class="checkbox-inline">
                                        <label class="checkbox checkbox-primary">
                                            <input data-cy="page-per-family" type="checkbox" name="page-per-family">
                                            <span></span>
                                            Print single page per family
                                        </label>
                                        <label class="checkbox checkbox-primary">
                                            <input data-cy="include-inactive-people" type="checkbox" name="include-inactive">
                                            <span></span>
                                            Include inactive people
                                        </label>
                                        <label class="checkbox checkbox-primary">
                                            <input data-cy="include-child-avatars" type="checkbox" name="include-child-avatars">
                                            <span></span>
                                            Include child profile photos
                                        </label>
									</div>
								</div>
							</div>
							<br/>
							
							{{/if}}

							{{#if reportRows}}
							<div id="reportData">
								{{#if reportTitle}}<h3 style="padding-left:5px">{{reportTitle}}</h3>{{/if}}
								
									{{#each person in reportRows.people}}
									<div class="contact-container">
										<table class="table ">
											<tbody>
											<tr class="header-row">
												<td colspan=3 class="font-weight-boldest">
                                                    <div class="row">
                                                        {{# if showAvatars }}
                                                            <div data-cy="person-avatar" class="col-auto">{{> _personAvatarPhoto person=person height=100 width=100 }}</div>
                                                        {{/ if }}
                                                        <div class="col">
                                                            <div class="row">
                                                                <div data-cy="person-name" class="ml-3">{{ person.lastName }}, {{ person.firstName }}</div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-12 d-flex justify-content-around mt-3 mb-5">
                                                                    <div>Date of birth:
                                                                        <span data-cy="date-of-birth" class="font-weight-bolder">
                                                                            {{ formatDate (getBirthdayPropertyValue person) 'MM/DD/YYYY' }}
                                                                        </span>
                                                                    </div>
                                                                    <div>Allergies:
                                                                        <span data-cy="allergies" class="font-weight-bolder">
                                                                            {{getStandardOutlookData person "allergies"}}
                                                                        </span>
                                                                    </div>
                                                                    <div>Important Notes:
                                                                        <span data-cy="important-notes" class="font-weight-bolder">
                                                                            {{getStandardOutlookData person "importantNotes"}}
                                                                        </span>
                                                                    </div>
                                                                    <div>Special Needs:
                                                                        <span data-cy="special-needs" class="font-weight-bolder">
                                                                            {{getStandardOutlookData person "specialNeeds"}}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
												</td>
											</tr>
											{{#each contact in person.contacts}}
											<tr>
												<td width="10%"></td>
												<td data-cy="family-name" width="35%">
													{{contact.firstName}} {{contact.lastName}}<br/>
													{{contact.relationshipType}}{{#if contact.relationshipDescription}} ({{contact.relationshipDescription}}){{/if}}
												</td>
												<td data-cy="family-contact" width="55%">
													{{#each method in contact.availableContactMethods}}
													{{method.description}}: {{method.value}}<br/>
													{{/each}}
													{{#if contact.getEmailAddress}}
													Email: {{contact.getEmailAddress}}
													{{/if}}
												</td>
											</tr>
											
											{{/each}}
											</tbody>
										</table>
									</div>
									{{/each}}
									
							</div>
							{{/if}}
						</div>
					</div>
							
				</div>
			</div>
		</div>
	</div>


</template>
