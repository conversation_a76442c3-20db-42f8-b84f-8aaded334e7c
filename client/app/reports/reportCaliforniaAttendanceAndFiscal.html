<template name="reportCaliforniaAttendanceAndFiscal">

	<div class="row">
		<div class="col-md-12">
			<div class="box">
				<div class="box-header">
				  <h3 class="box-title">California Attendance and Fiscal Report</h3>
				</div>
				<div class="box-body">
					  <div class="row">
						<div class="col-lg-4">
							<div class="form-group">
								<label>Date Range</label>
								<div class="input-group input-daterange">
									<input type="text" class="form-control" id="attendanceStartDate" value="">
									<div class="input-group-addon" >
										to
									</div>
									<input type="text" class="form-control" id="attendanceEndDate" value="">
								</div>
							</div>
						</div>
						
					  </div>
				
					 <div class="row">
						<div class="col-lg-4">
							<div class="form-group">
								<br/>
								<button type="button" class="btn btn-primary" id="update">Update</button>
								<button type="button" class="btn" id="btnExportCsv"><i class="fa fa-download"></i> Export CSV</button>
							</div>
						</div>					
					  </div>
				</div>
			
				{{#if (sections "certified")}}
				<div class="box-body no-padding">
					<table class="table" id="californiaAttendanceFiscalTable" style="overflow-x:auto;display:block">
						<tbody>
							<tr>  
								<th>Category</th>
								<th>Count</th>
							</tr>
							<tr>
								<th colspan="2">Certified Children</th>
							</tr>
							{{#each row in (sections "certified")}}
							<tr>
								<td>{{row.sectionName}}</td>
								<td>{{row.count}}</td>
							</tr>
							{{/each}}
							<tr>
								<th colspan="2">Non-certified Children</th>
							</tr>
							{{#each row in (sections "noncertified")}}
							<tr>
								<td>{{row.sectionName}}</td>
								<td>{{row.count}}</td>
							</tr>
							{{/each}}
						</tbody>
					</table>
				</div>
				{{/if}}
			</div>
		</div>
	</div>
</template>
