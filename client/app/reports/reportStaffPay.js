import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './reportQueueTemplate';
import './reportStaffPay.html';
import { reportQueuedSwal } from './queueReportsUtil';

Template.reportStaffPay.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.includeAdmins = new ReactiveVar(false);
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	this.isUpdating = new ReactiveVar(false);
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId }).then((response) => {
			if (!response) {
				return;
			}
			this.savedMode.set(true);
			this.reportData.set(response.data);
			const reportArgs = response.args;
			let header;
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgs }).then((resp) => {
				header = 'Orgs: ' + resp;
			}).catch((err) => {
				header = 'Orgs: ';
			}).finally(() => {
				header += '<br>Include Admins: ' + (reportArgs.includeAdmins ? 'Yes' : 'No');
				this.savedHeader.set(header);
			});
		}).catch((err) => {
			return;
		});
	}
}

Template.reportStaffPay.rendered = function() {
}

Template.reportStaffPay.events({
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
	"click #btnUpdate": async function(e, i) {
		document.getElementById('btnUpdate').disabled = true;
		i.isUpdating.set(true);

		const includeAdmins = document.getElementById('includeAdmins').checked;

		var orgs = $("#announcementOrgs").val() || [];

		if (i.queueMode.get()) {
			i.isUpdating.set(false);
			document.getElementById('btnUpdate').disabled = false;
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'staffPayData',
				reportName: 'Staff Pay',
				reportArgs: { orgs, includeAdmins },
				userId: Meteor.userId(),
				reportRoute: 'reports/reportStaffPay'
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "staffPay"
			});
			reportQueuedSwal();
			return;
		}

		Meteor.callAsync("staffPayData", { orgs, includeAdmins }).then((response) => {
			i.isUpdating.set(false);
			document.getElementById('btnUpdate').disabled = false;
			i.reportData.set(response);
		}).catch((err) => {
			i.isUpdating.set(false);
			document.getElementById('btnUpdate').disabled = false;
			i.reportData.set(undefined);
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "staffPay"
		});
	},
	"click #btnPrint": async function (e,i) {
		var outputFile = 'export.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "staffPay"
		});
	},
});

Template.reportStaffPay.helpers({
	showOrgSelection() {
		const user = Meteor.user();
		const userPerson = (user) ? user.fetchPerson() : {};
		return userPerson.isMasterAdmin();
	},
	reportRows() {
		const data = Template.instance().reportData.get();
		return data;
	},
	updateLabel() {
		return Template.instance().isUpdating.get() ? 'Updating' : (Template.instance().queueMode.get() ? 'Queue' : 'Update');
	},
	isQueueMode() {
		return Template.instance().queueMode.get();
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
});
