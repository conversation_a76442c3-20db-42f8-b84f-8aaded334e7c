<template name="reportGroupEnrollments">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Enrollments By Group
				<span class="text-muted pt-2 font-size-sm d-block">View total enrollments by group.</span></h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
							<div class="row">
								<div class="col-4">
									<label>Organize by:</label>
									<div class="radio-list">
										<label class="radio radio-primary">
											<input data-cy="current-default-group" type="radio" name="group-type" value="default-group" checked="checked">
											<span></span>
											Current Default Group
										</label>
										<label class="radio radio-primary">
											<input data-cy="historical-schedule-checkbox" type="radio" name="group-type" value="historical-schedule">
											<span></span>
											Historical Date (using schedules):

										</label>
										
										
									</div>
									<input data-cy="historical-date-input" class="form-control" type="date" name="historical-date" style="display:inline;margin-left:25px;width:unset">
								</div>
								<div class="col-4 form-group">
									<div class="checkbox-list">
										<label class="checkbox"><input data-cy="show-names-by-group" type="checkbox" id="chkShowNames"><span></span> Show names by group</label>
									</div>
								</div>
							</div>
							
							<br/>
						
							{{#if reportRows}}
							<h4 class="text-center">Enrollments By Group Report</h4>
							<h5 class="text-center">{{dateLabel}}</h5>
							<br/>
							<table class="table">
								<tr style="border-bottom: 1px solid #000 ">
									<th>Group</th>
									<th style="text-align:right">Count</th>
								</tr>
								{{#each row in reportRows}}
									<tr >
										<td data-cy="group-name">{{row.label}}</td>
										<td data-cy="group-count" style="text-align:right">{{row.count}}</td>
									</tr>
									{{#if showNames}}
										{{#each person in row.people}}
											<tr>
												<td data-cy="person-name" style="padding-left:50px"><a href="/people/{{person._id}}#reservations" target="_blank">{{person.firstName}} {{person.lastName}}</a></td>
												<td></td>
											</tr>
										{{/each}}
									{{/if}}
								{{/each}}
								<tr style="border-top:1px solid #000;border-bottom: 1px solid #000 ">
									<td style="font-weight:bold">Total</td>
									<td data-cy="total-enrollments" style="text-align:right">{{totals}}</td>
								</tr>
							</table>
							{{/if}}
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</template>
