import { Orgs } from '../../../lib/collections/orgs';
import './reportScheduleTypesField.html';

Template.reportScheduleTypesField.helpers({
	"getEmptyOption": function () {
		return Template.instance().emptyOption;
	},
	"getScheduleTypes": function () {
		return Orgs.current().getScheduleTypes();
	},
});

Template.reportScheduleTypesField.onCreated(function () {
	$("#scheduleTypeMultiSelector").multiselect("rebuild");
	const data = Template.instance().data;
	if (data?.emptyOptionLabel) {
		Template.instance().emptyOption = {
			label: data.emptyOptionLabel,
			value: data.emptyOptionValue || ""
		}
	}
});

Template.reportScheduleTypesField.onRendered(function () {
	const opts = Template.instance().data.opts;
	$("#scheduleTypeMultiSelector").multiselect({
		includeSelectAllOption: true,
		enableFiltering: true,
		maxHeight: 300,
		nonSelectedText: "All",
		buttonContainer: '<div class="btn-group w-100" />',
		buttonClass: 'custom-select w-100 text-left',
		onChange: function (option, checked, select) {
			if (opts?.onChange) {
				opts.onChange($("#scheduleTypeMultiSelector").val());
			}
		},
		onSelectAll: function () { if (opts?.onChange) opts.onChange($("#scheduleTypeMultiSelector").val()); },
		onDeselectAll: function () { if (opts?.onChange) opts.onChange($("#scheduleTypeMultiSelector").val()); },
	});
	$("#scheduleTypeMultiSelector").multiselect("rebuild");
});
