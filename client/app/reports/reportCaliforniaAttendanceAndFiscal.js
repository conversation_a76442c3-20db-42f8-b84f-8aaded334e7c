import './reportCaliforniaAttendanceAndFiscal.html';

const dayMap = { "mon": "M", "tue": "Tu", "wed": "W", "thu": "Th", "fri": "F"},
	allCategories = {
		"certified": [ 
			"Three years and older",
			"Exceptional Needs",
			"Limited and non-English proficient",
			"Children at risk of abuse or neglect",
			"Severely handicapped"
		],
		"noncertified": [
			"Toddlers",
			"Three years and older",
			"Exceptional Needs",
			"Limited and non-English proficient",
			"Children at risk of abuse or neglect",
			"Severely handicapped"
		]},
	categoryModifiers = [
		"Full time plus",
		"Full time",
		"Three-quarter time",
		"One-half time"
	];

Template.reportCaliforniaAttendanceAndFiscal.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.startDate = new ReactiveVar();
	this.endDate = new ReactiveVar();
}

Template.reportCaliforniaAttendanceAndFiscal.rendered = function() {
	//$('#attendanceStartDate').datepicker({autoclose:true});
	//$('#attendanceEndDate').datepicker({autoclose:true});
	$('.input-daterange').datepicker({autoclose:true});
}

Template.reportCaliforniaAttendanceAndFiscal.events({
	"click button#update": function() {
		var instance = Template.instance();
		const startDate = $("#attendanceStartDate").val(), 
			endDate = $("#attendanceEndDate").val();
		instance.startDate.set(startDate);
		instance.endDate.set(endDate);
		if (!moment(startDate).isValid() || !moment(endDate).isValid())
			return swal("Error", "You must supply a valid date range for this report.", "error");
		const options = {
			startDate,
			endDate,
			includeAll: true,
			summarize: true
		};
		Meteor.callAsync("attendanceReportCalifornia", options).then((result) => {
			console.log("result", result);
			instance.reportData.set(result);
		}).catch((err) => {
			console.error("Error", err);
			instance.reportData.set(result);
		});
	},
	"click #btnExportCsv": function(event, instance) {
		var outputFile = 'attendancefiscal.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#californiaAttendanceFiscalTable'), outputFile]);
	}
});



Template.reportCaliforniaAttendanceAndFiscal.helpers({
	"sections": function(certifiedStatus) {
		const data = Template.instance().reportData.get(),
			result = [];
		if (!data) return;

		_.chain(allCategories[certifiedStatus]).each( k => {
			_.each(categoryModifiers, modifier => {
				const categoryLabel = k + " " + modifier,
					sectionValue = data.categorySummaries[categoryLabel];
				result.push( {
					sectionName: categoryLabel,
					count: sectionValue && sectionValue[certifiedStatus]
				});
			});
		});
		return result;
	}
});