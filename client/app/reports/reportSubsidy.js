import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './reportQueueTemplate';
import './reportSubsidy.html';
import { reportQueuedSwal } from './queueReportsUtil';

Template.reportSubsidy.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.dateRangeLabel = new ReactiveVar();
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId }).then((response) => {
			if (!response) {
				return;
			}
			this.savedMode.set(true);
			this.reportData.set(response.data);
			const reportArgs = response.args;
			const startDate = new moment(reportArgs.startDate).format("MM/DD/YYYY");
			const endDate = new moment(reportArgs.endDate).format("MM/DD/YYYY");
			this.dateRangeLabel.set(  startDate + " - " + endDate);
			let header;
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgs }).then((resp) => {
				header = 'Orgs: ' + resp;
				this.savedHeader.set(header);
			}).catch((err) => {
				header = 'Orgs: ';
				this.savedHeader.set(header);
			});
		}).catch((err) => {
			return;
		});
	}
}

Template.reportSubsidy.rendered = function() {
	$('#reportStartDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
	$('#reportEndDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
}

Template.reportSubsidy.events({
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
	"click #btnUpdate": async function(e, i) {
		const startDate = $("#reportStartDate").val(),
			endDate = $("#reportEndDate").val();
			
		if (startDate == "" || endDate == "") return;		
		const startDateNum = new moment(startDate, "MM/DD/YYYY").valueOf(),
			endDateNum = new moment(endDate, "MM/DD/YYYY").valueOf();
		$("#btnUpdate").prop('disabled', true);
		
		var orgs = $("#announcementOrgs").val() || [];
		if (i.queueMode.get()) {
			$("#btnUpdate").prop('disabled', false);
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'subsidyReport',
				reportName: 'Subsidy',
				reportArgs: { startDate: startDateNum, endDate: endDateNum, orgs },
				userId: Meteor.userId(),
				reportRoute: 'reports/reportSubsidy'
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "subsidy"
			});
			reportQueuedSwal();
			return;
		}
		Meteor.callAsync("subsidyReport", { startDate: startDateNum, endDate: endDateNum, orgs }).then((response) => {
			$("#btnUpdate").prop('disabled', false);
			i.reportData.set(response);		
			i.dateRangeLabel.set(  startDate + " - " + endDate);
		}).catch((err) => {
			$("#btnUpdate").prop('disabled', false);
			i.reportData.set(response);	
			i.dateRangeLabel.set(  startDate + " - " + endDate);
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "subsidy"
		});
	},
	"click #btnPrint": async function (e,i) {
		var outputFile = 'export.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "subsidy"
		});
	}
});

Template.reportSubsidy.helpers({
	showOrgSelection() {
		const user = Meteor.user();
		const userPerson = (user) ? user.fetchPerson() : {};
		return userPerson.isMasterAdmin();
	},
	updateLabel() {
		return Template.instance().queueMode.get() ? 'Queue' : 'Update';
	},
	isQueueMode() {
		return Template.instance().queueMode.get();
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
	reportRows() {
		const data = Template.instance().reportData.get();
		return data && data.rows;
	},
  isFamily(type) {
    return type && type.toLowerCase() == "family";
  },
  getProfileValue(data, fieldName, date = "false") {
    if (data && data[fieldName] && date == "date") return new moment(data[fieldName]).format("MM/DD/YYYY");
    if (data && data[fieldName]) return data[fieldName];
  },
	dateRangeLabel() {
		return Template.instance().dateRangeLabel.get();
	},
	totals() {
		const data = Template.instance().reportData.get();
		return data && data.totals;
	},
})
