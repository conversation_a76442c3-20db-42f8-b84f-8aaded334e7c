import { Groups } from '../../../lib/collections/groups';
import './reportImmunizations.html';

Template.reportImmunizations.onCreated( function() {
	const instance = this;

	instance.reportData = new ReactiveVar();
});

Template.reportImmunizations.events( {
	"click #btnPrint": async function(e, instance) {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "immunizations"
		});
	},
	"click #btnUpdate": async function() {
		const instance = Template.instance(),
			selectedGroup = $("#selectedGroup").val(),
			showOverdueOnly = $("#chkShowOverdueOnly").prop("checked");
		
		const options = { selectedGroupId: selectedGroup, showOverdueOnly };

		$("#btnUpdate").text("Updating").prop('disabled', true);

		Meteor.callAsync("immunizationsDueReport", options).then((result) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			instance.reportData.set(result);
		}).catch((err) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			instance.reportData.set(result);
		});

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "immunizations"
		});
	}
});

Template.reportImmunizations.helpers( {
	"reportRows"() {
		const reportData = Template.instance().reportData.get();
		return _.sortBy(reportData, r => r.name + "|" + r.immunizationType);
	},
	"groups"() {
		return Groups.find({}, {sort:{name:1}});
	}
});