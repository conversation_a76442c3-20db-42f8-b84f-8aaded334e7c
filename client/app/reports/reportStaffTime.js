import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import currency from 'currency.js';
import { getPeopleData } from '../../services/peopleMeteorService';
import './reportOrgsField';
import './reportStaffTime.html';
import { Orgs } from '../../../lib/collections/orgs';
import { processPermissions } from '../../../lib/permissions';
import { reportQueuedSwal } from './queueReportsUtil';

Template.reportStaffTime.created = function () {
	const instance = this;
	this.reportData = new ReactiveVar();
	this.showProjectedOverTime = new ReactiveVar();
	this.dateRangeLabel = new ReactiveVar();
	this.firstWeekLabel = new ReactiveVar();
	this.secondWeekLabel = new ReactiveVar();
	this.showDetails = new ReactiveVar(false);
	this.showPayDetails = new ReactiveVar(false);
	this.groupByStaff = new ReactiveVar(false);
	this.payCorpToggle = new ReactiveVar(false);
	this.isCrossSite = new ReactiveVar(false);
	this.peopleData = new ReactiveVar([]);

    this.queueMode = new ReactiveVar(false);
    this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	this.isUpdating = new ReactiveVar(false);
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId }).then((response) => {
			if (!response) {
				return;
			}
			this.savedMode.set(true);
			this.reportData.set(response.data);
			const reportArgs = response.args;
			const startDate = new moment(reportArgs.startDate).format("MM/DD/YYYY");
			const endDate = new moment(reportArgs.endDate).format("MM/DD/YYYY");
			let header = 'Date range: ' + startDate + ' - ' + endDate + '<br>';
			header += 'Specified Person: ' + (reportArgs.specifiedPersonName ?? '') + '<br>';
			header += 'Include Detail: ' + (reportArgs.includeDetail ? 'Yes' : 'No') + '<br>';
			header += 'Include Pay Detail: ' + (reportArgs.includePayDetail ? 'Yes' : 'No') + '<br>';
			header += 'Group By Staff: ' + (reportArgs.groupByStaff ? 'Yes' : 'No') + '<br>';
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgIds }).then((resp) => {
				header += 'Orgs: ' + resp + '<br>';
				this.savedHeader.set(header);
			}).catch((err) => {
				header += 'Orgs: <br>';
				this.savedHeader.set(header);
			});
			const startDateValue = new moment(reportArgs.startDate).valueOf();
			const endDateValue = new moment(reportArgs.endDate).valueOf();
			instance.showProjectedOverTime.set(reportArgs.showProjectedOverTime);
			instance.dateRangeLabel.set(`${new moment(startDateValue).format("MM/DD/YYYY")} (${new moment(startDateValue).format('ddd')}) -  ${new moment(endDateValue).format("MM/DD/YYYY")} (${new moment(endDateValue).format('ddd')})`);
			const firstWeekMoment = new moment(reportArgs.startDate);
			const secondWeekMoment = new moment(reportArgs.startDate).add(1, 'week');
			instance.firstWeekLabel.set(`${firstWeekMoment.format("MM/DD/YYYY")} (${firstWeekMoment.format("ddd")}) - ${firstWeekMoment.add(6, "days").format("MM/DD/YYYY")} (${firstWeekMoment.add(6, "days").format("ddd")})`)
			instance.secondWeekLabel.set(`${secondWeekMoment.format("MM/DD/YYYY")} (${secondWeekMoment.format("ddd")}) - ${secondWeekMoment.add(6, "days").format("MM/DD/YYYY")} (${secondWeekMoment.add(6, "days").format("ddd")})`)
		}).catch((err) => {
			return;
		});
	}

	getPeopleData({ type: { "$in": ["staff", "admin"] } }, { sort: { lastName: 1, firstName: 1 }, fields: { _id: 1, lastName: 1, firstName: 1, inActive: 1 } }).then(res => {
		instance.peopleData.set(res);
	}).catch(err => {
		console.log(err);
	});
}

Template.reportStaffTime.rendered = function () {
	var momentStartDate = new moment();
	$('#reportStartDate').val(momentStartDate.format("MM/DD/YYYY"));
	var momentEndDate = new moment().add(1, 'week').add(6, 'days');
	$('#reportEndDate').val(momentEndDate.format("MM/DD/YYYY"));

	$("#reportStartDate").datepicker({
		autoclose: true, todayHighlight: true
	})//.on("changeDate", function(e) {
	//var momentEndDate = new moment(e.date).add(1, 'week').add(6, 'days');
	//$("#reportEndDate").val(momentEndDate.format("MM/DD/YYYY"))
	//})
	$("#reportEndDate").datepicker({
		autoclose: true, todayHighlight: true
	})
}

Template.reportStaffTime.events({
	"click #chkQueue": function(e, i) {
        i.queueMode.set(document.getElementById('chkQueue').checked);
    },
	"click #btnUpdate": async function (e, i) {
		if ($("#reportStartDate").val() == "") return;
		const startDate = new moment($("#reportStartDate").val()).toDate().valueOf(),
			endDate = new moment($("#reportEndDate").val()).toDate().valueOf(),
			todayValue = new moment().valueOf(),
			includeDetail = $("#chkIncludeDetail").prop("checked"),
			includePayDetail = $("#chkIncludePayDetail").prop("checked"),
			groupByStaff = $("#chkGroupByStaff").prop("checked"),
			specifiedPerson = $("#selectedPerson").val(),
			specifiedPersonName = $("#selectedPerson option:selected").text();
		const frequency = $("#overtimeFrequency").val();
		const orgIds = $("#reportOrgs").val() || [];
		const orgSelect = document.getElementById('reportOrgs');
		const orgHierarchyIds = Array.from(orgSelect?.options ?? []).map(option => option.value);
		i.showProjectedOverTime.set(endDate > todayValue);

		if (!orgIds.length) {
			orgIds.push(Orgs.current()._id);
		}

		const options = {
			startDate,
			endDate,
			specifiedPerson,
			specifiedPersonName,
			includeDetail,
			includePayDetail,
			groupByStaff,
			frequency,
			orgIds,
			orgHierarchyIds,
			showProjectedOverTime: i.showProjectedOverTime.get()
		}

		if (i.queueMode.get()) {
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'staffTimeReportAggregates',
				reportName: 'Staff Time Report',
				reportArgs: options,
				userId: Meteor.userId(),
				reportRoute: FlowRouter.current().path.slice(1)
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "staffTime"
			});
			reportQueuedSwal();
			return;
		}

		i.isUpdating.set(true);
		Meteor.callAsync("staffTimeReportAggregates", options).then((response) => {
			i.reportData.set(response);
		}).catch((err) => {
			i.reportData.set(response);
		}).finally(() => {
			i.isUpdating.set(false);
			i.dateRangeLabel.set(`${new moment(startDate).format("MM/DD/YYYY")} (${new moment(startDate).format('ddd')}) -  ${new moment(endDate).format("MM/DD/YYYY")} (${new moment(endDate).format('ddd')})`);
			var firstWeekMoment = new moment($("#reportStartDate").val());
			var secondWeekMoment = new moment($("#reportStartDate").val()).add(1, 'week');
			i.firstWeekLabel.set(`${firstWeekMoment.format("MM/DD/YYYY")} (${firstWeekMoment.format("ddd")}) - ${firstWeekMoment.add(6, "days").format("MM/DD/YYYY")} (${firstWeekMoment.add(6, "days").format("ddd")})`)
			i.secondWeekLabel.set(`${secondWeekMoment.format("MM/DD/YYYY")} (${secondWeekMoment.format("ddd")}) - ${secondWeekMoment.add(6, "days").format("MM/DD/YYYY")} (${secondWeekMoment.add(6, "days").format("ddd")})`)
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "staffTime"
		});
	},
	"click #btnPrint": async function (e, i) {
		const meteorUser = Meteor.user();
		const result = await mpSwal.fire({
			showCloseButton: true,
			showCancelButton: true,
			title: "Send the result to your email?",
			input: 'text',
			inputValue: meteorUser.emails[0].address,
		});
		const { isConfirmed, value } = result;
		const outputFile = 'export.csv'
		if (isConfirmed) {
			const blob = exportTableToBlob.apply(this, [$('#dvData table')]);
			await Meteor.callAsync('sendReportFile', { dataReport: blob, email: value });
		} else {
			// CSV
			exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);
		}

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "staffTime"
		});
	},
	"change #chkIncludeDetail": function (e, i) {
		i.showDetails.set($(e.currentTarget).prop("checked"));
	},
	"change #chkIncludePayDetail": function (e, i) {
		i.showPayDetails.set($(e.currentTarget).prop("checked"));
	},
	"change #chkGroupByStaff": function (e, i) {
		i.groupByStaff.set($(e.currentTarget).prop("checked"));
	},
	"click #btnPaycorp": function (e, i) {
		const newPaycorpToggleVal = !i.payCorpToggle.get();
		if ($("#chkGroupByStaff").prop("checked") == false && newPaycorpToggleVal) {
			$("#chkGroupByStaff").prop("checked", true);
			$("#chkGroupByStaff").trigger("change");
		}
		i.payCorpToggle.set(newPaycorpToggleVal);
		if (newPaycorpToggleVal) {
			$("#btnPaycorp").text("Reset View");
			$("#btnUpdate").trigger('click');
		} else {
			$("#btnPaycorp").text("Toggle Paycor View");
		}
	},
	"click .BT-SW-W":function(e, i){
		e.preventDefault();
		const showMore = $('.' + $(e.target).data('id'));
		if (showMore.length) {
			const currentlyHidden = (showMore.css('display') === 'none');
			showMore.css('display', currentlyHidden ? 'table-row' : 'none');
		}
		e.target.innerText = e.target.innerText === 'See More Info' ? 'See Less Info' : 'See More Info';
	},
});

Template.reportStaffTime.helpers({
	hasMultiplesOrgs() {
		let data = Template.instance().reportData.get()
		return data.length > 1
	},
	getData(){
		const data = Template.instance().reportData.get();
		return data;
	},
	hasProjectedOvertime() {
		return Template.instance().showProjectedOverTime.get()
	},
	isPayCorpActive() {
		return Template.instance().payCorpToggle.get();
	},
	exportPaycorp() {
		const org = Orgs.current();
		if (org && org.hasCustomization("reports/paycor/enabled")) {
			return true;
		}
		return false;
	},
	formatTimeLabel(minutes) {
		return Math.floor(minutes / 60) + ":" + (minutes % 60).toString().padStart(2, "0")
	},
	formatTotalHoursLabel(minutes) {
		return currency(minutes / 60).toString();
	},
	getClassificationTotals(details) {
		return Object.values(details);
	},
	getOTFrequency() {
		return Orgs.current().getOvertimeFrequency();
	},
	getPeriodDescription(week) {
		if (parseInt(week) == 0) {
			return Template.instance().firstWeekLabel.get()
		} else {
			return Template.instance().secondWeekLabel.get();
		}
	},
	showGroupByStaff() {
		return Template.instance().groupByStaff.get();
	},
	showDetails() {
		return Template.instance().showDetails.get();
	},
	getWeekArray() {
		const data = Template.instance().reportData.get(), out = [];
		for (let i = 0; i < Object.keys(data?.totals || {}).length; i++) out.push(i);
		return out;
	},
	getWeekTotals(weekNum, prop) {
		const data = Template.instance().reportData.get(),
			dataItem = data?.totals && data.totals[weekNum.toString()];
		return dataItem && dataItem[prop];
	},
	getOrg() {
		const data = Template.instance().reportData.get();
		return data.currentOrg.name;
	},
	weeksData() {
		const data = Template.instance().reportData.get();
		if (!data) return;
		const out = _.map(data.rows, (row, idx) => ({
			weekNum: idx,
			weekNumLabel: (parseInt(idx) + 1).toString(),
			weekLabel: data.dateRangeLabels[idx],
			employees: row,
			totals: data.totals[idx]
		}));
		return out;
	},
	weeksDataX2(data) {
		if (!data) return;
		const out = _.map(data.rows, (row, idx) => ({
			weekNum: idx,
			weekNumLabel: (parseInt(idx) + 1).toString(),
			weekLabel: data.dateRangeLabels[idx],
			employees: row,
			totals: data.totals[idx]
		}));
		return out;
	},
	getWeekLabel(weekNum) {
		return Template.instance().firstWeekLabel.get();
	},
	reportRows() {
		const data = Template.instance().reportData.get();
		return (data && data.rows) ?? data?.length > 0 ;
	},
	combinedArray() {
		const data = Template.instance().reportData.get();
		return data && data.combinedArray;
	},
	dateRangeLabel() {
		return Template.instance().dateRangeLabel.get();
	},
	totals() {
		const data = Template.instance().reportData.get();
		return data && data.totals;
	},
	timeCardConfirmed(employee) {
		const timeCards = employee.timeCardDetails;
		if (timeCards.every(obj => obj.confirmed === true)) {
			return "Yes"
		} else {
			return "No"
		}
	},
	people() {
		return Template.instance().peopleData.get();
	},
	getGrandTotals() {
		const data = Template.instance().reportData.get();
		const groupByStaff = Template.instance().groupByStaff.get();
		if (data?.grandTotal) {
			return data.grandTotal;
		}
		if (!groupByStaff && !data.length) {
			return undefined;
		}
		const combinedGrandTotal = {
			allTotalOvertimeMinutes: 0,
			allTotalMinutes: 0,
			allTotalFutureOvertime: 0,
			totalOvertimeHours: 0,
			totalFutureOvertimeHours: 0,
			paidHours: 0,
			totalHours: 0
		};
		for (const orgData of data) {
			let grandTotal = orgData.grandTotal;
			combinedGrandTotal.allTotalOvertimeMinutes += grandTotal.allTotalOvertimeMinutes ?? 0;
			combinedGrandTotal.allTotalMinutes += grandTotal.allTotalMinutes ?? 0;
			combinedGrandTotal.allTotalFutureOvertime += grandTotal.allTotalFutureOvertime ?? 0;
			combinedGrandTotal.totalOvertimeHours += grandTotal.totalOvertimeHours ?? 0;
			combinedGrandTotal.totalFutureOvertimeHours += grandTotal.totalFutureOvertimeHours ?? 0;
			combinedGrandTotal.paidHours += grandTotal.paidHours ?? 0;
			combinedGrandTotal.totalHours += grandTotal.totalHours ?? 0;
		}

		const totalMinutes = combinedGrandTotal.allTotalMinutes;
		const hours = Math.floor(totalMinutes / 60);
		const minutes = totalMinutes % 60;

		combinedGrandTotal.totalHoursMinutesLabel = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
		return combinedGrandTotal;

	},
	getRegularHours(classification) {
		if (classification.type == "Standard") {
			return numeral(classification.totalHours).format("0.00");
		}
	},
	getOvertimeHours(classification) {
		if (classification.type == "Standard" && parseFloat(classification?.totalOvertimeHours ?? 0) > 0) {
			return numeral(classification.totalOvertimeHours).format("0.00");
		}
	},
	getHoursCode(classification) {
		if (classification.type != "Standard") {
			return classification.type;
		}
	},
	getCodedHours(classification) {
		if (classification.type != "Standard") {
			return numeral(classification.totalHours).format("0.00");
		}
	},
	getFutureOTHours(classification) {
		if (classification.type == "Standard")
			return numeral(classification.futureOT).format("0.00");
	},
	showPayDetail() {
		return Template.instance().showPayDetails.get();
	},
	getEmployeeClassification(employee) {
		if (employee.employeeClassification) {
			return employee.employeeClassification;
		}
		return employee.payRate ? 'Non-Exempt' : 'Exempt'
	},
	hasPayDetailPermission() {
		return processPermissions({
			assertions: [{ context: "people/profile/pay", action: "read" }],
			evaluator: (thisPerson) => thisPerson.type === "admin",
		})
	},
	updateLabel() {
		return Template.instance().isUpdating.get() ? '<i class="fa fa-spinner fa-spin"></i> Updating' : (Template.instance().queueMode.get() ? 'Queue' : 'Update');
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
	getGroupByOrgGroupings() {
		const data = Template.instance().reportData.get();
		const orgRows = [];
		if (data && data.rows) {
			orgRows.push(data);
		} else {
			orgRows.push(...data);
		}
		return orgRows;
	},
	getGroupByOrgName(orgGroup) {
		const data = Template.instance().reportData.get();
		if (data && data.rows) {
			return '';
		}
		return `(${orgGroup.currentOrg.name})`;
	}
})
