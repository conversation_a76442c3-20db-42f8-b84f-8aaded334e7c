import './reportsSavedList.html';
import { showModal } from '../main';
import './customReports/modalReportSave';

Template.reportsSavedList.onCreated(function () {
    this.reportsList = new ReactiveVar([]);
    this.loading = new ReactiveVar(true);
    Meteor.callAsync('getSavedCustomReportsList')
        .then((result) => {
            this.reportsList.set(result);
            this.loading.set(false);
        })
        .catch((error) => {
            mpSwal.fire('Error', 'Error retrieving saved reports', 'error');
        })
    ;
});

Template.reportsSavedList.helpers({
    reportsList() {
        return Template.instance().reportsList.get();
    },
    noReports() {
        return Template.instance().reportsList.get().length === 0
    }
});

Template.reportsSavedList.events({
    'click .edit-saved-report-btn' (event, instance) {
        const reportId = event.currentTarget.dataset.reportId;
        const name = instance.reportsList.get().find((report) => report._id === reportId).name;
        showModal('modalReportSave', { reportId: reportId, reportName: name, isEdit: true, callback: () => {
            reloadList(instance)
            }
        }, '#modalReportSave');
    },
});

function reloadList(instance) {
    Meteor.callAsync('getSavedCustomReportsList')
        .then((result) => {
            instance.reportsList.set(result);
        })
}