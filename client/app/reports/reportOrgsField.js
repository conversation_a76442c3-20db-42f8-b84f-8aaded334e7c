import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './reportOrgsField.html';
import { Orgs } from '../../../lib/collections/orgs';
import { Log } from '../../../lib/util/log';

Template.reportOrgsField.helpers({
	availableOrgs() {
		return Template.instance().orgs.get();
	},
	isLoading() {
		return !Template.instance().loaded.get();
	},
	orgName() {
		return Orgs.current() && Orgs.current().name;
	}
});

Template.reportOrgsField.onCreated( function() {
	const self = this;
	self.orgs = new ReactiveVar();
	self.loaded = new ReactiveVar();
	const switchableSites = Session.get('switchableSites');
	if (switchableSites && switchableSites.length > 0) {
    self.orgs.set(switchableSites);
		self.loaded.set(true);

    const currentOrgId = Orgs.current() && Orgs.current()._id;
    if (currentOrgId) {
      $("#reportOrgs").multiselect('select', currentOrgId);

      const opts = Template.instance().data.opts;
      if (opts?.onChange) {
        opts.onChange($("#reportOrgs").val() || [currentOrgId]);
      }
    }
  } else {
    Meteor.callAsync('getSwitchableSites').then((result) => {
      Session.set('switchableSites', result);
      self.orgs.set(result);
      self.loaded.set(true);
      $("#reportOrgs").multiselect("rebuild");

      const currentOrgId = Orgs.current() && Orgs.current()._id;
      if (currentOrgId) {
        $("#reportOrgs").multiselect('select', currentOrgId);

        const opts = Template.instance().data.opts;
        if (opts?.onChange) {
          opts.onChange($("#reportOrgs").val() || [currentOrgId]);
        }
      }
    }).catch((error) => {
      Log.error("Error in getSwitchableSites:", error);
    });
	}
});
Template.reportOrgsFieldDropdown.onRendered( function() {
	const opts = Template.instance().data.opts;
	var self = this;
	$("#reportOrgs").multiselect({
		includeSelectAllOption: true,
		enableFiltering: true,
		maxHeight: 300,
		nonSelectedText: opts?.overrideNonSelectedText || (Orgs.current() && Orgs.current().name),
		onChange: function(option, checked, select) { 
			const orgs = self.data.orgs;
			checkAllBelow($(option).val(), orgs, checked);
			if (opts?.onChange) {
				//console.log("reporting back", $("#reportOrgs").val());
				opts.onChange($("#reportOrgs").val()); 
			}
		},
		onSelectAll: function() { if (opts?.onChange) opts.onChange($("#reportOrgs").val()); },
		onDeselectAll: function() { if (opts?.onChange) opts.onChange($("#reportOrgs").val()); },
	});
  if (opts?.selectedIds) {
    $("#reportOrgs").multiselect('select', opts.selectedIds);
  } else {
    const currentOrgId = Orgs.current() && Orgs.current()._id;
    if (currentOrgId) {
      $("#reportOrgs").multiselect('select', currentOrgId);
      if (opts?.onChange) {
        opts.onChange($("#reportOrgs").val() || [currentOrgId]);
      }
    }
  }
});

function checkAllBelow(orgId, orgs, checked) {
	// if selected org is brand check all that apply
	const isSelected = checked ? 'select' : 'deselect';
	if(orgs.find(org => org._id === orgId).isBrand){
		orgs.filter( o => o.selectedBrand === orgId).forEach(o => {
			$("#reportOrgs").multiselect(isSelected, o._id);
		});
	}
	orgs.filter( o => o.parentOrgId == orgId).forEach(o => {
		$("#reportOrgs").multiselect(isSelected, o._id);
		checkAllBelow(o._id, orgs);
	});
}