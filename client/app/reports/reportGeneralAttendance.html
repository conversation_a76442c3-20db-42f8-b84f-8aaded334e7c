<template name="reportGeneralAttendance">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">General Attendance Report
				<span class="text-muted pt-2 font-size-sm d-block">Attendance data filtered by individual, date range, and more</span></h3>
			</div>
			<div class="card-toolbar">

				{{> exportCsvAndPdfGeneralAttendance elementName="dvData" showSingleChildPerPage=showSingleChildPerPage }}

                {{#unless isSavedMode}}
					<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
						Update
					</div>
				{{/unless}}
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-header">
				  <h3 class="box-title">General Attendance Report</h3>
				</div>
				<div class="box-body">
						{{#if isSavedMode}}
								<div class="row font-weight-bolder text-dark my-4">
										<div class="col">
												{{{ savedHeader }}}
										</div>
								</div>
						{{/if}}
						{{#unless isSavedMode}}
							<div class="row">
							<div class="col-lg-2">
									<div class="form-group">
									<label>Start Date</label>
									<div class="input-group">

										<input data-cy="start-date-input" type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}">
									</div>
								</div>
							</div>
							<div class="col-lg-2">
									<div class="form-group">
									<label>End Date</label>
									<div class="input-group">
										<input data-cy="end-date-input" type="text" class="form-control pull-right" id="attendanceEndDate" value="{{formattedEndDate}}">
									</div>
								</div>
							</div>
							<div class="col-lg-2">
								<div class="form-group">
									<label>Person</label>
									<div class="input-group">
										<select data-cy="select-person" class="form-control" id="filterPerson">
											<option value="">All</option>
												{{#each people}}
												<option value="{{_id}}">{{lastName}}, {{firstName}}</option>
												{{/each}}
											</select>
									</div>
								</div>
							</div>
							<div class="col-lg-2">
								<div class="form-group">
									<label>Type</label>
									<div class="input-group">
										<select data-cy="filter-type" class="form-control" id="filterType">
											<option value="">All</option>
												<option value="person">Person</option>
											</select>
									</div>
								</div>
							</div>
							<div class="col-lg-3">
								<div class="form-group">
									<label>Group:</label>
									<select data-cy="select-group" class="form-control" id="filterGroup">
										<option value="">All</option>
											{{#each groups}}
											<option value="{{_id}}">{{name}}</option>
											{{/each}}
										</select>
								 </div>
							</div>
							</div>
							<div class="row">
							<div class="col-lg-2">
								<label>Sort:</label>
								<select data-cy="select-sort" class="form-control" id="sortType">
									<option value="attendeeFirstName" {{selectedIfEqual currentSortType "attendeeFirstName"}}>First Name</option>
									<option value="attendeeLastName" {{selectedIfEqual currentSortType "attendeeLastName"}}>Last Name</option>
									<option value="attendanceDate" {{selectedIfEqual currentSortType "attendanceDate"}}>Date</option>
								</select>
							</div>
								<div class="col-lg-2">
									<label>Max results per page:</label>
									<select data-cy="page-limit" class="form-control" id="pageLimit">
										<option value=50 {{selectedIfEqual currentLimit 50}}>50</option>
										<option value=100 {{selectedIfEqual currentLimit 100}}>100</option>
										<option value=150 {{selectedIfEqual currentLimit 150}}>150</option>
										<option value='all' {{selectedIfEqual currentLimit 'all'}}>Show All (May take a long time)</option>
									</select>
								</div>
								<div class="col-lg-2">
									<div data-cy="select-org" class="form-group">
										<label>Org(s):</label><br />
										{{> reportOrgsField }}
									</div>
								</div>
								<div class="col-lg-2">
									<div class="form-group">
										<label>Subsidy Agency:</label><br />
										{{> reportSubsidyAgencyField people=people }}
									</div>
								</div>
						</div>
							<div class="row mt-3 mb-3 px-4 d-flex justify-content-start">
								<div class="mr-10">
									<div class="d-inline-flex align-items-center">
										{{#if isSortTypeAttendanceDate}}
										<div class="form-group">
											<div class="checkbox-list">
												<label class="checkbox text-muted text-decoration-line-through">
													<input data-cy="email-when-report-complete-checkbox" type="checkbox" value="" id="chkQueue" disabled><span></span>Email me when this report is complete
												</label>
											</div>
										</div>
										{{else}}
										{{> reportQueueCheckbox }}
										{{/if}}
									</div>
								</div>
								<div class="mr-10">
									<div class="form-group d-inline-flex align-items-center">
										<div class="checkbox-list">
											<label class="checkbox">
												<input data-cy="include-absences-checkbox" type="checkbox" value="" id="chkAbsences"><span></span>Include absences
											</label>
										</div>
									</div>
								</div>
								<div class="mr-10">
									<div class="d-inline-flex align-items-center">
										<div class="form-group">
											<div class="checkbox-list">
												<label class="checkbox">
													<input data-cy="child-per-page-checkbox" type="checkbox" value="" id="chkchildPerPage"><span></span>Print each child on a separate page (PDF Only)
												</label>
											</div>
										</div>
									</div>
								</div>
								<div class="mr-10">
									<div class="form-group d-inline-flex align-items-center">
										<div class="checkbox-list">
											<label class="checkbox">
												<input data-cy="include-id-number-checkbox" type="checkbox" value="" id="chkIdNumber"><span></span>Include User ID Number
											</label>
										</div>
									</div>
								</div>
							</div>
						{{/unless}}
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
					{{#unless showDownloadMessage}}
				  <table data-cy="attendance-report-table" class="table" id='dvData'>
					<tbody><tr class="reportHeader">
					  {{#if showIdNumber}}
							<th>Id Number</th>
						{{/if}}
					  <th>Name</th>
					  <th>Date</th>
					  <th>Check-In</th>
					  <th>Check-Out</th>
					  <th>Checked In By</th>
					  <th>Checked Out By</th>
					  <th style="text-align:right">Elapsed Time</th>
					  {{#if showElapsedTimeUnits}}
						<th style="text-align:right">Elapsed Time (units)</th>
					  {{/if}}
					  {{#if hasCustomization "people/types/showPayer"}}
						  <th>Payer</th>
					  {{/if}}
					  {{#if hasCustomization "moments/checkin/showTransportation"}}
						  <th>Transportation (Check-In)</th>
						  <th>Transportation (Check-Out)</th>
					  {{/if}}
					  {{#if showStaffMember}}
						<th>Staff Member</th>
					  {{/if}}
					  <th>Schedule</th>
					  <th style="text-align:right">Variance (mins)</th>
					  <th style="text-align:right">Subsidy Agency</th>
					  {{#if showObjectiveField}}
						<th>Objective</th>
					  {{/if}}
					  {{#if showOrg}}
						<th>Org</th>
					  {{/if}}
						<th>Check In/Out Deviation Reason</th>
					</tr>
					
					{{# each attendees}}
					<tr style="{{isPageBreakchecked addPageBreak}}">
						{{#if showIdNumber}}
							<td>{{_id}}</td>
						{{/if}}
					  <td>{{name}} {{#if inActive}}<span style="color:#ff0000">(i)</span>{{/if}}</td>
					  <td>{{date}}</td>
					  <td>{{cInTime}}</td>
					  <td>{{cOutTime}}</td>
					  <td>{{cInBy}}</td>
					  <td>{{cOutBy}}</td>
					  <td style="text-align:right">{{elapsedTime}}</td>
					  {{#if showElapsedTimeUnits}}
						<td style="text-align:right">{{getElapsedTimeUnits elapsedTime}}</td>
					  {{/if}}
					  {{#if hasCustomization "people/types/showPayer"}}
						  <td>{{payer}}</td>
					  {{/if}}
					  {{#if hasCustomization "moments/checkin/showTransportation"}}
						  <td>{{trans}}</td>
						  <td>{{cOutTrans}}</td>
					  {{/if}}
					  {{#if showStaffMember}}
						<td>{{staffName}}</td>
					  {{/if}}
					  <td>{{scheduleOnDay}}</td>
					  <td style="text-align:right">{{variance}}</td>
						<td style="text-align:right">{{formatReimbursementTypes reimbursementTypes}}</td>
						{{#if showObjectiveField}}
						<td>{{carePlan}}</td>
					  {{/if}}
					  {{#if showOrg}}
						<td>{{orgName}}</td>
					  {{/if}}
					  <td><pre>{{{reason}}}</pre></td>
					</tr>
					{{/each}}

				  </tbody></table>
						{{else}}
							<h3 class="d-flex justify-content-center">
								Your report is too large to display. CSV file will be automatically downloaded.
							</h3>
						{{/unless}}
					{{#if showPageNav}}
					<nav aria-label="Page navigation">
						<ul class="pagination justify-content-end">
							<li data-cy="previous-btn" class="page-item {{previousDisabled}}"><a id="previousBtn" class="page-link" href="#">Previous Page</a></li>
							<li data-cy="next-btn" class="page-item {{nextDisabled}}"><a id="nextBtn" class="page-link" href="#">Next Page</a></li>
						</ul>
						<div class="d-flex justify-content-center">
							<div>{{getPage}}</div>
						</div>
					</nav>
					{{/if}}
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
