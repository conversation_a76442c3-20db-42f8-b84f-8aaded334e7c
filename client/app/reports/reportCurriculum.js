import $ from 'jquery';
import './reportCurriculum.html';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Curriculums } from '../../../lib/collections/curriculum';
import { User } from '../../../lib/collections/users';
import { CurriculumReportUtils } from '../../../lib/activities/curriculumReportUtils';

Template.reportCurriculum.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.availableOrgs = new ReactiveVar();
}

Template.reportCurriculum.rendered = function() {
	$('#scheduledStartDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
	$('#scheduledEndDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
	$("#inputCurriculumStandards").select2( {
		width: 'resolve',
		templateSelection: function(item)
		    {
		        return item.text;
			}
		}
	);
	$("#inputCurriculumTypes").select2( {
		width: 'resolve',
		templateSelection: function(item)
		    {
		        return item.text;
			}
		}
	);
}

Template.reportCurriculum.events({
	"click #update": async function() {
		var scheduledStartDate = $("#scheduledStartDate").val();
		if (scheduledStartDate == "") return;		
		var scheduledEndDate = $("#scheduledEndDate").val();
		if (scheduledEndDate == "") return;		
		var scheduledStartDateNum = new moment(scheduledStartDate, "MM/DD/YYYY").valueOf();
		var scheduledEndDateNum = new moment(scheduledEndDate, "MM/DD/YYYY").valueOf();
		var selectedGroup = $("#selectedGroup").val();
		var selectedStandards = $("#inputCurriculumStandards").val();
		var selectedTypes = $("#inputCurriculumTypes").val();
		var searchText = $("#searchText").val();
		var instance = Template.instance();
		Meteor.callAsync("curriculumReportAggregates", 
		{ 
			scheduledStartDate: scheduledStartDateNum,
			scheduledEndDate: scheduledEndDateNum,
			selectedGroup: selectedGroup,
			selectedStandards: selectedStandards,
			searchText: searchText,
			selectedTypes: selectedTypes
		})
		.then((response) => {
			instance.reportData.set(response);
		}).catch((err) => {
			instance.reportData.set(response);
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "curriculum"
		});
	},
	"click #btnExportCsv": async function() {
		const data = Template.instance().reportData.get();
		if (!data) {
			return;
		}
		const csvFile = CurriculumReportUtils.convertReportDataToCsv(data);
		DownloadFile(csvFile, `export-activities.csv`);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "curriculum"
		});
	}
});

Template.reportCurriculum.helpers({
	"curriculums": function() {
		return Template.instance().reportData.get();
	},
	'availableCurriculumStandards': function() {
		return Curriculums.getStandards();
	},
	'availableCurriculumTypes': function() {
		if (Orgs.current()) {
			var types =  Orgs.current().availableCurriculumTypes({mapped: true});
			return _.map(types, function(v) {return {value: v, name: v.replace('`', ' > ')};});
		}
	},
	"groups": function() {
		return Groups.find({
			orgId: Meteor.user()["orgId"]
		}, {sort: {"name" : 1}});
	},
	"standardsFormatted": function() {
		var output="";
		_.each(this.selectedStandards,function(s) { if (output != "") output = output + ", "; output = output + s.replace("|", " "); });
		return output;
	},
	"typesFormatted": function() {
		var output="";
		_.each(this.selectedTypes,function(s) { if (output != "") output = output + ", "; output = output + s.replace("`", " > "); });
		return output;
	}
});
