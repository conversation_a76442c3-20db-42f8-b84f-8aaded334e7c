<template name="reportBillingExpanded">
		<div class="card card-custom">
			<div class="card-header flex-wrap border-0 pt-6 pb-0">
				<div class="card-title">
					<h3 class="card-label">Expanded Billing Report</h3>
				</div>
				<div class="card-toolbar">
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>
					<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
						Update
					</div>
				</div>
			</div>
			<div class="card-body">
				<div class="box">
					<div class="box-body">
						<div class="row">
							<div class="col-lg-3">
								<div class="form-group">
									<label>Start Date</label>
									<div class="input-group">
										<input type="text" class="form-control pull-right" id="startDate" value="{{formattedStartDate}}">
									</div>
								</div>
							</div>
							<div class="col-lg-3">
								<div class="form-group">
									<label>End Date</label>
									<div class="input-group">
										<input type="text" class="form-control pull-right" id="endDate" value="{{formattedEndDate}}">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="box-body no-padding mt-6">
						<table class="table" style="overflow-x:auto;display:block">
						<tbody><tr>
							
							<th>Guest Name</th>
							<th>Payment Classification</th>
							<th># Full Days</th>
							<th>Days Attended</th>
							<th>Scholarship Allowance</th>
							<th>Scholarship Amount</th>
							<th>Hours</th>
							<th>Units</th>
							<th># No Shows</th>
							<th># Billable No Shows</th>
							<th>Dates of Billable No Shows</th>
							<th># Cancellations</th>
							<th># Billable Cancellations</th>
							<th>Dates of Billable Cancellations</th>
							<th># Billable Late Minutes</th>
							<th>Baths</th>
						</tr>
						
						{{#each entries}}
						<tr>
							<td>{{name}}</td>
							<td>{{paymentClassification}}</td>
							<td align=right>{{numFullDays}}</td>
							<td>{{{detailedDates}}}</td>
							<td align=right>{{scholarshipAllowance}}</td>
							<td align=right>{{formatCurrency scholarshipAmount}}</td>
							<td align=right>{{formatNumber hours "0.00"}}</td>
							<td align=right>{{formatNumber units "0.00"}}</td>
							<td align=right>{{noShows}}</td>
							<td align=right>{{noShows}}</td>
							<td>{{{noShowDates}}}</td>
							<td align=right>{{cancellations}}</td>
							<td align=right>{{billableCancellations}}</td>
							<td>{{{billableCancellationDays}}}</td>
							<td align=right>{{{lateMinutes}}}</td>
						</tr>
						{{/each}}

						</tbody></table>
					</div><!-- /.box-body -->
				</div>
			</div>
		</div>
</template>
