<template name="reportMoments">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Moments Report
				<span class="text-muted pt-2 font-size-sm d-block">View moments and filter by group or individual.</span></h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">

				<div class="box-body">
				  <div class="row">
					<div class="col-lg-2">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								
								<input data-cy="start-date-input" type="text" class="form-control pull-right" id="momentsStartDate" value="{{formattedStartDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-2">
						  <div class="form-group">
							<label>End Date</label>
							<div class="input-group">
								
								<input data-cy="end-date-input" type="text" class="form-control pull-right" id="momentsEndDate" value="{{formattedEndDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-3 checkbox-list">
						<br/>
						<label class="checkbox"><input data-cy="details-checkbox" type="checkbox" value="" id="chkIncludeDetail"><span></span>Include Detail as Column</label>
					</div>
				  </div>
				  	<div class="row">
						<div class="col-lg-2">
							<div class="form-group">
								<label>Type</label>
								<div class="input-group">
									<select data-cy="select-moment-type" class="form-control" id="filterType">
										<option value=""></option>
										{{#each availableMomentTypes}}
										  <option value="{{momentType}}">{{prettyName}}</option>
										  {{/each}}
									  </select>
								</div>
							</div>
						</div>

						<div class="col-lg-2">
							<div class="form-group">
								<label>Moment Group:</label>
								<select data-cy="select-moment-group" class="form-control" id="filterMomentGroup">
									<option value="">All</option>
									  {{#each groups}}
									  <option value="{{_id}}">{{name}}</option>
									  {{/each}}
								  </select>
							 </div>
						</div>
	
						<div class="col-lg-2">
							<div class="form-group">
								<label>Default Group:</label>
								<select data-cy="select-default-group" class="form-control" id="filterGroup">
									<option value="">All</option>
									  {{#each (groups true)}}
									  <option value="{{_id}}">{{name}}</option>
									  {{/each}}
								  </select>
							 </div>
						</div>

						<div class="col-lg-3">
							<div class="form-group">
								<label>Person</label>
								<div class="input-group">
									<select data-cy="select-person" class="form-control" id="filterPerson">
										<option value="">All</option>
										  {{#each people}}
										  <option value="{{_id}}">{{lastName}}, {{firstName}}</option>
										  {{/each}}
									  </select>
								</div>
							</div>
						</div>
						
					</div>
				
				</div><!-- /.box-header -->
				<div class="box-body no-padding mt-6" id="dvData">
				  <table class="table" id="moment-report-table">
					<tbody>
						{{#if moments}}
						<tr>
					  
							<th>Date/Time</th>
							<th>Creator</th>
							<th>Attribution</th>
							<th>Type</th>
							<th>Description</th>
							<th>People</th>
							{{#if showGroup}}
							<th>Group</th>
							{{/if}}
							{{#if showDetail}}
							<th>Detail</th>
							{{/if}}
							<th></th>
					  <!--<th>Detail</th>-->
						</tr>
						{{/if}}
					
					{{# each moments}}
					<tr>
					  <td data-cy="date-time" class='no-wrap'>{{date}} {{time}}</td>
											<td data-cy="created-person-name">{{createdByPersonName}}</td>
											<td data-cy="attribution-name">{{attributionName}}</td>
					  <td data-cy="moment-type">{{momentTypePrettyTranslation}}</td>
					  <td data-cy="description-comment">{{breaklines comment}}</td>
											<td data-cy="people-moment" >{{{highlightInactives peopleListString}}}</td>
											{{#if showGroup}}
											<td data-cy="group-name">{{groupName}}</td>
											{{/if}}
											{{#if showDetail}}
											<td data-cy="detail-moment" class='no-wrap'>{{breaklines detailString}}</td>
											{{/if}}
											<td class='no-wrap'>
												<a data-cy=detail-btn href="#" class="linkViewMomentDetail" data-id="{{_id}}">Detail</a> | 
												<a data-cy="go-to-btn" href="#" class="linkGoToMoment" data-id="{{_id}}">Go To</a>
											</td>
					  <!--<td align=center>{{detailLink}}</td>-->
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
