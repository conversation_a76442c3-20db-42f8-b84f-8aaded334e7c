import {FlowRouter} from 'meteor/ostrio:flow-router-extra';
import {AvailableCustomizations} from "../../../lib/customizations";
import { CANCELLATION_REASONS } from '../../../lib/constants/reservationConstants';
import './attendanceGridByDate.html';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import './reportScheduleTypesField';

const moment = require('moment-timezone');

const dayMap = {"mon": "M", "tue": "Tu", "wed": "W", "thu": "Th", "fri": "F"};

Template.attendanceGridByDate.created = function () {
	const template = this;

	const isPrintableView = FlowRouter.getQueryParam('printable');

	const startDateParam = FlowRouter.getQueryParam('startDate');
	const scheduleTypesParam = FlowRouter.getQueryParam('scheduleTypes');
	const passedGroupId = FlowRouter.getQueryParam('groupId');
	const groupByScheduleTypeParam = FlowRouter.getQueryParam('groupByScheduleType');

	template.reportIsUpdating = new ReactiveVar(false);

	const startDateMoment = startDateParam ? new moment(startDateParam, "MM/DD/YYYY") : new moment();
	const formattedStartDateMoment = startDateMoment.format("MM/DD/YYYY")
	template.reportStartDate = new ReactiveVar(formattedStartDateMoment);

	const scheduleTypesArray = scheduleTypesParam ? scheduleTypesParam.split(",") : [];
	template.scheduleTypes = new ReactiveVar(scheduleTypesArray);
	template.selectedGroup = new ReactiveVar(passedGroupId);
	template.selectionMade = new ReactiveVar(isPrintableView ? true : false);

	const shouldGroupByScheduleType = groupByScheduleTypeParam === 'true' ? true : false
	template.groupByScheduleType = new ReactiveVar(shouldGroupByScheduleType);

	// it's important to set title and rows to empty values so that as the printable
	// report loads there's no flicker of the empty attendance grid. May 
	// want to consider a loading skeleton in the future.

	template.reportGroups = new ReactiveVar([{ title: '', rows: [] }]);

	template.autorun(async function () {
		if (isPrintableView) {
			template.reportIsUpdating.set(true);
			template.selectionMade.set(false); // Avoids an empty results error
			template.reportGroups.set([]);
			Meteor.callAsync('getAttendanceGridByDate', {
				orgId: Orgs.current()._id,
				reportStartDate: formattedStartDateMoment,
				selectedGroup: passedGroupId,
				scheduleTypes: scheduleTypesArray,
				groupByScheduleType: shouldGroupByScheduleType
			}).then((result) => {
				template.reportIsUpdating.set(false);
				template.reportGroups.set(handleGetAttendanceGridByDateResult(result));
				template.selectionMade.set(true);
			}).catch((err) => {
				template.reportIsUpdating.set(false);
				console.error(err);
				mpSwal.fire("Error", "Failed to generate attendance grid", "error");
			})
		}
	});
}

Template.attendanceGridByDate.helpers({
	"getReportIsUpdating": function () {
		return Template.instance().reportIsUpdating.get();
	},
	"formattedStartDate": function () {
		return Template.instance().reportStartDate.get();
	},
	"formattedGeneratedOnDateForReport": function () {
		// use timezone of org to take the current time
		// and make it like June 9, 2023 5:06pm (EST)
		const timezone = Orgs.current().getTimezone();
		return new moment().tz(timezone).format("MMMM D, YYYY h:mma (z)");
	},
	"formattedStartDateForReport": function () {
		// Formats like June 12, 2023:
		return new moment(Template.instance().reportStartDate.get(), "MM/DD/YYYY").format("MMMM D, YYYY");
	},
	"formattedOrgName": function () {
		return Orgs.current()?.name || "";
	},
	"groups": function () {
		return Groups.find({}, { sort: { name: 1 } });
	},
	"incremented": function (index) {
		return index + 1;
	},
	"fullLayout"() {
		return FlowRouter.getQueryParam('printable') != "true";
	},
	"reportGroups"() {
		if (!Template.instance().selectionMade.get()) {
			return;
		}
		// Return reportGroups from the reactive var:
		return Template.instance().reportGroups.get();
	},
	"hasMadeSelection"() {
		return Template.instance().selectionMade.get();
	},
	showGrade() {
		const personProfileFields = Orgs.current().valueOverrides?.profileFields;
		const hasStudentGradeProfileField = personProfileFields.some(field => field.name === 'studentGrade');
		return hasStudentGradeProfileField && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	},
	getFormattedScheduleCell(scheduleCell) {
		return scheduleCell
        .replace(/Absent: Absent -/g, 'Absent -')
        .split(/(Absent:[^,]+|Absent -[^,]+)/)
        .map(segment => {
            if (segment.startsWith('Absent -')) {
                return `<span style="color:red">${segment.replace('Absent -', 'Absent:').trim()}</span>`;
            }
            if (segment.startsWith('Absent:')) {
                return `<span style="color:red">${segment.trim()}</span>`;
            }
            return segment;
        })
        .join('');

	}
});

Template.attendanceGridByDate.rendered = function () {
	$('#attendanceStartDate').datepicker({ autoclose: true });
}

Template.attendanceGridByDate.events({
	"change #groupByScheduleType": function (e, instance) {
		Template.instance().groupByScheduleType.set(e.target.checked);
	},
	"click #btnPrint": async function (e, instance) {
		const selectedScheduleTypes = $("#scheduleTypeMultiSelector").val();

		const params = new URLSearchParams({
			printable: true,
			scheduleTypes: selectedScheduleTypes,
			groupId: instance.selectedGroup.get() || "",
			startDate: instance.reportStartDate.get(),
			groupByScheduleType: instance.groupByScheduleType.get()
		});

		const url = new URL(`/reports/attendanceGridByDate`, window.location.origin);
		url.search = params.toString();

		window.open(url.toString(), '_blank');

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "attendanceGridByDate"
		})
	},
	"click #btnUpdate": function (e, template) {
		template.reportIsUpdating.set(true);
		template.selectionMade.set(false); // Avoids an empty results error
		template.reportGroups.set([]);

		const attendanceStartDateValue = $("#attendanceStartDate").val()
		const attendanceStartDateMoment = new moment(attendanceStartDateValue, "MM/DD/YYYY");
		const formattedAttendanceStartDate = attendanceStartDateMoment.format("MM/DD/YYYY");
		template.reportStartDate.set(formattedAttendanceStartDate);

		const selectedGroup = $("#selectedGroup").val();
		template.selectedGroup.set(selectedGroup);

		const selectedScheduleTypes = $("#scheduleTypeMultiSelector").val();
		template.scheduleTypes.set(selectedScheduleTypes);

		const groupByScheduleType = $("#groupByScheduleType").prop("checked");
		template.groupByScheduleType.set(groupByScheduleType);

		Meteor.callAsync('getAttendanceGridByDate', {
			orgId: Orgs.current()._id,
			reportStartDate: formattedAttendanceStartDate,
			selectedGroup: selectedGroup,
			scheduleTypes: selectedScheduleTypes,
			groupByScheduleType
		}).then((result) => {
			template.reportIsUpdating.set(false);
			template.reportGroups.set(handleGetAttendanceGridByDateResult(result));
			template.selectionMade.set(true);
		}).catch((err) => {
			template.reportIsUpdating.set(false);
			console.error(err);
			mpSwal.fire("Error", "Failed to generate attendance grid", "error");
		})
		
	},
});

// We want to make sure "(No Schedule Type)" is always at the bottom of the list
// and we want to have the rest of the items sorted alphabetically.

const includesNoScheduleType = (item) => item.title.toLowerCase().includes("no schedule type");

function handleGetAttendanceGridByDateResult(arrayOfGroupsWithResultRows) {

	const sortedResult = arrayOfGroupsWithResultRows
		.filter(item => !includesNoScheduleType(item))
		.sort((a, b) => a.title.localeCompare(b.title));

	const noScheduleTypeItem = arrayOfGroupsWithResultRows.find(includesNoScheduleType);

	const finalResults = noScheduleTypeItem ? [...sortedResult, noScheduleTypeItem] : sortedResult;

	return finalResults.map(result => {
		const sortedRows = result.rows.sort((a, b) => `${a.lastName}`.localeCompare(`${b.lastName}`)) // Sort by last name
		return {
			...result,
			rows: sortedRows
		}
	})
}
