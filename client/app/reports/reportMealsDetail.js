const moment = require('moment-timezone');
import {getPeopleData} from "../../services/peopleMeteorService";
import { People } from "../../../lib/collections/people";
import './reportMealsDetail.html';
import { Groups } from "../../../lib/collections/groups";
import { Moments } from "../../../lib/collections/moments";

Template.reportMealsDetail.onCreated( function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportEndDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportDetail = new ReactiveVar(false);
	this.mealsReportAggregates = new ReactiveVar();
	this.resPeopleList = new ReactiveVar([]);
	

	this.autorun(() => {
		const start = this.reportStartDate.get();
		const end = this.reportEndDate.get();
		this.subscribe("mealsDetailsReportMoments", start, end);
	})
});

Template.reportMealsDetail.rendered = function() {
	$('#attendanceStartDate').datepicker({autoclose:true});
	$('#attendanceEndDate').datepicker({autoclose:true});

	var self = Template.instance();

	Tracker.autorun(function() {
		var mealsReportAggregates = self.mealsReportAggregates.get();
	});
}

Template.reportMealsDetail.events({
	"click #btnUpdate": async function() {
		
		var instance = Template.instance();
		instance.reportStartDate.set($("#attendanceStartDate").val());
		instance.reportEndDate.set($("#attendanceEndDate").val());

		fetchMealsReportAggregates(Template.instance());
		
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "mealsDetail"
		});
	},
	"click #btnExportCsv": async function (event) {
		var outputFile = 'export.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#dvData > table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "mealsDetail"
		});
	},
	"change #detailCheckbox": function (e, i) {
		i.reportDetail.set($(e.currentTarget).prop("checked"));
	},
});

Template.reportMealsDetail.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formattedEndDate": function(){
		return Template.instance().reportEndDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.createdAt).format("hh:mm a");
	},
	"data": function() {
		
		const startDate = new moment(Template.instance().reportStartDate.get(), "MM/DD/YYYY"),
			endDate = new moment(Template.instance().reportEndDate.get(), "MM/DD/YYYY").add("1", "days"),
			numDays = endDate.diff(startDate, "days"),
			reportData = Template.instance().mealsReportAggregates.get(),
			people = _.uniq( _.map(reportData, (l) => {return l.personId;} )),
			resPeopleList = Template.instance().resPeopleList.get(),
			mealTypes = ["Breakfast", "AM Snack", "Lunch", "PM Snack"];
		
		let output = {};
		output.days = [];

		_.times(numDays, (d) => { 
			const dayMoment=startDate.clone().add(d ,'days'); 
			if (!(dayMoment.day() == 0 || dayMoment.day()==6)) 
				output.days.push(dayMoment);
		});
		output.meals = mealTypes;
		output.overallTotals = { };
		_.each(mealTypes, (mt) => {output.overallTotals[mt] = {"Paid": 0, "Reduced": 0, "Free":0};})
		output.people = _.map(resPeopleList, (person)=> {
			let cacfpSubsidy = (person.profileData && person.profileData.cacfpSubsidy) ? person.profileData.cacfpSubsidy : person.cacfpSubsidy;
			if (cacfpSubsidy === true) cacfpSubsidy = "Free";

			let personDetail = {
				name: person && (person.lastName + ", " + person.firstName),
				inActive: person && person.inActive,
				subsidyType: cacfpSubsidy || "Paid",
				lines: _.filter(reportData, (l) => { return l.personId == person._id; }),
				totals: {}
			};
			if(Template.instance().reportDetail.get()){
				personDetail.subsidyReason = (person.profileData && person.profileData.subsidyReason) ? person.profileData.subsidyReason : person.subsidyReason;
				personDetail.race = (person.profileData && person.profileData.racialIdentity) ? person.profileData.racialIdentity : person.racialIdentity;
				personDetail.ethnicity = (person.profileData && person.profileData.ethnicIdentity) ? person.profileData.ethnicIdentity : person.ethnicIdentity;
				personDetail.personId = person._id;
			}
			_.each(mealTypes, (mt) => {
				const mealKey = mt.toLowerCase().replace(" ","");
				const personAmount = _.filter(personDetail.lines, (l) => { return l.meals[mealKey];}).length;
				personDetail.totals[mt] = personAmount;
				output.overallTotals[mt][personDetail.subsidyType] += personAmount;
			});
			return personDetail;
		});
		
		return output;
	},
	"groups": function() {
		return Groups.find({}, {sort: {name: 1}});
	},
	"hasMeal": function(day, mealType, personLines) {
		const mealDate = new moment(day).format("MM/DD/YYYY"),
			dayMeals = _.find( personLines, (pl) => { return pl.date == mealDate;}),
			mealKey = mealType.toLowerCase().replace(" ","");
		
		if (dayMeals) {
			return dayMeals.meals[mealKey];
		}
	},
	"headerSpan": function(days) {
		return days.length + 4;
	},
	"totalSpan": function(days) {
		return days.length + 1;
	},
	"sumOf": function(person, mealType, paymentType) {
		const  subsidyType = person.subsidyType && person.subsidyType.toLowerCase();
		return (paymentType == subsidyType || (paymentType=="paid" && !subsidyType)) ? person.totals[mealType] : 0;
	},
	"grandTotal": function (grandTotals, mealType, paymentType) {
		return grandTotals[mealType][paymentType];
	},
	"showDetails": function () {
		return Template.instance().reportDetail.get();
	},
	"getAttendance": function (day, person) {
		return getAttendance(day, person)
	},
	"getMealTime": function (meal, day, person) {
		return getMealTime(meal, day, person);
	},
	"isOutOfBounds": function (meal, day, person) {
		const attendanceTime = getAttendance(day, person, true);
		const mealTime = getMealTime(meal, day, person, true);
		if (!mealTime) {
			return false;
		}
		if (!attendanceTime && mealTime) {
			return true;
		}
		return !(attendanceTime.checkin <= mealTime && attendanceTime.checkout >= mealTime);
	}

});

const getMealTime = function (meal, day, person, flag = false) {
	const date = new moment(day).format("MM/DD/YYYY");
	const data = Moments.find({date: {$eq: date}, taggedPeople: {$elemMatch: {$eq: person.personId}}}).fetch();
	if (data.length) {
		const matchedMeal = _.find(data, d => d.momentType === 'food' && d.foodType === meal)
		if (!matchedMeal) {
			return null;
		}
		//for purposes of highlighting violations
		if (flag) {
			return moment(matchedMeal.time, 'h:mm a').unix() * 1000;
		}
		return matchedMeal.time;
	}
}

const getAttendance = function (day, person, flag = false) {
	const date = new moment(day).format("MM/DD/YYYY");
	const data = Moments.find({date: {$eq: date}, owner: {$eq: person.personId}}).fetch();
	if (data.length) {
		const checkIn = _.find(data, d => d.momentType === 'checkin');
		const checkOut = _.find(data, d => d.momentType === 'checkout');
		//for purposes of highlighting violations
		if (flag) {
			return {
				checkin: checkIn ? moment(checkIn.time, 'h:mm a').unix() * 1000 : 0,
				checkout: checkOut ? moment(checkOut.time, 'h:mm a').unix() * 1000 : 0,
			}
		}
		return `${checkIn ? checkIn.time : ''} <br> ${checkOut ? checkOut.time : ''}`
	}
}


var fetchMealsReportAggregates = function (instance) {
	Meteor.callAsync("mealsReportDetailAggregates", {
		startDate: instance.reportStartDate.get(),
		endDate: instance.reportEndDate.get(),
		personId: $("#filterPerson").val(),
		groupId: $("#filterGroup").val(),
		reportDetail: instance.reportDetail.get()
	}).then((response) => {
		/* _.each(response, (r) => {
			const p = People.findOne({_id: r.personId});
			if (p) {
				r.name = p.lastName + ", " + p.firstName;
				r.cacfpSubsidy = p.cacfpSubsidy;
			}
		}); */

			const mealPeopleIds = response.map((r)=>{
				return r.personId
			})
			const peopleIds = [... new Set(mealPeopleIds)]

			getPeopleData({"_id":{"$in": peopleIds }}, {sort: {lastName:1, firstName:1}, fields:{_id:1, lastName:1, firstName:1, "profileData.cacfpSubsidy": 1, cacfpSubsidy: 1, inActive: 1, "profileData.subsidyReason": 1, subsidyReason:1, "profileData.racialIdentity": 1, racialIdentity: 1, "profileData.ethnicIdentity": 1, ethnicIdentity: 1 }}).then(res => {
				instance.mealsReportAggregates.set(response);
				instance.resPeopleList.set(res)
			}).catch(err => {
				console.log(err);
			});
			
			
	}).catch((error) => {
		alert(error);
	});
};
