import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService";
import <PERSON> from 'papaparse';
import './reportReservations.html';
import { Groups } from "../../../lib/collections/groups";
import { Moments } from "../../../lib/collections/moments";
import { Reservations } from "../../../lib/collections/reservations";

var resultPeopleList;
Template.reportReservations.created = function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportEndDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.person = new ReactiveVar("");
	this.group = new ReactiveVar("");
	this.personList = new ReactiveVar([]);
	this.resPeopleList = new ReactiveVar([]);
	resultPeopleList = this.resPeopleList;
	

	getPeopleData({"type":"person", inActive:{$ne:true}}, {sort: {lastName:1, firstName:1}, fields: {_id: 1, lastName:1, firstName:1, inActive: 1}}).then(res => {
		this.personList.set(res)
	}).catch(err => {
		console.log(err);
	});

	instance.autorun(function() {
		instance.subscribe('reservationsReportMoments', instance.person.get(), instance.reportStartDate.get(), instance.reportEndDate.get(), instance.group.get());
		instance.subscribe('reservationsReportReservations', instance.person.get(), instance.reportStartDate.get(), instance.reportEndDate.get(), instance.group.get());
	});
}

Template.reportReservations.rendered = function() {
	$('#attendanceStartDate').datepicker({autoclose:true});
	$('#attendanceEndDate').datepicker({autoclose:true});
	
	this.autorun(function() {
		const checkinMoments = Moments.find().fetch();
		const reservationList = Reservations.find().fetch()
		const momentPeopleIds = checkinMoments.map((m) =>  {
			const user = Meteor.users.findOne({ _id: m.createdBy })
			return user && user.personId 
			
		})
		const reservationPeopleIds = reservationList.map(r => r.selectedPerson)
		const peopleIds = [... new Set(momentPeopleIds.concat(reservationPeopleIds))]
		getPeopleData({"_id":{"$in": peopleIds }}, {sort: {lastName:1, firstName:1}, fields: {_id:1, lastName:1, firstName:1, payer: 1, inActive: 1}}).then(res => {
			resultPeopleList.set(res);
		}).catch(err => {
			console.log(err);
		});
	})
}

Template.reportReservations.events({
	"click #btnUpdate": async function() {
		
		var instance = Template.instance();
		instance.reportStartDate.set($("#attendanceStartDate").val());
		instance.reportEndDate.set($("#attendanceEndDate").val());
		instance.person.set($("#filterPerson").val());
		instance.group.set($("#filterGroup").val());

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "reservations"
		});
	},
	"click #btnExportCsv": async function() {
		var csvContent = Papa.unparse(_.map(getAttendees(), function(o) {
			return {
				"Attendee": o.attendeeName,
				"Date": o.attendanceDate,
				"Check-In": o.checkInTime ? (o.checkInTime.time ? o.checkInTime.time : moment(o.checkInTime.createdAt).format("hh:mm a")) : "",
				"Check-Out": o.checkOutTime ? ( o.checkOutTime.time ? o.checkOutTime.time : moment(o.checkOutTime.createdAt).format("hh:mm a")) : "",
				"Elapsed Time": o.elapsedTime,
				"Elapsed Time (Units)": Math.ceil(o.elapsedTime * 60 / 15),
				"Scheduled Length": o.scheduledLength,
				"Checked In By": o.checkedInBy,
				"Payer": o.attendeePayer,
				"Cancellation Reason": o.cancellationReason,
				"Cancellation Comments": o.cancellationComments,
				"Cancellation Date": moment(o.cancellationDate).format("MM/DD/YYYY")
			};
		}));
    	
    	//window.open('data:text/csv;charset=utf-8,' + escape(csvContent), '_self');
    	DownloadFile(csvContent, "reservations.csv");

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "reservations"
		});
	}
});

Template.reportReservations.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formattedEndDate": function(){
		return Template.instance().reportEndDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.createdAt).format("hh:mm a");
	},
	"people": function() {
		return Template.instance().personList.get();
	},
	"groups": function() {
		return Groups.find({}, {sort: {name: 1}});
	},
	"attendees": function() {
		return getAttendees();
	}
});

function getAttendees() {
	const resPeopleList = Template.instance().resPeopleList.get();
	const checkinMoments = Moments.find().fetch();

	const reservationList = Reservations.find().fetch().map(r => {
		const person = resPeopleList[resPeopleList.findIndex(p => p._id === r.selectedPerson)];
		if(!person) return null;
		const endTimeBarrier = r.scheduledDate + 60 * 60 * 24 * 1000;

		const checkInTime = checkinMoments
			.filter(m => m.taggedPeople.includes(r.selectedPerson) && m.momentType === "checkin" && m.createdAt >= r.scheduledDate && m.createdAt < endTimeBarrier)
			.sort((a, b) => {
				const aTime = a.time ? moment(a.time, "HH:mm a").format("HH:mm") : moment(a.createdAt).format("HH:mm");
				const bTime = b.time ? moment(b.time, "HH:mm a").format("HH:mm") : moment(b.createdAt).format("HH:mm");
				return aTime.localeCompare(bTime);
			})
			.shift();

		const checkOutTime = checkinMoments
			.filter(m => m.taggedPeople.includes(r.selectedPerson) && m.momentType === "checkout" && m.createdAt >= r.scheduledDate && m.createdAt < endTimeBarrier)
			.sort((a, b) => {
				const aTime = a.time ? moment(a.time, "HH:mm a").format("HH:mm") : moment(a.createdAt).format("HH:mm");
				const bTime = b.time ? moment(b.time, "HH:mm a").format("HH:mm") : moment(b.createdAt).format("HH:mm");
				return bTime.localeCompare(aTime);
			})
			.pop();

		const checkInUser = checkInTime ? Meteor.users.findOne({ _id: checkInTime.createdBy }) : null;
		const checkInPerson = checkInUser ? resPeopleList[resPeopleList.findIndex(p => p._id === checkInUser.personId)] : null;

		const elapsedTime = checkInTime && checkOutTime ? (
			(moment(checkOutTime.time, "HH:mm a") - moment(checkInTime.time, "HH:mm a")) / 1000 / 60 / 60
		).toFixed(2) : "";

		return {
			attendeeName: person ? `${person.firstName} ${person.lastName}` : "",
			attendanceDate: moment(r.scheduledDate).format("MM/DD/YYYY"),
			attendeePayer: person ? person.payer : "",
			checkInTime,
			checkOutTime,
			elapsedTime,
			elapsedTimeUnits: elapsedTime ? `(${(elapsedTime * 60 / 15).toFixed(2)})` : "",
			scheduledLength: r.scheduledLength,
			checkedInBy: checkInUser ? `${checkInPerson.firstName} ${checkInPerson.lastName}` : "",
			cancellationReason: r.cancellationReason,
			cancellationComments: r.cancellationComments,
			cancellationDate: r.cancellationDate
		};
	});

	return reservationList.filter(res => res !== null);
}
