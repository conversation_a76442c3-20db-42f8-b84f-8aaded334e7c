<template name="reportProfile">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Profile Report
					<span class="text-muted pt-2 font-size-sm d-block">View profile data and filter by select fields.</span>
				</h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="print-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
                {{#unless isSavedMode}}
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
                {{/unless}}
			</div>
		</div>
        <div class="card-body">
            <div class="box">

                <div class="box-body">
                    {{#if isSavedMode}}
                        <div class="row font-weight-bolder text-dark my-4">
                            <div class="col">
                                {{{ savedHeader }}}
                            </div>
                        </div>
                    {{/if}}
                    {{#unless isSavedMode}}
                        <div class="row">
                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label>Type:</label>
                                    <select data-cy="select-person-type" class="form-control" id="filterPersonType">
                                        <option value="person">{{ getEntityTypePerson }}</option>
                                        <option value="family">{{ getEntityTypeFamily }}</option>
                                        <option value="familyAndChild">{{ getEntityTypeFamily }} + {{ getEntityTypePerson }}</option>
                                        <option value="staff">{{ getEntityTypeStaff }}</option>
                                        <option value="admin">{{ getEntityTypeAdmin }}</option>
                                    </select>
                                </div>
                            </div>
                            {{#if hasDesignations}}
                                <div class="col-lg-2">
                                    <div class="form-group">
                                        <label>Designations:</label>
                                        <select data-cy="select-designation" class="form-control" id="filterDesignation">
                                            <option value=""></option>
                                            {{#each d in designations}}
                                                <option value="{{d}}">{{d}}</option>
                                            {{/each}}
                                        </select>
                                    </div>
                                </div>
                            {{/if}}
                            <div class="col-lg-4">
                                <form>
                                    <label>{{#if isFcMode}}Family Field(s){{else}}Field(s){{/if}}</label>

                                    <input data-cy="filter-type" name="filterType" id="filterType">


                                </form>
                            </div>
                            <div class="col-lg-2" style="{{#unless isFcMode}}display: none{{/unless}}"></div>
                            <div class="col-lg-4" style="{{#unless isFcMode}}display: none{{/unless}}">
                                <form>
                                    <label>Child Field(s)</label>
                                    <input name="filterTypeChild" id="filterTypeChild">
                                </form>
                            </div>

                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label>Group:</label>
                                    <select data-cy="select-group" class="form-control" id="filterGroup">
                                        <option value="">All</option>
                                        {{#each groups}}
                                            <option value="{{_id}}">{{name}}</option>
                                        {{/each}}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Org(s):</label><br/>
                                    {{> reportOrgsField }}
                                </div>
                            </div>
                            <div class="col-lg-7 d-flex">
                                {{> reportQueueCheckbox }}
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-lg-2">
                                <div class="checkbox-list">
                                    <label class="checkbox">
                                        <input data-cy="include-inactive-people" type="checkbox" id="chkIncludeInactive"><span></span> Include inactive
                                        people
                                    </label>
                                </div>
                            </div>
                            {{#if showIncludeAuthPickupAndEmergencyOption}}
                                <div class="col-lg-2">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="chkIncludeAuthPickupAndEmergencyOption"> Include
                                            Emergency Contacts and
                                            Authorized Pickups
                                        </label>
                                    </div>
                                </div>
                            {{/if}}
                        </div>
                    {{/unless}}
					<div class="row" id="dvData">
						{{#if reportRows}}

							<div data-cy="matched-items" class="text-center"> <strong>{{rowCount reportRows}}</strong> item(s) matched your query. </div>
							<table data-cy="profile-report-table" class="table ">
								<tbody>
									<tr>
										<th style="width:1%;white-space: nowrap;">#</th>
										<th data-export-value="{{#if isFcMode}}Family {{/if}}Name">
											<a href="#" class="sortable" data-id="">{{#if isFcMode}}Family {{/if}}Name<i
													class="fa {{#if trueIfEq currentSortField ""}}
										{{#if trueIfEq currentSortDirection "asc"}}fa-sort-asc{{else}}fa-sort-desc{{/if}}{{else}}fa-sort{{/if}}"></i></a>
										</th>
                                        {{#if isFcMode }}
                                        <th data-export-value="Child Name">
                                            <a href="#" class="sortable" data-id="childName">Child Name <i
                                                    class="fa {{#if trueIfEq currentSortField "childName"}}
                                                        {{#if trueIfEq currentSortDirection "asc"}}fa-sort-asc{{else}}fa-sort-desc{{/if}}{{else}}fa-sort{{/if}}"></i></a>
                                        </th>
                                        {{/if}}
										{{#each col in reportColumns}}
											<th data-export-value="{{col.prefixedLabel}}"><a href="#" class="sortable" data-id="{{col.prefixedName}}">{{col.prefixedLabel}}
													<i class="fa {{#if trueIfEq currentSortField col.prefixedName}}
										{{#if trueIfEq currentSortDirection "asc"}}fa-sort-asc{{else}}fa-sort-desc{{/if}}{{else}}fa-sort{{/if}}"></i>
												</a></th>
										{{/each}}
									</tr>

									{{# each row in reportRows}}
										<tr>
                                            <td style="width:1%;white-space:nowrap">{{ incremented @index }}.</td>
                                            <td>{{ row.lastName }}, {{ row.firstName }}
                                                {{# if row.middleName }}
                                                    {{ row.middleName }}
                                                {{/ if }}
                                                {{# if row.preferredName }}
                                                    ({{ row.preferredName }})
                                                {{/ if }}
                                                {{# if row.inActive }}
                                                    <span style="color:#ff0000">(i)</span>
                                                {{/ if }}
                                                {{#unless isFcMode}}
                                                {{#if row.authorizedPickup}}<span style="color:#ff0000">(AP)</span>{{/if}} {{#if row.emergencyContact}}<span style="color:#ff0000">(EC)</span>{{/if}}
                                                {{/unless}}
                                            </td>
                                            {{#if isFcMode }}
                                                <td>{{ row.childLastName }}, {{ row.childFirstName }}
                                                    {{# if row.childMiddleName }}
                                                        {{ row.childMiddleName }}
                                                    {{/ if }}
                                                    {{# if row.childPreferredName }}
                                                        ({{ row.childPreferredName }})
                                                    {{/ if }}
                                                    {{# if row.childInactive }}
                                                        <span style="color:#ff0000">(i)</span>
                                                    {{/ if }}
                                                </td>
                                            {{/if}}
											{{#each col in reportColumns}}
												<td>{{rowValue row col.prefixedName}}</td>
											{{/each}}
										</tr>
										{{/each}}

								</tbody>
							</table>
						{{/if}}
					</div><!-- /.box-body -->
				</div><!-- /.box-header -->
			</div>
		</div>
	</div>


</template>