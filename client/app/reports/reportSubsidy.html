<template name="reportSubsidy">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Subsidy Report
				<span class="text-muted pt-2 font-size-sm d-block">View subsidy data.</span></h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				{{#unless isSavedMode}}
				<div data-cy="subsidy-report-update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					{{updateLabel}}
				</div>
				{{/unless}}
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
							{{#if isSavedMode}}
							<div class="row font-weight-bolder text-dark">
								<div class="col">
									{{{ savedHeader }}}
								</div>
							</div>
							{{/if}}
							{{#unless isSavedMode}}
							<div class="row">
								<div class="col-sm-3">
									<div class="form-group">
										<label>Start Date</label>
										<div class="input-group">
											
											<input type="text" class="form-control pull-right" id="reportStartDate">
										</div>
									</div>
								</div>
								<div class="col-sm-3">
									<div class="form-group">
										<label>End Date</label>
										<div class="input-group">
											
											<input type="text" class="form-control pull-right" id="reportEndDate">
										</div>
									</div>
								</div>
								{{#if showOrgSelection}}
								<div class="col-sm-3">
									<div class="form-group">
										<label>Select Orgs</label>
										<div class="input-group">
											{{> announcementOrgsField }}
										</div>
									</div>
								</div>
								{{/if}}
								{{> reportQueueCheckbox }}
							</div>
							{{/unless}}
							<br/>
						
							{{#if reportRows}}
							<h4 class="text-center">Subsidy Report</h4>
							<h5 class="text-center">{{dateRangeLabel}}</h5>
              <h5 data-cy="children-served" class="text-center">Children Served: {{totals.totalChildren}}</h5>
              <h5 data-cy="parents-served" class="text-center">Parents Served: {{totals.totalParents}}</h5>
              <h5 data-cy="single-parents-served" class="text-center">Single Parents Served: {{totals.totalSingleParents}}</h5>
              <h5 data-cy="families-served" class="text-center">Families Served: {{totals.totalFamilies}}</h5>
							<br/>
							<table class="table" id="subsidy-report-table" style="display:block;overflow-x:auto;padding-bottom:10px;">
								<tr>
									<th class='no-wrap'>Name</th>
									<th class='no-wrap'>Type</th>
                  <th class='no-wrap'>Child Relationships</th>
									<th class='no-wrap'>Enrollment Date</th>
                  <th class='no-wrap'>Withdraw Date</th>
									<th class='no-wrap'>Center</th>
                  <th class='no-wrap'>Deactivated</th>
                  <th class='no-wrap'>Deactivated Reason</th>
                  <th class='no-wrap'>Service Need</th>
                  <th class='no-wrap'>Single Parent</th>
                  <th class='no-wrap'>Attended Parent Conference</th>
                  <th class='no-wrap'>Health Assessment Date</th>
                  <th class='no-wrap'>Health Assessment Outcome</th>
                  <th class='no-wrap'>May Assessment Date</th>
                  <th class='no-wrap'>November Assessment Date</th>
                  <th class='no-wrap'>Bus Rider 2020</th>
                  <th class='no-wrap'>Income Category</th>
                  <th class='no-wrap'>Household Size</th>
                  <th class='no-wrap'>Ethnic Identity</th>
                  <th class='no-wrap'>Racial Identity</th>
                  <th class='no-wrap'>Primary Classroom</th>
                  <th class='no-wrap'>Program</th>
                  <th class='no-wrap'>Withdrawal Reason</th>
                  <th class='no-wrap'>Other Withdrawal</th>
								</tr>
								{{#each reportRows}}
									<tr>
										<td class='no-wrap'>{{firstName}} {{lastName}}</td>
										<td class='no-wrap'>{{type}}</td>
                    {{#if isFamily type}}
                      <td class='no-wrap'></td>
                      <td class='no-wrap'></td>
                      <td class='no-wrap'></td>
                      <td class='no-wrap'>{{orgName}}</td>
                      <td class='no-wrap'></td>
                      <td class='no-wrap'></td>
                      <td class='no-wrap'>{{getProfileValue profileData "serviceNeed"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "singleParent"}}</td>
                    {{else}}
                      <td class='no-wrap'>{{parents}}</td>
                      <td class='no-wrap'>{{enrollmentDate}}</td>
                      <td class='no-wrap'>{{withdrawDate}}</td>
                      <td class='no-wrap'>{{orgName}}</td>
                      <td class='no-wrap'>{{deactivated}}</td>
                      <td class='no-wrap'>{{deactivationReason}}</td>
                      <td class='no-wrap'></td>
                      <td class='no-wrap'></td>
                      <td class='no-wrap'>{{getProfileValue profileData "additionalProfileInformation.attendedParentConference" "date"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "additionalProfileInformation.healthAssessmentDate2020" "date"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "additionalProfileInformation.healthAssessmentOutcome2020"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "additionalProfileInformation.mayISTARAssessmentDate2020" "date"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "additionalProfileInformation.novemberISTARAssessmentDate2020" "date"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "additionalProfileInformation.busRiderFamily"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "familyInformation.incomeCategory"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "familyInformation.householdSize"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "familyInformation.ethnicIdentity"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "familyInformation.racialIdentity"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "programInfo.primaryClassroom"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "programInfo.program"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "programInfo.withdrawalReason"}}</td>
                      <td class='no-wrap'>{{getProfileValue profileData "programInfo.otherWithdrawal"}}</td>
                    {{/if}}
									</tr>
								{{/each}}
							</table>
							{{/if}}
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</template>
