<template name="reportClassListSchedule">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Capacity Planning Report
				<span class="text-muted pt-2 font-size-sm d-block">Understand current headcounts, available openings, and classroom utilization rates.</span></h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
					Update
				</div>
			</div>
		</div>
		<div class="card-body" id="classlistschedule-body">
			<div class="box">

				<div class="box-body">
				  <div class="row">
					<div class="col-4">
						<div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								<input type="text" class="form-control pull-right" id="reportStartDate" value="{{formattedStartDate}}">
							</div>
						</div>
					</div>
					<div class="col-4">
						<div class="form-group">
							<label>Group</label><br/>
							<select id="reportGroupId" class="form-control">
								<option value="">All</option>
								{{#each group in groupsList}}
								<option value="{{group._id}}">{{group.name}}</option>
								{{/each}}
							</select>
						</div>
					</div>
					<div class="col-4 text-right">
						<b>Jump to:</b><br/>
						<a href="/reports/reportClassList" target="_blank">Enrollment Forecast Report</a>
					</div>
					<!--
										<div class="col-md-2 form-group">
											<div class="checkbox-list">
												<label class="checkbox">
													<input type="checkbox" value="includeChildDetails" id='includeChildDetails'> <span></span> Include Child Details
												</label>
											</div>
										</div>
									-->
				  </div>
				  <div class="row">
					<div class="col-4">
						<div class="checkbox-list">
							<label class="checkbox checkbox-primary">
							  <input type="checkbox" class="form-check-input" name="only-current-schedules"  >
							  <span></span>
							  Use current schedules only
							</label>
						</div>
					</div>
					{{#if groups}}
						{{#unless isGroupSelected}}
						<div class="col-8" style="text-align:right;">
							{{#unless hidePrevButtonFlag}}
								<span class="page-item-set" style="cursor:pointer;" data-action="subtract" data-cy="cp-pagination-previous-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
							{{/unless}}
							<span class="font-size-h2" data-cy="cp-pagination-records-count">Page {{getItemCountStart}} of {{getItemCountEnd}}</span>
							{{#unless hideNextButtonFlag}}
								<span class="page-item-set" style="cursor:pointer;" data-action="add" data-cy="cp-pagination-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
							{{/unless}}
						</div>
						{{/unless}}
					{{/if}}
				  </div>			
				</div>
				{{#if groups}}

				<div class="box-body no-padding" id="dvData">
				  <table class="table" id="classlistschedule-report-table" style="display:block;overflow-x:auto;">
					<thead>
						<tr class="tr-fixed">
							<th rowspan="2" class="align-bottom">Name</th>
							<th rowspan="2" class="align-bottom">Birthday</th>
							<th rowspan="2" class="align-bottom">Age</th>
							<th rowspan="2" class="align-bottom">Transition</th>
							<th rowspan="2" class="align-bottom">Destination</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Mon</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Tue</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Wed</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Thu</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Fri</th>
							<th rowspan="2" class="align-bottom">Plan Rate</th>
							<th rowspan="2" class="align-bottom">Discount</th>
							<th rowspan="2" class="align-bottom">Current Revenue</th>
							<th rowspan="2" class="align-bottom">Potential FT Revenue</th>
							<th rowspan="2" class="align-bottom">Variance</th>
							
						</tr>
					
					
						<tr class="tr-fixed">
							{{#each day in days}}
								{{#each slot in slots}}
									<th class="align-bottom day-slot second-fixed" style="text-align:center;font-size:11px">{{slot.type}}</th>
								{{/each}}
							{{/each}}
						</tr>
					</thead>
					<tbody>
						<!-- for csv export -->
						<tr class="d-none">
							<th rowspan="2" class="align-bottom">Name</th>
							<th rowspan="2" class="align-bottom">Birthday</th>
							<th rowspan="2" class="align-bottom">Age</th>
							<th rowspan="2" class="align-bottom">Transition</th>
							<th rowspan="2" class="align-bottom">Destination</th>
							{{#each day in days}}
								{{#each slot in slots}}
									<th class="align-bottom day-slot second-fixed" style="text-align:center;font-size:11px">{{day.capitalizeFirstLetter}} {{slot.type}}</th>
								{{/each}}
							{{/each}}
							<th rowspan="2" class="align-bottom">Plan Rate</th>
							<th rowspan="2" class="align-bottom">Discount</th>
							<th rowspan="2" class="align-bottom">Current Revenue</th>
							<th rowspan="2" class="align-bottom">Potential FT Revenue</th>
							<th rowspan="2" class="align-bottom">Variance</th>
						</tr>
						<!-- end for csv export -->
					{{#unless shouldHideGroup}}
						{{#each group in groups}}
						
							<tr>
							
							<td colspan=50><b>{{group.name}}</b><br/>
							
								Class Ratio: {{group.ratio}} |
								Age Range: {{group.ageRangeLabel}} | 
								Weekly FTE %: {{formatNumber group.weeklyFTEPercent "0.0"}}% | 
								Weekly FTE: {{group.averageFTE }} | 
								Staff Needed: {{group.staffNeeded}}
							
							</td>

							</tr>
							
						
							{{#each person in group.groupPeople}}
							<tr>

							<td class='text-nowrap' style="font-weight:bold;"><a href="/people/{{person._id}}" target="_blank">{{person.name}} {{#if person.inActive}}<span style="color:#ff0000"> (i)</span>{{/if}}</a> <a href="/people/{{person._id}}?printable=true#reservations" class="btnPersonPopup"><i class="flaticon2-menu-1" aria-hidden="true"></i></a></td>
							<td >{{formatDate person.birthday "M/DD/YY"}}</td>
							<td class='text-nowrap'>{{person.age}}</td>
							<td >{{formatDate person.transition "M/DD/YY"}}</td>
							<td class='text-nowrap'>{{person.destination}}</td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-size:10px;vertical-align:middle;text-align:center">{{slotValue person slot._id day false}}</td>
								{{/each}}
							{{/each}}
							<td style="text-align:right">{{formatCurrency person.tuition}}</td>
							<td style="text-align:right">{{formatCurrency person.discount}}</td>
							<td style="text-align:right">{{formatCurrency person.currentRevenue}}</td>
							<td style="text-align:right">{{formatCurrency person.fullTuition}}</td>
							<td style="text-align:right">{{formatCurrency person.variance}}</td>
							
							</tr>
							{{#if showSameGroupTransition person}}
							<tr>
								<td class='text-nowrap' style="font-weight:bold;">{{person.name}} (change)</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								{{#each day in days}}
									{{#each slot in slots}}
										<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-size:10px;vertical-align:middle;text-align:center">{{slotValue person slot._id day true}}</td>
									{{/each}}
								{{/each}}
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>
							{{/if}}
							{{/each}}		
							
							<tr>
								<td>Total Enrolled</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								{{#each day in days}}
									{{#each slot in slots}}
										<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{slotTotal group slot._id day}}</td>
									{{/each}}
								{{/each}}
								<td style="text-align:right">{{formatCurrency group.totalTuition}}</td>
								<td style="text-align:right">{{formatCurrency group.totalDiscount}}</td>
								<td style="text-align:right">{{formatCurrency group.totalCurrentRevenue}}</td>
								<td style="text-align:right">{{formatCurrency group.totalFullTuition}}</td>
								<td style="text-align:right">{{formatCurrency group.totalVariance}}</td>
								
							</tr>
							
							<tr>
								<td>Total Capacity</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								{{#each day in days}}
									{{#each slot in slots}}
										<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{getDailyCapacity group day}}</td>
									{{/each}}
								{{/each}}
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
							</tr>

							<tr>
								<td style="font-weight:bold">Available Slots</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								{{#each day in days}}
									{{#each slot in slots}}
										<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-weight:bold;text-align:center;{{#if isNegative (slotAvailable group slot._id day)}}color:#ff0000{{/if}}">{{slotAvailable group slot._id day}}</td>
									{{/each}}
								{{/each}}
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								<td style="text-align:right"></td>
								
							</tr>
							<tr>
								<td colspan=50>
							
									Total Enrolled: {{group.totalEnrolled}} |
									Total Capacity: {{group.totalCapacity}} | 
									Weekly Enrollment %: {{formatNumber group.weeklyEnrollmentPercent "0.0"}}% 
							
								</td>
							</tr>
						
						{{/each}}
					{{/unless}}
						<tr><td colspan="50"> </td></tr>
					{{#if isSummarySection}}	
						{{#each groupType in groupTypes}}
								<tr class="d-none"><td colspan="50"> </td></tr>
								<tr>
									<td colspan="50" style="font-weight:bold">{{groupType.name.capitalizeFirstLetter}}</td>
								</tr>
								<tr>
									<td>Total Enrolled</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getGroupTypeCount groupType day slot._id "enrolled"}}</td>
										{{/each}}
									{{/each}}
								</tr>
								<tr>
									<td>Total Capacity</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getGroupTypeCount groupType day slot._id "capacity"}}</td>
										{{/each}}
									{{/each}}
								</tr>
								<tr>
									<td style="font-weight:bold">Available Spots</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-weight:bold;text-align:center">{{getGroupTypeCount groupType day slot._id "available"}}</td>
										{{/each}}
									{{/each}}
								</tr>
								<tr>
									<td>Staff Needed</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{getGroupTypeCount groupType day slot._id "staff"}}</td>
										{{/each}}
									{{/each}}
								</tr>
						{{/each}}
						<tr><td colspan="50"> </td></tr>
						<tr>
							<td>Total Enrolled (All)</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getAllCount day slot._id "enrolled"}}</td>
								{{/each}}
							{{/each}}
						</tr>
						<tr>
							<td>Total Capacity (All)</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getAllCount day slot._id "capacity"}}</td>
								{{/each}}
							{{/each}}
						</tr>
						<tr>
							<td>% Enrolled</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{formatNumber (getPercentEnrolled day slot._id) "0.00"}}%</td>
								{{/each}}
							{{/each}}
						</tr>
						<tr ><td colspan=50> </td></tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Overall %</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatNumber (getOverallPercentEnrolled) "0.00"}}%</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Overall Current Revenue</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatCurrency (getTuitionAmount "current")}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Overall Potential FT Revenue</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatCurrency (getTuitionAmount "full")}}</td>
						</tr>
						<tr ><td colspan=50> </td></tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Discounts</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatCurrency (getSummaries.totalDiscounts)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Current Revenue</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummaries.totalCurrentRevenue)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Plan Rate</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummaries.totalFullTimeRevenue)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Variance</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummaries.totalVariance)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Ideal Revenue</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummaries.idealRevenue)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Variance - Ideal Revenue</td>
							<td style="text-align:right" colspan=2>{{formatCurrency getSummaries.varianceToIdealRevenue}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Payroll</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummaries.totalPayroll)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Payroll %</td>
							<td style="text-align:right" colspan=2>{{formatNumber (getSummaries.totalPayrollPercentage) "0.0"}}%</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Target Payroll %</td>
							<td style="text-align:right" colspan=2>{{formatNumber (getSummaries.targetPayrollPercentage) "0.0"}}%</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Target Payroll Variance</td>
							<td style="text-align:right;{{#if isNegative getSummaries.targetPayrollVariance}}color:#ff0000{{/if}}" colspan=2>{{formatNumber (getSummaries.targetPayrollVariance) "0.0"}}%</td>
						</tr>
					{{/if}}
					</tbody>
				</table>
				<script language="javascript">cltableCb()</script>
				</div>
				{{/if}}
				{{#if groups true}}

				<div style="display: none;" class="box-body no-padding" id="dvDataCsv">
				  <table class="table" id="classlistschedule-report-table" style="display:block;overflow-x:auto;">
					<thead>
						<tr class="tr-fixed">
							<th rowspan="2" class="align-bottom">Name</th>
							<th rowspan="2" class="align-bottom">Birthday</th>
							<th rowspan="2" class="align-bottom">Age</th>
							<th rowspan="2" class="align-bottom">Transition</th>
							<th rowspan="2" class="align-bottom">Destination</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Mon</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Tue</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Wed</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Thu</th>
							<th colspan="{{slotsCount}}" style="text-align:center">Fri</th>
							<th rowspan="2" class="align-bottom">Plan Rate</th>
							<th rowspan="2" class="align-bottom">Discount</th>
							<th rowspan="2" class="align-bottom">Current Revenue</th>
							<th rowspan="2" class="align-bottom">Potential FT Revenue</th>
							<th rowspan="2" class="align-bottom">Variance</th>
							
						</tr>
					
					
						<tr class="tr-fixed">
							{{#each day in days}}
								{{#each slot in slots}}
									<th class="align-bottom day-slot second-fixed" style="text-align:center;font-size:11px">{{slot.type}}</th>
								{{/each}}
							{{/each}}
						</tr>
					</thead>
					<tbody>
						<!-- for csv export -->
						<tr class="d-none">
							<th rowspan="2" class="align-bottom">Name</th>
							<th rowspan="2" class="align-bottom">Birthday</th>
							<th rowspan="2" class="align-bottom">Age</th>
							<th rowspan="2" class="align-bottom">Transition</th>
							<th rowspan="2" class="align-bottom">Destination</th>
							{{#each day in days}}
								{{#each slot in slots}}
									<th class="align-bottom day-slot second-fixed" style="text-align:center;font-size:11px">{{day.capitalizeFirstLetter}} {{slot.type}}</th>
								{{/each}}
							{{/each}}
							<th rowspan="2" class="align-bottom">Plan Rate</th>
							<th rowspan="2" class="align-bottom">Discount</th>
							<th rowspan="2" class="align-bottom">Current Revenue</th>
							<th rowspan="2" class="align-bottom">Potential FT Revenue</th>
							<th rowspan="2" class="align-bottom">Variance</th>
						</tr>
						<!-- end for csv export -->
					{{#each group in groups true}}
					
						<tr>
						
						<td colspan=50><b>{{group.name}}</b><br/>
						
							Class Ratio: {{group.ratio}} |
							Age Range: {{group.ageRangeLabel}} | 
							Weekly FTE %: {{formatNumber group.weeklyFTEPercent "0.0"}}% | 
							Weekly FTE: {{group.averageFTE }} | 
							Staff Needed: {{group.staffNeeded}}
						
						</td>

						</tr>
						
					
						{{#each person in group.groupPeople}}
						<tr>

						<td class='text-nowrap' style="font-weight:bold;"><a href="/people/{{person._id}}" target="_blank">{{person.name}} {{#if person.inActive}}<span style="color:#ff0000"> (i)</span>{{/if}}</a> <a href="/people/{{person._id}}?printable=true#reservations" class="btnPersonPopup"><i class="flaticon2-menu-1" aria-hidden="true"></i></a></td>
						<td >{{formatDate person.birthday "M/DD/YY"}}</td>
						<td class='text-nowrap'>{{person.age}}</td>
						<td >{{formatDate person.transition "M/DD/YY"}}</td>
						<td class='text-nowrap'>{{person.destination}}</td>
						{{#each day in days}}
							{{#each slot in slots}}
								<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-size:10px;vertical-align:middle;text-align:center">{{slotValue person slot._id day false}}</td>
							{{/each}}
						{{/each}}
						<td style="text-align:right">{{formatCurrency person.tuition}}</td>
						<td style="text-align:right">{{formatCurrency person.discount}}</td>
						<td style="text-align:right">{{formatCurrency person.currentRevenue}}</td>
						<td style="text-align:right">{{formatCurrency person.fullTuition}}</td>
						<td style="text-align:right">{{formatCurrency person.variance}}</td>
						
						</tr>
						{{#if showSameGroupTransition person}}
						<tr>
							<td class='text-nowrap' style="font-weight:bold;">{{person.name}} (change)</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-size:10px;vertical-align:middle;text-align:center">{{slotValue person slot._id day true}}</td>
								{{/each}}
							{{/each}}
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
						</tr>
						{{/if}}
						{{/each}}		
						
						<tr>
							<td>Total Enrolled</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{slotTotal group slot._id day}}</td>
								{{/each}}
							{{/each}}
							<td style="text-align:right">{{formatCurrency group.totalTuition}}</td>
							<td style="text-align:right">{{formatCurrency group.totalDiscount}}</td>
							<td style="text-align:right">{{formatCurrency group.totalCurrentRevenue}}</td>
							<td style="text-align:right">{{formatCurrency group.totalFullTuition}}</td>
							<td style="text-align:right">{{formatCurrency group.totalVariance}}</td>
							
						</tr>
						
						<tr>
							<td>Total Capacity</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{getDailyCapacity group day}}</td>
								{{/each}}
							{{/each}}
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
						</tr>

						<tr>
							<td style="font-weight:bold">Available Slots</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-weight:bold;text-align:center;{{#if isNegative (slotAvailable group slot._id day)}}color:#ff0000{{/if}}">{{slotAvailable group slot._id day}}</td>
								{{/each}}
							{{/each}}
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							<td style="text-align:right"></td>
							
						</tr>
						<tr>
							<td colspan=50>
						
								Total Enrolled: {{group.totalEnrolled}} |
								Total Capacity: {{group.totalCapacity}} | 
								Weekly Enrollment %: {{formatNumber group.weeklyEnrollmentPercent "0.0"}}% 
						
							</td>
						</tr>
					
					{{/each}}
						<tr><td colspan="50"> </td></tr>
						
						{{#each groupType in groupTypes true}}
								<tr class="d-none"><td colspan="50"> </td></tr>
								<tr>
									<td colspan="50" style="font-weight:bold">{{groupType.name.capitalizeFirstLetter}}</td>
								</tr>
								<tr>
									<td>Total Enrolled</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getGroupTypeCount groupType day slot._id "enrolled"}}</td>
										{{/each}}
									{{/each}}
								</tr>
								<tr>
									<td>Total Capacity</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getGroupTypeCount groupType day slot._id "capacity"}}</td>
										{{/each}}
									{{/each}}
								</tr>
								<tr>
									<td style="font-weight:bold">Available Spots</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}" style="font-weight:bold;text-align:center">{{getGroupTypeCount groupType day slot._id "available"}}</td>
										{{/each}}
									{{/each}}
								</tr>
								<tr>
									<td>Staff Needed</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									{{#each day in days}}
										{{#each slot in slots}}
											<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{getGroupTypeCount groupType day slot._id "staff"}}</td>
										{{/each}}
									{{/each}}
								</tr>
						{{/each}}
						<tr><td colspan="50"> </td></tr>
						<tr>
							<td>Total Enrolled (All)</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getAllCount day slot._id "enrolled" true}}</td>
								{{/each}}
							{{/each}}
						</tr>
						<tr>
							<td>Total Capacity (All)</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}"  style="text-align:center">{{getAllCount day slot._id "capacity" true}}</td>
								{{/each}}
							{{/each}}
						</tr>
						<tr>
							<td>% Enrolled</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							{{#each day in days}}
								{{#each slot in slots}}
									<td class="day-slot-value {{hasBorder @index slots.length}}" style="text-align:center">{{formatNumber (getPercentEnrolled day slot._id true) "0.00"}}%</td>
								{{/each}}
							{{/each}}
						</tr>
						<tr ><td colspan=50> </td></tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Overall %</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatNumber (getOverallPercentEnrolled true) "0.00"}}%</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Overall Current Revenue</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatCurrency (getTuitionAmount "current" true)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Overall Potential FT Revenue</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatCurrency (getTuitionAmount "full" true)}}</td>
						</tr>
						<tr ><td colspan=50> </td></tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Discounts</td>
							<td style="text-align:right;padding-left:40px" colspan=2>{{formatCurrency (getSummariesCsv.totalDiscounts)}}</td>
						</tr>format
						<tr>
							<td style="font-weight:bold" colspan=2>Total Current Revenue</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummariesCsv.totalCurrentRevenue)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Plan Rate</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummariesCsv.totalFullTimeRevenue)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Variance</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummariesCsv.totalVariance)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Ideal Revenue</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummariesCsv.idealRevenue)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Variance - Ideal Revenue</td>
							<td style="text-align:right" colspan=2>{{formatCurrency getSummariesCsv.varianceToIdealRevenue}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Payroll</td>
							<td style="text-align:right" colspan=2>{{formatCurrency (getSummariesCsv.totalPayroll)}}</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Total Payroll %</td>
							<td style="text-align:right" colspan=2>{{formatNumber (getSummariesCsv.totalPayrollPercentage) "0.0"}}%</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Target Payroll %</td>
							<td style="text-align:right" colspan=2>{{formatNumber (getSummariesCsv.targetPayrollPercentage) "0.0"}}%</td>
						</tr>
						<tr>
							<td style="font-weight:bold" colspan=2>Target Payroll Variance</td>
							<td style="text-align:right;{{#if isNegative getSummariesCsv.targetPayrollVariance}}color:#ff0000{{/if}}" colspan=2>{{formatNumber (getSummariesCsv.targetPayrollVariance) "0.0"}}%</td>
						</tr>
					
					</tbody>
				</table>
				<script language="javascript">cltableCb()</script>
				</div>
				{{/if}}
			  </div>

		</div>
	</div>
</template>

<template name="_personPopup">
	<div id="_personPopup" class="modal fade">
		<div class="modal-dialog modal-dialog-scrollable modal-xl" style=" height: 100% !important;" >
			<div class="modal-content" style="height: auto; min-height: 100%; border-radius: 0;">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">Quick View</h5>
					<div class="d-flex align-items-center">
					<div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
						<span class="fad-regular fad-primary fad fa-times"></span>
					</div>
					</div>
				</div>
				<div class="modal-body bg-white">
					<div style="display: flex; width: 100%; height: 100%; flex-direction: column; overflow: hidden;">
						
						<iframe src="{{location}}" style="flex-grow: 1; border: none; margin: 0; padding: 0;">
						</iframe>
					</div>
				</div>
				<div class="modal-footer">
					<!-- NOTE: the save button should execute on the yielded _personAccount JS -->
					<div class="btn btn-secondary font-weight-bolder" data-dismiss="modal" id="btnCloseRefresh">Close &amp; Refresh Report</div>
					<div class="btn btn-primary font-weight-bolder" data-dismiss="modal">Close</div>
				</div>
			</div>
		</div>
	</div>
</template>