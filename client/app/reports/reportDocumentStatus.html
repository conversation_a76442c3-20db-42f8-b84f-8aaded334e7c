<template name="reportDocumentStatus">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Document Summary
				<span class="text-muted pt-2 font-size-sm d-block">View status of documents by person.</span></h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="print-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				
			
				<div class="box-body roster-report {{#if singlePageMode}}page-break{{/if}}">
					  
					<div class="row" id="dvData">
						<div class="col-12">
				
							<div class="row">
								<div class="col-sm-4">
									<label>Group:</label><br/>
									<select data-cy="select-group" id="selectedGroup" class='form-control'>
										<option value="">All</option>
										{{#each group in groups}}
										<option value="{{group._id}}">{{group.name}}</option>
										{{/each}}
									</select>
								</div>
							</div>
							<br/>
							<div class="row">
									<div class="col-sm-4">
										<label>Sort:</label><br/>
										<select data-cy="select-sort" id="selectedSort" class='form-control'>
											<option value="approved_date">Date Approved</option>
											<option value="submitted_date">Date Submitted</option>
											<option value="rejected_date">Date Rejected</option>
											<option value="document_name">Document Name</option>
											<option value="person_name" selected>Person Name</option>
										</select>
									</div>
								</div>
							<br/>
							<div class="row">
								<div class="col-sm-12">
									<div class="checkbox">
										<label><input data-cy="show-incomplete-only" type="checkbox" name="show-incomplete-only"> Only Show Incomplete</label>
									</div>
									<div class="checkbox">
										<label>
											<input data-cy="include-inactive-people" type="checkbox" id="chkIncludeInactive"> Include inactive people
										</label>
									</div>
                                    {{#if showIncludeWaitList}}
                                        <div class="checkbox">
                                            <label>
                                                <input data-cy="include-wait-list" type="checkbox" id="chkIncludeWaitList" checked> Include Wait List
                                            </label>
                                        </div>
                                    {{/if}}
                                    <br/>
							
								</div>
							</div>
							<br/>
							

							{{#if reportRows}}
							<div id="reportData">
								{{#if reportTitle}}<h3 style="padding-left:5px">{{reportTitle}}</h3>{{/if}}
								
									
									<div id="dvData">
										<table data-cy="documents-summary-table" class="table ">
											<tbody>
												<tr>
													<th>Name </th>
													<th>Document</th>
													<th>Status</th>
													<th>Submitted</th>
													<th>Rejected</th>
													<th>Approved</th>
												</tr>
											
												
												{{#each document in reportRows}}
												<tr>
													<td data-cy="person-name">{{document.personDetail.lastName}}, {{document.personDetail.firstName}} {{#if isWaitList document}}<span style="color:#ff0000">(W)</span>{{/if}}</td>
													<td data-cy="document-definition">{{document.definition.name}}</td>
													<td data-cy="document-status">{{document.status}}</td>
													<td data-cy="submitted-date">{{formatDate document.matchedItem.createdAt "MM/DD/YYYY"}}</td>
													<td data-cy="rejected-date">{{formatDate document.matchedItem.rejectedAt "MM/DD/YYYY"}}</td>
													<td data-cy="approved-date">{{formatDate document.matchedItem.approvedAt "MM/DD/YYYY"}}</td>
												</tr>
												
												{{/each}}
											
											</tbody>
										</table>
									</div>
									
									
							</div>
							{{/if}}
						</div>
					</div>
							
				</div>
			</div>
		</div>
	</div>


</template>
