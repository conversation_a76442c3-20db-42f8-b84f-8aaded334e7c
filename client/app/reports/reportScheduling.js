import <PERSON> from 'papaparse';
import './reportScheduling.html';

Template.reportScheduling.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
}

Template.reportScheduling.rendered = function() {
	$('#schedulingStartDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
}

Template.reportScheduling.events({
	"click #btnUpdate": async function() {
		var scheduledDate = $("#schedulingStartDate").val();
		if (scheduledDate == "") return;		
		var scheduledDateNum = new moment(scheduledDate, "MM/DD/YYYY").valueOf();
		var instance = Template.instance();
		Meteor.callAsync("schedulingReportAggregates", { scheduledDate: scheduledDateNum}).then((response) => {
			instance.reportData.set(response);	
		}).catch((err) => {
			instance.reportData.set(undefined);	
		});

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "scheduling"
		});
	},
	"click #btnExportCsv": async function() {
		var timeslots = Template.instance().reportData.get();
		if (!timeslots) return;
		var csvContent = Papa.unparse(_.map(timeslots, function(o) {
			return {
				timeSlot: new moment(o.momentSlotStart).format("h:mm a"),
		        level1People: o.peopleCount.level1,
		        level2People: o.peopleCount.level2,
		        level3People: o.peopleCount.level3,
		        requiredEmployees: o.requiredEmployees,
		        staffCount: o.staffCount,
		        surplusDeficit: o.surplusDeficit
			};
		}));
    	
    	//window.open('data:text/csv;charset=utf-8,' + escape(csvContent), '_self');
    	DownloadFile(csvContent, "schedule.csv");

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "scheduling"
		});
	}
});

Template.reportScheduling.helpers({
	"formattedTime": function(rawDate){
		return moment(rawDate).format("h:mm a");
	},
	
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.createdAt).format("hh:mm a");
	},
	"timeslots": function() {
		
		return Template.instance().reportData.get();
	}
});
