<template name="reportOrgsField">
	{{#if availableOrgs}}
	{{> reportOrgsFieldDropdown orgs=availableOrgs isFullwidth=fullwidth opts=opts}}
	{{else if isLoading}}
	<i>Loading...</i>
	{{else}}
	<span data-cy="report-orgs-field-name">{{orgName}}</span>
	{{/if}}
</template>

<template name="reportOrgsFieldDropdown">
	<div data-cy="report-orgs-field" class="report-orgs-field {{#if isFullwidth}}fullwidth{{/if}}">
		<select data-cy="report-orgs" name='reportOrgs' id="reportOrgs" multiple class="form-control">
			{{#each orgs}}
				<option value="{{_id}}">{{name}}</option>
			{{/each}}
		</select>
	</div>
</template>