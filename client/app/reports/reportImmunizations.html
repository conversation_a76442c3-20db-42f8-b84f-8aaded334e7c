<template name="reportImmunizations">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Immunizations Report
				<span class="text-muted pt-2 font-size-sm d-block">View upcoming and past due immunizations.</span></h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="print-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
							<div class="row">

								<div class="col-sm-3">
									<label>Group:</label><br/>
									<select data-cy="select-group" id="selectedGroup" class='form-control'>
										<option value="">All</option>
										{{#each group in groups}}
										<option value="{{group._id}}">{{group.name}}</option>
										{{/each}}
									</select>
								</div>
							</div>
							<div class="row mt-6">
								<div class="col-sm-12 form-group">
									<div class="checkbox-list">
										<div class="checkbox">
											<label><input data-cy="show-overdue-only" type="checkbox" id="chkShowOverdueOnly"><span></span>Show overdue only</label>
										</div>
									</div>
								</div>
							</div>

							<br/>
						
							{{#if reportRows}}
							<h4 class="text-center">Immunizations Report</h4>
							<h5 class="text-center">{{dateLabel}}</h5>
							<br/>
							<table class="table">
								<tr style="border-bottom: 1px solid #000 ">
									<th>Name</th>
									<th>Immunization</th>
									<th>Status</th>
									<th>Due Date</th>
								</tr>
								{{#each row in reportRows}}
									<tr >
										<td data-cy="person-name">{{row.name}}</td>
										<td data-cy="immunization-type">{{row.immunizationType}}</td>
										<td data-cy="immunization-status">{{row.status}}</td>
										<td data-cy="due-date">{{row.dueDate}}</td>
									</tr>
								{{/each}}
							</table>
							{{/if}}
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</template>
