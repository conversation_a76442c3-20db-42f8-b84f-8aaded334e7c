import './reportAdp.html';
import <PERSON> from 'papaparse';

Template.reportAdp.onRendered(function() {
    $('#reportStartDate').datepicker({autoclose:true});
    $('#reportEndDate').datepicker({autoclose:true});
});

Template.reportAdp.helpers({
    getStartDate() {
        return new moment().startOf('week').add(-2, "weeks").format("MM/DD/YYYY");
    },
    getEndDate() {
        return new moment().add(-1, 'weeks').endOf('week').format('MM/DD/YYYY');
    } 
});

Template.reportAdp.events({
    "click #btnExportCsv": function (event, instance) {
        // set date range
        const startDate = new moment($("#reportStartDate").val()).toDate();
        const endDate = new moment($("#reportEndDate").val()).toDate();
        Meteor.callAsync('getAdpPayrollReport', {startDate: startDate.valueOf(), endDate: endDate.valueOf()}).then((response) => {
            // download CSV
            const csvData = Papa.unparse(response);
            DownloadFile(csvData, `lineLeaderAdpPayRoll-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.csv`);
            mpSwal.fire("Success", "Successfully downloaded CSV", "success");
        }).catch((error) => {
            mpSwal.fire("Error", error.reason, "error");
        });
    }
});