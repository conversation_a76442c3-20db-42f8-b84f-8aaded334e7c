<template name="reportStaffTime">
	<style>
		.swal2-popup .swal2-file,
		.swal2-popup .swal2-input,
		.swal2-popup .swal2-textarea {
			font-size: 14px;
		}
	</style>
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Staff Time Report - <label>{{getOTFrequency}}</label>
					<span class="text-muted pt-2 font-size-sm d-block">View staff time by classification for time
						tracking and
						payroll.</span>
				</h3>
				<input type="hidden" id="overtimeFrequency" value={{getOTFrequency}} />
			</div>
			<div class="card-toolbar">
				{{#if exportPaycorp}}
				<div class="btn btn-info font-weight-bolder btn-text-white mr-4" id="btnPaycorp">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-white"></i>Toggle Paycor View
				</div>
				{{/if}}
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
                {{#unless isSavedMode}}
                    <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
                        {{{updateLabel}}}
                    </div>
                {{/unless}}
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
                            {{#if isSavedMode}}
                                <div class="row font-weight-bolder text-dark">
                                    <div class="col">
                                        {{{ savedHeader }}}
                                    </div>
                                </div>
                            {{/if}}
                            {{#unless isSavedMode}}
							<div class="row">
								<div class="col-sm-3">
									<div class="form-group">
										<label>Start Date</label>
										<div class="input-group">
											<input type="text" class="form-control pull-right" id="reportStartDate">
										</div>
									</div>
								</div>
								<div class="col-sm-3">
									<div class="form-group">
										<label>End Date</label>
										<div class="input-group">
											<input type="text" class="form-control pull-right" id="reportEndDate">
										</div>
									</div>
								</div>
								<div class="col-sm-3">
									<label>Specific Person:</label><br />
									<select id="selectedPerson" class='form-control'>
										<option value="">All</option>
										{{#each person in people}}
										<option value="{{person._id}}">{{person.lastName}}, {{person.firstName}}
										</option>
										{{/each}}
									</select>
								</div>
								<div class="col-sm-3">
									<div class="form-group">
										<label>Org(s):</label><br />
										{{> reportOrgsField }}
									</div>
								</div>
								<div class="col-sm-3">
									<label>Options: (Works only for current site)</label><br />
									<div class="col-sm-12 form-group">
										<div class="checkbox-list">
											<label class="checkbox checkbox-primary"><input type="checkbox"
													id="chkIncludeDetail"><span></span>Include Detail</label>
											{{#if hasPayDetailPermission}}
											<label class="checkbox checkbox-primary"><input type="checkbox"
													id="chkIncludePayDetail"><span></span>Include Pay Detail</label>
											{{/if}}
											<label class="checkbox checkbox-primary"><input type="checkbox"
													id="chkGroupByStaff"><span></span>Group by Staff</label>

											<label class="checkbox checkbox-primary"><input type="checkbox"
													id="chkQueue"><span></span>Email me when this report is
												complete</label>
										</div>
									</div>
								</div>
							</div>
                            {{/unless}}
							<br/>
							 {{#if isPayCorpActive}} 
								{{#if reportRows}}
									<h4 class="text-center">Paycor Report</h4>
									<h5 class="text-center">{{dateRangeLabel}}</h5>
									<br />
									<table class="table">
									<tr>
										<th>Employee Number</th>
										<th>Regular Hours</th>
										<th>Overtime Hours</th>
										<th>Hours Code</th>
										<th>Coded Hours</th>
									</tr>
									{{#each personTotal in combinedArray}} 
										{{#each payPeriod in personTotal.data}}
											{{#each classification in payPeriod.classificationDetails}}
												<tr>
													<td>{{personTotal.employeeNumber}}</td>
													<td class="font-weight-bolder">{{getRegularHours classification}}</td>
													<td class="font-weight-bolder">{{getOvertimeHours classification}}</td>
													<td class="font-weight-bolder">{{getHoursCode classification}}</td>
													<td class="font-weight-bolder">{{getCodedHours classification}}</td>
												</tr>
											{{/each}} 
										{{/each}} 
									{{/each}}
									</table>
								{{/if}} 
                            {{else}}
                                {{#if reportRows}}
                                    <h4 class="text-center">Staff Time Report</h4>
                                    <h5 class="text-center">{{dateRangeLabel}}</h5>
                                    <br />
                                    <table class="table">
                                        <tr>
                                            <th>Employee</th>
                                            {{#if showPayDetail}}
                                                <th>Employee Classification</th>
                                                <th>Pay Rate</th>
                                                <th>Work Department</th>
                                            {{/if}}
                                            <th>Time Classification</th>
                                            <th>Confirmed</th>
                                            <th style="text-align: right">Time (hours:minutes)</th>
                                            <th style="text-align: right">Total Hours</th>
                                            <th style="text-align: right">Overtime Hours</th>
                                            {{#if hasProjectedOvertime}}
                                                <th style="text-align: right">Projected Overtime Hours</th>
                                            {{else}}
                                                <th></th>
                                            {{/if}}
                                        </tr>
                                        {{#if showGroupByStaff}}
                                            {{#each orgGroup in getGroupByOrgGroupings }}
                                                {{#each personTotal in orgGroup.combinedArray}}
                                                    {{#if showPayDetail}}
                                                        <tr style="border-top: 1px solid #000">
                                                            <td style="font-weight: bold">{{personTotal.name}}</td>
                                                            <td class="">{{getEmployeeClassification payPeriod}}</td>
                                                            <td class="">{{payPeriod.payRate}}</td>
                                                            <td colspan="7" class="">{{payPeriod.workDepartment}}</td>
                                                        </tr>
                                                    {{else}}
                                                        <tr style="border-top: 1px solid #000">
                                                            <td colspan="7" style="font-weight: bold">{{personTotal.name}} {{ getGroupByOrgName orgGroup }}</td>
                                                        </tr>
                                                    {{/if}}
                                                    {{#if showDetails}}
                                                        <tr>
                                                            <td>Date</td>
                                                            <td>Check In</td>
                                                            <td>Check Out</td>
                                                            <td>Time (hours:minutes)</td>
                                                            <td>Total Hours</td>
                                                            <td>Pay Type</td>
                                                        </tr>
                                                        {{#each payPeriod in personTotal.data}}
                                                            {{#each payPeriod.timeCardDetails}}
                                                                <tr>
                                                                    <td>{{date}}</td>
                                                                    <td>{{in}}</td>
                                                                    <td>{{out}}</td>
                                                                    <td>{{formatTimeLabel calculatedMinutes}}</td>
                                                                    <td>{{formatTotalHoursLabel calculatedMinutes}}</td>
                                                                    <td>{{payType}}</td>
                                                                </tr>
                                                            {{/each}}
                                                        {{/each}}
                                                    {{/if}}
                                                    {{#each payPeriod in personTotal.data}}
                                                        {{#each payPeriod.classificationDetails}}
                                                            <tr>
                                                                <td></td>
                                                                {{#if showPayDetail}}
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                {{/if}}
                                                                <td class="">{{label}}</td>
                                                                <td class=""></td>
                                                                <td class="" style="text-align: right">{{hoursMinutesLabel}}</td>
                                                                <td class="" style="text-align: right">
                                                                {{formatNumber totalHours "0.00"}}
                                                                </td>
                                                                <td class="" style="text-align: right">
                                                                {{formatNumber totalOvertimeHours "0.00"}}
                                                                </td>
                                                                <td></td>
                                                            </tr>
                                                        {{/each}}
                                                    {{/each}}
                                                    {{#each payPeriod in personTotal.data}}
                                                        <tr style="border-top: 1px solid #aaa">
                                                            <td style="font-weight: bold">
                                                            Total {{getPeriodDescription payPeriod.week}}
                                                            </td>
                                                            {{#if showPayDetail}}
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            {{/if}}
                                                            <td></td>
                                                            <td></td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{payPeriod.totalHoursMinutesLabel}}
                                                            </td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatNumber payPeriod.totalHours "0.00"}}
                                                            </td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatNumber payPeriod.totalOvertimeHours "0.00"}}
                                                            </td>
                                                            <td></td>
                                                        </tr>
                                                    {{/each}}
                                                    <tr style="border-top: 1px solid #aaa">
                                                        <td style="font-weight: bold">All Weeks Total {{ getGroupByOrgName orgGroup }}</td>
                                                        <td></td>
                                                        {{#if showPayDetail}}
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                        {{/if}}
                                                        <td class="font-weight-bolder" style="text-align: right"></td>
                                                        <td class="font-weight-bolder" style="text-align: right"></td>
                                                        <td class="font-weight-bolder" style="text-align: right">
                                                        {{formatNumber personTotal.combinedTotalHours "0.00"}}
                                                        </td>
                                                        <td class="font-weight-bolder" style="text-align: right">
                                                        {{formatNumber personTotal.combinedTotalOvertimeHours "0.00"}}
                                                        </td>
                                                        <td></td>
                                                    </tr>
                                                    {{#each personTotal.combinedClassificationTotals}}
                                                        <tr style="border-top: 1px solid #aaa">
                                                            <td></td>
                                                            <td class="font-weight-bolder">{{type}} (All Weeks Total)</td>
                                                            <td class="font-weight-bolder" style="text-align: right"></td>
                                                            {{#if showPayDetail}}
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            {{/if}}
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatTimeLabel totalMinutes}}
                                                            </td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatTotalHoursLabel totalMinutes}}
                                                            </td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatTotalHoursLabel totalOvertimeMinutes}}
                                                            </td>
                                                            <td></td>
                                                        </tr>
                                                    {{/each}}
                                                {{/each}}
                                            {{/each}}
                                        {{else}}
                                            <!-- Aqui empieza lo chido -->
                                            {{#if hasMultiplesOrgs}}
                                                {{#each getData}}
                                                {{#let data=this dataIndex=@index}}
                                                    <tr style="background-color: var(--primary); color: #ffffff;">
                                                        <td style="font-weight:bold">{{data.currentOrg.name}} </td>
                                                        <td></td>
                                                        <td></td>
                                                        {{#if showPayDetail}}
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                        {{/if}}
                                                        <td class="font-weight-bolder" style="text-align: right">
                                                            {{ data.grandTotal.totalHoursMinutesLabel }}
                                                        </td>
                                                        <td class="font-weight-bolder" style="text-align: right">
                                                            {{ formatNumber data.grandTotal.totalHours "0.00" }}
                                                        </td>
                                                        <td class="font-weight-bolder" style="text-align: right">
                                                            {{ formatNumber data.grandTotal.totalOvertimeHours "0.00" }}
                                                        </td>
                                                        {{#if hasProjectedOvertime}}
                                                            <td class="font-weight-bolder" style="text-align: right;">
                                                                {{formatNumber data.grandTotal.totalFutureOvertimeHours "0.00"}}
                                                            </td>
                                                        {{else}}
                                                            <td></td>
                                                        {{/if}}
                                                    </tr>
                                                    {{#each weeksDataX2 data}}
                                                        {{#let week=this weekIndex=@index}}
                                                            <tr style="border-top: 1px solid #000; border-bottom: 1px double #000">
                                                                <td style="font-weight: bold">
                                                                All Employees Total (Week {{week.weekLabel}}) - <a class="BT-SW-W" data-id="SW-W{{weekIndex}}-{{dataIndex}}" href="">See More Info</a>

                                                                </td>
                                                                <td></td>
                                                                <td></td>
                                                                {{#if showPayDetail}}
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                {{/if}}
                                                                <td class="font-weight-bolder" style="text-align: right">
                                                                {{week.totals.totalHoursMinutesLabel}}
                                                                </td>
                                                                <td class="font-weight-bolder" style="text-align: right">
                                                                {{formatNumber week.totals.totalHours "0.00"}}
                                                                </td>
                                                                <td class="font-weight-bolder" style="text-align: right">
                                                                {{formatNumber week.totals.totalOvertimeHours "0.00"}}
                                                                </td>
                                                                {{#if hasProjectedOvertime}}
                                                                    <td class="font-weight-bolder" style="text-align: right; color: red">
                                                                    {{formatNumber week.totals.totalFutureOvertimeHours "0.00"}}
                                                                    </td>
                                                                {{else}}
                                                                    <td></td>
                                                                {{/if}}
                                                            </tr>


                                                            {{#each employee in week.employees}}
                                                                {{#if showPayDetail}}
                                                                    <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none; border-top: 1px solid #000;">
                                                                        <td style="font-weight: bold">{{employee.name}}</td>
                                                                        <td class="">{{getEmployeeClassification employee}}</td>
                                                                        <td class="">{{employee.payRate}}</td>
                                                                        <td colspan="5" class="">{{employee.workDepartment}}</td>
                                                                    </tr>
                                                                {{else}}
                                                                    <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;border-top: 1px solid #000;">
                                                                        <td colspan="5" style="font-weight: bold">{{employee.name}}</td>
                                                                    </tr>
                                                                {{/if}}
                                                                {{#if showDetails}}
                                                                    <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;">
                                                                        <td>Date</td>
                                                                        <td>Check In</td>
                                                                        <td>Check Out</td>
                                                                        <td>Time (hours:minutes)</td>
                                                                        <td>Total Hours</td>
                                                                        <td>Pay Type</td>
                                                                    </tr>
                                                                    {{#each employee.timeCardDetails}}
                                                                        <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;">
                                                                            <td>{{date}}</td>
                                                                            <td>{{in}}</td>
                                                                            <td>{{out}}</td>
                                                                            <td>{{formatTimeLabel calculatedMinutes}}</td>
                                                                            <td>{{formatTotalHoursLabel calculatedMinutes}}</td>
                                                                            <td>{{payType}}</td>
                                                                        </tr>
                                                                    {{/each}}
                                                                {{/if}}
                                                                {{#each employee.classificationDetails}}
                                                                    <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;">
                                                                        <td></td>
                                                                        {{#if showPayDetail}}
                                                                            <td></td>
                                                                            <td></td>
                                                                            <td></td>
                                                                        {{/if}}
                                                                        <td class="">{{label}}</td>
                                                                        <td>{{timeCardConfirmed employee}}</td>
                                                                        <td class="" style="text-align: right">{{hoursMinutesLabel}}</td>
                                                                        <td class="" style="text-align: right">
                                                                        {{formatNumber totalHours "0.00"}}
                                                                        </td>
                                                                        <td class="" style="text-align: right">
                                                                        {{formatNumber totalOvertimeHours "0.00"}}
                                                                        </td>
                                                                        <td></td>
                                                                    </tr>
                                                                {{/each}}

                                                                <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none; border-top: 1px solid #aaa;" >
                                                                    <td style="font-weight: bold">Total {{employee.name}}</td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    {{#if showPayDetail}}
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                    {{/if}}
                                                                    <td class="font-weight-bolder" style="text-align: right">
                                                                    {{totalHoursMinutesLabel}}
                                                                    </td>
                                                                    <td class="font-weight-bolder" style="text-align: right">
                                                                    {{formatNumber employee.totalHours "0.00"}}
                                                                    </td>
                                                                    <td class="font-weight-bolder" style="text-align: right">
                                                                    {{formatNumber employee.totalOvertimeHours "0.00"}}
                                                                    </td>
                                                                    {{#if hasProjectedOvertime}}
                                                                        <td class="font-weight-bolder" style="text-align: right; color: red">
                                                                        {{formatNumber employee.futureOT "0.00"}}
                                                                        </td>
                                                                    {{else}}
                                                                        <td></td>
                                                                    {{/if}}
                                                                </tr>
                                                            {{/each}}
                                                        {{/let}}
                                                    {{/each}}
                                                {{/let}}
                                                {{/each}}

                                            {{else}}
                                                <tr style="background-color: #ac52db; color: #ffffff;">
                                                    <td style="font-weight:bold" colspan="11">{{getOrg}} </td>
                                                </tr>
                                                {{#each weeksData}}
                                                    {{#let week=this weekIndex=@index}}
                                                        <tr style="border-top: 1px solid #000; border-bottom: 1px double #000">
                                                            <td style="font-weight: bold">
                                                            All Employees Total (Week {{week.weekLabel}}) - <a class="BT-SW-W" data-id="SW-W{{weekIndex}}-{{dataIndex}}" href="">See More Info</a>

                                                            </td>
                                                            <td></td>
                                                            <td></td>
                                                            {{#if showPayDetail}}
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            {{/if}}
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{week.totals.totalHoursMinutesLabel}}
                                                            </td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatNumber week.totals.totalHours "0.00"}}
                                                            </td>
                                                            <td class="font-weight-bolder" style="text-align: right">
                                                            {{formatNumber week.totals.totalOvertimeHours "0.00"}}
                                                            </td>
                                                            {{#if hasProjectedOvertime}}
                                                                <td class="font-weight-bolder" style="text-align: right; color: red">
                                                                {{formatNumber week.totals.totalFutureOvertimeHours "0.00"}}
                                                                </td>
                                                            {{else}}
                                                                <td></td>
                                                            {{/if}}
                                                        </tr>


                                                        {{#each employee in week.employees}}
                                                            {{#if showPayDetail}}
                                                                <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none; border-top: 1px solid #000;">
                                                                    <td style="font-weight: bold">{{employee.name}}</td>
                                                                    <td class="">{{getEmployeeClassification employee}}</td>
                                                                    <td class="">{{employee.payRate}}</td>
                                                                    <td colspan="5" class="">{{employee.workDepartment}}</td>
                                                                </tr>
                                                            {{else}}
                                                                <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;border-top: 1px solid #000;">
                                                                    <td colspan="5" style="font-weight: bold">{{employee.name}}</td>
                                                                </tr>
                                                            {{/if}}
                                                            {{#if showDetails}}
                                                                <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;">
                                                                    <td>Date</td>
                                                                    <td>Check In</td>
                                                                    <td>Check Out</td>
                                                                    <td>Time (hours:minutes)</td>
                                                                    <td>Total Hours</td>
                                                                    <td>Pay Type</td>
                                                                </tr>
                                                                {{#each employee.timeCardDetails}}
                                                                    <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;">
                                                                        <td>{{date}}</td>
                                                                        <td>{{in}}</td>
                                                                        <td>{{out}}</td>
                                                                        <td>{{formatTimeLabel calculatedMinutes}}</td>
                                                                        <td>{{formatTotalHoursLabel calculatedMinutes}}</td>
                                                                        <td>{{payType}}</td>
                                                                    </tr>
                                                                {{/each}}
                                                            {{/if}}
                                                            {{#each employee.classificationDetails}}
                                                                <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none;">
                                                                    <td></td>
                                                                    {{#if showPayDetail}}
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                    {{/if}}
                                                                    <td class="">{{label}}</td>
                                                                    <td>{{timeCardConfirmed employee}}</td>
                                                                    <td class="" style="text-align: right">{{hoursMinutesLabel}}</td>
                                                                    <td class="" style="text-align: right">
                                                                    {{formatNumber totalHours "0.00"}}
                                                                    </td>
                                                                    <td class="" style="text-align: right">
                                                                    {{formatNumber totalOvertimeHours "0.00"}}
                                                                    </td>
                                                                    <td></td>
                                                                </tr>
                                                            {{/each}}

                                                            <tr class="SW-W{{weekIndex}}-{{dataIndex}}" style="display: none; border-top: 1px solid #aaa;" >
                                                                <td style="font-weight: bold">Total {{employee.name}}</td>
                                                                <td></td>
                                                                <td></td>
                                                                {{#if showPayDetail}}
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                {{/if}}
                                                                <td class="font-weight-bolder" style="text-align: right">
                                                                {{totalHoursMinutesLabel}}
                                                                </td>
                                                                <td class="font-weight-bolder" style="text-align: right">
                                                                {{formatNumber employee.totalHours "0.00"}}
                                                                </td>
                                                                <td class="font-weight-bolder" style="text-align: right">
                                                                {{formatNumber employee.totalOvertimeHours "0.00"}}
                                                                </td>
                                                                {{#if hasProjectedOvertime}}
                                                                    <td class="font-weight-bolder" style="text-align: right; color: red">
                                                                    {{formatNumber employee.futureOT "0.00"}}
                                                                    </td>
                                                                {{else}}
                                                                    <td></td>
                                                                {{/if}}
                                                            </tr>
                                                        {{/each}}
                                                    {{/let}}
                                                {{/each}}
                                            {{/if}}
                                        {{/if}}

                                        {{#if getGrandTotals}}
                                            <tr style="border-top: 48px solid white">
                                                <td style="font-weight: bold"></td>
                                            </tr>
                                            <tr style="border-top: 1px solid #000; border-bottom: 1px double #000">
                                                <td style="font-weight: bold">All Employees Total (All Weeks)</td>
                                                <td></td>
                                                <td></td>
                                                {{#if showPayDetail}}
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                {{/if}}
                                                <td class="font-weight-bolder" style="text-align: right">
                                                {{getGrandTotals.totalHoursMinutesLabel}}
                                                </td>
                                                <td class="font-weight-bolder" style="text-align: right">
                                                {{formatNumber getGrandTotals.totalHours "0.00"}}
                                                </td>
                                                <td class="font-weight-bolder" style="text-align: right">
                                                {{formatNumber getGrandTotals.totalOvertimeHours "0.00"}}
                                                </td>
                                                {{#if hasProjectedOvertime}}
                                                    <td class="font-weight-bolder" style="text-align: right; color: red">
                                                    {{formatNumber getGrandTotals.totalFutureOvertimeHours "0.00"}}
                                                    </td>
                                                {{else}}
                                                    <td></td>
                                                {{/if}}
                                            </tr>
                                        {{/if}}
                                    </table>
								{{/if}} 
							{{/if}}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>