import './reportQueueTemplate.html';

Template.reportQueueCheckbox.helpers({
    showCheckbox() {
        return Template.instance().isMultiOrg.get();
    }
});

Template.reportQueueCheckbox.onCreated( function() {
    const self = this;
    self.isMultiOrg = new ReactiveVar(false);
    const switchableSites = Session.get('switchableSites');
    if (switchableSites && switchableSites.length > 0) {
        self.isMultiOrg.set(switchableSites.length > 1);
    } else {
        Meteor.callAsync('getSwitchableSites').then((result) => {
            Session.set('switchableSites', result);
            if (result) {
                self.isMultiOrg.set(result.length > 1);
            }
        })
    }
});
