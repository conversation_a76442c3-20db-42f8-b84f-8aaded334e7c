<template name="reportBusRoster">
    <style type="text/css" media="print">
        @page { 
            size: landscape;
            display: table-header-group;
        }
        body {
            zoom: 80%;
        }
    </style>
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-3 pb-0">
            {{#unless shouldHideHeaderForPrint}}
			<div class="card-title">
				<h3 class="card-label">Bus Run Roster
				<span class="text-muted pt-2 font-size-sm d-block">Print a roster for bus drivers to track students under their care.</span></h3>
			</div>
				<div class="card-toolbar">
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint" data-cy="print-btn">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Print View
					</div>
					<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
						Update
					</div>
				</div>
			{{/unless}}
		</div>
		<div class="card-body" style="padding-top: 0px;">
			<div class="box">
                <div class="box">
                    <div class="box-body attendance-grid">                          
                        <div class="row" id="dvData">
                            <div class="col-12">
                                {{#if fullLayout}}
                                <div class="row">
                                    <div class="col-sm-4">
                                        <label>Week Of:</label><br/>
                                        <input type="text" class="form-control" id="startMonday" value="{{currentMonday}}" data-cy="start-week-of-input">
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="radio-inline">
                                            <label class="radio radio-primary">
                                                <input type="radio" name="busRosterAmPm" value="am" checked="checked" data-cy="bus-roster-morning-input">
                                                <span></span>
                                                AM
                                            </label>
                                            <label class="radio radio-primary">
                                                <input type="radio" name="busRosterAmPm" value="pm" data-cy="bus-roster-evening-input">
                                                <span></span>
                                                PM
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <br/>
                                {{/if}}
                                {{#if reportRoutes}}
                                    {{#each reportRoutes}}
                                        <div id="reportData">                                      
                                            <table class="table">
                                                <thead style="border-color: black; border: 2px; padding-top: 0px; margin-top: 0px;">
                                                    <tr>
                                                        <td colspan=22 style="text-align: center; border: 2px solid black;">
                                                            <h3>{{org.name}}</h3>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan=2 style="text-align: center; border: 2px solid black;">
                                                            {{org.name}}
                                                        </td>
                                                        <td colspan=12 style="text-align: center; border: 2px solid black;">
                                                            <b>Bus Run Roster</b><br>
                                                            <b>Week of: {{reportStartMonday}}</b>
                                                        </td>
                                                        <td colspan=8 style="text-align: center; border: 2px solid black;">
                                                            {{org.phoneNumber}}<br>
                                                            {{org.replyToAddress}}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <th colspan=2 style="text-align: center; border: 2px solid black;"><h3>{{this.name}}-{{reportAmPmDisplay}}</h3></th>
                                                        {{#each day in days}}
                                                            <th colspan=4 style="text-align: center; border: 2px solid black;">{{day}}</th>
                                                        {{/each}}
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align: center; border: 2px solid black;"></td>
                                                        <td style="text-align: center; border: 2px solid black;">Driver's Name:</td>
                                                        {{#each day in days}}
                                                            <td colspan=4 style="text-align: center; border: 2px solid black;"></td>
                                                        {{/each}}
                                                    </tr>
                                                    <tr>
                                                        <td rowspan=2 style="text-align: center; border: 2px solid black;" >Child's Name</td>
                                                        <td rowspan=2 style="text-align: center; border: 2px solid black;" >DOB</td>
                                                    </tr>
                                                    <tr>
                                                        {{#each day in days}}
                                                            <td style="text-align: center; border: 2px solid black;">(A)<br>Time</td>
                                                            <td style="text-align: center; border: 2px solid black;">✔<br>On</td>
                                                            <td style="text-align: center; border: 2px solid black;">✔<br>Off</td>
                                                            <td style="text-align: center; border: 2px solid black;">(B)<br>Time</td>
                                                        {{/each}}
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {{#each child in children}}
                                                        <tr style="border: 1px solid black">
                                                            <td data-cy="child-name-bus-report" style="border: 1px solid black">{{child.lastName}}, {{child.firstName}} <br> {{getSchool child}}</td>
                                                            <td data-cy="child-dob-bus-report" style="border: 1px solid black">{{getChildDob child}}</td>
                                                            {{#each day in days}}
                                                                <td style="border: 1px solid black"></td>
                                                                <td style="border: 1px solid black"></td>
                                                                <td style="border: 1px solid black"></td>
                                                                <td style="border: 1px solid black"></td>
                                                            {{/each}}
                                                        </tr>
                                                    {{/each}}
                                                    {{# each (repeat 3)}}
                                                        <tr style="border: 1px solid black">
                                                            <td style="border: 1px solid black">&nbsp;<br>&nbsp;</td>
                                                            <td style="border: 1px solid black"></td>
                                                            {{#each day in days}}
                                                                <td style="border: 1px solid black"></td>
                                                                <td style="border: 1px solid black"></td>
                                                                <td style="border: 1px solid black"></td>
                                                                <td style="border: 1px solid black"></td>
                                                            {{/each}}
                                                        </tr>
                                                    {{/each}}
                                                
                                                </tbody>
                                                <tfoot style="page-break-inside: avoid;">
                                                {{#each signatureItem in signatureItems}}
                                                <tr>
                                                    <td colspan=2 style="border: 1px solid black">{{signatureItem}}</td>
                                                    {{#each day in days}}
                                                    <td colspan=4 style="border: 1px solid black"></td>
                                                    {{/each}}
                                                </tr>
                                                {{/each}}
                                                <tr>
                                                    <td colspan="100%">
                                                        <div style="width: fit-content; margin-top: 10px; page-break-inside: avoid;">
                                                            <h3 style="padding-left:5px">Total Count: {{count children}}</h3>
                                                            <p style="white-space: nowrap">
                                                                <b>Instructions to Driver</b><br>
                                                                (A) Enter the time the child entered your care and you became directly responsible for the child's supervision.<br>
                                                                (B) Enter the time the child left your care and direct supervision.<br>
                                                            </p>
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                        {{#unless endReportCheck @index reportRoutes}}
                                            <p style="page-break-after: always;">&nbsp;</p>
                                            <p style="page-break-before: always;">&nbsp;</p>
                                        {{/unless}}
                                    {{/each}}
                                {{/if}}
                            </div>
                        </div>
                    </div>
                </div>                
            </div>
        </div>
    </div>
    {{#if openPrintableView}}
        <script type="text/javascript">
            window.print();
        </script>
    {{/if}}
</template>