import { Orgs } from '../../../lib/collections/orgs';
import './reportSubsidyAgencyField.html';

Template.reportSubsidyAgencyField.helpers({
    availablePayers() {
        const subsidies = Orgs.current() && Orgs.current().availablePayerSources(true)

        return subsidies.map(subsidy => {
            const description = subsidy.archived
                ? `${subsidy.description} (Archived)`
                : subsidy.description;

            return {
                "type": subsidy.type,
                "description": description
            };
        });
    },
})

Template.reportSubsidyAgencyField.onRendered(function () {
    var $filterAvailablePayers = $("#filterAvailablePayers");

    // Initialize the multiselect with includeSelectAllOption
    $filterAvailablePayers.multiselect({
        includeSelectAllOption: true,
        enableFiltering: true,
        maxHeight: 300,
        onChange: function (option, checked, select) {
            // Handle the change event
            const selectedOptions = $filterAvailablePayers.val() || [];
            const allSelectedText = 'All selected (' + selectedOptions.length + ')';
            $filterAvailablePayers.siblings('.btn-group').find('.multiselect-selected-text').text(allSelectedText);
        },
    });

    // Manually set all options as selected
    $filterAvailablePayers.multiselect('selectAll', false);

    // Update the button text and ensure all options are selected
    $filterAvailablePayers.multiselect('updateButtonText');
});






