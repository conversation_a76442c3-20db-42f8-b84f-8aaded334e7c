import './modalReportSave.html';

Template.modalReportSave.helpers({
    isEdit() {
        return Template.instance().data.isEdit;
    },
    defaultName() {
        const reportName = Template.instance().data.reportName;
        if (reportName) {
            return reportName;
        }
        return "My Report";
    }
});

Template.modalReportSave.events({
    'click #saveReportBtn': async function (event, instance) {
        const newName = document.getElementById('inputReportName').value?.trim();
        if (!newName) {
            await mpSwal.fire('Error', 'Report name cannot be empty', 'error');
            return;
        }
        if (instance.data.isEdit) {
            if (newName === instance.data.reportName) {
                await mpSwal.fire('Warning', 'Name has not changed', 'warning');
                return;
            }
            Meteor.callAsync('copySavedCustomReport', instance.data.reportId, newName)
            .then((result) => {
                mpSwal.fire('Success', 'Report saved successfully to ' + result, 'success');
                instance.data.callback();
                $('#modalReportSave').modal('hide');
            })
            return;
        }
        Meteor.callAsync("saveCustomReport",
            instance.data.reportData,
            instance.data.index,
            newName
        ).then((result) => {
            mpSwal.fire('Success', 'Report saved successfully to ' + result, 'success');
            $('#modalReportSave').modal('hide');
        }).catch((error) => {
            console.error("Error saving report", error);
        });
    },
    'click #deleteReportBtn': function (e, instance) {
        mpSwal.fire({
            text: 'Are you sure you want to delete this report?',
            type: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
        }).then(result => {
            if (result.isConfirmed) {
                Meteor.callAsync("deleteSavedCustomReport",
                    instance.data.reportId,
                ).then((result) => {
                    mpSwal.fire('Success', 'Report deleted', 'success');
                    instance.data.callback();
                    $('#modalReportSave').modal('hide');
                }).catch((error) => {
                    console.error("Error deleting report", error);
                });
            }
        });

    }
})