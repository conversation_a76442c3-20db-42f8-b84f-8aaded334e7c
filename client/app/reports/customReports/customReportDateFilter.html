<template name="customReportDateFilter">
    <div id="customReportDateFilter" class="modal fade" data-backdrop="static">
        <div class="modal-dialog modal-md modal-dialog-centered" >
            <div class="modal-content" style="height:auto">
                <div class="modal-header">
                    <h5 class="modal-title">Filter "{{fieldName}}":</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg btn-close-filters" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <div class="row">
                        <div class="input-group">
                            <div class="col-5 text-right">
                                <label>Start Date</label>
                            </div>
                            <div class="col-4">
                                <input type="text" class="form-control mb-2 mt-n2" id="start-date" value="{{start}}">
                            </div>
                            <div class="col-1">
                                <i class="fad-regular fad-primary fad fa-times cursor-pointer" id="clear-start"></i>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="input-group">
                            <div class="col-5 text-right">
                                <label>End Date</label>
                            </div>
                            <div class="col-4">
                                <input type="text" class="form-control mt-n2" id="end-date" value="{{end}}">
                            </div>
                            <div class="col-1">
                                <i class="fad-regular fad-primary fad fa-times cursor-pointer" id="clear-end"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary font-weight-bolder" id="apply-button">Apply</button>
                    <button type="button" class="btn btn-secondary font-weight-bolder btn-close-filters" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>