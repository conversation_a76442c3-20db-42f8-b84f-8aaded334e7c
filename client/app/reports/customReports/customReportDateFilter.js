import { AVAILABLE_REPORTS } from "../../../../lib/constants/reportConstants";
import { CustomReportUtils } from "../../../../lib/customReports/customReportUtils";
import { getReportFieldMapping } from "./customReportService/customReportsOrgLevels";
import './customReportDateFilter.html';

Template.customReportDateFilter.created = function() {
    this.field = this.data.field;
    if (!this.field.start) {
        this.field.start = moment().subtract(90, 'days').format('YYYY-MM-DD');
        this.field.end = moment().format('YYYY-MM-DD');
    }
}

Template.customReportDateFilter.rendered = function() {
    $('#start-date').datepicker({
        autoclose: true
    });
    $('#end-date').datepicker({
        autoclose: true
    });
}

Template.customReportDateFilter.helpers({
    fieldName() {
        return Template.instance().field.label;
    },
    start() {
        return Template.instance().field.start ? moment(Template.instance().field.start).format('MM/DD/YYYY') : '';
    },
    end() {
        return Template.instance().field.end ? moment(Template.instance().field.end).format('MM/DD/YYYY') : '';
    }
});

Template.customReportDateFilter.events({
    'click #apply-button': function(e, i) {
        const filter = {...i.field};
        filter.start = document.getElementById('start-date').value;
        filter.end = document.getElementById('end-date').value;
        i.data.dateCallback(filter);
    },
    'click #clear-start': function(e, i) {
        document.getElementById('start-date').value = '';
    },
    'click #clear-end': function(e, i) {
        document.getElementById('end-date').value = '';
    }
});

Template.customReportDateFilter.destroyed = function() {
    this.data.onClose();
}