<template name="customReportFilters">
    <div id="customReportFilters" class="modal fade" data-backdrop="static">
        <div class="modal-dialog modal-dialog-scrollable modal-lg" >
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header">
                    <h1>Step 2 - Load data onto your computer:
                        <a href="#" data-toggle="tooltip" data-placement="right"
                           data-html="true"
                           title="You can remove more fields and<br> add more filters in Step 3."><i
                                class="fad fad-primary fa-info-circle"></i></a>
                    </h1>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg btn-close-filters" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <div class="card card-custom">
                        <div class="card-header flex-wrap border-0 pt-6 pb-0">
                            <div class="card-title">
                                <h3 class="card-label">
                                    <i id="show-fields-button" class="fad {{buttonType showFields}} mr-2 cursor-pointer"></i>
                                    Select the fields you want:
                                </h3>
                            </div>
                        </div>
                        <div class="card-body" style="max-height: 44.8ex; overflow-y: auto; display: {{visibility showFields}}">
                            {{#if loaded}}
                            <div class="col-md-6 offset-md-3 checkbox-list">
                                <label class="switch">
                                    <input type="checkbox" name="alphabetical" id="alpha-box" checked>
                                    <span class="slider round"></span>
                                    List in alphabetical order
                                </label>
                                <label class="ml-8">
                                    <input type="checkbox" class="form-check-input" id="select-all-box" checked><em>Select All</em>
                                </label>
                                {{#each field in displayFields}}
                                    <label class="ml-8">
                                        <input type="checkbox" value="{{field.field}}" class="form-check-input field-box" id="box-field-{{field.field}}" {{checkIfSelected field.field}}> {{ field.label }}
                                    </label>
                                {{/each}}
                            </div>
                            {{else}}
                                <h3>Loading...</h3>
                            {{/if}}
                        </div>
                    </div>
                    {{#if showDateFilters}}
                    <div class="card card-custom">
                        <div class="card-header flex-wrap border-0 pt-6 pb-0">
                            <div class="card-title">
                                <h3 class="card-label">
                                    <i id="show-filters-button" class="fad {{buttonType showFilters}} mr-2 cursor-pointer"></i>
                                    Select the filters you want:
                                </h3>
                            </div>
                        </div>
                        <div class="card-body" style="max-height: 45ex; overflow-y: auto; display: {{visibility showFilters}}">
                            {{#each filter in dateFilters}}
                                <div class="row">
                                    <div class="col-md-6 offset-md-3">
                                        <label class="ml-3">{{filter.label}}</label>
                                        <i class="fad fa-pencil date-filter-edit cursor-pointer" data-field="{{filter.field}}" id="edit-{{filter.field}}"></i>
                                    </div>
                                </div>
                            {{/each}}
                            <div class="col-md-6 offset-md-3">
                                <select id="filter-select" class="form-control form-control-solid">
                                    <option value="">Add a Filter</option>
                                    {{#each filter in availableDateFilters}}
                                        <option value="{{filter.field}}">{{filter.label}}</option>
                                    {{/each}}
                                </select>
                                If you have multiple filters, only data that meets all the filter criteria will be loaded.
                            </div>
                        </div>
                    </div>
                    {{/if}}
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary font-weight-bolder" id="load-data-button" {{loadDisabled}}>Load Data</button>
                    <button type="button" class="btn btn-secondary font-weight-bolder btn-close-filters" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>