<template name="modalReportSave">
    <div id="modalReportSave" class="modal fade">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{#if isEdit}}Edit{{else}}Save{{/if}} Custom Report</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close">
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <div class="container">
                        <div class="d-flex flex-wrap justify-content-center">
                            <div class="form-group row">
                                <label for="inputReportName" class="col-12 text-lg-center col-form-label">Report Name:</label>
                                <br>
                                <div class="col-12">
                                    <input class="form-control form-control-lg form-control-solid" type="text" id="inputReportName" placeholder="" value="{{ defaultName }}" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d.flex justify-content-center align-content-center">
                    <button type="button" class="btn btn-primary font-weight-bolder mr-2" id="saveReportBtn">Save</button>
                    {{#if isEdit}}
                        <button type="button" class="btn btn-secondary font-weight-bolder mr-2" id="deleteReportBtn">Delete</button>
                    {{/if}}
                </div>
            </div>
        </div>
    </div>
</template>
