#fm-toolbar-wrapper #fm-toolbar li svg .fill-background {
    fill: #ac52db;
    
}

#fm-toolbar-wrapper #fm-toolbar li svg  .fill-border {
    fill: #ac52db;
} 

 #fm-pivot-view .fm-grid-layout .fm-filters .fm-filter-header, #fm-pivot-view .fm-grid-layout.fm-flat-view .fm-filter-header#fm-pivot-view .fm-grid-layout .fm-filters .fm-filter-header, #fm-pivot-view .fm-grid-layout.fm-flat-view .fm-filter-header{
    background-color:  #ac52db !important;
}


#fm-toolbar-wrapper #fm-toolbar li#fm-tab-save a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-save a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-export a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-export a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-grid a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-grid a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-charts a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-charts a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-format a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-format a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-options a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-options a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-fields a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-fields a svg .fill-border { fill: #ac52db; /* Border color */ } 

#fm-toolbar-wrapper #fm-toolbar li#fm-tab-fullscreen a svg .fill-background { fill: #ac52db; /* Background color */ }
#fm-toolbar-wrapper #fm-toolbar li#fm-tab-fullscreen a svg .fill-border { fill: #ac52db; /* Border color */ } 

/* #fm-pivot-view .fm-cols-sheet .fm-scroll-content .fm-row .fm-cell:not(.fm-filter-header){
    background-color: red !important;
}
#fm-pivot-view .fm-grid-layout div.fm-sheet-headers .fm-cell{
    background-color: red !important;
} */

#fm-pivot-view .fm-cols-sheet .fm-scroll-content .fm-row .fm-cell:not(.fm-filter-header) {
    background-color: #ac52db !important;
}

#fm-pivot-view .fm-grid-layout div.fm-sheet-headers .fm-cell {
    background-color: #ac52db !important;
}

.fm-icon {
    color: red
}
.fm-row .fm-filter-header .fm-filter-icon {
    display: none !important;
}
#fm-toolbar-wrapper #fm-toolbar li svg {
    fill: #cd97e9 !important;
}
#fm-toolbar-wrapper #fm-toolbar li svg .fill-background {
    fill: #cd97e9 !important;
}
.pivot-container .grid-heading {
    color: black;
    font-weight:bold !important;
}

.fm-bar {
    fill: #ac52db !important;
}

.fm-line, .fm-circle, .fm-scatter-point {
    stroke: #ac52db !important;
}

.fm-bar-stack rect {
    fill: #ac52db !important;
}

.fm-charts-color-1 {
    fill: #730099 !important;
}

.fm-charts-color-2 {
    fill: #ac00e6 !important;
}

.fm-charts-color-3 {
    fill: #c61aff !important;
}

.fm-charts-color-4 {
    fill: #c61aff !important;
}

.fm-charts-color-5 {
    fill: #df80ff !important;
}

.fm-charts-color-6 {
    fill: #ecb3ff !important;
}

.fm-charts-color-7 {
    fill: #f9e6ff !important;
}

.fm-charts-color-8 {
    fill: none !important;
}

#fm-pivot-view .fm-axis text {
    font-weight: bold;
}

.fm-label{
    font-weight: normal !important;
}

#fm-pivot-view .fm-grid-layout.fm-flat-view .fm-filter-header {
    padding-left: 4px !important;
}

#fm-pivot-view .fm-grid-column {
    max-width: 200px !important;
}

.fm-cell {
    white-space: pre-wrap !important;
  }