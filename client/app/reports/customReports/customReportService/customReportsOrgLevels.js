import { FLEX_TABLE_NAME } from '../../../../../lib/constants/reportConstants';
import { getBaseUrl } from "../../../../../lib/baseUtils";
import { CustomReportUtils } from "../../../../../lib/customReports/customReportUtils";
import { Orgs } from '../../../../../lib/collections/orgs';
import { processPermissions } from '../../../../../lib/permissions';

/**
     * Returns object with all the required field/column names.
     * @param {Array} orgLevelNames 
     * @param {String} index
     * @returns {Object}
     */
export async function fieldsMappingObject(orgLevelNames, index){
    let data = await fetch(getBaseUrl(window.location) + `api/v2/reports/field?index=${index}`);
    let orgLevelFields = {};
    data = await data.json();
    for(let iter = 0; iter < orgLevelNames.length; iter++) {
        const levelIndex = iter < orgLevelNames.length - 1 ? 'level' + (iter + 2) : 'orgname'; // level index in orgLevelNames
        orgLevelFields[levelIndex] = {
            caption: orgLevelNames[iter].trim(),
            type: 'string'
        }
    }

    if(index === FLEX_TABLE_NAME.ORGS) {
        delete data?.name;
    }
    delete data?.orgName;
    delete data?.org_name;
    if (index === FLEX_TABLE_NAME.LABOR) {
        const canSeePay = processPermissions({
            assertions: [{context: "people/profile/pay", action: "read"}],
            evaluator: (person) => person.type === "admin"
        });
        if (!canSeePay) {
            delete data?.pay_rate;
        }
    }

    return {...orgLevelFields, ...data};
}

/**
 * Pass parameters to API, based on the selection orgIds
 * @param {String} index 
 * @returns 
 */
export async function getReportsUrl(index, filters) {
    let hash = sessionStorage.getItem('flexReportHash');
    if (!hash) {
        const orgIds = sessionStorage.getItem('flexReportOrgIds') || Orgs.current()._id;
        hash = await Meteor.callAsync('getCustomReportHash', orgIds);
    }
    let urlPart = 'timezone='+Orgs.current().timezone+`&index=${index}`
    if (filters) {
        urlPart += '&' + CustomReportUtils.filtersToUrl(filters);
    }
    return getBaseUrl(window.location) + `api/v2/reports/${index}?orgHash=`+hash+'&' + urlPart;
}

/**
 * Return object contaning fields for the custom reports.
 * @param {String} index 
 * @returns {Object}
 */
export async function getReportFieldMapping(index) {
    const orgLevelNames = sessionStorage.getItem('flexReportOrgLevels')?.split(',');
    const mapping = (await fieldsMappingObject(orgLevelNames, index));
    return mapping;
}

