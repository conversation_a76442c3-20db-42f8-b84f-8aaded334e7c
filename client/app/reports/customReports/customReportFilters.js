import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { CustomReportUtils } from "../../../../lib/customReports/customReportUtils";
import { getReportFieldMapping } from "./customReportService/customReportsOrgLevels";
import './customReportFilters.html';
import './customReportDateFilter';
import { hideModal, showModal } from '../../main';

Template.customReportFilters.created = async function() {
    this.report = CustomReportUtils.getReportObject(this.data.reportId);
    this.showFields = new ReactiveVar(true);
    this.showFilters = new ReactiveVar(true);
    this.displayFields = new ReactiveVar([]);
    this.dateFields = new ReactiveVar([]);
    this.selectedFields = new ReactiveVar([]);
    this.dateFilters = new ReactiveVar([]);
    this.loaded = new ReactiveVar(false);
    this.savedReport = new ReactiveVar(null);
    if (this.data.savedReportId) {
        this.savedReport.set(await Meteor.callAsync('getOneSavedCustomReport', this.data.savedReportId));
    }
    this.reportAvailableFields = await getReportFieldMapping(this.report.index);
    if (this.savedReport.get()) {
        this.selectedFields.set(CustomReportUtils.getSavedFields(this.savedReport.get()));
        this.dateFilters.set(CustomReportUtils.getSavedDateFilters(this.savedReport.get(), this.reportAvailableFields));
        const orgHash = CustomReportUtils.getSavedOrgHash(this.savedReport.get());
        sessionStorage.setItem('flexReportHash', orgHash);
    } else {
        this.selectedFields.set(Object.keys(this.reportAvailableFields));
    }
    reorderFields(true, this);
    this.loaded.set(true);
    this.dateFields.set(CustomReportUtils.getDateFields(this.displayFields.get()));
}

Template.customReportFilters.rendered = function() {
    $('[data-toggle="tooltip"]').tooltip({trigger : 'hover'});
}

Template.customReportFilters.helpers({
    reportAvailableFields() {
        return Template.instance().reportAvailableFields;
    },
    loaded() {
        return Template.instance().loaded.get();
    },
    showFields() {
        return Template.instance().showFields.get();
    },
    showFilters() {
        return Template.instance().showFilters.get();
    },
    displayFields() {
        return Template.instance().displayFields.get();
    },
    checkIfSelected(field) {
        return Template.instance().selectedFields.get().includes(field) ? 'checked' : '';
    },
    visibility(flag) {
        return flag ? 'block' : 'none';
    },
    buttonType(flag) {
        return flag ? 'fa-minus' : 'fa-plus';
    },
    loadDisabled() {
        return Template.instance().selectedFields.get().length === 0 ? 'disabled' : '';
    },
    showDateFilters() {
        return Template.instance().dateFields.get().length > 0;
    },
    availableDateFilters() {
        return CustomReportUtils.getAvailableDateFields(Template.instance().dateFields.get(), Template.instance().dateFilters.get());
    },
    dateFilters() {
        return Template.instance().dateFilters.get();
    }
});

Template.customReportFilters.events({
    'click .btn-close-filters': function(e, i) {
        e.preventDefault();
        hideModal("#customReportFilters");
        if (i.data.savedReportId) {
            FlowRouter.go('/reports-custom-saved');
        } else {
            FlowRouter.go('/reports-custom-builder');
        }
    },
    'click #load-data-button': function(e, i) {
        const fields = i.selectedFields.get();
        const filters = i.dateFilters.get();
        i.data.dataCallback(fields, filters, i.savedReport.get());
    },
    'click .field-box': function(e, i) {
        const field = e.target.value;
        if (e.target.checked) {
            i.selectedFields.set([...i.selectedFields.get(), field]);
        } else {
            i.selectedFields.set(i.selectedFields.get().filter(f => f !== field));
        }
    },
    'click #alpha-box': function(e, i) {
        reorderFields(e.target.checked, i);
    },
    'click #select-all-box': function(e, i) {
        const selected = e.target.checked;
        if (selected) {
            i.selectedFields.set(Object.keys(i.reportAvailableFields));
        } else {
            i.selectedFields.set([]);
        }
        document.querySelectorAll('.field-box').forEach(f => f.checked = selected);
    },
    'click #show-fields-button': function(e, i) {
        i.showFields.set(!i.showFields.get());
    },
    'click #show-filters-button': function(e, i) {
        i.showFilters.set(!i.showFilters.get());
    },
    'change #filter-select': function(e, i) {
        const field = e.target.value;
        if (field === '') {
            return;
        }
        const fieldObj = i.dateFields.get().find(f => f.field === field);
        showModal('customReportDateFilter', { field: fieldObj, onClose: dateClosed, dateCallback: (filter) => {
            setDateFilter(i, filter);
            hideModal('#customReportDateFilter');
        }}, '#customReportDateFilter');
    },
    'click .date-filter-edit': function(e, i) {
        const field = e.target.dataset.field;
        const fieldObj = i.dateFilters.get().find(f => f.field === field);
        showModal('customReportDateFilter', { field: fieldObj, onClose: dateClosed, dateCallback: (filter) => {
                setDateFilter(i, filter);
                hideModal('#customReportDateFilter');
            }}, '#customReportDateFilter');
    }
});

function setDateFilter(i, filter) {
    document.getElementById('filter-select').value = '';
    i.dateFilters.set(i.dateFilters.get().filter(f => f.field !== filter.field));
    if (!filter.start && !filter.end) {
        return;
    }
    i.dateFilters.set([...i.dateFilters.get(), filter]);
}

function reorderFields(alpha, i) {
    if (alpha) {
        const fields = CustomReportUtils.mappingToFields(i.reportAvailableFields);
        fields.sort((a, b) => a?.label?.localeCompare(b?.label));
        i.displayFields.set(fields);
        return;
    }
    i.displayFields.set(CustomReportUtils.mappingToFields(i.reportAvailableFields));
}

function dateClosed() {
    document.getElementById('filter-select').value = '';
}