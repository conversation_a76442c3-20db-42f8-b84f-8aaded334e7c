
<template name="multiSiteOrgReport">
	{{#if availableOrgs}}
	    {{> multiSiteOrgReportDropdown orgs=availableOrgs}}
	{{else if isLoading}}
	    <i>Loading...</i>
	{{else}}
	    {{orgName}}
	{{/if}}
</template>

<template name="multiSiteOrgReportDropdown">
	<div class="report-orgs-field">
		{{> reportOrgsField opts=orgsFieldOpts}}
	</div>
</template>

<template name="childOrgList">
    {{#each this}}
        <option value="{{id}}">{{name}}</option>
        {{#if children.length}}
            {{> childOrgList children}}
        {{/if}}
    {{/each}}
</template>


