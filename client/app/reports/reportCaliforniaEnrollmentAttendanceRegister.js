import './reportCaliforniaEnrollmentAttendanceRegister.html';

const dayMap = { "mon": "M", "tue": "Tu", "wed": "W", "thu": "Th", "fri": "F"};
Template.reportCaliforniaEnrollmentAttendanceRegister.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.startDate = new ReactiveVar();
	this.endDate = new ReactiveVar();
}

Template.reportCaliforniaEnrollmentAttendanceRegister.rendered = function() {
	//$('#attendanceStartDate').datepicker({autoclose:true});
	//$('#attendanceEndDate').datepicker({autoclose:true});
	$('.input-daterange').datepicker({autoclose:true});
}

Template.reportCaliforniaEnrollmentAttendanceRegister.events({
	"click button#update": function() {
		var instance = Template.instance();
		const startDate = $("#attendanceStartDate").val(), 
			endDate = $("#attendanceEndDate").val();
		instance.startDate.set(startDate);
		instance.endDate.set(endDate);
		if (!moment(startDate).isValid() || !moment(endDate).isValid())
			return swal("Error", "You must supply a valid date range for this report.", "error");
		const options = {
			startDate,
			endDate
		};
		Meteor.callAsync("attendanceReportCalifornia", options).then((result) => {
			instance.reportData.set(result);
		}).catch((err) => {
			console.error("Error", err);
			instance.reportData.set(result);
		});
	},
	"click #btnExportCsv": function(event, instance) {
		var outputFile = 'enrollmentattendanceregister.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#californiaAttendanceRegisterTable'), outputFile]);
	}
});



Template.reportCaliforniaEnrollmentAttendanceRegister.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.sortStamp).format("hh:mm a");
	},
	"attendees": function() {
		const data = Template.instance().reportData.get();
		return data && data.rows;
	},
	"sections": function() {
		const data = Template.instance().reportData.get(),
			groups = data && _.groupBy( data.rows, r => r.category + ' - ' + r.subcategory);

		console.log("groups", groups);
		return _.chain(groups).map( (v, k) => ({
			sectionName: k,
			attendees: v,
			order: v && v.length > 0 && v[0].order
		})).sortBy( v => v.order).value();
	},
	"daysInMonth": function() {
		const data = Template.instance().reportData.get();
		return data && data.daysInMonth;
	},
	"findPayments": function(payments, endStamp) {
		const endDate = Template.instance().endDate.get();
		endStamp = endStamp || moment(endDate, "MM/DD/YYYY").endOf("day").valueOf();
		const out = _.filter(payments, p => p.createdAt <= endStamp && p.createdAt > (new moment(endStamp).add(-7, "days").valueOf()));
		return out;
	},
	"showEndPaymentColumn": function() {
		const endDate = Template.instance().endDate.get();
		return endDate && moment(endDate, "MM/DD/YYYY").day() != 5;
	}
});