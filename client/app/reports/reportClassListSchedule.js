import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';
import { Log } from '../../../lib/util/log';
import { showModal } from '../main';
import './reportClassListSchedule.html'

const RECORD_LIMIT = 1;

Template.reportClassListSchedule.created = function() {
	const instance = this;
	let initialMoment = new moment();
	initialMoment = initialMoment.day() <= 1 ? initialMoment.day(1) : initialMoment.day(8)
	this.reportStartDate = new ReactiveVar(initialMoment.format("MM/DD/YYYY"));
	this.classListReportAggregates = new ReactiveVar({groups: [], totals: []});
	this.onlyCurrentSchedules = new ReactiveVar();
	this.totalCount = new ReactiveVar();
	this.selectedOption = new ReactiveVar('');
	this.currentPage = new ReactiveVar(1);
	this.nextData = new ReactiveVar();
	this.previousData = new ReactiveVar();
	this.summaryData = new ReactiveVar();
	this.isLastPage = new ReactiveVar(false);
	this.isPreviousPage = new ReactiveVar(false);
	this.fetchSummaryFlag = new ReactiveVar(false);
	this.isResponse = new ReactiveVar(false);
};

Template.reportClassListSchedule.rendered = function() {
	$('#reportStartDate').datepicker({
		autoclose: true,
	});

	$(window).on("resize", window, cltableCb)
}
cltableCb = function() { 
	if ($("#classlistschedule-report-table").length == 0) {
		return;
	}
	const windowHeight = $(window).height();
	const topOffset = $("#classlistschedule-report-table").offset().top;
	const calcHeight =  windowHeight - topOffset - 65;
	$("#classlistschedule-report-table").css("max-height", calcHeight + "px");
}
Template.reportClassListSchedule.destroyed = function() {
	$(window).off("resize", window, cltableCb);
}
Template.reportClassListSchedule.events({
	"click #btnUpdate": async function(event, template) {
		event.preventDefault();
		
		const selectedValue = $("#reportGroupId").val()
		template.selectedOption.set(selectedValue)
		
		$("#btnUpdate").text("Updating").prop('disabled', true);
		
		const passedValue = $("#reportStartDate").val(),
			newMoment = new moment(passedValue, "MM/DD/YYYY");

		if (newMoment.day() != 1) {
			$("#btnUpdate").text("Update").prop('disabled', false);
			return mpSwal.fire("Error", "Enrollment forecast reports can only be run for Mondays", "error");
		}

		const instance = Template.instance();
		instance.reportStartDate.set(passedValue);
		instance.classListReportAggregates.set({groups: [], totals: []})
		if (selectedValue === '') {
			instance.currentPage.set(1)
			instance.isPreviousPage.set(false)
		} 
		instance.isLastPage.set(false)
		if (instance.selectedOption.get() === '') {
			Promise.all([
				getSummaryData(),
				fetchClassListReportAggregates(instance, true),
			]).then((res) => {
				if (res && res.length > 0) {
					if (res[0]?.responseSummary && res[1]?.responseClassList) {
						let responseSummary = res[0]?.responseSummary
						updateData(instance, res[1]?.responseClassList)
						if (responseSummary.groups.length > 0) {
							instance.isResponse.set(false);
						}
						if (responseSummary.fetchSummaryFlag) {
							instance.summaryData.set(responseSummary);
						}
						instance.fetchSummaryFlag.set(false);
					}
				}
			}).catch(error => {
				Log.error(error);
				mpSwal.fire('Error', error.message || error.reason, 'error');
			});
		} else {
			fetchClassListReportAggregates(instance, false)
		}
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "classListSchedule"
		});
	},
	"click #btnExportCsv": async function() {
		const outputFile = 'export.csv'
		const rawHtml = $('#dvDataCsv > table').clone();
		rawHtml.find("thead").first().remove();
		// CSV
		exportTableToCSV.apply(this, [rawHtml, outputFile]);
		
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "classListSchedule"
		});
	},
	"click .btnPersonPopup"(e,i) {
		e.preventDefault();
		const tgt = $(e.currentTarget).prop("href");
		showModal("_personPopup", {
			location: tgt,
			onCloseRefresh: () => { $("#btnUpdate").text("Updating").prop('disabled', true);  fetchClassListReportAggregates(i, false); }
		}, "#_personPopup");
	},
	"click .page-item-set": function(event, template) {
		const action = $(event.currentTarget).attr("data-action");
		let currentPageNo = template.currentPage.get();
		let lastPageNo = template.totalCount.get()
		template.isLastPage.set(false)
		
		if (!template.isResponse.get()) {
			if (action == "add" && lastPageNo >= currentPageNo) {
				template.isPreviousPage.set(false)
				currentPageNo = currentPageNo + 1
				if (currentPageNo === lastPageNo + 1) {
					template.isLastPage.set(true)
				}
			} else if (action == "subtract" && currentPageNo > 1) {
				template.isPreviousPage.set(true)
				currentPageNo = currentPageNo - 1
				isPreviousPage = true
			} else {
				if (currentPageNo !== 1) {
					template.isLastPage.set(true)
				}
			}
			
			template.currentPage.set(currentPageNo)
			refreshResults(template);
		}
		
	},
});

Template.reportClassListSchedule.helpers({
	"hasBorder": function (index, size) {
		const i = parseInt(index) || 0;
		const s = parseInt(size) || 99;
		return (index == size -1 ) ? "report-cp-br" : "";
	},
	"hasNumberValue": function(val) {
		return !isNaN(val);
	},
	"getVarianceTextClass": function(preferredCapacity, currentFte) {
		let cFte = parseFloat(currentFte);
		let pC = parseFloat(preferredCapacity);
		if (cFte < pC) return "color:red;"
		return "";
	},
	"formatCurrency": function(num) {
		// Create our number formatter.
		const formatter = new Intl.NumberFormat('en-US', {
		  style: 'currency',
		  currency: 'USD',
		});
		if (num) {
			return formatter.format(num);
		}
	},
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"groups": function(isCsv = false) {
		return !isCsv ? Template.instance().classListReportAggregates.get().groups : Template.instance().summaryData.get().groups
	},
	"programs": function() {
		const programs = Template.instance().classListReportAggregates.get().programs;
		return _.map(programs, (program, name) => {
			program.name = name;
			return program;
		});
	},
	"groupTypes": function(isCsv) {
		const groupTypes = !isCsv ? Template.instance().classListReportAggregates.get().groupTypes : Template.instance().summaryData.get().groupTypes
		return _.map(groupTypes, (groupType, name) => {
			groupType.name = name;
			return groupType;
		});
	},
	"totals": function() {
		return Template.instance().classListReportAggregates.get().totals;
	},
	"ageGroupLabel": function(ageGroup) {
		let label = "";
		if (ageGroup && ageGroup.begin) label += `${ageGroup.begin} - `;
		if (ageGroup && ageGroup.end) label += `${ageGroup.end} `;
		if (ageGroup && ageGroup.type) label += `${ageGroup.type}`;
		return label;
	},
	"ratioLabel": function(ratio) {
		let label = "";
		if (ratio) label += `1:${ratio}`;
		return label;
	},
	"displayChildDetails": function () {
		return $("#includeChildDetails").prop("checked");
	},
	"slots": function() {
		return _.sortBy(Orgs.current().getScheduleTypes().filter(st => st.startTime && !st.hideInForecasting), st => st.sortStart);
	},
	"slotsCount": function() {
		const slots = Orgs.current().getScheduleTypes();
		return slots.filter(st => st.startTime && !st.hideInForecasting).length;
	},
	"slotsDaysCount": function() {
		const slots = Orgs.current().getScheduleTypes();
		return slots.filter(st => st.startTime && !st.hideInForecasting).length * 5;
	},
	"slotValue": function (person, slotId, day, isTransitionRow) {
		const slots = Orgs.current().getScheduleTypes(),
			slot = slots.find(s => s._id == slotId),
			slotLabel = (slot.startTime + " - " + slot.endTime);
		if (person.daySlots[day] && person.daySlots[day][slotLabel] && !isTransitionRow)
			return (person.daySlots[day][slotLabel]) ? "1" : "";
		else if (person.nextTransitionSlots[day] && person.nextTransitionSlots[day][slotLabel] && (isTransitionRow || !person.hasSameGroupTransition))
			return "*";
	},
	"slotTotal": function (group, slotId, day) {
		const slots = Orgs.current().getScheduleTypes(),
		slot = slots.find(s => s._id == slotId),
		slotLabel = slot.startTime + " - " + slot.endTime;
		return (group.dayTotals[day] && group.dayTotals[day][slotLabel]);
	},
	"slotAvailable": function (group, slotId, day) {
		const slots = Orgs.current().getScheduleTypes(),
		slot = slots.find(s => s._id == slotId),
		slotLabel = slot.startTime + " - " + slot.endTime;
		return (group.dayTotals[day] && (group.capacity - group.dayTotals[day][slotLabel]));
	},
	isNegative(val) {
		return val && val < 0;
	},
	"days": function() {
		return ["mon","tue","wed","thu","fri"];
	},
	"capacityTuition": function(group) {
		return group.capacity * group.defaultTuition;
	},
	"getProgramCount": function(program, day, slotId, valueType) {
		const slots = Orgs.current().getScheduleTypes(),
		slot = slots.find(s => s._id == slotId),
		slotLabel = slot.startTime + " - " + slot.endTime;
		
		if (valueType == "available")
			return program.daySlots[day][slotLabel]["capacity"] - program.daySlots[day][slotLabel]["enrolled"];
		else
			return program.daySlots[day][slotLabel][valueType];
	},
	"getGroupTypeCount": function(groupType, day, slotId, valueType) {
		const slots = Orgs.current().getScheduleTypes(),
		slot = slots.find(s => s._id == slotId),
		slotLabel = slot.startTime + " - " + slot.endTime;
		if (!groupType.daySlots[day] || !groupType.daySlots[day][slotLabel]) return;
		if (valueType == "available")
			return groupType.daySlots[day][slotLabel]["capacity"] - groupType.daySlots[day][slotLabel]["enrolled"];
		else
			return groupType.daySlots[day][slotLabel][valueType];
	},
	"getAllCount": function(day, slotId, valueType, isCsv) {
		const slots = Orgs.current().getScheduleTypes(),
		slot = slots.find(s => s._id == slotId),
		slotLabel = slot.startTime + " - " + slot.endTime,
		programs = !isCsv ? Template.instance().classListReportAggregates.get().programs : Template.instance().summaryData.get().programs
		let sum = 0;
		_.each(programs, program => {
			if (program.daySlots && program.daySlots[day] && program.daySlots[day][slotLabel])
				sum += program.daySlots[day][slotLabel][valueType];
		});
		return sum;
	},
	"getPercentEnrolled": function(day, slotId, isCsv) {
		const slots = Orgs.current().getScheduleTypes(),
		slot = slots.find(s => s._id == slotId),
		slotLabel = slot.startTime + " - " + slot.endTime,
		programs = !isCsv ? Template.instance().classListReportAggregates.get().programs : Template.instance().summaryData.get().programs
		let sumEnrolled = 0, sumCapacity = 0;
		_.each(programs, program => {
			if (program.daySlots && program.daySlots[day] && program.daySlots[day][slotLabel]) {
				sumEnrolled += program.daySlots[day][slotLabel]["enrolled"];
				sumCapacity += program.daySlots[day][slotLabel]["capacity"];
			}
		});
		return sumCapacity > 0 ? sumEnrolled/sumCapacity*100.0 : 0;
	},
	"getOverallPercentEnrolled": function(isCsv) {
		const programs = !isCsv ? Template.instance().classListReportAggregates.get().programs : Template.instance().summaryData.get().programs
		let sumEnrolled = 0, sumCapacity = 0;
		_.each(programs, program => {
			_.each(program.daySlots, (programDaySlots, dayLabel) => {
				_.each(programDaySlots, (programDaySlotValues, daySlotLabel) => {
					sumEnrolled += programDaySlotValues.enrolled;
					sumCapacity += programDaySlotValues.capacity;
				});
			})
		});
		return sumCapacity > 0 ? sumEnrolled/sumCapacity*100.0 : 0;
	},
	"getTuitionAmount": function(tuitionType, isCsv) {
		const groups = !isCsv ? Template.instance().classListReportAggregates.get().groups : Template.instance().summaryData.get().groups
		let sum = 0.0;
		if (tuitionType == "current") {
			_.each(groups, group => {
				_.each(group.groupPeople, groupPerson => {
					sum += groupPerson.tuition;
				});
			});
		} else if (tuitionType == "full") {
			_.each(groups, group => {
				sum += group.fullTuition;
			});
		} else {
			_.each(groups, group => {
				sum += parseInt(group.capacity * group.defaultTuition);
				
			});
			if (tuitionType == "85")
					sum = sum * 0.85;
				
		}
		return sum;
	},
	"getSummaries"() {
		const summaries = Template.instance().classListReportAggregates.get().summaries
		return summaries || {};
	},
	"getSummariesCsv"() {
		const summaries = Template.instance().summaryData.get().summaries
		return summaries || {};
	},
	"groupsList"() {
		return Groups.find({},{sort:{name:1}});
	},
	"getDailyCapacity"(group, day) {
		return group.dailyCapacities[day];
	},
	"showSameGroupTransition"(person) {
		return !Template.instance().onlyCurrentSchedules.get() && person.hasSameGroupTransition;
	},
	'getItemCountStart': function() {
		let itemCount = Template.instance().currentPage.get() * RECORD_LIMIT;
		const totalItems = Template.instance().totalCount.get()
		if (itemCount > totalItems + 1) {
			return totalItems
		}
		return itemCount;
	},
	'getItemCountEnd': function() {
		const totalCount = Template.instance().totalCount.get() + 1
		return totalCount
	},
	'isGroupSelected': function() {
		const selectedOption = Template.instance().selectedOption.get()
		return selectedOption === '' ? false : true
	},
	'shouldHideGroup': function() {
		const isLastPage = Template.instance().isLastPage.get()
		return isLastPage ? true : false
	},
	'isSummarySection': function() {
		if (Template.instance().isLastPage.get() === true && Template.instance().selectedOption.get() === '') {
			return true
		} if (Template.instance().selectedOption.get() !== '') {
			return true
		} 
	},
	'hidePrevButtonFlag': function() {
		if (Template.instance().currentPage.get() === 1) {
			return true
		} else {
			return false
		} 
	},
	'hideNextButtonFlag': function() {
		if (Template.instance().currentPage.get() === Template.instance().totalCount.get() + 1) {
			return true
		} else {
			return false
		} 
	}
});

const fetchClassListReportAggregates = function (instance, flag) {
	return new Promise(async (resolve, reject) => {
		const includeChildDetails = $("#includeChildDetails").prop("checked");
		const groupId = $("#reportGroupId").val();
		const onlyCurrentSchedules = $("input[name='only-current-schedules']").prop("checked");
		instance.onlyCurrentSchedules.set(onlyCurrentSchedules);
		instance.isResponse.set(true);
		let paginationRequestObj = null;
		if (instance.selectedOption.get() === '') {
			paginationRequestObj = {
				currentPage: instance.currentPage.get(),
				isPreviousPage: instance.isPreviousPage.get()
			};
		}
		await Meteor.callAsync("classListScheduleBased", {
			startDate: instance.reportStartDate.get(),
			includeChildDetails,
			groupId,
			onlyCurrentSchedules,
			paginationRequestObj
		}).then((response) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			if (!flag) {
				updateData(instance, response)
			}
			resolve({responseClassList: response});
		}).catch((error) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			Log.error(error);
			mpSwal.fire('Error', error.message || error.reason, 'error');
			reject(error);
		});
	});
 };

Template._personPopup.events({
	"click #btnCloseRefresh"(e, i) {
		i.data.onCloseRefresh();
	}
});

function refreshResults (template) {
	template.isResponse.set(false)
	if (template.isPreviousPage.get() === true) {
		template.nextData.set(template.classListReportAggregates.get())
		template.classListReportAggregates.set(template.previousData.get())
	} else {
		template.previousData.set(template.classListReportAggregates.get())
		if (template.isLastPage.get() === true) {
			template.classListReportAggregates.set(template.summaryData.get())
		} else {
			template.classListReportAggregates.set(template.nextData.get())
		}
	}
	
	if (template.currentPage.get() > 1) {
		fetchClassListReportAggregates(Template.instance(), false)
	}
}

const getSummaryData = function () {
	return new Promise(async (resolve, reject) => {
		let instance = Template.instance();
		instance.fetchSummaryFlag.set(true);
		const includeChildDetails = $("#includeChildDetails").prop("checked");
		const groupId = $("#reportGroupId").val();
		const onlyCurrentSchedules = $("input[name='only-current-schedules']").prop("checked");
		instance.isResponse.set(false);
		const paginationRequestObj = null;
		const fetchSummaryFlag = instance.fetchSummaryFlag.get();
		instance.onlyCurrentSchedules.set(onlyCurrentSchedules);
		await Meteor.callAsync("classListScheduleBased", {
			startDate: instance.reportStartDate.get(),
			includeChildDetails,
			groupId,
			onlyCurrentSchedules,
			paginationRequestObj,
			fetchSummaryFlag
		}).then((response) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			resolve({ responseSummary: response });
		}).catch((error) => {
			$("#btnUpdate").text("Update").prop('disabled', false);
			instance.fetchSummaryFlag.set(false);
			Log.error(error);
			mpSwal.fire('Error', error.message || error.reason, 'error');
			reject(error);
		});
	});
};

function updateData (instance, response) {
	if (instance.selectedOption.get() === '') {
		if (response) {
			instance.isResponse.set(false);
		}
		instance.totalCount.set(response.groupsTotalCount);
		if (response.groups.length > 1) {
			const groupData = JSON.parse(JSON.stringify(response));
			groupData.groups = [];
			groupData.groups.push(response.groups[0]);
			instance.classListReportAggregates.set(groupData);
			const nextGroupData = JSON.parse(JSON.stringify(response));
			nextGroupData.groups = [];
			nextGroupData.groups.push(response.groups[1]);
			instance.nextData.set(nextGroupData);
		} else {
			if (instance.isPreviousPage.get() === true) {
				instance.previousData.set(response);
			} else {
				instance.nextData.set(response);
			}
		}
	} else {
		instance.classListReportAggregates.set(response);
		instance.summaryData.set(response);
	}
}