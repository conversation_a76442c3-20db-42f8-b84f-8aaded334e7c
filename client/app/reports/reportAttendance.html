<template name="reportAttendance">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">(Legacy) Attendance Report
				<span class="text-muted pt-2 font-size-sm d-block">Historical attendance data filtered by individual, date range, and more. Please use the separate Child Attendance Report and Staff Time reports</span></h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv" data-cy="export-btn">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-header">
				  <h3 class="box-title">(Legacy) Attendance Report</h3>
				</div>
				<div class="box-body">
				  <div class="row">
					<div class="col-lg-2">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								
								<input type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}" data-cy="start-date-input">
							</div>
						</div>
					</div>
					<div class="col-lg-2">
						  <div class="form-group">
							<label>End Date</label>
							<div class="input-group">
								
								<input type="text" class="form-control pull-right" id="attendanceEndDate" value="{{formattedEndDate}}" data-cy="end-date-input">
							</div>
						</div>
					</div>
					<div class="col-lg-2">
						<div class="form-group">
							<label>Person</label>
							<div class="input-group">
								<select class="form-control" id="filterPerson" data-cy="select-person">
									<option value="">All</option>
									  {{#each people}}
									  <option value="{{_id}}">{{lastName}}, {{firstName}}</option>
									  {{/each}}
								  </select>
							</div>
						</div>
					</div>
					<div class="col-lg-2">
						<div class="form-group">
							<label>Type</label>
							<div class="input-group">
								<select class="form-control" id="filterType" data-cy="filter-type">
									<option value="">All</option>
									  <option value="person">Person</option>
									  <option value="family">Family</option>
									  <option value="staff">Staff</option>
									  <option value="admin">Admin</option>
								  </select>
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						<div class="form-group">
							<label>Group:</label>
							<select class="form-control" id="filterGroup" data-cy="select-group">
								<option value="">All</option>
								  {{#each groups}}
								  <option value="{{_id}}">{{name}}</option>
								  {{/each}}
							  </select>
						 </div>
					</div>
				  </div>
				  <div class="row">
					<div class="col-lg-3">
						<label>Sort:</label>
						<select class="form-control" id="sortType" data-cy="select-sort">
							<option value="attendeeFirstName" {{selectedIfEqual currentSortType "attendeeFirstName"}}>First Name</option>
							<option value="attendeeLastName" {{selectedIfEqual currentSortType "attendeeLastName"}}>Last Name</option>
							<option value="attendanceDate" {{selectedIfEqual currentSortType "attendanceDate"}}>Date</option>
						</select>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-3">
						<div class="form-group">
							<br/>
							<label>Org(s):</label><br/>
							{{> reportOrgsField }}
						</div>
					</div>
				</div>
				
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table class="table" data-cy="attendance-report-table">
					<tbody><tr>
					  
					  <th>Name</th>
					  <th>Date</th>
					  <th>Check-In</th>
					  <th>Check-Out</th>
					  <th>Checked In By</th>
					  <th>Checked Out By</th>
					  <th style="text-align:right">Elapsed Time</th>
					  {{#if showElapsedTimeUnits}}
						<th style="text-align:right">Elapsed Time (units)</th>
					  {{/if}}
					  {{#if hasCustomization "people/types/showPayer"}}
						  <th>Payer</th>
					  {{/if}}
					  {{#if hasCustomization "moments/checkin/showTransportation"}}
						  <th>Transportation (Check-In)</th>
						  <th>Transportation (Check-Out)</th>
					  {{/if}}
					  {{#if showStaffMember}}
						<th>Staff Member</th>
					  {{/if}}
					  <th>Schedule</th>
					  <th style="text-align:right">Variance (mins)</th>
					  {{#if showObjectiveField}}
						<th>Objective</th>
					  {{/if}}
					  {{#if showOrg}}
						<th>Org</th>
					  {{/if}}
					</tr>
					
					{{# each attendees}}
					<tr>
					  <td>{{name}} {{#if inActive}}<span style="color:#ff0000">(i)</span>{{/if}}</td>
					  <td>{{date}}</td>
					  <td>{{cInTime}}</td>
					  <td>{{cOutTime}}</td>
					  <td>{{cInBy}}</td>
					  <td>{{cOutBy}}</td>
					  <td style="text-align:right">{{elapsedTime}}</td>
					  {{#if showElapsedTimeUnits}}
						<td style="text-align:right">{{getElapsedTimeUnits elapsedTime}}</td>
					  {{/if}}
					  {{#if hasCustomization "people/types/showPayer"}}
						  <td>{{payer}}</td>
					  {{/if}}
					  {{#if hasCustomization "moments/checkin/showTransportation"}}
						  <td>{{trans}}</td>
						  <td>{{cOutTrans}}</td>
					  {{/if}}
					  {{#if showStaffMember}}
						<td>{{staffName}}</td>
					  {{/if}}
					  <td>{{scheduleOnDay}}</td>
					  <td style="text-align:right">{{variance}}</td>
					  {{#if showObjectiveField}}
						<td>{{carePlan}}</td>
					  {{/if}}
					  {{#if showOrg}}
						<td data-cy="org-name">{{orgName}}</td>
					  {{/if}}
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
