import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService"
import './reportRoster.html';
import { Groups } from '../../../lib/collections/groups';
import { Relationships } from '../../../lib/collections/relationships';
import { Person } from '../../../lib/collections/people';
import '../people/_personAvatarPhoto.js';

Template.reportRoster.onCreated( function() {
	const instance = this;
	const passedGroupId = FlowRouter.getQueryParam('groupId');
	const isPrintableView = FlowRouter.getQueryParam('printable');
	const singlePageMode = FlowRouter.getQueryParam('singlepageperfamily');
	const showAvatars = FlowRouter.getQueryParam('showAvatars');

	instance.selectedGroup = new ReactiveVar(passedGroupId);
	instance.selectionMade = new ReactiveVar(!!isPrintableView);
	instance.singlePageMode = new ReactiveVar(singlePageMode);
	instance.includeInactive = new ReactiveVar(false);
	instance.includeAvatars = new ReactiveVar(!!showAvatars);
	instance.personList = new ReactiveVar([]);
	instance.relPeopleList = new ReactiveVar([]);
	instance.subscribe("theUsers");

	this.autorun(() => {
		const selectedGroup = instance.selectedGroup.get();
		const includeInactive = instance.includeInactive.get();
		let query = {type:"person", designations: {$nin: ["Wait List"]}};
		if (!includeInactive) {
			query["inActive"] = { "$ne": true };
		}
		if (selectedGroup) {
			query["defaultGroupId"] = selectedGroup;
		}
		let relPersonIds = []
		getPeopleData(query, {sort: {lastName:1, firstName:1}}).then(res => {
			this.personList.set((res ?? []).map(person => new Person(person)))
			res.forEach((per)=>{
				relPersonIds = relPersonIds.concat(Relationships.find({ $or: [{ personId: per._id }, { targetId: per._id }] }).map(
					function (m, index) { return m.personId; }));
			})

			getPeopleData({_id:{$in:relPersonIds}}, {sort: {lastName:1, firstName:1}}).then(relPeople => {
				this.relPeopleList.set(relPeople);
			}).catch(err => {
				console.log(err);
			});


		}).catch(err => {
			console.log(err);
		});
	})

});

Template.reportRoster.onCreated( function() {
	Tracker.afterFlush( function() {
		//if (Router.current().params.query.printable) window.print();
	})
});
Template.reportRoster.events( {
	"click #btnPrint": async function(e, instance) {
		const singlePageMode = $("input[name='page-per-family']").prop("checked");
		const showAvatars = $("input[name='include-child-avatars']").prop("checked");
		const url = "/reports/reportRoster?printable=true&groupId=" +
			(instance.selectedGroup.get() || "") +
			(singlePageMode ? "&singlepageperfamily=true" : "") +
			(showAvatars ? "&showAvatars=true" : "");
		window.open( url, '_blank');

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "roster"
		});
	},
	"click #btnUpdate": async function() {
		const instance = Template.instance();
		instance.selectedGroup.set($("#selectedGroup").val());
		instance.selectionMade.set(true);
		instance.includeInactive.set($("input[name='include-inactive']").prop("checked"));
		instance.includeAvatars.set($("input[name='include-child-avatars']").prop("checked"));

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "roster"
		});
	}
});

Template.reportRoster.helpers( {
	"reportRows"() {
		if (!Template.instance().selectionMade.get()) return;

		const selectedGroup = Template.instance().selectedGroup.get();
		const includeInactive = Template.instance().includeInactive.get();

		let query = {type:"person", designations: {$nin: ["Wait List"]}};
		if (!includeInactive) {
			query["inActive"] = { "$ne": true };
		}
		if (selectedGroup) {
			query["defaultGroupId"] = selectedGroup;
		}

		const people = Template.instance().personList.get(),
			relPeopleList = Template.instance().relPeopleList.get(),
			relTypes = {"family": "Family", "authorizedPickup": "Authorized Pickup", "emergencyContact": "Emergency Contact"};
		let maxMethods = 0;

		_.each(people, (p) => {
			
			p.contacts = [];
			Relationships.find({ $or: [{ personId: p._id }, { targetId: p._id }] }).forEach( (r) => {
				const cp = new Person(relPeopleList.find(person => {
					return person._id === r.personId && (includeInactive || !person.inActive);
				}));
				if (cp) {
					cp.relationshipType = relTypes[r.relationshipType];
					cp.relationshipDescription = r.relationshipDescription;
					p.contacts.push(cp);
					if (cp.availableContactMethods().length > maxMethods)
						maxMethods = cp.availableContactMethods().length;
				}

			})
			p.contacts = _.sortBy( p.contacts, (c) => c.relationshipType + "|" + c.lastName + "|" + c.firstName);
		});
		return {
			people, maxMethods
		};
	},
	"reportTitle"() {
		const selectedGroupId = Template.instance().selectedGroup.get();
		if (selectedGroupId) {
			const group = Groups.findOne(selectedGroupId);
			if (group) return group.name + " Contacts";
		} else {
			return "All Contacts";
		}
	},
	"filler"(cms = [], maxMethods = 0) {
		return _.times(maxMethods - cms.length, ()=>"x");
	},
	"colspanAmount"(maxMethods = 0) {
		return maxMethods + 2;
	},
	"groups"() {
		return Groups.find({}, {sort:{name:1}});
	},
	"fullLayout"() {
		return FlowRouter.getQueryParam('printable') != "true";
	},
	"singlePageMode"() {
		return Template.instance().singlePageMode.get() == "true";
	},
	getBirthdayPropertyValue(person) {
		return person?.profileData?.birthday || person?.birthday
	},
	getStandardOutlookData(person, variable){
		return person?.standardOutlook?.[variable]
			|| person?.profileData?.standardOutlook?.[variable] || 'N/A'
	},
	showAvatars() {
		return Template.instance().includeAvatars.get();
	}
});