import { Groups } from '../../../lib/collections/groups';
import './reportLaborUtilization.html';

Template.reportLaborUtilization.created = function() {
    let initialMoment = new moment();
    initialMoment = initialMoment.day() <= 1 ? initialMoment.day(1) : initialMoment.day(8)
    this.reportStartMonth = new ReactiveVar(initialMoment.format("MM/DD/YYYY"));
    this.laborUtilizationReportAggregates = new ReactiveVar({groups: [], totals: []});
    this.selectedGroupId = new ReactiveVar();
};

Template.reportLaborUtilization.rendered = function() {
    $('#reportStartMonth').datepicker({
        autoclose: true,
    });
}

Template.reportLaborUtilization.events({
    "click #btnUpdate": async function(event) {
        event.preventDefault();
        $("#btnUpdate").text("Updating").prop('disabled', true);

        const passedValue = $("#reportStartMonth").val(),
            newMoment = new moment(passedValue, "MM/DD/YYYY");

        if (newMoment.day() != 1) {
            $("#btnUpdate").text("Update").prop('disabled', false);
            return mpSwal.fire("Error", "Labor Utilization reports require the Start Date to be a Monday", "error");
        }

        const instance = Template.instance();
        instance.reportStartMonth.set(passedValue);

        fetchLaborUtilizationReportAggregates(Template.instance());

        await Meteor.callAsync('trackClientActivity', {
            label: "report-created",
            reportType: "laborUtilization"
        });
    },
    "click #btnExportCsv": async function() {
        var outputFile = 'export.csv'

        // CSV
        exportTableToCSV.apply(this, [$('#dvData > table'), outputFile]);

        await Meteor.callAsync('trackClientActivity', {
            label: "report-exported",
            reportType: "laborUtilization"
        });
    }
});

Template.reportLaborUtilization.helpers({
    "formatCurrency": function(num) {
        // Create our number formatter.
        var formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        });
        if (num) return formatter.format(num);
    },
    "formattedStartMonth": function(){
        return Template.instance().reportStartMonth.get();
    },
    "groups": function() {
        return Template.instance().laborUtilizationReportAggregates.get().groups;
    },
    "totals": function() {
        return Template.instance().laborUtilizationReportAggregates.get().totals;
    },
    "availableGroups": function () {
        return Groups.find({}, {sort:{"name":1}});
    }
});

var fetchLaborUtilizationReportAggregates = function (instance) {
    const selectedGroupId = $("#selectedGroupId").val();
    instance.selectedGroupId.set(selectedGroupId);
    Meteor.callAsync("classListReportAggregates", {
        startMonth: instance.reportStartMonth.get(),
        selectedGroupId,
        laborUtilizationOnly: true
    }).then((response) => {
        $("#btnUpdate").text("Update").prop('disabled', false);
        instance.laborUtilizationReportAggregates.set(response[0]);
    }).catch((error) => {
        $("#btnUpdate").text("Update").prop('disabled', false);
        alert(error);
    });
};
