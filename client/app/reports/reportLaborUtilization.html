<template name="reportLaborUtilization">

    <div class="card card-custom">
        <div class="card-header flex-wrap border-0 pt-6 pb-0">
            <div class="card-title">
                <h3 class="card-label">Labor Utilization Report
                    <span class="text-muted pt-2 font-size-sm d-block">Center level details for staff utilization.</span></h3>
            </div>
            <div class="card-toolbar">
                <div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
                    <i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
                </div>
                <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
                    Update
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="box">

                <div class="box-body">
                    <div class="row">
                        <div class="col-2">
                            <div class="form-group">
                                <label>Start Date</label>
                                <div class="input-group">
                                    <input type="text" class="form-control pull-right" id="reportStartMonth" value="{{formattedStartMonth}}">
                                </div>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Group</label>
                                <select class="form-control" id="selectedGroupId">
                                    <option value=""></option>
                                    {{#each availableGroups}}
                                        <option value="{{_id}}">{{name}}</option>
                                    {{/each}}
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="box-body no-padding" id="dvData">
                    {{#if groups}}
                        <table class="table  fix-head-table" id="moment-report-table" style="display:block;overflow-x:auto;">
                            <tbody>
                                <tr>
                                    <th class="text-center align-bottom">Group</th>
                                    <th class="text-center align-bottom">Loaded Monthly Payroll</th>
                                    <th class="text-center align-bottom">Payroll Ratio</th>
                                    <th></th>
                                </tr>
                                {{#each groups}}
                                    <tr>
                                        <td style="font-weight:bold;"><span class="text-nowrap">{{name}}</span></td>
                                        <td class='no-wrap text-right'>{{formatCurrency monthlyLoadedPayroll}}</td>
                                        <td class='no-wrap text-right'>{{payrollRatio}}%</td>
                                    </tr>
                                {{/each}}
                                <tr>
                                    <td class='no-wrap' style='font-weight:bold'>Totals</td>
                                    <td class='no-wrap text-right'>{{formatCurrency totals.monthlyLoadedPayroll}}</td>
                                    <td class='no-wrap text-right'>{{totals.payrollRatio}}%</td>
                                </tr>
                            </tbody>
                        </table>
                    {{/if}}
                </div>
            </div>
        </div>
    </div>
</template>
