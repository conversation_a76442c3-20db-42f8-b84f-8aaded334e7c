<template name="attendanceGridByDateEmptyResults">
	<div class="text-center my-10" style="font-size: 16px;">
		No attendance records found for the selected date.
	</div>
</template>


<template name="attendanceGridByDate">
	
	<style>
		.group-title-container {
			margin-bottom: 20px;
		}
		.group-title-content {
			margin: 10px;
			background: var(--light-primary);
			padding: 10px;
			border-radius: 10px;
		}
	</style>

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">
					Attendance Grid by Date
					<span class="text-muted pt-2 font-size-sm d-block">Generate sign-in/sign-out grid for groups by date</span>
				</h3>
			</div>
			<div class="card-toolbar">
				{{#unless shouldHideHeaderForPrint}}
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint" data-cy="print-btn">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Print View
					</div>
					{{#if getReportIsUpdating }}
						<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" disabled>
							Updating...
						</div>
					{{else}}
						<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
							Update
						</div>
					{{/if}}
				{{/unless}}
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<!--
				<div class="box-header">
				</div>
				-->
				{{#if fullLayout}}
				<div class="box-header">
					<div class="row">
						<div class="col-lg-2">
							<div class="form-group">
								<label>Date:</label>
								<div class="input-group">
									<input type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}" data-cy="start-week-of-input">
								</div>
							</div>
						</div>
						<div class="col-lg-10 d-flex align-items-center">
							<div class="form-group mr-4">
								<label>Schedule Types:</label>
								{{> reportScheduleTypesField emptyOptionLabel="(No Schedule Type)" emptyOptionValue="" }}
							</div>
							<div class="form-group">
								<label>Group:</label>
								<select class="form-control" id="selectedGroup" data-cy="select-group">
									<option value="">All</option>
									{{#each groups}}
									<option value="{{_id}}">{{name}}</option>
									{{/each}}
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-4">
							<div class="checkbox-list">
								<label class="checkbox checkbox-primary">
									<input type="checkbox" class="form-check-input" name="groupByScheduleType" id="groupByScheduleType" data-cy="group-by-schedule-type">
								<span></span>
								Group by Schedule Type
								</label>
							</div>
						</div>
					</div>
					<br/>
				</div><!-- /.box-header -->
				{{/if}}
				<div class="box-body no-padding">

					{{#if getReportIsUpdating}}
						<div class="d-flex" style="height: 60px; justify-content: center; align-items: center;">
							<div class="spinner spinner-primary spinner-lg"></div>
						</div>
					{{/if}}

					<!-- Check hasMadeSelection: -->

					{{#if hasMadeSelection}}

						{{#if reportGroups}}
							{{#each group in reportGroups}}

								<!-- If it's after the first item, add a page break: -->

								{{#if @index}}
									<div style="page-break-after: always;"></div>
								{{/if}}

								<div class="group-title-container">
									<div class="group-title-content">
										<div class="row text-center justify-content-center">
											<div class="col-12 col-md-8 col-xl-6">
												<h2 class="font-weight-bold">
													{{group.title}}
												</h2>
											</div>
										</div>
										<div class="row">
											<div class="col-6 text-left">
												<h4 class="font-weight-bold">
													Generated on: {{formattedGeneratedOnDateForReport}}
												</h4>
												<h4 class="font-weight-bold">
													Report Date: {{formattedStartDateForReport}}
												</h4>
											</div>
											<div class="col-6 text-right">
												<h4 class="font-weight-bold">
													{{formattedOrgName}}
												</h4>
												<h4 class="font-weight-bold">
													Check In/Out Sheet
												</h4>
											</div>
										</div>
									</div>
								</div>
								<div>

									{{#if group.rows.length }}

										<table class="table table-bordered" data-cy="attendance-report-table">
											<thead>

												<tr>
													<th>#</th>
													<th>Name</th>
													<th>Group</th>
													{{#if showGrade}}
													<th>Grade</th>
													{{/if}}
													<th>Schedule</th>
													<th style="width: 100px">Time In</th>
													<th style="width: 150px">Signature</th>
													<th style="width: 100px">Time Out</th>
													<th style="width: 150px">Signature</th>
												</tr>

											</thead>
											<tbody>

											{{#each person in group.rows}}
											<tr>
												<td>
													{{incremented @index}}
												</td>
												<td>{{person.lastName}}, {{person.firstName}}</td>
												<td>{{person.cellGroupName}}</td>
												{{#if showGrade}}
												<td style="white-space: nowrap">{{person.studentGrade}}</td>
												{{/if}}
												<td>
													{{{getFormattedScheduleCell person.cellScheduleType}}}
												</td>
												<td>{{person.timeIn}}</td>
												<td>{{person.timeInSignature}}</td>
												<td>{{person.timeOut}}</td>
												<td>{{person.timeOutSignature}}</td>
											</tr>
											{{/each}}

											</tbody>
										</table>

									{{else}}
										{{> attendanceGridByDateEmptyResults }}
									{{/if}}

								</div>
							{{/each}}
						{{else}}
							{{> attendanceGridByDateEmptyResults }}
						{{/if}}


					{{/if}}
		
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
