<template name="reportScheduling">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Scheduling Report
				<span class="text-muted pt-2 font-size-sm d-block">Determine scheduling availability based on reservations.</span></h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
			
				<div class="box-body">
				  <div class="row">
					<div class="col-lg-3">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								<div class="input-group-addon" id="calendarBox">
									<i class="fa fa-calendar"></i>
								</div>
								<input type="text" class="form-control pull-right" id="schedulingStartDate">
							</div>
						</div>
					</div>
					
				  </div>
				  
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table class="table">
					<tbody><tr>
					  <th>Start Time</th>
					  <th>Level 1</th>
					  <th>Level 2</th>
					  <th>Level 3</th>
					  <th>Required Employees</th>
					  <th>Scheduled Employees</th>
					  <th>Surplus/Deficit</th>
					</tr>
					
					{{# each timeslots}}
					<tr>
					  <td>{{formattedTime momentSlotStart}}</td>
					  <td>{{peopleCount.level1}}</td>
					  <td>{{peopleCount.level2}}</td>
					  <td>{{peopleCount.level3}}</td>
					  <td>{{requiredEmployees}}</td>
					  <td>{{staffCount}}</td>
					  <td>{{surplusDeficit}}</td>
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
