<template name="reportWaitList">
  <div id="waitlist-report-container">
    <div class="card card-custom mb-3">
      <div class="card-header flex-wrap border-0 pt-6 pb-6">
        <div class="card-title">
          <h3 class="card-label">WaitList Report
            <span class="text-muted pt-2 font-size-sm d-block">Active and Waitlisted people per group</span>
          </h3>
        </div>
        <div class="card-toolbar">
          <div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
            <i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
          </div>
          <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
            Update
          </div>
        </div>
      </div>
    </div>

    {{#if groupTotals}}
          <div class="card mr-3 mb-3">
            <div class="card-body">
              <table id="group-totals" style="font-size:125%;width:100%">
                <tbody>
                  <tr>
                    <td style="width:20%"><small>Total Capacity</small></td>
                    <td style="width:20%"><small>Total Preferred Capacity</small></td>
                    <td style="width:20%"><small>Total Enrolled</small></td>
                    <td style="width:20%"><small>Total Variance</small></td>
                    <td style="width:20%"><small>Occupancy</small></td>
                  </tr>
                  <tr>
                    <td style="width:20%"><strong>{{groupTotals.capacity}}</strong></td>
                    <td style="width:20%"><strong>{{groupTotals.preferredCapacity}}</strong></td>
                    <td style="width:20%"><strong>{{groupTotals.enrolled}}</strong></td>
                    <td style="width:20%"><strong>{{groupTotals.variance}}</strong></td>
                    <td style="width:20%"><strong>{{groupTotals.occupancy}}%</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
    {{/if}}
    <div class="box-body no-padding mb-3" id="dvData" style="overflow-x:scroll">
      <table id="waitlist-table" style="background-color: white;overflow-x:auto;cursor: grab;">
        <tr >

        {{#each group in groups}}
          <td style="vertical-align:top;height:inherit" class="pb-3">
            <div class="card mr-3 mb-3" style="min-width:650px;height:100%">
              <div class="card-body">
                <table style="width:100%" id="group-header-{{group._id}}">
                  <tbody>
                    <tr>
                      <td rowspan=2><h6>{{group.name}}</h6></td>
                      <td class="text-nowrap" style="width:17%"><small>Preferred Capacity</small></td>
                      <td style="width:15%"><small>Ratio</small></td>
                      <td style="width:15%"><small>FTE's (current)</small></td>
                      <td style="width:15%"><small>Variance</small></td>
                      <td style="width:15%"><small>Occupancy</small></td>
                      <td style="width:0"></td>
                      <td style="width:0"></td>
                      <td style="width:0"></td>
                    </tr>
                    <tr>
                      <td class="text-nowrap" style="width:17%"><strong>{{group.preferredCapacity}}</strong></td>
                      <td style="width:15%"><strong>1:{{group.ratio}}</strong></td>
                      <td style="width:15%"><strong>{{formatNumber group.fteTotals "0.0"}}</strong></td>
                      <td style="width:15%"><strong>{{group.totalVariance}}</strong></td>
                      <td style="width:15%"><strong>{{group.totalOccupancy}}%</strong></td>
                      <td style="width:0"></td>
                      <td style="width:0"></td>
                      <td style="width:0"></td>
                    </tr>
                  </tbody>
                </table>
                <table class="mt-3 table table-sm table-striped" id="group-active-{{group._id}}">
                  <tbody>
                    <tr>
                      <th class="text-nowrap align-bottom">Name</th>
                      <th class="text-nowrap align-bottom">Birthday</th>
                      <th class="text-nowrap align-bottom">Age</th>
                      <th class="text-nowrap  align-bottom">FTE</th>
                      <th class="text-nowrap align-bottom">Schedule</th>
                      <th class="text-nowrap align-bottom">Transition</th>
                      <th class="text-nowrap align-bottom">Destination</th>
                      <th class="text-nowrap align-bottom">Withdraw</th>
                      <th></th>
                    </tr>
                    {{#each person in group.groupPeople}}
                      <tr>
                        <td class="text-nowrap"><a href="/people/{{person._id}}#profile" target="_blank">{{person.name}}</a></td>
                        <td class="text-nowrap">{{formatDate person.birthday "M/DD/YY"}}</td>
                        <td class="text-nowrap">{{person.age}}</td>
                        <td class="text-nowrap">{{person.currentFte}}</td>
                        <td class="text-nowrap">{{reformatScheduleLabel person.scheduleLabelDaysOnly}}</td>
                        <td class="text-nowrap">{{formatDate person.transition "M/DD/YY"}}</td>
                        <td class="text-nowrap">{{person.destination}}</td>
                        <td class="text-nowrap">{{formatDate person.withdrawDate "M/DD/YY"}}</td>
                        <td></td>
                      </tr>
                    {{/each}}

                  </tbody>
                </table>
              </div>
            </div>
          </td>
          {{/each}}
        </tr>
        <tr>
          {{#each group in groups}}
          <td style="vertical-align:top;height:inherit">
            <div class="card mr-3 mb-3" style="min-width:650px;height:100%">
              <div class="card-body">
               
                <table class="mt-3 table table-sm table-striped" id="group-preselected-{{group._id}}">
                  <tbody>
                    <tr>
                      <th colspan=8> <h6>{{group.name}} Pre-selected List</h6></th>
                    </tr>
                    <tr>
                      <th class="align-bottom">Name</th>
                      <th class="align-bottom">Birthday</th>
                      <th class="align-bottom">Age</th>
                      <th class="align-bottom">Added To Waitlist</th>
                      <th class="align-bottom">Desired Schedule</th>
                      <th class="align-bottom">Anticipated Start Date</th>
                      <th class="align-bottom">Actual Start Date</th>
                      <th style="width:0"></th>
                    </tr>
                    {{#each person in group.prospects}}
                      <tr>
                        <td class='text-nowrap'><a href="/people/{{person._id}}#profile" target="_blank">{{person.name}}</a></td>
                        <td class='text-nowrap'>{{formatDate person.birthday "M/DD/YY"}}</td>
                        <td class='text-nowrap'>{{person.age}}</td>
                        <td class='text-nowrap'>{{formatDate person.waitlistAddedDate "M/DD/YY"}}</td>
                        <td class='text-nowrap'>{{reformatScheduleLabel person.scheduleLabel}}</td>
                        <td class='text-nowrap'>{{formatDate person.anticipatedStartDate "M/DD/YY"}}</td>
                        <td class='text-nowrap'>{{formatDate person.enrollmentDate "M/DD/YY"}}</td>
                        <td style="width:0"></td>
                      </tr>
                    {{/each}}
                  
                  </tbody>
                </table>
              </div>
            </div>
          </td>
        {{/each}}
        </tr>
      </table>
    </div>
        
        {{#if groupTotals}}
          <div class="card mr-3 mb-3">
            <div class="card-body">
              <h6>Unassigned</h6>
              <table class="mt-3 table table-sm table-striped" id="group-unassigned">
                <tbody>
                  <tr>
                    <th>Name</th>
                    <th>Birthday</th>
                    <th>Age</th>
                    <th>Added To Waitlist</th>
                    <th></th>
                    <th></th>
                    <th>Anticipated Start Date</th>
                    <th></th>
                  </tr>
                  {{#each person in getUnassignedPeople}}
                    <tr>
                      <td class='text-nowrap pr-10'><a href="/people/{{person._id}}#profile" target="_blank">{{person.name}}</a></td>
                      <td class='text-nowrap pr-10'>{{formatDate person.birthday "M/DD/YY"}}</td>
                      <td class='text-nowrap pr-10'>{{person.age}}</td>
                      <td class='text-nowrap pr-10'>{{formatDate person.waitlistAddedDate "M/DD/YY"}}</td>
                      <td class='text-nowrap pr-10'>{{reformatScheduleLabel person.scheduleLabelDaysOnly}}</td>
                      <td class='text-nowrap pr-10'>{{person.destination}}</td>
                      <td class='text-nowrap pr-10'>{{formatDate person.anticipatedStartDate "M/DD/YY"}}</td>
                      <td class='text-nowrap pr-10'></td>
                    </tr>
                  {{/each}}
                </tbody>
              </table>
            </div>
          </div>
        {{/if}}
      
    </div>
</template>