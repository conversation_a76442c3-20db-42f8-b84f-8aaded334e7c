<template name="reportStaffPay">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Staff Pay Report</h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->

				<div data-cy="print-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				{{#unless isSavedMode }}
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					{{updateLabel}}
				</div>
				{{/unless}}
				
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
							{{#if isSavedMode}}
							<div class="row font-weight-bolder text-dark">
								<div class="col">
									{{{ savedHeader }}}
								</div>
							</div>
							{{/if}}
							{{#unless isSavedMode}}
							<div class="row">
								{{#if showOrgSelection}}
								<div class="col-sm-3">
									<div class="form-group">
										<label>Select Orgs</label>
										<div class="input-group">
											{{> announcementOrgsField }}
										</div>
									</div>
								</div>
								{{/if}}
								<div class="col-sm-3 pt-8">
									<div class="form-group">
										<div class="checkbox-list">
											<br/>
											<label data-cy="include-admins-checkbox" class="checkbox"><input type="checkbox" value="" id="includeAdmins"><span></span>Include Admins</label>
										</div>
									</div>
								</div>
								{{> reportQueueCheckbox }}
							</div>
							{{/unless}}
							<br/>
							
						
							{{#if reportRows}}
								<table data-cy="staff-pay-report-table" class="table">
									<thead>
										<tr>
											<th><label>Site</label></th>
											<th><label>Name</label></th>
											<th><label>Pay Rate</label></th>
											<th><label>Status</label></th>
										</tr>
									</thead>
									<tbody>
										{{#each row in reportRows}}
											<tr>
												<td>{{ row.site }}</td>
												<td>{{ row.name }}</td>
												<td>{{ row.payRate }}</td>
												<td>{{ row.status }}</td>
											</tr>
										{{/each}}
									</tbody>
								</table>
							{{/if}}
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</template>
