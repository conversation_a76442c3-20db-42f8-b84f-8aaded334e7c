import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ReactiveDict } from 'meteor/reactive-dict';
import moment from 'moment-timezone';
import {GeneralAttendanceReportUtils} from "../../../lib/util/generalAttendanceReportUtils";
import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService";
import './reportGeneralAttendance.html';
import { User } from '../../../lib/collections/users';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import './reportOrgsField';
import './reportQueueTemplate';
import './reportSubsidyAgencyField';
import '../../app/components/exportCsvAndPdf/exportCsvAndPdfGeneralAttendance';
import { reportQueuedSwal, upperFirst } from './queueReportsUtil';

Template.reportGeneralAttendance.created = function() {
	var instance = this;
	const defaultSortType = (Meteor.user() && Meteor.user().uiOptions && Meteor.user().uiOptions.attendanceReportSort) || "attendeeFirstName";
	this.sortType = new ReactiveVar(defaultSortType);
	this.showAutomaticDownloadMessage = new ReactiveVar(false);
	this.attendees = new ReactiveVar();
	this.params = new ReactiveVar();
	this.groupedMoments = new ReactiveVar([]);
	this.cachedData = new ReactiveVar([]);
	this.pageNumber = new ReactiveVar(1);
	this.maxPageCount = new ReactiveVar();
	this.pageLimit = new ReactiveVar(50);
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	this.disableEmail = new ReactiveVar();
	this.showSingleChildPerPageWhenQueued = new ReactiveVar(false);
	this.personList = new ReactiveVar([]);
	this.includeIdNumber = new ReactiveVar(false);
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId }).then(async (response) => {
			if (!response) {
				return;
			}
			this.savedMode.set(true);
			const reportArgs = response.args;
			this.showSingleChildPerPageWhenQueued.set(reportArgs.showSingleChildPerPage);
			this.sortType.set(reportArgs.sortType);
			await Meteor.callAsync("setUiOption", "attendanceReportSort", reportArgs.sortType);
			let header;
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgIds }).then((resp) => {
				header = 'Orgs: ' + resp + '<br>';
			}).catch((err) => {
				header = 'Orgs:  <br>';
			}).finally(async () => {
				header += 'Start Date: ' + reportArgs.reportStartDate + '<br>';
				header += 'End Date: ' + reportArgs.reportEndDate + '<br>';
				let personLabel = 'All';
				const typeLabel = upperFirst(reportArgs.personType) || 'All';
				let groupLabel = 'All';
				if(reportArgs.personId) {
					
					const person = await getPeopleById(reportArgs.personId);
					personLabel = person.firstName + ' ' + person.lastName;
				}
				if(reportArgs.groupId) {
					groupLabel = Groups.findOne({_id: reportArgs.groupId}).name;
				}
				header += 'Person: ' + personLabel + '<br>';
				header += 'Type: ' + typeLabel + '<br>';
				header += 'Group: ' + groupLabel + '<br>';
				header += 'Sort By: ' + getSortTypeLabel(reportArgs.sortType) + '<br>';
				const subsidyDescriptions = replaceTypesWithDescriptions(reportArgs.subsidyFilterValues);
				header += 'Subsidy Agency: ' + subsidyDescriptions.join(', ') + '<br>';
				this.savedHeader.set(header);
			});
			const data = JSON.parse(response.data);
			if(data.length > 500) {
				this.showAutomaticDownloadMessage.set(true);
				downloadCSV(data, 'attendance.csv');
				this.attendees.set(data);
			}else{
				this.attendees.set(data);
			}
		}).catch((err) => {
			return;
		});
	}

	function replaceTypesWithDescriptions(strings) {
		return strings.map(str => {
			const types = Orgs.current()?.availablePayerSources(true)
			const match = types.find(type => type.type === str);
			return match ? match.description : str;
		});
	}

	
	getPeopleData({"type":{"$in":["person"]}}, {sort: {lastName:1, firstName:1}, fields: {_id:1, lastName:1, firstName:1, inActive: 1}}).then(res => {
		this.personList.set(res)
	}).catch(err => {
		console.log(err);
	});
}

Template.reportGeneralAttendance.rendered = function() {
	$('#attendanceStartDate').datepicker({autoclose:true});
	$('#attendanceEndDate').datepicker({autoclose:true});
}

Template.reportGeneralAttendance.events({
	"change #sortType": function(e, instance) {
		const selectedSortType = e.target.value;
		instance.sortType.set(selectedSortType);
		// Remove the queueMode if selected before choosing date sort type
		if (selectedSortType === 'attendanceDate' && document.getElementById('chkchildPerPage').checked) {
			instance.queueMode.set(false);
			instance.disableEmail.set(true);
		}
		else {
			instance.disableEmail.set(false);
		}
	},
	"click #chkchildPerPage": function(e, i) {
		if (i.sortType.get() === 'attendanceDate' && e.target.checked) {
			i.queueMode.set(false);
			i.disableEmail.set(true);
		}
		else {
			i.disableEmail.set(false);
		}
	},
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
	"click #chkIdNumber": function(e, i) {
		i.includeIdNumber.set(document.getElementById('chkIdNumber').checked);
	},
	"change #pageLimit": function(e, i) {
		const pageLimit = e.target.value;
		if(pageLimit === 'all') {
			i.pageLimit.set(null);
			return;
		}

		i.pageLimit.set(parseInt(pageLimit));
	},
	"click #previousBtn": function(e, i) {
		e.preventDefault();
		$("#btnUpdate").text("Updating").prop('disabled', true);
		i.attendees.set([]);
		const pageNumber = i.pageNumber.get() - 1;
		const cachedData = i.cachedData.get();
		const pageLimit = i.pageLimit.get();
		if(cachedData[pageNumber - 1]) {
			i.attendees.set(cachedData[pageNumber - 1]);
			i.pageNumber.set(pageNumber);
			$("#btnUpdate").text("Update").prop('disabled', false);
			return;
		}

		const groupedMoments = i.groupedMoments.get();
		const params = i.params.get();
		const paginatedMoments = getElementsByPage(groupedMoments, pageLimit, pageNumber);
		params.groupedMoments = paginatedMoments;
		i.pageNumber.set(pageNumber);
		i.params.set(params);
		Meteor.callAsync('processAttendanceData', params).then((result) => {
			const data = JSON.parse(result);
			cachedData[pageNumber - 1] = data;
			i.cachedData.set(cachedData);
			i.attendees.set(data);
			$("#btnUpdate").text("Update").prop('disabled', false);
		}).catch((error) => {
			mpSwal.fire(error.reason, error.details, "error");
			$("#btnUpdate").text("Update").prop('disabled', false);
		});
	},

	"click #nextBtn": function(e, i) {
		e.preventDefault();
		$("#btnUpdate").text("Updating").prop('disabled', true);
		i.attendees.set([]);
		const pageNumber = i.pageNumber.get() + 1;
		const cachedData = i.cachedData.get();
		const pageLimit = i.pageLimit.get();

		if(cachedData[pageNumber - 1]) {
			i.attendees.set(cachedData[pageNumber - 1]);
			i.pageNumber.set(pageNumber);
			$("#btnUpdate").text("Update").prop('disabled', false);
			return;
		}

		const groupedMoments = i.groupedMoments.get();
		const params = i.params.get();
		const paginatedMoments = getElementsByPage(groupedMoments, pageLimit, pageNumber);
		params.groupedMoments = paginatedMoments;
		i.pageNumber.set(pageNumber);
		i.params.set(params);
		Meteor.callAsync('processAttendanceData', params).then((result) => {
			const data = JSON.parse(result);
			cachedData[pageNumber - 1] = data;
			i.cachedData.set(cachedData);
			i.attendees.set(data);
			$("#btnUpdate").text("Update").prop('disabled', false);
		}).catch((error) => {
			mpSwal.fire(error.reason, error.details, "error");
			$("#btnUpdate").text("Update").prop('disabled', false);
		});
	},
	"click #btnUpdate": async function() {
		$("#btnUpdate").text("Updating").prop('disabled', true);
		const instance = Template.instance();

		const params = {
			reportStartDate: $("#attendanceStartDate").val(),
			reportEndDate: $("#attendanceEndDate").val(),
			personId: $("#filterPerson").val(),
			groupId: $("#filterGroup").val(),
			personType: $("#filterType").val(),
			orgIds: $("#reportOrgs").val(),
			ignoreStaff: true,
			sortType: $("#sortType").val(),
			showAbsences: $("#chkAbsences").is(":checked"),
			showSingleChildPerPage: $("#chkchildPerPage").is(":checked"),
			subsidyFilterValues: $("#filterAvailablePayers").val()
		};
		instance.sortType.set($("#sortType").val());
		//reset the page number and cached data
		instance.pageNumber.set(1);
		instance.cachedData.set([]);
		await Meteor.callAsync("setUiOption", "attendanceReportSort", $("#sortType").val());

		if (instance.queueMode.get()) {
			$("#btnUpdate").text("Update").prop('disabled', false);
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'attendanceReport',
				reportName: 'General Attendance Report',
				reportArgs: params,
				userId: Meteor.userId(),
				reportRoute: 'reports/reportGeneralAttendance'
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "generalAttendance"
			});
			reportQueuedSwal();
			return;
		}

		Meteor.callAsync('getAttendanceReportData', params).then(async (result) => {
			const {groupedMoments, orgsScope} = result;

			// Default to logged in org if no org is selected
			const selectedOrgs = params.orgIds?.length > 0 ? params.orgIds : [Orgs.current()._id];
			const subsidyFilterValues = $("#filterAvailablePayers").val()
			const filteredGroupMoments = await GeneralAttendanceReportUtils.filterGroupedMoments(groupedMoments, selectedOrgs, subsidyFilterValues);
			instance.groupedMoments.set(filteredGroupMoments);
			instance.maxPageCount.set(calculateMaxPages(filteredGroupMoments.length, instance.pageLimit.get()));
			const pageLimit = instance.pageLimit.get();
			params.orgsScope = orgsScope;
			params.groupedMoments = getElementsByPage(filteredGroupMoments, pageLimit);
			instance.params.set(params);
			// this process is what takes the most time so we want to break it up into chunks
			Meteor.callAsync('processAttendanceData', params).then((resp) => {
				$("#btnUpdate").text("Update").prop('disabled', false);
				const data = JSON.parse(resp);

				instance.cachedData.set([data]);
				instance.attendees.set(data);
			}).catch((error) => {
				$("#btnUpdate").text("Update").prop('disabled', false);
				mpSwal.fire(error.reason, error.details, "error");
			});
		}).catch((error) => {
			mpSwal.fire(error.reason, error.details, "error");
			$("#btnUpdate").text("Update").prop('disabled', false);
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "generalAttendance"
		});
	},
	"click #btnExportCsv": async function(e, i) {
		e.preventDefault();
		/*
		var csvContent = Papa.unparse(_.map(i.attendees.get(), function(o) {
			let outLine = {
				"Attendee": o.attendeeName,
				"Date": o.attendanceDate,
				"Payer": o.attendeePayer,
				"Check-In": o.checkInTime ? (o.checkInTime.time ? o.checkInTime.time : moment(o.checkInTime.sortStamp).format("hh:mm a")) : null,
				"Check-Out": o.checkOutTime ? (o.checkOutTime.time ? o.checkOutTime.time : moment(o.checkOutTime.sortStamp).format("hh:mm a")) : null,
				"Transportation (Check-In)": o.attendeeTransportation,
				"Transportation (Check-Out)": o.attendeeCheckoutTransportation,
				"Elapsed Time": o.elapsedTime,
				"Elapsed Time (units)": o.elapsedTimeUnits,
				"Schedule Days": o.scheduleDays,
				"Staff Member": o.staffMemberName,
				"Variance (mins)": o.checkOutVariance
			};
			if (Orgs.current().hasCustomization("report/attendance/showCarePlanObjective"))
				outLine["Objective"] = o.carePlanObjective;
			return outLine;
		}));
    	
    	DownloadFile(csvContent, "attendance.csv");
		*/
		if(i.showAutomaticDownloadMessage.get()){
			downloadCSV(i.attendees.get(), 'attendance.csv');
		}else{
			var outputFile = 'attendance.csv'
			exportTableToCSV.apply(this, [$('#dvData'), outputFile]);

			await Meteor.callAsync('trackClientActivity', {
				label: "report-exported",
				reportType: "generalAttendance"
			});
		}
	}
});

Template.reportGeneralAttendance.helpers({
	showSingleChildPerPage: function() {
		return Template.instance().showSingleChildPerPageWhenQueued.get();
	},
	formatReimbursementTypes: function(reimbursementTypes=[]) {
		return reimbursementTypes?.filter((rt) => rt !== null).join(', ');
	},
	isSortTypeAttendanceDate() {
		return Template.instance().sortType.get() === 'attendanceDate' && Template.instance().disableEmail.get();
	},
	"isPageBreakchecked"(addPageBreak) {
		return addPageBreak ? "page-break-after: always;" : "";
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
	"formattedStartDate": function(){
		return new moment().add(-1, "days").format("MM/DD/YYYY");
	},
	"formattedEndDate": function(){
		return new moment().format("MM/DD/YYYY");
	},
	"people": function() {
		return Template.instance().personList.get();
	},
	"groups": function() {
		return Groups.find({}, {sort: {name: 1}});
	},
	"attendees": function() {
		return Template.instance().attendees.get();
	},

	"currentSortType": function() {
		return (Meteor.user() && Meteor.user().uiOptions && Meteor.user().uiOptions.attendanceReportSort) || "attendeeFirstName";
	},
	"showStaffMember": function() {
		return Orgs.current().language === "translationsEnAdultCare";
	},
	"showElapsedTimeUnits": function() {
		return Orgs.current().language === "translationsEnAdultCare";
	},
	"showObjectiveField": function() {
		return Orgs.current().hasCustomization("report/attendance/showCarePlanObjective");
	},
	"showIdNumber": function() {
		return Template.instance().includeIdNumber.get();
	},
	"showOrg": function() {
		const attendees = Template.instance().attendees.get();
		return attendees && attendees.length > 0 && attendees[0].orgName;
	},
	previousDisabled() {
		return Template.instance().pageNumber.get() === 1 ? 'disabled' : '';
	},
	nextDisabled() {
		return Template.instance().pageNumber.get() === Template.instance().maxPageCount.get() ? 'disabled' : '';
	},
	getPage() {
		const pageNumber = Template.instance().pageNumber.get();
		const maxPageCount = Template.instance().maxPageCount.get();
		return `Page ${pageNumber} of ${maxPageCount}`;
	},
	showPageNav() {
		return Template.instance().maxPageCount.get() > 1;
	},
	currentLimit() {
		return Template.instance().pageLimit.get();
	},
	getElapsedTimeUnits(elapsedTime) {
		return elapsedTime ? (elapsedTime * 60 / 15).toFixed(2) : ""
	},
	showDownloadMessage() {
		return Template.instance().showAutomaticDownloadMessage.get();
	}
});

const getSortTypeLabel = function(sortType) {
	switch(sortType) {
		case 'attendanceDate':
			return 'Date';
		case 'attendeeFirstName':
			return 'First Name';
		case 'attendeeLastName':
			return 'Last Name';
		default:
			return '';
	}
}

const getElementsByPage = function(arr, limit, pageNumber = 1) {
	if(!limit) {
		return arr;
	}

	const startIndex = limit * (pageNumber - 1);
	const endIndex = startIndex + limit;
	return arr.slice(startIndex, endIndex);
}

const calculateMaxPages = function(arrLength, limit) {
	if(!limit) {
		return 1;
	}

	return Math.ceil(arrLength / limit);
}

function convertArrayToCSV(dataArray, customHeaders) {
	const csvRows = [];

	const header = Object.values(customHeaders);
	csvRows.push(header.join(','));

	dataArray.forEach((dataItem) => {
		const values = Object.keys(customHeaders).map(column => dataItem[column]);
		csvRows.push(values.join(','));
	});

	// Join rows with line breaks
	return csvRows.join('\n');
}

const downloadCSV = function(dataArray, fileName) {
	const org = Orgs.current();
	const showTimeUnitsAndStaff = org.language === "translationsEnAdultCare";
	const showPayer = org.hasCustomization("people/types/showPayer");
	const showTransportation = org.hasCustomization('moments/checkin/showTransportation');
	const showObjectiveField = org.hasCustomization("report/attendance/showCarePlanObjective");

	const headers = {
		name: 'Name',
		inActive: 'Inactive',
		date: 'Date',
		payer: 'Payer',
		cInTime: 'Check-In',
		cOutTime: 'Check-Out',
		cInBy: 'Checked In By',
		cOutBy: 'Checked Out By',
		trans: 'Transportation (Check-In)',
		cOutTrans: 'Transportation (Check-Out)',
		elapsedTime: 'Elapsed Time',
		scheduleOnDay: 'Schedule',
		variance: 'Variance (mins)',
		staffName: 'Staff Member',
		carePlan: 'Objective',
		orgName: 'Org'
	}
	// delete instead of add because they are in a specific order and this is easiest.
	if(!showTimeUnitsAndStaff) {
		delete headers.elapsedTime;
		delete headers.staffName;
	}

	if(!showPayer) {
		delete headers.payer;
	}

	if(!showTransportation) {
		delete headers.trans;
		delete headers.cOutTrans;
	}

	if(!showObjectiveField) {
		delete headers.carePlan;
	}

	const csv = convertArrayToCSV(dataArray, headers);
	const blob = new Blob([csv], { type: 'text/csv' });
	const url = window.URL.createObjectURL(blob);

	const a = document.createElement('a');
	a.style.display = 'none';
	a.href = url;
	a.download = fileName;

	document.body.appendChild(a);
	a.click();

	window.URL.revokeObjectURL(url);
}
