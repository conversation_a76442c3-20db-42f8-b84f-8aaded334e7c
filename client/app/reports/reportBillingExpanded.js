import './reportBillingExpanded.html';
import <PERSON> from 'papaparse';

Template.reportBillingExpanded.created = function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportEndDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.billingReportAggregates = new ReactiveVar();
};

Template.reportBillingExpanded.rendered = function() {
	$('#startDate').datepicker({autoclose:true});
	$('#endDate').datepicker({autoclose:true});

}

Template.reportBillingExpanded.events({
	"click #btnUpdate": async function() {
		
		var instance = Template.instance();
		instance.reportStartDate.set($("#startDate").val());
		instance.reportEndDate.set($("#endDate").val());

		fetchBillingReportAggregates(Template.instance());

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "billingExpanded"
		});
	},
	"click #btnExportCsv": async function() {
		const data = Template.instance().billingReportAggregates.get();
		const replaceBreak = new RegExp('\<br\/\>', 'g');
		console.log("data", data);
		
		var csvContent = Papa.unparse(_.map(data, function(o) {
			return {
				"Guest Name": o.name,
				"Payment Classification": o.paymentClassification,
				"# Full Days": o.numFullDays,
				"Days Attended": o.detailedDates.replace(replaceBreak,", "),
				"Scholarship Allowance": o.scholarshipAllowance,
				"Scholarship Amount": o.scholarshipAmount,
				"Hours": numeral(o.hours).format("0.00"),
				"Units": numeral(o.units).format("0.00"),
				"# No Shows": o.noShows,
				"# Billable No Shows": o.billableNoShows,
				"Dates of Billable NoShows": o.noShowDates.replace(replaceBreak,", "),
				"# Cancellation": o.cancellations,
				"# Billable Cancellations": o.billableCancellations,
				"Dates of Billable Cancellations": o.billableCancellationDays.replace(replaceBreak,", "),
				"# Billable Late Minutes": o.lateMinutes
			};
		}));
    	
    	DownloadFile(csvContent, "billingReportExpanded.csv");

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "billingExpanded"
		});
	}
});

Template.reportBillingExpanded.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formattedEndDate": function(){
		return Template.instance().reportEndDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.createdAt).format("hh:mm a");
	},
	"entries": function() {
		return Template.instance().billingReportAggregates.get();
	}
});

var fetchBillingReportAggregates = function (instance) {
	Meteor.callAsync("billingExpandedReportAggregates", {startDate: instance.reportStartDate.get(), endDate: instance.reportEndDate.get()}).then((response) => {
		instance.billingReportAggregates.set(response);
	}).catch((error) => {
		alert(error);
	});
};
