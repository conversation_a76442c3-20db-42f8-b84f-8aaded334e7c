import { AvailableCustomizations } from "../../../lib/customizations";
import { USER_TYPES } from "../../../lib/constants/profileConstants";
import { processPermissions } from "../../../lib/permissions";
import { Orgs, Org } from "../../../lib/collections/orgs";
import './reportsHeader.html';

Template.reportsHeader.helpers({
    "canSeeCustomReport"() {
        return Orgs.current() &&
            processPermissions({
                assertions: [{ context: "reports/standard", action: "read" }],
                evaluator: (person) => person.type === USER_TYPES.ADMIN || person.type === USER_TYPES.STAFF
            });
    }
});