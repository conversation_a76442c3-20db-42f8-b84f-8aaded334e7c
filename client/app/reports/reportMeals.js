import Papa from 'papaparse';
import './reportMeals.html';

Template.reportMeals.created = function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportEndDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.mealsReportAggregates = new ReactiveVar();
};

Template.reportMeals.rendered = function() {
	$('#attendanceStartDate').datepicker({autoclose:true});
	$('#attendanceEndDate').datepicker({autoclose:true});

	var self = Template.instance();

	Tracker.autorun(function() {
		var mealsReportAggregates = self.mealsReportAggregates.get();
	});
}

Template.reportMeals.events({
	"click #btnUpdate": async function() {
		
		var instance = Template.instance();
		instance.reportStartDate.set($("#attendanceStartDate").val());
		instance.reportEndDate.set($("#attendanceEndDate").val());

		fetchMealsReportAggregates(Template.instance());
	
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "meals"
		});
	},
	"click #btnExportCsv": async function() {
		var csvContent = Papa.unparse(_.map(getMealsData(Template.instance()), function(o) {
			return {
				"Date": o.date,
				"Breakfast": o.breakfast,
				"Lunch": o.lunch,
				"SnackAm": o.snackAm,
				"SnackPm": o.snackPm,
			};
		}));
    	
    	DownloadFile(csvContent, "meals.csv");

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "meals"
		});
	}
});

Template.reportMeals.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formattedEndDate": function(){
		return Template.instance().reportEndDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.createdAt).format("hh:mm a");
	},
	"meals": function() {
		return getMealsData(Template.instance());
	}
});

var getMealsData = function (instance) {
	return _.map(instance.mealsReportAggregates.get(), 
			function(m, key) { 
				return {
					date: key,
					breakfast: m.Breakfast,
					lunch: m.Lunch,
					snackAm: m["AM Snack"],
					snackPm: m["PM Snack"]
				};
			}
		);
};

var fetchMealsReportAggregates = function (instance) {
	Meteor.callAsync("mealsReportAggregates", {startDate: instance.reportStartDate.get(), endDate: instance.reportEndDate.get()}).then((response) => {
		var outputMeals = {};
			_.each(response, function(m) {
				var dateVal = m._id.month + "/" + m._id.day + "/" + m._id.year;
				if (!_.has(outputMeals, dateVal)) 
					outputMeals[dateVal] = {};
				outputMeals[dateVal][m._id.foodType] = m.total;
			});
			
			instance.mealsReportAggregates.set(outputMeals);
	}).catch((error) => {
		alert(error);
	});
};
