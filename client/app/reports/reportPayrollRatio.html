<template name="reportPayrollRatio">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Payroll Ratio Report
				<span class="text-muted pt-2 font-size-sm d-block">View payroll ratio for center with staff and groups.</span></h3>
			</div>
			<div class="card-toolbar">
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
							<div class="row">
								<div class="col-sm-3">
									<div class="form-group">
										<label>Start Date</label>
										<div class="input-group">
											
											<input type="text" class="form-control pull-right" id="reportStartDate">
										</div>
									</div>
								</div>
								<div class="col-sm-3">
									<div class="form-group">
										<label>End Date</label>
										<div class="input-group">
											
											<input type="text" class="form-control pull-right" id="reportEndDate" disabled>
										</div>
									</div>
								</div>
                <div class="col-md-2 checkbox-list">
                  <label class="checkbox">
                    <input type="checkbox" value="showGroup" id='showGroup'><span></span>Filter By Group
                  </label>
                </div>
                {{#if groupFilterActive}}
                <div class="col-md-2 checkbox-list">
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" value="addStaffToGroup" id='addStaffToGroup'><span></span>Include Staff Details
                  </label>
                </div>
                {{/if}}
							</div>
						
							<br/>
						
							{{#if reportRows}}
							<h4 class="text-center">Payroll Ratio Report</h4>
							<h5 class="text-center">{{dateRangeLabel}}</h5>
							<br/>
							<table class="table" id="subsidy-report-table" style="display:block;overflow-x:auto;padding-bottom:10px;">
								<tr>
									<th class='no-wrap'>Name</th>
									<th class='no-wrap'>Weekly Payroll</th>
									<th class='no-wrap'>Weekly Loaded Payroll</th>
                  <th class='no-wrap'>Payroll Ratio</th>
								</tr>
                {{#if groupFilterActive}}
                  {{#each reportRows.groups}}
                    <tr style="border-top: 10px solid white">
                      <td class='no-wrap' style="font-weight:bold;">{{name}}</td>
                      <td class='no-wrap' style="font-weight:bold;">{{formatCurrency weeklyPayroll}}</td>
                      <td class='no-wrap' style="font-weight:bold;">{{formatCurrency weeklyLoadedPayroll}}</td>
                      <td class='no-wrap' style="font-weight:bold;">{{payrollRatio}}%</td>
                    </tr>
                    {{#if groupStaffDetails}}
                      {{#each staffInGroup _id}}
                        <tr>
                          <td class='no-wrap'>{{firstName}} {{lastName}}</td>
                          <td class='no-wrap'>{{formatCurrency weeklyPayroll}}</td>
                          <td class='no-wrap'>{{formatCurrency weeklyLoadedPayroll}}</td>
                        </tr>
                      {{/each}}
                    {{/if}}
                  {{/each}}
                {{else}}
                  {{#each reportRows.calculatedStaffPayroll}}
                    <tr>
                      <td class='no-wrap'>{{firstName}} {{lastName}}</td>
                      <td class='no-wrap'>{{formatCurrency weeklyPayroll}}</td>
                      <td class='no-wrap'>{{formatCurrency weeklyLoadedPayroll}}</td>
                    </tr>
                  {{/each}}
                {{/if}}
								<tr style="border-top: 10px solid white">
									<td class='no-wrap' style="font-weight:bold;">CENTER TOTAL</td>
									<td class='no-wrap' style="font-weight:bold;">{{formatCurrency reportRows.centerTotal.weeklyPayroll}}</td>
									<td class='no-wrap' style="font-weight:bold;">{{formatCurrency reportRows.centerTotal.weeklyLoadedPayroll}}</td>
									<td class='no-wrap' style="font-weight:bold;">{{reportRows.centerTotal.payrollRatio}}%</td>
								</tr>
							</table>
							{{/if}}
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</template>
