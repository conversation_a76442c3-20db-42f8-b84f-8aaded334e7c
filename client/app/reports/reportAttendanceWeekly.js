const moment = require('moment-timezone');
const { getPeopleData } = require('../../services/peopleMeteorService');
import { Person } from '../../../lib/collections/people';
import { Groups } from '../../../lib/collections/groups';
import { Moments } from '../../../lib/collections/moments';
import { Orgs } from '../../../lib/collections/orgs';
import Papa from 'papaparse';
import './reportAttendanceWeekly.html';

const dayMap = { "mon": "M", "tue": "Tu", "wed": "W", "thu": "Th", "fri": "F"};
let peopleList = [];
Template.reportAttendanceWeekly.created = function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(new moment().startOf('week').format("MM/DD/YYYY"));
	this.group = new ReactiveVar("");
	this.personType = new ReactiveVar("");
	this.reportData = new ReactiveVar();
	const defaultSortType = (Meteor.user() && Meteor.user().uiOptions && Meteor.user().uiOptions.attendanceReportSort) || "attendeeFirstName";
	this.sortType = new ReactiveVar(defaultSortType);
	
	instance.autorun(function() {
		const beginPeriod = new moment(instance.reportStartDate.get(), "MM/DD/YYYY");
		const reportStartDate = beginPeriod.startOf('week').format("MM/DD/YYYY");
		const reportEndDate = beginPeriod.endOf('week').format("MM/DD/YYYY");
		instance.subscribe('theReservations');
		instance.subscribe('attendanceReportMoments', null, reportStartDate, reportEndDate, instance.group.get(), instance.personType.get(), function () {
			const groupedMoments = _.groupBy(Moments.find().fetch(), function (m) { return m.owner + "|" + moment(m.sortStamp).format("MM/DD/YYYY"); });

			if (Object.keys(groupedMoments).length !== 0) {

				const allPeopleIds = [];
				_.each(groupedMoments, function (m, k) { allPeopleIds.push(k.split("|")[0]) })
				getPeopleData({ "_id": { "$in": allPeopleIds } }, {}).then(taggedPeople => {

					const primaryCaregiverIds = taggedPeople.filter(person => person.primaryCaregiver !== undefined ).map((person) => person.primaryCaregiver);

					getPeopleData({ "_id": { "$in": primaryCaregiverIds } }, {}).then(primaryCaregiver => {
						peopleList = primaryCaregiver.concat(taggedPeople);
						instance.reportData.set(getAttendees(defaultSortType));
					}).catch(err => {
						console.log(err);
					});

				}).catch(err => {
					console.log(err);
				});
			}
		});
	});

}

Template.reportAttendanceWeekly.rendered = function() {
	$('#attendanceStartDate').datepicker({autoclose:true});
}

Template.reportAttendanceWeekly.events({
	"click #btnUpdate": async function() {
		var instance = Template.instance();
		instance.reportStartDate.set($("#attendanceStartDate").val());
		instance.group.set($("#filterGroup").val());
		instance.sortType.set($("#sortType").val());
		instance.personType.set($("#filterType").val());
		await Meteor.callAsync("setUiOption", "attendanceReportSort", $("#sortType").val());
		instance.reportData.set(getAttendees($("#sortType").val()));
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "attendanceWeekly"
		});
	},
	"click #btnExportCsv": async function(event, instance) {
		let mappedData = _.map(getAttendees(Template.instance().sortType.get()), function(o) {
			return {
				"Attendee": o.attendeeName,
				"Birthday": o.birthday,
				"Schedule": o.schedule,
				"Sunday":  formatTimeSpans("sun", o.checkinDays) ,
				"Monday":  formatTimeSpans("mon", o.checkinDays) ,
				"Tuesday":  formatTimeSpans("tue", o.checkinDays) ,
				"Wednesday":  formatTimeSpans("wed", o.checkinDays),
				"Thursday":  formatTimeSpans("thu", o.checkinDays) ,
				"Friday":  formatTimeSpans("fri", o.checkinDays) ,
				"Saturday":  formatTimeSpans("sat", o.checkinDays),
				"Hours": o.elapsedTime
			};
		});

		var csvContent = Papa.unparse(mappedData);

		const beginPeriod = new moment(instance.reportStartDate.get(), "MM/DD/YYYY");
		const reportStartDate = beginPeriod.startOf('week').format("MM/DD/YYYY");
		const reportEndDate = beginPeriod.endOf('week').format("MM/DD/YYYY");

		csvContent = "Weekly Attendance Report " + reportStartDate + " - " + reportEndDate + "\n" + csvContent
    	DownloadFile(csvContent, "attendance.csv");
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "attendanceWeekly"
		});
	}
});

function getAttendees(sortType) {
	var groupedMoments = _.groupBy(Moments.find().fetch(), function(m) { return m.owner + "|" + moment(m.sortStamp).format("MM/DD/YYYY"); });
	var output = [];
	var daysByOwner = {};

	_.each(groupedMoments, function(m, k) {
		var pid = k.split("|")[0];
		var checkinDate = k.split("|")[1];
		var checkinDay = new moment(checkinDate, "MM/DD/YYYY").format('ddd').toLowerCase();
		var checkInTimes = _.chain(m)
				.filter(function(i) { return i.momentType=="checkin";})
				.sortBy(function(i) { return i.sortStamp; })
				.value();
		
		_.each(checkInTimes, function(checkInTime) {
			var checkOutTime = _.chain(m)
				.filter(function(i) { return i.momentType=="checkout" && i.sortStamp > checkInTime.sortStamp;})
				.sortBy(function(i) { return i.sortStamp; })
				.first()
				.value();
			let elapsedTime = 0;
			if( checkInTime && checkOutTime) {
				const startTime = moment(checkInTime.sortStamp), endTime = moment(checkOutTime.sortStamp)
					duration = moment.duration(endTime.diff(startTime));
				elapsedTime = duration.asHours();
			}
			if (!daysByOwner[pid]) daysByOwner[pid] = {};
			if (!daysByOwner[pid][checkinDay]) daysByOwner[pid][checkinDay] = [];
			daysByOwner[pid][checkinDay].push({start: checkInTime, end: checkOutTime, elapsedTime});
		});		
	});

	const orgs = Orgs.current();
	const prefix = orgs.profileDataPrefix();

	_.each(daysByOwner, (checkInDays, pid) => {
		// var person = new Person(await Meteor.callAsync("getPeopleById", {_id:pid}));
		var person = new Person(peopleList.find((p)=> p._id === pid));
		let scheduleText = "";
		const recurringSchedule = person && person.getRecurringSchedule();

		if (recurringSchedule) {
			const recurringDays = (recurringSchedule && recurringSchedule.recurringDays) || [];
			if (recurringDays.includes("mon")) scheduleText += "M";
			if (recurringDays.includes("tue")) scheduleText += "T";
			if (recurringDays.includes("wed")) scheduleText += "W";
			if (recurringDays.includes("thu")) scheduleText += "R";
			if (recurringDays.includes("fri")) scheduleText += "F";
			if (scheduleText == "MTWRF") scheduleText = "M-F";
			if (recurringSchedule.scheduledTime) scheduleText += " " + recurringSchedule.scheduledTime;
			if (recurringSchedule.scheduledEndTime) scheduleText += "-" + recurringSchedule.scheduledEndTime;
		}
		let personBirthday = null;
		if(person){
			 personBirthday = prefix ? (person[prefix] && person[prefix].birthday) : person.birthday;
		}

		let newRec = {
			id: pid,
			attendeeName: person ? person.firstName + " " + person.lastName : "",
			attendeeInactive: person && person.inActive,
			birthday: personBirthday && new moment(personBirthday).format("M/DD/YYYY"),
			schedule: scheduleText,
			attendeeFirstName: person && person.firstName,
			attendeeLastName: person && person.lastName,
			checkinDays: checkInDays,
			elapsedTime: (_.reduce( checkInDays, (memo, items) => { 
					return memo + _.reduce(items, (daymemo, item) => daymemo + item.elapsedTime, 0);
				}, 0) || 0).toFixed(2)
		};
		if (Orgs.current().hasCustomization("people/types/showPrimaryCaregiver")) {
			const caregiver = peopleList.find((p)=> p._id === person.primaryCaregiver );
			if (caregiver) newRec.primaryCaregiver = caregiver.firstName + " " + caregiver.lastName;
		}
		
		output.push( newRec );
	});
	return _.sortBy(output, sortType);
}
function formatTimeSpans(day, checkinDays) {
	let out = "";

	if (checkinDays[day]) {
		let current = "";
		(checkinDays[day] || []).forEach( (span) => {
			current = current + (span.start ? moment(span.start.sortStamp).format("h:mm a") : "") + " - "
					+ (span.end ? moment(span.end.sortStamp).format("h:mm a") : "");
			out = out + (out != "" ? "\n" : "") + current;
		} );
	}
	return out;
}
Template.reportAttendanceWeekly.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.sortStamp).format("hh:mm a");
	},
	"findAndFormatTime": function (checkinDays, type) {
		let outMoment;
		if (type =="checkin" && checkinDays.start) outMoment = checkinDays.start;
		if (type =="checkout" && checkinDays.end) outMoment = checkinDays.end;
		return outMoment ? 		moment.tz(outMoment.sortStamp, Orgs.current().getTimezone()).format("h:mm a") : "";
	},
	"groups": function() {
		return Groups.find({}, {sort: {name: 1}});
	},
	"attendees": function() {
		return Template.instance().reportData.get();
	},
	"currentSortType": function() {
		return (Meteor.user() && Meteor.user().uiOptions && Meteor.user().uiOptions.attendanceReportSort) || "attendeeFirstName";
	},
	"includedDays": function() {
		return _.map(dayMap, (v, k) => { return k;});
	},
	"showPrimaryCaregiver": function() {
		return Orgs.current().hasCustomization("people/types/showPrimaryCaregiver");
	},
	"findDayValues": function(checkinDays, currentDay) {
		return checkinDays[currentDay];
	}
});
