<template name="reportCaliforniaEnrollmentAttendanceRegister">

	<div class="row">
		<div class="col-md-12">
			<div class="box">
				<div class="box-header">
				  <h3 class="box-title">California Enrollment and Attendance Register</h3>
				</div>
				<div class="box-body">
					  <div class="row">
						<div class="col-lg-4">
							<div class="form-group">
								<label>Date Range</label>
								<div class="input-group input-daterange">
									<input type="text" class="form-control" id="attendanceStartDate" value="">
									<div class="input-group-addon" >
										to
									</div>
									<input type="text" class="form-control" id="attendanceEndDate" value="">
								</div>
							</div>
						</div>
						
					  </div>
				
					 <div class="row">
						<div class="col-lg-4">
							<div class="form-group">
								<br/>
								<button type="button" class="btn btn-primary" id="update">Update</button>
								<button type="button" class="btn" id="btnExportCsv"><i class="fa fa-download"></i> Export CSV</button>
							</div>
						</div>					
					  </div>
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table class="table" id="californiaAttendanceRegisterTable" style="overflow-x:auto;display:block">
					<tbody>
					<tr>  
						<th>Name</th>
						<th>Group</th>
						<th>Daily Fee</th>
						{{#each daysInMonth}}
						<th class="centered">{{dayLabel}} {{dayOfMonth}}</th>
							{{#if trueIfEq dayOfWeek 5}}
							<th>Payments</th>
							{{/if}}
						{{/each}}
						{{#if showEndPaymentColumn}}
							<th>Payments</th>
						{{/if}}
						<th>Days of Enrollment</th>
						<th>Days of Attendance</th>
						<th>Total Fees Due for Month</th>
						<th>Total Fees Received This Month</th>
						<th>Remarks</th>
					</tr>
					{{# each section in sections}}
					<tr>
					<td colspan="100%" style="font-weight:bold">
						{{section.sectionName}}
					</td>
					</tr>
						{{# each attendee in section.attendees}}
						<tr>
							<td>{{attendee.attendeeName}} {{#if attendee.attendeeInactive}}<span style="color:#ff0000">(i)</span>{{/if}}</td>
							<td>{{attendee.category}} - {{attendee.subcategory}}</td>
							<td><!-- daily fee --></td>
							{{#each day in attendee.personDays}}
								<td class="centered">{{day.dayCode}}</td>
								{{#if trueIfEq day.dayOfWeek 5}}
									<td>
									{{#each (findPayments attendee.payments day.dateEndStamp)}}
										{{formatCurrency amount}} - {{receiptNumber}} - {{formatDate createdAt "M/DD/YY"}}
									{{/each}}
									</td>
								{{/if}}
							{{/each}}
							{{#if showEndPaymentColumn}}
								<td>
									{{#each (findPayments attendee.payments null)}}
									{{formatCurrency amount}} - {{receiptNumber}} - {{formatDate createdAt "M/DD/YY"}}
									{{/each}}
								</td>
							{{/if}}
							<td>{{attendee.daysEnrolled}}</td>
							<td>{{attendee.daysAttended}}</td>
							<td>{{formatCurrency attendee.feesDueTotal}}</td>
							<td>{{formatCurrency attendee.feesReceivedTotal}}</td>
							<td>{{remarks}}</td>
						</tr>
						{{/each}}
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
