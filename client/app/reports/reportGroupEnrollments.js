import './reportGroupEnrollments.html';

Template.reportGroupEnrollments.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.dateRangeLabel = new ReactiveVar();
	this.showNames = new ReactiveVar(false)
}

Template.reportGroupEnrollments.rendered = function() {
	$('#reportStartDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
	$('#reportEndDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
}

Template.reportGroupEnrollments.events({
	"click #btnUpdate": async function(e, i) {
		const showNames = $("#chkShowNames").prop("checked"),
			selectionMethod = $("input[name='group-type']:checked").val(),
			historicalDate = selectionMethod && $("input[name='historical-date']").val();

		if (selectionMethod == "historical-schedule" && !historicalDate)
			return mpSwal.fire("Error", "Please select a valid date", "error");

		i.showNames.set(showNames);

		Meteor.callAsync("groupEnrollmentsReport", { selectionMethod, historicalDate }).then((response) => {
			i.reportData.set(response);		
			if (selectionMethod == "historical-schedule")
				i.dateRangeLabel.set(`As of ${historicalDate}`);
			else
				i.dateRangeLabel.set(`As of ${new moment().format("YYYY-MM-DD")}`);
		}).catch((err) => {
			i.reportData.set(undefined);	
			if (selectionMethod == "historical-schedule")
				i.dateRangeLabel.set(`As of ${historicalDate}`);
			else
				i.dateRangeLabel.set(`As of ${new moment().format("YYYY-MM-DD")}`);
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "groupEnrollments"
		});
	},
	"click #btnPrint": async function (e,i) {
		var outputFile = 'export.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "groupEnrollments"
		});
	}
});

Template.reportGroupEnrollments.helpers({
	reportRows() {
		const data = Template.instance().reportData.get();
		return data ;
	},
	dateLabel() {
		return Template.instance().dateRangeLabel.get();
	},
	totals() {
		const data = Template.instance().reportData.get();
		return data && _.reduce(data, (memo, item) => memo + item.count, 0);
	},
	showNames() {
		return Template.instance().showNames.get();
	}
})