import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { getPeopleData } from '../../services/peopleMeteorService';
import './reportSignInGrid.html';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';

Template.reportSignInGrid.onCreated( function() {
	const instance = this;
	const passedGroupId = FlowRouter.getQueryParam('groupId');
	const showScheduleAndDateOfBirthParam = FlowRouter.getQueryParam('showScheduleAndDateOfBirth');
	const includeNextWeekInQueryParam = FlowRouter.getQueryParam('includeNextWeekInQuery');
	const isPrintableView = FlowRouter.getQueryParam('printable');

	instance.selectedGroup = new ReactiveVar(passedGroupId);
	instance.selectionMade = new ReactiveVar(!!isPrintableView);
	instance.showScheduleAndDateOfBirth = new ReactiveVar(!!showScheduleAndDateOfBirthParam);
	instance.includeNextWeekInQuery = new ReactiveVar(!!includeNextWeekInQueryParam);
	instance.reservations = new ReactiveVar([]);
	instance.schedulesTypes = new ReactiveVar(Orgs.current().getScheduleTypes());
	instance.reportRowsQuery = new ReactiveVar();
	instance.reportRowsQueryCheck = new ReactiveVar('');
	instance.reportRows = new ReactiveVar();
	instance.autorun(() => {
		const selectedGroup = instance.selectedGroup.get();
		const includeNextWeekInQueryBool = printPageParameters('includeNextWeekInQuery', instance.includeNextWeekInQuery.get())

		let query = { type: "person", inActive: { "$ne": true }, designations: { $nin: ["Wait List"] }};
		if (selectedGroup) {
			query["defaultGroupId"] = selectedGroup;
		}

		getPeopleData(query, {sort: { lastName: 1, firstName: 1 }, fields: {_id: 1}}).then(people => {
			const peopleIds = people.map(person => person._id);

			Meteor.callAsync("getActiveRecurringReservations", { peopleIds, includeNextWeekInQueryBool })
			.then((result) => {
				instance.reservations.set(result);
			})
			.catch((error) => {
				console.error("Meteor method call error:", error);
			});
		}).catch(err => {
			console.log(err)
		});
	});

	instance.autorun(() => {
		if (instance.reportRowsQuery.get()) {
			if (instance.reportRowsQueryCheck.get() !== JSON.stringify(instance.reportRowsQuery.get())) {
				instance.reportRowsQueryCheck.set(JSON.stringify(instance.reportRowsQuery.get()));
				getPeopleData(instance.reportRowsQuery.get(), {sort:{lastName:1, firstName:1}}).then(res => {
					if (res?.length) {
						instance.reportRows.set(res);
					}
				}).catch(err => {
					console.log(err);
				});
			}
		}
	});
});

Template.reportSignInGrid.onCreated( function() {
	Tracker.afterFlush( function() {
		//if (Router.current().params.query.printable) window.print();
	})
});
Template.reportSignInGrid.events( {
	"click #btnPrint": async function(e, instance) {
		const singlePageMode = $("input[name='page-per-family']").prop("checked")
		const url = `/reports/reportSignInGrid?printable=true&groupId=${instance.selectedGroup.get() || ""}&showScheduleAndDateOfBirth=${instance.showScheduleAndDateOfBirth.get()}&includeNextWeekInQuery=${instance.includeNextWeekInQuery.get()}`;
		window.open( url, '_blank');

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "signInGrid"
		});
	},
	"click #btnUpdate": async function() {
		const instance = Template.instance();
		instance.selectedGroup.set($("#selectedGroup").val());
		instance.selectionMade.set(true);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "signInGrid"
		});
	},
	"change input[name=showScheduleAndDateOfBirth]": function (e) {
		const newValue = $(e.currentTarget).prop("checked");
		Template.instance().showScheduleAndDateOfBirth.set(newValue)
	},
	"change input[name=includeNextWeekInQuery]": function (e) {
		const newValue = $(e.currentTarget).prop("checked");
		Template.instance().includeNextWeekInQuery.set(newValue)
	},
});

Template.reportSignInGrid.helpers( {
	"formatDays"(days) {
		const dayMap = {
			"mon": "M",
			"tue": "T",
			"wed": "W",
			"thu": "R",
			"fri": "F",
			"sat": "Sa",
			"sun": "Su",
		};

		return days.map(day => dayMap[day]).join('');
	},
	"getLiClass"(index) {
		// Applies spacing only *between* the li elements
		if (index === 0) {
			return "mt-0";
		} else {
			return "mt-2";
		}
	},
	"reportRows"() {
		if (!Template.instance().selectionMade.get()) return;

		const selectedGroup = Template.instance().selectedGroup.get();

		let query = {type:"person", inActive:{"$ne":true}, designations: { $nin: ["Wait List"] }};
		if (selectedGroup)
			query["defaultGroupId"] = selectedGroup;

		Template.instance().reportRowsQuery.set(query);
		const people = Template.instance().reportRows.get();

		return {
			people
		};
	},
	"reportTitle"() {
		const selectedGroupId = Template.instance().selectedGroup.get();
		if (selectedGroupId) {
			const group = Groups.findOne(selectedGroupId);
			if (group) return group.name + " Attendance";
		} else {
			return "All Contacts";
		}
	},
	"days"() {
		return ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
	},
	"groups"() {
		return Groups.find({}, {sort:{name:1}});
	},
	"fullLayout"() {
		return FlowRouter.getQueryParam('printable') !== "true";
	},
	"showScheduleAndDateOfBirth"() {
		const templateInstance = Template.instance();
		return printPageParameters('showScheduleAndDateOfBirth', templateInstance.showScheduleAndDateOfBirth.get())
	},
	"userReservations"(_id) {
		const reservations = Template.instance().reservations.get();
		const scheduleTypes = Template.instance().schedulesTypes.get();

		if (reservations !== null) {
			const filteredReservations = reservations.filter(reservation => reservation.selectedPerson === _id);

			// Map schedule type names to each reservation
			return filteredReservations.map(reservation => {
				// Find the corresponding schedule type
				const scheduleType = scheduleTypes.find(type => type._id === reservation.scheduleType);

				// Add the schedule type name to the reservation object
				return {
					...reservation,
					scheduleTypeName: scheduleType ? scheduleType.type : "Unknown",
				};
			});
		}

		return [];
	},
    "getBirthday"(person) {
        return (person?.profileData && person?.profileData.birthday) || person.birthday
    },
});

function printPageParameters(query, param){
	if (FlowRouter.getQueryParam('printable')) {
		const queryParam = FlowRouter.getQueryParam(query);
		if (queryParam !== null) {
			return queryParam === "true";
		}
	}

	return param
}