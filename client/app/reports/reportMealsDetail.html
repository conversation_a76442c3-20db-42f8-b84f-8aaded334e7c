<template name="reportMealsDetail">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Meals Detail Report
				<span class="text-muted pt-2 font-size-sm d-block">View detailed meal consumption data.</span></h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">

				<div class="box-body">
				  <div class="row">
					<div class="col-lg-3">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								
								<input data-cy="start-date-input" type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						  <div class="form-group">
							<label>End Date</label>
							<div class="input-group">
								
								<input data-cy="end-date-input" type="text" class="form-control pull-right" id="attendanceEndDate" value="{{formattedEndDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-2">
						<div class="form-group">
							<label>Person</label>
							<div class="input-group">
								<select data-cy="select-person" class="form-control" id="filterPerson">
									<option value="">All</option>
									{{#each people}}
									<option value="{{_id}}">{{lastName}}, {{firstName}}</option>
									{{/each}}
								</select>
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						<div class="form-group">
							<label>Group:</label>
							<select data-cy="select-group" class="form-control" id="filterGroup">
								<option value="">All</option>
								{{#each groups}}
								<option value="{{_id}}">{{name}}</option>
								{{/each}}
							</select>
							</div>
					</div>
				  </div>
                    <div class="col-lg-auto d-flex align-items-center pl-0">
                        <div class="form-group">
                            <label>Include Detail</label>
                            <input data-cy="include-detail" type="checkbox"  class="mx-3" id="detailCheckbox">
                        </div>
                    </div>
				  
				</div><!-- /.box-header -->
				<div class="box-body no-padding" id="dvData">
					{{#if data.days}}
				  <table  class="table" style="overflow-x:auto;display:block">
					<tbody><tr>
					  <th>Name/Subsidy</th>
                        {{#if showDetails}}
                            <th class="text-center">Subsidy Reason</th>
                            <th class="text-center">Race</th>
                            <th class="text-center">Ethnicity</th>
                        {{/if}}
						<th class="text-center">Meal</th>
						{{#each day in data.days}}
						<th class="text-center">{{formatDate day "MM/DD"}}</th>
						{{/each}}
						<th class="text-center">Free</th>
						<th class="text-center">Reduced</th>
						<th class="text-center">Paid</th>
					</tr>
					
					{{# each person in data.people}}
					<tr style="background-color:#eee">
					  <td data-cy="person-name">
                          {{person.name}}/{{person.subsidyType}}
                          {{#if person.inActive}}<span style="color:#ff0000">(i)</span>{{/if}}
                      </td>
                        {{#if showDetails}}
                            <td data-cy="subsidy-reason">{{person.subsidyReason}}</td>
                            <td data-cy="person-race">{{person.race}}</td>
                            <td data-cy="person-ethnicity">{{person.ethnicity}}</td>
                            <td data-cy="checked-in-out">
                                Checked In:
                                <br>
                                Checked Out:
                            </td>
                                {{#each day in data.days}}
                                    <td data-cy="attendance-day" class="text-center">{{{getAttendance day person}}}</td>
                                {{/each}}
                        {{/if}}
						<td colspan={{headerSpan data.days}}></td>
					</tr>
						{{#each meal in data.meals}}
						<tr>
							<td></td>
                            {{#if showDetails}}
                                <td></td>
                                <td></td>
                                <td></td>
                            {{/if}}
							<td data-cy="meal-name" >{{meal}}</td>
							{{#each day in data.days}}
                                {{#if isOutOfBounds meal day person}}
                                    <td data-cy="meal-person-time" class="text-center" style="background-color: #FFFF00">{{#if
                                            (hasMeal day meal person.lines)}}
                                        X{{#if showDetails}}<br>{{getMealTime meal day person}}{{/if}}{{/if}}</td>
                                {{else}}
                                    <td data-cy="meal-person-line" class="text-center">{{#if (hasMeal day meal person.lines)}}
                                        X{{#if showDetails}}<br>{{getMealTime meal day person}}{{/if}}{{/if}}</td>
                                {{/if}}
                            {{/each}}
							<td data-cy="meal-free">{{sumOf person meal 'free'}}</td>
							<td data-cy="meal-reduced" >{{sumOf person meal 'reduced'}}</td>
							<td data-cy="meal-paid">{{sumOf person meal 'paid'}}</td>
						</tr>
						{{/each}}
					
					{{/each}}
					<tr style="background-color:#eee">
						<td>Totals</td>
                        {{#if showDetails}}
                            <td></td>
                            <td></td>
                            <td></td>
                        {{/if}}
						<td colspan={{totalSpan data.days}}></td>
						<td>Free</td>
						<td>Reduced</td>
						<td>Paid</td>
					</tr>
					{{#each meal in data.meals}}
					<tr>
						<td></td>
                        {{#if showDetails}}
                            <td></td>
                            <td></td>
                            <td></td>
                        {{/if}}
						<td colspan={{totalSpan data.days}}>{{meal}}</td>
						<td data-cy="total-meal-free">{{grandTotal data.overallTotals meal 'Free'}}</td>
						<td data-cy="total-meal-reduced">{{grandTotal data.overallTotals meal 'Reduced'}}</td>
						<td data-cy="total-meal-paid">{{grandTotal data.overallTotals meal 'Paid'}}</td>
					</tr>
					{{/each}}
					</tbody></table>
					{{/if}}
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
