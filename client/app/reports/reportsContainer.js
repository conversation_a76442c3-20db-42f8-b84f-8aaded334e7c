import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './reportsContainer.html';
import { Orgs } from '../../../lib/collections/orgs';
import { processPermissions } from '../../../lib/permissions';
import {BASE_REPORTS, CONDITIONAL_REPORTS} from "../../../lib/constants/reportConstants";

Template.reportsContainer.onCreated(function() {
  const instance = this;
  instance.enabledReports = new ReactiveVar([]);
  instance.searchText = new ReactiveVar('');
  instance.isLoading = new ReactiveVar(true);

  instance.autorun(async () => {
    try {
      const enabledReports = await getAvailableReports();
      instance.enabledReports.set(enabledReports);
    } finally {
      instance.isLoading.set(false);
    }
  });
});

Template.reportsContainer.helpers({
  isLoading() {
    return Template.instance().isLoading.get();
  },

  availableReports() {
    const instance = Template.instance();
    const enabledReports = instance.enabledReports.get();
    const searchText = instance.searchText.get().toLowerCase();

    return enabledReports
    .filter(report => report.name.toLowerCase().includes(searchText))
    .sort((a, b) => a.name.localeCompare(b.name));
  },

  displayReportName() {
    const reportName = FlowRouter.getParam("reportName") || "";
    const enabledReports = Template.instance().enabledReports.get();
    const matchedReport = enabledReports && _.find(enabledReports, er => er._id === reportName);
    return matchedReport && matchedReport._id;
  },

  searchIsEnabled() {
    return !FlowRouter.getParam('reportName');
  },

  checkTitle(title) {
    return title === 'Name to Face Roster';
  }
});

Template.reportsContainer.events({
  'input #searchReports': _.debounce((event, instance) => {
    instance.searchText.set(event.target.value);
  }, 300)
});


/**
 *	Function to check if ADP is enabled for the current organization
 */
const checkAdpEnabled = _.memoize(() => {
  return Meteor.callAsync('hasAdpEnabled')
    .catch(error => {
      console.error('Error checking ADP status:', error);
      return false;
    });
});


/**
 * Function to clear report cache
 */
const clearReportCache = () => {
  getAvailableReports.cache.clear();
  checkAdpEnabled.cache.clear();
  const instance = Template.instance();
  if (instance && instance.enabledReports) {
    instance.enabledReports.set([]);
  }
  console.log('Report cache cleared');
};

/**
 * Function to get available reports based on customizations and permissions
 */
const getAvailableReports = _.memoize(async function() {
  let enabledReports = [...BASE_REPORTS];

  Object.entries(CONDITIONAL_REPORTS.customizations).forEach(([customization, reports]) => {
    reports.forEach(report => {
      const hasCustomization = Orgs.current().hasCustomization(customization);
      if (report.inverse ? !hasCustomization : hasCustomization) {
        if (report.requiresPermission) {
          if (processPermissions({
            assertions: [{ context: report.requiresPermission, action: 'read' }],
            evaluator: (person) => person.type === 'admin'
          })) {
            enabledReports.push(report);
          }
        } else {
          enabledReports.push(report);
        }
      }
    });
  });

  CONDITIONAL_REPORTS.permissions.forEach(report => {
    if (report.permission && processPermissions({
      assertions: report.permission.context ? [{ context: report.permission.context, action: report.permission.action }] : [],
      evaluator: report.permission.evaluator
    })) {
      enabledReports.push(report);
    }
  });

  if (Orgs.current().busRoutes) {
    enabledReports.push(...CONDITIONAL_REPORTS.special.busRoutes);
  }

  try {
    const showAdpReport = await checkAdpEnabled();
    if (showAdpReport) {
      enabledReports.push(...CONDITIONAL_REPORTS.special.adp);
    }
  } catch (error) {
    console.error('Error checking ADP status:', error);
  }

  return enabledReports;
});

export { getAvailableReports, clearReportCache };