<template name="reportAttendanceWeekly">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Attendance Report (Weekly View)
				<span class="text-muted pt-2 font-size-sm d-block">Weekly breakdown of attendance data.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				
				<!--begin::Button-->
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv" data-cy="export-btn">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
					Update
				</div>
				<!--end::Button-->
				
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-header">
				  <h3 class="box-title">Weekly Attendance Report</h3>
				</div>
				<div class="box-body">
				  <div class="row">
					<div class="col-lg-2">
						  <div class="form-group">
							<label>Report Week</label>
							<div class="input-group">
								
								<input type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}" data-cy="start-week-of-input">
							</div>
						</div>
					</div>

					<div class="col-lg-3">
						<div class="form-group">
							<label>Group:</label>
							<select class="form-control" id="filterGroup" data-cy="select-group">
								<option value="">All</option>
								  {{#each groups}}
								  <option value="{{_id}}">{{name}}</option>
								  {{/each}}
							  </select>
						 </div>
					</div>

					<div class="col-lg-3">
						<div class="form-group">
							<label>Type</label>
							<div class="input-group">
								<select class="form-control" id="filterType" data-cy="filter-type">
								<option value="">All</option>
									<option value="person">Person</option>
									<option value="family">Family</option>
									<option value="staff">Staff</option>
									<option value="admin">Admin</option>
								</select>
							</div>
						</div>
					</div>
					
				  </div>
				  <div class="row">
						<div class="col-lg-3">
							<label>Sort:</label>
							<select class="form-control" id="sortType" data-cy="select-sort">
								<option value="attendeeFirstName" {{selectedIfEqual currentSortType "attendeeFirstName"}}>First Name</option>
								<option value="attendeeLastName" {{selectedIfEqual currentSortType "attendeeLastName"}}>Last Name</option>
								
							</select>
						</div>
					</div>
					<br/>
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table class="table" data-cy="attendance-report-table">
					<tbody>
					<tr>  
						<th>Name</th>
						{{#if showPrimaryCaregiver}}
						<th>Primary Caregiver</th>
						{{/if}}
					  <th>Birthday</th>
					  <th>Schedule</th>
					  {{#each includedDays}}
						<th style="text-align:center">{{capitalizeFirstLetter this}}</th>
						{{/each}}
						<th>Hours</th>
					</tr>
					
					{{# each attendee in attendees}}
					<tr>
						<td>{{attendee.attendeeName}} {{#if attendee.attendeeInactive}}<span style="color:#ff0000">(i)</span>{{/if}}</td>
						{{#if showPrimaryCaregiver}}
						<td>{{attendee.primaryCaregiver}}</td>
						{{/if}}
					  <td>{{attendee.birthday}}</td>
					  <td>{{attendee.schedule}}</td>
					  {{#each includedDay in includedDays}}
					  <td style="text-align:center">
						{{#each checkinDay in (findDayValues attendee.checkinDays includedDay)}}
						  {{findAndFormatTime checkinDay "checkin"}} - {{findAndFormatTime checkinDay "checkout"}}<br/>
						{{/each}}
					  </td>
						{{/each}}
						<td>{{attendee.elapsedTime}}</td>
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
