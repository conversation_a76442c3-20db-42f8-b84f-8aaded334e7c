import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from 'moment-timezone';
import './reportClassList.html';
import './reportQueueTemplate';
import { Groups } from '../../../lib/collections/groups';
import { reportQueuedSwal, upperFirst } from './queueReportsUtil';

Template.reportClassList.created = function() {
	var instance = this;
	let initialMoment = new moment();
	initialMoment = initialMoment.day() <= 1 ? initialMoment.day(1) : initialMoment.day(8)
	this.reportStartMonth = new ReactiveVar(initialMoment.format("MM/DD/YYYY"));
	this.classListReportAggregates = new ReactiveVar({groups: [], totals: []});
	this.displayChildDetails = new ReactiveVar();
	this.selectedGroupId = new ReactiveVar();
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	this.isUpdating = new ReactiveVar(false);
	this.selectedOrgs = new ReactiveVar([]);
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId }).then((response) => {
			if (!response) {
				return;
			}
			this.savedMode.set(true);
			this.classListReportAggregates.set(response.data);
			const reportArgs = response.args;
			this.reportStartMonth.set(reportArgs.startMonth);
			this.selectedGroupId.set(reportArgs.selectedGroupId);
			this.displayChildDetails.set(reportArgs.includeChildDetails);
			const ageRangeStart = reportArgs.ageRangeStart;
			const ageRangeEnd = reportArgs.ageRangeEnd;
			this.selectedOrgs.set(reportArgs.orgs)
			let header = 'Start Month: ' + reportArgs.startMonth;
			let orgsLabel = response.data.map(r => r.name).join(', ') || 'Current Org';
			header += '<br>Orgs: ' + orgsLabel;
			let ageRangeLabel = 'All';
			if(ageRangeStart && ageRangeEnd) {
				ageRangeLabel = ageRangeStart + ' - ' + ageRangeEnd;
			}
			header += '<br>Age Range: ' + ageRangeLabel;
			let selectedGroupLabel = 'All';
			if(reportArgs.selectedGroupId) {
				selectedGroupLabel = Groups.findOne({_id: reportArgs.selectedGroupId}).name;
			}
			header += '<br>Selected Group: ' + selectedGroupLabel;
			header += '<br>Include Child Details: ' + upperFirst(reportArgs.includeChildDetails.toString());
			this.savedHeader.set(header);
		}).catch((err) => {
			return;
		});
	}
};

Template.reportClassList.rendered = function() {
	$('#reportStartMonth').datepicker({
		autoclose: true,
	});
	document.getElementsByTagName('body')[0].addEventListener('orgChanged', () => {
		//forgive me it was the only way I could find to get the event from the announcementOrgsFieldDropdown component
		const selectedOrgs = this.selectedOrgs.get().sort();
		const orgs = $("#announcementOrgs").val().sort();
		if(JSON.stringify(selectedOrgs) !== JSON.stringify(orgs)) {
			this.selectedOrgs.set(orgs);
			if (orgs.length > 1) {
				$("#includeChildDetails").prop("checked", false);
				$("#includeChildDetails").prop("disabled", true);
			} else {
				$("#includeChildDetails").prop("disabled", false);
			}
		}
	})

	var self = Template.instance();

}

Template.reportClassList.events({
	"click #btnUpdate": async function(event) {
		event.preventDefault();
		const instance = Template.instance();
		const passedValue = $("#reportStartMonth").val(),
			newMoment = new moment(passedValue, "MM/DD/YYYY");

		if (newMoment.day() != 1) {
			$("#btnUpdate").prop('disabled', false);
			return mpSwal.fire("Error", "Enrollment forecast reports can only be run for Mondays", "error");
		}

		instance.isUpdating.set(true);
		instance.reportStartMonth.set(passedValue);
		if (instance.queueMode.get()) {

			await fetchClassListReportAggregates(Template.instance(), true);

			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "classList"
			});
			reportQueuedSwal();
			return;
		}


		await fetchClassListReportAggregates(Template.instance());

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "classList"
		});
	},
	"click #btnExportCsv": async function() {
		var outputFile = 'export.csv'
			
		// CSV
		exportTableToCSV.apply(this, [$('#dvData > table'), outputFile]);
		
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "classList"
		});
	},
	"change #selectedGroupId": function() {
		const newValue = $("#selectedGroupId").val();
		if (newValue && !$("#announcementOrgs").val().length > 1)
			$("#includeChildDetails").prop("checked", true);
	},
	"change #ageRangeStart, change #ageRangeEnd": function() {
		const newStart = $("#ageRangeStart").val(),
			newEnd = $("#ageRangeEnd").val();
		if (newStart || newEnd && !$("#announcementOrgs").val().length > 1)
			$("#includeChildDetails").prop("checked", true);
	},
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
});

Template.reportClassList.helpers({
	"hasNumberValue": function(val) {
		return !isNaN(val);
	},
	"getVarianceTextClass": function(preferredCapacity, currentFte) {
		let cFte = parseFloat(currentFte);
		let pC = parseFloat(preferredCapacity);
		if (cFte < pC) return "color:red;"
		return "";
	},
	"formatCurrency": function(num) {
		// Create our number formatter.
		var formatter = new Intl.NumberFormat('en-US', {
		  style: 'currency',
		  currency: 'USD',
		});
		if (num) return formatter.format(num);
	},
	"formattedStartMonth": function(){
		return Template.instance().reportStartMonth.get();
	},
	"isMultiSite": function() {
		return Template.instance().classListReportAggregates.get().length > 1;
	},
	"groups": function(index = 0) {
		if(Template.instance().classListReportAggregates.get().length) {
			return Template.instance().classListReportAggregates.get()[index].groups;
		}
	},
	"totals": function(index = 0) {
		return Template.instance().classListReportAggregates.get()[index].totals;
	},
	"orgName": function(index = 0) {
		return Template.instance().classListReportAggregates.get()[index].name;
	},
	"orgs": function() {
		return Template.instance().classListReportAggregates.get();
	},
	"ageGroupLabel": function(ageGroup) {
		let label = "";
		if (ageGroup && ageGroup.begin) label += `${ageGroup.begin} - `;
		if (ageGroup && ageGroup.end) label += `${ageGroup.end} `;
		if (ageGroup && ageGroup.type) label += `${ageGroup.type}`;
		return label;
	},
	"ratioLabel": function(ratio) {
		let label = "";
		if (ratio) label += `1:${ratio}`;
		return label;
	},
	"displayChildDetails": function () {
		return Template.instance().displayChildDetails.get();
	},
	"availableGroups": function () {
		return Groups.find({}, {sort:{"name":1}});
	},
	"showHeader": function(i) {
		return i == 0 || Template.instance().displayChildDetails.get();
	},
	"showScheduleLabel": function() {
		return Template.instance().selectedGroupId.get();
	},
	showOrgSelection() {
		const user = Meteor.user();
		const userPerson = (user) ? user.fetchPerson() : {};
		return userPerson.isMasterAdmin();
	},
	updateLabel() {
		return Template.instance().isUpdating.get() ? 'Updating' : (Template.instance().queueMode.get() ? 'Queue' : 'Update');
	},
	isQueueMode() {
		return Template.instance().queueMode.get();
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	}
});

var fetchClassListReportAggregates = async function (instance, defer = false) {
	const includeChildDetails = $("#includeChildDetails").prop("checked"),
		selectedGroupId = $("#selectedGroupId").val(),
		ageRangeStart = parseFloat($("#ageRangeStart").val()),
		ageRangeEnd = parseFloat($("#ageRangeEnd").val()),
		orgs = $("#announcementOrgs").val() || [];
	instance.displayChildDetails.set(includeChildDetails);
	instance.selectedGroupId.set(selectedGroupId);
	if(defer) {
		instance.isUpdating.set(false);
		await Meteor.callAsync('queueReportDefer', {
			reportFunction: 'classListReportAggregates',
			reportName: 'Enrollment Forecast Report',
			reportArgs: {
				startMonth: instance.reportStartMonth.get(),
				includeChildDetails,
				selectedGroupId,
				ageRangeStart,
				ageRangeEnd,
				orgs
			},
			userId: Meteor.userId(),
			reportRoute: 'reports/reportClassList'
		});
		return;
	}
	Meteor.callAsync("classListReportAggregates", {
		startMonth: instance.reportStartMonth.get(),
		includeChildDetails,
		selectedGroupId,
		ageRangeStart,
		ageRangeEnd,
		orgs
	}).then((response) => {
		instance.isUpdating.set(false);
		instance.classListReportAggregates.set(response);
	}).catch((error) => {
		instance.isUpdating.set(false);
		alert(error);
	});
};
