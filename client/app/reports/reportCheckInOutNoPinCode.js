import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './reportCheckInOutNoPinCode.html';
import './reportOrgsField';
import './reportQueueTemplate';
import { reportQueuedSwal, upperFirst } from './queueReportsUtil';

Template.reportCheckInOutNoPinCode.created = function() {
    const instance = this;
    this.reportData = new ReactiveVar();
    this.queueMode = new ReactiveVar(false);
    this.savedMode = new ReactiveVar(false);
    this.savedHeader = new ReactiveVar();
    const queueId = FlowRouter.getQueryParam('queueId');
    if (queueId) {
        Meteor.callAsync('retrieveQueuedReport', {queueId}).then((response) => {
          if (!response) {
              return;
          }
          this.savedMode.set(true);
          this.reportData.set(response.data);
          const reportArgs = response.args;
          let header;
          Meteor.callAsync('retrieveOrgNames', {orgs: reportArgs.orgIds}).then((resp) => {
              header = 'Orgs: ' + resp + '<br>';
          }).catch((err) => {
              header = 'Orgs: <br>';
          }).finally(() => {  
              header += 'Start Date: ' + reportArgs.startDate + '<br>';
              header += 'End Date: ' + reportArgs.endDate + '<br>';
              header += 'Type: ' + upperFirst(reportArgs.aggregation) + '<br>';
              this.savedHeader.set(header);
          });
        }).catch((err) => {
            return;
        });
    }
}

Template.reportCheckInOutNoPinCode.rendered = function () {
  const momentStartDate = new moment();
	$('#reportStartDate').val(momentStartDate.format("MM/DD/YYYY"));
  const momentEndDate = new moment().add(1, 'week').add(6,'days');
	$('#reportEndDate').val(momentEndDate.format("MM/DD/YYYY"));
	
	$("#reportStartDate").datepicker({
		autoclose: true, todayHighlight: true
	}).on("changeDate", function(e) {
		var momentEndDate = new moment(e.date).add(1, 'week').add(6, 'days');
		$("#reportEndDate").val(momentEndDate.format("MM/DD/YYYY"))
	})
	$("#reportEndDate").datepicker({
		autoclose: true, todayHighlight: true
	})
}

Template.reportCheckInOutNoPinCode.events({
    "click #chkQueue": function(e, i) {
        i.queueMode.set(document.getElementById('chkQueue').checked);
    },
  "click button#update": async function() {
    const instance = Template.instance();
		// var instance = Template.instance();
		// const startDate = $("#attendanceStartDate").val(), 
		// 	endDate = $("#attendanceEndDate").val();
		// instance.startDate.set(startDate);
		// instance.endDate.set(endDate);
		// if (!moment(startDate).isValid() || !moment(endDate).isValid())
		// 	return swal("Error", "You must supply a valid date range for this report.", "error");
		// const options = {
		// 	startDate,
		// 	endDate
		// };
    const startDate = $("#reportStartDate").val();
    const endDate = $("#reportEndDate").val();
    let orgIds = $("#reportOrgs").val() || [];
    const aggregation = $("#aggregationSelect").val();
      if (instance.queueMode.get()) {
          await Meteor.callAsync('queueReportDefer', {
              reportFunction: 'checkWithoutPin',
              reportName: 'Check In/Out without PIN Code',
              reportArgs: { startDate, endDate, orgIds, aggregation },
              userId: Meteor.userId(),
              reportRoute: 'reports/reportCheckInOutNoPinCode'
          });
          reportQueuedSwal();
          return;
      }

    Meteor.callAsync("checkWithoutPin", { startDate, endDate, orgIds, aggregation }).then((result) => {
      console.log("params");
      console.log({ startDate, endDate, orgIds, aggregation });
      console.log("the result");
      console.log(result);
			instance.reportData.set(result);
    }).catch((err) => { 
      console.error("Error", err);
      instance.reportData.set(result);
    });
  }
});

const renderPersonInfo = (person) => {
  if (!person) return { name: 'N/A', id: 'N/A' };
  let { firstName, lastName } = person;
  firstName = firstName ? firstName : '';
  lastName = lastName ? lastName : '';
  return {name: firstName + " " + lastName, id: person._id};
};

const renderPersonType = (person) => {
  if (!person) return 'N/A';
  if(person.type === 'person') return 'child';
  return person.type;
};

Template.reportCheckInOutNoPinCode.helpers({
    isSavedMode() {
        return Template.instance().savedMode.get();
    },
    savedHeader() {
        return Template.instance().savedHeader.get();
    },
  "moments": function() {
		const data = Template.instance().reportData.get();
		if (!data) return;
		const output = data.moments.map( m => {
      const person = m.peopleCreated[0];
      const owner = m.ownerPerson[0];
      const org = m.organization[0];
			const outputRow = [
        org.name,
        m.momentType,
        m.date,
        m.time,
        renderPersonInfo(person),
        m.createdAt,
        renderPersonType(person),
        renderPersonInfo(owner),
        renderPersonType(owner),
      ];
			return outputRow;
		});
		return output;
	},
	"columnTitles": function() {
		const data = Template.instance().reportData.get();
		if (!data) return;
		return data.headers;
	},
  "totalColumnTitles": function () {
    return ['Name', 'Global Check In/Out', 'Percentage Without PIN'];
  },
  "totals": function() {
    const data = Template.instance().reportData.get();
		if (!data) return;
    const names = data.moments.reduce((current, next) => {
      const org = next.organization[0];
      if (!(org._id in current)) {
        current[org._id] = org;
      }
      return current;
    }, {});
    return Object.keys(names).map(key => {
      const total = data.totals.find(t => t._id === key);
      const summarization = data.summarizations.find(s => s._id === key);
      return [
        names[key].name,
        total.count,
        `${numeral((summarization.count/total.count)*100).format("0.00")}%`,
      ];
    });
  },
  "isLink": (index) => [4,7].findIndex(e => e === index) > -1,
});
