<template name="reportReservations">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Scheduling Report
				<span class="text-muted pt-2 font-size-sm d-block">View reservations, cancellations, and attendance.</span></h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-body">
				  <div class="row">
					<div class="col-lg-3">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								
								<input data-cy="start-date-input" type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						  <div class="form-group">
							<label>End Date</label>
							<div class="input-group">
								
								<input data-cy="end-date-input" type="text" class="form-control pull-right" id="attendanceEndDate" value="{{formattedEndDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						<div class="form-group">
							<label>Person</label>
							<div class="input-group">
								<select data-cy="select-person" class="form-control" id="filterPerson">
									<option value="">All</option>
									  {{#each people}}
									  <option value="{{_id}}">{{lastName}}, {{firstName}}</option>
									  {{/each}}
								  </select>
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						<div class="form-group">
							<label>Group:</label>
							<select data-cy="select-group" class="form-control" id="filterGroup">
								<option value="">All</option>
								  {{#each groups}}
								  <option value="{{_id}}">{{name}}</option>
								  {{/each}}
							  </select>
						 </div>
					</div>
				  </div>
				 
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table data-cy="scheduling-report-table" class="table">
					<tbody><tr>
					  
					  <th>Name</th>
					  <th>Date</th>
					  <th>Check-In</th>
					  <th>Check-Out</th>
					  <th>Elapsed Time (Units)</th>
					  <!--<th>Full/Half Day</th>-->
					  <th>Checked In By</th>
					  {{#if hasCustomization "people/types/showPayer"}}
						  <th>Payer</th>
					  {{/if}}
											<th>Cancellation (Comments)</th>
											<th>Cancellation Date</th>
					</tr>
					
					{{# each attendees}}
					<tr>
					  <td data-cy="attendee-name">{{attendeeName}}</td>
					  <td data-cy="attendance-date">{{attendanceDate}}</td>
					  <td data-cy="check-in-time">{{formatTime checkInTime}}</td>
					  <td data-cy="check-out-time">{{formatTime checkOutTime}}</td>
					  <td data-cy="elapsed-time">{{elapsedTime}} {{elapsedTimeUnits}}</td>
					  <!--<td>{{scheduledLength}}</td>-->
					  <td data-cy="checked-in-by">{{checkedInBy}}</td>
					  {{#if hasCustomization "people/types/showPayer"}}
						  <td data-cy="attendance-payer">{{attendeePayer}}</td>
					  {{/if}}
											<td data-cy="cancellation-reason">{{#if cancellationReason}}{{cancellationReason}} ({{cancellationComments}}){{/if}}</td>
											<td data-cy="cancellation-date">{{#if cancellationDate}}{{formatDate cancellationDate "MM/DD/YYYY"}}{{/if}}</td>
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
