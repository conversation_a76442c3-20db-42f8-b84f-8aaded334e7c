import './reportCaliforniaRoster.html';
import { Orgs } from "../../../lib/collections/orgs";

Template.reportCaliforniaRoster.created = function() {
    this.reportData = new ReactiveVar();
    this.rows = new ReactiveVar();
}

Template.reportCaliforniaRoster.events({
    "click button#update": function() {
        var instance = Template.instance();
        const threeYearsAgo = moment().subtract(3, 'years').startOf('day').toDate();
        Meteor.callAsync("RosterReportCalifornia", { startDate: threeYearsAgo }).then((result) => {
            if(result.error) {
                bug(result.errMsg);
            } else {
                instance.rows.set(result.data.length);
                instance.reportData.set(result.data);
            }
        });
    },
    "click #btnExportCsv": function(event, instance) {
        var outputFile = 'CaliforniaRosterReport.csv'
        exportTableToCSV.apply(this, [$('#californiaRosterTable'), outputFile]);
    }
});

Template.reportCaliforniaRoster.helpers({
    ShowResults(option) {
        return Template.instance().rows.get() >= 0 ? true : false;
    },
    DataReport(option) {
        return Template.instance().reportData.get() || [];
    },
    orgName() {
        return Orgs.current() && Orgs.current().name;
    },
    getToDay() {
        return new moment().format("MM/DD/YYYY");
    },
    parseDate(date) {
        return typeof date == "undefined" ? "" : new moment(date).format("MM/DD/YYYY");
    },
    getPhysician(data) {
        return typeof data == "undefined" ? "No Records" : data
    },
    getPhysicianNumber(data) {
        return typeof data == "undefined" ? "No Records" : ` (${data})`
    },
    getAddress(data) {
        if (!data || !data.parentStreetAddress) {
            return "No Address";
        }
        return data.parentStreetAddress;
    },
    getParents(data) {
        if (!data) {
            return "";
        }
        let lastName = data.lastName || "";
        let firstName = data.firstName || "";

        return lastName && firstName ? `${lastName}, ${firstName}` : "No Name";
    },
    hasParents(parents){
        return parents.length > 0 
    },
    getParentsPhone(data) {
        if(typeof data == "undefined")
            return ""
        return typeof data.phonePrimary != "undefined" ? data.phonePrimary : ""
    },
    legalFacilityName() {
        return Orgs.current() && Orgs.current().legalFacilityName; 
    },
    facilityLicenseNumber() {
        return Orgs.current() && Orgs.current().facilityLicenseNumber;
    },
    reportTitle() {
        return "California Roster Report (All Attendees in Past 3 Years)";
    },
    reportDescription() {
        return "This report shows all children who have attended at any point in the past 3 years, regardless of their enrollment date or current status.";
    },
    reportDateRange() {
        const threeYearsAgo = moment().subtract(3, 'years').format('MM/DD/YYYY');
        const today = moment().format('MM/DD/YYYY');
        return `${threeYearsAgo} - ${today}`;
    },
    enrollmentStatus(child) {
        return child.status === 'Enrolled' ? 'Active' : 'Inactive';
    }
});