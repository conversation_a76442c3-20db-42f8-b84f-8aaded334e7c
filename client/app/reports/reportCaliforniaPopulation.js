import './reportCaliforniaPopulation.html';
import _ from '../../../lib/util/underscore';

Template.reportCaliforniaPopulation.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.startDate = new ReactiveVar();
	this.endDate = new ReactiveVar();
}

Template.reportCaliforniaPopulation.rendered = function() {
	//$('#attendanceStartDate').datepicker({autoclose:true});
	//$('#attendanceEndDate').datepicker({autoclose:true});
	$('.input-daterange').datepicker({autoclose:true});
}

Template.reportCaliforniaPopulation.events({
	"click button#update": function() {
		var instance = Template.instance();
		const startDate = $("#attendanceStartDate").val(), 
			endDate = $("#attendanceEndDate").val();
		instance.startDate.set(startDate);
		instance.endDate.set(endDate);
		if (!moment(startDate).isValid() || !moment(endDate).isValid())
			return swal("Error", "You must supply a valid date range for this report.", "error");
		const options = {
			startDate,
			endDate
		};
		Meteor.callAsync("populationReportCalifornia", options).then((result) => {
			console.log("result", result);
			instance.reportData.set(result);
		}).catch((err) => {
			console.error("Error", err);
			instance.reportData.set(result);
		});
	},
	"click #btnExportCsv": function(event, instance) {	

		var outputFile = 'populationreport.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#californiaPopulationTable'), outputFile]);
	}
});



Template.reportCaliforniaPopulation.helpers({
	"people": function() {
		const data = Template.instance().reportData.get();
		if (!data) return;
		const output = data.map( person => {
			const outputRow = [];
			_.each(populationReportFieldMap, fieldMap => {
				outputRow.push( fieldMap.val(person));
			});
			return outputRow;
		});
		return output;
	},
	"columnTitles": function() {
		const data = Template.instance().reportData.get();
		if (!data) return;
		return populationReportFieldMap.map( i => i.label);
	}
});

var populationReportFieldMap = [
		{
			label: 'Report Date',
			val: (obj) => "TBD" 
		},
		{
			label: 'Vendor Code',
			val: (obj) => "TBD" 
		},
		{
			label: 'FICN',
			val: (obj) => _.deep(obj, "profileData.FICN")
		},
		{
			label: 'HoH Last',
			val: (obj) => _.deep(obj, "profileData.headOfHousehold.lastName") 
		},
		{
			label: 'HoH First',
			val: (obj) => _.deep(obj, "profileData.headOfHousehold.firstName") 
		},
		{
			label: 'HoH MI',
			val: (obj) => _.deep(obj, "profileData.headOfHousehold.middleInitial") 
		},
		{
			label: 'HoH Zip',
			val: (obj) => _.deep(obj, "profileData.headOfHousehold.zipcode") 
		},
		{
			label: 'Tanf/Calworks',
			val: (obj) => _.deep(obj, "profileData.hasTanfOrCalworks")
		},
		{
			label: 'Income Greater?',
			val: (obj) => _.deep(obj, "profileData.incomeGreater70Percent")
		},
		{
			label: 'Family Size',
			val: (obj) => _.deep(obj, "profileData.familySize")
		},
		{
			label: 'Family Income',
			val: (obj) => _.deep(obj, "profileData.familyIncomeMonthly") 
		},
		{
			label: 'Services Reason',
			val: (obj) => _.deep(obj, "profileData.serviceReasonCode")
		},
		{
			label: 'HoH FIPS Code',
			val: (obj) => _.deep(obj, "profileData.headOfHousehold.fipsCode")
		},
		{
			label: 'Family Start Date',
			val: (obj) => _.deep(obj, "profileData.familyStartDate")
		},
		{
			label: "Child's Last Name",
			val: (obj) => obj.lastName
		},
		{
			label: "Child's First Name",
			val: (obj) => obj.firstName
		},
		{
			label: "Child's MI",
			val: (obj) => _.deep(obj, "profileData.middleInitial")
		},
		{
			label: "Child's Ethnicity",
			val: (obj) => _.deep(obj, "profileData.hasEthnicity")
		},
		{
			label: 'Child Race: American Indian/Alaskan Native',
			val: (obj) => _.deep(obj, "profileData.race") == "American Indian/Alaskan Native"
		},
		{
			label: 'Child Race: Asian',
			val: (obj) => _.deep(obj, "profileData.race") == "Asian"
		},
		{
			label: 'Child Race: Black or African American',
			val: (obj) => _.deep(obj, "profileData.race") == "Black/African American"
		},
		{
			label: 'Child Race: Native Hawaiin or Other Pacific Islander',
			val: (obj) => _.deep(obj, "profileData.race") == "Native Hawaiin/Other Pacific Islander"
		},
		{
			label: 'Child Race: White',
			val: (obj) => _.deep(obj, "profileData.race") == "White" 
		},
		{
			label: "Child's Gender",
			val: (obj) => _.deep(obj, "profileData.gender") 
		},
		{
			label: "Child's DoB",
			val: (obj) => _.deep(obj, "profileData.birthday") 
		},
		{
			label: "Child IEP",
			val: (obj) => _.deep(obj, "profileData.hasIEP") 
		},
		{
			label: "Child Primary Language",
			val: (obj) => _.deep(obj, "profileData.primaryLanguageCode")  
		},
		{
			label: 'Child English Learner',
			val: (obj) => _.deep(obj, "profileData.isEnglishLearner") 
		},
		{			
			label: "Child Start Date",
			val: (obj) => _.deep(obj, "profileData.enrollmentDate") 
		},
		{
			label: "Child Part-time?",
			val: (obj) => _.deep(obj, "profileData.isPartTime") 
		},
		{
			label: "Provider FEIN",
			val: (obj) => "TBD" 
		},
		{
			label: "Provider FIPS",
			val: (obj) => "TBD" 
		},
		{
			label: "Provider Zip",
			val: (obj) => "TBD" 
		},
		{
			label: "QRIS",
			val: (obj) => "TBD" 
		},
		{
			label: "Accreditation Status",
			val: (obj) => "TBD" 
		},
		{
			label: "Child Care Type",
			val: (obj) => "TBD" 
		},
		{
			label: "Program Code 1",
			val: (obj) => "" 
		},
		{
			label: "Program Code 2",
			val: (obj) => "" 
		},
		{
			label: "Program Code 3",
			val: (obj) => "" 
		},
		{
			label: "Services Date",
			val: (obj) => "" 
		},
	];