import { Template } from 'meteor/templating';
import './_orgCustomizationToggleRow.html';
import { OrgCustomizationToggleRowService } from './orgCustomizationToggleRowService';

Template._orgCustomizationToggleRow.onCreated(function () {
    this.service = new OrgCustomizationToggleRowService(
        this.data?.customization,
        this.data?.title,
        this.data?.description
    );
});

Template._orgCustomizationToggleRow.helpers({
    isEnabled() {
        return Template.instance().service.isEnabled();
    },
});

Template._orgCustomizationToggleRow.events({
    'change input[name="customization-toggle"]': async function (e, i) {
        await i.service.toggle(e.currentTarget);
    }
});