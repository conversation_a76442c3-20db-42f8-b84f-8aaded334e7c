import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './zkteco.html';
import { Orgs } from '../../../../lib/collections/orgs';

Template.zkteco.created = function () {
    this.syncing = new ReactiveVar(false);
    this.monitoring = new ReactiveVar(false);
}

Template.zkteco.rendered = function() {
    checkZkTecoForm();
}

Template.zkteco.helpers({
    values() {
        return Orgs.current().zkTecoValues || {};
    },
    isSyncing() {
        return Template.instance().syncing.get()
    },
    syncText() {
        return Template.instance().syncing.get() ? 'Syncing...' : 'Sync Org To ZkTeco';
    },
    isMonitoring() {
        return Template.instance().monitoring.get()
    },
    monitorText() {
        return Template.instance().monitoring.get() ? 'Syncing Events...' : 'Sync Events';
    },
    lastUpdatedText() {
        if (!Orgs.current().zkTecoValues?.lastUpdate) {
            return '';
        }
        return 'Last Door Lock Sync: ' + moment(Orgs.current().zkTecoValues.lastUpdate).format('MM/DD/YYYY - h:mm A');
    },
    lastBatchUpdatedText() {
        if (!Orgs.current().zkTecoValues?.lastBatchUpdate) {
            return '';
        }
        return 'Last Full Batch Door Lock Sync: ' + moment(Orgs.current().zkTecoValues.lastBatchUpdate).format('MM/DD/YYYY - h:mm A');
    }
});

Template.zkteco.events({
   'click #btnSaveZkTeco': function(e) {
       e.preventDefault();
       const formData = new FormData(document.getElementById('zkTecoForm'));
       const values = Object.fromEntries(formData);

       Meteor.callAsync("saveZkTecoValues", values).then(result => {
           mpSwal.fire({title:"Save Successful!"});
       }).catch(error => {
           mpSwal.fire({icon:"error", title:"Problem saving ZKTeco configuration", text: error.reason});
       });
   },
    'click #btnMonitor': async function(e, i) {
        i.monitoring.set(true);
        e.preventDefault();
        Meteor.callAsync('processZkTecoTransactions', Orgs.current()._id).then(result => {
            mpSwal.fire({title:"Sync events finished."})
            i.monitoring.set(false);
        }).catch(error => {
            mpSwal.fire({title:"Sync events finished."})
            i.monitoring.set(false);
        });
    },
    'click #btnUpdateOrg': function(e, i) {
        i.syncing.set(true);
        e.preventDefault();
        Meteor.callAsync('updateZkTecoForOrg', Orgs.current()._id).then(result => {
            mpSwal.fire({title:"Sync triggered."})
            i.syncing.set(false);
        }).catch(error => {
            mpSwal.fire({title:"Sync triggered."})
            i.syncing.set(false);
        });
    },
   'input #zkTecoForm': function(event, instance) {
       checkZkTecoForm();
   },
});

function checkZkTecoForm() {
    const form = document.getElementById('zkTecoForm');
    document.getElementById('btnSaveZkTeco').disabled = !form.checkValidity();
}