import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './airslateFlows.html';
import { processPermissions } from '../../../../lib/permissions';
import { showModal } from '../../main';
import '../../simpleModal/simpleModal';

Template.airslateFlows.onCreated( function() {
	var self=this;
	self.flows = new ReactiveVar();
	populateFlows(self);
});

Template.airslateFlows.helpers({
	flows() {
		return Template.instance().flows.get();
	},
	canManageAirslate() {
		return processPermissions({
			assertions: [{ context: "integrations/airslate", action: "edit" }],
			evaluator: (person) => person.type=="admin"
		  });
	}
});

Template.airslateFlows.events({
	"click .btn-delete"(e) {
		const flowId = $(e.currentTarget).data("flow-id");
		mpSwal.fire({
				title: "Confirm Deletion",
				text: `You are about to delete this airSlate flow registraion from MomentPath. Are you sure?`,
				showCancelButton:true
			}).then( result => {
				if (result.value) {
					Meteor.callAsync("airslateDeleteFlow", {flowId}).then(result => {
						mpSwal.fire("Flow deleted.");
					}).catch(error => {
						mpSwal.fire("Error deleting flow", error.reason, "error");
					});
				}
			});
	},
	"click #btn-register-flow"(e, instance) {
		showModal("simpleModal", {
			title: "Register New airSlate Flow",
			template: "airslateNewFlowModal",
			data: { hideCopy: true },
			onSave: (e, i, formFieldData) => {
				Meteor.callAsync("airslateRegisterFlow", formFieldData).then(response => {
					mpSwal.fire("Flow successfully registered.");
					$("#simpleModal").modal("hide");
					populateFlows(instance);
				}).catch(error => {
					instance.flows.set(null);
					$(e.target).html('Save').prop("disabled", false);
					return mpSwal.fire("Error", "There was an issue registering your flow: " + error.reason, "error");
				});
			}
		});
	}
});

function populateFlows(instance) {
	Meteor.callAsync("airslateGetFlows").then(response => {
		instance.flows.set(response);
	}).catch(error => {
		instance.flows.set(null);
		return mpSwal.fire("Error", "There was an issue loading your organization's flows.", "error");
	});
}