<template name="zkteco">
  <div class="container">
    <h2 class="ml-4">ZKTeco</h2>
    <br/>
    <div class="flex-row-fluid">
      <div class="card card-custom mb-4">
        <div class="card-header flex-wrap border-0 pt-6 pb-0">
          <div class="card-title">
            <h3 class="card-label">Door Lock Identification</h3>
          </div>
          <div class="card-toolbar">
            {{#if isSuperAdmin }}
                <button disabled={{isSyncing}} class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" title="Should only need to be done once manually" id="btnUpdateOrg">
                  {{syncText}}
                </button>
                <button disabled={{isMonitoring}} class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" title="For Testing Only" id="btnMonitor">
                  {{monitorText}}
                </button>
            {{/if}}
          </div>
        </div>
        <div class="row">
          <div class="col-4 offset-8 text-right pr-17">
            <span title="Note that the door lock mechanism can take several minutes to update after the sync">
              {{ lastUpdatedText }}
                <br>
              {{ lastBatchUpdatedText }}
            </span>
          </div>
        </div>
        <div class="card-body">
          <form id="zkTecoForm">
            <div class="form-group">
              <label for="accLevelId">Access Level ID</label>
              <input type="text" name="accLevelId" id="accLevelId" class="form-control" required value="{{values.accLevelId}}">
            </div>
            <div class="form-group">
              <label for="areaId">Area ID</label>
              <input type="text" name="areaId" id="areaId" class="form-control" required value="{{values.areaId}}">
            </div>
            <div class="form-group">
              <label for="outerDoorId">Outer Door Lock ID#</label>
              <input type="text" name="outerDoorId" id="outerDoorId" class="form-control" value="{{values.outerDoorId}}">
            </div>
            <div class="form-group">
              <label for="innerDoorId">Inner Door Lock ID# (if one exists)</label>
              <input type="text" name="innerDoorId" id="innerDoorId" class="form-control" value="{{values.innerDoorId}}">
            </div>
            <div>
              <button class="btn btn-primary font-weight-bolder" id="btnSaveZkTeco">Save</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
