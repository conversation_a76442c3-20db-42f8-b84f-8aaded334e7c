<template name="airslate">
	<div class="container">
		
		<h2 class="ml-lg-8">airSlate</h2>
		<br/>

		
	  	<div class="flex-row-fluid ml-lg-8">
			<div class="card card-custom gutter-bs">
				{{#if shouldShowTemplate "overview"}}
				<div class="card-header card-header-tabs-line">
					<div class="card-toolbar">
					  <ul class="nav nav-tabs nav-tabs-space-lg nav-tabs-line nav-tabs-bold nav-tabs-line-3x" role="tablist">
						  <li class="nav-item mr-3">
							<a class="nav-link {{activeIfCurrentTab "recent-slates"}}" href="#recent-slates">
							  <span class="nav-icon"><i class="fad fa-info-circle mr-2"></i></span>
							  <span class="nav-text font-weight-bold">Recent Slates</span>
							</a>
						  </li>
						<li class="nav-item mr-3">
						  <a class="nav-link {{activeIfCurrentTab "flows"}}" href="#flows">
							<span class="nav-icon"><i class="fad fa-cog mr-2"></i></span>
							<span class="nav-text font-weight-bold">Flows</span>
						  </a>
						</li>
						<li class="nav-item mr-3">
							<a class="nav-link {{activeIfCurrentTab "config"}}" href="#config">
							  <span class="nav-icon"><i class="fad fa-tools mr-2"></i></span>
							  <span class="nav-text font-weight-bold">Config</span>
							</a>
						</li>
					  </ul>
					</div>
				</div>
				<div class="card-body px-0">
					<div class="p-5">
						{{#if routeContainsHash "flows"}}
							{{> airslateFlows}}
						{{else if routeContainsHash "config"}}
							<div class='row'>
								
								<div class="col-4"><span style="font-weight:bold">Internal Location ID</span></div>
								<div class="col-4">{{internalLocationId}}</div>
								<div class="col-4"><a href="#" id="btnEditLocationId">Edit</a></div>
								
							</div>
						{{else}}
							{{> airslateSlates}}
						{{/if}}
					</div>
				</div>

				{{else if shouldShowTemplate "slate"}}
					<a href="javascript:history.back();" class="btn btn-primary btn-sm">&lt; Overview</a><br/>
					Slates go here.
				{{else if shouldShowTemplate "flow"}}
					{{> airslateFlow}}
				{{/if}}

			</div>
		</div>

		
	</div>
</template>