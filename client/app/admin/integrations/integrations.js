import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {AvailableCustomizations} from "../../../../lib/customizations";
import { Orgs } from '../../../../lib/collections/orgs.js';
import { processPermissions } from '../../../../lib/permissions.js';
import './_kinderConnect.js';
import './adp.js';
import './airslate.js';
import './hubspot.js';
import './salesforce.js';
import './zkteco.js';
import './integrations.html';

Template.integrations.onCreated(function() {
	var self = this;
	self.isManageKinderClicked = new ReactiveVar(false);
});

Template.integrations.helpers({
	"selectedIntegration"() {
		return FlowRouter.getParam("integration");
	},
	hasAirslate() {
		return Orgs.current() && Orgs.current().hasCustomization("integrations/airslate/enabled") && processPermissions({
			assertions: [{ context: "integrations/airslate", action: "read" }],
			evaluator: (person) => person.type=="admin"
		});
	},
	hasSalesforce() {
		return Orgs.current() && Orgs.current().salesforce?.masterOrg;
	},
	hasZkTeco() {
		return Orgs.current().hasCustomization(AvailableCustomizations.DOOR_LOCKS_ZKTECO);
	},
	shouldShowAdpUpload() {
		return processPermissions({
			assertions: [{ context: "people/uploadAdp", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowKinderConnect() {
		return true;
	},
	isManageKinderClicked() {
		return Template.instance().isManageKinderClicked.get();
	}
});

Template.integrations.events({
	"click  #btn-kinder-manage"(e, instance) {
		instance.isManageKinderClicked.set(!instance.isManageKinderClicked.get());
	}
});