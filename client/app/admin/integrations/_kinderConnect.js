import _ from 'lodash';
import { Log } from '../../../../lib/util/log';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating'
import { ReactiveVar } from 'meteor/reactive-var';
import './_kinderConnect.html';

Template._kinderConnect.onCreated(function() {
    const self = this;
    self.saveDisabled = new ReactiveVar(true);
    // Call server to fetch provider list from db ...
    self.providers = new ReactiveVar([]);
    self.groupPassword = new ReactiveVar('');
    Meteor.callAsync("getKinderConnectSettings").then(result => {
        let providers = result?.providers ? result.providers : [{id:''}];
        self.providers.set(providers);
        self.groupPassword.set(result?.groupPassword);
    }).catch(error => {
        Log.info("Kinder connect setting errors", error);
        self.providers.set([]);
        self.groupPassword.set('');
        return mpSwal.fire("Error", "There was an issue loading your organization's kinder connect settings.", "error");
    });

})

Template._kinderConnect.onRendered(function() {
    $('#btnSaveKinderConnect').prop('disabled', Template.instance().saveDisabled.get());
});


Template._kinderConnect.helpers({
    providers() {
        return Template.instance().providers.get();
    },
    groupPassword() {
        return Template.instance().groupPassword.get();
    }
});

Template._kinderConnect.events({
    "keypress .providers" (e, instance){

        let ASCIICode = (e.which) ? e.which : e.keyCode
         if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57)){
            return false;
        }
        if (e.target.value.length > 8) {
            return false;
        }
    },
    "click #btn-add-provider-id"(e, instance) {
        let providers = instance.providers.get();
        providers.push({id:""});
		instance.providers.set(providers);
	},
    "click #btn-save-kinder-connect"(e, instance) {

        const groupPassword = $.trim($("#group-password").val());
        if(groupPassword === undefined || groupPassword === '' ){
            return mpSwal.fire("Error", "Group Password can not be empty", "error");
            return false;
        }
        let providers = []
        let allProviders = []
        $(".providers").each(function(index,ele){
            let value = $.trim(($(ele).val()));
            const orgValue = $.trim(ele.getAttribute('data-orgvalue'));
            if(value !== undefined && value !== ''){
                let mode = 'nochange';
                if(orgValue === ''){
                    mode = 'add';
                }else{
                    mode = value != orgValue ? 'edit' : 'nochange';
                }
                allProviders.push({id:$(ele).val()});
                if(mode !== 'nochange')  {
                    providers.push({id:$(ele).val(),mode:mode,oldValue:{id:orgValue}});
                }    
            }else{
                if (orgValue !='') {
                    providers.push({id:orgValue,mode:'delete',oldValue:{id:orgValue}});
                }
            }
        });
        
        const allProviderIds = _.map(allProviders,'id');
        const duplicate = _.filter(allProviderIds, (val, i, iteratee) => _.includes(iteratee, val, i + 1));
        if(duplicate.length > 0) {
            return mpSwal.fire("Error", "Duplicate providers were found", "error");
        }        

        const kinderConnectorSettings = {providers,groupPassword}

        Meteor.callAsync("upsertProviders", {kinderConnectorSettings}).then(result => {
            if(allProviders.length === 0 ) {
                allProviders.push({id:""})
            }
            instance.providers.set(allProviders);
            instance.groupPassword.set(groupPassword);
            return mpSwal.fire("Success", "Settings saved successfully", "success");
        }).catch(error => {
            Log.info("Kinder connect update providers errors", error);
            if (error["error"] === 403) {
                return mpSwal.fire("Error", error["reason"], "error");
            }
            return mpSwal.fire("Error", "There was an issue with saving kinder connect settings.", "error");
        });
        
        return false;
	}
});
