<template name="addScheduleType">
    <div class="row">
        <form id="frmAddScheduleType" style="display:none" class="row col-md-12 mb-6">
            <input type="hidden" name="schedule-type-id">
            <div class="row col-md-12 mb-4">
                <div class='col-md-8'>
                    <label>Type:</label>
                    <input data-cy="input-schedule-type-name" type="text" class="form-control" id="AddScheduleType-Name" placeholder="e.g. Full-time, Before Care, etc." required>
                </div>
                {{#if hasAutomaticGroupUpdating}}
                    <div class="col-md-4">
                        <label>Maps to Default Group:</label>
                        <select data-cy="maps-to-default-group-select" class="form-control" id="AddScheduleType-DefaultGroup">
                            <option value="">Select a group</option>
                            {{#each getGroups}}
                                <option value="{{_id}}">{{name}}</option>
                            {{/each}}
                        </select>
                    </div>
                {{/if}}
                {{#if isSuperAdmin}}
                    <div class="col-md-4">
                        <label>id:</label>
                        <input data-cy="schedule-type-id" type="text" class="form-control" id="scheduleId" value="" disabled>
                    </div>
                {{/if}}
            </div>
            <div class="row col-12">
                <div class="col-4">
                    <label>Time Block:</label>
                    <div class="radio-list">
                        <label class="radio radio-primary">
                            <input data-cy="all-day-check" type="radio" name="scheduleTypeTimeBlock" value="all-day" checked="checked">
                            <span></span>
                            All-day
                        </label>
                        <label class="radio radio-primary">
                            <input data-cy="scheduled-from-check" type="radio" name="scheduleTypeTimeBlock" value="scheduled">
                            <span></span>
                            Scheduled from
                        </label>
                        <div class="form-group form-inline row ml-10">
                            <input data-cy="set-start-time" type="text" class="form-control form-control-solid datetimepicker-input" id="schedule-type-start-time" placeholder="Set start time"  data-toggle="datetimepicker" data-target="#schedule-type-start-time"/>
                            to
                            <input data-cy="set-end-time" type="text" class="form-control form-control-solid datetimepicker-input" id="schedule-type-end-time" placeholder="Set end time"  data-toggle="datetimepicker" data-target="#schedule-type-end-time"/>
                        </div>
                    </div>
                </div>
                {{# if hasRegistrationFlow }}
                    <div class="row col-12 mb-4">
                        <div class="col-4">
                            <label>Max Daily Enrollment:</label>
                        </div>
                    </div>
                    {{#if hasWeekendsEnabled}}
                        <div class="row col-12 mb-4 align-items-center">
                            <div class="col-2 text-right">
                                <label class="mb-0">Sunday:</label>
                            </div>
                            <div class="col-2">
                                <input data-cy="max-daily-enrollment-sunday" type="number" class="form-control"
                                       id="AddScheduleType-maxEnrollmentSun" min="0" max="100" value="10" step="1">
                            </div>
                        </div>
                    {{/if}}
                    <div class="row col-12 mb-4 align-items-center">
                        <div class="col-2 text-right">
                            <label class="mb-0">Monday:</label>
                        </div>
                        <div class="col-2">
                            <input data-cy="max-daily-enrollment-monday" type="number" class="form-control"
                                   id="AddScheduleType-maxEnrollmentMon" min="0" max="100" value="10" step="1">
                        </div>
                    </div>
                    <div class="row col-12 mb-4 align-items-center">
                        <div class="col-2 text-right">
                            <label class="mb-0">Tuesday:</label>
                        </div>
                        <div class="col-2">
                            <input data-cy="max-daily-enrollment-tuesday" type="number" class="form-control"
                                   id="AddScheduleType-maxEnrollmentTue" min="0" max="100" value="10" step="1">
                        </div>
                    </div>
                    <div class="row col-12 mb-4 align-items-center">
                        <div class="col-2 text-right">
                            <label class="mb-0">Wednesday:</label>
                        </div>
                        <div class="col-2">
                            <input data-cy="max-daily-enrollment-wednesday" type="number" class="form-control"
                                   id="AddScheduleType-maxEnrollmentWed" min="0" max="100" value="10" step="1">
                        </div>
                    </div>
                    <div class="row col-12 mb-4 align-items-center">
                        <div class="col-2 text-right">
                            <label class="mb-0">Thursday:</label>
                        </div>
                        <div class="col-2">
                            <input data-cy="max-daily-enrollment-thursday" type="number" class="form-control"
                                   id="AddScheduleType-maxEnrollmentThu" min="0" max="100" value="10" step="1">
                        </div>
                    </div>
                    <div class="row col-12 mb-4 align-items-center">
                        <div class="col-2 text-right">
                            <label class="mb-0">Friday:</label>
                        </div>
                        <div class="col-2">
                            <input data-cy="max-daily-enrollment-friday" type="number" class="form-control"
                                   id="AddScheduleType-maxEnrollmentFri" min="0" max="100" value="10" step="1">
                        </div>
                    </div>
                    {{#if hasWeekendsEnabled}}
                        <div class="row col-12 mb-4 align-items-center">
                            <div class="col-2 text-right">
                                <label class="mb-0">Saturday:</label>
                            </div>
                            <div class="col-2">
                                <input data-cy="max-daily-enrollment-saturday" type="number" class="form-control"
                                       id="AddScheduleType-maxEnrollmentSat" min="0" max="100" value="10" step="1">
                            </div>
                        </div>
                    {{/if}}
                {{/if}}
                <div class="col-4 mb-4 checkbox-list">
                    <label class="checkbox checkbox-primary">
                        <input data-cy="hide-from-forecasting-check" type="checkbox" class="form-check-input" id="schedule-type-hide-forecasting"  >
                        <span></span>
                        Hide from forecasting
                    </label>
                </div>
            </div>
            <div class="row col-12 mb-4">
                <div class="col-4">
                    <label>FTE Count:</label>
                    <input data-cy="fte-count-input" type="number" class="form-control" id="AddScheduleType-FTE" min="0" max="1.0" value="1.0" step="0.1">
                </div>
            </div>
            <div class="row col-md-4 pt-8">
                <div data-cy="save-add-schedule-type" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveAddScheduleType">Save</div>
                <div data-cy="cancel-add-schedule-type" class="btn btn-secondary font-weight-bolder" id="btnCancelAddScheduleType">Cancel</div>
            </div>
        </form>
        <table class="table">
            <tr style="border-bottom:1px solid #000">
                <th>Name</th>
                {{#if hasAutomaticGroupUpdating}}
                    <th>Maps to Default Group</th>
                {{/if}}
                <th>Start Time</th>
                <th>End Time</th>
                <th>FTE</th>
                <th>Hidden in Forecasts</th>
                <th>Actions</th>
            </tr>
            {{#each getScheduleTypes}}
                <tr>
                    <td data-cy="name-schedule-type">
                        {{type}}
                    </td>
                    {{#if hasAutomaticGroupUpdating}}
                        <td data-cy="default-group-map">
                            {{getDefaultGroupName defaultGroupId}}
                        </td>
                    {{/if}}
                    <td data-cy="start-time">
                        {{startTime}}
                    </td>
                    <td data-cy="end-time">
                        {{endTime}}
                    </td>
                    <td data-cy="fte-count">
                        {{fteCount}}
                    </td>
                    <td data-cy="hidden-in-forecasts-opt">
                        {{#if hideInForecasting}}X{{/if}}
                    </td>
                    <td>
                        <span data-cy="edit-schedule-type-btn" class="btnEditScheduleType text-primary" style="cursor:pointer;" data-id="{{_id}}">Edit</span> |
                        <span data-cy="delete-schedule-type-btn" class="btnDeleteScheduleType text-primary" style="cursor:pointer;" data-id="{{_id}}">Delete</span>
                    </td>
                </tr>
            {{/each}}
        </table>
    </div>
</template>