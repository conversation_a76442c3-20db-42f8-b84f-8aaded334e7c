<template name="_organizationTagModal">
    <div class="modal fade" id="organizationTagModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content p-4 rounded-4">
                <div class="modal-header">
                    <h5 class="modal-title">Organization Tags</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <form id="organizationTagForm" class="form-horizontal">
                            <ul id="organizationTagList" class="list-unstyled">
                                {{#each tags}}
                                    {{> tagRow this}}
                                {{/each}}
                            </ul>
                        </form>
                    </div>
                </div>

                <div class="row">
                    <div class="col-2">
                        <button class="btn btn-primary ml-4 px-4" id="addTagBtn" data-cy="add-tag">Add Tag</button>
                    </div>
                </div>

                <div class="modal-footer justify-content-right border-0">
                    <button class="btn btn-primary px-4 mr-2" id="saveTags" data-cy="save-org-tags" disabled="{{isSaveDisabled}}">Save</button>
                    <button class="btn btn-secondary px-4" data-cy="cancel-org-tags" data-dismiss="modal" aria-label="Cancel">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>

<template name="tagRow">
    <li data-id="{{ _id }}" data-cy="organization-tag-{{ _id }}">
        <fieldset data-cy="organization-tag-group" class="form-group organizationTagGroup">
            <legend style="font-size: inherit; font-weight: 600;">
            </legend>
            <div class="form-group row mt-2">
                <div class="col-1 align-items-center d-flex text-right justify-content-end">
                    <i class="fad fad-primary fa-arrows-up-down sort-handle"></i>
                </div>
                <label for="organizationTag-{{ _id }}" class="col-form-label col-2 text-right">
                    Tag:
                </label>
                <div class="col-6 ml-n4">
                    <input
                            data-cy="input-organization-tag"
                            type="text"
                            value="{{name}}"
                            required
                            id="organizationTag-{{ _id }}"
                            data-index="{{ index }}"
                            readonly="{{isReadonly}}"
                            class="form-control form-control-solid {{invalidClass}}"
                    >
                    {{#if showValidationError}}
                        <div class="invalid-feedback d-block">
                            {{validationMessage}}
                        </div>
                    {{/if}}
                </div>
                <label for="isArchivedReason-{{ _id }}" class="col-form-label col-1 text-right">
                    Archived:
                </label>
                <div class="col-1 align-items-center d-flex ml-n4">
                    <input checked="{{archived}}" type="checkbox" class="archiveOrganizationTag ml-2" data-index="{{ index }}" title="Archived" data-cy="archive-organization-tag" />
                </div>
                <div class="col-1 align-items-center d-flex">
                    {{# if isNew }}
                        <a href="#" class="removeOrganizationTag ml-2" data-id="{{ _id }}" title="Remove"><i class="fad fad-primary fa-trash-alt"></i></a>
                    {{/if}}
                </div>
            </div>
        </fieldset>
    </li>
</template>
