<template name="orgPlans">

	{{#if hasCustomPlan}}
	<div class="box box-solid content-box descriptive-content">
		<div class="box-body">
			<h1>Plan Details</h1>
			You currently have a custom or enterprise plan. Please contact your account representative with any concerns or requests.<br/>
		</div>
	</div>
	{{else if hasAutomaticPlan}}
	<div class="box box-solid content-box descriptive-content">
		<div class="box-body">
			<ul class="nav nav-pills" role="tablist">
				<li role="presentation" class="active"><a href="#plan-details" aria-controls="plan-details" role="tab" data-toggle="tab">Plan Details</a></li>
				<li role="presentation"><a href="#momentpath-invoices" aria-controls="momentpath-invoices" role="tab" data-toggle="tab">MomentPath Invoices</a></li>
			</ul>
			<div class="tab-content">
				<div role="tabpanel" class="tab-pane active" id="plan-details">
					<div class="row">
						<div class="col-sm-offset-1 col-sm-2 field-label">
							Current Plan:
						</div>
						<div class="col-sm-8 field-data">
							{{currentPlanInfo.planDescription}}
						</div>
					</div>
					<div class="row">
						<div class="col-sm-offset-1 col-sm-2 field-label">
							Price:
						</div>
						<div class="col-sm-8 field-data">
							{{currentPlanInfo.priceDescription}}
						</div>
					</div>
					<div class="row">
						<div class="col-sm-offset-1 col-sm-2 field-label">
							Maximum Active Children:
						</div>
						<div class="col-sm-8 field-data">
							{{currentPlanInfo.seatDescription}}
						</div>
					</div>
					<div class="row">
						<div class="col-sm-offset-1 col-sm-2 field-label">
							Current Payment Method:
						</div>
						<div class="col-sm-8 field-data">
							{{paymentMethodDescription}}<br/>
							<a href="#" class="edit-field btn-replace-existing-payment-method"><i class="fa fa-fw fa-pencil-square-o"></i> replace payment method</a>
						</div>
					</div>
				</div>
				<div role="tabpanel" class="tab-pane" id="momentpath-invoices">
					{{> _orgInvoices}}
				</div>
			</div>
		</div>
	</div>
	{{else if isTrialCustomer}}
	<div class="box-body plans-and-billing">
		<div class="row ">
			<div class="col-sm-6">
				<h1>Choose the Right Plan For You</h1>
				<h4>You currently have {{trialDaysRemaining}} day(s) left in your trial.</h4>
			</div>
			<div class="col-sm-6 box-rounded-white" id="box-child-count">
				<table>
					<tr>
						<td style="vertical-align: middle;">
							<b>How many children are in your care?</b><br/>
							<br/>
							<p>Select the maximum number of children you expect to be active in MomentPath. Family members, staff, and deactivated customers do not count towards your total.</p>
						</td>
						<td style="vertical-align: middle;">
							<input type="number"  name='seat-count' class="form-control" value="0" min="0" step="1" style="text-align:center;display:inline;width:120px;font-size:36px;height:56px;font-weight:bold;background-color:#efefef;border:none;border-radius:8px">
						</td>
						<td style="vertical-align: middle;">
							<img src='/img/susan.png'>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12 text-center">
				<div class="box-rounded-white" style="width:300px;margin:auto;margin-top:18px;margin-bottom:18px">
					
					<b>Billing frequency</b><br/>
					
					<div class="btn-group" id="plan-frequency" role="group" aria-label="...">
						<button type="button" class="btn btn-default {{activeIfEq 'monthly' currentTerm}}" data-id="monthly">Monthly</button>
						<button type="button" class="btn btn-default {{activeIfEq 'annual' currentTerm}}" data-id="annual">Annual</button>
					</div>
					<br/>
					Annual plans receive a 10% discount.<br/>
				</div>
				
				<div class="adv-plan-boxes plans-table">
					<div class="plans-table-row">
						
						<div class="adv-plan-box box-rounded-white">
							<div class="offer-flag-container">
								<div class="offer-flag">Limited Time Offer</div>
							</div>
							<h3>Recovery Bundle</h3>
							<p>The best parts of the MomentPath platform at a friendly price for your single location business.</p>
							<div class="plan-features-block">
								<b>Key features:</b><br/>
								<ul class="plan-key-features">
									<li> Activity Tracking</li>
									<li> Real-time Sharing</li>
									<li> Planning Calendar</li>
								</ul>
							</div>
							<div class="plan-select-box">
								<b><span class="plan-price-label">$249</span> per year (~ 85% discount)</b><br/>
								{{#if planSelected 'recovery-suite'}}
								<button class="btn btn-outline-primary disabled">
									Recovery Selected
								</button>
								{{else}}
								<button class="btn btn-primary" data-id="recovery-suite">
									Select Recovery 
								</button>
								{{/if}}
							</div>
						</div>
						<div class="adv-plan-box box-rounded-white">
							<div class="offer-flag-container"></div>
							<h3>Standard</h3>
							<p>The key features you need to connect with your customers, manage your business, and respond to the changing environment.</p>
							<div class="plan-features-block">
								<b>Key features:</b>
								<ul class="plan-key-features">
									<li> Activity Tracking</li>
									<li> Real-time Sharing</li>
									<li> Planning Calendar</li>
									<li> Online Registration</li>
									<li> Portfolio Generator</li>
								</ul>
							</div>
							<div class="plan-select-box">
								<b><span class="plan-price-label">{{priceFor "core"}}</span> per {{termLabel}} </b><br/>
								{{#if planSelected 'core'}}
								<button class="btn btn-outline-primary disabled" >
									Standard Selected
								</button>
								{{else}}
								<button class="btn btn-primary" data-id="core">
									Select Standard
								</button>
								{{/if}}
							</div>
						</div>
						<div class="adv-plan-box box-rounded-white">
							<div class="offer-flag-container"></div>
							<h3>Pro</h3>
							<p>Improve your customer experience, collections, and retention with advanced tools like billing/payments and multi-location support.</p>
							<div class="plan-features-block">
								<b>All features in Standard, plus:</b>
								<ul class="plan-key-features">
									<li> Automated Invoicing</li>
									<li> Collect Payments</li>
									<li> Planning Calendar</li>
									<li> Sponsor Payment Reconciliation</li>
								</ul>
							</div>
							<div class="plan-select-box">
								<b><span class="plan-price-label">{{priceFor "advanced"}}</span> per {{termLabel}} </b><br/>
								{{#if planSelected 'advanced'}}
								<button class="btn btn-outline-primary disabled" >
									Pro Selected
								</button>
								{{else}}
								<button class="btn btn-primary" data-id="advanced">
									Select Pro
								</button>
								{{/if}}
							</div>
						</div>
						<div class="adv-plan-box box-rounded-white">
							<div class="offer-flag-container"></div>
							<h3>Enterprise</h3>
							<p>Partner with the leading platform in early childhood education to build your unique and superior digital presence.</p>
							<div class="plan-features-block">
								<b>All features in Pro, plus:</b>
								<ul class="plan-key-features">
									<li> White-labeled App</li>
									<li> Multi-site Reporting</li>
									<li> Advanced Integrations</li>
									<li> Data Warehouse</li>
								</ul>
							</div>	
							<div class="plan-select-box">
								<b><span class="plan-price-label">Custom</span></b><br/>
								<button class="btn btn-primary" id="btn-contact-us">Contact Us</button>
							</div>
						</div>
					</div>
				</div>
				<br/>
				<div class="box-rounded-white" style="width:420px;margin:auto;margin-top:18px;margin-bottom:18px">
					{{#if paymentMethodDescription}}
					<b>Payment method selected</b><br/>
					{{paymentMethodDescription}}<br/>
					<a href="#" id="btn-replace-payment-method">replace payment method</a><br/>
					{{else}}
					<b>Choose a Payment Method</b><br/>
					<button type="button" class="btn btn-lg btn-primary" id="btn-add-payment-method"> Add Payment Method</button><br>
					{{/if}}
				</div>

				<div class="box-rounded-white" style="width:420px;margin:auto;margin-top:18px;margin-bottom:18px">
					{{#if readyToActivate}}
					<br/>
					<b>Amount you will be charged today:</b> {{formatCurrency amountDue}}<br/>
					
					<br/>
					<div>
						<button type="button" class="btn btn-lg btn-primary" id="btn-activate-subscription"> Activate Subscription</button>
					</div>

					{{else}}
						Please make sure to select the number of children in your care and the type of plan you would like in order to proceed with activation.<br/>
					{{/if}}
				</div>
			</div>
		</div>
	</div>
	{{/if}}
	

</template>

<template name="orgPlansPayment">
	<div class="row">
		<div class="col-xs-12">
			Choose a payment type:<br/>
			<input type="radio" name="payment-type" value="card"> Credit Card 
			<input type="radio" name="payment-type" value="bank-account"> Bank Account
		</div>
	</div>
	{{#if selectedCreditCard}}
		{{> orgPlansPaymentCreditCard}}
	{{else if selectedBankAccount}}
		{{> orgPlansPaymentBankAccount}}
	{{/if}}
</template>

<template name="orgPlansPaymentCreditCard">
	{{> billingPaymentCreditCard providerAdyen=true}}
</template>

<template name="orgPlansPaymentBankAccount">
	{{> billingPaymentBankAccount providerAdyen=true}}
</template>
