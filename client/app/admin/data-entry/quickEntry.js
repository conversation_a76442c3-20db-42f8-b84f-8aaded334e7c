import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './quickEntry.html';
import { AVAILABLE_FAMILY_MEMBERS_DATA } from "../../../../lib/constants/billingConstants";
import { getPeopleData } from "../../../services/peopleMeteorService";
import { Relationships } from '../../../../lib/collections/relationships';

Template.quickEntry.onCreated( function() {
	var self=this;
	self.activeEntryType = new ReactiveVar();
	self.recentEntries = new ReactiveVar([])
});

Template.quickEntry.helpers({
	"activeEntryType"() {
		return Template.instance().activeEntryType.get();
	},
	"recentEntries"() {
		const recents = Template.instance().recentEntries.get();
		return recents.length > 0 ? recents : null;
	},
	"data"() {
		return {
			parentEntryType: Template.instance().activeEntryType,
			recentEntries: Template.instance().recentEntries
		}
	}
});

Template.quickEntry.events({
	"click #btnAddSecurityDeposit"(e, i) {
		i.activeEntryType.set("quickEntrySecurityDeposit");
	}
});

Template.quickEntrySecurityDeposit.onCreated( function () {
	var self=this;
	self.selectedPersonId = new ReactiveVar();
	self.relationshipsPersonIds = new ReactiveVar([]);
	self.availablePeople = new ReactiveVar([]);
	getPeopleData({type:"person", inActive:{$ne:true}},{sort:{lastName:1, firstName:1}}).then(res => {
		if (res?.length) {
			self.availablePeople.set(res);
		}
	}).catch(err => {
		console.log(err);
	});

	self.autorun(() => {
		if (self.relationshipsPersonIds.get()?.length) {
			getPeopleData({type:"family", _id:{"$in": self.relationshipsPersonIds.get()}}).then(res => {
				Session.set(AVAILABLE_FAMILY_MEMBERS_DATA, res);
			}).catch(err => {
				console.log(err);
			});
		}
	});
});

Template.quickEntrySecurityDeposit.helpers({
	"availablePeople"() {
		return Template.instance().availablePeople.get();
	},
	"availableFamilyMembers"() {
		const selectedPersonId = Template.instance().selectedPersonId.get(),
			relationships = selectedPersonId && Relationships.find({targetId: selectedPersonId, relationshipType: "family"}).fetch();
		console.log("selec", selectedPersonId, "relations", relationships);
		if (relationships?.length) {
			Template.instance().relationshipsPersonIds.set(relationships.map(r => r.personId));
			return Session.get(AVAILABLE_FAMILY_MEMBERS_DATA);
		}
		return;
	}
});

Template.quickEntrySecurityDeposit.events({
	"change #selectedPerson"(e, i) {
		const selectedPersonId = $("#selectedPerson").val();
		i.selectedPersonId.set(selectedPersonId);
	},
	"click #btnCancel"(e, i) {
		e.preventDefault();
		i.data.parentEntryType.set(null);
	},
	"click #btnAdd"(e,i) {
		e.preventDefault();
		const selectedPersonId = i.selectedPersonId.get(),
			selectedFamilyId = $("#selectedFamily").val(),
			amount = $("input[name=amount]").val(),
			parsedAmount = amount && parseFloat(amount);
		
		if (!selectedPersonId || !selectedFamilyId)
			return mpSwal.fire("Error", "Please make sure to pick a person and a family member.", "error");
		if (isNaN(parsedAmount))
			return mpSwal.fire("Error", "Please make sure to choose a valid amount.", "error");

		$("#btnAdd").text("Adding...").prop("disabled", true);
		Meteor.callAsync("quickEntryInsertSecurityDeposit", {selectedPersonId, selectedFamilyId, amount: parsedAmount}).then(result => {
			const recentEntries = i.data.recentEntries.get();
			recentEntries.push(result);
			i.data.recentEntries.set(recentEntries);
			i.data.parentEntryType.set(null);
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
			$("#btnAdd").text("Add Entry").prop("disabled", false);
		});
		
	}
});

Template.quickEntrySecurityDeposit.onDestroyed(function() {
	delete Session.keys[AVAILABLE_FAMILY_MEMBERS_DATA];
});