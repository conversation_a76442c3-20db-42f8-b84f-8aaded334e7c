<template name="paymentTypeImporter">
	<div class="container">

	  	<div class="flex-row-fluid ml-lg-8">
			<div class="card">
				<div class="card-header">
					<h1>Payment Type Import</h1>
				</div>
				<div class="card-body">
					
					<div class="row">
						<div class="col-12">
							
							<div class="row">
								<div class="form-group col-lg-4">
									<label  for="fileInput">Select file</label>
									<input class="form-control" id="fileInput" type="file" name="files[]">	
								</div>
							</div>

							<div class="row">
								<div class="form-group col-lg-4">
									<label for="import-mode">Mode:</label>
									<select class="form-control" name="import-mode" id="import-mode">
										<option value="preview" selected="selected">Preview - validates data before processing</option>
										<option value="live">Live - processes data</option>
									</select>
								</div>
							</div>
							
							<button class="btn btn-primary" id="btnUpload">Upload</button><br/>
							
							{{#if results}}
							<br/>
							<div class="card">
								<div class="card-header"><h6>Results:</h6></div>
								<div class="card-body" style="white-space: pre-wrap;">{{results}}</div>
							</div>
							{{/if}}
						</div>
					</div>
				</div>
				
			</div>
		</div>


	</div>
</template>