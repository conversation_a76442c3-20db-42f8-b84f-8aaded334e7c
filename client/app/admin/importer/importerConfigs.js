import { ImporterDocumentService } from './services/importerDocumentService';
import { importerTypes } from '../../../../lib/constants/importers/importerConstants';

/**
 * An array of importer configuration objects used to define and register
 * each available data importer within the application.
 *
 * Each configuration includes metadata and handler functions that control:
 * - The display title and description shown in the importer modal UI
 * - The action to download a CSV template for the importer
 * - The action to trigger the upload flow, including context-aware options
 *
 * This configuration enables the UI to dynamically render importer cards
 * and decouple importer-specific logic from the UI layer.
 *
 * @type {Array<{
 *   id: string,
 *   title: string,
 *   description: string,
 *   downloadTemplate: function(): void,
 *   uploadCSV: function(options: object): void
 * }>}
 *
 * @example
 * import { importerConfigs } from './importerConfigs';
 * const orgImporter = importerConfigs.find(cfg => cfg.id === 'org-importer');
 * orgImporter.downloadTemplate(); // triggers CSV download
 * orgImporter.uploadCSV({ rootOrg }); // opens upload modal with org context
 */
export const importerConfigs = [
    {
        id: importerTypes.ORG,
        title: 'Site Importer',
        description: 'Create new sites / organizations via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/org-import-template.csv',
            filename: 'Org Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Site Importer',
            importerId: importerTypes.ORG,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.BILLING_PLANS,
        title: 'Billing Plans Importer',
        description: 'Create new Billing Plan via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/billing-plan-import-template.csv',
            filename: 'Billing Plan Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Billing Plans Importer',
            importerId: importerTypes.BILLING_PLANS,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.INVOICES,
        title: 'Open Invoices Importer',
        description: 'Import data about existing open invoices via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/open-invoice-import-template.csv',
            filename: 'Open Invoice Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Open Invoice Importer',
            importerId: importerTypes.INVOICES,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.DISCOUNT,
        title: 'Discount Type Importer',
        description: 'Create new discount types via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/discounts-import-template.csv',
            filename: 'Discounts Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Discount Types Importer',
            importerId: importerTypes.DISCOUNT,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.DISTRICT_FUNDED,
        title: 'District-Funded Importer',
        description: 'Import district-funded student data via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/district-funded-import-template.csv',
            filename: 'District Funded Import.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'District Funded Importer',
            importerId: importerTypes.DISTRICT_FUNDED,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.PROGRAM,
        title: 'Programs Importer',
        description: 'Create new programs via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/program-import-template.csv',
            filename: 'Program Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Program Importer',
            importerId: importerTypes.PROGRAM,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.STAFF_AND_ADMIN,
        title: 'Staff and Admin Importer',
        description: 'Create new Staff and Admin via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/staff-and-admin-import-template.csv',
            filename: 'Staff and Admin Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Staff and Admin Importer',
            importerId: importerTypes.STAFF_AND_ADMIN,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.SCHEDULE_TYPE,
        title: 'Schedule Type Importer',
        description: 'Create new schedule types via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/scheduleType-importer-template.csv',
            filename: 'Schedule Type Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Schedule Type Importer',
            importerId: importerTypes.SCHEDULE_TYPE,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.SUBSIDY,
        title: 'Subsidy Type Importer',
        description: 'Create new Subsidy Types via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/subsidy-import-template.csv',
            filename: 'subsidy import template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Subsidy Type Importer',
            importerId: importerTypes.SUBSIDY,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.TIME_PERIOD,
        title: 'Time Periods Importer',
        description: 'Create new Time Periods via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/timePeriod-import-template.csv',
            filename: 'time period import template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Time Periods Importer',
            importerId: importerTypes.TIME_PERIOD,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.GSI,
        title: 'Grades Served Importer',
        description: 'Create Grades Served via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/grades-served-import-template.csv',
            filename: 'Grades Served Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Grades Served Importer',
            importerId: importerTypes.GSI,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.ITEMS,
        title: 'Item Type Importer',
        description: 'Create new Item types via CSV upload',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/items-import-template.csv',
            filename: 'Items Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Item Type Importer',
            importerId: importerTypes.ITEMS,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        })
    },
    {
        id: importerTypes.FAMILY,
        title: 'Family Importer',
        description: 'Import existing Family and Child data (including schedules, etc.) via multiple CSV uploads',
        downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
            path: '/templates/family-import-template.csv',
            filename: 'Family Import Template.csv'
        }),
        uploadCSV: (options) => ImporterDocumentService.showUploadModal({
            importerTitle: 'Family Importer',
            importerId: importerTypes.FAMILY,
            rootOrg: options?.rootOrg,
            allOrgs: options?.allOrgs
        }),
    },
    // Add more configs here
    ];