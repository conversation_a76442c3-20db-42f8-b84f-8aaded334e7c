<template name="orgHeader">
  <ul class="menu-nav">
    <li id="orgSettings" class="menu-item {{#if routeContainsHashLocal 'orgSettings'}}menu-item-active{{/if}}" aria-haspopup="true">
      <a href="/admin/org#orgSettings" class="menu-link">
          <span data-cy="configuration-tab" class="menu-text">Configuration</span>
      </a>
    </li>
    {{#if showPlansAndBilling}}
      <li id="billingSettings" class="menu-item {{#if routeContainsHashLocal 'billingSettings'}}menu-item-active{{/if}}" aria-haspopup="true">
        <a href="/admin/org#billingSettings" class="menu-link">
          <span class="menu-text">Plans &amp; Billing</span>
        </a>
      </li>
    {{/if}}
    {{#if showIntegrations}}
      <li id="/integrations" class="menu-item {{#if routeContainsHashLocal '/integrations'}}menu-item-active{{/if}}" aria-haspopup="true">
        <a href="/admin/integrations" class="menu-link">
          <span class="menu-text">Integrations</span>
        </a>
      </li>
    {{/if}}
    {{#if showOrgInformation}}
      <li id="orgInformation" class="menu-item {{#if routeContainsHashLocal 'orgInformation'}}menu-item-active{{/if}}" aria-haspopup="true">
        <a href="/admin/org#orgInformation" class="menu-link">
          <span data-cy="org-info-tab" class="menu-text">Organization Info</span>
        </a>
      </li>
    {{/if}}
  </ul>
</template>
