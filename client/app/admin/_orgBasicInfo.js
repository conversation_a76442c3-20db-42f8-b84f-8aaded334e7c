import { ReactiveDict } from 'meteor/reactive-dict';
import { AsYouType } from 'libphonenumber-js';
import { AvailableStatesAndProvinces } from '../../../lib/constants/orgConstants';
import { AvailableTimezoneMap } from '../../../lib/constants/timezoneConstants';
import './_orgBasicInfo.html';
import { Orgs } from '../../../lib/collections/orgs';
import $ from 'jquery';

const timezones = {
    "America/New_York": "Eastern Time",
    "America/Chicago": "Central Time",
    "America/Denver": "Mountain Time",
    "America/Los_Angeles": "Pacific Time",
    "America/Phoenix": "Mountain Time (Arizona)",
    "Pacific/Honolulu": "Hawaii-Aleutian Time"
};

const customClass = {
    input: 'org-info-input',
    validationMessage: 'swal2-invalid-feedback',
    cancelButton: 'btn btn-secondary font-weight-bolder',
    confirmButton: 'btn btn-primary font-weight-bolder'
};

Template.orgBasicInfo.onCreated(function () {
    this.isOrgCreation = this.data.isOrgCreation ?? false;
    this.showReportsTo = this.data.showReportsTo ?? false;
    this.sessionDataName = this.data.sessionDataName ?? false;
    this.orgData = new ReactiveDict({
        orgId: this.data._id,
        name: this.data.name,
        reportsTo: this.data.reportsTo, // Not used with master org.
        enableSwitchOrg: this.data.enableSwitchOrg,
        shortCode: this.data.shortCode,
        language: this.data.language,
        timezone: this.data.timezone,
        legalFacilityName: this.data.legalFacilityName,
        facilityLicenseNumber: this.data.facilityLicenseNumber,
        phoneNumber: this.data.phoneNumber,
        schoolHighGrade: this.data.schoolHighGrade,
        schoolLowGrade: this.data.schoolLowGrade,
        streetAddress: this.data.streetAddress,
        cityName: this.data.cityName,
        stateName: this.data.stateName,
        zipCode: this.data.zipCode,
        websiteUrl: this.data.websiteUrl,
        tagUpdateTrigger: 0,
        orgAvailableTags: this.data.orgAvailableTags,
        additionalFields: this.isOrgCreation ? { orgTags: [], countyName: '', siteNumber: '', regionNumber: '', areaNumber: '' } : this.data.additionalFields
    });
    this.availableParentOrgTags = new ReactiveVar([]);

    this.hasDreambox = new ReactiveVar(false);
    Meteor.callAsync('topHasDreambox', Orgs.current()._id).then(result => {
        this.hasDreambox.set(true);
    });

    this.fetchAvailableParentOrgTags = async () => {
        try {
            const orgId = this.isOrgCreation ? Meteor.user().fetchPerson().orgId : this.data._id;
            const tags = await Meteor.callAsync('getAvailableTagsFromParentOrg', orgId);
            this.availableParentOrgTags.set(tags);
        } catch (error) {
            console.error('Error fetching parent organization tags:', error);
        }
    };

    this.fetchAvailableParentOrgTags();

})

Template.orgBasicInfo.helpers({
    "orgTagDisplay"() {
        const instance = Template.instance();
        const data = instance.data;
        instance.orgData.get('tagUpdateTrigger');
        const allOrgTags = instance.availableParentOrgTags.get();

        const tagIds = this.isOrgCreation
          ? (instance.orgData.get('additionalFields')?.orgTags ?? [])
          : (data.additionalFields?.orgTags ?? []);

        const tagNames = tagIds.map(tagId => {
            const tagObj = allOrgTags.find(tag => tag._id === tagId);
            return tagObj ? tagObj.name : 'Missing Tag Name';
        });

        return tagNames.join(', ');
    },
    "orgData"() {
        // This is just to help pass the org data back to the parent template inside the formData object.
        const orgData = Template.instance().orgData.all();
        return JSON.stringify(orgData);
    },
    "orgId"() {
      return Template.instance().orgData.get("orgId");
    },
    "orgName"() {
        return Template.instance().orgData.get("name");
    },
    "reportsTo"() {
      return Template.instance().orgData.get("reportsTo");
    },
    "isMasterOrg"() {
      return Template.instance().orgData.get("enableSwitchOrg") === true ? "Yes" : "No";
    },
    "hasDreambox"() {
        return Template.instance().hasDreambox.get();
    },
    "industry"() {
        switch (Template.instance().orgData.get("language")) {
            case "translationsEnAdultCare":
                return "Adult / Senior";
            default:
                return "Child Care / Schools";
        }
    },
    "timezoneLabel"() {
        return AvailableTimezoneMap[Template.instance().orgData.get("timezone")];
    },
    "shortCodeLabel"() {
        return Template.instance().orgData.get("shortCode");
    },
    "legalFacilityName"() {
        return Template.instance().orgData.get("legalFacilityName");
    },
    "facilityLicenseNumber"() {
        return Template.instance().orgData.get("facilityLicenseNumber");
    },
    "phoneNumber"() {
        return Template.instance().orgData.get("phoneNumber");
    },
    "schoolHighGrade"() {
        return Template.instance().orgData.get("schoolHighGrade");
    },
    "schoolLowGrade"() {
        return Template.instance().orgData.get("schoolLowGrade");
    },
    "streetAddress"() {
        return Template.instance().orgData.get("streetAddress");
    },
    "cityName"() {
        return Template.instance().orgData.get("cityName");
    },
    "stateName"() {
        return Template.instance().orgData.get("stateName");
    },
    "zipCode"() {
        return Template.instance().orgData.get("zipCode");
    },
    "countyName"() {
        return Template.instance().orgData.get("additionalFields").countyName;
    },
    "websiteUrl"() {
        return Template.instance().orgData.get("websiteUrl");
    },
    "isSuperAdmin"() {
        const userPerson = Meteor.user().fetchPerson();
        return userPerson?.superAdmin;
    },
    "siteNumber"() {
        return Template.instance().orgData.get("additionalFields").siteNumber;
    },
    "regionNumber"() {
        return Template.instance().orgData.get("additionalFields").regionNumber;
    },
    "areaNumber"() {
        return Template.instance().orgData.get("additionalFields").areaNumber;
    },

    showReportsTo() {
        return Template.instance().showReportsTo;
    },
    isOrgCreation() {
        return Template.instance().isOrgCreation;
    },
})

Template.orgBasicInfo.events({
    'click .btn-edit-property'(event, instance) {
        event.preventDefault();
        const property = $(event.currentTarget).data("prop");
        if (property === 'orgTags') {
            mpSwal.fire({
                title: 'Loading tags...',
                allowOutsideClick: false,
                didOpen: () => {
                    mpSwal.showLoading();
                }
            });

            instance.fetchAvailableParentOrgTags()
              .then(() => {
                  mpSwal.close();
                  const options = getOptionsFromProp(property, instance);
                  mpSwal.fire(options).then(handleResult);
              })
              .catch(error => {
                  console.error('Error loading tags:', error);
                  mpSwal.fire({
                      title: 'Error',
                      text: 'Could not load organization tags',
                      icon: 'error'
                  });
              });
        } else {
            const options = getOptionsFromProp(property, instance);
            mpSwal.fire(options).then(handleResult);
        }

        function handleResult(result) {
            if (result.isConfirmed) {
                let area = property;
                let value = result.value;

                if (property === 'orgTags') {
                    const availableTags = instance.availableParentOrgTags.get() || [];
                    value = availableTags
                      .filter(tag => result.value.includes(tag._id))
                      .map(tag => tag._id);

                    area = 'additionalFields.orgTags';
                }
                if (property === 'siteNumber' || property === 'regionNumber' || property === 'areaNumber' || property === 'countyName') {
                    area = `additionalFields.${property}`;
                    value = result.value;
                }

                // reportsTo isn't actually a prop on the Org document but parentOrgId is.
                if (property === 'reportsTo') {
                    area = 'parentOrgId';
                    const selectedOrg = instance.data.availableOrgsToReportTo.find(org => org.name === result.value);
                    value = selectedOrg._id;
                }

                if (property === 'enableSwitchOrg') {
                    value = result.value === 'true';
                    result.value = value;
                }

                if (property === 'websiteUrl') {
                    // Add https:// if one doesn't exist
                    value = addHttpToWebsiteUrl(result.value);
                    result.value = value;
                }

                if (property === 'stateName') {
                    // upper case State/Province
                    value = result.value.toUpperCase();
                    result.value = value;
                }

                // Only edit the property on the backend if it's not a new org creation.
                if (!instance.isOrgCreation && instance.data._id) {
                    Meteor.callAsync('changeCustomerProperty', instance.data._id, {area, value}).then(res => {
                        mpSwal.fire({
                            title: "Success",
                            text: "Your changes have been saved",
                            icon: "success"
                        })
                        updatePropertyValue(instance, property, value, result);

                        if (instance.sessionDataName) {
                            Session.set(instance.sessionDataName, instance.orgData.all());
                        }
                    }).catch(err => {
                        mpSwal.fire({
                            title: "Error",
                            text: err.message || err.reason,
                            icon: "error"
                        });
                    });
                } else {
                    updatePropertyValue(instance, property, value, result);

                    if (instance.sessionDataName) {
                        Session.set(instance.sessionDataName, instance.orgData.all());
                    }
                }
            }
        }
    },
    'click #superAdminLink'(event, instance) {
        const orgId = instance.data._id;
        const currentOrg = Orgs.current();
        event.preventDefault();
        if (currentOrg._id === orgId) {
            window.open(`/superadmin/customers/${ orgId }`, '_blank');
        } else {
            Meteor.callAsync('adminSwitchOrg', orgId).then(result => {
                window.open(`/superadmin/customers/${ orgId }`, '_blank');
                location.reload();
            }).catch(error => {
                mpSwal.fire('Error', error.reason, 'error');
            });
        }

    }
})

function getOptionsFromProp(prop, instance) {
    const isParentOrg = instance.showReportsTo === false;
    const isOrgCreation = instance.isOrgCreation;
    const orgId = isOrgCreation ? null : instance.data._id;
    const value = instance.orgData.get(prop) ?? "";
    const selectableOrgs = instance.data.availableOrgsToReportTo?.map(org => org.name);
    switch (prop) {
        case "name":
            return {
                title: "Edit Organization Name",
                text: "Please enter the name you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            };
        case "reportsTo":
            return {
                title: "Select organization to report to",
                html: '<select id="organizationSelect" class="swal2-select"></select>',
                preConfirm: () => {
                    return document.getElementById('organizationSelect').value;
                },
                didOpen: () => {
                    // Initialize Select2 on the dropdown -- this will allow searchable select dropdown. Swal2 does not support this natively.
                    $('#organizationSelect').select2({
                        data: selectableOrgs,
                        placeholder: 'Search for an organization',
                        width: '100%',
                    });
                },
                inputValue: value,
                showCancelButton: true,
            };
        case "enableSwitchOrg":
            return {
                title: "Enable Master Org",
                text: "Would you like to enable the ability to select this org as a parent org?",
                input: "select",
                inputValue: value ? "true" : "false",
                inputOptions: { true: "Yes", false: "No" },
                showCancelButton: true,
            };
        case "shortCode":
            return {
                title: "Edit Site/School Code",
                text: "Please enter the short code of your site, It must be less than 31 characters",
                input: "text",
                inputValue: value,
                inputAttributes: {
                    maxlength: "30"
                },
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "orgTags":
            const availableTags = instance.availableParentOrgTags.get() || [];
            const noTagsWarning = availableTags.length === 0 ?
              `<div class="text-warning mt-3">
                    <i class="fa fa-info-circle"></i>
                    No available tags found from parent organization.
                </div>` : '';

                    return {
                        title: "Organization Tag",
                        html: `
                    <div class="mb-3">Select tag(s) to add to this organization</div>
                    <select id="orgTagsSelect" class="swal2-select" multiple="multiple"></select>
                    ${noTagsWarning}
                `,
                preConfirm: () => $('#orgTagsSelect').val(),
                didOpen: () => {
                    if (availableTags.length > 0) {
                        const currentTagIds = instance.isOrgCreation
                          ? (instance.orgData.get('additionalFields')?.orgTags ?? [])
                          : (instance.data.additionalFields?.orgTags || []);

                        // Filter the tags to only show non-archived OR selected tags
                        const tagsToDisplay = availableTags.filter(tag =>
                          !tag.archived || currentTagIds.includes(tag._id)
                        );

                        $('#orgTagsSelect').select2({
                            data: tagsToDisplay.map(tag => ({
                                id: tag._id,
                                text: tag.name
                            })),
                            placeholder: 'Select one or more tags',
                            width: '100%',
                            multiple: true
                        });

                        if (currentTagIds.length > 0) {
                            $('#orgTagsSelect').val(currentTagIds).trigger('change');
                        }
                    }
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "timezone":
            return {
                title: "Select organization timezone",
                text: "Please select the timezone for your organization.  Please note that all times will reflect the chosen timezone.",
                input: "select",
                inputOptions: AvailableTimezoneMap,
                inputValue: value,
                showCancelButton: true,
            }
        case "legalFacilityName":
            return {
                title: "Edit Organization Legal Facility Name",
                text: "Please enter the Legal Facility Name you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "phoneNumber":
            return {
                title: "Edit Phone Number",
                text: "Please enter the phone number for your organization",
                input: "text",
                didOpen: () => {
                    const phoneInput = document.querySelector('.org-info-input');
                    phoneInput.value = value;
                    phoneInput.addEventListener('input', (e) => {
                        const phone = new AsYouType('US');
                        phone.input(e.target.value);
                        e.target.value = phone.getNumber() ? phone.getNumber().formatNational() : '';
                    });
                },
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "schoolLowGrade":
            return {
                title: "Edit School Low Grade",
                text: "Please enter the Low Grade for your organization (if applicable)",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "schoolHighGrade":
            return {
                title: "Edit School High Grade",
                text: "Please enter the High Grade for your organization (if applicable)",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "facilityLicenseNumber":
            return {
                title: "Edit Organization Facility License Number",
                text: "Please enter the Facility License Number you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "streetAddress":
            return {
                title: "Edit Organization Street Address",
                text: "Please enter the Street Address you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "cityName":
            return {
                title: "Edit Organization City Name",
                text: "Please enter the City Name you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "stateName":
            return {
                title: "Edit Organization State Name",
                text: "Please enter the State Name you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputAttributes: {
                    maxlength: "2"
                },
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        case "zipCode":
            return {
                title: "Edit Organization Zip Code",
                text: "Please enter the Zip Code you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputAttributes: {
                    maxlength: "9"
                },
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                buttonsStyling: false,
                showCancelButton: true,
            };
        case "countyName":
            return {
                title: "Edit County",
                text: "Please enter a county for this site",
                input: "text",
                inputValue: value,
                customClass: customClass,
                showCancelButton: true,
            };
        case "siteNumber":
            return {
                title: "Edit Site Number",
                text: "Please enter a site number for this site",
                input: "text",
                inputValue: value,
                customClass: customClass,
                showCancelButton: true,
            };

        case "regionNumber":
            return {
                title: "Edit Region Number",
                text: "Please enter a region number (if applicable to this site)",
                input: "text",
                inputValue: value,
                customClass: customClass,
                showCancelButton: true,
            };

        case "areaNumber":
            return {
                title: "Edit Area Number",
                text: "Please enter an area number (if applicable to this site)",
                input: "text",
                inputValue: value,
                customClass: customClass,
                showCancelButton: true,
            };

        case "websiteUrl":
            return {
                title: "Edit Organization Website URL",
                text: "Please enter the Website URL you would like to use for your organization",
                input: "text",
                inputValue: value,
                inputValidator: (inputValue) => {
                    return new Promise(async (resolve) => {
                        const validation = await validateOrgData(prop, inputValue, isOrgCreation, isParentOrg, orgId);
                        if (!validation.valid) {
                            resolve(validation.message);
                        } else {
                            resolve();
                        }
                    });
                },
                customClass: customClass,
                showCancelButton: true,
            }
        default:
            return "unknown property"
    }
}

async function validateOrgData(prop, value, isOrgCreation, isParentOrg, orgId) {
    // ReportsTo and Timezone are (almost) always required but are never empty values, so we don't check them here. We also don't check the enableSwitchOrg property because it's a boolean.
    let response = {
        valid: true,
        message: ""
    };

    switch (prop) {
        case "name": // Required and can't be the same as another org.
            if (!value.length > 0) {
                response.valid = false;
                response.message = "Organization name cannot be blank";
            } else {
                const exists = await new Promise((resolve, reject) => {
                    Meteor.callAsync('orgNameExists', value, orgId).then(res => {
                        resolve(res);
                    }).catch(err => {
                        reject(err);
                    });
                });

                if (exists) {
                    response.valid = false;
                    response.message = "This name is already in use. Please pick another.";
                }
            }
            break;
        case "shortCode": // Can be blank, but can't be longer than 30 characters.
            if (value.length > 0 && value.length > 30) {
                response.valid = false;
                response.message = "Short code cannot be longer than 30 characters";
            }
            break;
        case "legalFacilityName":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "Legal facility name cannot be blank";
            }
            break;
        case "phoneNumber":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "Phone number cannot be blank";
            } else if (value.length > 0) {
                // Validate phone number
                const asYouType = new AsYouType('US');
                asYouType.input(value);
                if (!asYouType.getNumber() || !asYouType.getNumber().isPossible()) {
                    response.valid = false;
                    response.message = "Invalid phone number";
                }
            }
            break;
        case "schoolLowGrade":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "School low grade cannot be blank";
            }
            break;
        case "schoolHighGrade":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "School high grade cannot be blank";
            }
            break;
        case "facilityLicenseNumber":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "Facility license number cannot be blank";
            }
            break;
        case "streetAddress":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "Street address cannot be blank";
            }
            break;
        case "cityName":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "City name cannot be blank";
            }
            break;
        case "stateName":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "State name cannot be blank";
            } else if (value.length > 0 && !AvailableStatesAndProvinces.includes(value.toUpperCase())) {
                // Has to be valid state/province
                response.valid = false;
                response.message = "This is not a valid state/province. Please enter another.";
            }
            break;
        case "zipCode":
            if (!value.length > 0 && isParentOrg) {
                // Parent org cannot be blank
                response.valid = false;
                response.message = "Zip code cannot be blank";
            }
            break;
        case "websiteUrl": // Can be blank, but cannot be invalid.
            if (value.length > 0) {
                // Validate website URL
                const urlRegex = /^(https?:\/\/)?([\w\d.-]+)\.([a-z]{2,})(:[0-9]{1,5})?([\w\d/_.-]*)*$/i;
                if (!urlRegex.test(value)) {
                    response.valid = false;
                    response.message = "This is not a valid URL. Please enter a valid web site.";
                }
            }
            break;
        default:
            response.valid = false;
            response.message = "Unknown property";
    }

    return response;
}

function addHttpToWebsiteUrl(url) {
    if (url.length > 0 && (!url.startsWith('http') && !url.startsWith('https'))) {
        return `https://${url}`;
    }
    return url;
}

function updatePropertyValue(instance, property, value, result) {
    const updateNestedProperty = (propPath, val) => {
        if (propPath.includes('.')) {
            const [parent, child] = propPath.split('.');
            const parentObj = instance.orgData.get(parent) || {};
            parentObj[child] = val;
            instance.orgData.set(parent, parentObj);

            // Update the instance data structure if it exists
            if (instance.data && instance.data[parent]) {
                instance.data[parent] = instance.data[parent] || {};
                instance.data[parent][child] = val;
            }
        } else {
            instance.orgData.set(property, result.value);
        }
    };

    // Map properties to their storage paths
    const propertyMap = {
        'orgTags': 'additionalFields.orgTags',
        'countyName': 'additionalFields.countyName',
        'siteNumber': 'additionalFields.siteNumber',
        'regionNumber': 'additionalFields.regionNumber',
        'areaNumber': 'additionalFields.areaNumber'
        // Add other nested properties here as needed
    };

    // Get the correct path for storage (or use the property itself)
    const storagePath = propertyMap[property] || property;

    // Update the value in the appropriate location
    updateNestedProperty(storagePath, value);

    // Always trigger the update trigger for UI refresh
    instance.orgData.set('tagUpdateTrigger', new Date().getTime());
}
