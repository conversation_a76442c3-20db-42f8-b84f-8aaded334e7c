import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from '../main';
import './_documentFormModal.js';
import './documents.html';
import _ from '../../../lib/util/underscore.js';
import { OrgsLib } from '../../../lib/orgsLib.js';

Template.documents.onCreated( function () {
	var self=this;
	self.showArchived = new ReactiveVar();
});

Template.documents.helpers({
	"documents": function() {
		const showArchived = Template.instance().showArchived.get();
		return OrgsLib.getDocumentDefinitions(showArchived);
	},
	"formatTemplateOption": function(opt) {
		switch (opt) {
			case "ack":
				return "Acknowledge Document";
			case "signature":
				return "Signature Required";
			default:
				return ""
		}
	},
	"canAddDocument": function() {
		return processPermissions({
			assertions: [{ context: "documents", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type=="staff"
		});
	}
});

Template.documents.events({
	"click #btnAddDocument": function() {
		showModal("_documentFormModal", {}, "#_documentFormModal");
	},
	"click .btnViewTemplate": function(event) {
		const templateId = $(event.currentTarget).data("id");
		Meteor.callAsync("getDocumentRepositoryLink", { type:"template", templateId}).then(result => {
			window.open(result.url, '_blank');
		}).catch(error => {
			mpSwal.fire("Error retrieving document", error.reason);
		});
	},
	"click .btnEditDocument": function(event) {
		const templateId = $(event.currentTarget).data("id"),
			documentTemplate = _.find(Orgs.current().documentDefinitions, (dd) => { return dd._id == templateId;} );
		
		showModal("_documentFormModal", documentTemplate, "#_documentFormModal");
	},
	"click .btnDeleteDocument": function(event) {
		const templateId = $(event.currentTarget).data("id"),
			documentTemplate = _.find(Orgs.current().documentDefinitions, (dd) => { return dd._id == templateId;} );
		
		mpSwal.fire({
			title: "Are you sure?",
			text: "Archiving this document type will remove it from all profiles.",
			icon: "warning",
			showCancelButton: true,
			closeOnConfirm:false
		}).then(result => {
			if (result.value) {
				Meteor.callAsync("removeDocumentRepository", {documentId: templateId}).then(response => {
					mpSwal.fire("Document removed");
				}).catch(err => {
					mpSwal.fire("Document removed");
				});
			}
		});
	},
	"click #chkArchived": function(event, instance) {
		const status = $(event.currentTarget).prop("checked")
		instance.showArchived.set(status);
	}
});

