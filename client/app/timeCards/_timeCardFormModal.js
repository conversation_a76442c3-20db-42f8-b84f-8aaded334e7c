import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal } from "../main";
import './_timeCardFormModal.html';
import { TimeCardUtils } from "../../../lib/util/timeCardUtils";
import moment from "moment-timezone";
import { getPeopleById } from "../../services/peopleMeteorService";

Template._timeCardFormModal.onCreated(function() {
	this.timeCard = new ReactiveVar({});
    this.otherTimeCards = new ReactiveVar([]);
  this.loaded = new ReactiveVar(false);
  this.totalHours = new ReactiveVar(0.00);
  this.conflict = new ReactiveVar(false);
  this.canSave = new ReactiveVar(false);
  this.formatName = new ReactiveVar("");
  this.autorun(() => {
    const timeCard = this.timeCard.get();
    if (timeCard && timeCard.personId) {
      getPeopleById(timeCard.personId).then(p => {
        if (p) {
          this.formatName.set(`${p.firstName} ${p.lastName}`);
        }
      }).catch(err => {
        console.log(err);
      });
    }
  });
});

Template._timeCardFormModal.events({
  'change .time-input': function(e, i) {
    const card = Template.instance().timeCard.get();
    card.checkInTime = moment($("#checkInTime").val(),["h:mm a","HH:mm"]).format("h:mm a")
    card.checkOutTime = ($("#checkOutTime").val()) ? moment($("#checkOutTime").val(),["h:mm a","HH:mm"]).format("h:mm a") : null;
    if (!card.checkOutDate && card.checkOutTime) {
      card.checkOutDate = card.checkInDate;
    }
    i.totalHours.set(TimeCardUtils.getTimeCardTotalHours(card));
    i.conflict.set(TimeCardUtils.getConflictRange(card, i.otherTimeCards.get()));
    checkStatus(i);
  },
  'change #selectedPayType': function(e, i) {
    checkStatus(i);
  },
	'click #saveTimeCard': function(event) {
		event.preventDefault();
    const checkInTime = moment($("#checkInTime").val(),["h:mm a","HH:mm"]).format("h:mm a")
    const checkOutTime = ($("#checkOutTime").val()) ? moment($("#checkOutTime").val(),["h:mm a","HH:mm"]).format("h:mm a") : null;
    const selectedPayTypeId = $("select[name=selectedPayType]").val();
    const timeCard = Template.instance().timeCard.get();
    $("#saveTimeCard").prop('disabled', true).prop("value","Updating...");
    Meteor.callAsync("modifyTimeCard", { checkInTime, checkOutTime, selectedPayTypeId, timeCardId: timeCard._id }).then(respons => {
      $("#saveTimeCard").prop('disabled', false).prop("value","Save");
      hideModal("#_timeCardFormModal");
    }).catch(error => {
      $("#saveTimeCard").prop('disabled', false).prop("value","Save");
      if (error) return mpSwal.fire("Error", error.reason, "error");
      hideModal("#_timeCardFormModal");
    });
	}
});

Template._timeCardFormModal.rendered = function() {	

	var self = this;
  const timeCardId = Template.instance().data.timeCardId;

  Meteor.callAsync('getTimeCard', { timeCardId }).then(result => {
    self.timeCard.set(result);
    self.totalHours.set(TimeCardUtils.getTimeCardTotalHours(result));
    Meteor.callAsync('getOtherTimeCards', result).then(result => {
      self.otherTimeCards.set(result);
      self.conflict.set(TimeCardUtils.getConflictRange(self.timeCard.get(), self.otherTimeCards.get()));
    });
    self.loaded.set(true);
  });
};

Template._timeCardFormModal.helpers({
  "loaded": function() {
    return Template.instance().loaded.get();
  },
  conflictWarning: function() {
    if (!Template.instance().conflict.get()) return "";
    const conflict = Template.instance().conflict.get();
    if (conflict.noCheckOut && conflict.start.format('MM/DD/YYYY') === moment.tz(Orgs.current().getTimezone()).format('MM/DD/YYYY')) {
      return 'Invalid card: this person has another time card on this date. They checked in at ' +
          conflict.start.format('h:mm a') + ' and have not checked out yet.';
    }
    return 'Invalid card: this person has another time card on this date from ' +
        conflict.start.format('h:mm a') + ' to ' + conflict.end.format('h:mm a');
  },
  "totalHours": function() {
    return Template.instance().totalHours.get().toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
  },
  "disabled": function() {
    return !Template.instance().canSave.get();
  },
	"formatName": function() {
    return Template.instance().formatName.get();
	},
  "formattedCheckInTime": function() {
    const timeCard = Template.instance().timeCard.get();
    var curTime = moment(timeCard.checkInTime, "h:mm a");
    return curTime.format("HH:mm")
  },
  "formattedCheckOutTime": function() {
    const timeCard = Template.instance().timeCard.get();
    if (!timeCard.checkOutTime) return "";
    var curTime = moment(timeCard.checkOutTime, ["h:mm a", "h:mm:ss A"]);
    return curTime.format("HH:mm") 
  },
	"getCheckInTime": function() {
		const timeCard = Template.instance().timeCard.get();
    if (timeCard && timeCard.checkInTime) return timeCard.checkInTime;
    return ""
	},
	"getCheckOutTime": function() {
    const timeCard = Template.instance().timeCard.get();
    if (timeCard && timeCard.checkOutTime) return  moment(timeCard.checkOutTime, ["h:mm a", "h:mm:ss A"]).format("h:mm a");
    return ""
	},
  "getTimeCardDate": function() {
    const timeCard = Template.instance().timeCard.get();
    if (timeCard && timeCard.checkInDate) return timeCard.checkInDate;
    return "";  
  },
  "getTimeCardPayTypeId": function() {
    const timeCard = Template.instance().timeCard.get();
    if (timeCard && timeCard.selectedPayTypeId) return timeCard.selectedPayTypeId;
    return "standard";  
  },
  "availablePayTypes": function() {
    const org = Orgs.current();
    const types = org && org.customStaffPaySettings && org.customStaffPaySettings.types;
    if (!types) return [];
    return types;
  },
});

function checkStatus(i) {
  if (i.conflict.get()) {
    i.canSave.set(false);
    return;
  }
  if (i.totalHours.get() <= 0) {
    i.canSave.set(false);
    return;
  }
  if (!$('#checkInTime').val()) {
    i.canSave.set(false);
    return;
  }
  if (!$('#checkOutTime').val()) {
    i.canSave.set(false);
    return;
  }
  i.canSave.set(true);
}