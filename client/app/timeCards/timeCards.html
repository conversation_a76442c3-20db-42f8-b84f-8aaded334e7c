<template name="timeCards">
  <div class="container d-flex flex-row pl-0 pr-0">


    <div class="container d-flex flex-row justify-content-start mb-4 pl-0">
      <div class="mr-4 d-flex flex-column justify-content-end">
        <label>People:</label>
        <select data-cy="select-people" class="form-control min-w-350px max-w-350px" id="timeCardsPeopleFilter" multiple>
        </select>
      </div>
      <div data-cy="date-range-picker" class="mr-4 d-flex flex-column justify-content-end">
        <label>Date Range:</label>
        {{> dateRangePicker dateFields}}
      </div>
      <div class="mr-7 d-flex flex-column justify-content-end" style="white-space: nowrap;">
        <label>Filter by Confirmation Status:</label>
        <span class="switchHolder">
        <label class="switch mb-1">
          <input data-cy="filter-by-confirmation-status" type="checkbox" name="confirmedFilter" id="confirmedFilter">
          <span class="slider round"></span>
        </label>
      </span>
      </div>
      <div class="mr-7 d-flex flex-column justify-content-end" style="white-space: nowrap;">
        <label>Filter by Automatic Checkout <i class="fad fad-primary fa-alarm-clock" title="Auto Checkout"></i></label>
        <span class="switchHolder">
        <label class="switch mb-1">
          <input data-cy="filter-by-automatic-checkout" type="checkbox" name="autoCheckoutFilter" id="autoCheckoutFilter">
          <span class="slider round"></span>
        </label>
      </span>
      </div>
      <div class="mr-0 d-flex flex-column justify-content-end" style="white-space: nowrap;">
        <label>Filter by Edited Timecards <i class="fad fad-primary fa-pencil-ruler" title="Edited"></i></label>
        <span class="switchHolder">
        <label class="switch mb-1">
          <input data-cy="filter-by-is-edited" type="checkbox" name="isEditedFilter" id="isEditedFilter">
          <span class="slider round"></span>
        </label>
      </span>
      </div>
    </div>
    <div class="container d-flex flex-row justify-content-end mb-4 pr-0">
      <div class="d-flex flex-column justify-content-end">
        <div data-cy="export-to-csv-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnExportCsv">
          <i class="fad-regular fad fa-download fa-swap-opacity mr-2" style="color:#fff"></i>Export CSV
        </div>
      </div>
    </div>
  </div>
  <div class="container pr-0 pl-0">
    <div class="card card-cusom" id="timeCardTable">
      <table class="table" id="subsidy-report-table" style="display:block;overflow-x:auto;border:none;padding-bottom:10px;width:100%">
        <tbody style="display: table;width:100%">
          <tr style="line-height:30px;">
            <th class='no-wrap' style="border-top:none;" width="10%">Name</th>
            <th class='no-wrap' style="border-top:none;" width="8%">Date</th>
            <th class='no-wrap' style="border-top:none" width="8%">Time In</th>
            <th class='no-wrap' style="border-top:none;" width="8%">Time Out</th>
            <th class='no-wrap' style="border-top:none;" width="8%">Pay Type</th>
            <th class="no-wrap" style="border-top:none;" width="8%">Time</th>
            <th class='no-wrap' style="border-top:none;" width="10%">Confirmed</th>
            <th class='no-wrap' style="border-top:none;" width="20%">Void Reason</th>
            <th class="no-wrap" style="border-top:none;" width="5%" data-export-value="Automatic Checkout/Edited"></th>
            <th class="no-wrap" style="border-top:none;" width="15%"></th>
          </tr>
          {{#each tc in timeCardData}}
            <tr style="line-height:30px;">
              <td data-cy="name" class='no-wrap' width="10%">{{formatName tc}}</td>
              <td data-cy="date" class='no-wrap' width="8%">{{tc.checkInDate}}</td>
              <td data-cy="time-in" class='no-wrap' width="8%">{{tc.checkInTime}}</td>
              <td data-cy="time-out" class='no-wrap' width="8%">{{formatCheckOutTime tc.checkOutTime}}</td>
              <td data-cy="pay-type" class='no-wrap' width="8%">{{payTypeLabel tc.selectedPayTypeId}}</td>
              <td data-cy="format-time" class='no-wrap' width="8%">{{formatTimecardTime tc}}</td>
              <td data-cy="confirmed" class='no-wrap' width="10%">{{timeCardConfirmed tc}}</td>
              <td data-cy="void-reason" width="20%">{{formatVoidReason tc}}</td>
              <td data-cy="auto-checkout" width="5%"
                  data-export-value="{{#if isAutoCheckout tc}}Automatic Checkout{{else}}{{#if isEdited tc}}Edited{{else}}  {{/if}}{{/if}}"
              >
                {{#if isAutoCheckout tc}}
                  <i class="fad fad-primary fa-alarm-clock" title="Auto Checkout"></i>
                {{/if}}
                {{#if isEdited tc}}
                  <i class="fad fad-primary fa-pencil-ruler" title="Edited"></i>
                {{/if}}
              </td>
              <td width="15%" data-export-value="  ">
              <div style="margin-left:8px;padding-right: 8px;">
                <a data-cy="history-opt" href="#" class="text-primary font-weight-bold viewTimeCardLink" data-id="{{tc._id}}">History</a>
                {{#unless tc.void}}
                {{#if canEdit tc}}
                  |
                  <a data-cy="edit-opt" href="#" class="text-primary font-weight-bold editTimeCardLink" data-id="{{tc._id}}">Edit</a>
                  |
                  <a data-cy="void-opt" href="#" class="text-primary font-weight-bold voidTimeCardLink" data-id="{{tc._id}}">Void</a>
                {{/if}}
                {{#if hasKinderConnect}}
                    | <a href="#" class="text-primary font-weight-bold sendToKinderConnect" data-id="{{tc._id}}">KC Sync</a>
                  {{/if}}
                {{/unless}}
              </div>
              </td>
            </tr>
          {{/each}}
        </tbody>
      </table>
    </div>
    {{#if showLoading Template.subscriptionsReady}}
      {{> loading}}
    {{/if}}
  </div>
</template>
