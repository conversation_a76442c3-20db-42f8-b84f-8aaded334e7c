<template name="_timeCardFormModal">
  <div id="_timeCardFormModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" style="width: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Edit Time Card</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        {{#if loaded}}
          <div class="modal-body bg-gray-100">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right">Name</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <label data-cy="time-card-name">{{formatName}}</label>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right">Date</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <label data-cy="time-card-date">{{getTimeCardDate}}</label>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">CheckIn Time</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <input data-cy="checkin-time-card" type="time" class="form-control time-input" name="timePicker" id="checkInTime" placeholder="Choose a time" value="{{formattedCheckInTime}}">
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">CheckOut Time</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <input data-cy="checkout-time-card" type="time" class="form-control time-input" name="timeEndPicker" id="checkOutTime" placeholder="Set CheckOut Time (will create a CheckOut Moment)" value="{{formattedCheckOutTime}}">
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Total Hours</label>
              <div data-cy="total-hours" class="col-lg-6 col-md-9 col-sm-12 align-content-center">
                {{ totalHours }}
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Selected Pay Type</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="select-pay-type" class="form-control" name="selectedPayType" id="selectedPayType">
                  <option value="standard" {{selectedIfEqual getTimeCardPayTypeId "standard"}}>Standard</option>
                  {{#each pt in availablePayTypes}}
                    <option value="{{pt._id}}" {{selectedIfEqual getTimeCardPayTypeId pt._id}}>{{pt.type}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
          </div>
        {{/if}}
        <div class="modal-footer">
          {{#if conflictWarning}}
            <div class="text-danger mr-12" style="max-width: 70%">{{ conflictWarning }}.</div>
          {{/if}}
          <button data-cy="save-btn" type="button" class="btn btn-primary font-weight-bolder mr-2" disabled={{disabled}} id="saveTimeCard">Save</button>
          <button data-cy="close-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>
