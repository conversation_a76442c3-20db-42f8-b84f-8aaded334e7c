<template name="addDiscountDropdown">
    {{#if discountsAllowed}}
    <div class="row mb-2 ml-1">
        <div id="discountDropdown" class="dropdown">
            <a data-cy="add-discount-btn" href="" id="addDiscount" class="font-weight-bolder"><i class="fad fa-plus-circle mr-2"></i>Add Discount</a>
            <div class="dropdown-options">
                {{#if hasDiscount 'coupon_codes'}}
                <a data-cy="coupon-discount" id="addCouponCode" href="" class="d-block font-weight-bolder mb-2"><i class="fad fa-plus-circle mr-2"></i>Coupon Code</a>
                {{/if}}
                {{#if hasDiscount 'subsidy_approval_process'}}
                <a data-cy="subsidy-discount" id="addSubsidy" href="" class="d-block font-weight-bolder mb-2"><i class="fad fa-plus-circle mr-2"></i>Subsidy Approval Process</a>
                {{/if}}
                {{#if hasDiscount 'district_employee_approval_process'}}
                <a data-cy="district-email-discount" id="addDistrictEmail" href="" class="d-block font-weight-bolder mb-2"><i class="fad fa-plus-circle mr-2"></i>District Employee Approval Process</a>
                {{/if}}
                {{#if hasDiscount 'employee_id'}}
                    {{#if showEmployeeId}}
                        <a data-cy="employee-discount" id="addEmployeeId" href="" class="d-block font-weight-bolder mb-2"><i class="fad fa-plus-circle mr-2"></i>Employee ID</a>
                    {{/if}}
                {{/if}}
            </div>
        </div>
    </div>
    {{/if}}
</template>