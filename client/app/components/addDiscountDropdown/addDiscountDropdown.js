import { Template } from 'meteor/templating';
import { Orgs } from '../../../../lib/collections/orgs';
import './addDiscountDropdown.html';
import { AvailableCustomizations } from '../../../../lib/customizations';

Template.addDiscountDropdown.helpers({
    "discountsAllowed"() {
        const source = Template.instance().data.source;
        let discountsAllowed = []
        if (source === 'initialRegistration') {
            discountsAllowed = Template.instance().data.regSettings?.discountsAllowed;
        } else {
            discountsAllowed = Orgs.current().familyRegistrationSettings?.discountsAllowed;
        }

        return discountsAllowed?.length > 0;
    },
    "hasDiscount"(discountType) {
        const source = Template.instance().data.source;
        let discountsAllowed = []

        if (source === 'initialRegistration') {
            discountsAllowed = Template.instance().data.regSettings.discountsAllowed;
        } else {
            discountsAllowed = Orgs.current().familyRegistrationSettings?.discountsAllowed;
        }

        return discountsAllowed.includes(discountType);
    },
    showEmployeeId() {
        const source = Template.instance().data.source;

        if (source === 'initialRegistration') {
            return Template.instance().data.showEmployeeId;
        } else {
            return Orgs.current().hasCustomization(AvailableCustomizations.SHOW_EMPLOYEE_ID_PLR);
        }
    },
});