import { Template } from 'meteor/templating';
import './exportCsvAndPdfGeneralAttendance.html';
import { Log } from '../../../../lib/util/log';
Template.exportCsvAndPdfGeneralAttendance.events({
    "click #btnExportPdf": (e, instance) => {
        e.preventDefault();
        const content = document.getElementById(Template.instance().data.elementName);
        const isChildPerPage = $("#chkchildPerPage").is(":checked") || Template.instance().data.showSingleChildPerPage;
        const sortType = $("#sortType").val();

        // Get the outer HTML of the content element
        const contentHTML = content.outerHTML;
        let sortedTableHTML

        if (isChildPerPage) {
            // Sort the table HTML by the "Name" column
            let tableWrapper = document.createElement('div');
            tableWrapper.innerHTML = contentHTML;
            let tableElement = tableWrapper.querySelector('table');

            // Get the header row
            let headerRow = tableElement.querySelector('.reportHeader');

            // Get the table rows
            let rows = Array.from(tableElement.getElementsByTagName('tr'));

            // Remove the header row from the array, will be added to each page separately
            rows.shift();

            if (sortType === "attendanceDate") {
                // Group the rows by the common names
                let groupedRows = rows.reduce((groups, row) => {
                    let name = row.cells[0].textContent.trim();
                    if (!groups[name]) {
                        groups[name] = [];
                    }
                    groups[name].push(row);
                    return groups;
                }, {});

                // Sort each group of rows by the "Date" column
                for (let name in groupedRows) {
                    groupedRows[name].sort((rowA, rowB) => {
                        let dateA = new Date(rowA.cells[1].textContent.trim());
                        let dateB = new Date(rowB.cells[1].textContent.trim());
                        return dateA - dateB;
                    });
                }

                // If the sortType is "attendanceDate", sort the groups by the first name
                let sortedGroups = Object.keys(groupedRows).sort();
                let sortedRows = [];
                for (let name of sortedGroups) {
                    sortedRows.push(...groupedRows[name]);
                }
                rows = sortedRows;
            }

            // Create a new HTML table string with sorted rows
            sortedTableHTML = '<table class="table">';
            // Add the header row to the sorted table HTML
            sortedTableHTML += '<thead>' + headerRow.outerHTML + '</thead>';
            sortedTableHTML += '<tbody>';
            rows.forEach(row => {
                sortedTableHTML += row.outerHTML;
            });
            sortedTableHTML += '</tbody></table>';
        }

        // Open a new window for printing
        const printWindow = window.open();

        if (printWindow) {
            printWindow.document.open();
            printWindow.document.write('<html><head></head><body></body></html>');
            printWindow.document.head.innerHTML = document.head.innerHTML;
            printWindow.document.body.innerHTML = isChildPerPage ? sortedTableHTML : contentHTML;
            printWindow.document.close();
            printWindow.print();
        } else {
            Log.error("Unable to open print window. Element:", Template.instance().data.elementName);
        }
    },
})