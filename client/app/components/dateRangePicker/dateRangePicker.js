import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './dateRangePicker.html';

Template.dateRangePicker.onCreated(function() {
	var self = this;
	const endDate = this.data.filterEndDate.get(),
		startDate = this.data.filterStartDate.get();
	self.startDate = new ReactiveVar(startDate.format("YYYY-MM-DD"));
	self.endDate = new ReactiveVar(endDate.format("YYYY-MM-DD"));
});

Template.dateRangePicker.onRendered(function() {
	const instance = Template.instance();
  const data = Template.instance().data;
	$('input[name="mp-date-range-picker"]').daterangepicker(
		{
			startDate: new moment(instance.startDate.get(), "YYYY-MM-DD"),
			endDate: new moment(instance.endDate.get(), "YYYY-MM-DD"),
			ranges: {
				'Today': [moment(), moment()],
				'Last 30 Days': [moment().subtract(29, 'days'), moment()],
				'Last 60 Days': [moment().subtract(59, 'days'), moment()],
				'Last 120 Days': [moment().subtract(119, 'days'), moment()],
				'Last 365 Days': [moment().subtract(364, 'days'), moment()],
			},
			"maxSpan": {
				"years": 2
			}
		}
	).on('apply.daterangepicker', function(ev, picker) {		
		instance.startDate.set( picker.startDate.format('YYYY-MM-DD'));
		instance.endDate.set(picker.endDate.format('YYYY-MM-DD'));
    
		if (data) {
		data.filterStartDate.set(new moment(picker.startDate))
		data.filterEndDate.set(new moment(picker.endDate))
		}
	})
});
