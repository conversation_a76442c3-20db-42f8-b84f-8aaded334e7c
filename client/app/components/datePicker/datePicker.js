import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Orgs } from '../../../../lib/collections/orgs';
import './datePicker.html';
import moment from "moment-timezone";
import $ from 'jquery';

Template.datePicker.rendered = function() {
  var self = this;
  $(`#${getDatePickerId(self)}`).datepicker({
    autoclose: true, todayHighlight: true, clearBtn: true
  }).on("change", function(e) {
    if (validateDateInput(e.target.value)) {
      const date = new moment.tz(e.target.value, 'MM/DD/YYYY', Orgs.current().getTimezone());
      self.selectedDate.set(date);
      self.data.selectedDate.set(date);
    } else if (e.target.value === "") {
      self.selectedDate.set(false);
      self.data.selectedDate.set(false);
    }
  });
}

Template.datePicker.onCreated(function() {
  this.selectedDate = new ReactiveVar(false)
});

Template.datePicker.helpers({
  getDatePickerId() {
    return getDatePickerId(Template.instance());
  },
  getSelectedDate() {
    const instance = Template.instance();
    const sd = instance.data.selectedDate.get() || "";
    return sd && new moment(sd).format("MM/DD/YYYY");
  }
});

function getDatePickerId(i) {
  return i.data.datePickerId || "datePickerField";
}

function validateDateInput(dateValue) {
  const dateRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|1\d|2\d|3[01])\/(19|20)\d{2}$/;

  if (dateRegex.test(dateValue)) {
    // Valid MM/DD/YYYY format
    return true;
  } else {
    // Invalid format
    return false;
  }
}