<template name="planAllocation">
    {{#if trueIfEq type 'card'}}
        <div>
            <span>{{allocationType allocation}}{{#if trueIfEq allocation.discountType "coupon"}}
                : {{allocationContent allocation}}{{/if}}</span>
            {{#if trueIfEq allocation.discountType "bundle"}}
                <strong>{{allocation.allocationDescription}}</strong>{{/if}}
        </div>
    {{else}}
        <tr class="font-italic">
            <td>
                  <span>- {{allocationType allocation}}{{#if trueIfEq allocation.discountType "coupon"}} : {{allocationContent allocation}}{{/if}}{{#if trueIfEq allocation.discountType "bundle"}} {{allocation.allocationDescription}}{{/if}}
                      ({{#if trueIfEq allocation.amountType "percent"}} {{allocation.amount}}% {{else}} {{formatCurrency allocation.amount}} {{/if}} off )</span>
            </td>
            <td></td>
            <td style="text-align:right">- {{formatCurrency allocation.discountAmount}}</td>
            <td></td>
        </tr>
    {{/if}}
</template>