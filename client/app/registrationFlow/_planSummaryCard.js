import { Template } from 'meteor/templating';
import './_planSummaryCard.html';
import { MiscUtils } from '../../../lib/util/miscUtils';
import { ITEM_TYPE, PUNCH_CARD_TYPE } from '../../../lib/constants/billingConstants';
import { DiscountTypes } from '../../../lib/discountTypes';
import moment from 'moment-timezone';
import './_planAllocation';
import './_planSummaryCard.html';
import '../people/programs/_programDetails.js'


Template.planSummaryCard.helpers({
    priceRange: (plan, childIndex, weekIndex = null) => {
        const currentData = Template.instance().data.parentData.currentData;
        const plans = currentData.plans[childIndex]
        const availablePlans = Template.instance().data.parentData.availablePlans;
        const bundles = Template.instance().data.parentData.bundles;
        return MiscUtils.getPriceRange(plan, plans, availablePlans, bundles, weekIndex);
    },
    getTime: (plan) => {
        if (plan.details && plan.details.startTime && plan.details.endTime) {
            return `${plan.details.startTime}-${plan.details.endTime}`;
        }
    },
    selectedDaysText: (selectedDays) => {
        let daysText = "";
        selectedDays.forEach((day, index) => {
            daysText += day.capitalizeFirstLetter();
            daysText += index !== selectedDays.length - 1 ? ", " : ""
        });
        return daysText;
    },
    checkIfItemOrPunchcard: (planType) => {
        return planType === ITEM_TYPE || planType === PUNCH_CARD_TYPE
    },
    isSelectiveWeekPlan: (plan) => {
        return plan.details?.selectiveWeeks?.length > 0;
    },
    weeks: (plan) => {
        return plan.selectedWeeks;
    },
    formatWeekDates: (plan, week) => {
        return `${plan.details?.selectiveWeeks[week][0]} - ${plan.details?.selectiveWeeks[week][1]}`
    },
    getWeekNum: (index) => {
        return index + 1;
    },
    getRegWindow: (plan) => {
        if (plan.details && plan.details.regStartDate && plan.details.regEndDate) {
            const timezone = Template.instance().data.parentData.orgTimezone;
            const formattedStartDate = moment.tz(plan.details.regStartDate, timezone).format('MM/DD/YYYY')
            const formattedEndDate = moment.tz(plan.details.regEndDate, timezone).format('MM/DD/YYYY')

            return `${formattedStartDate}-${formattedEndDate}`
        }
    },
    getServiceDates: (plan) => {
        if (plan.details) {
            const timezone = Template.instance().data.parentData.orgTimezone;
            if (plan.details.dateType === 'dateRange' && plan.details.serviceStartDate && plan.details.serviceEndDate) {
                const dateRangeStartDate = moment.tz(plan.details.serviceStartDate, timezone).format('MM/DD/YYYY')
                const dateRangeEndDate = moment.tz(plan.details.serviceEndDate, timezone).format('MM/DD/YYYY')
                return `${dateRangeStartDate}-${dateRangeEndDate}`;
            } else if (plan.details.dateType === 'recurring' && plan.details.recurringStartDate && plan.details.recurringFrequency && plan.details.recurringOccurrences) {
                const startingDate = moment.tz(plan.details.recurringStartDate, timezone).format('MM/DD/YYYY')
                const recurringFrequency = plan.details.recurringFrequency
                const recurringOccurrences = plan.details.recurringOccurrences
                return `Starting ${startingDate}, Repeats every ${recurringFrequency} week(s) for ${recurringOccurrences} occurrences`
            } else if (plan.details.dateType === 'individualDates' && plan.details.individualDates) {
                const individualDates = plan.details.individualDates;
                const formattedDates = [];
                individualDates.forEach(date => {
                    const formatDate = moment.tz(date, timezone).format('MM/DD/YYYY')
                    formattedDates.push(formatDate)
                })
                return `${formattedDates.join(" , ")}`
            }
            if (plan.details.dateType === 'timePeriod' && plan.details.timePeriod) {
                const timezone = Template.instance().data.parentData.orgTimezone;
                const timePeriods = Template.instance().data.parentData.timePeriods;
                const filteredTimePeriods = timePeriods.find(timeperiod => timeperiod._id === plan.details.timePeriod)
                if (filteredTimePeriods) {
                    const serviceStartDate = moment.tz(filteredTimePeriods.startDate, timezone).format('MM/DD/YYYY')
                    const serviceEndDate = moment.tz(filteredTimePeriods.endDate, timezone).format('MM/DD/YYYY')
                    return `${serviceStartDate}-${serviceEndDate}`
                }
            }

        }
    },
    getGrades: (plan) => {
        if (plan.details && plan.details.grades) {
            return plan.details.grades.join(",")
        }
    },
    getOrgFrequency: (frequencyType) => {
        if(!frequencyType) {
            return 'One-time charge, paid at checkout.'
        }
        const frequencies = Template.instance().data.parentData.frequencies;
        return frequencies.find(af => af.type === frequencyType)?.description || '';
    },
    hasProgramDetails: (plan) => {
        if (!plan.programDetails) {
            return false;
        }
        return true;
    },  
});