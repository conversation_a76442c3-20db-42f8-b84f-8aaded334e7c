import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './_planCard.html';
import moment from 'moment-timezone';
import { MiscUtils } from '../../../lib/util/miscUtils';
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import './_planCard.html';
import '../people/programs/_programDetails.js'

Template.planCard.created = function () {
    const plan = this.data.plan;
    this.hiddenWeeks = new ReactiveVar([]);
    Tracker.autorun(() => {
        const hiddenWeeks = [];
        // Find and set the weeks that are not available for this plan
        if (plan?.details?.selectiveWeeks?.length) {
            const availableWeeks = RegistrationUtils.getAvailableSelectiveWeeks(
                plan,
                this.data.parentData.onHoldPlansVar?.get()?.length ? this.data.parentData.onHoldPlansVar.get() : this.data.parentData.existingPlans,
                this.data.parentData.orgTimezone
            );
            const availableWeeksString = JSON.stringify(availableWeeks);
            let index = 0;
            for (const week of plan.details?.selectiveWeeks) {
                if (!availableWeeksString.includes(JSON.stringify(week))) {
                    hiddenWeeks.push(index);
                }
                index++;
            }
        }
        this.hiddenWeeks.set(hiddenWeeks);
    });
};

Template.planCard.helpers({
    activePlan: function (plan) {
        if (plan.details?.selectiveWeeks?.length > 0) {
            return 'selective-weeks'
        }

        const plans = Template.instance().data.selectedPlans.get() ?? [];
        return plans.map(p => p._id).includes(plan._id) ? 'active' : '';
    },
    activeWeek: function (plan, index) {
        const plans = Template.instance().data.selectedPlans.get() ?? [];
        const selectedPlan = plans.find(p => p._id === plan._id);
        if (selectedPlan) {
            return selectedPlan.selectedWeeks?.includes(index) ? 'active' : '';
        } else {
            return '';
        }
    },
    priceRange(plan, index = null) {
        const plans = Template.instance().data.selectedPlans.get() ?? [];
        const availablePlans = Template.instance().data.parentData.availablePlans ?? [];
        const bundles = Template.instance().data.parentData.availableBundles ?? [];
        return MiscUtils.getPriceRange(plan, plans, availablePlans, bundles, index);
    },
    getOrgFrequency: (frequencyType) => {
        if (!frequencyType) {
            return 'One-time charge, paid at checkout.'
        }
        const frequencies = Template.instance().data.parentData.availableBillingFrequencies;
        return frequencies.find(af => af.type === frequencyType)?.description;
    },
    hasDetails: function (plan) {
        return plan.details
    },
    getServiceDates: (plan) => {
        const { details } = plan || {};

        if (details) {
            const { dateType, serviceStartDate, serviceEndDate, recurringStartDate, recurringFrequency, recurringOccurrences, individualDates, timePeriod } = details;
            const timezone = Template.instance().data.parentData.orgTimezone;

            if (dateType === 'dateRange' && serviceStartDate && serviceEndDate) {
                const dateRangeStartDate = moment.tz(serviceStartDate, timezone).format('MM/DD/YYYY');
                const dateRangeEndDate = moment.tz(serviceEndDate, timezone).format('MM/DD/YYYY');
                return `${dateRangeStartDate}-${dateRangeEndDate}`;
            } else if (dateType === 'recurring' && recurringStartDate && recurringFrequency && recurringOccurrences) {
                const startingDate = moment.tz(recurringStartDate, timezone).format('MM/DD/YYYY');
                const recurringFrequency = recurringFrequency
                const recurringOccurrences = recurringOccurrences
                return `Starting ${startingDate}, Repeats every ${recurringFrequency} week(s) for ${recurringOccurrences} occurrences`
            } else if (dateType === 'individualDates' && individualDates) {
                const formattedDates = individualDates.map(date => moment.tz(date, timezone).format('MM/DD/YYYY'));
                return formattedDates.join(" , ");
            }
            const timePeriods = Template.instance().data.parentData.availableTimePeriods;
            const filteredTimePeriods = timePeriods.find(timeperiod => timeperiod._id === timePeriod)
            if (filteredTimePeriods) {
                const serviceStartDate = moment.tz(filteredTimePeriods.startDate, timezone).format('MM/DD/YYYY');
                const serviceEndDate = moment.tz(filteredTimePeriods.endDate, timezone).format('MM/DD/YYYY');
                return `${serviceStartDate}-${serviceEndDate}`;
            }
        }
    },
    getTime: function (plan) {
        if (plan.details && plan.details.startTime && plan.details.endTime) {
            return `${plan.details.startTime}-${plan.details.endTime}`;
        }
    },
    getGrades: function (plan) {
        if (plan.details && plan.details.grades) {
            return plan.details.grades.join(",")
        }
        return 'All';
    },
    getRegWindow: (plan) => {
        if (plan.details && plan.details.regStartDate && plan.details.regEndDate) {
            const timezone = Template.instance().data.parentData.orgTimezone;
            const formattedStartDate = moment.tz(plan.details.regStartDate, timezone).format('MM/DD/YYYY');
            const formattedEndDate = moment.tz(plan.details.regEndDate, timezone).format('MM/DD/YYYY');

            return `${formattedStartDate}-${formattedEndDate}`
        }
    },
    isSelectiveWeekPlan: (plan) => {
        return plan.details?.selectiveWeeks?.length > 0;
    },
    weeks: (plan) => {
        return plan.details?.selectiveWeeks;
    },
    isWeekHidden: (plan, index) => {
        const weekIsHidden = Template.instance().hiddenWeeks?.get()?.includes(index);
        const selectedPlans = Template.instance().data.selectedPlans.get();
        if (selectedPlans.length) {
            const matchedPlan = selectedPlans.find(p => p._id === plan._id);
            if (matchedPlan) {
                const weekIsSelected = matchedPlan.selectedWeeks?.includes(index);
                return weekIsHidden && !weekIsSelected;
            }
            return weekIsHidden;
        }
        return weekIsHidden;
    },
    allWeeksHidden: () => {
        return Template.instance().hiddenWeeks?.get()?.length === Template.instance().data.plan.details?.selectiveWeeks?.length;
    },
    formatWeekDates: (week) => {
        if(week.length === 2) {
            return week.join(' - ');
        } else {
            return `${week[0]} - ${week[1]}`
        }
    },
    getWeekNum: (index) => {
        return index + 1;
    },
    hasProgramDetails: (plan) => {
        if (!plan.programDetails) {
            return false;
        }
        return true;
    },
});

Template.planCard.events({
    "click .registration-card": function (event, instance) {
        handleClick(event, instance);
    },
    "click .week-card": function (event, instance) {
        handleClick(event, instance);
    }
});

const handleClick = function (event, instance) {
    const planId = $(event.currentTarget).data("id");
    const weekIndex = $(event.currentTarget).data("week-index");
    const selectedPlans = instance.data.selectedPlans.get();

    const clickedPlan = instance.data.parentData.availablePlans.find(plan => plan._id === planId);

    // Handle selective weeks
    if (clickedPlan.details && clickedPlan.details.selectiveWeeks?.length > 0) {
        handleSelectiveWeeksClick(clickedPlan, weekIndex, selectedPlans, instance);
        return;
    }

    // Handle regular plan click
    handleRegularClick(clickedPlan, selectedPlans, instance);
};

const handleSelectiveWeeksClick = function (plan, weekIndex, selectedPlans, instance) {
    // Add a data guard to ensure weekIndex is defined
    if (weekIndex === undefined) {
        return;
    }

    const planIndex = selectedPlans.findIndex(selectedPlan => selectedPlan._id === plan._id);

    if (planIndex === -1) {
        // Plan not in selectedPlans, create a new entry
        const newSelectedPlan = {
            ...plan,
            selectedWeeks: [weekIndex],
        };
        selectedPlans.push(newSelectedPlan);
    } else {
        // Plan already in selectedPlans, update selectedWeeks
        const existingSelectedPlan = selectedPlans[planIndex];

        if (existingSelectedPlan.selectedWeeks && existingSelectedPlan.selectedWeeks.includes(weekIndex)) {
            // Week is already selected, remove it
            existingSelectedPlan.selectedWeeks = existingSelectedPlan.selectedWeeks.filter(week => week !== weekIndex);

            // If week was hidden due to capacity and the child removed the week, unhide it.
            const hiddenWeeks = instance.hiddenWeeks.get();
            if(hiddenWeeks.includes(weekIndex)) {
                hiddenWeeks.splice(hiddenWeeks.indexOf(weekIndex), 1);
                instance.hiddenWeeks.set(hiddenWeeks);
            }

            // If all weeks are deselected, remove the plan
            if (existingSelectedPlan.selectedWeeks.length === 0) {
                selectedPlans.splice(planIndex, 1);
            }
        } else {
            // Week is not selected, add it
            existingSelectedPlan.selectedWeeks = existingSelectedPlan.selectedWeeks || [];
            existingSelectedPlan.selectedWeeks.push(weekIndex);
        }
    }

    instance.data.selectedPlans.set([...selectedPlans]);
};


const handleRegularClick = function (plan, selectedPlans, instance) {
    const planIdArray = selectedPlans.filter(selectedPlan => !selectedPlan.createdAt).map(selectedPlan => selectedPlan._id);
    const enrolledPlans = selectedPlans.filter(selectedPlan => selectedPlan.createdAt);

    const timezone = instance.data.parentData.orgTimezone;
    const todaysDate = moment.tz(new Date(), timezone).valueOf();

    if (plan.details && plan.details.regStartDate) {
        const regStartDate = moment.tz(plan.details.regStartDate, timezone).format('MM/DD/YYYY');
        if (todaysDate < plan.details.regStartDate) {
            mpSwal.fire(`Registration is not currently open. Please check back on ${regStartDate}.`);
            return;
        }
    }

    if (enrolledPlans.find(selectedPlan => selectedPlan._id === plan._id)) {
        mpSwal.fire('You cannot remove a plan that is already enrolled.');
    } else if (planIdArray.includes(plan._id)) {
        selectedPlans.splice(selectedPlans.indexOf(plan), 1);
        // If removing a plan that is at capacity, unmark it.
        if (plan.atCapacity) {
            plan.atCapacity = false;
        }
    } else {
        selectedPlans.push(plan);
    }

    instance.data.selectedPlans.set([...selectedPlans]);
};
