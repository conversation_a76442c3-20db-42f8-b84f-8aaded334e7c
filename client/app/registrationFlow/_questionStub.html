<template name="registrationQuestionStub">
    <div class="form-group">
        {{#unless isCheckbox}}
        <!-- If the question is not a checkbox, then display the label -->
        <label data-cy="registration-child-question" for="child{{question.mappedTo}}">
            {{ question.question }}
            {{#if link}}
            <a href="{{link}}" target="_blank"><i class="fa fa-link"></i></a>
            {{/if}}
        </label>
        {{/unless}}
        {{#if isConditional}}
            {{!--
            <!--
                This will get removed before we merge this branch likely,
                but there's a chance product may want a radio instead of a select
                so if so it's here for reference.
            -->
            <!--
            <div>
                <div class="radio-inline">
                    <label class="radio radio-primary">
                        <input type="radio" name="conditional-{{ questionIndex }}" class="radio-single conditional-select {{ class }} {{validationClass}}" data-required="{{question.isRequired}}" value="Yes" {{checkedIfEq prefill "Yes"}}>
                        <span></span>
                        Yes
                    </label>
                    <label class="radio radio-primary">
                        <input type="radio" name="conditional-{{ questionIndex }}" class="radio-single conditional-select {{ class }} {{validationClass}}" data-required="{{question.isRequired}}" value="No" {{checkedIfEq prefill "No"}}>
                        <span></span>
                        No
                    </label>
                </div>
            </div>
            -->
            <!--
                We do not execute addConditionalClass here because the only
                result for that call is `is-nested`, and we never nest conditionals at this point.
                It does have `conditional-select` though which is important when constructing
                the final Person item.
            -->
            --}}
            <select
                id="childconditional-{{ questionIndex }}"
                name="conditional-{{ questionIndex }}"
                class="form-control rounded select-single conditional-select {{#if trueIfEq validationClass 'not-validated'}}border-0{{/if}} {{ class }} {{validationClass}}"
                data-required="{{question.isRequired}}"
            >
                <option value="">Select Yes or No</option>
                <option value="Yes" {{selectedIfEqual prefill "yes"}}>Yes</option>
                <option value="No" {{selectedIfEqual prefill "no"}}>No</option>
            </select>
        {{/if}}
        {{#if isText}}
            {{#unless isReadOnlyEmail}}
                <input
                        type="text"
                        id="child{{question.mappedTo}}"
                        name="{{question.mappedTo}}"
                        value={{prefill}}
                        class="form-control rounded {{ class }} {{validationClass}} {{addConditionalClass}}"
                        data-required="{{question.isRequired}}"
                >
            {{else}}
                <input
                        type="text"
                        id="child{{question.mappedTo}}"
                        name="{{question.mappedTo}}"
                        value={{prefill}}
                        class="form-control rounded {{ class }} {{addConditionalClass}}"
                        readonly
                >
            {{/unless}}
        {{/if}}
        {{#if isDate}}
        <div class="input-group">
            <input
                    type="text"
                    class="form-control date-picker {{#if trueIfEq validationClass 'not-validated'}}border-right-0{{/if}} {{ class }} {{validationClass}} {{addConditionalClass}}"
                    id="child{{question.mappedTo}}"
                    name="{{question.mappedTo}}"
                    value="{{prefill}}"
                    data-required="{{question.isRequired}}"
            >
            <div class="input-group-append">
                <span class="input-group-text" style="background-color: var(--secondary); border: none;"><i class="fad-regular fad fa-calendar"></i></span>
            </div>
        </div>
        {{/if}}
        {{#if isSingleSelect}}
        <select
                id="child{{question.mappedTo}}"
                name="{{question.mappedTo}}"
                class="form-control rounded select-single {{#if trueIfEq validationClass 'not-validated'}}border-0{{/if}} {{ class }} {{validationClass}} {{addConditionalClass}}"
                data-required="{{question.isRequired}}"
        >
            {{#each choice in choices}}
                {{!--
                <!-- 
                    For nested conditionals we have choice.value instead of choice. This
                    is for future flexibility but it does lead to needing to conditionally
                    render different options.
                    
                    We don't have an explicit label on each choice yet, we just use value for both.
                -->
                --}}
                {{# if isNested }}
                <option value="{{choice.value}}" {{selectedIfEqual prefill choice.value}}>{{choice.value}}</option>
                {{ else }}
                <option value="{{choice}}" {{selectedIfEqual prefill choice}}>{{choice}}</option>
                {{/if}}
            {{/each}}
        </select>
        {{/if}}
        {{#if isMultiSelect}}
        <div class="select-multi-group" data-required="{{question.isRequired}}" data-name="{{question.mappedTo}}">
            {{#each choice in choices}}
                {{!--
                <!-- 
                    Same as above, we have choice.value instead of choice for nested conditionals.
                -->
                --}}
                {{# if isNested }}
                <div class="checkbox-inline">
                    <label class="checkbox checkbox-primary">
                        <input type="checkbox" value="{{choice.value}}" class="select-multi-option {{validationClass}} {{addConditionalClass}}" {{checkedIfContains prefillArr choice.value}}>
                        <span></span>
                        {{choice.value}}
                    </label>
                </div>
                {{ else }}
                <div class="checkbox-inline">
                    <label class="checkbox checkbox-primary">
                        <input type="checkbox" value="{{choice}}" class="select-multi-option {{validationClass}} {{addConditionalClass}}" {{checkedIfContains prefillArr choice}}>
                        <span></span>
                        {{choice}}
                    </label>
                </div>
                {{/if}}
            {{/each}}
        </div>
        {{/if}}
        {{#if isCheckbox}}
            {{#if trueIfEq question.mappedTo 'copyToChildren'}}
            <div class="checkbox-inline" style="display: {{showElement}}">
            <label class="checkbox checkbox-primary">
                <input type="checkbox" name="{{question.mappedTo}}" class="checkbox-single {{ class }} {{validationClass}} {{addConditionalClass}}" data-required="{{question.isRequired}}" checked>
                <span></span>
                {{ question.question }}
                {{#if link}}
                <a class="ml-2" href="{{link}}" target="_blank"><i class="fad-regular fad fa-external-link"></i></a>
                {{/if}}
            </label>
        </div>
            {{else}}
                <div class="checkbox-inline">
                    <label class="checkbox checkbox-primary">
                        <input type="checkbox" name="{{question.mappedTo}}" class="checkbox-single {{ class }} {{validationClass}} {{addConditionalClass}}" data-required="{{question.isRequired}}"  {{checkedIfEq prefill "Yes"}}>
                        <span></span>
                        {{ question.question }}
                        {{#if link}}
                            <a class="ml-2" href="{{link}}" target="_blank"><i class="fad-regular fad fa-external-link"></i></a>
                        {{/if}}
                    </label>
                </div>
            {{/if}}
        {{/if}}
        {{#if question.isRequired}}
            {{#unless isReadOnlyEmail}}
                {{#if isConditional}}
                    <div id="validateconditional-{{ questionIndex }}" class="invalid-feedback" style="display: none">
                        {{{validationMessage}}}
                    </div>
                {{else}}
                    <div id="validate{{question.mappedTo}}" class="invalid-feedback" style="display: none">
                        {{{validationMessage}}}
                    </div>
                {{/if}}
            {{else}}
                <div style="color: var(--danger)">
                    You may not change your email address after initial registration
                </div>
            {{/unless}}
        {{/if}}
    </div>
    <!-- Begin conditional yes/no path: -->
    <div>
        {{!--
        <!-- Does rendering a _questionStub inside a _questionStub lead to a possible recursion error? -->
        <!-- Yes, yes it does. Let's hope we are good coders in the future -->
        <!-- Note, this will likely change before being finalized so going with it for now -CC -->
        <!-- We let conditional-{{ questionIndex }} equal to the parent value. -->
        --}}
        {{#let parentName=(concat 'conditional-' questionIndex)}}
            {{#if logicalEquals getConditionalPathToRender 'yes'}}
                {{>registrationQuestionStub question=question.ifYes prefill=parentPrefill isNested=true parentName=parentName}}
            {{/if}}
            {{#if logicalEquals getConditionalPathToRender 'no'}}
                {{>registrationQuestionStub question=question.ifNo prefill=parentPrefill isNested=true parentName=parentName}}
            {{/if}}
        {{/let}}
    </div>
    <!-- End conditional yes/no path -->
</template>