import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import './registrationFlowStep2.html';
import { MiscUtils } from '../../../lib/util/miscUtils';
import _ from '../../../lib/util/underscore';
const { AsYouType } = require("libphonenumber-js");
import { validNext, primaryContact, checkValidity, registrationDataSessionName, isParentEdit } from './registrationFlow';
import {RegistrationUtils} from "../../../lib/util/registrationUtils";
import '../people/components/_relationshipFormFields';

function disabledClick(e) {
    e.preventDefault();
}

Template.registrationFlowStep2.created = function() {
    this.currentStep = new ReactiveVar(this.data.currentStep);
    this.contactsSummaryView = new ReactiveVar(this.data.contactsSummaryView);
    this.regSettings = new ReactiveVar(this.data.regSettings);
}

Template.registrationFlowStep2.rendered = function() {
    validNext.set(checkValidity(this));
}


Template.registrationFlowStep2.helpers({
    prefills: () => {
        const contactIndex = Template.instance().data.currentContactIndex;
        const childIndex = Template.instance().data.currentChildIndex;
        const contactArray = Template.instance().data.savedData.contacts || [];
        const filteredArray = RegistrationUtils.getChildContacts(contactArray, childIndex);
        if(!Template.instance().data.editingContact) return {};
        return filteredArray[contactIndex] || {};
    },
    contacts: () => {
        const contacts = Template.instance().data.savedData.contacts || [];
        const currentChildIndex = Template.instance().data.currentChildIndex;
        return RegistrationUtils.getChildContacts(contacts, currentChildIndex);
    },
    isSummaryView: () => {
        return Template.instance().data.contactsSummaryView;
    },
    header: () => {
        if (Template.instance().data.contactsSummaryView) {
            return 'Add Contacts';
        }
        return Template.instance().data.currentContactIndex === 0 ? 'Add Your Information' : 'Add a Contact';
    },
    requiredContactsCountText: () => {
        let count = parseInt(Template.instance().data.requiredContactsCount);
        return count === 1 ? '1 emergency contact or authorized pickup is required' : `${count} emergency contacts or authorized pickups are required`;
    },
    requiredPrimaryContactText: () => {
        return 'You are required to select at least one primary contact.'
    },
    showContactsCountLine: () => {
        return Template.instance().data.requiredContactsCount > 0;
    },
    showPrimaryContactLine: () => {
        return !RegistrationUtils.doesSessionHavePrimaryContacts()
    },
    isPrimaryCaregiver: (index) => {
        const contacts = Template.instance().data.savedData.contacts || [];
        const currentChildIndex = Template.instance().data.currentChildIndex;
        const contactArray = RegistrationUtils.getChildContacts(contacts, currentChildIndex);
        const contact = contactArray[index] || {};
        return contact['primaryCaregiver'] === 'Yes';
    },
    isAuthorizedPickup: (index) => {
        const contacts = Template.instance().data.savedData.contacts || [];
        const currentChildIndex = Template.instance().data.currentChildIndex;
        const contactArray = RegistrationUtils.getChildContacts(contacts, currentChildIndex);
        const contact = contactArray[index] || {};
        return contact['authorizedPickup'] === 'Yes';
    },
    isEmergencyContact: (index) => {
        const contacts = Template.instance().data.savedData.contacts || [];
        const currentChildIndex = Template.instance().data.currentChildIndex;
        const contactArray = RegistrationUtils.getChildContacts(contacts, currentChildIndex);
        const contact = contactArray[index] || {};
        return contact['emergencyContact'] === 'Yes';
    },
    emailReadOnly: () => {
        const  index = Template.instance().data.currentContactIndex
        const contactArray = Template.instance().data.savedData.contacts || [];
        const contact = contactArray[index] || {};
        return !!(contact && contact._id)
    },
    displayCopy: () => {
      return isParentEdit.get();
    },
    qFirstName: () => {
        return {
            mappedTo: 'firstName',
            question: 'First Name',
            isRequired: true,
            type: 'text'
        }
    },
    qLastName: () => {
        return {
            mappedTo: 'lastName',
            question: 'Last Name',
            isRequired: true,
            type: 'text'
        }
    },
    qRelationship: () => {
        return {
            mappedTo: 'relationshipDescription',
            question: 'Relationship to Child',
            isRequired: true,
            type: 'text'
        }
    },
    qPrimaryPhone: () => {
        return {
            mappedTo: 'phonePrimary',
            question: 'Primary Phone #',
            isRequired: true,
            type: 'text'
        }
    },
    qAltPhone: () => {
        return {
            mappedTo: 'phoneAlt',
            question: 'Alt Phone #',
            isRequired: true,
            type: 'text'
        }
    },
    qEmail: () => {
        return {
            mappedTo: 'profileEmailAddress',
            question: 'Email Address',
            isRequired: true,
            type: 'text'
        }
    },
    qAddress: () => {
        return {
            mappedTo: 'address',
            question: 'Physical Address',
            isRequired: true,
            type: 'text'
        }
    },
    qCity: () => {
        return {
            mappedTo: 'city',
            question: 'City',
            isRequired: true,
            type: 'text'
        }
    },
    qState: () => {
        return {
            mappedTo: 'state',
            question: 'State',
            isRequired: true,
            type: 'text'
        }
    },
    qZip: () => {
        return {
            mappedTo: 'zip',
            question: 'Zip',
            isRequired: true,
            type: 'text'
        }
    },
    qAuthorizedPickup: () => {
        return {
            mappedTo: 'authorizedPickup',
            question: 'Authorized Pickup',
            isRequired: false,
            type: 'checkbox'
        }
    },
    qEmergencyContact: () => {
        return {
            mappedTo: 'emergencyContact',
            question: 'Emergency Contact',
            isRequired: false,
            type: 'checkbox'
        }
    },
    qPrimaryCaregiver: () => {
        return {
            mappedTo: 'primaryCaregiver',
            question: 'Primary Caregiver',
            isRequired: false,
            type: 'checkbox'
        }
    },
    qCopy: () => {
        return {
            mappedTo: 'copyToChildren',
            question: 'Copy this contact to all my children',
            isRequired: false,
            type: 'checkbox'
        }
    },
    phoneClass: () => {
        return 'phone';
    },
    emailClass: () => {
        return 'email';
    },
    caregiverClass: () => {
        return 'primary-caregiver select-multi-option';
    },
    emergencyContactClass: () => {
        return 'emergency-contact select-multi-option';
    },
    pickupClass: () => {
        return 'authorized-pickup select-multi-option';
    },
    whiteLabelLogoOverride: function() {
        const host = window.location.host.split(".")[0];
        const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
        const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
        if (currentOrg && _.deep(currentOrg, "whiteLabel.assets.appLogo")) {
            return {
                small: currentOrg.whiteLabel.assets.appLogoSmall,
                large: currentOrg.whiteLabel.assets.appLogo
            };
        } else if (enabledSites && enabledSites.indexOf(host) > -1) {
            return {
                small: Meteor.settings.public.whitelabel[host].small_logo,
                large: Meteor.settings.public.whitelabel[host].large_logo
            }
        }
    },
});

Template.registrationFlow.events({
    'input .phone': function (e) {
        const ayt = new AsYouType('US');
        ayt.input(e.target.value);
        e.target.value = ayt.getNumber() ? ayt.getNumber().formatNational() : '';
    },
    'click .primary-caregiver': function (e, i) {
        const checked = e.target.checked;
        const contactIndex = i.currentContactIndex.get();
        const primaryArray = primaryContact.get();
        const checkboxes = document.querySelectorAll('.emergency-contact, .authorized-pickup');
        if (checked) {
            for (const checkbox of checkboxes) {
                checkbox.checked = true;
                checkbox.setAttribute('aria-disabled', true);
                checkbox.addEventListener('click', disabledClick);
            }
            primaryArray.add(contactIndex);
            primaryContact.set(primaryArray);
        } else {
            for (const checkbox of checkboxes) {
                checkbox.setAttribute('aria-disabled', false);
                checkbox.removeEventListener('click', disabledClick);
            }
            primaryArray.delete(contactIndex);
            primaryContact.set(primaryArray);
        }
    },
    'click .primary-caregiver-checkbox': function (e, i) {
        let sessionData = JSON.parse(JSON.stringify(Session.get(registrationDataSessionName)));
        const contactEmailAddress = $(e.currentTarget).attr("data-id");
        const currentChildIndex = i.currentChildIndex.get();
        const contactTypeValue = (e.target.checked === true) ? 'Yes' : 'No';

        sessionData.contacts = RegistrationUtils.addOrUpdateContact(
            sessionData.contacts,
            contactEmailAddress,
            currentChildIndex,
            'primaryCaregiver',
            contactTypeValue
        );

        Session.set(registrationDataSessionName, sessionData);
        RegistrationUtils.setPrimaryContact(sessionData.contacts, primaryContact, currentChildIndex);
    },
    'click .authorized-pickup-checkbox': function (e, i) {
        let sessionData = JSON.parse(JSON.stringify(Session.get(registrationDataSessionName)));
        const contactEmailAddress = $(e.currentTarget).attr("data-id");
        const currentChildIndex = i.currentChildIndex.get();
        const contactTypeValue = (e.target.checked === true) ? 'Yes' : 'No';

        sessionData.contacts = RegistrationUtils.addOrUpdateContact(
            sessionData.contacts,
            contactEmailAddress,
            currentChildIndex,
            'authorizedPickup',
            contactTypeValue
        );

        Session.set(registrationDataSessionName, sessionData);
    },
    'click .emergency-contact-checkbox': function (e, i) {
        let sessionData = JSON.parse(JSON.stringify(Session.get(registrationDataSessionName)));
        const contactEmailAddress = $(e.currentTarget).attr("data-id");
        const currentChildIndex = i.currentChildIndex.get();
        const contactTypeValue = (e.target.checked === true) ? 'Yes' : 'No';

        sessionData.contacts = RegistrationUtils.addOrUpdateContact(
            sessionData.contacts,
            contactEmailAddress,
            currentChildIndex,
            'emergencyContact',
            contactTypeValue
        );

        Session.set(registrationDataSessionName, sessionData);
    },
    'click #relationship-group .select-multi-option': function (e) {
        const validation = MiscUtils.validateInput(e);
        if(!validation.valid) {
            $('#relationship-group-validation').show();
        }else {
            $('#relationship-group-validation').hide();
        }
    }
});