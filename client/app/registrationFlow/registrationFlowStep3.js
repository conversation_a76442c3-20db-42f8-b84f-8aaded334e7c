import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './registrationFlowStep3.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { selectedPlans, validNext, checkValidity } from './registrationFlow';
import { MiscUtils } from "../../../lib/util/miscUtils";
import { Log } from '../../../lib/util/log';
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import moment from 'moment-timezone';
import logger from '../../../imports/winston/index';
import './_planCard';

Template.registrationFlowStep3.created = function () {
    this.activeProgramIndex = new ReactiveVar(0);
    this.availablePrograms = new ReactiveVar([]);
    this.availablePlans = new ReactiveVar([]);
    this.availableTimePeriods = new ReactiveVar([])
    this.availableBillingFrequencies = new ReactiveVar([]);
    this.currentStep = new ReactiveVar(this.data.currentStep);
    this.orgTimezone = new ReactiveVar('America/New_York');
    this.availableBundles = new ReactiveVar([]);
    const orgId = FlowRouter.getQueryParam('orgId');
    this.designation = FlowRouter.getQueryParam('designation') || null;

    // Consolidate all the needed org info into one call
    Meteor.callAsync('getOrgInformation', orgId, true, this.designation).then((result) => {
        if (result) {
            const { plans, programs, frequencies, timePeriods, timezone, bundles } = result;
            this.availableBillingFrequencies.set(frequencies);
            this.availableTimePeriods.set(timePeriods);
            this.orgTimezone.set(timezone);
            this.availableBundles.set(bundles);

            const currentChildIndex = this.data.currentChildIndex;
            const currentChildGrade = this.data.savedData.children[currentChildIndex].studentGrade;
            const currentDate = moment().tz(timezone).startOf('day').valueOf();
            const filteredPlansWithDetails = plans.filter(plan => {
                if (plan.details && plan.details.regEndDate) {
                    const currentDateFormatted = moment(currentDate).startOf('day').format('YYYY-MM-DD');
                    const regEndDateFormatted = moment(plan.details.regEndDate).startOf('day').format('YYYY-MM-DD');
                    return plan.details.regEndDate > currentDate || currentDateFormatted === regEndDateFormatted;
                }

                return true;
            });

            const filteredPlansWithGrades = filteredPlansWithDetails.filter(plan => {
                if (plan.details && plan.details.grades) {
                    return plan.details.grades.includes(currentChildGrade);
                }

                return true;
            });

            // Remove plans that are outside of the registration period
            const filterPlansOutsideRegPeriod = filteredPlansWithGrades.filter(plan => {
                if (plan.details?.regStartDate || plan.details?.regEndDate) {
                    const today = moment.tz(timezone).startOf('day');
                    if (plan.details?.regStartDate) {
                        const regStartDate = moment.tz(plan.details.regStartDate, timezone);
                        if (today.isBefore(regStartDate)) {
                            return false;
                        }
                    }

                    if (plan.details?.regEndDate) {
                        const regEndDate = moment.tz(plan.details.regEndDate, timezone);
                        if (today.isAfter(regEndDate)) {
                            return false;
                        }
                    }
                }
                return true;
            });

            const programWithPlans = programs.filter(program => filterPlansOutsideRegPeriod.some(plan => plan?.program === program._id));

            filterPlansOutsideRegPeriod.forEach(plan => {
                const matchedProgram = programWithPlans.find(program => program._id === plan.program);
                if (matchedProgram) {
                    plan.isRequiredAdvanceNotice = matchedProgram.isRequiredAdvanceNotice;
                }
            });

            logger.info('registrationFlowStep3 > Available programs', { 'Available programs': programWithPlans });
            this.availablePrograms.set(programWithPlans);

            // Pre-select program if passed in query param
            if (FlowRouter.getQueryParam('program')) {
                const programId = FlowRouter.getQueryParam('program');
                const program = this.availablePrograms.get().findIndex(p => p._id === programId);
                logger.info('registrationFlowStep3 > Pre-selected program', { 'Pre-selected program': program });
                this.activeProgramIndex.set(program ?? 0);
            }

            if (this.data.savedData.plans && this.data.savedData.plans[this.data.currentChildIndex] && this.data.savedData.plans[this.data.currentChildIndex].length) {
                selectedPlans.set(this.data.savedData.plans[this.data.currentChildIndex]);
            } else {
                selectedPlans.set([]);
            }

            Meteor.callAsync('getItemAndSelectiveWeekAvailabilities', orgId, filterPlansOutsideRegPeriod, this.data.savedData).then((result) => {
                if (result) {
                    this.availablePlans.set(result.filter(plan => {
                        // Don't remove items if they are currently selected so that you can de-select them.
                        const itemIsSelected = selectedPlans.get().some(selectedPlan => selectedPlan._id === plan._id);
                        const planIsAtCapacity = plan.atCapacity;
                        return !planIsAtCapacity || itemIsSelected;
                    }));
                    logger.info('registrationFlowStep3 > getItemAndSelectiveWeekAvailabilities availablePlans', { 'Available plans': this.availablePlans.get() });
                }
            }).catch((error) => {
                mpSwal.fire('Error', error.reason || error.message, 'error');
                logger.error('registrationFlowStep3 > Error getting item and selective week availabilities', { 'Error reason': error.reason, 'Error message': error.message });
                Log.error(error);
                this.availablePlans.set(filterPlansOutsideRegPeriod);
            });

            validNext.set(checkValidity(this));
        }
    }).catch((error) => {
        mpSwal.fire('Error', error.reason || error.message, 'error');
        logger.error('registrationFlowStep3 > No org information was found from getOrgInformation', { 'Error reason': error.reason, 'Error message': error.message });
        Log.error(error);
    });
};


Template.registrationFlowStep3.helpers({
    programs: () => {
        return Template.instance().availablePrograms.get();
    },
    activeButton: (index) => {
        const activeProgramIndex = Template.instance().activeProgramIndex.get();
        return index === activeProgramIndex ? 'btn-primary' : 'btn-outline-primary';
    },
    plans: () => {
        const availablePrograms = Template.instance().availablePrograms.get();
        const currentProgramIndex = Template.instance().activeProgramIndex.get();
        const availablePlans = Template.instance().availablePlans.get();
        const timezone = Template.instance().orgTimezone.get();
        const timePeriods = Template.instance().availableTimePeriods.get();
        const selectedProgram = availablePrograms[currentProgramIndex];

        // Filter plans by selectedProgram
        const programFilteredPlans = availablePlans.filter(plan => plan.program === selectedProgram._id);

        // Loop over programFilteredPlans and update each plan with getServiceDates result
        programFilteredPlans.forEach(plan => {
            updatePlanWithServiceDates(plan, timePeriods, timezone);
        });

        programFilteredPlans.sort(MiscUtils.comparePlansWithServiceDatesFirst);

        return programFilteredPlans;
    },
    getOrgFrequency: (frequencyType) => {
        if(!frequencyType) {
            return 'One-time charge, paid at checkout.'
        }
      const frequencies = Template.instance().availableBillingFrequencies.get();
      return _.find(frequencies, af => af.type === frequencyType)?.description;
    },
    removable: (plan) => {
        return !plan.createdAt;
    },
    hasDetails: (plan) => {
        return plan.details
    },
    whiteLabelLogoOverride: function() {
        const host = window.location.host.split(".")[0];
        const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
        const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
        if (currentOrg && _.deep(currentOrg, "whiteLabel.assets.appLogo")) {
            return {
                small: currentOrg.whiteLabel.assets.appLogoSmall,
                large: currentOrg.whiteLabel.assets.appLogo
            };
        } else if (enabledSites && enabledSites.indexOf(host) > -1) {
            return {
                small: Meteor.settings.public.whitelabel[host].small_logo,
                large: Meteor.settings.public.whitelabel[host].large_logo
            }
        }
    },
    parentData: function() {
        return {
            availablePlans: Template.instance().availablePlans.get(),
            availableTimePeriods: Template.instance().availableTimePeriods.get(),
            availableBillingFrequencies: Template.instance().availableBillingFrequencies.get(),
            orgTimezone: Template.instance().orgTimezone.get(),
            availableBundles: Template.instance().availableBundles.get(),
        }
    },
    selectedPlans: function() {
        return selectedPlans;
    }
});

Template.registrationFlowStep3.events({
    "click .program-button": function (event, instance) {
        instance.activeProgramIndex.set($(event.currentTarget).data("id"));
    },
});

// Function to update each plan with serviceDates result
const updatePlanWithServiceDates = (plan, timePeriods, timezone) => {
    const serviceDatesResult = RegistrationUtils.getServiceDates(plan, timePeriods, timezone);

    // Update the plan with the result
    plan.serviceDates = serviceDatesResult;
}
