import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_questionStub.html';
import { MiscUtils } from '../../../lib/util/miscUtils';
import { ReactiveVar } from 'meteor/reactive-var';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { registrationDataSessionName } from "./registrationFlow";
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';

Template.registrationQuestionStub.created = function () {
    this.validationMessage = new ReactiveVar('This is a required field.');
    this.validationClass = new ReactiveVar('not-validated');
    this.supportUrl = new ReactiveVar('');
    this.conditionalPathToRender = new ReactiveVar(''); // '' or 'yes' or 'no'
}

Template.registrationQuestionStub.events({
    'input': (event, instance) => {
        validateInput(event, instance);
    },
    'focus input': (event, instance) => {
        validateInput(event, instance);
    },
    'change select': (event, instance) => {
        validateInput(event, instance);
    },
    'focus select': (event, instance) => {
        validateInput(event, instance);
    }
});

Template.registrationQuestionStub.helpers({
    "concat": (a, b) => {
        return a + b;
    },
    "addConditionalClass": () => {
        // Returns 'is-nested' if this question stub has isNested
        // in the data. This is important because we don't want
        // the above events to trigger on nested input items (yet)
        const isNested = Template.instance().data.isNested
        if (isNested) {
            // parentName should never be undefined if isNested is true,
            // but we add this check here just in case.
            // We could consider raising an error here if parentName is undefined,
            // but for now we'll just return 'is-nested' to keep the page from breaking.
            const parentName = Template.instance().data.parentName;
            if (parentName) {
                return `is-nested ${parentName}-result`;
            }
            return 'is-nested'
        }
        return '';
    },
    "getConditionalPathToRender": () => {
        return Template.instance().conditionalPathToRender.get();
    },
    "validationMessage": () => {
        return Template.instance().validationMessage.get();
    },
    "validationClass": () => {
        return Template.instance().validationClass.get();
    },
    "questionIndex": () => {
        return Template.instance().data.questionIndex;
    },
    "parentPrefill": () => {
        //
        // Because of the way nesting of _questionStub templates work,
        // when rendering the child conditional cases (Yes/No have been selected),
        // we need to look at the parent prefill data to see if yes/no has been selected ...
        // if so, we need to return the mapping with the value.
        //
        const templateData = Template.instance().data;
        if (templateData.prefill.registrationConditionals) {
            // See if the `conditional-${templateData.questionIndex}` is
            // either in registrationConditionals.yes or registrationConditionals.no
            const checkString = `conditional-${templateData.questionIndex}`;
            const yes = templateData.prefill.registrationConditionals.yes || [];
            const no = templateData.prefill.registrationConditionals.no || [];
            let useMapTo = undefined;
            if (yes.includes(checkString)) {
                useMapTo = (templateData.question.ifYes || {}).mappedTo;
            } else if (no.includes(checkString)) {
                useMapTo = (templateData.question.ifNo || {}).mappedTo;
            }
            if (useMapTo) {
                const returnValue = templateData.prefill[useMapTo] || '';
                return {
                    [useMapTo]: returnValue
                }
            }
        }
        return {};
    },
    "prefill": () => {
        if (!Template.instance().data || !Template.instance().data.prefill) {
            return '';
        }
        const templateData = Template.instance().data;
        const thisQuestion = templateData.question;
        if (thisQuestion.type !== 'conditional') {
            return templateData.prefill[templateData.question.mappedTo] || '';
        }
        // If this is a conditional question, then we need to check
        // if the prefill value is in the ifYes or ifNo path.
        if (templateData.prefill.registrationConditionals) {
            // See if the `conditional-${templateData.questionIndex}` is
            // either in registrationConditionals.yes or registrationConditionals.no
            const checkString = `conditional-${templateData.questionIndex}`;
            const yes = templateData.prefill.registrationConditionals.yes || [];
            const no = templateData.prefill.registrationConditionals.no || [];
            if (yes.includes(checkString)) {
                // Set the conditionalPathToRender to 'yes'
                Template.instance().conditionalPathToRender.set('yes');
                return 'yes';
            }
            if (no.includes(checkString)) {
                // Set the conditionalPathToRender to 'no'
                Template.instance().conditionalPathToRender.set('no');
                return 'no';
            }
        }
        return '';
    },
    'prefillArr': () => {
        if (!Template.instance().data || !Template.instance().data.prefill) {
            return '';
        }
        const str = Template.instance().data.prefill[Template.instance().data.question.mappedTo] || '';
        return str.split(',');
    },
    "isText": () => {
        return Template.instance().data.question.type === 'text';
    },
    "isSingleSelect": () => {
        return Template.instance().data.question.type === 'selectSingle';
    },
    "isMultiSelect": () => {
        return Template.instance().data.question.type === 'selectMultiple';
    },
    "isDate": () => {
        return Template.instance().data.question.type === 'date';
    },
    "isCheckbox": () => {
        return Template.instance().data.question.type === 'checkbox';
    },
    "isConditional": () => {
        return Template.instance().data.question.type === 'conditional';
    },
    "choices": () => {
        const choices = Template.instance().data.question.choices || [];
        if (Template.instance().data.question.type === 'selectSingle') {
            return [''].concat(choices);
        }
        return choices;
    },
    'link': () => {
        return Template.instance().data.question.includedLink;
    },
    'isReadOnlyEmail': () => {
        return Template.instance().data.readOnly;
    },
    'showElement': () => {
        return Template.instance().data.displayElement ? '' : 'none';
    }
});

Template.registrationQuestionStub.rendered = function () {
    const birthdayPicker = $('.date-picker[name="birthday"]').datepicker({
        autoclose: true,
        endDate: '+0d'
    })
    birthdayPicker.on('changeDate', (ev) => {
        ev.target.dispatchEvent(new Event('input', { 'bubbles': true }));
    });
    birthdayPicker.on('hide', (ev) => {
        ev.target.dispatchEvent(new Event('input', { 'bubbles': true }));
    }).on('hide', (ev) => {
        MiscUtils.sendHeight();
    }).on('show', (ev) => {
        setTimeout(() => {
            MiscUtils.sendHeight('xl')
        }, 100)
    })
    $('.date-picker[name!="birthday"]').datepicker({
        autoclose: true
    }).on('changeDate', (ev) => {
        ev.target.dispatchEvent(new Event('input', { 'bubbles': true }));
    }).on('hide', (ev) => {
        MiscUtils.sendHeight();
    }).on('show', (ev) => {
        setTimeout(() => {
            MiscUtils.sendHeight('xl')
        }, 100)
    })
}

const validateInput = function (event, instance) {
    if (instance.data.isNested) {
        // This short-circut is different than when we check is-nested
        // below. This validateInput call gets fired on the parent question
        // because of the way inputs and input groups are nested.
        // We return here because we don't want to validate the parent question
        // when the child question is being validated.
        event.preventDefault();
        return;
    }
    // If this currentTarget has the "is-nested" class, then
    // simply return for now.
    if (Array.from(event.currentTarget.classList).includes('is-nested')) {
        // We get `questionIndex` from instance.data.questionIndex.
        // Using that, we determine if "Yes" or "No" has been selected.
        // (search for `conditional-${questionIndex}` and get the .value.trim().toLowerCase() of that element)
        const questionIndex = instance.data.questionIndex;
        // Get the item with "name" of `conditional-${questionIndex}`
        const conditionalElement = document.querySelector(`[name="conditional-${questionIndex}"]`);
        if (!conditionalElement) {
            // This should never happen, but just in case...
            console.warn(`Could not find element with name "conditional-${questionIndex}"`);
            return;
        }
        // Get the value of that element
        const conditionalValue = conditionalElement.value.trim().toLowerCase();
        // If "yes", then we need to look at instance.data.question.ifYes.
        // If "no", then we need to look at instance.data.question.ifNo.
        const conditionalPathToRender = conditionalValue === 'yes' ? instance.data.question.ifYes : instance.data.question.ifNo;
        const mappedToConditional = conditionalPathToRender.mappedTo;
        if (instance.validationClass.get() === 'not-validated') {
            event.currentTarget.classList.remove('is-valid', 'is-invalid');
        }
        if (conditionalPathToRender.isRequired) {
            const validation = MiscUtils.validateInput(event);
            if (!validation.valid) {
                instance.validationClass.set('is-invalid');
                instance.validationMessage.set(validation.message);
                $(`#validate${mappedToConditional}`).show();
            } else {
                instance.validationClass.set('is-valid');
                instance.validationMessage.set('');
                $(`#validate${mappedToConditional}`).hide();
            }
        }
        return;
    }
    if (instance.validationClass.get() === 'not-validated') {
        event.currentTarget.classList.remove('is-valid', 'is-invalid');
    }
    if (instance.data.question.type === 'conditional') {
        const question = instance.data.question;
        const currentTargetValue = $(event.currentTarget).val().trim().toLowerCase();
        let updatedConditionalPathToRender = '';
        if (currentTargetValue === 'yes') {
            updatedConditionalPathToRender = question.ifYes?.type === 'noFollowUpNeeded' ? '' : 'yes';
        } else if (currentTargetValue === 'no') {
            updatedConditionalPathToRender = question.ifNo?.type === 'noFollowUpNeeded' ? '' : 'no';
        }
        instance.conditionalPathToRender.set(updatedConditionalPathToRender);
    }
    if (instance.data.question.isRequired) {
        const questionIdentifier = instance.data.question.type !== 'conditional' ? instance.data.question.mappedTo : `conditional-${instance.data.questionIndex}`;
        const validation = MiscUtils.validateInput(event);
        if (!validation.valid) {
            instance.validationClass.set('is-invalid');
            instance.validationMessage.set(validation.message);
            $(`#validate${questionIdentifier}`).show();
        } else {
            instance.validationClass.set('is-valid');
            instance.validationMessage.set('');
            $(`#validate${questionIdentifier}`).hide();
        }
        // check against existing user emails but only on initial registration
        if (validation.valid && Array.from(event.currentTarget.classList).includes('email') && instance.data.readOnly === false) {
            const value = $(event.currentTarget).val().trim().toLowerCase();
            const org = FlowRouter.getQueryParam('orgId') || Orgs.current()?._id || null;
            const contactEmails = Session.get(registrationDataSessionName)?.contacts?.map(contact => contact.profileEmailAddress) || [];
            if (value && org) {
                Meteor.callAsync('validateChatSupport', org).then((res) => {
                    instance.supportUrl.set(res);
                })
                Meteor.callAsync('checkEmailExistsForOrg', value, org).then((res) => {
                    if (res) {
                        instance.validationClass.set('is-invalid');
                        if (!instance.supportUrl.get()) {
                            instance.validationMessage.set('This email is already in use. Please log in to make updates or contact customer support.');
                            $(`#validate${questionIdentifier}`).show();
                        } else {
                            instance.validationMessage.set(`This email is already in use. Please log in to make updates or contact <a href=${instance.supportUrl.get()}> customer support. </a>`);
                            $(`#validate${questionIdentifier}`).show();
                        }
                    }
                    else if (contactEmails.includes(value)) {
                        instance.validationClass.set('is-invalid');
                        instance.validationMessage.set('This email is already in use. Please use a different email address.');
                        $(`#validate${questionIdentifier}`).show();
                    }
                    else {
                        instance.validationClass.set('is-valid');
                        instance.validationMessage.set('');
                        $(`#validate${questionIdentifier}`).hide();
                    }
                }).catch((err) => {
                    console.log("error in checkEmailExistsForOrg", err);
                });
            }
        }
    }
}
