import { Template } from 'meteor/templating';
import moment from 'moment-timezone';
import { parentSource } from '../../../lib/constants/registrationConstants';
import './_effectiveDate.html';
import { Orgs } from '../../../lib/collections/orgs';
import './_effectiveDate.html';

Template.effectiveDate.helpers({
    initDatePicker: function (plan) {
        const instance = Template.instance();

        if (instance.data.parentSource === parentSource.EDIT_PROGRAM) {
            const { linkedPlan, bundleLinkedPlan } = instance.data;
            const org = Orgs.current();
            const timezone = org?.getTimezone();
            const { planDetails } = plan;
            const serviceStartDate = instance.data.parentData.prefillEnrolledStartDate(plan);
            let serviceEndDate = null;

            let availabilityPlan = null;
            if (linkedPlan._id === plan._id) {
                instance.data.parentData.effectiveDate?.set(serviceStartDate);
                instance.data.parentData.getAvailability(linkedPlan, serviceStartDate);
                availabilityPlan = linkedPlan;
            } else {
                instance.data.parentData.bundleEffectiveDate?.set(serviceStartDate);
                instance.data.parentData.getAvailability(bundleLinkedPlan, serviceStartDate);
                availabilityPlan = bundleLinkedPlan;
            }

            if (planDetails?.details.timePeriod) {
                const availableTimePeriods = org?.billing.timePeriods ?? [];
                const planTimePeriod = availableTimePeriods.find(tp => tp._id === planDetails?.details.timePeriod);
                if (planTimePeriod) {
                    serviceEndDate = moment.tz(planTimePeriod.endDate, timezone).format('MM/DD/YYYY');
                }
            }

            setTimeout(() => {
                $(`.date-picker[data-id=${plan._id}]`).datepicker({
                    autoclose: true,
                    startDate: serviceStartDate,
                    endDate: serviceEndDate
                }).on('changeDate', function (ev) { // Use function to maintain `this` context
                    ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
                    instance.data.parentData.getAvailability(availabilityPlan, ev.target.value);
                });
            }, 400);
        }
    },


    prefillStartDate: function (plan) {
        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            if (Template.instance().data.bundle) {
                return Template.instance().data.parentData.bundleEffectiveDate?.get();
            } else {
                return Template.instance().data.parentData.effectiveDate?.get();
            }
        } else {
            return prefillNewStartDate(plan, Template.instance());
        }
    },
});

function prefillNewStartDate(plan, instance) {
    const timezone = instance.data.parentData.orgTimezone;
    let defaultStartDate = null;
    let minStartDate = null;
    let requiredAdvanceNotice = false;

    if (instance.data.parentSource === parentSource.ADD_PROGRAM) {
        const planProgram = instance.data.parentData.programs?.find(program => program._id === plan.program);
        requiredAdvanceNotice = planProgram?.isRequiredAdvanceNotice;
    } else {
        requiredAdvanceNotice = plan.isRequiredAdvanceNotice;
    }

    if (requiredAdvanceNotice) {
        defaultStartDate = moment().tz(timezone).startOf('day').add(7, 'days');
    } else {
        defaultStartDate = moment().tz(timezone).startOf('day');
    }

    minStartDate = defaultStartDate;
    const availableTimePeriods = instance.data.parentData.availableTimePeriods;

    if (plan.details && plan.details.timePeriod) {
        const planTimePeriod = availableTimePeriods.find(tp => tp._id === plan.details.timePeriod);
        if (planTimePeriod) {
            defaultStartDate = moment.tz(planTimePeriod.startDate, timezone);
        }
    }

    if (defaultStartDate.valueOf() < minStartDate.valueOf()) {
        defaultStartDate = minStartDate;
    }

    return plan.startDate ? moment(plan.startDate).format('MM/DD/YYYY') : defaultStartDate.format('MM/DD/YYYY');
}