import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import './registrationFlowStep1.html';
import { validNext, checkValidity } from './registrationFlow';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { MiscUtils } from "../../../lib/util/miscUtils";
import {RegistrationUtils} from "../../../lib/util/registrationUtils";
import _ from '../../../lib/util/underscore';
import './_questionStub';
import { rasGradeLabel } from '../../../lib/constants/registrationConstants';
import { AvailableCustomizations } from '../../../lib/customizations';

Template.registrationFlowStep1.created = function () {
    this.currentStep = new ReactiveVar(this.data.currentStep);
    this.studentGradeField = new ReactiveVar({
        name: 'studentGrade',
        description: 'Grade',
        type: 'select',
        values: [
            'Infant',
            'Toddler',
            'Pre-Kindergarten',
            'Kindergarten',
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8'
        ]
    });
    this.studentGenderField = new ReactiveVar({
        name: 'gender',
        description: 'Gender',
        type: 'select',
        values: [
            'Male',
            'Female',
            'Unspecified'
        ]
    });
    Meteor.callAsync('getStudentGradeField', FlowRouter.getQueryParam('orgId')).then((results) => {
        if (results) {
            this.studentGradeField.set(results);
        }
    }).catch((err) => {
        console.log("error in getStudentGradeField", err);
    });
    Meteor.callAsync('getGenderField', FlowRouter.getQueryParam('orgId')).then((results) => {
        if (results) {
            this.studentGenderField.set(results)
        }
    }).catch((err) => {
        console.log("error in getGenderField", err);
    });
    Meteor.callAsync('hasCustomizationByOrgId', FlowRouter.getQueryParam('orgId'), AvailableCustomizations.RAS_GRADE_LABEL).then((result) => {
        if (result) {
            const currentGradeField = this.studentGradeField.get()
            currentGradeField.description = rasGradeLabel.GRADE_LABEL
            this.studentGradeField.set(currentGradeField);
        }
    }).catch((err) => {
        if (err) {
            console.log("error in hasCustomizationByOrgId", err);
        }
    });
}

Template.registrationFlowStep1.rendered = function () {
    // Datepickers have been moved to _questionStub .rendered function
    // in order to properly capture fields that are conditionally rendered
    validNext.set(checkValidity(this));
}

Template.registrationFlowStep1.helpers({
    prefills: () => {
        const idx = Template.instance().data.currentChildIndex;
        const childrenArray = Template.instance().data.savedData?.children || [];
        return childrenArray[idx] || {}
    },
    qFirstName: () => {
        return {
            mappedTo: 'firstName',
            question: "Child's First Name",
            isRequired: true,
            type: 'text'
        }
    },
    qLastName: () => {
        return {
            mappedTo: 'lastName',
            question: "Child's Last Name",
            isRequired: true,
            type: 'text'
        }
    },
    qDob: () => {
        return {
            mappedTo: 'birthday',
            question: "Date of Birth",
            isRequired: true,
            type: 'date'
        }
    },
    qGrade: () => {
        const field = Template.instance().studentGradeField.get();
        return {
            mappedTo: field.name,
            question: field.description,
            isRequired: true,
            type: 'selectSingle',
            choices: field.values
        }
    },
    qGender: () => {
        const field = Template.instance().studentGenderField.get();
        return {
            mappedTo: field.name,
            question: field.description,
            isRequired: true,
            type: 'selectSingle',
            choices: field.values
        }
    },
    whiteLabelLogoOverride: function () {
        const host = window.location.host.split(".")[0];
        const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
        const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
        if (currentOrg && _.deep(currentOrg, "whiteLabel.assets.appLogo")) {
            return {
                small: currentOrg.whiteLabel.assets.appLogoSmall,
                large: currentOrg.whiteLabel.assets.appLogo
            };
        } else if (enabledSites && enabledSites.indexOf(host) > -1) {
            return {
                small: Meteor.settings.public.whitelabel[host].small_logo,
                large: Meteor.settings.public.whitelabel[host].large_logo
            }
        }
    },
});
Template.registrationFlowStep1.events({
    'focus .date-picker': function () {
        MiscUtils.sendHeight('lg')
    }
})
