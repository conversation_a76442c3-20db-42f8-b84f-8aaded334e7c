<template name="planDescription">
    <div class="row">
        <h4 data-cy="plan-description"><strong>{{getDescription plan}}</strong></h4>
    </div>
    {{#if hasProgramDetails plan}}
        {{> programDetails plan=plan}}
    {{/if}}
    <div class="mt-1">
        <h6 data-cy="plan-pricing">Pricing: {{priceRange plan}}</h6>
    </div>
    <div class="mt-1">
        <span data-cy="billing-frequency"><strong>Billing Frequency: </strong>{{getOrgFrequency plan}}</span>
    </div>
    {{#if hasDetails plan}}
        <div class="mt-1">
            <span><strong>Service Dates: </strong>{{getServiceDates plan}}</span>
        </div>
        <div class="mt-1">
            <span><strong>Time: </strong>{{getTime plan}}</span>
        </div>
        <div class="mt-1">
            <span><strong>Eligible Grades: </strong>{{getGrades plan}}</span>
        </div>
        <div class="mt-1 mb-4">
            <span><strong>Registration Period: </strong>{{getRegWindow plan}}</span>
        </div>
    {{/if}}
</template>
