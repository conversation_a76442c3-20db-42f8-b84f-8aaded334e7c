import { Template } from 'meteor/templating';
import { parentSource } from '../../../lib/constants/registrationConstants';
import { ReactiveDict } from 'meteor/reactive-dict';
import './_daySelection.html';

Template.daySelection.onCreated(function() {
     this.availableDays = [
        { name: 'Mon', shortName: 'mon', longName: 'monday', disabled: false },
        { name: '<PERSON><PERSON>', shortName: 'tue', longName: 'tuesday', disabled: false },
        { name: 'Wed', shortName: 'wed', longName: 'wednesday', disabled: false },
        { name: 'Thurs', shortName: 'thu', longName: 'thursday', disabled: false },
        { name: 'Fri', shortName: 'fri', longName: 'friday', disabled: false },
    ];
    this.validationData = new ReactiveDict();

    const showWeekends = this.data.parentData.showWeekends ?? false;

    if (showWeekends) {
        this.availableDays.unshift({ name: 'Sun', shortName: 'sun', longName: 'sunday', disabled: false });
        this.availableDays.push({ name: 'Sat', shortName: 'sat', longName: 'saturday', disabled: false });
    }

    const isPlanEdit = this.data.parentSource === parentSource.EDIT_PROGRAM;
    const plan = this.data.plan;
    const enrollmentMax = isPlanEdit ? plan.masterPlanInfo?.requiredEnrollmentMax : plan.requiredEnrollmentMax;
    const offeredDays = isPlanEdit ? plan.masterPlanInfo?.programOfferedOn : plan.programOfferedOn;
    if (plan && offeredDays) {
        this.availableDays.forEach(day => {
            day.disabled = !offeredDays.includes(day.longName);
        });

        if (!isPlanEdit && plan.selectedDays?.length) {
            // If not editing an enrolled plan and selectedDays is already set, then remove days that this plan does not offer
            if (plan.selectedDays.length > offeredDays.length) {
                plan.selectedDays = plan.selectedDays.filter(day => offeredDays.includes(day));
            }

            if(enrollmentMax && plan.selectedDays?.length > enrollmentMax) {
                // If selectedDays is greater than requiredEnrollmentMax, then remove the extra days
                plan.selectedDays = plan.selectedDays.slice(0, enrollmentMax);
            }
        }
    }
});

Template.daySelection.onRendered(function() {
    // Start with a valid next because the default values should be valid.
    this.data.validNext.set(true);

    // Listen for the custom event
    this.findAll('.day-checkbox').forEach((checkbox) => {
        checkbox.addEventListener('revalidate', (event) => {
            validateDayInput(event, this);
        });
    });
});

Template.daySelection.helpers({
    days() {
        return Template.instance().availableDays;
    },
    getDayName(day) {
        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            return day.shortName;
        } else {
            return day.longName;
        }
    },
    prefillDays(plan, day) {
        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            let selectedDays;
            if (Template.instance().data.bundle) {
                selectedDays = Template.instance().data.parentData.bundleSelectedDays.get();
            } else {
                selectedDays = Template.instance().data.parentData.selectedDays.get();
            }
            return !!selectedDays.includes(day.shortName);
        } else {
            return !!plan.selectedDays?.includes(day.longName);
        }
    },
    validationMessage(id) {
        const validationData = Template.instance().validationData.get(id);
        if (!validationData) {
            return '';
        }
        return validationData;
    },
    isDayDisabled(day) {
        return day.disabled ? 'disabled' : '';
    },
});

Template.daySelection.events({
    'click .day-checkbox'(event, instance) {
        validateDayInput(event, instance);
    },
});

/**
 * Validates inputs and shows/hides validation message.
 * @param {Event} event - The event object.
 * @param instance  - The template instance.
 */
function validateDayInput(event, instance) {
    const target = $(event.currentTarget)[0];
    const type = $(event.currentTarget).data('type');
    const id = $(event.currentTarget).data('id');
    const plan = instance.data.plan;
    const isPlanEdit = instance.data.parentSource === parentSource.EDIT_PROGRAM;
    const enrollmentMax = isPlanEdit ? plan.masterPlanInfo?.requiredEnrollmentMax : plan.requiredEnrollmentMax;
    const enrollmentMin = isPlanEdit ? plan.masterPlanInfo?.requiredEnrollmentMin : plan.requiredEnrollmentMin;
    let valid = true;
    let message = '';

    const parentNode = target.closest('.select-multi-group');
    const checked = parentNode.querySelectorAll('.select-multi-option:checked');

    if (plan && enrollmentMax) {
        if (checked.length > enrollmentMax) {
            valid = false;
            message = `You may only select ${enrollmentMax} days of attendance.`;
        }
    }

    if (plan && enrollmentMin) {
        if (checked.length < enrollmentMin) {
            valid = false;
            message = `You are required to select at least ${enrollmentMin} days.`;
        }
    }

    if (checked.length === 0) {
        valid = false;
        const dayCount = enrollmentMin && enrollmentMin > 1 ? enrollmentMin + ' ' + 'days' : 'one day';
        message = `You are required to select at least ${dayCount}.`;
    }

    if (valid) {
        instance.data.validNext.set(true);
        instance.validationData.set(id, null);
        $(`#validate-${type}-${id}`).hide();
    } else {
        instance.data.validNext.set(false)
        instance.validationData.set(id, message);
        $(`#validate-${type}-${id}`).show();
    }
}
