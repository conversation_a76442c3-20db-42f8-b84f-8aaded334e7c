<template name="registrationFlowStep4">
    <div class="row justify-content-between align-items-center">
        <h2>What days would you like to attend?</h2>
        {{#if whiteLabelLogoOverride}}
            <img alt="Logo" src="{{whiteLabelLogoOverride.small}}" class="max-w-80px max-h-136px rounded-big-logo" />
        {{else}}
            <img alt="Logo" src="/media/svg/icons/ll_brandmark.svg" class="max-w-80px max-h-136px rounded-big-logo" />
        {{/if}}
    </div>
    <div class="mt-5">
        {{# if showLinkBundle }}
            <div class="btn btn-primary font-weight-bolder" id="btnAddMissingPlan" style="width: 100%;">
                Save on {{getPlanToAdd.otherPlanDescription}} by adding {{ getPlanToAdd.description }} -- Tap to explore!
            </div>
        {{/ if }}
        {{# if planAdded }}
            <div class="btn btn-secondary font-weight-bolder" id="btnRemovePlan" style="width: 100%;">
                Tap to remove {{ getPlanToAdd.description }}
            </div>
        {{/ if }}
        {{#each plan in plans}}
            <form data-cy="plan-card" id="{{plan._id}}" class="card registration-card day-card my-2" data-id="{{plan._id}}">
                <div class="card-body">
                    {{> planDescription plan=plan parentData=parentData parentSource='initialRegistration'}}
                    {{> daySelection plan=plan parentData=parentData parentSource='initialRegistration' validNext=validNext}}
                    {{> effectiveDate plan=plan parentData=parentData parentSource='initialRegistration'}}
                    {{#if isScaledAmount plan._id}}
                        <div class="w-100 d-flex">
                            <div class="card text-center min-w-275px d-inline-block mx-auto mt-10 p-7" style="color: var(--primary);">
                                <span><strong> Price </strong></span>
                                <h1><strong> ${{planAmount plan._id}} </strong></h1>
                                {{#if isAllDaysSelected plan._id}}
                                <p class="save-more-text-{{plan._id}} d-block"> Save more per day by adding more days </p>
                                {{/if}}
                            </div>
                        </div>
                    {{/if}}
                </div>
            </form>
        {{/each}}
        {{#if showSavings}}
            <div class="card registration-card day-card h3 my-2 py-4">
                <div class="row ml-1 font-weight-bold">
                    <div class="col-auto text-right">
                        <div class="row">
                            <div class="col">
                                {{ formatCurrency regularPrice }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                {{ formatCurrency bundledPrice }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col" style="color: var(--primary);">
                                {{ formatCurrency bundleSavings }}
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="row">
                            <div class="col">
                                Cost of plans if purchased separately
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                Bundled Price
                            </div>
                        </div>
                        <div class="row">
                            <div class="col" style="color: var(--primary);">
                                Your savings!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{/if}}
    </div>
</template>