<template name="daySelection">
    <div class="row d-flex justify-content-between select-multi-group" data-required="true">
        {{#each day in days}}
            <div class="col-auto d-flex flex-column align-items-center">
                <h5>{{day.name}}</h5>
                <input name="{{getDayName day}}"
                       class="select-multi-option big-checkbox day-checkbox"
                       type="checkbox"
                       checked="{{prefillDays plan day}}"
                       data-cy="{{day.longName}}-checkbox"
                       data-type="days"
                       data-id="{{plan._id}}"
                    {{isDayDisabled day}}>
                {{#unless day.disabled}}
                    <div data-id="{{plan._id}}" data-type="unavailable-note" class="d-none">Unavailable:<br>At Capacity
                    </div>
                {{/unless}}
                {{#if day.disabled}}
                    <div data-type="unavailable-note">Unavailable:<br>Program is not offered</div>
                {{/if}}
            </div>
        {{/each}}
    </div>
    <div id="validate-days-{{plan._id}}" class="invalid-feedback font-weight-bolder text-center" style="display: none" data-cy=no-days-warning>
        <h5>{{ validationMessage plan._id }}</h5>
    </div>
</template>
