import { Template } from 'meteor/templating';
import './_planDescription.html';
import { MiscUtils } from '../../../lib/util/miscUtils';
import moment from 'moment-timezone';
import { parentSource } from '../../../lib/constants/registrationConstants';
import './_planDescription.html';
import '../people/programs/_programDetails.js'

Template.planDescription.helpers({
    getDescription: (plan) => {
        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            return plan.planDetails.description;
        } else {
            return plan.description;
        }
    },

    priceRange: (plan) => {
        const isEditProgram = Template.instance().data.parentSource === parentSource.EDIT_PROGRAM;
        const selectedPlans = Template.instance().data.parentData.selectedPlans;
        const availablePlans = Template.instance().data.parentData.availablePlans;
        const bundles = Template.instance().data.parentData.bundles;
        const timezone = Template.instance().data.parentData.orgTimezone;
        return MiscUtils.getPriceRange((isEditProgram ? plan.planDetails : plan), selectedPlans, availablePlans, bundles, timezone);
    },

    getOrgFrequency: (plan) => {
        let frequencyType;

        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            frequencyType = plan.planDetails.frequency;
        } else {
            frequencyType = plan.frequency;
        }

        if (!frequencyType) {
            return 'One-time charge, paid at checkout.'
        }

        const frequencies = Template.instance().data.parentData.availableBillingFrequencies;
        return frequencies.find(af => af.type === frequencyType)?.description;
    },

    hasDetails: (plan) => {
        if(Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            return !!plan.planDetails.details;
        } else {
            return !!plan.details
        }
    },

    getServiceDates: (plan) => {
        let details;

        if(Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            details = plan.planDetails.details;
        } else {
            details = plan.details;
        }

        if (details) {
            const timezone = Template.instance().data.parentData.orgTimezone;
            if (details.dateType === 'dateRange' && details.serviceStartDate && details.serviceEndDate) {
                const dateRangeStartDate = moment.tz(details.serviceStartDate, timezone).format('MM/DD/YYYY')
                const dateRangeEndDate = moment.tz(details.serviceEndDate, timezone).format('MM/DD/YYYY')
                return `${dateRangeStartDate}-${dateRangeEndDate}`;
            } else if (details.dateType === 'recurring' && details.recurringStartDate && details.recurringFrequency && details.recurringOccurrences) {
                const startingDate = moment.tz(details.recurringStartDate, timezone).format('MM/DD/YYYY')
                const recurringFrequency = details.recurringFrequency
                const recurringOccurrences = details.recurringOccurrences
                return `Starting ${startingDate}, Repeats every ${recurringFrequency} week(s) for ${recurringOccurrences} occurrences`
            } else if (details.dateType === 'individualDates' && details.individualDates) {
                const individualDates = details.individualDates;
                const formattedDates = [];
                individualDates.forEach(date => {
                    const formatDate = moment.tz(date, timezone).format('MM/DD/YYYY')
                    formattedDates.push(formatDate)
                })
                return `${formattedDates.join(" , ")}`
            }

            if (details.dateType === 'timePeriod' && details.timePeriod) {
                const timePeriods = Template.instance().data.parentData.availableTimePeriods;
                const filteredTimePeriods = timePeriods.find(timeperiod => timeperiod._id === details.timePeriod)
                if (filteredTimePeriods) {
                    const serviceStartDate = moment.tz(filteredTimePeriods.startDate, timezone).format('MM/DD/YYYY')
                    const serviceEndDate = moment.tz(filteredTimePeriods.endDate, timezone).format('MM/DD/YYYY')
                    return `${serviceStartDate}-${serviceEndDate}`
                }
            }
        }
    },

    getTime: (plan) => {
        let details;

        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            details = plan.planDetails.details;
        } else {
            details = plan.details;
        }

        if (details && details.startTime && details.endTime) {
            return `${details.startTime}-${details.endTime}`;
        }
    },

    getGrades: (plan) => {
        let details;

        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            details = plan.planDetails.details;
        } else {
            details = plan.details;
        }

        if (details && details.grades) {
            return details.grades.join(",")
        }
    },

    getRegWindow: (plan) => {
        let details;
        if (Template.instance().data.parentSource === parentSource.EDIT_PROGRAM) {
            details = plan.planDetails.details;
        } else {
            details = plan.details;
        }

        if (details && details.regStartDate && details.regEndDate) {
            const timezone = Template.instance().data.parentData.orgTimezone;
            const formattedStartDate = moment.tz(details.regStartDate, timezone).format('MM/DD/YYYY')
            const formattedEndDate = moment.tz(details.regEndDate, timezone).format('MM/DD/YYYY')

            return `${formattedStartDate}-${formattedEndDate}`
        }
    },
    hasProgramDetails: (plan) => {
        if (!plan.programDetails) {
            return false;
        }
        return true;
    },  
});