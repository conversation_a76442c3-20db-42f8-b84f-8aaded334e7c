<template name="registrationPaymentAddCard">
    <div id="customCard-container">
        <label>
            <span>Card number:</span>
            <span data-cy="card-number-input" class="customCard-field" data-cse="encryptedCardNumber"></span>
        </label>
        <label>
            <span>Expiration date:</span>
            <span data-cy="expiration-date-input" class="customCard-field"  data-cse="encryptedExpiryDate"></span>
        </label>
        <label>
            <span>CVV/CVC:</span>
            <span data-cy="card-security-code-input" class="customCard-field"  data-cse="encryptedSecurityCode"></span>
        </label>
    </div>
</template>

<template name="registrationPaymentAddBankAccount">
    <br/>
    <div class="text-dark">
        <b>Note:</b> Only US-based checking accounts are supported at this time. All other accounts (including savings) will result in a returned payment.<br/>
    </div>
    <br/>
    <form id="frmPayment">
        <div id="ach-component-container"></div>
    </form>
    <div class="checkbox-list text-dark">
        <label data-cy="certify-account" class="checkbox checkbox-primary">
            <input type="checkbox" class="form-check-input" name="is-checking" >
            <span></span>
            I certify that the above account is a US-based checking account and understand that all other account types will result in a returned payment.
        </label>
    </div>
</template>