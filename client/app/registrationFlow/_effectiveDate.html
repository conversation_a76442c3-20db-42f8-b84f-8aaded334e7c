<template name="effectiveDate">
    <div class="row text-center justify-content-center mt-10">
        <label class="mt-2"><h5><strong>Effective date: </strong></h5></label>
        <div class="input-group col col-xl-4 col-sm-5">
            <input name="startDate" type="text"
                   class="form-control date-picker border-right-0 {{initDatePicker plan}}"
                   value="{{prefillStartDate plan}}"
                   data-required="true"
                   data-type="date"
                   data-id="{{plan._id}}"
                   data-cy="effective-date-picker"
                   >
            <div class="input-group-append">
        <span class="input-group-text" style="background-color: var(--secondary); border: none;">
          <i class="fad-regular fad fa-calendar"></i>
        </span>
            </div>
            <div id="validate-date" class="invalid-feedback text-left" style="display: none">
                {{plan.dateErrorMessage}}
            </div>
        </div>
    </div>
</template>
