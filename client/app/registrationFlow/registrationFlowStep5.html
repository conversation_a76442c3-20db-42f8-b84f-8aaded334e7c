<template name="registrationFlowStep5">
    {{ showPayModal }}
    <div class="mt-5 mb-4">
    <div class="row justify-content-between align-items-center">
        <h2>Summary</h2>
        {{#if whiteLabelLogoOverride}}
        <img alt="Logo" src="{{whiteLabelLogoOverride.small}}" class="max-w-80px max-h-136px rounded-big-logo" />
        {{else}}
        <img alt="Logo" src="/media/svg/icons/ll_brandmark.svg" class="max-w-80px max-h-136px rounded-big-logo" />
        {{/if}}
    </div>
    {{#if isLoaded }}
    <div class="mt-5">
            {{#each children}}
                {{#let childIndex=@index}}
                    <div class="card card-custom card-flat mb-2">
                        <div class="card-body px-0">
                            <div class="row col-md-12 align-items-center pr-0">
                                <div
                                        class="d-flex avatar-circle avatar-small align-items-center justify-content-center"
                                        style="background-color: {{ getAvatarBackground initials}}"
                                >
                                    <span class="initials initials-search">{{ initials }}</span>
                                </div>
                                <div class="col-md-6">
                                        <a data-cy="child-name" href="" id="editChild" data-id="{{childIndex}}" class="mb-4 font-weight-bolder" style="font-size: 18px; line-height: 22px;">{{ fullName }}<i class="ml-2 mt-1 fad fad-primary fa-pencil" data-id="{{childIndex}}"></i> </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{#each program in programs childIndex}}
                        <h3 class="ml-4">
                           <span data-cy="program-name" class="font-weight-bolder"> {{ program.name }}</span>
                        </h3>
                        {{#each plan in plans childIndex program._id}}
                           {{> planSummaryCard plan=plan childIndex=childIndex parentData=parentData}}
                        {{/each}}
                    {{/each}}
                    <div class="row ml-4">
                        <a data-cy="add-edit-program" href="" id="addProgram" class="mb-8 font-weight-bolder" data-id="{{childIndex}}"><i class="fad fa-plus-circle mr-2"></i>Add or Edit Program(s)</a>
                    </div>
                {{/let}}
            {{/each}}
            <div class="row mb-4 ml-1">
                <a data-cy="add-child-btn" href="" id="addChild" class="mb-4 font-weight-bolder"><i class="fad fa-plus-circle mr-2"></i>Add Child</a>
            </div>
        </div>
    <h2>Order Summary</h2>
    <div class="mt-5">
        {{> addDiscountDropdown source='initialRegistration' regSettings=getRegSettings showEmployeeId=showEmployeeId}}
        {{#if hasEmployeeDiscount}}
            <div class="row no-gutters mb-2 ml-1">
                <label class="mr-4">Right at School Employee ID</label>
                <a href="" id="employeeIdLink" class="font-weight-bolder mr-2">{{employeeId}}</a>
                <a href="" id="deleteEmployeeDiscount" class="fad-regular fad-primary fad fa-lg fa-times mt-1"></a>
            </div>
        {{/if}}
        <div class="row mt-8 mb-2 ml-1">
            <table class="table">
                {{#each summaryChildPlans in summaryChildrenPlans}}
                    {{#let childIndex=@index}}
                        {{#each plan in summaryChildPlans}}
                            <tr>
                                <td>
                                    <strong>{{summaryChildFullName childIndex}} </strong> {{plan.description}}
                                </td>
                                <td></td>
                                <td style="text-align:right">
                                    {{ getPlanOriginalPrice plan }}
                                </td>
                                <td></td>
                            </tr>
                            {{#if plan.allocations}}
                                {{#each allocation in plan.allocations}}
                                      {{> planAllocation allocation=allocation type='summary'}}
                                {{/each}}
                            {{/if}}
                            <tr>
                                <td colspan=3>{{getPlanType plan}} Total:</td>
                                <td style="text-align:right">{{ getPlanDiscountedPrice plan }}</td>
                            </tr>
                        {{/each}}
                    {{/let}}
                {{/each}}
                <tr>
                    <td colspan=3><strong>Registration Fee:</strong></td>
                    <td style="text-align:right">{{getRegistrationFeeAmount}}</td>
                </tr>
                {{#if showRegistrationFeeDiscountsAndTotal}}
                    {{#each allocation in registrationAllocations}}
                     {{> planAllocation allocation=allocation type='summary'}}
                    {{/each}}
                    <tr>
                        <td colspan=3>Item Total:</td>
                        <td style="text-align:right">{{getRegistrationFeeTotal}}</td>
                    </tr>
                {{/if}}
                <tr>
                    <td colspan=3>Due Today: </td>
                    <td data-cy="due-today-amount" style="text-align:right"> {{formatCurrency dueTodayCharges}} </td>
                </tr>
                <tr>
                    <td colspan=3>Future Charges: </td>
                    <td style="text-align: right"> {{ getFutureDue }} </td>
                </tr>
                <tr style="border-top:1.5px solid #000;">
                    <td colspan=3><b>Total:</b></td>
                    <td data-cy="total-amount-to-pay" style="text-align:right"><b>{{ totalCharges }}</b></td>
                </tr>
            </table>
        </div>
    </div>
    {{else}}
        {{> loading}}
    {{/ if }}
    </div>
</template>

<template name="registrationFlowStep5AddCouponModal">
    <div id="registrationFlowStep5AddCouponModal" class="modal fade">
        <div class="modal-dialog modal-dialog-scrollable modal-lg" >
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header">
                    <h5 class="modal-title">Enter in your Coupon Code</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <form id="addCouponForm">
                        <div class="form-group row">
                                <label class="col-xl-3 col-lg-3 col-md-3 col-sm-3 text-right col-form-label">Coupon Code</label>
                                <div class="col-lg-6 col-md-6 col-sm-3">
                                    <input data-cy="coupon-code-input" type="text" class="form-control" name="code" maxlength="120" value="{{code}}">
                                </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button data-cy="apply-coupon-btn" type="button" class="btn btn-primary font-weight-bolder mr-2" id="applyCoupon" data-stayopen="false" disabled="{{submitButtonDisabled}}">Apply</button>
                    <button data-cy="close-modal-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</template>
<template name="registrationFlowStep5AddEditEmployeeIdModal">
    <div id="registrationFlowStep5AddEditEmployeeIdModal" class="modal fade">
        <div class="modal-dialog modal-dialog-scrollable modal-lg" >
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header">
                    <h5 class="modal-title">{{orgLongName}} Employee Id</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <form id="employeeIdForm">
                        <div class="form-group row">
                            <label class="col-xl-4 col-lg-3 col-md-3 col-sm-3 text-right col-form-label">{{orgLongName}} Employee Id</label>
                            <div class="col-lg-6 col-md-6 col-sm-3">
                                <input data-cy="employee-id-input" type="text" class="form-control" name="employee-id" maxlength="120" value="{{employeeIdValue}}">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button data-cy="employee-id-apply-btn" type="button" class="btn btn-primary font-weight-bolder mr-2" id="validateEmployeeId" data-stayopen="false" disabled="{{submitButtonDisabled}}">Apply</button>
                    <button data-cy="employee-id-close-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</template>

<template name="registrationFlowStep5PaymentModal">
    <div id="registrationFlowStep5PaymentModal" class="modal fade" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-dialog-scrollable modal-lg" >
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header">
                    <h5 class="modal-title">Please Fill Out Your Payment Method</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg cancel-payment" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    <div class="row mb-4">
                        <div class="col">
                            <label>Amount Due:</label>
                            {{ formatCurrency paymentAmount }}<br/>
                        </div>
                    </div>
                    {{# if showServiceChargeNotice }}
                        <div class="row mb-6">
                            <div class="col">
                              {{> feeVerbiage currentOrg=currentOrg paymentAmount=paymentAmount}}
                            </div>
                        </div>
                    {{/ if }}
                    {{#if showAutoPay}}
                    <div class="row mb-6">
                        <div class="col">
                            By entering in your payment method, you will automatically be enrolled in autopay. You may
                            change your autopay preferences on your profile once you have created your app account.
                        </div>
                    </div>
                        {{/if}}
                    <form id="paymentForm" class="form-horizontal" role="form">
                        <div class="form-group row">
                            {{#if showAchPayment }}
                                <div class="col d-flex justify-content-end">
                                    <label data-cy="bank-account-radio" class="radio-inline mr-12">
                                        <input type="radio" name="method" class="form-check-input" value="bank">
                                        <span></span>
                                        Bank
                                    </label>
                                </div>
                            {{/if}}
                            <div class="col d-flex {{#if showAchPayment}}justify-content-start{{else}}justify-content-center{{/if}}">
                                <label data-cy="credit-card-radio" class="radio-inline">
                                    <input type="radio" name="method" class="form-check-input" value="cc">
                                    <span></span>
                                    Credit Card
                                </label>
                            </div>
                        </div>
                    </form>
                    <div {{ hiddenIfEqual showBankForm false }}>
                        {{> registrationPaymentAddBankAccount personId=personId orgData=orgData }}
                    </div>
                    <div {{ hiddenIfEqual showCcForm false }}>
                        {{> registrationPaymentAddCard personId=personId orgData=orgData }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-cy="pay-now-btn-modal" type="button" class="btn btn-primary font-weight-bolder mr-2" id="payNow" data-stayopen="false" aria-disabled="true" disabled={{paymentProcessing}}>Pay Now</button>
                    <button type="button" class="btn btn-secondary font-weight-bolder cancel-payment" id="cancelPayment" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</template>
