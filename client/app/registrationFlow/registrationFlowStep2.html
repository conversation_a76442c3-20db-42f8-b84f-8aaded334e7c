<template name="registrationFlowStep2">
    <div class="row justify-content-between align-items-center">
        <h2>{{ header }}</h2>
        {{#if whiteLabelLogoOverride}}
        <img alt="Logo" src="{{whiteLabelLogoOverride.small}}" class="max-w-80px max-h-136px rounded-big-logo" />
        {{else}}
        <img alt="Logo" src="/media/svg/icons/ll_brandmark.svg" class="max-w-80px max-h-136px rounded-big-logo" />
        {{/if}}
    </div>
    {{# if isSummaryView }}
        {{# if showContactsCountLine }}
            <div class="mt-n2 mb-4" style="color: gray;">{{ requiredContactsCountText }}</div>
        {{/if}}
        {{# if showPrimaryContactLine }}
            <div data-cy="required-primary-contact-warning" class="mt-n2 mb-4" style="color: var(--danger);">{{ requiredPrimaryContactText }}</div>
        {{/ if }}
        <h3>Primary Caregivers</h3>
        {{# each contact in contacts }}
            <div class="row mb-2">
                <div class="col d-flex align-items-center">
                    <label class="checkbox checkbox-primary">
                        {{# if isPrimaryCaregiver @index }}
                            <input data-cy="primary-caregiver-checkbox" type="checkbox" class="primary-caregiver-checkbox" data-id="{{ contact.profileEmailAddress }}" checked>
                        {{ else }}
                            <input data-cy="primary-caregiver-checkbox" type="checkbox" class="primary-caregiver-checkbox" data-id="{{ contact.profileEmailAddress }}" >
                        {{/ if }}
                        <span></span>
                        <a data-cy="parent-link-primary" href="" class="contact-summary-row ml-2" data-id="{{ @index }}">{{ contact.firstName }} {{ contact.lastName }}</a>
                    </label>
                </div>
            </div>
        {{/ each }}
        <h3 class="mt-4">Authorized Pickups</h3>
        {{# each contact in contacts }}
            <div class="row mb-2">
                <div class="col d-flex align-items-center">
                    <label class="checkbox checkbox-primary">
                        {{# if isAuthorizedPickup @index }}
                            <input data-cy="authorized-pickup-checkbox" type="checkbox" class="authorized-pickup-checkbox" data-id="{{ contact.profileEmailAddress }}" checked>
                        {{ else }}
                            <input type="checkbox" class="authorized-pickup-checkbox" data-id="{{ contact.profileEmailAddress }}" >
                        {{/ if }}
                        <span></span>
                        <a data-cy="authorized-link" href="" class="contact-summary-row ml-2" data-id="{{ @index }}">{{ contact.firstName }} {{ contact.lastName }}</a>
                    </label>
                </div>
            </div>
        {{/ each }}
        <h3 class="mt-4">Emergency Contacts</h3>
        {{# each contact in contacts }}
            <div class="row mb-2">
                <div class="col d-flex align-items-center">
                    <label class="checkbox checkbox-primary">
                        {{# if isEmergencyContact @index }}
                            <input data-cy="emergency-contact-checkbox" type="checkbox" class="emergency-contact-checkbox" data-id="{{ contact.profileEmailAddress }}" checked>
                        {{ else }}
                            <input type="checkbox" class="emergency-contact-checkbox" data-id="{{ contact.profileEmailAddress }}" >
                        {{/ if }}
                        <span></span>
                        <a data-cy="emergency-link" href="" class="contact-summary-row ml-2" data-id="{{ @index }}">{{ contact.firstName }} {{ contact.lastName }}</a>
                    </label>
                </div>
            </div>
        {{/ each }}
        <div class="row">
            <div class="col text-center">
                <button data-cy="add-contacts-btn" class="btn btn-primary mb-4" id="addContact">
                    Add Additional Contacts
                </button>
            </div>
        </div>
    {{ else }}
    {{> _relationshipFormFields useFullWidthColumns=true rowClass="" prefills=prefills}}
    <div id="relationship-group" class="select-multi-group" data-required="true">
        <div class="row">
            <div class="col">
                {{> registrationQuestionStub question=qPrimaryCaregiver prefill=prefills class=caregiverClass }}
            </div>
        </div>
        <div class="row">
            <div class="col">
                {{> registrationQuestionStub question=qAuthorizedPickup prefill=prefills class=pickupClass }}
            </div>
        </div>
        <div class="row">
            <div class="col">
                {{> registrationQuestionStub question=qEmergencyContact prefill=prefills class=emergencyContactClass }}
            </div>
        </div>
    </div>
        <div id="relationship-group-validation" class="invalid-feedback mb-4 mt-n7" style="display: none">
            You are required to select at least one relationship type.
        </div>
        <div class="row">
            <div class="col">
                {{> registrationQuestionStub question=qCopy prefill=prefills displayElement=displayCopy}}
            </div>
        </div>
    {{/ if }}
</template>