<template name="registrationFlow">
  {{#if invalid}}
  Invalid org
  {{/if}}
  {{#if isLoading}}
    <div class="text-center">
      <i class="fa fa-spinner fa-spin fa-4x" style="color: var(--primary);"></i>
    </div>
  {{/if}}
  {{#if settings}}
      <div class="d-flex flex-column registration-flow-wrapper px-4">
        <div class="col-lg-6 col-md-8 offset-lg-3 offset-md-2 mt-4">
          {{> Template.dynamic template=currentTemplate data=data }}
        </div>
        {{#if disabledSubmitButtons}}
          <div class="d-flex align-items-center justify-content-center">
            <p class="mr-2 mt-1">Do not refresh</p>
          </div>
        {{/if}}
        <div class="d-flex justify-content-center">
          <div class="d-flex flex-row align-self-end mb-7 mt-4">
              {{#if showBack}}
                  <button data-cy="back-btn" class="btn btn-secondary mr-2" id="registrationFlowBack">
                      Go Back
                  </button>
              {{/if}}
              {{#if showNext}}
                  <button data-cy="continue-btn" class="btn btn-primary mr-2 {{nextDisabled}}" id="registrationFlowNext">
                      Continue
                  </button>
              {{/if}}
              {{#if showSave}}
                  <button class="btn btn-primary mr-2 {{nextDisabled}}" id="registrationFlowSave">
                      Save
                  </button>
              {{else}}
                  {{#if showSubmit}}
                    {{#if disabledSubmitButtons}}
                          <button data-cy="disabled-pay-btn" class="btn btn-primary mr-2 disabled" disabled={{disabledSubmitBtn}}>
                              Please wait
                          </button>
                    {{else}}
                        {{#if owesNothing}}
                          <button data-cy="submit-btn" class="btn btn-primary mr-2 {{nextDisabled}}" id="registrationFlowSubmit" disabled={{disabledSubmitBtn}}>
                              Submit
                          </button>
                        {{else}}
                          <button data-cy="pay-now-btn" class="btn btn-primary mr-2 {{nextDisabled}}" id="registrationFlowSubmit" disabled={{disabledSubmitBtn}}>
                              Pay now
                          </button>
                        {{#if showPayLater}}
                          <button class="btn btn-primary mr-2 {{nextDisabled}}" id="btnPayLater">
                              Send invoice to parent to complete payment
                          </button>
                        {{/if}}
                      {{/if}}
                    {{/if}}
                  {{/if}}
              {{/if}}
          </div>
        </div>
      </div>
  {{/if}}
</template>