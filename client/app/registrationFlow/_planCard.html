<template name="planCard">
    {{# unless allWeeksHidden plan}}
    <div class="card registration-card plan-card {{activePlan plan}}" data-id="{{plan._id}}">
        <div class="card-body" style="cursor: pointer;">
            <div>
                <h3><strong>{{plan.description}}</strong></h3>
            </div>
            {{#if hasProgramDetails plan}}
                {{> programDetails plan=plan cursor="pointer"}}
            {{/if}}
            <div class="mt-1">
                <h6>Pricing: {{priceRange plan}}</h6>
            </div>
            {{#unless isSelectiveWeekPlan plan}}
            <div class="mt-1">
                <span><strong>Billing Frequency: </strong>{{getOrgFrequency plan.frequency}}</span>
            </div>
            {{else}}
            <div class="mt-1">
                <span><strong>Billing Frequency: </strong> Charged weekly based on weeks selected</span>
            </div>
            {{/unless}}
            {{#if hasDetails plan}}
                <div class="mt-1">
                    <span><strong>Service Dates: </strong>{{plan.serviceDates.response}}</span>
                </div>
                <div class="mt-1">
                    <span><strong>Time: </strong>{{getTime plan}}</span>
                </div>
                <div class="mt-1">
                    <span><strong>Eligible Grades: </strong>{{getGrades plan}}</span>
                </div>
                <div class="mt-4">
                    <span><strong>Registration Period: </strong>{{getRegWindow plan}}</span>
                </div>
                {{#if isSelectiveWeekPlan plan}}
                    {{#each weeks plan}}
                        {{# unless isWeekHidden ../plan @index }}
                            <div class="card week-card my-4 {{activeWeek ../plan @index}}" data-id="{{../plan._id}}" data-week-index="{{@index}}">
                                <div class="card-body">
                                    <div>
                                        <h3><strong>{{../plan.description}} - Week {{getWeekNum @index}}</strong></h3>
                                    </div>
                                    <div class="mt-1">
                                        <h6>Pricing: {{priceRange ../plan @index}}</h6>
                                    </div>
                                    <div class="mt-1">
                                        <span><strong>Service Dates: </strong>{{formatWeekDates this}}</span>
                                    </div>
                                    <div class="mt-1">
                                        <span><strong>Time: </strong>{{getTime ../plan}}</span>
                                    </div>
                                </div>
                            </div>
                        {{/ unless }}
                    {{/each}}
                {{/if}}
            {{/if}}
        </div>
    </div>
    {{/unless}}
</template>