import { showModal } from '../../main';
import '../../people/checkout/addSubsidyModal';


export class RegistrationFlowUtils {

    /**
     * Add subsidy create modal
     */
    static addSubsidyModal(data, topLinePercentDiscounts, registrationFlowType, rerenderRegistration) {
        mpSwal.fire({
            title: 'Notice',
            text: 'Entering subsidy information ends the registration process. If you have coupon codes or employee discounts you have not entered yet, please go back and enter those and then come back here to enter your subsidy information. Please note that coupons/discounts cannot be combined with subsidy.',
            showCancelButton: true,
            confirmButtonText: 'Enter Subsidy',
            cancelButtonText: 'Go Back'
        }).then ( result => {
            if (result.value) {
                showModal('addSubsidyModal', {
                    savedData: data,
                    topLinePercentDiscounts: topLinePercentDiscounts,
                    rerenderRegistration: rerenderRegistration,
                    registrationFlowType: registrationFlowType
                },  '#addSubsidyModal');
            }
        });
    }

        /**
     * Add district employee email
     */
    static addDistrictEmployeeEmailModal(data, topLinePercentDiscounts, registrationFlowType, rerenderRegistration) {
        mpSwal.fire({
            title: 'Notice',
            text: 'Entering District Employee Email information ends the registration process. If you have additional information to enter, please go back and enter those and then come back here to enter your district employee email',
            showCancelButton: true,
            confirmButtonText: 'Enter District Employee Email',
            cancelButtonText: 'Go Back'
        }).then ( result => {
            if (result.value) {
                showModal('addSubsidyModal', {
                    savedData: data,
                    topLinePercentDiscounts: topLinePercentDiscounts,
                    rerenderRegistration: rerenderRegistration,
                    showDistrictEmail: true,
                    registrationFlowType: registrationFlowType
                }, '#addSubsidyModal');
            }
        });
    }

    /**
     * Build pay modal template data.
     *
     * @param templateInstance
     * @param availableBankAccount
     * @param availableCreditCard
     * @param currentOrg
     */
    static buildPayModalTemplateData(templateInstance, availableBankAccount, availableCreditCard, currentOrg) {
        const passThroughFees = currentOrg.billing.passthroughFees;
        const passThroughFeesAccountTypes = passThroughFees && currentOrg.billing.passthroughFeesAccountTypes;

        return {
            disableBankAccount: !availableBankAccount,
            disableCreditCard: !availableCreditCard,
            paymentAmount: templateInstance.dueTodayCharges.get(),
            showServiceChargeNotice: passThroughFees,
            showServiceChargeCreditCardsNotice: !passThroughFeesAccountTypes || (passThroughFeesAccountTypes || []).includes('card'),
            showServiceChargeBankAccountsNotice: !passThroughFeesAccountTypes || (passThroughFeesAccountTypes || []).includes('bank_account'),
            cardFeeNotice: currentOrg.cardFeeNotice(),
            achFeeNotice: currentOrg.achFeeNotice(),
            alternateServiceChargeFeeDescription: currentOrg.alternateServiceChargeFeeDescription(),
            personId: templateInstance.data.parent._id,
            showPayModal: templateInstance.data.showPayModal,
            disabledSubmitBtn: templateInstance.data.disabledSubmitBtn,
            rerenderRegistration: templateInstance.data.rerenderRegistration
        };
    }
}