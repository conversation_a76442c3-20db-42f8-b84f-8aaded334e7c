import { Template } from 'meteor/templating';
import './_planAllocation.html';
import { DiscountTypes } from '../../../lib/discountTypes';
import './_planAllocation.html';

Template.planAllocation.helpers({
    allocationType: (allocation) => {
        if (!allocation || !allocation.discountType) {
            if (allocation?.allocationDescription) {
                return allocation.allocationDescription;
            }
            return '';
        }
        switch (allocation.discountType) {
            case DiscountTypes.COUPON:
                return 'Coupon Code';
            case DiscountTypes.RAS_EMPLOYEE:
                return 'Employee Discount';
            case DiscountTypes.STAFF:
                return 'Employee Discount';
            case DiscountTypes.SIBLING:
                return 'Sibling Discount';
        }
        return '';
    },
    allocationContent: (allocation) => {
        if (!allocation || !allocation.code) {
            return '';
        }
        return allocation.code;
    },
});