<template name="_programDaysPlanInfo">
    <div class="card-body">
        {{> planDescription plan=plan parentData=parentData parentSource='addProgram'}}
        {{> daySelection plan=plan parentData=parentData parentSource='addProgram' validNext=validNext}}
        {{> effectiveDate plan=plan parentData=parentData parentSource='addProgram'}}
        {{#if isScaledAmount plan._id}}
            <div class="w-100 d-flex">
                <div class="card text-center min-w-275px d-inline-block mx-auto mt-10 p-7" style="color: var(--primary);">
                    <span><strong> Price </strong></span>
                    <h1 data-cy="plan-amount"><strong> ${{planAmount plan._id}} </strong></h1>
                    {{#if isAllDaysSelected plan._id}}
                        <p class="save-more-text-{{plan._id}} d-block"> Save more per day by adding more days </p>
                    {{/if}}
                </div>
            </div>
        {{/if}}
    </div>
</template>