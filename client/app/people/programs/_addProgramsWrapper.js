import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Tracker } from 'meteor/tracker';
import { Session } from 'meteor/session';
import './_addProgramsWrapper.html';
import '../_addPrograms';
import './_addProgramsSummary';
import './_programDays';
import { RegistrationUtils } from '../../../../lib/util/registrationUtils';
import {
    applyExistingAllocationsToNewPlans,
    applyExistingAllocationsToRegistrationFee,
    checkForCoupons,
    checkValidity,
    couponCodes,
    employeeDiscount,
    findAllocations,
    focusAll,
    registrationDataSessionName,
    validNext
} from '../../registrationFlow/registrationFlow';
import { ReactiveVar } from 'meteor/reactive-var';
import moment from 'moment-timezone';
import { ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE } from '../../../../lib/constants/billingConstants';
import { DiscountTypes } from '../../../../lib/discountTypes';
import { cloneDeep } from 'lodash';
import { Log } from 'meteor/logging';
import { RegistrationDesignationUtils } from "../../../../lib/registrationDesignationUtils";
import { Orgs } from '../../../../lib/collections/orgs';

const steps = {
    SELECT_PLANS: 1,
    SELECT_DAYS: 2,
    SUMMARY: 3
}

Template._addProgramsWrapper.onRendered(async function () {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectedProgramRaw = urlParams.get('program');
    
    if (redirectedProgramRaw) {
        $('.nav-tabs a[href="#programs"]').tab('show');
    }
});

Template._addProgramsWrapper.onCreated(async function () {
    Session.set('summaryLoaded', false);
    const self = this;
    this.addingProgram = new ReactiveVar(true); // RAS-15 This should always be true since the program tab is only used to add programs as of now.
    this.editingChild = new ReactiveVar(false); // RAS-15 Always false for now, but this is a required variable in checking siblingDiscount
    this.currentStep = new ReactiveVar(steps.SELECT_PLANS);
    this.maxSteps = 3;
    this.validNext = new ReactiveVar(false);
    this.selectedPlans = new ReactiveVar([]);
    this.showPayModal = new ReactiveVar(false);
    this.isLoaded = new ReactiveVar(false);
    this.disabledSubmitBtn = new ReactiveVar(false);

    const org = Orgs.current();
    const prefix = org?.profileDataPrefix();
    const programs = org?.availablePrograms();
    const designation = RegistrationDesignationUtils.getPersonDesignation(this.data.data);
    const plans = org?.availableBillingPlans(true, false, true, true, true, designation);
    const currentChildGrade = (prefix ? this.data.data[prefix]?.studentGrade : this.data.data.studentGrade) ?? null;
    const timezone = org?.getTimezone();
    const currentDate = moment.tz(timezone).startOf('day').valueOf();
    this.bundles = new ReactiveVar(org?.availableBundles() ?? []);

    const filteredPlansWithDetails = plans.filter(plan => {
        if (plan.details && plan.details.regEndDate) {
            const currentDateFormatted = moment(currentDate).startOf('day').format('YYYY-MM-DD');
            const regEndDateFormatted = moment(plan.details.regEndDate).startOf('day').format('YYYY-MM-DD');
            return plan.details.regEndDate > currentDate || currentDateFormatted === regEndDateFormatted;
        }
        return true;
    });

    const filteredPlansWithGrades = filteredPlansWithDetails.filter(plan => {
        if (plan.details && plan.details.grades?.length && currentChildGrade) {
            return plan.details.grades.includes(currentChildGrade);
        }
        return true;
    });

    // Remove plans that are outside of the registration period
    const filterPlansOutsideRegPeriod = filteredPlansWithGrades.filter(plan => {
        if (plan.details?.regStartDate || plan.details?.regEndDate) {
            const today = moment.tz(timezone).startOf('day');
            if (plan.details?.regStartDate) {
                const regStartDate = moment.tz(plan.details.regStartDate, timezone);
                if (today.isBefore(regStartDate)) {
                    return false;
                }
            }

            if (plan.details?.regEndDate) {
                const regEndDate = moment.tz(plan.details.regEndDate, timezone);
                if (today.isAfter(regEndDate)) {
                    return false;
                }
            }
        }
        return true;
    });

    this.availablePrograms = programs.filter(program => filterPlansOutsideRegPeriod.some(plan => plan.program === program._id));

    Meteor.callAsync('getItemAndSelectiveWeekAvailabilities', org._id, filterPlansOutsideRegPeriod).then(result => {
        if(result) {
            // Filter out items that are at capacity
            this.availablePlans = result.filter(plan => !plan.atCapacity);
            console.log("Available plans", this.availablePlans);
        }
    }).catch(error => {
        mpSwal.fire('Error', error.reason || error.message, 'error');
        Log.error(error);
        this.availablePlans = filterPlansOutsideRegPeriod;
    })

    const childId = this.data.data._id;
    const loggedInPerson = Meteor.user().fetchPerson();
    this.parent = loggedInPerson;
    Meteor.callAsync('buildExistingFamilyRegistrationData', org._id, this.parent._id).then(data => {
        if (data) {
            // Continue to only show this child's data
            const childIndex = data.children.findIndex(c => c._id === childId);
            data.children = [data.children[childIndex]];
            data.children[0].birthday = (new moment.tz(data.children[0].birthday, org.getTimezone())).format('MM/DD/YYYY');
            // However, show all the plans for the child, not just the new ones
            data.plans = [cloneDeep(data.plans[childIndex])];
            data.existingPlans = cloneDeep(data.plans[0]);
            self.existingPlans = data.existingPlans;
            Session.set(registrationDataSessionName, data);
            checkForCoupons(data);
            self.isLoaded.set(true);
        }
    }).catch(e => {
        Log.debug(e);
    })

    this.addingChild = new ReactiveVar(false);
    this.siblingDiscount = new ReactiveVar(null);
    this.registrationFee = new ReactiveVar(null);
    Meteor.callAsync('getSiblingDiscount', org._id).then(result => {
        this.siblingDiscount.set(result);
    })
    this.topLinePercentDiscounts = new ReactiveVar(false);
    Meteor.callAsync('getTopLinePercentDiscounts', org._id).then(result => {
        this.topLinePercentDiscounts.set(result);
    });
    Meteor.callAsync('getRegistrationFeeItem', org._id).then(result => {
        this.registrationFee.set(result);
    });

    this.owesNothingTotalCharges = new ReactiveVar(false);

    // handle redirected program data
    const urlParams = new URLSearchParams(window.location.search);
    const redirectedProgramRaw = urlParams.get('program');
    const redirectedProgram = redirectedProgramRaw ? JSON.parse(redirectedProgramRaw) : null;
    console.log("redirectedProgram", redirectedProgram, redirectedProgramRaw);
    
    if (redirectedProgram) {
        const preSelectedPlan = filterPlansOutsideRegPeriod.find(plan => plan._id === redirectedProgram.plan._id);
        this.selectedPlans.set([preSelectedPlan]);
        this.currentStep.set(steps.SELECT_DAYS);
    }

    Tracker.autorun(async () => {
        const savedData = Session.get(registrationDataSessionName);
        const orgId = Orgs.current()._id;
        const totalCharges = await RegistrationUtils.totalCharges(savedData, orgId, true, true);
        this.owesNothingTotalCharges.set(false);
        if (totalCharges === 0) {
            this.owesNothingTotalCharges.set(true);
        }
    });
});

Template._addProgramsWrapper.helpers({
    currentStep: () => {
        return Template.instance().currentStep.get() || steps.SELECT_PLANS;
    },
    disabledSubmitButtons: () => {
        return (Template.instance().disabledSubmitBtn.get() || !!Template.instance().showPayModal.get());
    },
    currentTemplate: () => {
        const step = Template.instance().currentStep.get() || steps.SELECT_PLANS;
        switch (step) {
            case steps.SELECT_DAYS:
                return '_programDays';
            case steps.SUMMARY:
                return '_addProgramsSummary';
            case steps.SELECT_PLANS:
            default:
                return '_addPrograms';
        }
    },
    data: () => {
        return {
            availablePlans: Template.instance().availablePlans,
            availablePrograms: Template.instance().availablePrograms,
            currentStep: Template.instance().currentStep.get(),
            personData: Template.instance().data.data,
            selectedPlans: Template.instance().selectedPlans,
            validNext: Template.instance().validNext,
            showPayModal: Template.instance().showPayModal,
            parent: Template.instance().parent,
            rerenderRegistration: Template.instance().data.rerenderRegistration,
            existingPlans: Template.instance().existingPlans,
            disabledSubmitBtn: Template.instance().disabledSubmitBtn
        }
    },
    showBack: () => {
        const step = Template.instance().currentStep.get() || steps.SELECT_PLANS;
        return step > 1 && step !== Template.instance().maxSteps;
    },
    showNext: () => {
        const step = Template.instance().currentStep.get() || steps.SELECT_PLANS;
        return step < Template.instance().maxSteps;
    },
    showSubmit: () => {
        const step = Template.instance().currentStep.get() || steps.SELECT_PLANS;
        return step === Template.instance().maxSteps && Session.get('summaryLoaded');
    },
    owesNothing: () => {
        return Template.instance().owesNothingTotalCharges.get();
    },
    showSave: () => {
        const atTheEndOfRegistration = (Template.instance().currentStep.get() || steps.SELECT_PLANS) === Template.instance().maxSteps;
        const savedData = Session.get(registrationDataSessionName);
        // we don't care about the save button if we are not at the end of registration
        if (!atTheEndOfRegistration || !savedData) {
            return false;
        }

        const hasPersonIdQueryParam = Template.instance.personId;
        const hasNoNewItems = !RegistrationUtils.checkForNewItems(savedData.plans);
        const requiresUpdateOnHold = RegistrationUtils.updateOnHold(savedData);
        return hasPersonIdQueryParam && (hasNoNewItems || requiresUpdateOnHold);
    },
    isLoaded: () => {
        return Template.instance().isLoaded.get();
    },
});

Template._addProgramsWrapper.events({
    'focus input': function (event, instance) {
        if (!instance.validNext.get()) {
            focusAll(instance, '.add-programs-flow-wrapper');
        }

        instance.validNext.set(checkValidity(instance, '.add-programs-flow-wrapper'));
    },
    'click #addProgramsNext': function (e, instance) {
        if (!instance.validNext.get()) {
            focusAll(instance, '.add-programs-flow-wrapper');
        }
        if (!checkValidity(instance, '.add-programs-flow-wrapper')) {
            return;
        }
        e.target.blur();
        storeData(instance);
        instance.currentStep.set(Number(instance.currentStep.get()) + 1);
        if (instance.currentStep.get() === steps.SELECT_DAYS) {
            const savedData = Session.get(registrationDataSessionName);
            const newPlans = savedData.plans.flat().filter(p => p.type === PLAN_TYPE && (!p.details?.selectiveWeeks || p.details?.selectiveWeeks.length === 0) && !p.createdAt);
            // If user doesn't choose any plan type
            if (!newPlans.length) {
                instance.currentStep.set(steps.SUMMARY);
            }
        }
    },
    'click #addProgramsBack': function (e, instance) {
        instance.currentStep.set(Number(instance.currentStep.get()) - 1);
    },
    'click #addProgram': function (e, instance) {
        e.preventDefault();
        instance.currentStep.set(steps.SELECT_PLANS);
    },
    'click #addProgramsSubmit': async function (e, instance) {
        e.preventDefault();
        if (instance.disabledSubmitBtn.get()) {
            return;
        }
        if (!instance.validNext.get()) {
            focusAll(instance);
        }
        instance.disabledSubmitBtn.set(true)
        const savedData = Session.get(registrationDataSessionName);
        savedData.couponCodes = Session.get(couponCodes) || [];
        const owesNothing = savedData && ((await RegistrationUtils.totalCharges(savedData, Orgs.current()._id, true, true)) === 0);
        if (owesNothing) {
            Meteor.callAsync('updateFamily', savedData).then(() => {
                mpSwal.fire({
                    text: 'Programs updated!',
                    allowOutsideClick: false
                }).then(result => {
                    if (result.isConfirmed) {
                        instance.data.rerenderRegistration.set(true);
                        // Clear the session data
                        Session.set(couponCodes, []);
                        Session.set(employeeDiscount, undefined);
                    }
                });
                instance.disabledSubmitBtn.set(false)
            }).catch(err => {
                mpSwal.fire("Error", `${ err.reason } ${ err.details }`, "error").then(result => {
                    if (result.isConfirmed) {
                        instance.data.rerenderRegistration.set(true);
                        // Clear the session data
                        Session.set(couponCodes, []);
                        Session.set(employeeDiscount, undefined);
                    }
                });
                instance.disabledSubmitBtn.set(false)
            });
        } else {
            instance.showPayModal.set(true);
        }
    },
    'click #addProgramsSave': function (e, instance) {
        if (!validNext.get()) {
            focusAll(instance);
        }
        const savedData = Session.get(registrationDataSessionName);
        if (RegistrationUtils.updateOnHold(savedData)) {
            mpSwal.fire({
                text: 'You have selected a new enrollment plan while having an existing subsidy voucher. If you choose to proceed your request will be placed on hold pending approval. Do you wish to proceed?',
                showCancelButton: true,
                confirmButtonText: "Proceed",
                cancelButtonText: "Go Back"
            }).then(result => {
                if (result.value) {
                    Meteor.callAsync('placeRegistrationOnHold', Orgs.current()._id, savedData).then(() => {
                        instance.data.rerenderRegistration.set(true);
                        // Clear the session data
                        Session.set(couponCodes, []);
                        Session.set(employeeDiscount, undefined);
                    }).catch(err => {
                        mpSwal.fire('Error', err.message, 'error').then(result => {
                            if (result.isConfirmed) {
                                instance.data.rerenderRegistration.set(true);
                                // Clear the session data
                                Session.set(couponCodes, []);
                                Session.set(employeeDiscount, undefined);
                            }
                        });
                    });
                }
            });
        } else {
            Meteor.callAsync('updateFamily', savedData).then(() => {
                mpSwal.fire('Family updated!').then(result => {
                    if (result.isConfirmed) {
                        instance.data.rerenderRegistration.set(true);
                        // Clear the session data
                        Session.set(couponCodes, []);
                        Session.set(employeeDiscount, undefined);
                    }
                });
            }).catch(err => {
                mpSwal.fire('Error', err.message, 'error').then(result => {
                    if (result.isConfirmed) {
                        instance.data.rerenderRegistration.set(true);
                        // Clear the session data
                        Session.set(couponCodes, []);
                        Session.set(employeeDiscount, undefined);
                    }
                });
            })
        }
    }
});

function storeData(instance) {
    const currentData = Session.get(registrationDataSessionName) || {};
    const currentStep = instance.currentStep.get();
    const childIdx = 0;

    if (currentStep === steps.SELECT_PLANS) {
        currentData.plans = [currentData.existingPlans] || [];
        currentData.plans[childIdx] = currentData.plans[childIdx] || [];
        const mappedSelectPlans = _.map(instance.selectedPlans.get(), plan => plan._id);
        const mappedExistingPlans = _.map(instance.existingPlans, plan => plan._id);

        // Remove plans that were deselected since last save
        currentData.plans[childIdx] = currentData.plans[childIdx].filter(p => mappedSelectPlans.indexOf(p._id) !== -1 || mappedExistingPlans.indexOf(p._id) !== -1);

        // Add plans that were selected since last save
        _.forEach(instance.selectedPlans.get(), plan => {
            // Don't add plans that already exist for the child unless they are selective Weeks programs.
            // We assume here that if they are capabable of selecting weeks then those weeks aren't currently enrolled in.
            const existingPlan = currentData.plans[childIdx].find(p => p._id === plan._id);
            const selectiveWeeks = plan.selectedWeeks?.length > 0;
            if (!existingPlan || (existingPlan && selectiveWeeks)) {
                // If plan is restricted to certain days, set default days
                if (plan.programOfferedOn) {
                    plan.selectedDays = plan.programOfferedOn;
                } else if (!plan.selectedDays && ![ITEM_TYPE, PUNCH_CARD_TYPE].includes(plan.type)) {
                    plan.selectedDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
                }

                // If plan has maxDays, set default days
                if (plan.requiredEnrollmentMax && plan.selectedDays.length > plan.requiredEnrollmentMax) {
                    plan.selectedDays = plan.selectedDays.slice(0, plan.requiredEnrollmentMax);
                }

                // Set the correct default scaled price when days length isn't the full week.
                if (plan.scaledAmounts?.length && plan.scaledAmounts.length > plan.selectedDays.length) {
                    plan.amount = plan.scaledAmounts[plan.selectedDays.length - 1];
                }

                if (!plan.allocations) {
                    plan.allocations = [];
                }

                currentData.plans[childIdx].push(plan);
            }
        });

        if (!currentData.plans[childIdx].filter(p => p.type === PLAN_TYPE).length) {
            applyExistingAllocationsToNewPlans(instance, currentData.plans);
            _.each(currentData.plans[childIdx], plan => {
                plan.planTotal = plan.amount;
            });
        }

        if (!currentData.registrationFee && instance.registrationFee.get()) {
            currentData.registrationFee = instance.registrationFee.get();
            currentData.registrationFee.planTotal = currentData.registrationFee.amount || 0;
            applyExistingAllocationsToRegistrationFee(currentData.registrationFee);
        }

        // If all the newly selected plans are selective weeks programs, find allocations now since we skip the next step.
        const newPlanTypes = currentData.plans[childIdx].filter(p => p.type === PLAN_TYPE && !p.createdAt);
        if (newPlanTypes.every(p => p.details?.selectiveWeeks?.length)) {
            applyExistingAllocationsToNewPlans(instance, currentData.plans);
            findAllocations(currentData.plans, false, instance);
            _.each(currentData.plans[childIdx], plan => {
                plan.planTotal = plan.amount;
            });
        }
    }

    if (currentStep === steps.SELECT_DAYS) {
        _.forEach(instance.selectedPlans.get(), plan => {
            const foundPlan = _.find(currentData.plans[childIdx], p => p._id === plan._id);
            if (!foundPlan) {
                currentData.plans[childIdx].push(plan);
            } else {
                currentData.plans[childIdx] = currentData.plans[childIdx].map(p => {
                    // Enrollment date checks for selective weeks programs, since you can have multiple plans with the same id.
                    // Otherwise it overwrites the plan and you end up with a bunch of duplicates.
                    if (p._id === plan._id && p.enrollmentDate === plan.enrollmentDate) {
                        return plan;
                    }
                    return p;
                });
            }
        });

        applyExistingAllocationsToNewPlans(instance, currentData.plans);
        findAllocations(currentData.plans, false, instance);
        const bundlesApplied = [];
        const currentChildPlans = currentData.plans[childIdx].filter(plan => plan.type !== ITEM_TYPE && plan.type !== PUNCH_CARD_TYPE);

        currentChildPlans.forEach(plan => {
            const formData = $(`#${plan._id}`).serializeArray();
            RegistrationUtils.validateAndPushBundleDiscount(currentData, plan, instance.bundles.get(), formData, bundlesApplied, childIdx);
        });

        // Make sure bundles are applied to all plans in the bundle
        for (const bundleId of bundlesApplied) {
            const bundle = instance.bundles.get().find(b => b._id === bundleId);
            const bundlePlans = currentData.plans[childIdx].filter(p => bundle.plans.includes(p._id));
            let allocation = null;
            for (const bundlePlan of bundlePlans) {
                if (bundlePlan.allocations?.find(a => a.discountType === DiscountTypes.BUNDLE)) {
                    allocation = bundlePlan.allocations.find(a => a.discountType === DiscountTypes.BUNDLE);
                    break;
                }
            }
            if (allocation) {
                for (const bundlePlan of bundlePlans) {
                    if (!bundlePlan.allocations?.find(a => a.discountType === DiscountTypes.BUNDLE)) {
                        if (!bundlePlan.allocations?.length) {
                            bundlePlan.allocations = [];
                        }
                        bundlePlan.allocations.push(allocation);
                    }
                }
            }
        }

        _.each(currentData.plans[childIdx], plan => {
            plan.planTotal = plan.amount;
        });
    }
    Session.set(registrationDataSessionName, currentData);
}
