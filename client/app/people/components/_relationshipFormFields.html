<template name="_relationshipFormFields">
  <div class="row {{rowClass}}">
    <div data-cy="parent-first-name" class="{{#if useFullWidthColumns}}col{{else}}col-md-6{{/if}}">
      {{> registrationQuestionStub question=qFirstName prefill=prefills}}
    </div>
    {{#unless useFullWidthColumns}}
      <div data-cy="parent-last-name" class="col-md-6">
        {{> registrationQuestionStub question=qLastName prefill=prefills}}
      </div>
    {{/unless}}
  </div>
  {{#if useFullWidthColumns}}
    <div class="row {{rowClass}}">
      <div data-cy="parent-last-name" class="col">
        {{> registrationQuestionStub question=qLastName prefill=prefills}}
      </div>
    </div>
  {{/if}}
  <div class="row {{rowClass}}">
    <div data-cy="relationship" class="{{#if useFullWidthColumns}}col{{else}}col-md-6{{/if}}">
      {{> registrationQuestionStub question=qRelationship prefill=prefills}}
    </div>
  </div>
  <div class="row {{rowClass}}">
    <div data-cy="primary-phone-number" class="{{#if useFullWidthColumns}}col-sm-12 col-md-6{{else}}col-md-6{{/if}}">
      {{> registrationQuestionStub question=qPrimaryPhone prefill=prefills class=phoneClass }}
    </div>
    <div data-cy="alt-phone-number" class="{{#if useFullWidthColumns}}col-sm-12 col-md-6{{else}}col-md-6{{/if}}">
      {{> registrationQuestionStub question=qAltPhone prefill=prefills class=phoneClass }}
    </div>
  </div>
  <div class="row {{rowClass}}">
    <div data-cy="email" class="col">
      {{> registrationQuestionStub question=qEmail prefill=prefills class=emailClass readOnly=emailReadOnly}}
    </div>
  </div>
  <div class="row {{rowClass}}">
    <div data-cy="physical-address" class="col">
      {{> registrationQuestionStub question=qAddress prefill=prefills}}
    </div>
  </div>
  <div class="row {{rowClass}}">
    <div data-cy="city" class="{{#if useFullWidthColumns}}col-sm-12 col-md-6{{else}}col-md-4{{/if}}">
      {{> registrationQuestionStub question=qCity prefill=prefills}}
    </div>
    <div data-cy="state" class="{{#if useFullWidthColumns}}col-sm-12 col-md-6{{else}}col-md-4{{/if}}">
      {{> registrationQuestionStub question=qState prefill=prefills}}
    </div>
    {{#unless useFullWidthColumns}}
      <div data-cy="zip" class="col-md-4">
        {{> registrationQuestionStub question=qZip prefill=prefills}}
      </div>
    {{/unless}}
  </div>
  {{#if useFullWidthColumns}}
    <div class="row {{rowClass}}">
      <div data-cy="zip" class="col-sm-12 col-md-6">
        {{> registrationQuestionStub question=qZip prefill=prefills}}
      </div>
    </div>
  {{/if}}
</template>