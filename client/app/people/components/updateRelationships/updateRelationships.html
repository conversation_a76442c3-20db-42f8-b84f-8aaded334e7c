<template name="updateRelationships">
    <div class="modal fade" id="updateRelationships" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Relationships for {{personName}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="d-flex flex-row justify-content-center flex-wrap">
                        {{#each child in children}}
                        <div class="mx-3 mb-3">
                            {{> _childRelationshipCheckboxes
                                child=child
                                childName=child.fullName
                                updateChildData=updateChildData
                                disablePrimaryCaregiver=(disablePrimaryCaregiverFromBeingEdited child)
                            }}
                        </div>
                        {{/each}}
                    </div>
                    <div class="d-flex justify-content-center mt-4">
                        <button type="button" class="btn btn-secondary mr-2" data-dismiss="modal">Go Back</button>
                        <button data-cy="save-relationship-edits-btn" type="button" class="btn btn-primary" id="saveRelationshipsBtn">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>