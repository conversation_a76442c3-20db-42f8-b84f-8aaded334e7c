import { Template } from 'meteor/templating';
import './_relationshipFormFields.html';
import '../../registrationFlow/_questionStub';

Template._relationshipFormFields.onCreated(function() {

})

Template._relationshipFormFields.helpers({
    qFirstName: () => {
        return {
            mappedTo: 'firstName',
            question: 'First Name',
            isRequired: true,
            type: 'text'
        }
    },
    qLastName: () => {
        return {
            mappedTo: 'lastName',
            question: 'Last Name',
            isRequired: true,
            type: 'text'
        }
    },
    qRelationship: () => {
        return {
            mappedTo: 'relationshipDescription',
            question: 'Relationship to Child',
            isRequired: true,
            type: 'text'
        }
    },
    qPrimaryPhone: () => {
        return {
            mappedTo: 'phonePrimary',
            question: 'Primary Phone #',
            isRequired: true,
            type: 'text'
        }
    },
    qAltPhone: () => {
        return {
            mappedTo: 'phoneAlt',
            question: 'Alt Phone #',
            isRequired: true,
            type: 'text'
        }
    },
    qEmail: () => {
        return {
            mappedTo: 'profileEmailAddress',
            question: 'Email Address',
            isRequired: true,
            type: 'text'
        }
    },
    qAddress: () => {
        return {
            mappedTo: 'address',
            question: 'Physical Address',
            isRequired: true,
            type: 'text'
        }
    },
    qCity: () => {
        return {
            mappedTo: 'city',
            question: 'City',
            isRequired: true,
            type: 'text'
        }
    },
    qState: () => {
        return {
            mappedTo: 'state',
            question: 'State',
            isRequired: true,
            type: 'text'
        }
    },
    qZip: () => {
        return {
            mappedTo: 'zip',
            question: 'Zip',
            isRequired: true,
            type: 'text'
        }
    },
    phoneClass: () => {
        return 'phone';
    },
    emailClass: () => {
        return 'email';
    },
    emailReadOnly: () => {
        return false;
    },
})