<template name="selectDaysModal">
    <div id="selectDaysModal" class="modal fade">
        <div class="modal-dialog modal-lg" style="width: 100%;height: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">What days are you interested in attending?</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white">
                    {{#if plan}}
                            <form data-cy="edit-plan-enrollment" id="editPlanEnrollmentFrm" class="card registration-card day-card my-2">
                                <div class="card-body">
                                    {{> planDescription plan=plan parentData=parentData parentSource='editProgram'}}
                                    {{> daySelection plan=plan parentData=parentData parentSource='editProgram' validNext=validNext}}
                                    {{#if decreaseInDays plan}}
                                        <div data-cy="decrease-days-program" class="text-center p-2 m-1">
                                            You have opted to decrease the number of days your child will attend. Please note, changes to your program pricing will take effect during the next billing cycle. Please ensure your effective date is set to the day this change should take place to avoid drop in fees.
                                        </div>
                                    {{/if}}
                                    {{> effectiveDate plan=plan parentData=parentData parentSource='editProgram' linkedPlan=linkedPlan bundleLinkedPlan=bundleLinkedPlan}}
                                    <div class="row d-flex justify-content-center mt-4">
                                        <a data-cy="cancel-program" href="#" class="nav-link align-items-center cancelProgram"> I want to cancel this program.</a>
                                    </div>
                                {{#unless showSavings}}
                                    {{#if isScaledAmount plan}}
                                        <div class="w-100 d-flex">
                                            <div class="card text-center min-w-275px d-inline-block mx-auto mt-10 p-7" style="color: var(--primary);">
                                                <span><strong> Price </strong></span>
                                                <h1 data-cy="plan-amount-bundle"><strong> ${{planAmount}} </strong></h1>
                                                {{#if isAllDaysSelected plan false}}
                                                    <p data-cy="save-more-text" class="save-more-text d-block"> Save more per day by adding more days </p>
                                                {{/if}}
                                            </div>
                                        </div>
                                    {{/if}}
                                {{/unless}}
                                </div>
                                {{#if bundle}}
                                    <div class="card-body">
                                        {{> planDescription plan=bundle parentData=parentData parentSource='editProgram'}}
                                        {{> daySelection plan=bundle parentData=parentData parentSource='editProgram' bundle=true validNext=validNext}}
                                        {{#if decreaseInDays bundle}}
                                            <div data-cy="decrease-days-program" class="text-center p-2 m-1">
                                                You have opted to decrease the number of days your child will attend. Please note, changes to your program pricing will take effect during the next billing cycle. Please ensure your effective date is set to the day this change should take place to avoid drop in fees.
                                            </div>
                                        {{/if}}
                                        {{> effectiveDate plan=bundle parentData=parentData parentSource='editProgram' linkedPlan=linkedPlan bundleLinkedPlan=bundleLinkedPlan bundle=true}}
                                        {{#unless showSavings}}
                                        {{#if isScaledAmount bundle}}
                                            <div class="w-100 d-flex">
                                                <div class="card text-center min-w-275px d-inline-block mx-auto mt-10 p-7" style="color: var(--primary);">
                                                    <span><strong> Price </strong></span>
                                                    <h1 data-cy="plan-amount-bundle"><strong> ${{planAmount true}} </strong></h1>
                                                    {{#if isAllDaysSelected bundle true}}
                                                        <p data-cy="save-more-text" class="save-more-text d-block"> Save more per day by adding more days </p>
                                                    {{/if}}
                                                </div>
                                            </div>
                                        {{/if}}
                                        {{/unless}}
                                    </div>
                                {{/if}}
                            </form>
                    {{/if}}
                    {{#if showSavings}}
                        <div class="card registration-card day-card h3 my-2 py-4">
                            <div class="row ml-1 font-weight-bold">
                                <div class="col-auto text-right">
                                    <div class="row">
                                        <div class="col">
                                            {{ formatCurrency regularPrice }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            {{ formatCurrency bundlePrice }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col" style="color: var(--primary);">
                                            {{ formatCurrency savings }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="row">
                                        <div class="col">
                                            Cost of plans if purchased separately
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            Bundled Price
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col" style="color: var(--primary);">
                                            Your savings!
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {{/if}}
                </div>
                <div class="modal-footer d.flex justify-content-center align-content-center">
                    <button data-cy="save-edit-enrollment-program" type="button" class="btn btn-primary font-weight-bolder mr-2" id="saveButton" disabled="{{saveDisabled}}">Save</button>
                    <button data-cy="cancel-edit-enrollment-program" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>

<template name="cancelProgramModal">
    <div data-cy="cancel-program-modal" id="cancelProgramModal" class="modal fade">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title font-weight-bolder text-primary">We're so sorry to see you go!</h5>
                    <button type="button" class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close">
                        <span class="fad-regular fad-primary fad fa-times"></span>
                    </button>
                </div>

                <div class="modal-body bg-gray-100">
                    <h3 class="text-dark-25 font-size-h4 d-none d-md-inline">
                        Canceling your enrollment takes place right after your child's last date selected below.
                        All future recurring charges will be removed.<br><br>
                        If you have questions or concerns please contact <a href="{{chatSupportUrl}}">{{longName}}</a> Support.
                    </h3>
                    {{#if getContinueButton}}
                    <div class="d-flex flex-column">
                        <div class="form-group mt-10 ml-1">
                            <h4 class="font-weight-bolder ml-4 mb-3"> When will your child's last day be? </h4>
                            <div class="input-group col col-xl-3">
                                <input
                                        name="cancelProgramDate"
                                        type="text"
                                        value="{{cancelDate}}"
                                        class="form-control date-picker border-right-0 {{cancelProgramInitDatePicker}}"
                                        data-required="true"
                                        data-type="date"
                                        data-cy="cancel-program-date"
                                />
                                <div class="input-group-append">
                                    <span class="input-group-text" style="background-color: var(--secondary); border: none;">
                                        <i class="fad-regular fad fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-2">
                            <h4 class="font-weight-bolder ml-5"> Why are you leaving? </h4>
                            <select data-cy="cancel-reason-select" required name="cancelReasons" id="cancelReasonsSelect" class="cancelReason form-control form-control-lg form-control-solid w-25 ml-5">
                                <option value="">Select a reason from the list </option>
                                {{#each singleReason in cancellationReasons}}
                                <option id="{{singleReason._id}}" {{getPrevCancelReason singleReason._id}}>{{singleReason.reason}}</option>
                                {{/each}}
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-cy="cancel-continue-btn" type="button" class="btn btn-primary" id="continueBtn">Continue</button>
                    </div>
                    {{else}}
                    <div class="form-group mt-20">
                        <h2 class="font-weight-bolder mb-7 ml-4"> Just to confirm: </h2>
                        <div class="d-flex flex-row font-weight-bold mt-4">
                            <div class="col-5">
                                <h4> When will your child's last day be? </h4>
                                <h3 class="text-dark-50 mt-5">{{cancelDate}}</h3>
                            </div>
                            <div>
                                <h4> Why are you leaving? </h4>
                                <h3 class="text-dark-50 mt-5">{{cancelReason}}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary backBtn">Go Back</button>
                        <button data-cy="cancel-plan-btn" type="button" class="btn btn-primary btnCancelPlan" {{continueDisabled}}>Continue</button>
                    </div>
                    {{/if}}
                </div>
            </div>
        </div>
    </div>
</template>
