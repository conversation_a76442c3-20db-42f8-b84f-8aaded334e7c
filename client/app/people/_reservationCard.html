<template name="_reservationCard">
    <style>
        .caret {
            transition: transform 0.2s ease;
        }

        .toggle-caret[aria-expanded="true"] .caret {
            transform: rotate(0deg); /* Default up */
        }

        .toggle-caret[aria-expanded="false"] .caret {
            transform: rotate(180deg); /* Point down */
        }

        .enroll-program-details p {
            margin-bottom: 3px; /* Spacing between paragraphs */
        }
        
        .bold {
            font-weight: bold;
        }

        .past-date {
            text-decoration: line-through;
        }

        .item-description {
            font-weight: bold;
            font-size: 1.1rem;
        }

    </style>
    {{#if trueIfEq type "item"}}
        <div data-cy="enrolled-item" class="card card-custom gutter-b" data-id="{{reservation._id}}">
            <div class="card-body d-flex">
                <!-- Main content container -->
                <div class="flex-grow-1">
                    <div class="row">
                        <div class="col-11">
                            <div class="row mb-2">
                                <div class="col-12">
                                    <div class="item-description">{{reservation.originalItem.description}}</div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <div>Program Details:</div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <div>{{{reservation.originalItem.programDetails}}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-1 align-content-center">
                            <div data-cy="enrolled-item-expand" class="btn btn-icon btn-clean toggle-caret" data-toggle="collapse" href="#item_{{reservation._id}}">
                                <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-angle-down fa-2x caret"></span>
                            </div>
                        </div>
                    </div>
                
                    <div data-cy="enrolled-item-expanded-dates" class="collapse show" id="item_{{reservation._id}}">
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="bold">Dates:</div>
                                <div class="enroll-program-dates">
                                    <div class="row">
                                        {{#each schedule in reservation.schedules}}
                                            <div class="col-md-6 mb-2">
                                                {{#if schedule.isStriked}}
                                                    <span data-cy="enrolled-item-date-striked" class="past-date">{{formatUnixToDate schedule.scheduledDate}}</span>
                                                {{else}}
                                                    <span>{{formatUnixToDate schedule.scheduledDate}}</span>
                                                {{/if}}
                                            </div>
                                        {{/each}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {{/if}}
    {{#if trueIfEq type "single"}}
        <div data-cy="enrolled-program" class="card card-custom gutter-b" data-id="{{reservation._id}}" 
        href="#reservation_{{reservation._id}}" 
        {{dynamicEnrollPlanAttributes reservation}}
        >
            <div class="card-body">
                <div class="row">
                    <div class="d-flex flex-column col-2">
                        <label>Start Date</label>
                        <div data-cy="enrolled-programs-start-date" class="d-flex flex-row flex-wrap align-items-center">
                            {{formatDate reservation.scheduledDate "M/DD/YYYY"}}
                        </div>
                        {{#if reservation.current }}
                            <span class="recurring-schedule-current-label">Current</span>
                        {{/if}}
                        {{# if reservation.isPending }}
                            <span class="recurring-schedule-current-label">
                                            Pending Review
                                            <i class="fa fad-secondary la-question-circle" data-toggle="tooltip"
                                               data-placement="top"
                                               title="{{ pendingReviewMessage }}"></i>
                                        </span>

                        {{/if}}
                    </div>
                    <div data-cy="enrolled-programs-end-date" class="d-flex flex-column col-2">
                        <label>End Date</label>
                        {{#if reservation.scheduledEndDate}}
                            <span>{{formatDate reservation.scheduledEndDate "M/DD/YYYY"}}</span>{{/if}}
                    </div>
                    <div data-cy="enrolled-programs-group" class="d-flex flex-column col-2">
                        <label>Group</label>
                        {{reservation.targetGroupName}}
                    </div>
                    <div data-cy="enrolled-program-days" class="d-flex flex-column col-2">
                        <label>Days</label>

                        {{#each day in reservation.recurringDays}}
                            {{dayLetter day}}&nbsp;
                        {{/each}}

                    </div>
                    <div data-cy="enrolled-program-type" class="d-flex flex-column col-2">
                        <label>Type</label>
                        <span><strong>{{scheduleTypeNameFor reservation.scheduleType}}</strong></span>
                    </div>
                    {{#if isEditable reservation}}
                        <div class="d-flex flex-column col-1">
                            <div class="d-flex flex-row flex-grow-1 justify-content-end">
                                <div class="btn btn-icon btn-clean edit-enrolled-program" data-id="{{reservation._id}}" data-cy="edit-enrolled-program-{{reservation._id}}">
                                    <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-angle-right fa-2x"></span>
                                </div>
                            </div>
                        </div>
                    {{/if}}
                </div>
                <div class="{{#if getLinkedPlanProgramDetails reservation}}show{{else}}collapse{{/if}}" id="reservation_{{reservation._id}}">
                    <br/>
                    {{#if getLinkedPlanProgramDetails reservation}}
                        <div class="bold">Program Details:</div>
                        <div class="enroll-program-details">{{{getLinkedPlanProgramDetails reservation}}}</div>
                    {{/if}}
                </div>
            </div>
        </div>
    {{/if}}
    {{#if trueIfEq type "group"}}
        <div class="card registration-card active">
            <div class="card-body">
                <div>
                    <h3>
                        <strong 
                        style="cursor:pointer;" 
                        href="#reservation_{{reservation._id}}" 
                        {{dynamicSelectivePlanAttributes reservation.programDetails}}
                        class="toggle-caret">
                            {{reservation.planDescription}}
                            {{#if reservation.programDetails}}<i class="caret fa fa-caret-down"></i>{{/if}}
                        </strong>
                    </h3>
                    <div class="{{#if reservation.programDetails}}show{{else}}collapse{{/if}}" id="reservation_{{reservation._id}}">
                        {{#if reservation.programDetails}}
                            <div class="bold">Program Details:</div>
                            <div class="enroll-program-details">{{{reservation.programDetails}}}</div>
                        {{/if}}
                    </div>
                </div>
                {{# if reservation.isPending }}
                    <span class="recurring-schedule-current-label">
                                            Pending Review
                                            <i class="fa fad-secondary la-question-circle" data-toggle="tooltip"
                                               data-placement="top"
                                               title="{{ pendingReviewMessage }}"></i>
                                        </span>

                {{/if}}
                {{#each reservation.reservations}}
                    <div class="card week-card my-4">
                        <div class="card-body">
                            <h3><strong>Week {{this.week}}</strong></h3>
                            <div class="row">
                                <div class="d-flex flex-column col-2">
                                    <label>Start Date</label>
                                    <div class="d-flex flex-row flex-wrap align-items-center">
                                        {{this.startDate}}
                                    </div>
                                </div>
                                <div class="d-flex flex-column col-2">
                                    <label>End Date</label>
                                    <span>{{this.endDate}}</span>
                                </div>
                                <div class="d-flex flex-column col-2">
                                    <label>Group</label>
                                    {{this.targetGroupName}}
                                </div>
                                <div class="d-flex flex-column col-2">
                                    <label>Days</label>
                                    {{#each day in this.recurringDays}}
                                        {{dayLetter day}}&nbsp;
                                    {{/each}}
                                </div>
                                <div class="d-flex flex-column col-2">
                                    <label>Type</label>
                                    <span>{{scheduleTypeNameFor this.scheduleType}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                {{/each}}
            </div>
        </div>
    {{/if}}
</template>