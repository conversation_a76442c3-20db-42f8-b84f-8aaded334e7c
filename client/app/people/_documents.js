import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_documents.html';
import _ from '../../../lib/util/underscore';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../lib/collections/people';
import { processPermissions } from '../../../lib/permissions';
import { Orgs } from '../../../lib/collections/orgs';
import { remapDownloadUrl } from '../../../mpweb';

Template._documents.onCreated(function () {
  this.documentsKey = new ReactiveVar(true);
});

Template._documents.events({
  'click .btnViewTemplate': function (event) {
    const templateId = $(event.currentTarget).data('id');
    Meteor.callAsync('getDocumentRepositoryLink', { type: 'template', templateId })
      .then((result) => {
        if (result) {
          console.log(result);
          window.open(result.url, '_blank');
        }
      })
      .catch((error) => {
        mpSwal.fire('Error retrieving document', error.reason, 'error');
      });
  },
  'click .btnUploadDocument': function (event, template) {
    const templateId = $(event.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;

    mpSwal
      .fire({
        title: 'Upload New Document',
        input: 'file'
      })
      .then((result) => {
        if (result.value) {
          const metaContext = { tokenId: tokenString() };
          const uploader = new Slingshot.Upload('myDocumentRepositoryUploads', metaContext);

          uploader.send(result.value, function (error, downloadUrl) {
            if (error) {
              alert(error);
            } else {
              downloadUrl = remapDownloadUrl(downloadUrl);
              console.log('download = ' + downloadUrl);
              Meteor.callAsync('insertDocumentRepositoryItem', {
                templateId,
                tokenId: metaContext.tokenId,
                personId
              })
                .then(() => {
                  mpSwal.fire('Success', 'Document uploaded successfully', 'success');
                  template.documentsKey.set(false);
                  template.documentsKey.set(true);
                })
                .catch((error) => {
                  mpSwal.fire('Error uploading document', error.reason, 'error');
                });
            }
          });
        }
      });
  },
  'click .btnReviewDocument': function (event, template) {
    const documentId = $(event.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;
    Meteor.callAsync('getDocumentRepositoryLink', { type: 'document', documentId, personId })
      .then((result) => {
        window.open(result.url, '_blank');
      })
      .catch((error) => {
        console.log('Error retrieving document', error);
        mpSwal.fire('Error retrieving document', error.reason, 'error');
      });
  },
  'click .btnRejectDocument': function (event, template) {
    const documentId = $(event.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;
    mpSwal
      .fire({
        title: 'Confirm Rejection',
        showCancelButton: true,
        html: `<div id='frmRejectDocument'>This will mark the document as rejected and require a resubmission. <br/><br/>
        <b>Notify Person?</b> <br/>
          <label><input type="radio" name="notify" value="yes" checked> Yes</label>
          <label><input type="radio" name="notify" value="no"> No</label><br/>
        <br/>
        <b>Additional Notes:</b><br/> 
        <textarea name="rejectionNotes" style="width:100%"></textarea>`,
        preConfirm: function () {
          //swal.showValidationError('Server Error!');
          return new Promise(function (resolve) {
            resolve([
              $('#frmRejectDocument input[name=notify]:checked').val(),
              $('#frmRejectDocument textarea[name=rejectionNotes]').val()
            ]);
          });
        }
      })
      .then((result) => {
        if (result.value) {
          Meteor.callAsync('updateDocumentRepositoryItem', {
            action: 'reject',
            documentId,
            personId,
            notifyPerson: result.value[0] == 'yes',
            rejectionNotes: result.value[1]
          })
            .then(() => {
              mpSwal.fire('Document Rejected');
            })
            .catch((error) => {
              mpSwal.fire('Error retrieving document', error.reason, 'error');
            });
        }
      });
  },
  'click .btnApproveDocument': function (event, template) {
    const documentId = $(event.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;
    Meteor.callAsync('updateDocumentRepositoryItem', { action: 'approve', documentId, personId })
      .then(() => {
        mpSwal.fire('Success', 'Document Approved', 'success');
        template.documentsKey.set(false);
        template.documentsKey.set(true);
      })
      .catch((error) => {
        mpSwal.fire('Error retrieving document', error.reason, 'error');
      });
  },
  'click #document-ack, click .document-ack': function (e, template) {
    const templateId = $(e.currentTarget).data('id');
    const personId = FlowRouter.current().params._id || template.data.child._id;
    const name = $(e.currentTarget).data('name');

    mpSwal
      .fire({
        title: 'Acknowledge Document',
        text: `I have read and acknowledged receipt of the ${name}`,
        showCancelButton: true
      })
      .then((result) => {
        if (result.value) {
          Meteor.callAsync('insertDocumentRepositoryItem', {
            action: 'ack',
            templateId,
            personId
          })
            .then(() => {
              mpSwal.fire('Success', 'Document Acknowledged', 'success');
              template.documentsKey.set(false);
              template.documentsKey.set(true);
            })
            .catch((error) => {
              mpSwal.fire('Error', error.reason, 'error');
            });
        }
      });
  },
  'click .btnExemptDocument': function (e, template) {
    const templateId = $(e.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;

    mpSwal
      .fire({
        title: 'Exempt person from document',
        text: `Please confirm that you want to exempt this person from this document`,
        showCancelButton: true
      })
      .then((result) => {
        if (result.value) {
          Meteor.callAsync('insertDocumentRepositoryItem', {
            action: 'exempt',
            templateId,
            personId
          })
            .then(() => {
              mpSwal.fire('Success', 'Document Exempted', 'success');
            })
            .catch((error) => {
              mpSwal.fire('Error', error.reason, 'error');
            });
        }
      });
  },
  'click #btnAssignDocument': function (e, template) {
    const personId = FlowRouter.current().params._id || template.data.child._id,
      availableDocuments = _.chain(Orgs.current().documentDefinitions)
        .filter((dd) => !dd.deletedAt && dd.assignmentType == 'individuals')
        .sortBy((dd) => dd.name)
        .map((dd) => [dd._id, dd.name])
        .object()
        .value();

    mpSwal
      .fire({
        title: 'Choose an available document',
        input: 'select',
        inputOptions: availableDocuments,
        showCancelButton: true
      })
      .then((result) => {
        console.log('RESULT', result);
        if (result.value) {
          Meteor.callAsync('insertDocumentRepositoryItem', {
            action: 'assign',
            templateId: result.value,
            personId
          })
            .then(() => {
              mpSwal.fire('Success', 'Document Assigned', 'success');
            })
            .catch((error) => {
              mpSwal.fire('Error', error.reason, 'error');
            });
        }
      });
  },
  'click .btnUnassignDocument': function (e, template) {
    const templateId = $(e.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;

    mpSwal
      .fire({
        title: 'Un-assign person from document',
        text: `Please confirm that you want to exempt this person from this document`,
        showCancelButton: true
      })
      .then((result) => {
        if (result.value) {
          Meteor.callAsync('insertDocumentRepositoryItem', {
            action: 'unassign',
            templateId,
            personId
          })
            .then(() => {
              mpSwal.fire('Success', 'Document Un-assigned', 'success');
            })
            .catch((error) => {
              mpSwal.fire('Error', error.reason, 'error');
            });
        }
      });
  },
  'click .btnArchiveDocument': function (e, template) {
    const documentId = $(e.currentTarget).data('id'),
      personId = FlowRouter.current().params._id || template.data.child._id;

    mpSwal
      .fire({
        title: 'Archive document',
        text: `Please confirm that you want to archive this document. You will still be able to find it by checking 'Show Archived Documents'.`,
        showCancelButton: true
      })
      .then((result) => {
        if (result.value) {
          Meteor.callAsync('updateDocumentRepositoryItem', {
            action: 'archive',
            documentId,
            personId
          })
            .then(() => {
              mpSwal.fire('Success', 'Document Archive', 'success');
              template.documentsKey.set(false);
              template.documentsKey.set(true);
            })
            .catch((error) => {
              mpSwal.fire('Error', error.reason, 'error');
            });
        }
      });
  },
  'change #chkShowArchived': function (e, i) {
    const target = $(e.currentTarget);
    i.showArchived.set(target.prop('checked'));
  }
});

Template._documents.helpers({
  documentRepositoryItems: function () {
    const child = People.findOne({ _id: this.child?._id }) || this;
    const 
      userPerson = Meteor.user() && Meteor.user().fetchPerson(),
      showArchived = Template.instance().showArchived.get();
    const documents = child.documentRepositoryItems( userPerson, showArchived);

    return documents;
  },
  documentRepositoryGroups: function (items) {
    return _.chain(items)
      .groupBy('section')
      .map((docs, section) => {
        return { 
          section, 
          documentRepositoryItems: docs 
        };
      })
      .sortBy('section')
      .value();
  },
  canEditDocuments: function () {
    return processPermissions({
      assertions: [{ context: 'documents', action: 'edit' }],
      evaluator: (person) => person.type == 'admin' || person.type == 'staff' || person.type == 'family'
    });
  },
  canAssignDocuments: function () {
    return processPermissions({
      assertions: [{ context: 'documents', action: 'edit' }],
      evaluator: (person) => person.type == 'admin' || person.type == 'staff'
    });
  },
  collapseLayout: function() {
    return Template.instance().data.collapseLayout;
  },
  fullName(firstName, lastName) {
		return `${firstName} ${lastName}`;
		
	},
});

Template._documents.onCreated(function () {
  var self = this;
  self.showArchived = new ReactiveVar(false);
});

var tokenString = function () {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz';
  var string_length = 20;
  var randomstring = '';
  for (var i = 0; i < string_length; i++) {
    var rnum = Math.floor(Math.random() * chars.length);
    randomstring += chars.substring(rnum, rnum + 1);
  }
  return randomstring;
};
