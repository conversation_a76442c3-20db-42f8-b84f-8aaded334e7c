<template name="_addMedicationModal">
  <div id="_addMedicationModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" >
      <div class="modal-content" style="height:auto;min-height:100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">{{getTitle}} Medication</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmAddMedication">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Medication</label>
              <div class="col-lg-9 col-xl-6">
                <input class="form-control form-control-lg form-control-solid" type="text" name="medication-name"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Dosage</label>
              <div class="col-lg-9 col-xl-6">
                <input class="form-control form-control-lg form-control-solid" type="text" name="medication-dosage"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Frequency</label>
              <div class="col-lg-9 col-xl-6">
                <div class="radio-list">
                  <label class="radio radio-primary">
                    <input type="radio" name="medication-frequency" value="everyhours" checked >
                    <span></span>
                    <div class="d-flex flex-row align-items-center">
                      <span class="mr-2">Every</span>
                      <input type="text" class="form-control form-control-solid" name="everyhours-amt" style="width:60px">
                      <span class="ml-2">hours</span>
                    </div>
                  </label>
                  <label class="radio radio-primary">
                    <input type="radio" name="medication-frequency" value="timesperday"/>
                    <span></span>
                    <div class="d-flex flex-row align-items-center">
                      <input type="text" class="form-control form-control-solid" name="timesperday-amt" style="width:60px">
                      <span class="ml-2">times per day</span>
                    </div>
                  </label>
                  <label class="radio radio-primary">
                    <input type="radio" name="medication-frequency" value="other"/>
                    <span></span>
                    Other (see notes)
                  </label>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right">Duration</label>
              <div class="col-lg-9 col-xl-6">
                <div class="radio-list">
                  <label class="radio radio-primary">
                    <input type="radio" name="medication-duration" value="ongoing" checked />
                    <span></span>
                    <div class="d-flex flex-row align-items-center">
                      <span>Ongoing</span>
                    </div>
                  </label>
                  <label class="radio radio-primary">
                    <input type="radio" name="medication-duration" value="end-date"/>
                    <span></span>
                    <div class="d-flex flex-row align-items-center">
                      <span class="mr-2">Ends</span>
                      <input type="text" class="form-control form-control-solid" id="medication-end-date" placeholder="mm/dd/yyyy" name="medication-end-date" style="width:200px">
                    </div>
                  </label>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Notes</label>
              <div class="col-lg-9 col-xl-6">
                <textarea class="form-control form-control-lg form-control-solid" name="medication-notes"></textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <div class="btn btn-primary font-weight-bolder mr-2" id="btnSaveMedication">Save</div>
          <div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</div>
        </div>
      </div>
    </div>
  </div>
</template>
