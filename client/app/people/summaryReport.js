import { Template } from 'meteor/templating';
import './summaryReport.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../lib/collections/people';
import _ from '../../../lib/util/underscore';
import { Moments } from '../../../lib/collections/moments';

Template.summaryReport.helpers({
	"momentSummaryDisplayTemplate": function() {
		return this.momentType + "SummaryDisplayTemplate";
	},
	"sections": function() {
		var allMoments = Moments.find().fetch();
		var distinctDates =	_.chain(allMoments)
			.map(function (m) { return moment(m.sortStamp).format('YYYYMMDD');})
			.uniq()
			.sortBy(function(i) { return i;})
			.value();
		
		var output = [];
		_.each(distinctDates, function (reportDate) {
			
			var reportDateMin = moment(reportDate,'YYYYMMDD').valueOf();
			var reportDateMax = moment(reportDate,'YYYYMMDD').add(24, 'hours').valueOf();
			
			var dailyMoments = {
				date: reportDate,
				prettyDate: moment(reportDate,'YYYYMMDD').format("dddd, MMMM Do YYYY"),
				comments: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="comment"}),
				food: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="food"}),
				potty: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="potty"}),
				sleep: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="sleep"}),
				checkout: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="checkout"}),
				mood: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="mood"}),
				learning: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="learning"}),
				incidents: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="incident"}),
				illness: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="illness"}),
				ouches: _.filter(allMoments, function(m) { return m.sortStamp>= reportDateMin && m.sortStamp < reportDateMax && m.momentType=="ouch"})
			};
			output.push(dailyMoments);
		});
		
		return output;
	},
	"availableSections": function() {
		var output = [];
		var section = this;
		_.each(["comments", "food", "potty", "sleep", "mood", "learning", "incidents", "illness", "ouches", "checkout"], function(cat) {
			if (section[cat].length > 0 ) output.push(cat);
		});
		return output;
	},
	"momentsForSection": function(title) {
		return _.sortBy(Template.parentData()[title], "sortStamp");
	},
	"equals": function(val1, val2) {
		return (val1 === val2) ? true : false;
	},
	"prependWithDash": function(val) {
		return ((val && val.trim() != "") ? " - " + val : "");
	},
	"reporteeName": function() {
		var currentPerson = People.findOne({_id: FlowRouter.current().params._id});
		return currentPerson.firstName + " " + currentPerson.lastName;
	},
	"faClass": function(sectionName) {
		switch (sectionName) {
			case "food": return "fa fa-cutlery";
			case "potty": return "fa fa-child";
			case "comments": return "fa fa-comment";
			case "sleep": return "fa fa-bed";
			case "mood": return "fa fa-smile-o";
			case "checkout": return "fa fa-clock-o";
			case "learning": return "fa fa-leanpub";
			case "incidents": return "fa fa-exclamation-circle";
			case "illness": return "fa fa-thermometer-empty";
			case "ouches": return "fa fa-hand-stop-o";
			default: return "";
		}
	}
});

Template.summaryReport.created = function() {
	var instance = this;
	instance.autorun(function() {
		instance.subscribe('personMoments', FlowRouter.current().params._id, null, FlowRouter.current().queryParams.startDate, FlowRouter.current().queryParams.endDate );
	});
};
