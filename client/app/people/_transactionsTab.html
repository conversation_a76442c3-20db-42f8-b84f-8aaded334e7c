<template name="transactionsTab">
	<div class="row">
		<div class="col-md-12 px-12 mb-6">
			<div class="row">
				<div class="col-lg-4 col-md-6 col-sm-12">
					<div class="form-group">
						<label>Invoice Date Range</label>
						
						<input data-cy="invoice-date-range-input" type="text" name="invoices-date-range" class="form-control">
						
					</div>
				</div>
				<div class="col-lg-8 col-md-6 col-sm-12 text-right">
					{{#if shouldShowYearEndStatementButton}}
					
						<button data-cy="payment-statement-report-button" class="btn btn-primary font-weight-bolder btnYearEndStatement">Payment Statement</button>
					
					{{/if}}
					{{#if shouldShowBalanceNoticeButton}}
					
						<button class="btn btn-primary font-weight-bolder btnSendBalanceNotice ml-4">Send Balance Notice</button>					

					{{/if}}
				</div>
			</div>
			
			<div class="table-responsive" style="height:100%;">
				<table class="table">
					<thead>
						<tr class="text-left">
							<th>Invoice</th>
							<th>Description</th>
							<th>Status</th>
							<th class="text-center"></th>
						</tr>
					</thead>
					<tbody>
						{{#each transactions}}
						<tr>
							<td>
								{{#if showInvoiceDetailLink}}
									<a data-cy="invoice-number-transaction" href="/billing/invoices/{{_id}}" class="text-dark-75 font-weight-bolder text-hover-primary mb-1 font-size-h5">#{{invoiceNumber}}</a>
								{{else}}
									<span class="text-dark-75 font-weight-bolder text-hover-primary mb-1 font-size-h5">#</span>{{invoiceNumber}}
								{{/if}}<br/>
								{{invoiceDate}}
							</td>
							<td data-cy="invoice-number-child">
								{{personName}}<br/>
								
								<ul data-cy="invoice-number-description" class="list-unstyled">
								{{#each desc in lineItemDescriptions}}
									<li>{{desc}}</li>
								{{/each}}
								</ul>
								
							</td>
							<td data-cy="invoice-number-amount">
								<span class="text-dark-75 font-weight-bolder mb-1 font-size-lg">
									{{#if trueIfEq invoiceStatus "open"}}
										{{#if payerSpecificInfo}}
										{{payerSpecificInfo}}
										{{else}}
										{{formatCurrency openAmount}} due
										{{/if}}
									{{else}}
										{{#unless voided}}
										Paid
										{{else}}
										Voided
										{{/unless}} 
									{{/if}}
								</span><br/>
								<span class="">Amount:</span>
								{{#unless voided}}
									{{#if lt originalAmount 0}}
										{{formatCurrency 0}}
									{{else}}
										{{formatCurrency originalAmount}}
									{{/if}}
									{{else}}
										{{formatCurrency 0}}
								{{/unless}}
								<br/>

								{{#if getRefundedAmount}}
									<br/>Refunded: {{formatCurrency getRefundedAmount}}
								{{/if}}
								{{#if getChargedBackAmount}}
									<br/>Charged back: {{formatCurrency getChargedBackAmount}}
								{{/if}}
							</td>
							<td>
								<div class="dropdown">
									<div data-cy="transactions-actions-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnGroupInvoiceActions" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
										Actions<i class="fad-regular fad fa-angle-down ml-4" style="color:#fff"></i>
									</div>
									<div class="dropdown-menu dropdown-menu-right" aria-labelledby="btnGroupInvoiceActions">
										{{#if shouldShowReceiptButton}}
											<span data-cy="view-receipt-btn" class="dropdown-item clickable-row btnViewReceiptDetail" data-id="{{_id}}">View Receipt</span>
										{{/if}}
										{{#if shouldShowPayButton}}
											<span data-cy="pay-btn" class="dropdown-item clickable-row btnPayNow" data-id="{{_id}}">Pay</span>
										{{/if}}
										{{#if shouldShowCreditButton}}
											<span data-cy="credit-btn" class="dropdown-item clickable-row btnCredit" data-id="{{_id}}">Credit</span>
										{{/if}}
										{{#if shouldShowRefundButton}}
											<span data-cy="refund-btn" class="dropdown-item clickable-row btnRefund" data-id="{{_id}}">Refund</span>
										{{/if}}
										{{#if shouldShowVoidButton}}
											<span data-cy="void-btn" class="dropdown-item clickable-row btnVoid" data-id="{{_id}}">Void</span>
										{{/if}}
									
										<span data-cy="quick-view-btn" class="dropdown-item clickable-row btnViewInvoiceDetail" data-id="{{_id}}">Quick View</span>
										{{#if shouldShowLedgerButton}}
											<span data-cy="invoice-ledger-btn" class="dropdown-item  clickable-row btnViewLedger" data-id="{{_id}}">Invoice Ledger</span>
										{{/if}}
									</div>
								</div>
							</td>
						</tr>
						{{/each}}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
