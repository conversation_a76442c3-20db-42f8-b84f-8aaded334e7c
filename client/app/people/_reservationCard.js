import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_reservationCard.html';
import moment from 'moment-timezone';
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import { BillingUtils } from '../../../lib/util/billingUtils';
import { EnrollmentUtils } from '../../../lib/util/enrollmentUtils';
import { RegistrationDesignationUtils } from "../../../lib/registrationDesignationUtils";
import { People } from '../../../lib/collections/people';
import { Orgs } from '../../../lib/collections/orgs';
import { Reservations } from '../../../lib/collections/reservations';
import { showModal } from '../main';
import { DESIGNATION_WAIT_LIST } from "../../../lib/constants/designationConstants";
import './_selectDaysModal';

Template._reservationCard.onCreated(function () {
    this.hasDesignation = new ReactiveVar(false);
    this.hasChildDesignation = new ReactiveVar(false);
    const currentPerson = Meteor.user()?.fetchPerson();
    const currentChild = Template.instance().data.parentData
    this.hasDesignation.set(currentPerson?.type === 'family' && RegistrationDesignationUtils.getPersonDesignation(currentPerson));
    this.hasChildDesignation.set(RegistrationDesignationUtils.getPersonDesignation(currentChild));
})

Template._reservationCard.helpers({
  dayLetter(day) {
    const dayMap = {
      sat: 'Sa',
      sun: 'Su',
      mon: 'M',
      monday: 'M',
      tue: 'T',
      tuesday: 'T',
      wed: 'W',
      wednesday: 'W',
      thu: 'R',
      thursday: 'R',
      fri: 'F',
      friday: 'F'
    };
    return dayMap[day];
  },
  isEditable(reservation) {
    if (Template.instance().hasDesignation.get() || Template.instance().hasChildDesignation.get()) {
      return false;
    }
    if (reservation.isPlan) {
      return false;
    }
    //const hasTimePeriod = linkedPlan.planDetails.details?.timePeriod ?? null;
    const isExpired = moment(reservation.scheduledEndDate).isBefore(moment().startOf('day'));
    const hasBeenAdjusted = reservation.parentAdjusted || null;

    return !isExpired && !hasBeenAdjusted;
  },
  pendingReviewMessage() {
    return (
      'Our team is verifying your information, which typically takes 24-48 hours. Once confirmed, your changes will be reflected here.' +
      'We will reach out if additional information is needed. Your patience is greatly appreciated.'
    );
  },
  scheduleTypeNameFor(id) {
    const scheduleTypeDefinition = Orgs.current()
      .getScheduleTypes()
      .find((st) => st._id === id);
    return scheduleTypeDefinition && scheduleTypeDefinition.type;
  },
  getLinkedPlanProgramDetails(reservation) {
    return reservation?.programDetails;
  },
  dynamicEnrollPlanAttributes(reservation) {
    const hasDetails = reservation?.programDetails;
    if (hasDetails) {
      return {
        'data-toggle': 'collapse'
      };
    } else {
      return {};
    }
  },
  dynamicSelectivePlanAttributes(programDetails){
    if (programDetails) {
      return {
        'data-toggle': 'collapse'
      };
    } else {
      return {};
    }
  },
  formatUnixToDate(timestamp) {
    const timezone = Orgs.current().timezone;
    return moment.tz(timestamp, timezone).format('MM/DD/YYYY');
  }
});

Template._reservationCard.events({
    "click .edit-enrolled-program": function(event, template){
        const target = $(event.currentTarget);
        const id = target.data("id");
        const defaultGroupId = template.data.parentData.defaultGroupId;
        const reservation = Reservations.findOne({_id: id });
        const selectedPersonObj = People.findOne(reservation.selectedPerson);
        const linkedPlan = reservation.linkedPlan();

        const isBundledPlan = RegistrationUtils.isEnrolledInBundle(selectedPersonObj, linkedPlan);
        const bundle = isBundledPlan ? RegistrationUtils.getBundle(linkedPlan.bundlePlanId) : null;
        const matchedPlan = bundle ? RegistrationUtils.getSecondPlan(selectedPersonObj, bundle, linkedPlan) : null;
        const bundleReservation = matchedPlan ? Reservations.findOne(matchedPlan.reservationId) : null;
        const bundleLinkedPlan = bundleReservation ? bundleReservation.linkedPlan() : null;

        showModal("selectDaysModal", {
            selectedPersonObj,
            linkedPlan,
            reservation,
            bundleLinkedPlan,
            bundleReservation,
            onFinalDateSelectDaysModal: (reservationId, finalDate, cancelReasonId) => {
                Meteor.callAsync('updateReservationEndDate', reservationId, selectedPersonObj._id, finalDate, cancelReasonId).then(async (res) => {
                    if (res) {
                        const {reservations, selectiveWeekPlans} = await EnrollmentUtils.buildEnrolledPrograms(selectedPersonObj._id, defaultGroupId);
                        template.data.enrolledPrograms.set(reservations);
                        template.data.enrolledSelectiveWeekPlans.set(selectiveWeekPlans);
                    }
                })
            },
            onSave: (e, i, formFieldData, bundledPlanData, changeInNumberOfDays) => {
                const currentOrg = Orgs.current();
                const newlySelectedDays = formFieldData.map(elem => elem.value === 'on' ? elem.name : null).filter(elem => elem !== null);
                const effectiveDate = moment.tz(formFieldData.find(elem => elem.name === 'startDate').value, "MM/DD/YYYY", currentOrg.getTimezone()).valueOf();
                const bundleNewlySelectedDays = bundledPlanData ? bundledPlanData.map(elem => elem.value === 'on' ? elem.name : null).filter(elem => elem !== null) :  null;
                const bundleEffectiveDate = bundledPlanData ? moment.tz(bundledPlanData.find(elem => elem.name === 'startDate').value, "MM/DD/YYYY", currentOrg.getTimezone()).valueOf() : null;

                const options = {
                    reservation,
                    effectiveDate,
                    selectedDays: newlySelectedDays,
                    linkedPlan,
                    daysChanged: RegistrationUtils.compareDays(reservation.recurringDays, newlySelectedDays),
                    changeInNumberOfDays,
                    billingCycle: BillingUtils.getPositionInBillingCycle(linkedPlan, effectiveDate, currentOrg, selectedPersonObj)
                }

                const bundleOptions = bundledPlanData ? {
                    reservation: bundleReservation,
                    effectiveDate: bundleEffectiveDate,
                    selectedDays: bundleNewlySelectedDays,
                    linkedPlan: bundleLinkedPlan,
                    daysChanged: RegistrationUtils.compareDays(bundleReservation.recurringDays, bundleNewlySelectedDays),
                    changeInNumberOfDays,
                    billingCycle: BillingUtils.getPositionInBillingCycle(bundleLinkedPlan, bundleEffectiveDate, currentOrg, selectedPersonObj)
                } : null;

                if (options.daysChanged || (bundleOptions && bundleOptions.daysChanged)) {
                    Meteor.callAsync('parentUpdateRegistrationDays', options, bundleOptions).then(async (res) => {
                        if (res) {
                            // rebuild reservations after updates
                            const defaultGroupId = template.data.parentData.defaultGroupId;
                            await Meteor.callAsync("getEnrolledPrograms", selectedPersonObj._id, defaultGroupId).then((result) => {
                              const {reservations, selectiveWeekPlans} = result
                              template.data.enrolledPrograms.set(reservations.filter(reservations => !reservations.isSelectiveWeeks && reservations.program));
                              template.data.enrolledSelectiveWeekPlans.set(selectiveWeekPlans);
                            })
                            mpSwal.fire('Success', res.message, 'success').then(() => {
                                $("#selectDaysModal").modal('hide');
                            });
                        }
                    }).catch((err) => {
                        if (err) {
                            mpSwal.fire('Error', err.message, 'error');
                        }
                    });
                }
                $("#selectDaysModal").modal('hide');
            }
        });
    }
});