import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_reservationsTab.html';
import {
  calculateReservationWeeklyHours,
  calculateWorkedHoursCurrentWeek
} from "../reservations/reservationsWeeklyHours";
import moment from "moment-timezone";
import { AvailableCustomizations } from "../../../lib/customizations";
import { Log } from '../../../lib/util/log';
import {RegistrationUtils} from "../../../lib/util/registrationUtils";
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from "../../../lib/collections/people";
import { Orgs } from '../../../lib/collections/orgs';
import { processPermissions } from '../../../lib/permissions';
import { hideModal, showModal } from '../main';
import { Session } from 'meteor/session';
import _ from '../../../lib/util/underscore';
import './_punchCards';
import '../registrationFlow/_planCard';
import { Reservations } from '../../../lib/collections/reservations';
import '../reservations/reservationFormModal';
import '../reservations/reservationCancelFormModal';
import '../simpleModal/simpleModal';
import { offlinePreventCheck } from '../../../mpweb';
import $ from 'jquery';

const refreshView = new ReactiveVar(false);
Template._reservationsTab.onCreated(function() {
  const timezone = Orgs.current().getTimezone();
  this.filterStartDate = new ReactiveVar(new moment.tz(timezone).format("MM/DD/YYYY"));
  this.filterEndDate = new ReactiveVar(new moment.tz(timezone).add(1, "month").format("MM/DD/YYYY"));
  this.scheduleView = new ReactiveVar("recurring");
  this.totalHoursScheduled = new ReactiveVar(0);
  this.reservationsData = new ReactiveVar([]);
  var instance = this;
  instance.autorun( function() {
      instance.subscribe('theReservations', {
          startDate: instance.filterStartDate.get(),
          endDate: instance.filterEndDate.get(),
          selectedPerson: instance.data._id,
          includeCancellations: true
      });
      instance.subscribe('theTimeCards', {
          startDate: new moment.tz(timezone).startOf('week').format("MM/DD/YYYY"),
          endDate: new moment.tz(timezone).format("MM/DD/YYYY"),
          peopleIds: [instance.data._id]
      });
      getReservations(instance);
      // When the reservation form modal closes, reload the reservations
      $(document).on('hidden.bs.modal', '#reservationFormModal', () => {
          getReservations(instance);
      });
  });
});

Template._reservationsTab.helpers({
  currentView: function () {
    return Template.instance().scheduleView.get();
  },
  canHaveReservation: function () {
    return (_.contains(["person", "staff"], this.type) &&
        processPermissions({
          assertions: [{context: "reservations", action: "edit"}],
          evaluator: (person) => person.type === "admin"
        })
    ) ? true : false;
  },
  "scheduledDateFormatted": function () {
    return moment.tz(this.scheduledDate, Orgs.current().getTimezone()).format("MM/DD/YYYY");
  },
  "scheduledLength": function () {
    let out = "";
    if (this.scheduledTime) out = out + this.scheduledTime;
    if (this.scheduledEndTime) out = out + " - " + this.scheduledEndTime;
    return out;
  },
  'userCanAddOrModifyReservations': function () {
    return processPermissions({
      assertions: [{context: "reservations", action: "edit"}],
      evaluator: (person) => person.type === "admin"
    }) ? true : false;
  },
  "idToEdit": function () {
    return this.originalId || this._id;
  },
  "reservations": function () {
      return Template.instance().reservationsData.get();
  },
  "showEditLink": function () {
    return Template.instance().scheduleView.get() === "recurring" || Template.instance().scheduleView.get() === "prior" || this.scheduledDate >= moment.tz(Orgs.current().getTimezone()).startOf("day");
  },
  "showDeleteLink": function () {
    return !this.recurringFrequency || this.scheduledDate >= moment.tz(Orgs.current().getTimezone()).startOf("day");
  },
  "showRemoveCancellation": function () {
    return processPermissions({
      assertions: [{context: "reservations", action: "edit"}],
      evaluator: (person) => person.type === "admin" || person.type === "staff"
    });
  },
  "scheduleTypeNameFor": function (id) {
    const stdef = _.find(Orgs.current().getScheduleTypes(), st => st._id === id);
    return stdef && stdef.type;
  },
  "dayLetter": function (day) {
    const dayMap = {"sat": "Sa", "sun": "Su", "mon": "M", "tue": "T", "wed": "W", "thu": "R", "fri": "F"};
    return dayMap[day];
  },
  "dateFields": function () {
    return {
      filterStartDate: Template.instance().filterStartDate,
      filterEndDate: Template.instance().filterEndDate,
    }
  },
  shouldShowEditAndRemovePlan() {
    return processPermissions({
      assertions: [{context: "billing/invoices/planAssignments", action: "edit"}],
      evaluator: (person) => person.type === "admin"
    });
  },
  "totalHours": function () {
      const instance = Template.instance();
      instance.totalHoursScheduled.set(calculateTotalHoursScheduled(instance) + calculateWorkedHoursCurrentWeek(instance.data._id, Orgs.current()._id));

    const sum = Template.instance().totalHoursScheduled.get();
    var h = Math.floor(sum / 3600);
    var m = Math.floor(sum % 3600 / 60);

    var hDisplay = h > 0 ? h + (h === 1 ? " hour " : " hours ") : "";
    var mDisplay = m > 0 ? m + (m === 1 ? " minute " : " minutes ") : "";
    return hDisplay + mDisplay;
  },
  "overtime": function () {
    const regularHours = 40 * 3600;
    return Template.instance().totalHoursScheduled.get() > regularHours;

  },
  "hasPunchCards": function () {
    let org = Orgs.current();
    if (org && org.hasCustomization(AvailableCustomizations.PUNCH_CARDS)) {
      const selectedPerson = Template.instance().data;
      return selectedPerson.totalPunchCardDays?.length > 0;
    } else {
      return false;
    }
  },
    "refreshView": function () {
        getReservations(Template.instance());
        refreshView.set(false);
    },
    "needsRefresh": function () {
        return refreshView.get();
    }
});

Template.reservationScheduleModal.events({
    'click #recurringNonRecurringBtn': function (event, template) {
        hideModal("#reservationScheduleModal");
        showModal("reservationFormModal", template.data, "#reservationFormModal");
    },
    'click #selectiveWeekBtn': function (event, template) {
        hideModal("#reservationScheduleModal");
        const personId = FlowRouter.current().params._id;
        Meteor.callAsync("getAvailableSelectiveWeekPlansForChild", { childId: personId }).then((result) => {
            showModal("selectiveWeekModal", { availablePlans: result }, "#selectiveWeekModal");
        }).catch((error) => {
            mpSwal.fire("Error", error.reason, "error");
        });
    }
});

Template._reservationsTab.events({
  'click #newReservationLink': function(event, template) {
      if (!Orgs.current()?.availableBillingPlans()?.find(plan => plan.details?.selectiveWeeks?.length > 0)) {
          showModal("reservationFormModal", {
              reservationId: null,
              selectedReservationPersonId: this._id,
              personType: this.type,
              totalHoursScheduled: template.totalHoursScheduled.get()
          }, "#reservationFormModal");
      } else {
          showModal("reservationScheduleModal", {
              reservationId: null,
              selectedReservationPersonId: this._id,
              personType: this.type,
              totalHoursScheduled: template.totalHoursScheduled.get()
          }, "#reservationScheduleModal");
      }
  },
  "change #reservationsStartDate": function(event, template) {
    template.filterStartDate.set( $("#reservationsStartDate").val());
  },
  "change #reservationsEndDate": function(event, template) {
    template.filterEndDate.set( $("#reservationsEndDate").val());
  },
  'click .editReservationLink': function(event, template) {
    showModal("reservationFormModal", {reservationId: $(event.target).attr("data-id"), totalHoursScheduled: template.totalHoursScheduled.get()}, "#reservationFormModal");
  },
    'click .deleteReservationLink': function (event, template) {
    var reservationId = $(event.target).attr("data-id");
    mpSwal.fire({
      title: "Delete Recurring Schedule",
      text: "This is a recurring schedule. Deleting it will alter future and prior Enrollment Forecast reports for this child. Are you sure you want to delete it?",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      cancelButtonText: "No",
      confirmButtonText: "Yes",
    }).then((result) => {
      if (result.value) {
         Meteor.callAsync("deleteReservation", reservationId).then((result) => {
             getReservations(template);
         }).catch((error) => {
             mpSwal.fire("Error", error.reason, "error");
         });
      }
    });
  },
  'click .cancelReservationLink': function(event) {
    var reservationId = $(event.target).attr("data-id");
    if (!this.recurringFrequency) {
      Session.set("reservationId", reservationId);
      showModal("reservationCancelFormModal", {reservationId: reservationId}, "#reservationCancelFormModal");
    } else {
      mpSwal.fire({
        title: "Cancellation Type",
        text: "This is a recurring event. Do you want to cancel this instance or delete the series?",
        icon: "warning",
        showCancelButton: true,
        showCloseButton: true,
        cancelButtonText: "Delete Series",
        confirmButtonText: "Cancel Instance",
      }).then(async (result) => {
        if (result.value) {
          // Session.set("reservationId", reservationId);
          // Session.set("reservationInstanceDate", this.scheduledDate);
          showModal("reservationCancelFormModal", {reservationId: reservationId, reservationInstanceDate: this.scheduledDate}, "#reservationCancelFormModal");
        } else if (result.dismiss == "cancel") {
          await Meteor.callAsync("deleteReservation", this.originalId);
        }
      });
    }
  },
  "click .editCancellationLink": function(event, template) {
    if (offlinePreventCheck()) return false;
    var reservationId = $(event.target).attr("data-id");
    if (!this.recurringFrequency) {
      showModal("reservationCancelFormModal", {reservationId: reservationId}, "#reservationCancelFormModal");
    }
  },
  "click .removeCancellationLink": function(event, template) {
    if (offlinePreventCheck()) return false;
    var reservationId = $(event.target).attr("data-id");
    if (!this.recurringFrequency) {
      mpSwal.fire({
        title: "Are you sure?",
        text: "This will remove the cancellation entry. If this entry occurs in the future, you can now create a new schedule entry for this date.",
        icon: "warning",
        showCancelButton: true,
        closeOnConfirm:false,
      }).then(result => {
        if (result.value) {
          Meteor.callAsync("removeCancellation", {reservationId}).then((result) => {
            mpSwal.fire("Cancellation removed");
          }).catch((error) => {
            mpSwal.fire("Cancellation removed");
          });
        }
      });
    }
  },
  "click .schedule-view": function(event, template) {
    event.preventDefault();
    var newViewType = $(event.target).attr("id").replace("schedule-view-", "");
    template.scheduleView.set(newViewType);
  },
  "click .btnEditPlan": (e, instance) => {
    e.preventDefault();
		const personId = instance.data._id;
		const planId = $(e.target).data("id");
        const createdAt = $(e.target).data("created-at");
		const existingPlan = _.find(instance.data.billing.enrolledPlans, (p) => { return p._id === planId && p.createdAt === createdAt; });
		const planInfo = _.find(Orgs.current().billing.plansAndItems, (p)=>{return p._id==existingPlan._id;});
        const timezone = Orgs.current().getTimezone();
		showModal("simpleModal", {
			title:"Edit Billing Plan",
			template: "personBillingEnrollPlanModal",
			data: {
				enrollmentDate: new moment.tz(existingPlan.enrollmentDate, timezone).format("MM/DD/YYYY"),
				expirationDate: existingPlan.expirationDate && new moment.tz(existingPlan.expirationDate, timezone).format("MM/DD/YYYY"),
				enrollmentForecastStartDate: existingPlan.enrollmentForecastStartDate && new moment.tz(existingPlan.enrollmentForecastStartDate, timezone).format("MM/DD/YYYY"),
				enrollmentForecastEndDate: existingPlan.enrollmentForecastEndDate && new moment.tz(existingPlan.enrollmentForecastEndDate, timezone).format("MM/DD/YYYY"),
				personId: personId,
				existingPlan: existingPlan,
				overrideRate: existingPlan.overrideRate,
				existingPlanAmount: existingPlan.overrideRate || planInfo.amount,
				reservationId: existingPlan.reservationId
			},
			onSave: (e, i, formFieldData) => {
				formFieldData.personId = personId;
				formFieldData.allocations = ((formFieldData.allocations || "") != "") ? JSON.parse(formFieldData.allocations) : null;
				formFieldData.existingPlanId = existingPlan._id;
                formFieldData.createdAt = createdAt;
        Meteor.callAsync("modifyBillingPlan", formFieldData).then((result) => {
          $("#simpleModal").modal('hide');
        }).catch((error) => {  
          $(e.target).html('Save').prop("disabled", false);
          mpSwal.fire("Error", error.reason, "error");
        });
			}
		});
	},
  "click .btnUnlinkPlan"(e, instance) {
    e.preventDefault();
    const personId = instance.data._id;
    const $button = $(event.target);
    $button.removeData();

    const planId = $button.data("id");
    const createdAt = $button.data("created-at");
    mpSwal.fire({
      title: "Are you sure?",
      text: "This will remove the link between the schedule item and the billing plan.",
      icon: "warning",
      showCancelButton: true,
      closeOnConfirm:false,
    }).then(result => {
      if (result.value) {
        Meteor.callAsync("unlinkBillingPlan", {personId, planId, createdAt}).then(() => {
          getReservations(instance);
          mpSwal.fire("Billing plan unlinked.");
        }).catch(() => {
          getReservations(instance);
          mpSwal.fire("Billing plan unlinked.");
        });
      }
    });
  }
})

Template._reservationsDateSelector.rendered = function() {
  var self = this;
  const timezone = Orgs.current().getTimezone();

  $("#reservationsStartDate").datepicker({ autoclose: true, todayHighlight: true }).on("changeDate", function(e) {
    self.data.filterStartDate.set(new moment.tz(e.target.value, "MM/DD/YYYY", timezone).format("MM/DD/YYYY"));
  })
  $("#reservationsEndDate").datepicker({ autoclose: true, todayHighlight: true }).on("changeDate", function(e) {
    self.data.filterEndDate.set(new moment.tz(e.target.value, "MM/DD/YYYY", timezone).format("MM/DD/YYYY"));
  })
}

Template._reservationsDateSelector.helpers({
  "formattedStartDate": function() {
    return this.filterStartDate.get();
  },
  "formattedEndDate": function() {
    return this.filterEndDate.get();
  },
})

Template.selectiveWeekModal.onCreated(function () {
    this.isProcessing = new ReactiveVar(false);
    this.activePlanId = new ReactiveVar(null);
    this.rerenderPlan = new ReactiveVar(false);
    this.org = Orgs.current();
    this.selectedPlans = new ReactiveVar([]);
    const personId = FlowRouter.current().params._id;
    this.child = People.findOne({ _id: personId });
    this.existingPlans = this.child?.enabledBillingPlans(moment().tz(this.org.getTimezone()).startOf("day").valueOf()) ?? [];
    this.onHoldPrograms = new ReactiveVar([]);
    Meteor.callAsync("getOnHoldRegistrationsWithChild", this.org._id, personId).then((registrations) => {
        if (registrations) {
            for (const registration of registrations) {
                const childIndex = registration.data.children.findIndex(c => c._id === personId);
                self.onHoldPrograms.set([...self.onHoldPrograms.get(), ...registration.data.plans[childIndex]]);
            }
        }
    });
});

Template.selectiveWeekModal.events({
    'change #registrationPlan': function (event, instance) {
        instance.activePlanId.set(event.target.value);
        instance.selectedPlans.set([]);
        instance.rerenderPlan.set(true);
    },
    'click .create-selective-week-reservation': function (event, instance) {
        event.preventDefault();
        if (instance.isProcessing.get()) {
            return;
        }
        if (!instance.selectedPlans.get().length) {
            mpSwal.fire("Error", "Please select at least one week for a program.", "error");
            return;
        }

        instance.isProcessing.set(true);
        const stayOpen = event.currentTarget.dataset.stayopen;
        const selectiveWeekPlan = instance.selectedPlans.get()[0];
        Meteor.callAsync("createSelectiveWeekReservations", instance.child._id, selectiveWeekPlan).then((result) => {
        }).catch((err) => {
          mpSwal.fire("Error", err.reason || err.message, "error");
        }).finally(() => {  
          // To force a refresh of the reservations list
          refreshView.set(true);

          instance.isProcessing.set(false);
          hideModal("#selectiveWeekModal");
          if (stayOpen === "true") {
              showModal("reservationScheduleModal", {
                  reservationId: null,
                  selectedReservationPersonId: instance.child._id,
                  personType: instance.child.type,
                  totalHoursScheduled: calculateTotalHoursScheduled(instance) + calculateWorkedHoursCurrentWeek(instance.child._id, instance.org._id)
              }, "#reservationScheduleModal")
          }
        });
    }
});

Template.selectiveWeekModal.helpers({
    parentData() {
        return {
            availablePlans: Template.instance().data.availablePlans,
            availableTimePeriods: Template.instance().org.billing.timePeriods ?? [],
            availableBillingFrequencies: Template.instance().org?.availableBillingFrequencies(),
            orgTimezone: Template.instance().org.getTimezone(),
            availableBundles: [],
            existingPlans: Template.instance().existingPlans,
            onHoldPlansVar: Template.instance().onHoldPrograms
        };
    },
    plans() {
        return Template.instance().data.availablePlans;
    },
    isRerenderPlanCard() {
        return Template.instance().rerenderPlan.get();
    },
    rerenderPlanCard() {
        Template.instance().rerenderPlan.set(false);
    },
    selectedPlan() {
        const plan = Template.instance().data.availablePlans.find(plan => plan._id === Template.instance().activePlanId.get());
        if (!plan) {
            return;
        }
        const serviceDatesResult = RegistrationUtils.getServiceDates(
            plan,
            Template.instance().org.billing.timePeriods ?? [],
            Template.instance().org.getTimezone()
        );
        plan.serviceDates = serviceDatesResult;
        return plan;
    },
    selectedPlans() {
        return Template.instance().selectedPlans;
    }
});

function calculateTotalHoursScheduled(instance) {
  const startOfWeek = moment.tz(Orgs.current().getTimezone()).startOf('week');
  const reservationsThisWeek = Reservations.find({
    recurrenceId: {"$exists":false},
    "$or": [
      {scheduledEndDate:  null},
      {scheduledEndDate: {"$gte": startOfWeek.valueOf()}}
    ],
    selectedPerson: instance.data._id
  }, {sort: {scheduledDate: 1}}).fetch();
  let sum = 0;
  for (const reservation of reservationsThisWeek) {
    sum += calculateReservationWeeklyHours(reservation);
  }
  return sum;
}

function getReservations(instance) {
    const filterStartDate = instance.filterStartDate.get();
    const filterEndDate = instance.filterEndDate.get();
    const currentView = instance.scheduleView.get();
    const _id = instance.data._id;
    const defaultGroupId = instance.data.defaultGroupId;
    
    Meteor.callAsync(
      'getCurrentAndPriorReservations',
      filterStartDate,
      filterEndDate,
      currentView,
      _id,
      defaultGroupId).then((result) => {
        instance.reservationsData.set(result);
      }).catch((error) => {
        Log.error('Error fetching reservations:', error);
      });
}

