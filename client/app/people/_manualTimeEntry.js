import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_manualTimeEntry.html';
import {TimeCardUtils} from "../../../lib/util/timeCardUtils";
const moment = require('moment-timezone');
import { Orgs } from '../../../lib/collections/orgs';
import { processPermissions } from '../../../lib/permissions';

Template._manualTimeEntry.onCreated(function() {
	this.startTime = new ReactiveVar(null);
	this.endTime = new ReactiveVar(null);
	this.datePicker = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.totalHours = new ReactiveVar(0);
	this.sortedTimeCards = new ReactiveVar([]);
	this.conflict = new ReactiveVar(false);

	this.checkForConflicts = () => {
		const start = this.startTime.get();
		const end = this.endTime.get();
		const timeCards = this.sortedTimeCards.get();
		const today = this.datePicker.get();

		const newTimecard = {
			checkInTime: moment(start, "HH:mm").format("h:mm A"),
			checkInDate: today,
			checkOutTime: moment(end, "HH:mm").format("h:mm A"),
			checkOutDate: today
		};

		const conflict = TimeCardUtils.getConflictRange(newTimecard, timeCards);
		if (this.totalHours.get() < 0) {
			$('#btnSave').prop('disabled', true);
			$('#errorMessage').text('Invalid Card: this person has a negative total hours.');
			this.conflict.set(true);
		} else if (conflict || this.totalHours.get() < 0) {
			$('#btnSave').prop('disabled', true);
			const checkInTime = conflict.start.format("h:mm A");
			const checkOutTime = conflict.end.format("h:mm A");
			$('#errorMessage').text(`Invalid Card: this person has another time card on this date from ${checkInTime} to ${checkOutTime}.`);
			this.conflict.set(true);
		} else {
			$('#errorMessage').text('');
			$('#btnSave').prop('disabled', false);
			this.conflict.set(false);
		}
	};


});

Template._manualTimeEntry.onRendered(function() {
	const org = Orgs.current();
	const timezone = org?.getTimezone();
	const personId = this.data.personId;
	$('#btnSave').prop('disabled', true);

	if (!org.hasCustomization("people/timeCardsLocked/enabled")) {
		$("#datePicker").datepicker({ autoclose: true, todayHighlight: true });
	}

	$("#datePicker").datepicker({ autoclose: true, todayHighlight: true }).on('changeDate', (event) => {
		this.datePicker.set($(event.target).datepicker('getFormattedDate'));
	});

	const lockedDate = Orgs.current()?.valueOverrides?.timeCardsLockedDate;
	const lockedTime = "9:00 am";
	const lockedDateTime = new moment.tz(lockedDate + " " + lockedTime, timezone);
	const now = new moment.tz(timezone);
	const passedLockedDate = now.isSameOrAfter(lockedDateTime);

	if (passedLockedDate) {
		const canEditLockedTimeCards = processPermissions({
			assertions: [{ context: "people/editLockedTimeCards", action: "edit"}],
			evaluator: (person) => person.type === "admin" || person.type === "staff"
		});

		if (canEditLockedTimeCards) {
			$("#datePicker").datepicker({ autoclose: true, todayHighlight: true });
		} else {
			$("#datePicker").datepicker({ autoclose: true, todayHighlight: true, startDate: lockedDateTime.format("MM/DD/YYYY") });
		}
	}

	this.autorun(() => {
		const start = this.startTime.get();
		const end = this.endTime.get();
		if (start && end) {
			const duration = moment.duration(moment(end, "HH:mm").diff(moment(start, "HH:mm")));
			const hours = duration.asHours();
			this.totalHours.set(hours.toFixed(2));
		} else {
			this.totalHours.set(0.00);
		}
	});

	this.autorun(() => {
		const date = this.datePicker.get();
		const orgId = Orgs.current()._id;
		Meteor.callAsync('findTimeCardsOnSpecificDayForPerson', orgId, date, personId).then(result => {
			this.sortedTimeCards.set(result);
		});
	});

	this.autorun(() => {
		const start = this.startTime.get();
		const end = this.endTime.get();
		const date = this.datePicker.get();
		if (start && end && date) {
			this.checkForConflicts();
		}
	});
});

Template._manualTimeEntry.helpers({
	todayDate() {
		return new moment().format("MM/DD/YYYY");
	},
	classifications() {
		return Orgs.current().availableCustomPayTypes();
	},
	totalHours() {
		const hours = Template.instance().totalHours.get()
		if (hours === 0) {
			return hours.toFixed(2);
		}
		return hours;
	},
});

Template._manualTimeEntry.events({
	'change #startTime'(event, instance) {
		instance.startTime.set(event.target.value);
	},
	'change #endTime'(event, instance) {
		instance.endTime.set(event.target.value);
	},
	'change #datePicker'(event, instance) {
		instance.datePicker.set(event.target.value);
	}
});
