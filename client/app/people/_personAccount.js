import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_personAccount.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import {AvailableCustomizations, CustomizationsService} from "../../../lib/customizations";
import { AvailablePermissions, AvailableActionTypes } from '../../../lib/constants/permissionsConstants';
import { Log } from '../../../lib/util/log';
import { People } from '../../../lib/collections/people';
import './_personAccount.html';
import $ from 'jquery';
import _ from '../../../lib/util/underscore';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { processPermissions } from '../../../lib/permissions';
import { hideModal } from '../main';
import { Session } from 'meteor/session';
import Swal from 'sweetalert2/dist/sweetalert2';
import { PeopleTypes } from '../../../lib/constants/peopleConstants';
import { UserUtils } from '../../../lib/util/usersUtil';

Template._personAccount.onCreated(function () {
    this.editPersonNewPersonId = new ReactiveVar();
    this.defaultGroupChanged = new ReactiveVar(false);
    this.canSeeStaffWorkableOrgs = new ReactiveVar(false);
    this.staffWorkableOrgs = new ReactiveVar([]);
    this.personUser = new ReactiveVar(null);
    this.personUserPrimaryOrgId = new ReactiveVar(null);
});

Template._personAccount.events({
    'change #inputDefaultGroup': function () {
        Template.instance().defaultGroupChanged.set(true);
    },
    "click #person-account-update-integration":function(event, template){
        const currentPerson = People.findOne(FlowRouter.current().params._id);
        if (!currentPerson) {
            return;
        }
    
        Meteor.callAsync('checkForIntegrationSourceIds', { personId: FlowRouter.current().params._id }).then((response) => {    
            if (response) {
                if (response.hubspotId) {
                    currentPerson.hubspotId = response.hubspotId;
                }
                if (response.importIndex) {
                    currentPerson.importIndex = response.importIndex;
                }
            }
        }).catch((error) => {
            mpSwal.fire('Error', error.reason, 'error');
            return
        }).finally(() => {
            mpSwal.fire({
                title: 'Manual edit Integration/Source',
                html: `
                    <label>AirSlate ID
                        <input class="form-control form-control-lg form-control-solid" type="text" id="importIndex" value="${currentPerson?.importIndex ?? ''}" >
                    </label>
                    
                    <label>Hubspot ID
                        <input class="form-control form-control-lg form-control-solid" type="text" id="hubspotId" value="${currentPerson?.hubspotId ?? ''}" >
                    </label>
                `,
                showCancelButton: true,
                confirmButtonText: 'Save',
                showLoaderOnConfirm: true,
                preConfirm: async (confirm) => {
                    let importIndex = $("#importIndex").val(), hubspotId = $("#hubspotId").val();
                    await Meteor.callAsync('updateHubspotId', { 'personId': FlowRouter.current().params._id, 'hubspotId': hubspotId, 'importIndex': importIndex });
                },
                allowOutsideClick: () => !Swal.isLoading()
            }).then((result) => {
                if (result.isConfirmed) {
                    mpSwal.fire(
                        'Success',
                        'The Hubspot ID has been updated!',
                        'success'
                    )
                }
            });
        });
    },
    "click .remove-designations": function () {
        const personId = Session.get("currentEditPersonId");
        Meteor.callAsync('removeDesignations', personId).then(() => {
            mpSwal.fire('Success', 'Designations Removed', 'success');
            $("#inputDesignations").multiselect("rebuild");
        }).catch((error) => {
            mpSwal.fire('Error', error.reason, 'error');
        });
    },
    "click .remove-waitlistAddedDate": function () {
        const personId = Session.get("currentEditPersonId");
        Meteor.callAsync('removeWaitListAdded', personId).then(() => {
            mpSwal.fire('Success', 'Wait List Date Removed', 'success');
        }).catch((error) => {
            mpSwal.fire('Error', error.reason, 'error');
        });
    },
    "click #person-account-form-save": async function (e, i) {
        $("#person-account-form-save").prop('disabled', true);
        const emailValue = $("#inputEmailAddress").val();
        const person = People.findOne({_id: Session.get('currentEditPersonId')});
        if (person && emailValue && emailValue !== person.getEmailAddress()) {
            Meteor.callAsync('checkEmailExistsForOrg', emailValue).then((res) => {
                if (res) {
                    $("#person-account-form-save").prop('disabled', false);
                    mpSwal.fire('Error', 'This email is already in use--please select another', 'error');
                } else {
                    mpSwal.fire({
                        text: "Clicking “Proceed” will immediately make the old email address unusable for logging in. Do you wish to proceed?",
                        showCancelButton: true,
                        showCloseButton: true,
                        cancelButtonText: "Cancel",
                        confirmButtonText: "Proceed"
                    }).then(async (confirmResult) => {
                        if (confirmResult.value) {
                            await saveProfile(e, i);
                        } else {
                            $("#person-account-form-save").prop('disabled', false);
                        }
                    });
                }
            }).catch((error) => {
                mpSwal.fire({
                    text: "Clicking “Proceed” will immediately make the old email address unusable for logging in. Do you wish to proceed?",
                    showCancelButton: true,
                    showCloseButton: true,
                    cancelButtonText: "Cancel",
                    confirmButtonText: "Proceed"
                }).then(async (confirmResult) => {
                    if (confirmResult.value) {
                        await saveProfile(e, i);
                    } else {
                        $("#person-account-form-save").prop('disabled', false);
                    }
                });
            });
        } else {
            await saveProfile(e, i);
        }
  },
	'submit form': function(event, template) {
		event.preventDefault();
		$(".person-save-button").prop('disabled', true);


	},
	'change #inputPersonType': function(event) {
		Session.set("editPersonModalPersonType", $("#inputPersonType").val());
	},
	'click #createPerson': function() {
		$("#stayOpen").val("false");
	},
	'click #createPersonAndStayOpen': function() {
		$("#stayOpen").val("true");
	},
	'click #createPersonAndAddFamily': function() {
		$("#stayOpen").val("add-family");
    },
    'change #primaryOrg': function (event, instance) {
        instance.personUserPrimaryOrgId.set(event.target.value);
	}
});


Template._personAccount.rendered = function() {
	var self = this;
  if (this.data && this.data._id) {
    this.editPersonNewPersonId.set(this.data._id);
    Session.set("currentEditPersonId", this.data._id);
    Session.set("editPersonModalPersonType", this.data.type)
  } else if (Session.equals("editPersonModalPersonType", "prospect")) {
    $("#inputPersonType").val("prospect");
  } else {
    Session.set("editPersonModalPersonType", "person");
  }

  $("#inputDesignations").multiselect();

  $("#waitlistAddedDate").datepicker({
    format: 'mm/dd/yyyy',
    keepEmptyValues: false,
    autoclose: true,
    todayHighlight: true,
    clearBtn: true
  });

    // Set whether the user can see the staff workable locations
    this.canSeeStaffWorkableOrgs.set(
        Orgs.current().hasCustomization(AvailableCustomizations.STAFF_WORK_LOCATIONS) &&
        Session.get("editPersonModalPersonType") === 'staff'
    );

    // Get the staff workable locations if accessible
    refreshWorkableOrgs(this, true);
};

Template._personAccount.helpers({
	'addFamilyButtonLabel': function() {
		return Template.instance().editPersonNewPersonId.get() ? "Save & Add Another Family Member" : "Save & Add Family Member";
	},
	'addingFamilyMemberInfo': function() {
		const newPersonId = Template.instance().editPersonNewPersonId.get();
		if (newPersonId) return People.findOne({_id: newPersonId});
	},
	'groups': function() {
		return Groups.find({
			orgId: Meteor.user()["orgId"]
		});
	},
	'person': function() {
		var returnData;
		if (Session.equals("currentEditPersonId", "")) {
			var defaultPersonType = !Session.equals("editPersonModalPersonType", "prospect") ? "person" : "prospect";
			const addFamilyPersonId = Template.instance().editPersonNewPersonId.get();
			if (addFamilyPersonId) defaultPersonType = "family";
			returnData = { _id:"", type: defaultPersonType, firstName:"", lastName: "", emailAddress:""};
		}
		else if (Session.equals("currentEditPersonId", null ))
			returnData = { type: ""};
		else
			returnData = People.findOne(Session.get("currentEditPersonId"));
		if (returnData)
			Session.set("editPersonModalPersonType", returnData.type);
		return returnData;
	},

	'editOrNewRecordLabel': function() {
		return (!Session.equals("currentEditPersonId", "") && !Session.equals("currentEditPersonId", null)) ?
			"Edit" : "New";
	},
	'showWillSendInvitation': function() {
		return Session.get("editPersonModalPersonType") != "prospect" && (Session.equals("currentEditPersonId", "") || Session.equals("currentEditPersonId", null));
	},
	'isNew': function() {
		return (!Template.instance().editPersonNewPersonId.get()) &&
			((Session.equals("currentEditPersonId", "") || Session.equals("currentEditPersonId", null)));
	},
	'showAddFamilyButton': function() {
		return (Session.get("editPersonModalPersonType") == "person" ||
			Template.instance().editPersonNewPersonId.get() ) &&
			(Session.equals("currentEditPersonId", "") || Session.equals("currentEditPersonId", null));
	},
    'isChild': function() {
        return Session.get("editPersonModalPersonType") === "person";
    },
    'isChildOrFamily': function () {
        return ['person', 'family'].includes(Session.get("editPersonModalPersonType"));
    },
	'showEmailAddress': function() {
        const selectedPersonType = Session.get("editPersonModalPersonType");
        // Hide contact information from staff, if the customization is on to do so
        if (!CustomizationsService.canViewContactInfo()) {
            return false;
        }
        return ["family", "admin", "staff"].indexOf(selectedPersonType) > -1;
	},
    'canEditEmailAddress': function() {
        return processPermissions({
            assertions: [{ context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.type == "admin",
            throwError: false
        });
    },
  'currentGroupId': function() {
		if (Session.equals("currentEditPersonId", "") || Session.equals("currentEditPersonId", null))
			return "";
		else {
			const currentPerson = People.findOne(Session.get("currentEditPersonId"));
      return currentPerson && currentPerson["defaultGroupId"];
    }
	},
    'isVendor': function() {
        return Session.get('editPersonModalPersonType') === 'vendor';
    },
	'canHaveDefaultGroup': function() {
		return !['admin', 'family', 'prospect', 'vendor'].includes(Session.get("editPersonModalPersonType"));
	},
	'isPending': function() {
		return Session.get("currentEditPersonPending");
	},
  'showSendInvitation': function() {
    const personType = Session.get("editPersonModalPersonType");
    return UserUtils.canSeeInviteStatus(personType, Orgs.current().hasCustomization(AvailableCustomizations.HIDE_SEND_INVITATION_FOR_STAFF_AND_ADMINS))
  },
	'targetGroupId': function() {
		if (Session.equals("currentEditPersonId", "") || Session.equals("currentEditPersonId", null))
			return "";
		else
			return People.findOne(Session.get("currentEditPersonId"))["targetGroupId"];
	},
  "designations": function(tag) {
    const org = Orgs.current();
    return org.valueOverrides.designations;
  },
  'selectedDesignation': function(tag) {
    const currentPerson = People.findOne(FlowRouter.current().params._id);
    if (currentPerson) {
      const designations = currentPerson.designations || [];
      return (designations.indexOf(tag) > -1) ? true : false;
    }
    return false
  },
  'hasDesignations': function () {
    if (Session.get('editPersonModalPersonType') === 'vendor') {
        return false;
    }
    const org = Orgs.current();
    if (org && org.valueOverrides && org.valueOverrides.designations &&  org.valueOverrides.designations.length > 0) {
      return true;
    }
    return false;
  },
  'hasWaitListAddedDate': function() {
    const currentPerson = People.findOne(FlowRouter.current().params._id);
    if (currentPerson) {
      const designations = currentPerson.designations || [];
      return (designations.indexOf("Wait List") > -1) ? true : false;
    }
    return false;
  },
	'canHaveTargetGroup': function() {
    const org = Orgs.current();
    const currentPerson = People.findOne(Session.get("currentEditPersonId"));
    let hasWaitList = false
    if (org && org.valueOverrides && org.valueOverrides.designations && org.valueOverrides.designations.indexOf("Wait List") > -1) {
            hasWaitList = currentPerson &&
                _.contains(["prospect", "person"], currentPerson.type) &&
                currentPerson.designations &&
                currentPerson.designations.indexOf("Wait List") > -1;
        }
		return Session.get("editPersonModalPersonType") == "prospect" || hasWaitList;
	},

    canSeeType: function() {
		return Meteor.user().fetchPerson().type === "admin";
	},

    canEditType: function() {
        if (Template.instance().data.isCreate) {
            return processPermissions({
                assertions: [{ context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS, action: AvailableActionTypes.EDIT}],
                evaluator: (person) => person.type === "admin",
                throwError: false
            });
        } else {
            return processPermissions({
                assertions: [{ context: AvailablePermissions.PEOPLE_EDIT_USER_TYPES, action: AvailableActionTypes.EDIT }],
                evaluator: (person) => person.type === 'admin',
                throwError: false
            });
        }
    },

    'editEmployeeIdADPCustomization': function() {
        return processPermissions({
            assertions: [{ context: AvailablePermissions.PEOPLE_EMPLOYEE_ID_ADP, action: AvailableActionTypes.EDIT }],
            evaluator: (person) => person.superAdmin === true
        });
    },
    'showEmployeeIdADPCustomization': function() {
        return processPermissions({
            assertions: [{ context: AvailablePermissions.PEOPLE_EMPLOYEE_ID_ADP, action: AvailableActionTypes.READ }],
            evaluator: (person) => person.superAdmin === true
        });
    },
    'hasADPIntegration': function() {
        return Orgs.current().hasCustomization('people/uploadAdp');
    },
    'isAdminOrStaff': function() {
        const currentPerson = People.findOne(Session.get("currentEditPersonId"));
        return currentPerson && (currentPerson.type === 'admin' || currentPerson.type === 'staff');
    },
	'showStaffType': function() {
		return Orgs.current().hasCustomization("people/types/customerSpecificStaffTypes") && Session.get("editPersonModalPersonType") === "staff";
	},
	'availableStaffTypes': function() {
		return Orgs.current().valueOverrides && Orgs.current().valueOverrides.staffTypes;
	},
  "hideSave": function() {
    var hideSave = Template.instance().data.hideSave;
    return (hideSave) ? "hidden" : "";
  },
  "hideSendInvite": function () {
    // if we have the id params then we're in a person detail context;
    var hideSendInvite = FlowRouter.current().params._id;
    return (hideSendInvite) ? "hidden" : "";
  },
  'integrationInfo': function(tag) {
    let chechContext = processPermissions({
			assertions: [{ context: AvailablePermissions.INTEGRATIONS_AIRSLATE_MANUAL_EDIT, action: AvailableActionTypes.SHOW }],
			evaluator: (person) => person.type == "admin",
			throwError: false
		});
    const currentPerson = People.findOne(FlowRouter.current().params._id);
    if (!currentPerson) {
        return false;
    }
    const CheckPermisions = People.findOne(Meteor.user().personId);
    let outstr = [];
    if (currentPerson?.importSlateDocumentId ) {
      outstr.push("AirSlate" + (currentPerson.hasOwnProperty("importIndex") ? ", Index " + currentPerson.importIndex : ""))
    }
    if (currentPerson?.hubspotId) {
      outstr.push("Hubspot ID " + currentPerson.hubspotId);
    }
	if (chechContext || CheckPermisions.role == "lspMasterAdmin") {
		return outstr.length > 0 ? outstr.join("<br/>") : " ";
	}
    return outstr.length > 0 ? outstr.join("<br/>") : false;
  },
  'integrationInfoManual': function(tag) {
    const checkPermisions = People.findOne(Meteor.user().personId);
    const checkContext = processPermissions({
        assertions: [{ context: AvailablePermissions.INTEGRATIONS_AIRSLATE_MANUAL_EDIT, action: AvailableActionTypes.EDIT }],
        evaluator: (person) => person.type === 'admin',
        throwError: false
    });
    
    if (checkContext || checkPermisions.role?.includes['lspMasterAdmin']) {
      return true;
    }
    return false;
  },
    typeFieldIsDisabled(type) {
        if (!Template.instance().data.isCreate) {
            return !canAddEditType(type);
        }
        return false;

    },

    canAddType: function (personType) {
        const { isCreate, type } = Template.instance().data;

        if (isCreate) {
            return canAddEditType(personType);
        }

        return type === personType || canAddEditType(personType);
    },

    canSeeStaffWorkableOrgsSection() {
        return Template.instance().canSeeStaffWorkableOrgs.get() && !!Template.instance().personUser.get();
    },
    canSeeStaffPrimaryOrgSelectList() {
        return processPermissions({
            assertions: [{ context: 'people/manageAdpStaffAndAdmins', action: 'read' }],
            evaluator: (person) => false // no fallback for now
        })
    },
    isDisabledStaffPrimaryOrgSelectList() {
        return !processPermissions({
            assertions: [{ context: 'people/manageAdpStaffAndAdmins', action: 'edit' }],
            evaluator: (person) => false // no fallback for now
        })
    },
    canEditAbleToWorkField(){
        return processPermissions({
            assertions: [{ context: 'designateStaffWorkLocations', action: 'edit' }],
            evaluator: (person) => person.masterAdmin
        });
    },
    canViewAbleToWorkField(){
        return processPermissions({
            assertions: [{ context: 'designateStaffWorkLocations', action: 'read' }],
            evaluator: (person) => person.masterAdmin
        });
    },
    getStaffPrimaryOrgId() {
        return Template.instance().personUserPrimaryOrgId.get();
    },
    getStaffPossiblePrimaryOrgs() {
        return Template.instance().staffWorkableOrgs.get()?.filter(org => org.edge && !org.enableSwitchOrg);
    },
    getStaffPossibleWorkableOrgs() {
        Log.debug('Staff workable orgs', Template.instance().staffWorkableOrgs.get())
        return Template.instance().staffWorkableOrgs.get()?.filter(org => (
            org.edge &&
            !org.enableSwitchOrg &&
            org._id !== Template.instance().personUserPrimaryOrgId.get()
        ));
    },
    isDisabledWorkableOrgsSelectList() {
        const currentUser = Meteor.user();
        const currentPerson = currentUser?.fetchPerson();
        if (currentPerson?.superAdmin) {
            return false;
        }
        return currentUser?.orgId !== Template.instance().personUser.get()?.primaryOrgId ||
            currentPerson?.type !== 'admin';
    },
    getPersonUserWorkableMembershipOrgIds() {
        return Template.instance().personUser.get()?.membership
            ?.filter(m => m.orgId !== Template.instance().personUserPrimaryOrgId.get())
            ?.map(m => m.orgId) ?? [];
    },
    'getEntityTypePerson': function () {
		return orgLanguageTranformationUtil.getEntityType('person');
	},
    'getEntityTypeFamily': function () {
		return orgLanguageTranformationUtil.getEntityType('family');
	},
    'getEntityTypeProspect': function () {
		return orgLanguageTranformationUtil.getEntityType('prospect');
	},
    'getEntityTypeStaff': function () {
		return orgLanguageTranformationUtil.getEntityType('staff');
	},
    'getEntityTypeAdmin': function () {
		return orgLanguageTranformationUtil.getEntityType('admin');
	},
    'getEntityTypeVendor': function () {
		return orgLanguageTranformationUtil.getEntityType('vendor');
	}
});

const setDefaultGroupFlag = function (template) {
    const person = People.findOne(Session.get("currentEditPersonId"));
    const defaultId = person.getChildDefaultGroup() ? person.getChildDefaultGroup()._id : null;
    const newGroupId = $("#inputDefaultGroup option:selected").val();
    if (defaultId && newGroupId) {
        const currentResGroup = person.getChildCurrentSchedules().filter(schedule => schedule.groupId === defaultId);

        if (!template.defaultGroupChanged.get() || newGroupId === defaultId || !currentResGroup) {
            return {};
        }
        return {reservation: currentResGroup, groupId: newGroupId};
    }
    return {};

}

const saveProfile = async function (event, template) {
    const personData = {};
    personData.firstName = $("#inputFirstName").val().replaceAll('"', "'").trim();
    personData.lastName = $("#inputLastName").val().replaceAll('"', "'").trim();
    personData.emailAddress = $("#inputEmailAddress")?.val()?.trim();
    personData.sendInvite = $("#inputSendInvite").prop("checked");
    personData.type = $("#inputPersonType option:selected").val();
    personData.middleName = $("#inputMiddleName")?.val()?.replaceAll('"', "'")?.trim();
    personData.preferredName = $("#inputPreferredName")?.val()?.replaceAll('"', "'")?.trim();
    personData.defaultGroupId = $("#inputDefaultGroup option:selected").val();
    personData.targetGroupId = $("#inputTargetGroup option:selected").val();
    personData.designations = $("#inputDesignations").val() || [];
    personData.waitlistAddedDate = $("#waitlistAddedDate").val();
    personData.employeeId = $("#inputEmployeeIdEdit").val();
    personData.editNewPersonFamilyId = template.editPersonNewPersonId.get();
    if (Orgs.current().hasCustomization("people/types/customerSpecificStaffTypes") && personData.type === "staff") {
        personData.staffType = $("#inputStaffType option:selected").val();
    }
    const successAction = function (newPersonId) {
        if (newPersonId) {
            Session.set("currentEditPersonId", null);
            try {
                $("#newPersonForm")[0].reset()
            } catch (e) {
                console.log("error when reseting newPersonForm");
            }
        }
        const inputPersonType = $("#inputPersonType");
        const stayOpen = $("#stayOpen");

        if (stayOpen.val() === "true") {
            template.editPersonNewPersonId.set(null);
            Session.set("currentEditPersonId", "");
        } else if (stayOpen.val() === "add-family") {
            if (!template.editPersonNewPersonId.get()) {
                template.editPersonNewPersonId.set(newPersonId);
            }
            Session.set("currentEditPersonId", "");
            inputPersonType.val("family");
            inputPersonType.prop("disabled", true);
        } else if (newPersonId) {
            template.editPersonNewPersonId.set(null);
            if (newPersonId) {
                FlowRouter.redirect("/people/" + newPersonId + "#profile");
            }
            hideModal("#_addPersonModal");
        } else {
            mpSwal.fire("Success", "Account Information Saved", "success");
        }

        if (personData.sendInvite && (personData.type === 'staff' || personData.type === 'admin')) {
            try {
                gtag('event', 'page_view', {page_title: 'onboarding?type=invite-staff'});
            }
            catch (e) {
                console.log("Error sending gtag event");
            }
        }

        if (personData.sendInvite && personData.type === 'family') {
            try {
                gtag('event', 'page_view', {page_title: 'onboarding?type=invite-family'});
            }
            catch (e) {
                console.log("Error sending gtag event");
            }
        }
    }

    if (!Session.equals("currentEditPersonId", "") && !Session.equals("currentEditPersonId", null)) {
        const payload = setDefaultGroupFlag(template);
        if (!_.isEmpty(payload)) {
            for ( const res of payload.reservation){
                await Meteor.callAsync('updateReservationGroupOnly', res._id, payload.groupId)
            }
        }
        personData.personId = Session.get("currentEditPersonId");

        const updateAction = function (data) {
            Meteor.callAsync('updatePerson', data).then(() => {
                $("#person-account-form-save").prop('disabled', false);
                successAction();
                refreshWorkableOrgs(template, false);
            }).catch((error) => {
                $("#person-account-form-save").prop('disabled', false);
                mpSwal.fire("Error", error.reason, "error");
            });
        }

        if (template.canSeeStaffWorkableOrgs.get()) {
            // Check if new orgs are added or removed

            // Get the old membership org ids, including the old primary org id
            const previousMembershipOrgIds = template.personUser.get()?.membership?.map(m => m.orgId) ?? [];
            if (template.personUser.get()?.primaryOrgId && !previousMembershipOrgIds.includes(template.personUser.get().primaryOrgId)) {
                // Add the primary org id in case it wasn't already in the membership list, even though it probably should be
                previousMembershipOrgIds.push(template.personUser.get().primaryOrgId);
            }

            // Get the new membership org ids, including the new primary org id
            personData.workableOrgsExist = !!document.getElementById('workableOrgs');
            const newMembershipOrgIds = Array.from(document.querySelectorAll('#workableOrgs option:checked'))
                .map(option => option.value);
            if (template.personUserPrimaryOrgId.get() && !newMembershipOrgIds.includes(template.personUserPrimaryOrgId.get())) {
                newMembershipOrgIds.push(template.personUserPrimaryOrgId.get());
            }

            // Get the all the org ids in the workable orgs select list
            const workableOrgsSelect = document.getElementById('workableOrgs');
            const allWorkableOrgsIds = Array.from(workableOrgsSelect?.options ?? []).map(option => option.value);

            // Get the orgs that were added
            const addedOrgIds = newMembershipOrgIds.filter(orgId => !previousMembershipOrgIds.includes(orgId));

            // Get the orgs that were removed
            const removedOrgIds = previousMembershipOrgIds.filter(orgId => !newMembershipOrgIds.includes(orgId) && allWorkableOrgsIds.includes(orgId));

            const orgNamesMap = new Map();
            template.staffWorkableOrgs.get()?.forEach(org => {
                orgNamesMap.set(org._id, org.name);
            });
            const fullName = `${ personData.firstName } ${ personData.lastName }`;

            // Build the contents of the alert
            let alertContent = '';
            if (addedOrgIds.length > 0) {
                alertContent += `<p style="font-weight: bold;">${ fullName } will be invited to join:</p>`;
                // Get the names of the added orgs
                alertContent += `<ul style="list-style-type: none; padding: 0;">`;
                addedOrgIds.forEach(orgId => {
                    alertContent += `<li>${ orgNamesMap.get(orgId) }</li>`;
                });
                alertContent += `</ul>`;
            }
            if (removedOrgIds.length > 0 && removedOrgIds.filter(orgId => !!orgNamesMap.get(orgId)).length) {
                alertContent += `<p style="font-weight: bold;">${ fullName } will be deactivated at:</p>`;
                // Get the names of the removed orgs
                alertContent += `<ul style="list-style-type: none; padding: 0;">`;
                removedOrgIds.forEach(orgId => {
                    if (!orgNamesMap.get(orgId)) {
                        return;
                    }
                    alertContent += `<li>${ orgNamesMap.get(orgId) }</li>`;
                });
                alertContent += `</ul>`;
            }

            // Set the current membership orgs; include orgs that aren't within the customer's org hierarchy
            personData.membershipOrgIds = [
                ...newMembershipOrgIds,
                ...previousMembershipOrgIds.filter(orgId => !newMembershipOrgIds.includes(orgId) && !removedOrgIds.includes(orgId))
            ];
            if (!alertContent) {
                personData.primaryOrgId = template.personUserPrimaryOrgId.get();
                updateAction(personData);
            } else {
                mpSwal.fire({
                    title: "Please confirm",
                    html: alertContent,
                    showCancelButton: true,
                }).then(result => {
                    if (result.value) {
                        personData.primaryOrgId = template.personUserPrimaryOrgId.get();
                        updateAction(personData);
                    } else {
                        $("#person-account-form-save").prop('disabled', false);
                    }
                });
            }
        } else {
            updateAction(personData);
        }
    } else {
        const callInsertPerson = function () {
            Meteor.callAsync('insertPerson', personData).then((result) => {
                $("#person-account-form-save").prop('disabled', false);
                successAction(result);
            }).catch((error) => {
                if (error && error.reason && error.reason == "Name already exists.") {
                    mpSwal.fire({
                        title: "Name already exists",
                        text: "A person with that name already exists, possibly deactivated. Would you like to create this new person anyway?",
                        showCancelButton: true,
                        showCloseButton: true,
                        cancelButtonText: "No",
                        confirmButtonText: "Yes"
                    }).then((confirmResult) => {
                        if (confirmResult.value) {
                            $("#person-account-form-save").prop('disabled', true);
                            personData.overrideNameCheck = true;
                            callInsertPerson();
                        } else {
                            $("#person-account-form-save").prop('disabled', false);
                            mpSwal.fire("Save Canceled", "The save was canceled.", "info");
                        }
                        ;
                    });
                }else if (error && error.reason && error.reason == "Something went wrong. Please check your credentials.") {
                    $("#person-account-form-save").prop('disabled', false);
                    mpSwal.fire("Error", "Email already exists", "error");
                } else if (error) {
                    $("#person-account-form-save").prop('disabled', false);
                    mpSwal.fire("Error", error.reason, "error");
                } 
            });
        };
        callInsertPerson();
    }
}

function refreshWorkableOrgs(instance, isNew) {
    if (instance.canSeeStaffWorkableOrgs.get()) {
        Meteor.callAsync('getUserFromPersonId', Session.get('currentEditPersonId')).then((res) => {
            instance.personUser.set(res);
            instance.personUserPrimaryOrgId.set(res?.primaryOrgId);
        })
        Meteor.callAsync('getOrgsInUsersOrg').then((res) => {
            if (res) {
                instance.staffWorkableOrgs.set(res);
                if (isNew) {
                    $("#primaryOrg").select2({})
                        .on("change", () => {
                            instance.personUserPrimaryOrgId.set($("#primaryOrg").val());
                            setTimeout(() => {
                                $("#workableOrgs").multiselect("rebuild");
                            }, 200);
                        });
                    $("#workableOrgs").multiselect({
                        enableFiltering: true,
                        enableCaseInsensitiveFiltering: true
                    });
                    setTimeout(() => {
                        $("#workableOrgs").multiselect("rebuild");
                    }, 200);
                }
            }
        }).catch((err) => {
            Log.debug('Error getting orgs in user\'s org', err);
        });
    }
}

function canAddEditType(type) {
    switch (type) {
        case 'admin':
            return canAddAdmin();
        case 'staff':
            return canAddStaff();
        case 'family':
            return canAddFamily();
        case 'person':
            return canAddChild();
        case 'prospect':
            return canAddProspect();
        case 'vendor':
            return canAddVendor();
        default:
            return false;
    }
}

function canAddAdmin() {
    const orgContexts = Orgs.current().availablePermissionsContexts() || Object.values(AvailablePermissions);
    const hasAdminContext = orgContexts.includes(AvailablePermissions.PEOPLE_ADD_ADMINS);
    if (!hasAdminContext) {
        return processPermissions({
            assertions: [{
                context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS,
                action: AvailableActionTypes.EDIT
            }],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    } else {
        // If they have both or just this one it doesn't matter, this one is the one that should be used.
        return processPermissions({
            assertions: [{context: AvailablePermissions.PEOPLE_ADD_ADMINS, action: AvailableActionTypes.EDIT}],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    }
}

function canAddStaff() {
    const orgContexts = Orgs.current().availablePermissionsContexts() || Object.values(AvailablePermissions);
    const hasStaffContext = orgContexts.includes(AvailablePermissions.PEOPLE_ADD_STAFF);
    if (!hasStaffContext) {
        return processPermissions({
            assertions: [{
                context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS,
                action: AvailableActionTypes.EDIT
            }],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    } else {
        // If they have both or just this one it doesn't matter, this one is the one that should be used.
        return processPermissions({
            assertions: [{context: AvailablePermissions.PEOPLE_ADD_STAFF, action: AvailableActionTypes.EDIT}],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    }
}

function canAddFamily() {
    const orgContexts = Orgs.current().availablePermissionsContexts() || Object.values(AvailablePermissions);
    const hasFamilyContext = orgContexts.includes(AvailablePermissions.PEOPLE_ADD_FAMILY);
    if (!hasFamilyContext) {
        return processPermissions({
            assertions: [{
                context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS,
                action: AvailableActionTypes.EDIT
            }],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    } else {
        // If they have both or just this one it doesn't matter, this one is the one that should be used.
        return processPermissions({
            assertions: [{context: AvailablePermissions.PEOPLE_ADD_FAMILY, action: AvailableActionTypes.EDIT}],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    }
}

function canAddChild() {
    const orgContexts = Orgs.current().availablePermissionsContexts() || Object.values(AvailablePermissions);
    const hasChildrenContext = orgContexts.includes(AvailablePermissions.PEOPLE_ADD_CHILDREN);
    if (!hasChildrenContext) {
        return processPermissions({
            assertions: [{
                context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS,
                action: AvailableActionTypes.EDIT
            }],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    } else {
        // If they have both or just this one it doesn't matter, this one is the one that should be used.
        return processPermissions({
            assertions: [{context: AvailablePermissions.PEOPLE_ADD_CHILDREN, action: AvailableActionTypes.EDIT}],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    }
}

function canAddProspect() {
    return Orgs.current().hasCustomization(AvailableCustomizations.INQUIRIES_ENABLED);
}

function canAddVendor() {
    const orgContexts = Orgs.current().availablePermissionsContexts() || Object.values(AvailablePermissions);
    const hasVendorContext = orgContexts.includes(AvailablePermissions.PEOPLE_ADD_VENDOR);
    const hasZkTecoCustomization = Orgs.current().hasCustomization(AvailableCustomizations.DOOR_LOCKS_ZKTECO);

    if (!hasZkTecoCustomization) {
        return false;
    }

    if (!hasVendorContext) {
        return hasZkTecoCustomization; // Before we only checked that the customization was here before showing this option.
    } else {
        // If they have both or just this one it doesn't matter, this one is the one that should be used.
        return processPermissions({
            assertions: [{ context: AvailablePermissions.PEOPLE_ADD_VENDOR, action: AvailableActionTypes.EDIT}],
            evaluator: (person) => person.type === "admin",
            throwError: false
        });
    }
}
