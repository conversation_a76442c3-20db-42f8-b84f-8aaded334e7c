<template name="_documents">
  {{#if collapseLayout}}
    {{#if documentRepositoryItems}}
      <div class="pt-6">
        <div class="font-size-h3 font-weight-bold" style="color:unset">
          {{fullName child.firstName child.lastName}}
        </div>
      
        <table class="w-100">
        {{#each docGroup in (documentRepositoryGroups documentRepositoryItems)}}
        
            <tr >
              <td colspan="2">
                <div class="w-100 mt-6 mb-3" style="border-top:1px solid var(--secondary)">
                </div>
                
                <div>
                  <span data-cy="document-section" style="text-transform:uppercase">{{docGroup.section}}</span>
                </div>
              </td>
            </tr>
            
            {{#each docGroup.documentRepositoryItems}}
              <tr data-cy="document-repo-item">
              
                <td>
                  <div data-cy="document-name" style="font-weight:bold; font-size:18px; color:var(--primary)">{{name}}</div>
                  {{#if signatureRequired}}
                  <div style="font-size:13px">Document Signature Required</div>
                  {{/if}}
                  {{#if acknowledgmentRequired}}
                  <div style="font-size:13px">Acknowledgment Required</div>
                  {{/if}}
                  <div style="font-size:11px">Status: {{status}}</div>
                  {{#if displayTemplateOptionResult}}
                    <div>{{displayTemplateOptionResult}}</div>
                  {{/if}}
                  {{#if isArchived}}
                  <span style="color:#ff0000">Archived</span>
                  {{/if}}
                </td>
                <td>
                  <div class="dropdown">
                    <div class="btn btn-primary font-weight-bolder btn-text-white mr-4" data-toggle="dropdown">
                      <i data-cy="documents-actions-btn" class="fad-regular fad fa-angle-down mr-2" style="color:#fff"></i>Action
                    </div>
                    <div class="dropdown-menu dropdown-menu-left">
                      {{#if showReviewDocument}}
                        <span data-cy="review-completed-document" class="dropdown-item clickable-row btnReviewDocument" data-id="{{documentId}}">Review Completed Document</span>
                      {{/if}}
                      {{#if canEditDocuments}}
                        {{#if showViewTemplate}}
                          <span data-cy="view-completed-document" class="dropdown-item clickable-row btnViewTemplate" data-id="{{templateId}}">View Document Template</span>
                        {{/if}}
                        {{#if showUploadDocument}}
                          <span data-cy="upload-completed-document" class="dropdown-item clickable-row btnUploadDocument" data-id="{{templateId}}">Upload Completed Document</span>
                        {{/if}}      
                        
                        {{#if showApproveRejectDocument}}
                          <span data-cy="reject-completed-document" class="dropdown-item clickable-row btnRejectDocument" data-id="{{documentId}}">Reject Completed Document</span>
                          <span data-cy="approve-completed-document" class="dropdown-item clickable-row btnApproveDocument" data-id="{{documentId}}">Approve Completed Document</span>
                        {{/if}}
                        {{#if showExemptDocument}}
                          <span data-cy="exempt-document" class="dropdown-item clickable-row btnExemptDocument" data-id="{{templateId}}">Exempt Person from Document</span>
                        {{/if}}
                        {{#if showUnassignDocument}}
                          <span data-cy="unassign-document" class="dropdown-item clickable-row btnUnassignDocument" data-id="{{templateId}}">Un-assign Document</span>
                        {{/if}}
                        {{#if showArchiveDocument}}
                          <span data-cy="archive-document" class="dropdown-item clickable-row btnArchiveDocument" data-id="{{templateId}}">Archive Document</span>
                        {{/if}}
                        {{#if acknowledgmentRequired}}
                          <span data-cy="document-ack" class="dropdown-item clickable-row document-ack" data-id="{{templateId}}" data-name="{{name}}">Acknowledge Document</span>
                        {{/if}}
                      {{/if}}
                    </div>
                  </div>
                </td>
              </tr>
            {{/each}}
            
        {{/each}}
        </table>
      </div>
    {{/if}}
  {{else}}
    <div class="row">
      <div class="col-lg-9 col-xl-6 offset-xl-3 mb-5">
        {{#if canAssignDocuments}}
        <div class="btn btn-primary pull-right" id="btnAssignDocument">Assign Document</div>
        
        <span class="font-size-h3">Documents</span>
        <div class="checkbox-list">
          <label class="checkbox checkbox-primary">
            <input type="checkbox" value="admin" id='chkShowArchived'>
            <span></span>
            Show Archived
          </label>
        </div>
        {{/if}}
      </div>
    </div>
    <div class="row col-12">
      
      {{#each documentRepositoryItems}}
        <div data-cy="document-repo-item" class="{{#if collapseLayout}}row {{else}} d-flex flex-row flex-grow-1 py-4{{/if}}" style="border-top:1px solid var(--secondary)">
          <div class="{{#if collapseLayout}}col-12 {{else}}col-3 d-flex flex-column{{/if}}">
            <span data-cy="document-section" style="text-transform:uppercase">{{section}}</span>
            <span data-cy="document-name" style="font-weight:bold; font-size:18px; color:var(--primary)">{{name}}</span>
          </div>
          <div class="{{#if collapseLayout}}col-12 {{else}}col-3 d-flex flex-column{{/if}}">
            <span style="text-transform:uppercase">Status</span>
            <span>{{status}}</span>
            {{#if isArchived}}
            <span style="color:#ff0000">Archived</span>
            {{/if}}
          </div>
          <div class="{{#if collapseLayout}}col-6 {{else}}col-3{{/if}}">
            <div class="dropdown">
              <div class="btn btn-primary font-weight-bolder btn-text-white mr-4" data-toggle="dropdown">
                <i data-cy="documents-actions-btn" class="fad-regular fad fa-angle-down mr-2" style="color:#fff"></i>Action
              </div>
              <div class="dropdown-menu dropdown-menu-left">
                {{#if showReviewDocument}}
                  <span data-cy="review-completed-document" class="dropdown-item clickable-row btnReviewDocument" data-id="{{documentId}}">Review Completed Document</span>
                {{/if}}
                {{#if canEditDocuments}}
                  {{#if showViewTemplate}}
                    <span data-cy="view-completed-document" class="dropdown-item clickable-row btnViewTemplate" data-id="{{templateId}}">View Document Template</span>
                  {{/if}}
                  {{#if showUploadDocument}}
                    <span data-cy="upload-completed-document" class="dropdown-item clickable-row btnUploadDocument" data-id="{{templateId}}">Upload Completed Document</span>
                  {{/if}}      
                  
                  {{#if showApproveRejectDocument}}
                    <span data-cy="reject-completed-document" class="dropdown-item clickable-row btnRejectDocument" data-id="{{documentId}}">Reject Completed Document</span>
                    <span data-cy="approve-completed-document" class="dropdown-item clickable-row btnApproveDocument" data-id="{{documentId}}">Approve Completed Document</span>
                  {{/if}}
                  {{#if showExemptDocument}}
                    <span data-cy="exempt-document" class="dropdown-item clickable-row btnExemptDocument" data-id="{{templateId}}">Exempt Person from Document</span>
                  {{/if}}
                  {{#if showUnassignDocument}}
                    <span data-cy="unassign-document" class="dropdown-item clickable-row btnUnassignDocument" data-id="{{templateId}}">Un-assign Document</span>
                  {{/if}}
                  {{#if showArchiveDocument}}
                    <span data-cy="archive-document" class="dropdown-item clickable-row btnArchiveDocument" data-id="{{templateId}}">Archive Document</span>
                  {{/if}}
                {{/if}}
              </div>
            </div>
          </div>
          <div class="{{#if collapseLayout}}col-6 {{else}}col-3{{/if}}">
            {{#if displayTemplateOptionResult}}
              <span>{{displayTemplateOptionResult}}</span>
            {{else}}
              {{#if signatureRequired}}
                <span data-cy="document-signature" style="text-transform:uppercase">Document Signature Required</span>
              {{/if}}
              {{#if canEditDocuments}}
                {{#if acknowledgmentRequired}}
                  <div data-cy="document-ack" class="btn btn-primary font-weight-bolder" id="document-ack" data-id="{{templateId}}" data-name="{{name}}">Acknowledge Document</div>
                {{/if}}
              {{/if}}
            {{/if}}
          </div>
        </div>
      {{/each}}
    </div>
  {{/if}}
</template>
