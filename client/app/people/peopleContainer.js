import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './peopleContainer.html';
import './people';
import './dataValidation';

Template.peopleContainer.helpers({
	"activeIfSelected": function(tab, opt) {
		const selected = tab || "directory";

		return opt == selected ? "active" : "";
	},
  "shouldShowDataValidation": function(opt) {
		var user = Meteor.user();
		var person = user && user.fetchPerson();
		return (user && person && person.type == "admin");
  }
})
