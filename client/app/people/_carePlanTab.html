<template name="_carePlanTab">

	{{#with carePlan}}
	<div class="row">
		<div class="col-12">

			<div class="accordion accordion-toggle-arrow" id="carePlanAccordian">
			{{#each carePlanSection in carePlanSections}}
				<div class="card">
					<div class="card-header">
						<div class="card-title" data-toggle="collapse" data-target="#{{escapedSectionId carePlanSection.title}}">
							{{carePlanSection.title}}
						</div>
					</div>
					<div id="{{escapedSectionId carePlanSection.title}}" class="collapse careplan-section">
						<div class="card-body">
							{{#each field in carePlanSection.fields}}
							<div class="row mb-4">
								<input type="hidden" class="field-data-id" value="{{field.dataId}}">
								<input type="hidden" class="field-data-type" value="{{field.fieldType}}">
								<div class="col-3">
									{{field.label}}
								</div>
								<div class="col-8">
									<span class="fieldValue">
									{{#if trueIfEq field.fieldType "attachments"}}
										{{#each file in (currentValue field.dataId)}}
										<a href="#" onclick="window.open('{{file.mediaUrl}}', '_blank');" >{{file.name}}</a><br/>
										{{/each}}
									{{else}}
										<span style="white-space: pre-wrap;">{{currentValue field.dataId}}</span>
									{{/if}}
									</span>
									{{#if trueIfEq field.fieldType "text"}}
									<textarea class="form-control d-none">{{currentValue field.dataId}}</textarea>
									{{else if trueIfEq field.fieldType "select"}}
									<select class="form-control d-none" multiple={{field.multi}}>
										{{#if field.allowBlankOption}}
										<option value=""></option>
										{{/if}}
										{{#each fieldValue in field.fieldValues}}
											<option value="{{fieldValue}}" selected={{selectedIfContains (currentValue field.dataId) fieldValue}}>{{fieldValue}}</option>
										{{/each}}
									</select>
									{{else if trueIfEq field.fieldType "string"}}
									<input class="form-control d-none" type="text" value="{{currentValue field.dataId}}">
									{{else if trueIfEq field.fieldType "buttons"}}
									<div class="btn-group d-none">
										{{#each fieldValue in field.fieldValues}}
										<button type="button" class="btn btn-default {{activeIfEq (currentValue field.dataId) fieldValue}}" data-value="{{fieldValue}}">{{fieldValue}}</button>
										{{/each}}
									</div>
									{{else if trueIfEq field.fieldType "customerDefinedList"}}
									<textarea class="form-control d-none">{{currentValue field.dataId}}</textarea>
									<span class="d-none field-help" style="font-size:8px">One line per entry</span>
									{{else if trueIfEq field.fieldType "attachments"}}
									<span class="d-none file-maintenance">
										{{#each file in (currentValue field.dataId)}}
											{{file.name}} <a href="#" class="remove-attachment" data-fieldid="{{field.dataId}}" data-token="{{file.mediaToken}}">remove</a><br/>
										{{/each}}
											<div class="form-group">
												<label>Attach New File</label>
												<input type="file" class="carePlanInputFile" data-fieldid="{{field.dataId}}">          
											</div>
									</span>
									{{/if}}
								</div>
								<div class="col-1 text-center">
									<a class="edit-field"><i class="fa fa-fw fa-pencil-square-o"></i></a>
									<button class="btn btn-default d-none btn-xs field-save">Save</button>
									<a class="field-history"><i class="fa fa-fw fa-list"></i></a>
								</div>
							</div>
							{{/each}}
						</div>
					</div>
				</div>
			{{/each}}
			</div>
		</div>
			
	</div>
	{{/with}}
</template>
