<template name="_reservationsTab">
    <div class="d-flex flex-column-fluid">
        <div class="container">
            {{#if canHaveReservation}}
                <div class="d-flex flex-row flex-grow-1 justify-content-end align-items-center">
                    <div data-cy="new-schedule-item" class="btn btn-primary font-weight-bolder btn-text-white" id="newReservationLink">
                        <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>New Schedule Item
                    </div>
                </div>
            {{/if}}
            <div class="d-flex flex-column mb-10">

                <ul class="nav nav-pills" id="myTab" role="tablist">
                    <li class="nav-item">
                        <a data-cy="current-schedule" class="nav-link {{#if trueIfEq currentView 'recurring'}}active{{/if}} schedule-view font-weight-bolder" href="#" id="schedule-view-recurring">Current Schedule &amp; Transitions</a>
                    </li>
                    <li class="nav-item">
                        <a data-cy="prior-schedules" class="nav-link {{#if trueIfEq currentView 'prior'}}active{{/if}} schedule-view font-weight-bolder" href="#" id="schedule-view-prior">Prior Schedules</a>
                    </li>
                    <li class="nav-item">
                        <a data-cy="schedule-view-all" class="nav-link {{#if trueIfEq currentView 'all'}}active{{/if}} schedule-view font-weight-bolder" href="#" id="schedule-view-all">All Entries</a>
                    </li>
                </ul>
            </div>
            {{#unless trueIfEq totalHours ''}}
                <label data-cy="total-hours-scheduled" class="col-lg-9 col-xl-6 font-weight-bolder {{#if overtime}}text-danger{{/if}}">
                    Total Hours Scheduled: {{totalHours}}
                    <a href="#" data-toggle="tooltip" data-placement="right" title="This total is based on actual hours worked plus all remaining scheduled hours (from all schedules, if there are more than one) for this employee for the week selected"><i class="fad fad-primary fa-info-circle"></i></a>
                </label>
            {{/unless}}
            {{#if trueIfEq currentView 'prior'}}
                <div class="d-flex flex-column">
                    {{#each reservations}}
                        <div class="card card-custom gutter-b" data-id="{{_id}}">
                            <div class="card-body">
                                <div class="row">
                                    <div class="d-flex flex-column col-2">
                                        <label>Start Date</label>
                                        <div data-cy="start-date" class="d-flex flex-row flex-wrap align-items-center">
                                            {{formatDate scheduledDate "M/DD/YYYY"}}
                                        </div>
                                        {{#if current}}<span class="recurring-schedule-current-label">Current</span>{{/if}}
                                    </div>
                                    <div class="d-flex flex-column col-2">
                                        <label>End Date</label>
                                        {{#if scheduledEndDate}}<span>{{formatDate scheduledEndDate "M/DD/YYYY"}}</span>{{/if}}
                                    </div>
                                    <div data-cy="scheduled-group" class="d-flex flex-column col-2">
                                        <label>Group</label>
                                        {{targetGroupName}}
                                    </div>
                                    <div data-cy="days-scheduled" class="d-flex flex-column col-2">
                                        <label>Days</label>

                                        {{#each day in recurringDays}}
                                            {{dayLetter day}}&nbsp;
                                        {{/each}}

                                    </div>
                                    <div class="d-flex flex-column col-2" style="font-weight: bold;">
                                        <label>Type</label>
                                        <span data-cy="type-scheduled">{{scheduleTypeNameFor scheduleType}}</span>
                                    </div>

                                    <div data-cy="schedule-menu-options" class="d-flex flex-column col-1">
                                        <div class="d-flex flex-row flex-grow-1 justify-content-end">
                                            <div class="btn btn-icon btn-clean" data-toggle="dropdown" >
                                                <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                                            </div>
                                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">

                                                {{#if userCanAddOrModifyReservations}}
                                                    {{#if showEditLink}}
                                                        <span data-cy="schedule-edit-link" class="dropdown-item clickable-row editReservationLink" data-id="{{idToEdit}}">Edit</span>
                                                    {{/if}}
                                                    {{#if showDeleteLink}}
                                                        <span class="dropdown-item clickable-row deleteReservationLink" data-id="{{idToEdit}}">Delete</span>
                                                    {{/if}}
                                                {{/if}}

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{#if linkedPlan}}
                                    <div class="row">
                                        <div data-cy="linked-plan" class="col-8">
                                            <label>Linked Plan</label><br/>

                                            {{linkedPlan.masterPlanInfo.description}}<br/>
                                            {{formatCurrency linkedPlan.calculatedAmount}} {{getFrequencyLabel linkedPlan.masterPlanInfo.frequency}}<br/>
                                            {{#if linkedPlan.discountDescription}}
                                                {{{linkedPlan.discountDescription}}}<br/>
                                            {{/if}}
                                            {{#if shouldShowEditAndRemovePlan}}
                                                <a href="#" data-id="{{linkedPlan._id}}"  data-created-at="{{ linkedPlan.createdAt }}" class="btnEditPlan">Edit</a> | <a href="#" data-id="{{linkedPlan._id}}" data-created-at="{{ linkedPlan.createdAt }}" reservation-id="{{_id}}" class="btnUnlinkPlan">Unlink</a>
                                            {{/if}}
                                        </div>
                                    </div>
                                {{/if}}
                            </div>
                        </div>
                    {{/each}}
                </div>
            {{else if trueIfEq currentView 'all'}}
                {{> _reservationsDateSelector dateFields}}
                <div class="d-flex flex-column">
                    {{#each reservations}}
                        <div class="card card-custom gutter-b" data-id="{{_id}}" data-cy="{{ @index }}">
                            <div class="card-body">
                                <div class="row">
                                    <div class="d-flex flex-column col-5">
                                        <label>Date</label>
                                        <div class="d-flex flex-row flex-wrap align-items-center">
                                            <span data-cy="scheduled-date-formatted">{{scheduledDateFormatted}}</span>
                                            {{#if recurringFrequency}}
                                                <i class="fad fad-regular fad-primary fa-sync-alt ml-4" style="color:var(--primary)"></i>
                                            {{/if}}
                                            {{#if cancellationReason}}
                                                <span class="text-danger ml-4">Canceled: {{cancellationReason}}</span>
                                            {{/if}}
                                        </div>
                                    </div>
                                    <div class="d-flex flex-column col-3" style="font-weight: bold;">
                                        <label>Type</label>
                                        <span data-cy="scheduled-type">{{scheduleTypeNameFor scheduleType}}</span>
                                    </div>
                                    <div class="d-flex flex-column col-2">
                                        <label>Length</label>
                                        <span>{{scheduledLength}}</span>
                                    </div>
                                    <div data-cy="schedule-menu-options" class="d-flex flex-column col-2">
                                        <div class="d-flex flex-row flex-grow-1 justify-content-end">
                                            <div class="btn btn-icon btn-clean" data-toggle="dropdown" >
                                                <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                                            </div>
                                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                                {{#unless cancellationReason}}
                                                    {{#if userCanAddOrModifyReservations}}
                                                        {{#if showEditLink}}
                                                            <span data-cy="schedule-edit-link" class="dropdown-item clickable-row editReservationLink" data-id="{{idToEdit}}">Edit</span>
                                                        {{/if}}

                                                        <span class="dropdown-item clickable-row cancelReservationLink" data-id="{{idToEdit}}">Cancel</span>

                                                    {{/if}}
                                                {{else}}
                                                    <span class="dropdown-item clickable-row editCancellationLink" data-id="{{idToEdit}}">Edit Cancellation</span>
                                                    {{#if showRemoveCancellation}}
                                                        <span class="dropdown-item clickable-row removeCancellationLink" data-id="{{idToEdit}}">Remove Cancellation</span>
                                                    {{/if}}
                                                {{/unless}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {{/each}}
                </div>
            {{else if trueIfEq currentView 'recurring'}}
                {{#if hasPunchCards}}
                    <div class="d-flex flex-column">
                        <div class="card card-custom gutter-b" data-id="{{_id}}">
                            <div class="d-flex flex-column card-body bg-gray-200 rounded">
                                <h2 style="font-weight: bold" class="mb-2"> Punch Cards </h2>
                                <div class="row">
                                    {{> _punchCards}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        {{#if needsRefresh}}
                            {{refreshView}}
                        {{else}}
                            {{#each reservations}}
                                <div class="card card-custom gutter-b" data-id="{{_id}}">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="d-flex flex-column col-2">
                                                <label>Start Date</label>
                                                <div data-cy="start-date" class="d-flex flex-row flex-wrap align-items-center">
                                                    {{formatDate scheduledDate "M/DD/YYYY"}}
                                                </div>
                                                {{#if current}}<span class="recurring-schedule-current-label">Current</span>{{/if}}
                                            </div>
                                            <div class="d-flex flex-column col-2">
                                                <label>End Date</label>
                                                {{#if scheduledEndDate}}<span>{{formatDate scheduledEndDate "M/DD/YYYY"}}</span>{{/if}}
                                            </div>
                                            <div data-cy="scheduled-group" class="d-flex flex-column col-2">
                                                <label>Group</label>
                                                {{targetGroupName}}
                                            </div>
                                            <div data-cy="days-scheduled" class="d-flex flex-column col-2">
                                                <label>Days</label>

                                                {{#each day in recurringDays}}
                                                    {{dayLetter day}}&nbsp;
                                                {{/each}}

                                            </div>
                                            <div class="d-flex flex-column col-2" style="font-weight: bold;">
                                                <label>Type</label>
                                                <span data-cy="type-scheduled">{{scheduleTypeNameFor scheduleType}}</span>
                                            </div>

                                            <div data-cy="schedule-menu-options" class="d-flex flex-column col-1">
                                                <div class="d-flex flex-row flex-grow-1 justify-content-end">
                                                    <div class="btn btn-icon btn-clean" data-toggle="dropdown">
                                                        <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                                                    </div>
                                                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">

                                                        {{#if userCanAddOrModifyReservations}}
                                                            {{#if showEditLink}}
                                                                <span data-cy="schedule-edit-link" class="dropdown-item clickable-row editReservationLink" data-id="{{idToEdit}}">Edit</span>
                                                            {{/if}}
                                                            {{#if showDeleteLink}}
                                                                <span class="dropdown-item clickable-row deleteReservationLink" data-id="{{idToEdit}}">Delete</span>
                                                            {{/if}}
                                                        {{/if}}

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {{#if linkedPlan}}
                                            <div class="row">
                                                <div data-cy="linked-plan" class="col-8">
                                                    <label>Linked Plan</label><br/>

                                                    {{linkedPlan.masterPlanInfo.description}}<br/>
                                                    {{formatCurrency linkedPlan.calculatedAmount}} {{getFrequencyLabel
                                                        linkedPlan.masterPlanInfo.frequency}}<br/>
                                                    {{#if linkedPlan.discountDescription}}
                                                        {{{linkedPlan.discountDescription}}}<br/>
                                                    {{/if}}
                                                    {{#if shouldShowEditAndRemovePlan}}
                                                        <a href="#" data-id="{{linkedPlan._id}}" data-created-at="{{ linkedPlan.createdAt }}"
                                                           class="btnEditPlan">Edit</a> | <a href="#" data-id="{{linkedPlan._id}}"
                                                                                             data-created-at="{{ linkedPlan.createdAt }}"
                                                                                             reservation-id="{{_id}}"
                                                                                             class="btnUnlinkPlan">Unlink</a>
                                                    {{/if}}
                                                </div>
                                            </div>
                                        {{/if}}
                                    </div>
                                </div>
                            {{/each}}
                        {{/if}}
                    </div>
                {{else}}
                    <div class="d-flex flex-column">
                        {{#if needsRefresh}}
                            {{refreshView}}
                        {{else}}
                            {{#each reservations}}
                                <div class="card card-custom gutter-b" data-id="{{_id}}">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="d-flex flex-column col-2">
                                                <label>Start Date</label>
                                                <div data-cy="start-date" class="d-flex flex-row flex-wrap align-items-center">
                                                    {{formatDate scheduledDate "M/DD/YYYY"}}
                                                </div>
                                                {{#if current}}<span class="recurring-schedule-current-label">Current</span>{{/if}}
                                            </div>
                                            <div class="d-flex flex-column col-2">
                                                <label>End Date</label>
                                                {{#if scheduledEndDate}}<span>{{formatDate scheduledEndDate "M/DD/YYYY"}}</span>{{/if}}
                                            </div>
                                            <div data-cy="scheduled-group" class="d-flex flex-column col-2">
                                                <label>Group</label>
                                                {{targetGroupName}}
                                            </div>
                                            <div data-cy="days-scheduled" class="d-flex flex-column col-2">
                                                <label>Days</label>

                                                {{#each day in recurringDays}}
                                                    {{dayLetter day}}&nbsp;
                                                {{/each}}

                                            </div>
                                            <div class="d-flex flex-column col-2" style="font-weight: bold;">
                                                <label>Type</label>
                                                <span data-cy="type-scheduled">{{scheduleTypeNameFor scheduleType}}</span>
                                            </div>

                                            <div data-cy="schedule-menu-options" class="d-flex flex-column col-1">
                                                <div class="d-flex flex-row flex-grow-1 justify-content-end">
                                                    <div class="btn btn-icon btn-clean" data-toggle="dropdown">
                                                        <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                                                    </div>
                                                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">

                                                        {{#if userCanAddOrModifyReservations}}
                                                            {{#if showEditLink}}
                                                                <span data-cy="schedule-edit-link" class="dropdown-item clickable-row editReservationLink" data-id="{{idToEdit}}">Edit</span>
                                                            {{/if}}
                                                            {{#if showDeleteLink}}
                                                                <span class="dropdown-item clickable-row deleteReservationLink" data-id="{{idToEdit}}">Delete</span>
                                                            {{/if}}
                                                        {{/if}}

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {{#if linkedPlan}}
                                            <div class="row">
                                                <div data-cy="linked-plan" class="col-8">
                                                    <label>Linked Plan</label><br/>

                                                    {{linkedPlan.masterPlanInfo.description}}<br/>
                                                    {{formatCurrency linkedPlan.calculatedAmount}} {{getFrequencyLabel
                                                        linkedPlan.masterPlanInfo.frequency}}<br/>
                                                    {{#if linkedPlan.discountDescription}}
                                                        {{{linkedPlan.discountDescription}}}<br/>
                                                    {{/if}}
                                                    {{#if shouldShowEditAndRemovePlan}}
                                                        <a href="#" data-id="{{linkedPlan._id}}" data-created-at="{{ linkedPlan.createdAt }}"
                                                           class="btnEditPlan">Edit</a> | <a href="#" data-id="{{linkedPlan._id}}"
                                                                                             data-created-at="{{ linkedPlan.createdAt }}"
                                                                                             reservation-id="{{_id}}"
                                                                                             class="btnUnlinkPlan">Unlink</a>
                                                    {{/if}}
                                                </div>
                                            </div>
                                        {{/if}}
                                    </div>
                                </div>
                            {{/each}}
                        {{/if}}
                    </div>
                {{/if}}
            {{/if}}
        </div>
    </div>
</template>

<template name="_reservationsDateSelector">
    <div class="d-flex flex-row mb-8 pl-5">
        <div class="d-flex flex-column mr-8">
          <label>Start Date:</label>
          <div class="input-group">
            <input data-cy="reservations-start-date" class="form-control form-control-solid" type="text" id="reservationsStartDate" value="{{formattedStartDate}}">
          </div>
        </div> 
        <div class="d-flex flex-column">
          <label>End Date:</label>
          <div class="input-group">
            <input data-cy="reservations-end-date" class="form-control form-control-solid" type="text" id="reservationsEndDate" value="{{formattedEndDate}}">
          </div>
        </div>
    </div>
</template>

<template name="_reservationsTabAll">

</template>

<template name="_reservationsTabRecurring">
    <div class="d-flex flex-row mb-8">
        <div class="d-flex flex-column mr-8">
            <label>Start Date:</label>
            <div class="input-group">
                <input class="form-control form-control-solid" type="text" id="reservationsStartDate" value="{{formattedStartDate}}">
            </div>
        </div>
        <div class="d-flex flex-column">
            <label>End Date:</label>
            <div class="input-group">
                <input class="form-control form-control-solid" type="text" id="reservationsEndDate" value="{{formattedEndDate}}">
            </div>
        </div>
        {{#if canHaveReservation}}
            <div class="d-flex flex-row flex-grow-1 justify-content-end align-items-center">
                <div class="btn btn-primary font-weight-bolder btn-text-white" id="newReservationLink">
                    <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>New Schedule Item
                </div>
            </div>
        {{/if}}
    </div>
    <div class="d-flex flex-column">
        {{#each reservations}}
            <div class="card card-custom gutter-b" data-id="{{_id}}">
                <div class="card-body">
                    <div class="row">
                        <div class="d-flex flex-column col-5">
                            <label>Date</label>
                            <div class="d-flex flex-row flex-wrap align-items-center">
                                {{scheduledDateFormatted}}
                                {{#if recurringFrequency}}
                                    <i class="fad fad-regular fad-primary fa-sync-alt ml-4" style="color:var(--primary)"></i>
                                {{/if}}
                                {{#if cancellationReason}}
                                    <span class="text-danger ml-4">Canceled: {{cancellationReason}}</span>
                                {{/if}}
                            </div>
                        </div>
                        <div class="d-flex flex-column col-3" style="font-weight: bold;">
                            <label>Type</label>
                            <span>{{scheduleTypeNameFor scheduleType}}</span>
                        </div>
                        <div class="d-flex flex-column col-2">
                            <label>Length</label>
                            <span>{{scheduledLength}}</span>
                        </div>
                        <div class="d-flex flex-column col-2">
                            <div class="d-flex flex-row flex-grow-1 justify-content-end">
                                <div class="btn btn-icon btn-clean" data-toggle="dropdown" >
                                    <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                                </div>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                    {{#unless cancellationReason}}
                                        {{#if userCanAddOrModifyReservations}}
                                            {{#if showEditLink}}
                                                <span class="dropdown-item clickable-row editReservationLink" data-id="{{idToEdit}}">Edit</span>
                                            {{/if}}
                                            <span class="dropdown-item clickable-row cancelReservationLink" data-id="{{idToEdit}}">Cancel</span>
                                        {{/if}}
                                    {{else}}
                                        <span class="dropdown-item clickable-row editCancellationLink" data-id="{{idToEdit}}">Edit Cancellation</span>
                                        {{#if showRemoveCancellation}}
                                            <span class="dropdown-item clickable-row removeCancellationLink" data-id="{{idToEdit}}">Remove Cancellation</span>
                                        {{/if}}
                                    {{/unless}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{/each}}
    </div>
</template>

<template name="reservationScheduleModal">
    <div id="reservationScheduleModal" class="modal fade">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content" style="height: auto; min-height: 100%; max-width: 500px; margin: auto;">

                <div class="modal-header text-center" style="border: none;">
                    <h6 class="modal-title font-weight-bold" id="exampleModalLabel" style="max-width: 400px; margin: 0 auto; font-size: 24px; font-weight: bold;">
                        What kind of schedule would you like to create?
                    </h6>
                </div>

                <div class="modal-body text-center">
                    <button id="recurringNonRecurringBtn" class="btn btn-primary font-weight-bolder btn-text-white mb-2" style="width: 18em; white-space: nowrap; display: block; margin: 0 auto;">
                        Recurring/Non Recurring
                    </button>

                    <button id="selectiveWeekBtn" class="btn btn-primary font-weight-bolder btn-text-white" style="width: 18em; white-space: nowrap; display: block; margin: 0 auto;">
                        Selective Week
                    </button>
                </div>

            </div>
        </div>
    </div>
</template>

<template name="selectiveWeekModal">
    <div id="selectiveWeekModal" class="modal fade">
        <div class="modal-dialog modal-dialog-scrollable modal-lg">
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Schedule Entry</h5>
                    <button type="button" class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close">
                        <span class="fad-regular fad-primary fad fa-times"></span>
                    </button>
                </div>
                <div class="modal-body bg-white">
                    <div class="row mb-3">
                        <div class="offset-3 col-md-1">
                            <label for="registrationPlan" class="col-form-label">Program:</label>
                        </div>
                        <div class="col-md-4">
                            <select id="registrationPlan" class="form-control form-control-solid">
                                <option value="" selected>Please select a program</option>
                                {{# each plans }}
                                    <option value="{{ _id }}">{{ description }}</option>
                                {{/ each }}
                            </select>
                        </div>
                    </div>
                    <div class="row mt-5 d-flex flex-column align-items-center mx-4">
                        {{# unless isRerenderPlanCard }}
                            {{# if selectedPlan }}
                                <div class="mb-6 text-left" style="font-weight: bold; width: 100%;">Click to select desired weeks</div>
                                {{> planCard plan=selectedPlan parentData=parentData selectedPlans=selectedPlans }}
                            {{/ if }}
                        {{ else }}
                            {{ rerenderPlanCard }}
                        {{/ unless }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary font-weight-bolder mr-2 create-selective-week-reservation"
                            id="createSelectiveWeekReservationAndCreateAnother"
                            data-stayopen="true">Save & Create Another
                    </button>
                    <button type="button" class="btn btn-primary font-weight-bolder mr-2 create-selective-week-reservation"
                            id="createSelectiveWeekReservation"
                            data-stayopen="false">Save
                    </button>
                    <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal" {{ isDisabled }}>Close</button>
                </div>
            </div>
        </div>
    </div>
</template>

