<template name="_profileBoxFieldGroup">
<div class="row profile-box-field-group">
	<div class="col-12">
		{{#unless topLevelDiv}}
			<h4 class="profile-box-field-group-header mt-4">
				<a href="#{{collapseIdForGroup}}" class="{{#unless group.overrideCollapse}}accordion-toggle collapsed{{/unless}}" data-toggle="{{#unless group.overrideCollapse}}collapse{{/unless}}">
				  {{group.description}} {{#if displayIndex}}({{displayIndex}}){{/if}}
				</a>
			</h4>
		{{/unless}}
		<div class="{{#unless group.overrideCollapse}}collapse{{/unless}}" id="{{collapseIdForGroup group}}">
			{{#each field in group.fields}}
				{{#if trueIfEq field.type "fieldGroup"}}
					{{#if field.multiple}}
						{{#each idx in dataIndexesForPath (pathConcat field.name)}}
							{{> _profileBoxFieldGroup group=field path=(pathConcat field.name) index=idx person=person allowableProfileFields=person.allowableProfileFields}}
						{{/each}}
						<!--
						<div class="row"><div class="col-xs-12">
							<button data-group-id="{{collapseIdForGroup}}" class="btn btn-default">+ Add</button>
						</div></div>
						-->
					{{else}}
						{{> _profileBoxFieldGroup group=field path=(pathConcat field.name) person=person allowableProfileFields=person.allowableProfileFields}}
					{{/if}}
                {{else if showField field}}
                    <div data-cy="bus-routes-input-{{field.name}}" class="row field-group-data-row mb-6" data-validation="{{field.validation}}" data-id="{{field.name}}">
                            <input type="hidden" class="field-data-id" value="{{field.name}}">
                            <input type="hidden" class="field-data-type" value="{{field.type}}">
                            <input type="hidden" class="field-data-path" value="{{path}}">
                            <input type="hidden" class="field-data-index" value="{{index}}">

                            <label class="col-xl-3 col-lg-3 text-lg-right col-form-label">
                                {{field.description}}
                            </label>
            <div data-cy="bus-routes-input-{{field.name}}" class="col-lg-8 col-xl-6">
              {{#if trueIfEq field.type "attachments"}}
                <span class="fieldValue">
                  {{#each file in (getProfileFieldValue field.name)}}
                    <a href="#" onclick="window.open('{{file.mediaUrl}}', '_blank');" >View {{file.name}}</a> | <a href="#" class="remove-attachment" data-fieldid="{{field.name}}" data-token="{{file.mediaToken}}">remove</a><br/>
                  {{/each}}
                </span>
              {{else if trueIfEq field.type "file"}}
                <span class="fieldValue">
                  <a href="#" onclick="window.open('{{getProfileFieldValue field.name}}', '_blank');">View File</a> | <a href="#" class="remove-file" data-fieldid="{{field.name}}">remove</a><br/>
                </span>
              {{else if trueIfEq field.type "hoursV1"}}
                <span class="fieldValue">
                  {{#each hoursBlock in getHoursBlocks}}
                    {{hoursBlock.dayLabel}}: {{hoursBlock.hoursDescription}}<br/>
                  {{/each}}
                </span>
              {{/if}}
              {{#if trueIfEq field.type "text"}}
                    <textarea class="form-control form-control-solid" {{disabledIfNotEditable (isEditableField field) }}>{{getProfileFieldValue field.name}}</textarea>
              {{else if trueIfEq field.type "select"}}
                <select data-cy="select-bus-route-{{field.name}}" class="form-control form-control-solid" id="select-{{field.name}}"
                        {{addMultiple field}}
                        {{disabledIfNotEditable (isEditableField field)}}
                >

                    {{#unless (addMultiple field)}}
                        <option value=""></option> <!-- Add an empty option only for single-select -->
                    {{/unless}}

                    {{#each fieldValue in field.values}}
                        {{#if (addMultiple field)}}
                            <option value="{{fieldValue}}" {{selectedIfContains (getProfileFieldValue field.name) fieldValue}}>{{fieldValue}}</option>
                        {{else}}
                            <option value="{{fieldValue}}" {{selectedIfEqual (getProfileFieldValue field.name) fieldValue}}>{{fieldValue}}</option>
                        {{/if}}
                    {{/each}}
                </select>
              {{else if trueIfEq field.type "query"}}
                <select data-cy="select-bus-route-{{field.name}}" class="form-control form-control-solid" {{disabledIfNotEditable (isEditableField field) }}>
                  <option value=""></option>
                    {{#each fieldValue in (queryFieldValues field)}}
                      <option value="{{fieldValue.id}}" {{selectedIfEqual (getProfileFieldValue field.name) fieldValue.id}}>{{fieldValue.value}}</option>
                    {{/each}}
                </select>
              {{else if trueIfEq field.type "string"}}
                <input class="form-control form-control-solid {{#if trueIfEq field.name "phonePrimary"}}phone{{/if}}" type="text" value="{{getProfileFieldValue field.name}}" {{disabledIfNotEditable (isEditableField field) }}>
              {{else if trueIfEq field.type "buttons"}}
                <div class="btn-group">
                  {{#each fieldValue in field.fieldValues}}
                    <div class="btn btn-default {{activeIfEq (getProfileFieldValue field.name) fieldValue}}" data-value="{{fieldValue}}">{{fieldValue}}</div>
                  {{/each}}
                </div>
              {{else if trueIfEq field.type "date"}}
								<input data-cy="enrollment-date-input" class="form-control form-control-solid profile-date-input" type="text" value="{{formatDate (getProfileFieldValue field.name) 'MM/DD/YYYY'}}" {{disabledIfNotEditable (isEditableField field) }}>
              {{else if trueIfEq field.type "customerDefinedList"}}
                <textarea class="form-control form-control-solid" {{disabledIfNotEditable (isEditableField field) }}>{{getProfileFieldValue field.name}}</textarea>
                <span class="field-help" style="font-size:8px">One line per entry</span>
              {{else if trueIfEq field.type "attachments"}}
                <span class="file-maintenance">
                  <!-- {{#each file in (getProfileFieldValue field.name)}}
                    {{file.name}} <a href="#" class="remove-attachment" data-fieldid="{{field.name}}" data-token="{{file.mediaToken}}">remove</a><br/>
                  {{/each}} -->
									{{#if isEditableField field}}
	                  <div class="form-group">
	                    <label for="{{field.name}}InputFile" class="btn btn-primary font-weight-bolder">
	                      <input type="file" id="{{field.name}}InputFile" class="profileInputFile text-right" data-fieldid="{{field.name}}" data-fieldtype="attachment">
	                      <i class="fad fa-cloud-upload"></i> Choose File
	                    </label>
	                    <!-- <br/> -->
	                    <!-- <label>Attach New File</label>
	                    <input type="file" class="profileInputFile text-right" data-fieldid="{{field.name}}" data-fieldtype="attachment">           -->
	                  </div>
									{{/if}}
                </span>
              {{else if trueIfEq field.type "crmDocument"}}
                  <span class="file-maintenance">
                      <div class="form-group">
                      <div class="btn btn-primary font-weight-bolder crm-document" data-doc-id="{{field.crmId}}" data-fieldtype="crmDocument">
                      <i class="fad fa-cloud-download"></i> Download
                      </div>
                  </div>
                </span>
              {{else if trueIfEq field.type "file"}}
                <span class="file-maintenance">
                  <!-- {{#if (getProfileFieldValue field.name)}}
                    {{getProfileFieldValue field.name}} <a href="#" class="remove-file" data-fieldid="{{field.name}}">remove</a><br/>
                  {{/if}} -->
									{{#if isEditableField field}}
	                  <div class="form-group text-left">
	                    <label for="{{field.name}}InputFile" class="btn btn-primary font-weight-bolder">
	                      <input type="file" id="{{field.name}}InputFile" class="profileInputFile" data-fieldid="{{field.name}}" data-fieldtype="file">
	                      <i class="fad fa-cloud-upload"></i> Choose File
	                    </label>
	                  </div>
									{{/if}}
                </span>
              {{/if}}
            </div>
  					<div class="text-right">
  					<!-- {{#if isEditableField field}}
  					<a href="#" class="edit-field"><i class="fa fa-fw fa-pencil-square-o"></i></a>
  					{{/if}}
  					<button class="btn btn-default hidden btn-xs field-save">Save</button> -->
    					{{#if isDateField field}}
								{{#if isEditableField field}}
									<!--<div class="btn btn-icon btn-clean btn-lg remove-date-field" >
											<span class="svg-icon svg-icon-primary fad-regular fad fa-times-square fa-swap-opacity text-danger"></span>
									</div>-->
								{{/if}}
    					{{/if}}
  					</div>
  				</div>
				{{/if}}
			{{/each}}
		</div>
	</div>
</div>
</template>
