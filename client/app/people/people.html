<template name="people">
  {{#if countLoader}}
      {{> loading}}
  {{else}}
    {{#if showFilterRow}}
      <div class="container d-flex flex-row justify-content-end mb-4">
        <div class="mr-4">
          <select class="form-control" id="quickViewType" data-cy="quick-view-drop-down">
            <option value="">My Default View</option>
            <option value="activepeople">Active People</option>
            <option value="checkedinpeople">Checked In People</option>
            <option value="allpeople">All Users</option>
          </select>
        </div>
        <div class="mr-4">
          <select class="form-control" id="sortButton" data-cy="sort-dropdown">
            <option>Sort</option>
            <option value="lastName|asc" {{selectedIfEqual currentSort "lastName|asc"}}>Last Name (A-Z)</option>
            <option value="firstName|asc" {{selectedIfEqual currentSort "firstName|asc"}}>First Name (A-Z)</option>
            <option value="engagement|asc" {{selectedIfEqual currentSort "engagement|asc"}}>Engagement (Least)</option>
            <option value="engagement|desc" {{selectedIfEqual currentSort "engagement|desc"}}>Engagement (Most)</option>
            <option value="type|asc" {{selectedIfEqual currentSort "type|asc"}}>Type</option>
          </select>
        </div>
        {{#if showPinCodeCheckin}}
          <div class="btn btn-primary font-weight-bolder btn-text-white mr-4" id="btnPinCheckin" data-cy="pin-check">
            <i class="fad-regular fad fa-clock fa-swap-opacity mr-2" style="color:#fff"></i>PIN Check In
          </div>
        {{/if}}
        <div class="btn btn-primary font-weight-bolder btn-text-white {{#if canAddPerson}}mr-4{{/if}}" id="filterPeopleLink" data-cy="people-directory-filter" data-toggle="collapse" data-target="#filterPeopleCollapse" aria-expanded="false" aria-controls="filterPeopleCollapse">
          <i class="fad-regular fad fa-filter fa-swap-opacity mr-2" style="color:#fff"></i>Filter {{#if activeFiltersCount}}({{activeFiltersCount}}){{/if}}
        </div>
        {{#if canAddPerson}}
          <div data-cy="add-person-btn" class="btn btn-primary font-weight-bolder btn-text-white {{#if canAddRegistration}}mr-4{{/if}}" id="newPersonLink" >
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add
          </div>
        {{/if}}
        {{#if canAddRegistration}}
        <div class="btn btn-primary font-weight-bolder btn-text-white" id="newRegistrationLink" data-cy="new-registration-link">
          <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>New Registration
        </div>
        {{/if}}
      </div>
    {{/if}}
    <div class="container d-flex flex-row justify-content-end mb-4">
        {{#if canAddChild}}
        <div class="btn btn-primary font-weight-bolder btn-text-white mr-2" id="newChildLink" data-cy="new-child-btn">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>New Child
        </div>
        {{/if}}
        {{#if showFamilyRelationships}}
        <div class="btn btn-primary font-weight-bolder btn-text-white" id="newRelationshipLink" data-cy="new-relationship-btn">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>New Relationship
        </div>
        {{/if}}
    </div>
    <div class="container {{#unless requireFilters}}collapse{{/unless}} mb-4" id="filterPeopleCollapse">
      <div class="card card-custom gutter-b">
        <div class="card-body">
          <div class="d-flex flex-row justify-content-between">
            <div class="d-flex flex-column">
              <span class="text-muted font-weight-bolder mb-2">Group:</span>
              <div class="checkbox-list">
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" class="group-filter group-filter-all" value="" {{checkedIfFilterActive "group" ""}}>
                  <span></span>
                  All
                </label>
                {{#each groups}}
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" class="group-filter group-filter-value" value="{{_id}}" {{checkedIfFilterActive "group" _id}}>
                    <span></span>
                    {{name}}
                  </label>
                {{/each}}
              </div>
            </div>
            <div class="d-flex flex-column">
              <span class="text-muted font-weight-bolder mb-2">Type:</span>
              <div class="checkbox-list">
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" value="" class="type-filter type-filter-all" data-cy="input-all-checkbox" {{checkedIfFilterActive "type" ""}}>
                  <span></span>
                  All
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" value="person" class="type-filter type-filter-value" data-cy="input-people-checkbox" {{checkedIfFilterActive "type" "person"}}>
                  <span></span>
                  {{ getEntityTypePeople }}
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" value="family" class="type-filter type-filter-value" data-cy="input-family-checkbox" {{checkedIfFilterActive "type" "family"}}>
                  <span></span>
                  {{ getEntityTypeFamily }}
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" value="staff" class="type-filter type-filter-value" data-cy="input-staff-checkbox" {{checkedIfFilterActive "type" "staff"}}>
                  <span></span>
                  {{ getEntityTypeStaff }}
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" value="admin" class="type-filter type-filter-value" data-cy="input-admin-checkbox" {{checkedIfFilterActive "type" "admin"}}>
                  <span></span>
                  {{ getEntityTypeAdmin }}
                </label>
                {{#if hasCustomization "inquiries/enabled"}}
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" value="prospect" class="type-filter type-filter-value" data-cy="input-prospect-checkbox" {{checkedIfFilterActive "type" "prospect"}}>
                    <span></span>
                    {{ getEntityTypeProspect }}
                  </label>
                {{/if}}
                {{#if hasZkTeco}}
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" value="vendor" class="type-filter type-filter-value" {{checkedIfFilterActive "type" "vendor"}}>
                  <span></span>
                  {{ getEntityTypeVendor }}
                </label>
                {{/if}}
              </div>
            </div>
            <div class="d-flex flex-column">
              <div class="checkbox-list">
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" id="filterInactive" {{checkedIfFilterActive "inactive" ""}}>
                  <span></span>
                  Show inactive
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" id="filterContacts" {{checkedIfFilterActive "contacts" ""}}>
                  <span></span>
                  Hide Family Contacts and Pickups
                </label>
                {{#if hasCustomization "reservations/enabled"}}
                  <label class="checkbox checkbox-primary">
                    <input type="checkbox" id="filterReservations" {{checkedIfFilterActive "reservations" ""}}>
                    <span></span>
                    Today's Reservations
                  </label>
                {{/if}}
              </div>
              <button class="btn btn-primary mt-4 font-weight-bolder" id="btnResetFilters">Reset All Filters</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container d-flex flex-row justify-content-end mb-4" >
      <div class="d-flex flex-row ml-6 justify-content-center align-items-center">
        <span class="page-item-set" style="cursor:pointer;" data-action="subtract" data-cy="pagination-previous-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
        <span class="font-size-h2" data-cy="pagination-records-count">{{pageStart}}-{{pageEnd}} of {{pageTotal}}</span>
        <span class="page-item-set" style="cursor:pointer;" data-action="add" data-cy="pagination-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
      </div>
    </div>
    <div class="d-flex flex-column-fluid">
      <div class="container">
        {{#if requireFilters}}
          <div style="text-align: center">
            <h2 data-cy="status-validation">Start by selecting a group or type filter</h2>
            You can also use the search bar to find a specific person.  Get started above!
          </div>
        {{else}}
          {{#each person in people}}
            {{> _peopleListRow person=person source="people" greyedOut=false}}
          {{/each}}
          {{#each person in onHoldPeople}}
            {{> _peopleListRow person=person source="people" greyedOut=true}}
          {{/each}}
        {{/if}}
      </div>
    </div>
  {{/if}}
</template>
