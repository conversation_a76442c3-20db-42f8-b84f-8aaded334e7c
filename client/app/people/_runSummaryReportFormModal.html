<template name="_runSummaryReportFormModal">
  <div id="_runSummaryReportFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Create Summary Report</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmCreateSummaryReport">
            {{> dateRangePicker dateFields}}
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <div class="btn btn-primary font-weight-bolder mr-2" id="btnCreateSummaryReport">Create Report</div>
          <div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</div>
        </div>
  		</div>
  	</div>
  </div>
</template>
