import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './dataValidation.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { PeopleDataValidations } from '../../../lib/collections/peopleDataValidation';

Template.dataValidation.helpers({
	"recordIsActive": function(record) {
		return record.active;
	},
	"firstValidation": function() {
		return PeopleDataValidations.findOne({});
	},
	"remainingValidations": function() {
		var remaining = PeopleDataValidations.find({}, {skip: 1}).fetch();
		var whatsleft = remaining.reduce(function(result, value, index, array) {
		  if (index % 2 === 0)
		    result.push(array.slice(index, index + 2));
		  return result;
		}, []);
		
		return whatsleft;
	}
});

Template.dataValidation.events({
	'click #addNewDataValidation': function() {
		FlowRouter.go("/people/data-validation");
	},
	'click #dataValidationDetails': function(e) {
		e.preventDefault();
		const _id = e.target.getAttribute('fieldid');
		FlowRouter.go(`/people/data-validation/${_id}`);
	},
})


Template.dataValidationItem.helpers({
	"formatBgColor": function(record) {
		return (record.active) ? "" : "background-color:#0000000D";
	},
	"formatStatusIcon": function(record) {
		return (record.active) ? "fa-sync" : "fa-check-circle";
	},
	"formatStatusLabel": function(record) {
		let ongoing = (record.ongoing) ? " - (Ongoing)" : "";
		if (record.active) {
			return `Running${ongoing}`;
		} else {
			return "Complete";
		}
	},
	"formatResponseTime": function(record) {
		if (record.lastResponseTime) {
			return `Last Response: ${new moment(record.lastResponseTime).format("dddd, MMMM Do")}`;
		}
		return "Last Response:";
	},
	"formatAudienceLabel": function(record) {
		if (record.audience == "staff") {
			return "Staff and Admins Responded";
		}
		return "Families Responded";
	},
	"formatResponseNumber": function(record) {
		const total = record.peopleIds && record.peopleIds.length;
		const responded = record.responses && record.responses.length;
		
		return `${responded}/${total}`;
	},
})

Template.dataValidationItem.events({
	'click #dv-view-details-active': function(e) {
		e.preventDefault();
		console.log(e.target);
		const _id = e.target.getAttribute('fieldid');
		FlowRouter.go(`/people/data-validation/${_id}`);
	},
	'click #dv-view-details-inactive': function(e) {
		e.preventDefault();
		const _id = e.target.getAttribute('fieldid');
		FlowRouter.go(`/people/data-validation/${_id}`);
	},
	'click #dv-send-reminder': function(e) {
		e.preventDefault();
		const _id = e.target.getAttribute('fieldid');
		mpSwal.fire({
			title: "Send Reminder",
			text: "An email will be sent to the remaining users who have not responded",
			showCancelButton: true,
			confirmButtonText:"Confim"
		}).then ( result => {
			if (result.value) {
				Meteor.callAsync("sendPeopleDataValidationReminder", _id).then( response => {
					mpSwal.fire({icon:"success"});
				}).catch( error => {
					mpSwal.fire({icon:"error", title:"Error", text: error.reason});
				});
			}
		});
	},
	'click #dv-clone': function(e) {
		e.preventDefault();
		const _id = e.target.getAttribute('fieldid');
		mpSwal.fire({
			title: "Clone Data Verification",
			text: "A copy of your selected data verificaiton will be available to the existing audience",
			showCancelButton: true,
			confirmButtonText:"Confim"
		}).then ( result => {
			if (result.value) {
				Meteor.callAsync("clonePeopleDataValidation", _id).then((result) => {
					FlowRouter.redirect("/people/data-validation/" + result);
				}).catch((error) => {
					mpSwal.fire({icon:"error", title:"Error", text: error.reason});
				});
			}
		});
	},
	'click #dv-stop': function(e) {
		e.preventDefault();
		const _id = e.target.getAttribute('fieldid');
		mpSwal.fire({
			title: "Stop running data verification",
			text: "Users will no longer see this verification request on their Now timeline",
			showCancelButton: true,
			confirmButtonText:"Confim"
		}).then ( result => {
			if (result.value) {
				Meteor.callAsync("stopPeopleDataValidation", _id).then((response) => {
					mpSwal.fire({icon:"success"});
				}	).catch((error) => {
					mpSwal.fire({icon:"error", title:"Error", text: error.reason});
				});
			}
		});
	},
	'click #dv-delete': function(e) {
		e.preventDefault();
		const _id = e.target.getAttribute('fieldid');
		mpSwal.fire({
			title: "Delete data verification",
			text: "This will permanently delete the verification - are you sure?",
			showCancelButton: true,
			confirmButtonText:"Confim"
		}).then ( result => {
			if (result.value) {
				Meteor.callAsync("deletePeopleDataValidation", _id).then((response) => {
					mpSwal.fire({icon:"success"});
				}).catch((error) => {
					mpSwal.fire({icon:"error", title:"Error", text: error.reason});
				});
			}
		});
	},
})
