<template name="_uploadNewAvatarFormModal">
  <div id="_uploadNewAvatarFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">New Avatar Image</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmUploadAvatar">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
              <div class="col-lg-9 col-xl-6">
                <label id="avatarInputLabel" for="avatarInputFile" class="btn btn-primary font-weight-bolder">
                  <input data-cy="choose-file" type="file" id="avatarInputFile" style="display:none;"> 
                  <i class="fad fa-cloud-upload"></i> Choose File
                </label>
                <span id="fileLabelSpan"></span>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <div data-cy="upload-file" class="btn btn-primary font-weight-bolder mr-2" id="btnAvatarSave">Upload</div>
          <div data-cy="cancel-upload" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</div>
        </div>
  		</div>
  	</div>
  </div>
</template>
