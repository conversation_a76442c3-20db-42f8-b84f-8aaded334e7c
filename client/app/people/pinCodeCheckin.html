<template name="pinCodeCheckin">
<div class="d-flex flex-row flex-grow-1 justify-content-center">
	<div class="col-lg-9 col-xl-6">		
		<div data-cy="pin-code-modal" class="card card-custom gutter-b box-pin-code-checkin">
			<div class="card-header">
        		<div class="card-title d-flex justify-content-center ">
					<h3 style="font-weight:bold">PIN Code Check In/Out</h3>
       			</div>
			</div>
			<div class="card-body">
				{{#if trueIfEq checkinState "phonePrompt"}}
				<div class="row">
					<div class="col-12 text-center">
						<div class="offset-md-3 col-md-6">
							<div>
								<h5 class="text-primary">{{orgName}}</h5>
							</div>
							<form data-cy="phone-pin-form" id="phonePinForm">
								<h2>Enter Phone Number:</h2>
								<div class="input-group input-group-lg">
									<input type="tel" class="form-control pin-code-box" maxlength="19" size="19" id="phoneNumber" style="font: large Verdana,sans-serif" data-cy="phone-number">
									<span class="input-group-btn ml-4 mt-1">
										<button type="button" class="btn btn-secondary btn-flat btn-lg font-weight-bolder" id="phoneSubmit" data-cy="phone-submit">Continue</button>
									</span>
								</div>
							</form>

						</div>
					</div>
				</div>
				{{#if hasCustomization "people/qrCodeCheckin/enabled"}}
					{{#if qrCodesExpireForThisOrgInMinutes }}
						{{> qrCodeBlockAutoRefresh }}
					{{else}}
						{{> qrCodeBlock}}
					{{/if}}
				{{/if}}

				{{/if}}

				{{#if trueIfEq checkinState "codePrompt"}}
					<div class="row">
						<div class="col-12 text-center">
							<div class="offset-md-2 col-md-8">
								<div>
									<h5 class="text-primary">{{orgName}}</h5>
								</div>
								{{#if isKioskModeAndOffline}}
									<h2 style="font-weight:bold;">This device is offline. Please check your child in at the front desk.</h2>
								{{else}}
								<form id="pinCodeForm">
									<h2>{{ enterPinText }}:</h2>
									<div class="input-group d-flex flex-row align-items-center">
										<input type="tel" class="form-control pin-code-box" maxlength="19" size="19" id="pinCode" style="font: large Verdana,sans-serif; letter-spacing: 1px;-webkit-text-security: disc;border-radius: 6px;" pattern="[0-9]*" data-cy="pincode-inp">
										<span class="input-group-btn ml-4 mt-1">
											{{#if isPhoneMode}}
											<button type="button" class="btn btn-secondary btn-flat btn-lg font-weight-bolder" id="pinCancel">
												Back
											</button>
											{{/if}}
											<button type="button" class="btn btn-secondary btn-flat btn-lg font-weight-bolder" id="pinSubmit" data-cy="pin-submit">
												{{#if isPhoneMode}}
												Continue
												{{else}}
												Submit
												{{/if}}
											</button>
										</span>
									</div>
								</form>
								{{/if}}
							</div>
						</div>
					</div>
					{{#if hasCustomization "people/qrCodeCheckin/enabled"}}
						{{#if qrCodesExpireForThisOrgInMinutes }}
							{{> qrCodeBlockAutoRefresh }}
						{{else}}
							{{> qrCodeBlock}}
						{{/if}}
					{{/if}}
				{{/if}}

				{{#if trueIfEq checkinState "personPrompt"}}
  				<div class="row">
  					<div class="col-md-12">
  						<form id="peopleIncludeForm">
  							{{#if announcements.count}}
  							<h4>Announcements:</h4>
  							<ul class="pinCodeAnnouncements">
  								{{#each announcements}}
  									<li><h4>{{headline}}</h4>{{message}}</li>
  								{{/each}}
  							</ul>
  							<hr />
  							{{/if}}
  							{{#if companionSelection}}
  							<h4>Your name:</h4>
  							<select class="form-control" id="companion-select">
  								<option value="">Choose One</option>
  								{{#each companionSelection}}
  									<option value={{_id}}>{{name}}</option>
  								{{/each}}
  							</select>
  							<hr/>
  							{{/if}}
  							<h4>People to Include:</h4>
  							<hr/>
  							{{#each includePeople}}
  								<div class="pretty p-icon p-smooth" style="display:block;margin-bottom:10px">
  									<input type="checkbox" class="includePersonCheckbox" data-id="{{_id}}" checked data-cy="include-person-chkbox"> 
  									<div class="state p-success" style="font-size:18px">
  										<i class="icon fa fa-check"></i>
  										<label>{{name}} 
  											<span>{{#if checkedIn}}
  											(Check Out)
  											{{else}}
  											(Check In)
  											{{/if}}</span>
  										</label>
  										
  									</div>
  								</div>
  								
  								{{#if messageMoments}}
  								<div class="reminder-block">
  									<b>Messages from today:</b><br/>
  									{{#each messageMoments}}
  									{{time}}: {{comment}}<br/>
  									{{/each}}
  								</div>
  								{{/if}}

  								{{#unless checkedIn}}
  										{{#if pinCodeCheckinFields}}	
	  										<div id="momentForm" data-id="{{_id}}">
	  										{{> dynamicMomentForm dynamicFieldValues=familyDataToDynamicFields}}
	  										</div>
  										{{/if}}
  								{{/unless}}
  								


  								{{#if checkedIn}}
  									{{#if checkInReminders}}
  									<div class="reminder-block">
  										<b>Reminders from check-in:</b><br/>
  										{{#each checkInReminders}}
  										{{label}}: {{value}}<br/>
  										{{/each}}
  									</div>
  									{{/if}}


  								{{/if}}
								{{#if showReasons}}
									<div data-cy="early-late-dropdown" class="form-group row align-items-center" autocomplete="off">
									{{#if checkedIn}}
									<label class="col-xl-3 col-lg-3 text-right col-form-label">Early Check Out Reason</label>
									{{else}}
									<label class="col-xl-3 col-lg-3 text-right col-form-label">Late Check In Reason</label>
									{{/if}}
										<div class="col-lg-6 col-md-9 col-sm-12">
											<select data-cy="reasons" name="reasons" id="reasons-{{_id}}" autocomplete="off" required>
												<option value=""></option>
												{{#each reasons}}
												<option value="{{this._id}}">{{this.reason}}</option>
												{{/each}}
											</select>
										</div>
									</div>
							  	{{/if}}
  								<hr/>
  							{{/each}}
  							
  							<button type="button" class="btn btn-secondary btn-lg font-weight-bolder"  style="border-color:var(--secondary)" id="peopleSubmit" data-cy="people-submit">Submit</button>
  							<button type="button" class="btn btn-secondary btn-lg font-weight-bolder" id="peopleCancel">Cancel</button>
  						</form>
  					</div>
  				</div>
				{{/if}}
			</div>
		</div>
	</div>
</div>
</template>

<template name="qrCodeBlock">
	<div class="row mt-4">
		<div class="d-flex flex-column col-12 text-center">
			<h2>Or use your</h2>
			<h2 style="color: #1E84FF;"><b><u>IN-APP</u> CAMERA</b></h2>
			<h4>(not your regular phone camera)</h4>
			<h2>to scan the QR Code</h2>
			<div id="qrCodeDiv" class="d-flex justify-content-center mt-8"></div>
		</div>
	</div>
</template>

<template name="qrCodeBlockAutoRefresh">
	<div class="row mt-4">
		<div class="col-12 text-center">
			<h4>Or use your</h4>
			<h4 style="color: #1E84FF;"><b><u>IN-APP</u> CAMERA</b></h4>
			<h4>(not your regular phone camera)</h4>
			<h4>to scan the QR Code</h4>
			<div data-cy="qr-code" id="qrCodeDiv" class="d-flex justify-content-center mt-8"></div>
		</div>
	</div>
</template>
