import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './_assessmentAdjustment.html';
import { getPeopleById } from "../../services/peopleMeteorService";
import { Orgs } from '../../../lib/collections/orgs';

Template._assessmentAdjustment.onCreated(function () {
	this.currentPerson = new ReactiveVar({});
	getPeopleById(this.data?.selectedStandard?.override?.createdByPersonId).then((person) => {
		this.currentPerson.set(person);
	})

})

Template._assessmentAdjustment.helpers({
	currentMeasurement() {
		const standard = Template.instance().data.selectedStandard, allLevels = Orgs.current().getAvailableAssessmentLevels();
		return standard.maxAssessment && allLevels[standard.maxAssessment].label;
	},
	availableLevels() {
		return Orgs.current().getAvailableAssessmentLevels();
	},
	existingOverride() {
		const standard = Template.instance().data.selectedStandard;
		if (!standard.override) return;
		const assessmentPerson = Template.instance().currentPerson.get();
		const overrideData = {...standard.override};
		overrideData.createdBy = assessmentPerson.firstName + " " + assessmentPerson.lastName;
		return overrideData;
	}
});