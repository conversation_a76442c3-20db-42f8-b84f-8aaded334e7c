import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './people.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import { template } from 'underscore';
import { AvailableCustomizations } from "../../../lib/customizations";
import { RegistrationDesignationUtils } from "../../../lib/registrationDesignationUtils";
import {AvailableActionTypes, AvailablePermissions} from "../../../lib/constants/permissionsConstants";
import { Log } from "../../../lib/util/log";
import { People, Person } from '../../../lib/collections/people';
import _ from '../../../lib/util/underscore';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { processPermissions } from '../../../lib/permissions';
import './_peopleListRow';
import { Groups } from '../../../lib/collections/groups';
import '../../layout/loading';
import { showModal } from '../main';
import './_addPersonModal';
import { offlinePreventCheck } from '../../../mpweb';
import { Relationships } from '../../../lib/collections/relationships';
import { Reservations } from '../../../lib/collections/reservations';
import './_registrationLinks';

const limit = 50;

Template.people.onCreated(function() {
	var subsCache = new SubsCache(-1, -1);
	let sortFieldName = "lastName", sortDirectionName = "asc";
	//pagination variables
	this.skip = new ReactiveVar(0);
	this.totalPeople = new ReactiveVar(0);
	this.query = new ReactiveVar();
	this.sort = new ReactiveVar(1);
	//client chache variables
	this.currentPagePeople = new ReactiveVar([]);
	this.previousPagePeople = new ReactiveVar([]);
	this.nextPagePeople = new ReactiveVar([]);
	this.countLoader = new ReactiveVar(true);
	this.hasChildren = new ReactiveVar(false);

	const filterOptions = _.deep(Meteor.user(), "uiOptions.peopleListFilters") || {};

	const sessionSort = filterOptions.peopleListSort;
	if (sessionSort && sessionSort !== "" && sessionSort.indexOf("|") >= 0) {
		sortFieldName = sessionSort.split("|")[0];
		sortDirectionName = sessionSort.split("|")[1];
	}
	this.sortField = new ReactiveVar(sortFieldName);
	this.sortDirection = new ReactiveVar(sortDirectionName);

	this.filterGroup = new ReactiveVar(filterOptions.peopleListFilterGroup || []);
	this.filterType = new ReactiveVar(filterOptions.peopleListFilterType || []);
	this.filterInactive = new ReactiveVar(filterOptions.peopleListFilterInactive || false);
	this.filterReservations = new ReactiveVar(filterOptions.peopleListFilterReservations || false);
	this.filterCheckedIn = new ReactiveVar(filterOptions.peopleListFilterCheckedIn || false);
	this.filterContacts = new ReactiveVar(filterOptions.peopleListFilterContacts || false);

	this.listStyle = new ReactiveVar(filterOptions.peopleListStyle || "list");
	this.designations = RegistrationDesignationUtils.getConfigurableDesignations(Orgs.current());

	this.onHoldPeople = new ReactiveVar([]);

	this.autorun(() => {

		const currentUser = Meteor.user(), currentPerson = currentUser && currentUser.fetchPerson();
		if (!currentPerson) return;

		let query = {};

		if (_.contains(["staff","admin"], currentPerson.type)) {
			const conditions = [];
			const filterGroup = this.filterGroup.get();
			if (filterGroup.length > 0) conditions.push({$or: [{defaultGroupId: {$in: filterGroup}}, {checkInGroupId: {$in: filterGroup}}]});
			const filterType = this.filterType.get();
			if (filterType.length > 0)
				conditions.push({type: {$in: filterType}});
			else
				conditions.push({type: {"$ne": "prospect"}});
			const filterInactive = this.filterInactive.get();
			if (!filterInactive)
				conditions.push({inActive: {$ne: true}});
			const filterCheckedIn = this.filterCheckedIn.get();
			if (filterCheckedIn)
				conditions.push({checkedIn: true});
			if (this.filterReservations.get()) {
				const startDate = new moment().startOf("day");
				const endDate = new moment(startDate).add(1, 'day');

				const reservationIds = Reservations.find({
						scheduledDate: {
							$gte: startDate.valueOf(),
							$lt: endDate.valueOf()
						}
					},
					{fields: {selectedPerson: 1}}).map(function (r) {
					return r.selectedPerson;
				});

				conditions.push({_id: {$in: reservationIds}});
			}
			if (this.filterContacts.get()) {
				const familyIds = Relationships.find({relationshipType: "family"}, {fields: {"personId": 1}}).map((r) => r.personId);
				conditions.push({
					$or: [
						{type: "family", _id: {$in: familyIds}},
						{type: {"$ne": "family"}}
					]
				});
			}
			query = (conditions.length > 0) ? {$and: conditions} : {};
		} else {
			query = {inActive: {$ne: true}, type: {"$in": ["person", "family"]}};
			if (currentPerson.type == "family") {
        let includeList = [currentPerson._id];
        includeList = includeList.concat(Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).map(
          function (m) { return m.targetId; }));
        query["_id"] = { $in: includeList };
      }
		}

		const sortField = this.sortField.get(), sortDirection = this.sortDirection.get();
		let sort;
		if (sortField === "firstName")
			sort = {firstName: sortDirection === "asc" ? 1 : -1, lastName: 1};
		else if (sortField === "engagement")
			sort = {};
		else if (sortField === "type")
			sort = {type: sortDirection === "asc" ? 1 : -1, lastName: 1};
		else
			sort = {lastName: sortDirection === "asc" ? 1 : -1, firstName: 1};

		Session.set('peopleListQuery', JSON.stringify(query));
		Session.set('peopleListSort', JSON.stringify(sort));

		Meteor.callAsync('thePeopleCount', query).then((res) => {
			this.countLoader.set(false)
			this.totalPeople.set(res);
		}).catch((err) => {
			this.countLoader.set(false)
			console.log(err);
		});
		this.subscriptionHandle = subsCache.subscribe("thePeopleList", { query: query, skip: this.skip.get(), limit: limit, sort: sort });
	});

	this.autorun(() => {
		const queryString = Session.get('peopleListQuery');
		const sortString = Session.get('peopleListSort');

		if (!queryString || !sortString) return;

		try {
			const people = People.find({
				...JSON.parse(queryString),
			}, {
				skip: this.skip.get(),
				limit,
				sort: JSON.parse(sortString)
			}).fetch()
				.map(person => new Person({
					...person,
					clickable: true
				}));

			if (!Orgs.current().hasCustomization("people/showFamilyRelationships")) {
				this.currentPagePeople.set(people);
				return;
			}

			Meteor.callAsync('getPeopleFromRelationships', people.map(person => person._id))
				.then((res) => {
					const relatedPeople = res.map(person => new Person({
						...person,
						clickable: false
					}));

					const primaryIds = new Set(people.map(person => person._id));
					const uniqueRelatedPeople = relatedPeople.filter(person => !primaryIds.has(person._id));

					this.currentPagePeople.set([...people, ...uniqueRelatedPeople]);
				})
				.catch(error => {
					Log.error('Error getting people from relationships:', error);
				});
		} catch (err) {
			Log.error('Error parsing session data:', err);
			this.currentPagePeople.set([]);
		}
	});

	const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
	// get on hold people
	if(currentPerson && currentPerson.type === "family") {
		Meteor.callAsync('fetchPeopleOnHold', currentPerson).then((res) => {
			this.onHoldPeople.set(res);
		}).catch((err) => {
			console.log(err);
		});
	}

});

async function updateFilterOptionsState(instance) {
	Template.instance().skip.set(0);
	const newState = {
		peopleListFilterGroup: instance.filterGroup.get(),
		peopleListFilterType: instance.filterType.get(),
		peopleListFilterInactive: instance.filterInactive.get(),
		peopleListFilterReservations: instance.filterReservations.get(),
		peopleListFilterCheckedIn: instance.filterCheckedIn.get(),
		peopleListFilterContacts: instance.filterContacts.get(),
		peopleListStyle: instance.listStyle.get()
	}
	await Meteor.callAsync("setUiOption", "peopleListFilters", newState);
} 

async function resetFilters(instance) {
	instance.filterGroup.set([]);
	instance.filterType.set([]);
	instance.filterInactive.set(false);
	instance.filterReservations.set(false);
	instance.filterCheckedIn.set(false);
	instance.filterContacts.set(false);
	Template.instance().totalPeople.set(0);
	await updateFilterOptionsState(instance);
}

Template.people.rendered= function() {
	const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
	if (!currentPerson) return;
	const defaultGroupId = currentPerson.defaultGroupId;
	if (defaultGroupId && currentPerson.type !== "admin") {
		Template.instance().filterGroup.set([defaultGroupId]);
	}
	$('.sparkbar').each(function () {
		const $this = $(this);
		$this.sparkline('html', {
			type: 'bar',
			height: $this.data('height') ? $this.data('height') : '30',
			barColor: $this.data('color')
		});
	});
	$('[data-toggle="tooltip"]').tooltip();

};


Template.people.helpers({
	countLoader() {
		return Template.instance().countLoader.get()
	},
	showFamilyRelationships() {
		const instance = Template.instance();
		const currentUser = Meteor.user();
		const currentPerson = currentUser && currentUser.fetchPerson();

		Meteor.callAsync('checkPrimaryCaregiverRelationshipExists', currentPerson._id).then((result) => {
			instance.hasChildren.set(result);
		}).catch((error) => {
			Log.error('Error fetching children:', error);
		});

		return Orgs.current().hasCustomization("people/showFamilyRelationships")
			&& currentPerson?.type === "family"
			&& instance.hasChildren.get();
	},
	requireFilters() {
		const currentUser = Meteor.user(), currentPerson = currentUser && currentUser.fetchPerson();
		if (!currentPerson || !_.contains(['staff','admin'], currentPerson.type)) return false;
		const filterGroup = Template.instance().filterGroup.get(),
			filterType = Template.instance().filterType.get();
		return (filterGroup.length === 0 && filterType.length === 0);
	},
	'people': function() {
		const sortField = Template.instance().sortField.get();
		const sortDirection = Template.instance().sortDirection.get();
		const people = Template.instance().currentPagePeople.get();

		let providerMax = 0, familyMax = 0;
		_.each(people, function (person, index) {
			if (person.type === "person" && !person.inActive) {
				const pData = person.engagementData();
				if (pData.providerCount > providerMax) providerMax = pData.providerCount;
				if (pData.familyCount > familyMax) familyMax = pData.familyCount;
				if (pData.providerCount > 0 || pData.familyCount > 0) {
					person.hasEngagement = true;
					person.engagementProviderCount = pData.providerCount;
					person.engagementFamilyCount = pData.familyCount;

				}
			}
			person.rank = index + 1;
		});
		_.each(people, function (person) {
			if (person.hasEngagement) {
				person.engagementProviderMax = providerMax;
				person.engagementFamilyMax = familyMax;
				person.engagementTotalCount = (providerMax > 0 ? person.engagementProviderCount / providerMax : 0) +
					(familyMax > 0 ? person.engagementFamilyCount / familyMax : 0);
			}
		});
		if (sortField === "engagement")
			return _.sortBy(people, function (p) {
				return (p.engagementTotalCount || 0) * (sortDirection === "asc" ? 1 : -1);
			});
		else
			return people;

	},
	"groups": function() {
         return Groups.find({}, {sort: {name: 1}});
    },
	'canAddPerson': function() {
		return processPermissions({
			assertions: [{ context: AvailablePermissions.PEOPLE_ADD_MODIFY_USERS, action: AvailableActionTypes.EDIT }],
			evaluator: (person) => person.type === "admin"
		}) || processPermissions({
			assertions: [{ context: AvailablePermissions.PEOPLE_ADD_VENDOR, action: AvailableActionTypes.EDIT}],
			evaluator: (person) => person.superAdmin === true
		});
	},
	'canAddRegistration': function() {
	return 	processPermissions({
		assertions: [{ context: "people/addModifyUsers", action: "edit" }],
		evaluator: (person) => person.type === "admin"
	}) && Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
	},
	'canAddChild': function() {
		const meteorUser = Meteor.user();
		const currentPerson = meteorUser && meteorUser.fetchPerson();
		const isParent = currentPerson && currentPerson.type === 'family';
		const hasRegistrationFlow = Orgs.current().hasCustomization(AvailableCustomizations.REGISTRATION_FLOW);
		return isParent && hasRegistrationFlow;
	},
	'sortIcon': function(fieldName) {
		const sortField = Template.instance().sortField.get();
		const sortDirection = Template.instance().sortDirection.get();
		if (sortField === fieldName && sortDirection === "asc")
			return "fa-sort-alpha-asc";
		else if (sortField === fieldName && sortDirection === "desc")
			return "fa-sort-alpha-desc";
		else
			return "fa-sort";
	},
	'currentSort': function() {
		return Session.get("peopleListSort");
	},
	'checkedIfFilterActive': function(filterType, filterValue) {
		switch (filterType) {
			case "group":
				return (filterValue === "" && Template.instance().filterGroup.get().length === 0) ||
				_.contains(Template.instance().filterGroup.get(), filterValue) ? "checked" : "";
			case "type":
				return (filterValue === "" && Template.instance().filterType.get().length === 0) ||
				_.contains(Template.instance().filterType.get(), filterValue) ? "checked" : "";
			case "inactive":
				return Template.instance().filterInactive.get() ? "checked" : "";
			case "reservations":
				return  Template.instance().filterReservations.get() ? "checked" : "";
			case "contacts":
				return  Template.instance().filterContacts.get() ? "checked" : "";
		}
	},
	'activeFiltersCount': function() {
		let filterCount = 0;
		if (Template.instance().filterGroup.get().length > 0) filterCount += 1;
		if (Template.instance().filterType.get().length > 0) filterCount += 1;	
		if (Template.instance().filterInactive.get()) filterCount += 1;
		if (Template.instance().filterReservations.get()) filterCount += 1;
		if (Template.instance().filterContacts.get()) filterCount += 1;
		if (filterCount > 0) return filterCount;
	},
	'showPinCodeCheckin': function() {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson(), org = Orgs.current();
    const hasPinEnabled = (org && org.hasCustomization("people/pinCodeCheckin/enabled"));
    const kioskPinAdminOnly = (org && org.hasCustomization("people/kioskMasterPinAdminOnly/enabled"));
    const predicade = (currentPerson && hasPinEnabled &&
			(currentPerson.type === "admin" || currentPerson.type === "staff")
		);
    if (!kioskPinAdminOnly) return predicade;
    return hasPinEnabled && currentPerson.type === "admin";
	},
	peopleListStyle: function() {
		return Template.instance().listStyle.get();
	},
	hasZkTeco: function() {
		return Orgs.current() && Orgs.current().hasCustomization(AvailableCustomizations.DOOR_LOCKS_ZKTECO);
	},
	showFilterRow() {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
		return (currentPerson && 
			(currentPerson.type == "admin" || currentPerson.type == "staff") 
		);
	
	},
	onHoldPeople: function() {
		const onHoldRegistrationData = Template.instance().onHoldPeople.get();
		return onHoldRegistrationData?.data?.children.filter((child) => !child._id).map((child) => {
			return {
				firstName: child.firstName,
				lastName: child.lastName,
				type: 'person',
				personInitials: function () {
					if (this.firstName && this.lastName)
						return this.firstName.charAt(0) + this.lastName.charAt(0);
					else if (this.firstName)
						return this.firstName.substring(0, 1);
					else if (this.lastName)
						return this.lastName.substring(0, 1);
					else
						return "";
				}
			};
		});
	},
	pageStart: function () {
		return ( Template.instance().totalPeople.get() > 0 ? Template.instance().skip.get() + 1 : 0) ;
	},
	pageEnd: function () {
		return ( Template.instance().totalPeople.get() > (Template.instance().skip.get() + limit) ? Template.instance().skip.get() + limit : Template.instance().totalPeople.get() );
	},
	pageTotal: function () {
		return Template.instance().totalPeople.get();
	},
	'getEntityTypePeople': function () {
		return orgLanguageTranformationUtil.getEntityType('people');
	},
    'getEntityTypeFamily': function () {
		return orgLanguageTranformationUtil.getEntityType('family');
	},
    'getEntityTypeProspect': function () {
		return orgLanguageTranformationUtil.getEntityType('prospect');
	},
    'getEntityTypeStaff': function () {
		return orgLanguageTranformationUtil.getEntityType('staff');
	},
    'getEntityTypeAdmin': function () {
		return orgLanguageTranformationUtil.getEntityType('admin');
	},
    'getEntityTypeVendor': function () {
		return orgLanguageTranformationUtil.getEntityType('vendor');
	}
});

Template.people.events({
	'click #newRelationshipLink'(event, template) {
		event.preventDefault();
		FlowRouter.go('addRelationship');
	},
	'click #btnPinCheckin': function(event) {
		event.preventDefault();
		FlowRouter.go("pin-code-checkin");
	},
	'click .list-type-buttons button': async function(event, template) {
		const btnClicked = event.currentTarget;
		if (btnClicked.id === "btnListTypeGrid") {
			template.listStyle.set("grid");
			await updateFilterOptionsState(template);
		} else {
			template.listStyle.set("list");
			await updateFilterOptionsState(template);
		}
	},
	'click #filterInfoShowFilters': function() {
		$("#filterCollapse").show();
		$("#filterInfo").hide();
	},
	'click #filterLink': function() {
		$("#filterInfo").hide();
	},
	'click #newPersonLink': function(event, template) {
		if (offlinePreventCheck()) return false;
		Session.set("currentEditPersonId", "");
		Session.set("currentEditPersonPending", true);
		showModal("_addPersonModal", {}, "#_addPersonModal");
		// $("#editPersonFormModal").modal();
	},
	'click #newRegistrationLink': function(event, template) {
		event.preventDefault();
		if (template.designations?.length) {
			showModal('_registrationLinks', { }, "#_registrationLinks");
			return;
		}
		const currentOrg = Orgs.current();
		const orgId = currentOrg._id;
		const currentPerson = Meteor.user().fetchPerson();
		const queryParams = { orgId: orgId, person: currentPerson._id };
		const queryString = Object.keys(queryParams)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
			.join('&');
		let baseUrl = (currentOrg && currentOrg.whiteLabel && currentOrg.whiteLabel.ROOT_URL) || Meteor.absoluteUrl().replace(/\/+$/, "");
		const url = `${baseUrl}/registration?${queryString}`;
		window.open(url, '_blank');
	},
	'click #newChildLink': function(event, template) {
		event.preventDefault();
		const currentOrg = Orgs.current();
		const orgId = currentOrg._id;
		const currentPerson = Meteor.user().fetchPerson();
		const queryParams = { orgId: orgId, personId: currentPerson._id };
		const personDesignation = RegistrationDesignationUtils.getPersonDesignation(currentPerson);
		if (personDesignation) {
			queryParams.designation = personDesignation;
		}
		const queryString = Object.keys(queryParams)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
			.join('&');
		let baseUrl = (currentOrg && currentOrg.whiteLabel && currentOrg.whiteLabel.ROOT_URL) || Meteor.absoluteUrl().replace(/\/+$/, "");
		const url = `${baseUrl}/registration?${queryString}`;
		window.open(url, '_blank');
	},
	"click .group-filter": async function(event, template) {
		let selectedValues = [];
		if (!$(event.target).val()) 
			$(".group-filter-value:checked").prop('checked', false);
		else {
			$(".group-filter-all:checked").prop('checked', false);
 			selectedValues = _.map($(".group-filter:checked"), function (e) {return e.value;});
		}
		template.filterGroup.set(selectedValues);
		await updateFilterOptionsState(template);
	},
	"click .type-filter": async function(event, template) {
		let selectedValues = [];
		if (!$(event.target).val()) 
			$(".type-filter-value:checked").prop('checked', false);
		else {
			$(".type-filter-all:checked").prop('checked', false);
 			selectedValues = _.map($(".type-filter:checked"), function (e) {return e.value;});
		}
		template.filterType.set(selectedValues);
		await updateFilterOptionsState(template);
	},
	"change #filterGroup": function(event, template) {
		const filterVal = $("#filterGroup").val();
		template.filterGroup.set(filterVal);
	},
	"change #filterType": function(event, template) {
		const filterVal = $("#filterType").val();
		template.filterType.set(filterVal);
	},
    "change #filterInactive": async function(event, template) {
		const filterVal = $("#filterInactive").prop('checked');
		template.filterInactive.set(filterVal);
		await updateFilterOptionsState(template);
	},
	"change #filterContacts": async function(event, template) {
		const filterVal = $("#filterContacts").prop('checked');
		template.filterContacts.set(filterVal);
		await updateFilterOptionsState(template);
	},
    "change #filterReservations": async function(event, template) {
		const filterVal = $("#filterReservations").prop('checked');
		template.filterReservations.set(filterVal);
		await updateFilterOptionsState(template);
	},
    "change #sortButton": async function(event, template) {
		const sortButtonSelection = $("#sortButton").val().split("|");
		if (sortButtonSelection.length > 0) {
			template.sortField.set(sortButtonSelection[0]);
			template.sortDirection.set(sortButtonSelection[1]);
			await updateFilterOptionsState(template);
		}
	},
	"click #btnResetFilters": async function(event, template) {
		await resetFilters(template);
	},
    "click .applySort": function(event, template) {
		const fieldName = $(event.target).data("id");
		if (fieldName !== template.sortField.get()) {
			template.sortField.set(fieldName);
			template.sortDirection.set("asc");
		} else {
			template.sortDirection.set(template.sortDirection.get() === "asc" ? "desc" : "asc");
		}
	},
    'change #quickViewType': function(event, template) {
		const newType = $("#quickViewType").val();
		Template.instance().filterType.set([]);
		Template.instance().filterInactive.set(false);
		Template.instance().filterContacts.set(false);
		Template.instance().filterReservations.set(false);
		Template.instance().filterCheckedIn.set(false);
		Template.instance().filterGroup.set([]);

		Template.instance().totalPeople.set(0);
		Template.instance().skip.set(0);

		switch (newType) {
			case "activepeople":
				Template.instance().filterType.set(["person"]);
				break;
    		case "checkedinpeople":
    			Template.instance().filterType.set(["person"]);
    			Template.instance().filterCheckedIn.set(true);
    			break;
    		case "allpeople":
    			break;
    		default:
    			var defaultGroupId = Meteor.user().fetchPerson().defaultGroupId;
				if (defaultGroupId) {		
					Template.instance().filterGroup.set([defaultGroupId]);
				}
    	}
    },
	"click .page-item-set": function (event, template) {
		const action = $(event.currentTarget).attr("data-action");
		if ( action == "add" ) {
			if ( Template.instance().totalPeople.get() > ( Template.instance().skip.get() + limit ) ){
				Template.instance().skip.set(Template.instance().skip.get() + limit);
				// nextPagePeople();
			}
		} else if ( action == "subtract" ) {
			if ( Template.instance().skip.get() > 0 ){
				Template.instance().skip.set(Template.instance().skip.get() - limit);
				// previousPagePeople();
			}
		}
	}
});


Template.people.onDestroyed(function() {
	if (this.subscriptionHandle) {
		this.subscriptionHandle.stop();
	}
})