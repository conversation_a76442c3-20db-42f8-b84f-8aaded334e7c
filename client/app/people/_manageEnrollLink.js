import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_manageEnrollLink.html';
import Swal from 'sweetalert2/dist/sweetalert2';
import { People } from "../../../lib/collections/people";
import { hideModal } from '../main';

const loadingPlaceholder = {
    id: '...',
    firstName: '...',
    lastName: '...',
    email: '...',
    phone: '...',
    birthday: '...',
    docCount: '...',
}
Template._manageEnrollLink.created = function() {
    this.isChild = new ReactiveVar(false);
    this.isFamily = new ReactiveVar(false);
    const person = People.findOne(this.data.personId);
    this.oldLinkType = new ReactiveVar('');
    this.oldEntityId = new ReactiveVar(0);
    if (person.type === 'person') {
        this.oldLinkType.set('child');
        this.oldEntityId.set(person.childcareCrm?.childId || null);
    } else if (person.type === 'family' && person.childcareCrm?.guardianId) {
        this.oldLinkType.set('guardian');
        this.oldEntityId.set(person.childcareCrm.guardianId);
    } else if (person.type === 'family' && person.childcareCrm?.contactId) {
        this.oldLinkType.set('contact');
        this.oldEntityId.set(person.childcareCrm.contactId);
    }
    this.newLinkType = new ReactiveVar(this.oldLinkType.get());
    this.isChild.set(person.type === 'person');
    this.isFamily.set(person.type === 'family');
    this.manageRecord = new ReactiveVar({});
    this.loadingRecord = new ReactiveVar(loadingPlaceholder);
    this.managePerson = new ReactiveVar(person);
    Meteor.callAsync('formatManageRecord', this.data.personId).then(response => {
        this.manageRecord.set(response);
    })
    this.enrollRecord = new ReactiveVar({});
    Meteor.callAsync('getEnrollData', this.data.personId).then(response => {
        this.enrollRecord.set(response);
        this.loadingRecord.set(null);
    });

    console.log({ data: this.data});
}

Template._manageEnrollLink.helpers({
    getUrl: function() {
    },
    cantSave: function() {
        return Boolean(Template.instance().loadingRecord.get()) || !Boolean(Template.instance().enrollRecord.get()?.familyId);
    },
    isChild: function () {
        return Template.instance().isChild.get();
    },
    isFamily: function () {
        return Template.instance().isFamily.get();
    },
    manageRecord: function () {
        return Template.instance().manageRecord.get();
    },
    enrollRecord: function () {
        return Template.instance().loadingRecord.get() || Template.instance().enrollRecord.get();
    },
    showDelete: function() {
        return Boolean(Template.instance().oldEntityId.get());
    },
    differentRecord: function () {
        const manage = Template.instance().managePerson.get();
        const enroll = Template.instance().enrollRecord.get();
        if (!enroll.familyId) {
            return false;
        }
        return !Template.instance().loadingRecord.get() && (manage.childcareCrm?.familyId !==  enroll.familyId ||
            Template.instance().oldLinkType.get() !== Template.instance().newLinkType.get() ||
            Template.instance().oldEntityId.get() !== enroll.id)
    }
});

Template._manageEnrollLink.events({
    'click #btnSubmitMel': function(event, i) {
        event.preventDefault();
        i.loadingRecord.set(loadingPlaceholder);
        Meteor.callAsync('setNewEnrollLink', i.data.personId, i.newLinkType.get(), i.enrollRecord.get()).then(response => {
            i.loadingRecord.set(null);
            hideModal('#_manageEnrollLink');
        }).catch(error => {
            i.loadingRecord.set(null);
            mpSwal.fire('Error', error.reason, 'error');
        });
    },
    'click #melDelete': function(event, i) {
        event.preventDefault();
        const isFamily = i.isFamily.get();
        const msg = 'The integration might re-link these records if they’re both still active and remain unlinked. Are you sure you want to delete the link?';
        mpSwal.fire({
            html: msg,
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
        }).then(result => {
            if (result.isConfirmed) {
                i.loadingRecord.set(loadingPlaceholder);
                Meteor.callAsync('setNewEnrollLink', i.data.personId, i.oldLinkType.get(), null).then(response => {
                    i.loadingRecord.set(null);
                    hideModal('#_manageEnrollLink');
                }).catch(error => {
                    i.loadingRecord.set(null);
                    mpSwal.fire('Error', error.reason, 'error');
                });
            }
        });
    },
    'click #melSearch': function(event, i) {
        event.preventDefault();
        const isFamily = i.isFamily.get();
        const title = 'What Enroll ID number do you want to link to?';
        let html = '<div class="mt-3"></div>';
        if (isFamily) {
            html += '<div class = "radio-list" >' +
                '<label class = "radio radio-primary">' +
                '<input type="radio" name="linkType" value="guardian" checked><span></span>Primary or 2nd Guardian</label>' +
                '<label class="radio radio-primary">' +
                '<input type="radio" name="linkType" value="contact" /><span></span>Additional Contact' +
                '</label></div>';
        } else {
            html += '<input type="hidden" name="linkType" value="child">'
        }
        html += '<div class="mt-4">' +
            '<input type="text" class="form-control swal2-input" id="melSearchInput" placeholder="Enroll ID">' +
        '</div>';
        mpSwal.fire({
            title: title,
            showCloseButton: true,
            width: '40rem',
            confirmButtonText: '<i class="fa fa-link"></i> Link',
            html: html,
            didOpen: () => {
                const swal = Swal.getPopup();
                const idInput = swal.querySelector('#melSearchInput');
                idInput.onkeyup = (event) => {
                    if (event.key === 'Enter') {
                        Swal.clickConfirm();
                    }
                }
                idInput.oninput = () => {
                    idInput.value = idInput.value.replace(/[^0-9]/g, '');
                }
            },
            preConfirm: () => {
                return new Promise(function (resolve) {
                    if (!document.getElementById('melSearchInput').value) {
                        resolve(false);
                    }
                    const linkType = document.querySelector('input[name="linkType"]:checked')?.value;
                    resolve({ enrollId: document.getElementById('melSearchInput').value, linkType });
                });
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const linkType = isFamily ? result.value.linkType : 'child';
                i.loadingRecord.set(loadingPlaceholder);
                Meteor.callAsync('getNewEnrollData', i.data.personId, linkType, result.value.enrollId).then(response => {
                    if (response) {
                        i.enrollRecord.set(response);
                        i.newLinkType.set(linkType);
                    } else {
                        const msg = 'The ID number you entered does not match any ' +
                         `${linkType} records in Enroll for this location. Please try again.`;
                        mpSwal.fire('Error', msg, 'error');
                    }
                    i.loadingRecord.set(null);
                });
            }
        });
    }
});