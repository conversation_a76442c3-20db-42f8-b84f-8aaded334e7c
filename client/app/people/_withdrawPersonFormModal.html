<template name="_withdrawPersonFormModal">
  <div id="_withdrawPersonFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Notate Withdraw Date</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmWithdrawPerson">
            <div class="form-group row">
              <label class="col-xl-6 col-lg-6 text-right col-form-label">Withdraw Date: Documentation purposes only - you must deactivate the person to suspend their account</label>
              <div class="col-lg-6 col-xl-6">
                <input type="text" class="form-control form-control-lg form-control-solid" id="withdrawPersonDate" value="{{withdrawPersonDateVal}}">
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <div class="btn btn-primary font-weight-bolder mr-2" id="btnSubmitWithdraw">Save</div>
          <div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</div>
        </div>
  		</div>
  	</div>
  </div>
</template>
