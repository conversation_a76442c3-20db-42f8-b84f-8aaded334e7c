import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_programsTab.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { BillingUtils} from "../../../lib/util/billingUtils";
import { AvailableCustomizations } from '../../../lib/customizations';
import { RegistrationDesignationUtils } from "../../../lib/registrationDesignationUtils";
import { Orgs } from '../../../lib/collections/orgs';
import './_punchCards';
import './_enrolledPrograms';
import './programs/_addProgramsWrapper';

Template._programsTab.onCreated(function() {
    this.rerenderRegistration = new ReactiveVar(false);
    this.onHoldPrograms = new ReactiveVar([])
    this.designation = RegistrationDesignationUtils.getPersonDesignation(this.data);
    this.hasParentDesignation = new ReactiveVar(false);
    const currentPerson = Meteor.user()?.fetchPerson();
    this.hasParentDesignation.set(currentPerson?.type === 'family' && RegistrationDesignationUtils.getPersonDesignation(currentPerson));

    Meteor.callAsync("getOnHoldRegistrationsWithChild", Orgs.current()._id, FlowRouter.current().params._id).then(registrations => {
        this.onHoldPrograms.set(registrations)
    });
});
Template._programsTab.helpers({
    personData: () => {
        return Template.instance().data;
    },
    rerenderRegistration: () => {
        return Template.instance().rerenderRegistration;
    },
    hasDesignation: () => {
        return !!Template.instance().designation || !!Template.instance().hasParentDesignation.get();
    },
    resetPrograms: () => {
        const instance = Template.instance();
        setTimeout(() => {
            instance.rerenderRegistration.set(false);
        }, 500);
    },
    showPrograms: () => {
        return !Template.instance().rerenderRegistration.get();
    },
    hasPunchCards: () => {
        let org = Orgs.current();
        if (org && org.hasCustomization(AvailableCustomizations.PUNCH_CARDS)) {
            const currentChild = Template.instance().data;
            return currentChild.totalPunchCardDays?.length > 0;
        } else {
            return false;
        }
    },
    isEnrolledInAllPlans: () => {
        const currentChild = Template.instance().data;
        const enrolledPlans = currentChild.billing?.enrolledPlans?.map(plan => plan._id) ?? [];
        const programs = Orgs.current().availablePrograms();
        const availablePrograms = programs.map(program => program._id);
        let onHoldPrograms = []

        if (Template.instance().onHoldPrograms.get()) {
            for (const registration of Template.instance().onHoldPrograms.get()) {
                const childIndex = registration.data.children.findIndex(c => c._id === currentChild._id);
                onHoldPrograms = registration.data.plans[childIndex]
            }
        }

        return getAllPlans().filter(plan => {
            return (availablePrograms.includes(plan.program)) &&
                !enrolledPlans.includes(plan._id) &&
                !onHoldPrograms.map(p => p._id).includes(plan._id);
        }).length === 0
    },
});
Template._programsTab.events({
});
const getAllPlans = () => {
    const org = Orgs.current();
    const prefix = org?.profileDataPrefix();
    const plans = org?.availableBillingPlans(true, false, true, true, true, Template.instance().designation);
    const currentChildGrade = (prefix ? Template.instance().data[prefix]?.studentGrade : Template.instance().data.studentGrade) ?? null;
    const filteredPlansWithDetails = BillingUtils.filteredPlansWithDetails(plans, org)
    return BillingUtils.filteredPlansWithGrades(filteredPlansWithDetails, currentChildGrade)
}