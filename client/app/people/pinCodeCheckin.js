import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './pinCodeCheckin.html';
import moment from "moment-timezone";
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AsYouType } from "libphonenumber-js";
import { AvailableCustomizations } from "../../../lib/customizations";
import { Orgs } from '../../../lib/collections/orgs';
import { Session } from 'meteor/session';
import _ from '../../../lib/util/underscore';
import '../moments/momentForms/dynamicMomentForm';
import { Announcements } from '../../../lib/collections/announcements';
import { QRCode } from '../bundles/_Aqrcode';
import { MomentUtils } from '../../../lib/util/momentUtils';
import { LateCheckInEarlyDropOffModes } from '../../../lib/constants/momentTypeConstants';

Template.pinCodeCheckin.created = function () {
	this.checkinState = new ReactiveVar("codePrompt");
	this.phoneMode = new ReactiveVar(false);
	const hasQr = FlowRouter.getQueryParam('qr') && window?.MpData?.qrdata;
	this.currentOrg = Orgs.current();
	if (!hasQr && this.currentOrg?.hasCustomization('people/phonePinCheckin/enabled')) {
		this.checkinState.set('phonePrompt');
		this.phoneMode.set(true);
	}
	this.pinHold = new ReactiveVar(null);
	this.includePeople = new ReactiveVar([]);
	this.companionSelection = new ReactiveVar([]);
	this.kioskPinCode = new ReactiveVar(null);
	this.pinTries = new ReactiveVar(0);
	this.phonePeople = new ReactiveVar(null);
	const instance = this;

	instance.isOffline = new ReactiveVar(!Meteor.status().connected);

	// Autorun to reactively update isOffline based on Meteor's connection status
	instance.autorun(() => {
		instance.isOffline.set(!Meteor.status().connected);
	});

	instance.autorun(() => {
		if (Orgs.current()) {
			this.kioskPinCode.set(Orgs.current().kioskPinCode);
		}
	});
	this.qrCodesExpireForThisOrgInMinutes = new ReactiveVar(null);
	if (this.currentOrg && this.currentOrg.hasCustomization('people/checkInCheckOutQrCodesExpire/enabled')) {
		this.qrCodesExpireForThisOrgInMinutes.set(
			this.currentOrg.getCheckInCheckOutQrCodesExpireAfterMinutesValue() ?? 24 * 60
		);
	}
};
Template.pinCodeCheckin.rendered = function () {
	$("#pinCode").trigger('focus');
	Session.set("activeMomentType", "pinCodeCheckinForm");
	const pinCode = window?.MpData?.pin;
	if (pinCode) {
		setTimeout(function () {
			$("#pinCode").val(pinCode);
			$("#pinSubmit").trigger("click");
			window.MpData.pin = null;
		}, 300);
	}
}

Template.pinCodeCheckin.helpers({
	'qrCodesExpireForThisOrgInMinutes': function () {
		return Template.instance().qrCodesExpireForThisOrgInMinutes.get();
	},
	"checkinState": function () {
		return Template.instance().checkinState.get();
	},
	"includePeople": function () {
		return Template.instance().includePeople.get();
	},
	"isPhoneMode": function () {
		return Template.instance().phoneMode.get();
	},
	"enterPinText": function () {
		const i = Template.instance();
		if (i.phoneMode.get() && i.phonePeople.get()?.length === 1) {
			return 'Hi ' + i.phonePeople.get()[0].firstName + '! Please enter your PIN code below'
		}
		return 'Enter PIN';
	},
	"orgName": function () {
		return Template.instance().currentOrg.name;
	},
	"announcements": function () {
		const today = new moment();
		const startDate = today.startOf("day").valueOf(); const endDate = today.add(1, "days").startOf("day").valueOf();
		const query = { "scheduledDate": { "$gte": startDate, "$lt": endDate }, "selectedGroups": [] };
		return Announcements.find(query);
	},
	"pinCodeCheckinFields": function () {
		const checkIns = _.filter(Template.instance().includePeople.get(), (p) => {
			return !p.checkedIn;
		});
		if (checkIns.length > 0) {
			return Template.instance().currentOrg.pinCodeCheckinFields();
		}
	},
	"companionSelection": function () {
		return Template.instance().companionSelection.get();
	},
	"familyDataToDynamicFields": function () {
		const obj = {};
		if (this.familyCheckin) {
			this.familyCheckin.forEach((item, i) => {
				obj[item.label] = item.value;
			});
		}
		return obj;
	},
	"reasons": function() {
		return Orgs.current().pickDropReasons;
	},
	"isKioskModeAndOffline": function() {
		const instance = Template.instance();
		const org = Orgs.current();
		// Kiosk mode is determined by organization customization
		const determinedIsKiosk = org && org.hasCustomization("people/kioskMasterPinAdminOnly/enabled");
		const offline = instance.isOffline.get();
		return determinedIsKiosk && offline;
	}
});

Template.pinCodeCheckin.events({
	"submit #pinCodeForm": function (e) {
		e.preventDefault();
	},
	"submit #phonePinForm": function (e) {
		e.preventDefault();
	},
	"keyup #pinCode": function (e) {
		if (event.keyCode === 13 && $("#pinCode").val().length > 0) {
			$("#pinSubmit").click();
		}
	},
	'click #pinCancel': function (e, i) {
		e.preventDefault();
		resetPhone(i);
	},
	'keypress #phoneNumber': function (e, i) {
		if (e.target.value.length >= 14) {
			return false;
		}
	},
	'input #phoneNumber': function (e, i) {
		if (e.target.value.length > 5) {
			// avoid deletion issues
			e.target.value = new AsYouType('US').input(e.target.value);
		}
	},
	'click #phoneSubmit': function (e, i) {
		e.preventDefault();
		const phone = document.getElementById('phoneNumber').value;
		const kioskPinCode = i.kioskPinCode.get() || "";
		if (phone !== "" && phone === kioskPinCode) {
			if (Session.get("kioskLocation") || "" !== "") {
				if (window.ReactNativeWebView) {
					window.ReactNativeWebView.postMessage("close");
				} else {
					Session.set("kioskLocation", null);
					FlowRouter.go("/");
				}
			} else {
				Session.set("kioskLocation", "pinCodeCheckin");
				if (window.ReactNativeWebView) {
					window.ReactNativeWebView.postMessage('kioskPinEntered');
				}
				FlowRouter.go("/");
			}
			return;
		}
		$("#phoneSubmit").prop('disabled', true).text("Submitting");
		Meteor.callAsync('phoneSearch', phone).then((result) => {
			$("#phoneSubmit").prop('disabled', false).text("Continue");
			if (!result || result.length === 0) {
				mpSwal.fire({ icon: "error", text: 'Sorry, that is not a valid phone number' })
					.then(errResult => {
						$("#phoneNumber").val("").trigger('focus');
					})
				return;
			}
			i.phonePeople.set(result);
			i.checkinState.set('codePrompt');
		}).catch((error) => {
			$("#phoneSubmit").prop('disabled', false).text("Continue");
			mpSwal.fire({ icon: "error", text: 'Sorry, that is not a valid phone number' })
				.then(errResult => {
					$("#phoneNumber").val("").trigger('focus');
				});
				return;
		});
	},
	"click #pinSubmit": function (e, template) {
		e.preventDefault();
		const pinCode = $("#pinCode").val();
		const kioskPinCode = template.kioskPinCode.get() || "";

		if (kioskPinCode !== "" && pinCode === kioskPinCode) {
			if (Session.get("kioskLocation") || "" !== "") {
				if (window.ReactNativeWebView) {
					window.ReactNativeWebView.postMessage("close");
				} else {
					Session.set("kioskLocation", null);
					FlowRouter.go("/");
				}
			} else {
				let kioskloc = "pinCodeCheckin"+location.search
				Session.set("kioskLocation", kioskloc);
				if (window.ReactNativeWebView) {
					window.ReactNativeWebView.postMessage('kioskPinEntered');
				}
				FlowRouter.go("/");
			}
			return;
		}
		$("#pinSubmit").prop('disabled', true).text("Submitting");
		const isKioskMode = FlowRouter.current().path.startsWith('/kiosk') || !!Session.get("kioskLocation");

		const peopleConstraint = template.phoneMode.get() && template.phonePeople.get();
		const checkinHandler = function (opts) {
			const qrdata = window?.MpData?.qrdata;
			const wasPinCodeCheckin = true;
			const mobileForceClose = window?.MpData?.mobileForceClose;
			const requestTime = moment.tz(Orgs.current().getTimezone()).valueOf();
			Meteor.callAsync("pinCodeCheckin", _.extend({
				pinCode: pinCode,
				qrdata,
				wasPinCodeCheckin,
				requestTime,
				isKioskMode
			}, opts)).then((response) => {
				const resetSubmit = () => {
					$("#pinSubmit").prop('disabled', false).text("Submit");
				};
				if (response.checkinCompleted) {
					const qr = FlowRouter.getQueryParam('qr');
					FlowRouter.setQueryParams({ qr: null });
					mpSwal.fire({
						"icon": "success",
						html: "Check In/Out Successful: <br/>" + response.checkinList.join("<br/>") + (response.checkinTimestamp ? "<br/>At " + response.checkinTimestamp : ""),
						timer: 12000
					})
						.then(res => {
							$("#pinCode").val("").trigger('focus');
						})
					resetPhone(template);

					if ((qr || mobileForceClose) && window.ReactNativeWebView) {
						window.ReactNativeWebView.postMessage("close");
					} else {
						$("#pinCode").val("").trigger('focus');
						resetSubmit();
					}
				} else if (response.needsStaffAffirmation) {
					mpSwal.fire({
						title: "Affirmation",
						text: "I certify these hours are a true and accurate record of my time worked on this day.",
						icon: "warning",
						showCancelButton: true
					}).then(result => {
						if (result.value)
							checkinHandler({ affirmed: true });
						else {
							$("#pinCode").val("").trigger('focus');
							resetSubmit();
						}
					});
				} else {
					resetSubmit();
					const org = Orgs.current(); // Get org again in case schedule times have changed or customizations have changed
					if (org.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
						const people = response.promptToInclude;
						const promises = people.map(person => {
							const personId = person._id;
							const timezone = org.getTimezone();
							const selectedTime = moment.tz(timezone).valueOf();
							const mode = person.checkedIn ? LateCheckInEarlyDropOffModes.EARLY_PICK_UP : LateCheckInEarlyDropOffModes.LATE_DROP_OFF;
							return Meteor.callAsync("getReservationsForToday", personId, org._id, timezone)
								.then(reservations => {
									person.showReasons = MomentUtils.isEarlyOrLateCheckInCheckOut(personId, org, timezone, selectedTime, mode, reservations);
								});
						});

						Promise.all(promises).then(() => {
							response.promptToInclude = people;
							template.pinHold.set(pinCode);
							template.checkinState.set("personPrompt");
							template.includePeople.set(response.promptToInclude);
							template.companionSelection.set(response.companionSelection);
						});
					} else {
						template.pinHold.set(pinCode);
						template.checkinState.set("personPrompt");
						template.includePeople.set(response.promptToInclude);
						template.companionSelection.set(response.companionSelection);
					}
				}
			}).catch((error) => {
				const resetSubmit = () => {
					$("#pinSubmit").prop('disabled', false).text("Submit");
				};
				mpSwal.fire({ icon: "error", text: error.reason })
					.then(errResult => {
						template.pinTries.set(template.pinTries.get() + 1);
						if (template.pinTries.get() >= 3) {
							resetPhone(template);
						}
						$("#pinCode").val("").trigger('focus');
						if (window.ReactNativeWebView) {
							const person = Meteor.user()?.fetchPerson?.();
							if(person.type === "family"){
								window.ReactNativeWebView.postMessage("close");
							}
						}
					});
				resetSubmit();
			})
		};
		checkinHandler({ peopleConstraint });

	},
	"click #peopleSubmit": function (e, template) {
		e.preventDefault();
		const companionSelection = template.companionSelection.get();
		let selectedCompanionId;
		if (companionSelection && companionSelection.length > 0) {
			const selectedCompanionValue = $("#companion-select").val();
			if (!selectedCompanionValue || selectedCompanionValue === "") {
				mpSwal.fire({ icon: "error", text: "You must select your name." });
				return;
			}
			selectedCompanionId = selectedCompanionValue;
		}
		const peopleToCheckin = _.map($(".includePersonCheckbox:checked"), function (cb) {
			return $(cb).data("id");
		});
		
		let peopleObjToCheckin = template.includePeople.get().filter(function(item){
			return peopleToCheckin.indexOf(item._id) > -1;
		});
		
		if (peopleToCheckin.length === 0) {
			mpSwal.fire({ icon: "error", text: "Please select at least one person." });
			return;
		}
		$("#peopleSubmit").prop('disabled', true).text("Submitting");

		let peoplePinCodeFormFields = {};
		let requiredError = false;
		let requiredMessage = "The following fields are required: ";
		let earlyLateReasons = {};
		let org = Orgs.current();
		if (org && org.pinCodeCheckinFields()) {
			for (const peopleObj of peopleObjToCheckin) {
				const currentPerson = peopleObj;
				let pinCodeFormFields = {};
				_.each(Orgs.current().pinCodeCheckinFields(), function (ff) {
					switch (ff.fieldType) {
						case "text":
						case "string":
						case "customerDefinedList":
						case "select":
							pinCodeFormFields[ff.dataId] = $("#momentForm[data-id='" + peopleObj._id + "'] #momentField" + ff.dataId).val();
							break;
						case "buttons":
							pinCodeFormFields[ff.dataId] = $("#momentForm[data-id='" + peopleObj._id + "'] #momentField" + ff.dataId + " button.active").data("value");
							break;
						case "timePicker":
							const timeMoment = new moment($("#momentForm[data-id='" + peopleObj._id + "'] #momentField" + ff.dataId).val(), "HH:mm");
							if (timeMoment.isValid()) pinCodeFormFields[ff.dataId] = timeMoment.format("h:mm a");
							break;
					}

					const testStr = pinCodeFormFields[ff.dataId] ? pinCodeFormFields[ff.dataId] : "";
					if (ff.required && currentPerson && currentPerson.checkedIn !== true && testStr.trim().length === 0) {
						requiredError = true;
						requiredMessage += `${ff.label} | `;
					}

					if (!ff.hideComment) pinCodeFormFields["comment"] = $("#momentForm[data-id='" + peopleObj._id + "'] #comment").val();
				});
				const selectedReason = $(`#reasons-${peopleObj._id}`).val();
				try {
					if (selectedReason && org.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
						earlyLateReasons[peopleObj._id] = selectedReason;
					} else if ((!selectedReason || selectedReason === '') && org.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED) && peopleObj.showReasons) {
						const errorMessage = "Please select a reason for early/late checkout";
						mpSwal.fire("Error", errorMessage, "error");
						$("#peopleSubmit").prop('disabled', false).text("Submit");
						return;
					}
				} catch (e) {
					mpSwal.fire("Error", e.message, "error");
					$("#peopleSubmit").prop('disabled', false).text("Submit");
					return;
				}
				peoplePinCodeFormFields[peopleObj._id] = pinCodeFormFields;
			};
		}

		if (requiredError) {
			mpSwal.fire("Required Fields Missing", requiredMessage, "error");
			$("#peopleSubmit").prop('disabled', false).text("Submit");
			return;
		}
		const qrdata = window?.MpData?.qrdata;
		const mobileForceClose = window?.MpData?.mobileForceClose;
		const wasPinCodeCheckin = true
		const requestTime = moment.tz(Orgs.current().getTimezone()).valueOf();
		const args = {
			pinCode: template.pinHold.get(),
			qrdata,
			companionId: selectedCompanionId,
			includePeople: peopleToCheckin,
			pinCodeFormFields: peoplePinCodeFormFields,
			wasPinCodeCheckin,
			requestTime,
			earlyLateReason: earlyLateReasons,
			isKioskMode: FlowRouter.current().path.startsWith('/kiosk') || !!Session.get("kioskLocation")
		}

		if (Orgs.current().hasCustomization(AvailableCustomizations.FEATURE_CHECKIN_ASYNC)) {
			Meteor.apply("pinCodeCheckin", [args],{
				onResultReceived: function (error, response) {
					$("#peopleSubmit").prop('disabled', false).text("Submit");
					if (error) {
						mpSwal.fire({ icon: "error", text: error.reason }).then(errResult => {
							if (window.ReactNativeWebView) {
								const person = Meteor.user()?.fetchPerson?.();
								if(person.type === "family"){
									window.ReactNativeWebView.postMessage("close");
								}
							}
							template.pinHold.set(null);
							template.checkinState.set("codePrompt");
							template.includePeople.set([]);
						});
					} else {
						const qr = FlowRouter.getQueryParam('qr');
						FlowRouter.setQueryParams({ qr: null });
						template.pinHold.set(null);
						template.checkinState.set("codePrompt");
						template.includePeople.set([]);
						mpSwal.fire({
							icon: "success",
							html: "Check In/Out Successful: <br/>" + response.checkinList.join("<br/>") + (response.checkinTimestamp ? "<br/>At " + response.checkinTimestamp : ""),
							timer: 12000
						}).then(result => {
							if ((qr || mobileForceClose) && window.ReactNativeWebView) {
								window.ReactNativeWebView.postMessage("close");
							}
						});
						resetPhone(template);
					}
				}
			})

		}
		else{
			Meteor.callAsync("pinCodeCheckin", args).then((response) => {
				$("#peopleSubmit").prop('disabled', false).text("Submit");
				const qr = FlowRouter.getQueryParam('qr');
				FlowRouter.setQueryParams({ qr: null });
				template.pinHold.set(null);
				template.checkinState.set("codePrompt");
				template.includePeople.set([]);
				mpSwal.fire({
					icon: "success",
					html: "Check In/Out Successful: <br/>" + response.checkinList.join("<br/>") + (response.checkinTimestamp ? "<br/>At " + response.checkinTimestamp : ""),
					timer: 12000
				}).then(result => {
					if ((qr || mobileForceClose) && window.ReactNativeWebView) {
						window.ReactNativeWebView.postMessage("close");
					}
				});
				resetPhone(template);
			}).catch((error) => {
				$("#peopleSubmit").prop('disabled', false).text("Submit");
				mpSwal.fire({ icon: "error", text: error.reason }).then(errResult => {
					if (window.ReactNativeWebView) {
						const person = Meteor.user()?.fetchPerson?.();
						if(person.type === "family"){
							window.ReactNativeWebView.postMessage("close");
						}
					}
					template.pinHold.set(null);
					template.checkinState.set("codePrompt");
					template.includePeople.set([]);
				});
			})
		}

	},
	"click #peopleCancel": function (e, template) {
		e.preventDefault();
		const mobileForceClose = window?.MpData?.mobileForceClose;

		template.pinHold.set(null);
		template.checkinState.set("codePrompt");
		template.includePeople.set([]);
		const qr = FlowRouter.getQueryParam('qr');
		if ((qr || mobileForceClose) && window.ReactNativeWebView) {
			window.ReactNativeWebView.postMessage("close");
		}
	}
});

function resetPhone(i) {
	if (i.phoneMode.get()) {
		i.checkinState.set('phonePrompt');
		i.phonePeople.set(null);
		i.pinTries.set(0);
	}
}
function resetPhoneWithoutPhoneNumber(i) {
	if (i.phoneMode.get()) {
		i.phonePeople.set(null);
		i.pinTries.set(0);
	}
}

Template.qrCodeBlock.onRendered(function () {
	const org = Orgs.current();
	const qr = FlowRouter.getQueryParam('qr');
	if (qr) {
		const person = Meteor.user()?.fetchPerson?.();
		const pc = person?.pinCode || "";
		const pcs = person?.pinCodeSupplemental || ""
		$("#pinCode").val(`${pc}${pcs}`);
		$("#pinSubmit").trigger("click");
	} else if (org.hasCustomization("people/qrCodeCheckin/enabled")) {
		Meteor.callAsync("getQrCode").then((result) => {
			const { authString } = result;
			const qrcode = new QRCode(document.getElementById("qrCodeDiv"), authString || "unavailable");
		})
	}
});

Template.qrCodeBlockAutoRefresh.onRendered(function () {
	const instance = this;

	const org = Orgs.current();
	const qr = FlowRouter.getQueryParam('qr');

	let qrCodeInterval;

	const checkEverySeconds = 10;

	let currentQrCodeItem = null;

	const refreshQrCode = function () {
		Meteor.callAsync("getQrCode").then((result) => {
			const { authString, qrCode: updatedQrCode } = result;
			currentQrCodeItem = updatedQrCode;
			const qrCodeDiv = document.getElementById("qrCodeDiv");
			qrCodeDiv.innerHTML = "";
			const qrcode = new QRCode(qrCodeDiv, authString || "unavailable");
		})
	};

	function isQrCodeExpired() {
		if (!currentQrCodeItem) {
			// This should never happen
			return false;
		}
		// currentQrCodeItem has a createdAt
		const qrCodesExpireAfterMinutes = org.getCheckInCheckOutQrCodesExpireAfterMinutesValue() ?? 24 * 60
		const qrCodeItemCreatedAt = moment(currentQrCodeItem.createdAt)
		const qrCodeItemExpiresAt = qrCodeItemCreatedAt.clone().add(qrCodesExpireAfterMinutes, 'minutes')
		const now = moment()
		if (now.isAfter(qrCodeItemExpiresAt)) {
			return true;
		}
		return false;
	}

	if (qr) {
		const person = Meteor.user()?.fetchPerson?.();
		const pc = person?.pinCode || "";
		const pcs = person?.pinCodeSupplemental || ""
		$("#pinCode").val(`${pc}${pcs}`);
		$("#pinSubmit").trigger("click");
	} else if (org.hasCustomization("people/qrCodeCheckin/enabled")) {
		refreshQrCode();
		qrCodeInterval = Meteor.setInterval(() => {
			if (isQrCodeExpired()) {
				refreshQrCode();
			}
		}, checkEverySeconds * 1000);
	}

	instance.autorun(() => {
		// If the Orgs.current() changes, or if the FlowRouter query param changes, 
		// you might want to clear and reset the interval here.

		// Stop the interval if it's running
		if (qrCodeInterval) {
			Meteor.clearInterval(qrCodeInterval);
		}

		// Reassign org and qr from their reactive data sources
		const org = Orgs.current();
		const qr = FlowRouter.getQueryParam('qr');

		// Recreate the interval
		if (org.hasCustomization("people/qrCodeCheckin/enabled") && !qr) {
			qrCodeInterval = Meteor.setInterval(() => {
				if (isQrCodeExpired()) {
					refreshQrCode();
				}
			}, checkEverySeconds * 1000);
		}
	});

	instance.view.onViewDestroyed(() => {
		// When the template is destroyed, clear the interval if it's running
		if (qrCodeInterval) {
			Meteor.clearInterval(qrCodeInterval);
		}
	});
});
