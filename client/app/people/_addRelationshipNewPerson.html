<template name="_addRelationshipNewPerson">
	<div id="_addRelationshipNewPerson" class="modal fade" data-cy="add-relationship-new-person">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">Create New Related Person</h5>
					<div class="d-flex align-items-center">
						<div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
							<span class="fad-regular fad-primary fad fa-times"></span>
						</div>
					</div>
				</div>
				<div class="modal-body bg-white">
					<form id="frmRelationshipPerson">
						<div class="form-group row">
			        <label class="col-xl-3 col-lg-3 text-right col-form-label">First Name</label>
			        <div class="col-lg-9 col-xl-6">
			          <input type="text" name="addRelationshipFirstName" id="addRelationshipFirstName" class="form-control form-control-lg form-control-solid" data-cy="input-relationship-first-name">
			        </div>
						</div>
			      <div class="form-group row">
			        <label class="col-xl-3 col-lg-3 text-right col-form-label">Last Name</label>
			        <div class="col-lg-9 col-xl-6">
			          <input type="text" name="addRelationshipLastName" id="addRelationshipLastName" class="form-control form-control-lg form-control-solid" data-cy="input-relationship-last-name">
			        </div>
			      </div>
			      <div class="form-group row">
			        <label class="col-xl-3 col-lg-3 text-right col-form-label">Type</label>
			        <div class="col-lg-9 col-xl-6">
			          <select class="form-control form-control-lg form-control-solid" name="relationshipType" id="inputRelationshipType" data-cy="add-relationship-type" multiple>
			            <option value="family" {{selectedIfEqual isFamilyRelationship true}}>Family</option>
			            <option value="authorizedPickup">Authorized Pickup</option>
			            <option value="emergencyContact">Emergency Contact</option>
			          </select>
			        </div>
			      </div>
						{{#if isFamilyRelationship}}
							<div class="form-group row">
								<label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
								<div class="col-lg-9 col-xl-6">
									<div class="checkbox-inline">
										<label class="checkbox checkbox-primary">
										  <input type="checkbox" id="primary_caregiver" name="primary_caregiver" data-cy="primary-caregiver" {{checkedIfEq true relationship.primaryCaregiver}}>
										  <span></span>
										  Primary Caregiver
										</label>
									</div>
								</div>
							</div>
						{{/if}}
			      <div class="form-group row">
			        <label class="col-xl-3 col-lg-3 text-right col-form-label">Description (e.g. grandparent, etc.)</label>
			        <div class="col-lg-9 col-xl-6">
			          <input type="text" class="form-control form-control-lg form-control-solid" name="relationshipDescription" id="inputRelationshipDescription" value="{{relationship.relationshipDescription}}" data-cy="add-relationship-description">
			        </div>
			      </div>
					</form>
				</div>
				<div class="modal-footer">
					<!-- NOTE: the save button should execute on the yielded _personAccount JS -->
					<div class="btn btn-primary font-weight-bolder mr-2" id="btnSaveNewRelationship" data-cy="relationship-save-btn">Save</div>
					<div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</div>
				</div>
			</div>
		</div>
	</div>
</template>
