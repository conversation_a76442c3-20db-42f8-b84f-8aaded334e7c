import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_historyTab.html';
import { Log } from '../../../lib/util/log';
import { HistoryAuditChangeTypes, HistoryAuditRecordTypes } from '../../../lib/constants/historyAuditConstants';

Template.historyTab.onCreated(function () {
    this.histories = new ReactiveVar([]);

    Meteor.callAsync('getHistoryForPerson', this.data._id).then(res => {
        this.histories.set(res);
    }).catch(err => {
        Log.error(err);
        mpSwal.fire({
            icon: 'error',
            title: 'Error',
            text: `Error fetching histories: ${err.reason || err.message}`
        });
        return;
    });
});

Template.historyTab.helpers({
    histories() {
        return Template.instance().histories.get();
    },
    getMadeBy(performedByName) {
        return performedByName || 'System';
    },
    getTypeFromRecordType(recordType) {
        switch (recordType) {
            case HistoryAuditRecordTypes.PERSON:
                return 'Record';
            case HistoryAuditRecordTypes.SCHEDULE:
                return 'Schedule';
            case HistoryAuditRecordTypes.PAYMENT_METHOD:
                return 'Payment Method';
            case HistoryAuditRecordTypes.STATUS:
                return 'Status';
            case HistoryAuditRecordTypes.REFUND:
                return 'Refunded';
            case HistoryAuditRecordTypes.RELATIONSHIP:
                return 'Relationship';
        }
    },
    getActionFromChangeType(changeType) {
        switch (changeType) {
            case HistoryAuditChangeTypes.ADD:
                return 'Added';
            case HistoryAuditChangeTypes.EDIT:
                return 'Edited';
            case HistoryAuditChangeTypes.DELETE:
                return 'Deleted';
            default:
                return changeType;
        }
    }
});

Template.historyTab.events({
    // Add your event handlers here
});