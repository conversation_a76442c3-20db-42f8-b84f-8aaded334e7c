<template name="addSubsidyModal">
    <div id="addSubsidyModal" class="modal fade" role="dialog">
        <div class="modal-dialog modal-dialog-scrollable modal-lg" role="document">
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header flex-column">
                    <div class="container-fluid p-0 d-flex justify-content-between">
                        {{#if showDistrictEmail}}
                            <h5 class="modal-title align-self-center">District Employee Email</h5>
                        {{else}}
                            <h5 class="modal-title align-self-center">Voucher Information</h5>
                        {{/if}}
                        <div class="d-flex align-items-center">
                            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                                <span class="fad-regular fad-primary fad fa-times"></span>
                            </div>
                        </div>
                    </div>
                    {{#if showDistrictEmail}}
                    {{else}}
                        <!-- Subsidy modal description -->
                        <div class="d-grid gap-3">
                            <div class="p-3">
                                Please enter the name of the subsidy program. If you do not know the name or the agency identifier/case number, please type “Unknown”.
                            </div>
                            <div class="p-3">
                                Note: If you are looking to add a Military, Free & Reduced Lunch, or other coupon code, tap the "Close" button below and visit the "Coupon Code" option instead.
                            </div>
                        </div>
                    {{/if}}
                </div>
                <div class="modal-body bg-white">
                    {{#if showDistrictEmail}}
                        <form id="districtEmailForm">
                            <div class="form-group row">
                                <label for="districtEmail" class="col-xl-3 col-lg-3 col-md-3 col-sm-3 text-right col-form-label">District Employee Email</label>
                                <div class="col-lg-6 col-md-6 col-sm-3">
                                    <input data-cy="district-email-input" id="districtEmail" type="text" class="form-control rounded email" data-required="true" name="districtEmail">
                                    <div id="validateDistrictEmail" class="invalid-feedback" style="display: none">
                                    </div>
                                </div>
                            </div>
                        </form>
                    {{else}}
                        <form id="subsidyForm">
                            {{#each child in children}}
                                <div class="row">
                                    <div class="col-3"></div>
                                    <label class="col-lg-6 col-md-6 col-sm-3" style="color:var(--primary);font-size:125%;font-weight:bolder">{{childFullName child}}</label>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-md-3 col-sm-3 text-right col-form-label">Agency:</label>
                                    <div class="col-lg-6 col-md-6 col-sm-3">
                                        <input data-cy="agency-subsidy" id="{{@index}}_agency" type="text" class="form-control" name="agency">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-md-3 col-sm-3 text-right col-form-label">Case #:</label>
                                    <div class="col-lg-6 col-md-6 col-sm-3">
                                        <input data-cy="voucher-subsidy-number" id="{{@index}}_case" type="text" class="form-control" name="case">
                                    </div>
                                </div>
                            {{/each}}
                        </form>
                    {{/if}}
                </div>
                <div class="modal-footer">
                    <button data-cy="finish-registration-btn" id="finishRegistration" type="button" class="btn btn-primary font-weight-bolder mr-2" data-stayopen="false"
                            disabled="{{submitButtonDisabled}}">
                        Finished
                    </button>
                    <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</template>