import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { RegistrationFlowType, RegistrationUtils } from "../../../../lib/util/registrationUtils";
import { MiscUtils } from "../../../../lib/util/miscUtils";
import { ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE } from "../../../../lib/constants/billingConstants";
import { registrationDataSessionName, couponCodes, employeeDiscount, isParentEdit } from "../../registrationFlow/registrationFlow";
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './addSubsidyModal.html';
import _ from '../../../../lib/util/underscore';
import { Orgs } from '../../../../lib/collections/orgs';
import { hideModal } from '../../main';

Template.addSubsidyModal.created = function () {
    this.disabledSubmitBtn = new ReactiveVar(true);
};

Template.addSubsidyModal.helpers({
    submitButtonDisabled() {
        return Template.instance().disabledSubmitBtn.get();
    },
    children() {
        return Template.instance().data.savedData.children || [];
    },
    childFullName(child) {
        return RegistrationUtils.childFullName(child);
    },
    showDistrictEmail() {
        return Template.instance().data.showDistrictEmail;
    }
});

Template.addSubsidyModal.events({
    "input #subsidyForm": function (e, i) {
        const children = Template.instance().data.savedData.children || [];
        let count = 0;
        _.each(children, (child, index) => {
            const agencyVal = $(`#${index}_agency`).val();
            const caseVal = $(`#${index}_case`).val();
            const hasBothFieldsFilled = !!agencyVal && !!caseVal;
            if (hasBothFieldsFilled) {
                count++;
            }
        })
        i.disabledSubmitBtn.set(count === 0);
    },
    "input #districtEmail": function (e, i) {
        const districtEmail = $('#districtEmail');
        const validationDiv = $('#validateDistrictEmail');
        const validation = MiscUtils.validateInput(e);
        if (!validation.valid) {
            districtEmail.addClass('is-invalid');
            districtEmail.removeClass('is-valid');
            validationDiv.text(validation.message);
            $("#validateDistrictEmail").show();
        } else {
            districtEmail.addClass('is-valid');
            districtEmail.removeClass('is-invalid');
            $("#validateDistrictEmail").hide();
        }
        if (districtEmail.val().trim().length > 2 && validation.valid) {
            i.disabledSubmitBtn.set(false);
        } else {
            i.disabledSubmitBtn.set(true);
        }
    },
    "click #finishRegistration": function (e, i) {
        i.disabledSubmitBtn.set(true);
        e.preventDefault();
        const currentData = Template.instance().data.savedData || {};
        const currentPlanTypes = (currentData.plans || []).flat().filter(p => p.type === PLAN_TYPE);
        if (!Template.instance().data.showDistrictEmail) {
            _.each(currentData.children, (child, index) => {
                child.agencyIdentifier = $(`#${index}_agency`).val() || '';
                child.subsidyCode = $(`#${index}_case`).val() || '';
            })
        }
        const cachedCoupons = Session.get(couponCodes) || [];
        const invalidCoupons = RegistrationUtils.validateCouponWithDiscounts(cachedCoupons);
        if (invalidCoupons.length > 0 && currentPlanTypes.length) {
            mpSwal.fire({
                text: `These coupons ${invalidCoupons} cannot be used with other discounts. If you choose to proceed, the registration process will end and these coupons will be removed`,
                showCancelButton: true,
                confirmButtonText: "Proceed",
                cancelButtonText: "Close"
            }).then(result => {
                if (result.value) {
                    // remove coupons from session
                    const newCachedCoupons = cachedCoupons.filter(coupon => !invalidCoupons.includes(coupon.code));
                    removeInvalidCouponsFromAllocations(currentData, invalidCoupons);
                    Session.set(couponCodes, newCachedCoupons);
                    currentData.couponCodes = newCachedCoupons;
                    putRegistrationOnHold(currentData, i, i.data.registrationFlowType);
                } else {
                    i.disabledSubmitBtn.set(false);
                }
            });
            return;
        }
        currentData.couponCodes = cachedCoupons;
        if (Template.instance().data.showDistrictEmail) {
            currentData.districtEmail = $('#districtEmail').val() || '';
        }
        putRegistrationOnHold(currentData, i, i.data.registrationFlowType);
    },
    "submit #subsidyForm": function (e, i) {
        e.preventDefault();
    }
});

function putRegistrationOnHold(currentData, instance, registrationFlowType) {
    // Add program tab and PLR registration should both be supported
    let orgId = FlowRouter.getQueryParam('orgId') || Orgs.current()._id
    Meteor.callAsync('placeRegistrationOnHold', orgId, currentData).then(() => {
        hideModal('#addSubsidyModal');
        Meteor.callAsync('formatMessage', currentData, true).then(res => {
            for (const o of res) {
                parent.postMessage({
                    name: o.action,
                    detail: o
                }, '*');
            }
        });
        if (registrationFlowType === RegistrationFlowType.PLR) {
            if (currentData.districtEmail) {
                FlowRouter.go(`/registration-completed?onHold=true&districtEmail=true&orgId=${FlowRouter.getQueryParam('orgId')}`);
            } else {
                FlowRouter.go(`/registration-completed?onHold=true&orgId=${FlowRouter.getQueryParam('orgId')}`);
            }
        } else if (registrationFlowType === RegistrationFlowType.PROGRAM_TAB) {
            instance.data.rerenderRegistration.set(true);
        } else { // Only two cases should be PLR and Program Tab for existing children, throw error
            mpSwal.fire('Error', 'Invalid registration flow type. Valid flow types are Parent Led Registration and the Program Tab', 'error')
        }
    }).catch(err => {
        mpSwal.fire('Error', err.message, 'error');
    });
}
