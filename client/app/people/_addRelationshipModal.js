import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_addRelationshipModal.html';
import { People } from "../../../lib/collections/people";
import $ from 'jquery';
import { hideModal } from '../main';
import logger from '../../../imports/winston';

Template._addRelationshipModal.created = function() {
  this.selectedRelationships = new ReactiveVar(["family"]);
  this.personId = new ReactiveVar("");
  this.isPrimaryCaregiver = new ReactiveVar(false);
  this.fullName = new ReactiveVar('');
  this.relationshipDescription = new ReactiveVar('');
}

Template._addRelationshipModal.rendered = function() {
  
	$("#existingPersonSearch").select2({
		dropdownParent: $('#_addRelationshipModal'),
		// theme: "bootstrap4"
	});

	var instance = this;
	this.autorun(() => {
		var subsCache = new SubsCache(-1, -1);
		subsCache.subscribe('thePeopleAssociateExisting',{"alreadySelectedRelationships":Template.instance().data.alreadySelectedRelationships});
	})

	if (this.data.relationshipId) {
		Meteor.callAsync('getRelationshipEditDetails', this.data.relationshipId)
			.then(result => {
				this.relationshipDescription.set(result.relationshipDescription);
				this.fullName.set(result.fullName);
				this.personId.set(result.personId);
				this.isPrimaryCaregiver.set(result.isPrimaryCaregiver);
				this.selectedRelationships.set(result.relationships);
			})
			.catch(error => {
				logger.error('Error fetching relationships:', error);
			});
	}

	this.autorun(() => {
		const relationships = instance.selectedRelationships.get();
		if (relationships) {
			$("#inputRelationshipType").multiselect('destroy');
			$("#inputRelationshipType").multiselect({
				multiple: true,
				selectionCssClass:"form-control form-control-lg form-control-solid",
				onChange: function(option, checked, select) {
					instance.selectedRelationships.set($("#inputRelationshipType").val());
				},
			});
			$("#inputRelationshipType").multiselect('select', relationships);
		}
	});
}

Template._addRelationshipModal.helpers({
	"isEditing": function() {
		const relationshipId = Template.instance().data.relationshipId;
		return !!(relationshipId);
	},
	"isPrimaryCaregiver": function () {
		return Template.instance().isPrimaryCaregiver.get();
	},
	"people": function() {
		const alreadySelectedRelationships = Template.instance().data.alreadySelectedRelationships || []
		return People.find({type: {$not: "person"}, inActive: {$ne: true}, _id: { $nin: Object.keys(alreadySelectedRelationships) }}, {sort: {lastName: 1, firstName: 1}});
	},
	"isFamilyRelationship": function() {
		return Template.instance().selectedRelationships.get()?.includes('family') || false;
	},
	"getFullName": function() {
		return Template.instance().fullName.get()
	},
	"getRelationshipDescription": function() {
		return Template.instance().relationshipDescription.get() || '';
	},
	"relationships": function() {
		return Template.instance().selectedRelationships.get();
	}
});

Template._addRelationshipModal.events({
	'click #btnSaveRelationship': function(event, template) {
		event.preventDefault();

		const relationshipId = template.data.relationshipId;
		var selectedRelationshipType = $("#inputRelationshipType").val();

		var relationshipData = {
			relationshipType: selectedRelationshipType,
			relationshipDescription: $("#inputRelationshipDescription").val(),
		};

		if (selectedRelationshipType.includes("family")) {
			relationshipData.primaryCaregiver = $("#primary_caregiver").prop("checked");
		} else {
			relationshipData.primaryCaregiver = false;
		}

		if(relationshipData?.relationshipType.length === 0){
			return mpSwal.fire("Error", "Please select a relationship type", "error");
		}

		if (relationshipId) {
			relationshipData.relationshipId = relationshipId;

			Meteor.callAsync("editRelationship", relationshipData).then((response)=>{
				if(template.data.onSave) {
					template.data.onSave();
				}

				if (response?.relationshipId) {
					$('.editRelationshipLink').attr('data-id', response.relationshipId);
					$('.deleteRelationshipLink').attr('data-id', response.relationshipId);
				}

				hideModal("#_addRelationshipModal");
			}).catch((error)=>{
				mpSwal.fire("Error", error.reason, "error");
				hideModal("#_addRelationshipModal");
			})
		} else {
			relationshipData["personId"] = $("#existingPersonSearch").val();
			relationshipData["targetPersonId"] = template.data.personId;

			Meteor.callAsync('addRelationship', relationshipData).then((response)=>{
				if(template.data.onSave) {
					template.data.onSave();
				}
				hideModal("#_addRelationshipModal");
			}).catch((error)=>{
				mpSwal.fire("Error", error.reason, "error");
				hideModal("#_addRelationshipModal");
			})
		}
	},
});
