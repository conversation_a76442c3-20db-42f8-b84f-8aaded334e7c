import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './_runSummaryReportFormModal.html';
const moment = require('moment-timezone');
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import '../components/dateRangePicker/dateRangePicker';

Template._runSummaryReportFormModal.created = function() {
	this.filterStartDate = new ReactiveVar(new moment());
	this.filterEndDate = new ReactiveVar(new moment().subtract(7, 'days'))
}

Template._runSummaryReportFormModal.events({
	"click #btnCreateSummaryReport": function(e, i) {
		e.preventDefault();
		var startDate = encodeURIComponent(i.filterStartDate.get().format("YYYY-MM-DD"));
		var endDate = encodeURIComponent(i.filterEndDate.get().format("YYYY-MM-DD"));
		//var baseUrl = process.env.ROOT_URL;
   		//if (baseUrl.slice(baseUrl.length-1) != "/") baseUrl+= "/";
   		var destUrl = "/people/" + FlowRouter.current().params._id + "/summaryReport?startDate=" + startDate + "&endDate=" + endDate;
   		console.log(destUrl);
   		window.open(destUrl, "_blank");
	}
});

Template._runSummaryReportFormModal.helpers({
	"dateFields": function() {
    return {
      filterStartDate: Template.instance().filterStartDate,
      filterEndDate: Template.instance().filterEndDate,
    }
	},
	"formattedTodayDate": function() {
		return new moment().format("YYYY-MM-DD");
	}
});
