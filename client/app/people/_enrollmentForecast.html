<template name="_enrollmentForecast">
  <div class="row">
    <div class="col-lg-9 col-xl-6 offset-xl-3 mb-5">
      <span class="font-size-h3">Enrollment Forecast</span>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Default Group</label>
    <div class="col-lg-9 col-xl-6">
      <input class="form-control bg-white" style="border-color:#fff;" type="text" value="{{getDefaultGroup}}" disabled/>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Enrollment Date</label>
    <div class="col-lg-9 col-xl-6">
      <input class="form-control bg-white" style="border-color:#fff;" type="text" value="{{getEnrollmentDate}}" disabled/>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Withdraw Date</label>
    <div class="col-lg-9 col-xl-6">
      <input class="form-control bg-white" style="border-color:#fff;" type="text" value="{{getWithdrawDate}}" disabled/>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Days of Week Value(s)</label>
    <div class="col-lg-9 col-xl-6 d-flex flex-row">
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="defaultMondayVal" step="0.01" min="0" max="1" value={{getDefaultClassListDayValue "M"}}>
        <span>Mon</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="defaultTuesdayVal" step="0.01" min="0" max="1" value={{getDefaultClassListDayValue "T"}}>
        <span>Tue</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="defaultWednesdayVal" step="0.01" min="0" max="1" value={{getDefaultClassListDayValue "W"}}>
        <span>Wed</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="defaultThursdayVal" step="0.01" min="0" max="1" value={{getDefaultClassListDayValue "R"}}>
        <span>Thu</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="defaultFridayVal" step="0.01" min="0" max="1" value={{getDefaultClassListDayValue "F"}}>
        <span>Fri</span>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-9 col-xl-6 offset-xl-3 mb-5">
      <span class="font-size-h3">Transition Group Settings</span>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Transition Group</label>
    <div class="col-lg-9 col-xl-6">
      <select class="form-control form-control-solid" name="transitionGroup" id="transitionDefaultGroup">
        <option value="" {{selectedIfEqual getTransitionGroupId ''}}></option>
        {{#each groups}}
          <option value="{{_id}}" {{selectedIfEqual getTransitionGroupId _id}}>{{name}}</option>
        {{/each}}
      </select>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Transition Date</label>
    <div class="col-lg-9 col-xl-6">
      <input class="form-control form-control-solid" type="date" id="transitionGroupDate" value="{{formatDate getTransitionGroupDate 'YYYY-MM-DD'}}">
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Days of Week Value(s)</label>
    <div class="col-lg-9 col-xl-6 d-flex flex-row">
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="transitionMondayVal" step="0.01" min="0" max="1" value={{getTransitionClassListDayValue "M"}}>
        <span>Mon</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="transitionTuesdayVal" step="0.01" min="0" max="1" value={{getTransitionClassListDayValue "T"}}>
        <span>Tue</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="transitionWednesdayVal" step="0.01" min="0" max="1" value={{getTransitionClassListDayValue "W"}}>
        <span>Wed</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="transitionThursdayVal" step="0.01" min="0" max="1" value={{getTransitionClassListDayValue "R"}}>
        <span>Thu</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="transitionFridayVal" step="0.01" min="0" max="1" value={{getTransitionClassListDayValue "F"}}>
        <span>Fri</span>
      </div>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
    <div class="col-lg-9 col-xl-6 d-flex flex-row">
      <div class="btn btn-primary font-weight-bolder mr-4" id="btnSaveClassList">Save Enrollment Forecast</div>
      <div class="btn btn-secondary font-weight-bolder" id="btnClearTransitionClassList">Clear Transition Settings</div>
    </div>
  </div>
</template>
