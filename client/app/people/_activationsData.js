import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_activationsData.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import _ from '../../../lib/util/underscore';

Template._activationsData.onCreated( function() {
	Template.instance().activationsData = new ReactiveVar();
});

Template._activationsData.onRendered( function() {
	var self = Template.instance();
	Meteor.callAsync("getActivationsData", {includeDetail: true}).then((result)=>{
		self.activationsData.set(result);
	}).catch(()=>{
		self.activationsData.set();
	})
});

Template._activationsData.helpers( {
  getType() {
    return Template.instance().data.type;
  },
	people() {
		const rawData = Template.instance().activationsData.get(),
			detailData = rawData && rawData.detailData,
			currentType = Template.instance().data.type;
		let output;

		if (detailData && currentType == "noActivations") {
			output = detailData.filter( (p) => p.associatedFamilies.filter( (af) => af.activeUserAccount ).length == 0 );
		} else if (detailData && currentType == "anyActivations") { 
			output = detailData.filter( (p) => p.associatedFamilies.filter( (af) => af.isPending  || !af.activeUserAccount).length > 0 );
		} else if (detailData) {
			output = detailData.filter( (p) => p.associatedFamilies.length == 0 );
		}

		return _.chain(output)
			.map( (p) => { 
				
				if (p.associatedFamilies.length == 0) {
					return {
						_id: p._id,
						fullName: p.lastName + ", " + p.firstName,
						status: "No relationships",
						actionsAvailable: ["associatePerson"]
					}
				} else 
					return p.associatedFamilies.filter( (fp) => fp.isPending || !fp.activeUserAccount ).map( (fp) => {
						const latestInvitation = fp.outstandingInvitations.length > 0 && _.last(_.sortBy(fp.outstandingInvitations, "createdAt")),
							status = (latestInvitation && latestInvitation.lastSentAt && "Invitation sent: " + new moment(latestInvitation.lastSentAt).format("M/DD/YYYY") ) ||
							(latestInvitation && "Invitation created" + ( latestInvitation.createdAt ? ": " + new moment(latestInvitation.createdAt).format("M/DD/YYYY") : "")) ||
							"No invitations sent",
							actionsAvailable = [];
						
						if (latestInvitation) 
							actionsAvailable.push("resendInvitation");
						else
							actionsAvailable.push("createInvitation");
						
						actionsAvailable.push("associatePerson");

						return {
							_id: p._id,
							fullName: p.lastName + ", " + p.firstName,
							familyName: fp.firstName + " " + fp.lastName,
							familyId: fp._id,
							status,
							actionsAvailable
						}; 
					}); 
			})
			.flatten()
			.sortBy( (r) => { return r.fullName+"|" + r.familyName; })
			.value();

	},
	'getPeopleLabel': function () {
		return orgLanguageTranformationUtil.getEntityType('person');		
	},
});

Template._activationsData.events( {
	"change .activations-action": function(e, i) {
    
    //poor way of rendering the same template in memory and only running this function once on duplicate
    //data rows
    var currentType = i.data.type
    var targetType = $(e.currentTarget).attr("data-type");
    if (currentType != targetType) return;
    
    
		const current = $(e.currentTarget).find("option:selected"), option = current.first();
		if ($(option).val() == "activations-resend-invitation") {
			const id = $(option).data("id");
			Meteor.callAsync("resendInvitationByPersonId", id).then((result)=>{
				mpSwal.fire("Success", "Invitation successfully re-sent");
			})
			.catch((error)=>{
				mpSwal.fire("Error", "Error re-sending invitation:" + error.reason);
			});	
		} else if ($(option).val() == "view-profile") {
			const id = $(option).data("id");
			FlowRouter.go("/people/" + id + "#relationships");
		} else if ($(option).val() == "activations-create-invitation") {
			const id = $(option).data("id");
			FlowRouter.go("/people/" + id + "#profile");
		}
		
	},
});
