import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_enrollmentForecast.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../lib/collections/people';
import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';

Template.person.onCreated(async function() {
  const groupName = await this.data.findDefaultGroup();
	this.personDefaultGroup.set(groupName);
});

Template._enrollmentForecast.helpers({
  groups: function() {
    return Groups.find({
      orgId: Meteor.user()["orgId"]
    });
  },
  getDefaultGroup: function() {
		if (Template.instance().personDefaultGroup.get())
			return Template.instance().personDefaultGroup.get().name;
	},
  getEnrollmentDate: function() {
    const orgs = Orgs.current();
    const prefix = orgs.profileDataPrefix();
    if (prefix) {
      return this[`${prefix}`] && new moment(this[`${prefix}`]["enrollmentDate"]).format("MM/DD/YYYY");
    } else if (this.enrollmentDate) {
      return new moment(this.enrollmentDate).format("MM/DD/YYYY");
    }
  },
  getWithdrawDate: function() {
    return this.getWithdrawDate();
  },
  getDefaultClassListDayValue: function(d) {
    if (this.classList && this.classList.defaultDays) {
      return this.classList.defaultDays[d];
    }
  },
  getTransitionClassListDayValue: function(d) {
    if (this.classList && this.classList.defaultDays) {
      return this.classList.transitionDays[d];
    }
  },
  getTransitionGroupId: function() {
    var currentPerson = People.findOne(Template.parentData()._id);
    return (currentPerson.transitionGroupId) ? currentPerson.transitionGroupId : '';
  },
  getTransitionGroupDate: function() {
    if (this.transitionGroupDate) {
      return this.transitionGroupDate;
    }
  },
});

Template._enrollmentForecast.events({
  'click #btnClearTransitionClassList': function(e) {
    e.preventDefault();
    var personId = FlowRouter.current().params._id;
    Meteor.callAsync('clearPersonTransitionClassList', { personId }).then((result) => {
      mpSwal.fire("Success", "Transition Changes Cleared", "success");
    } ).catch((error) => {
      mpSwal.fire("Error Clearing Transition Enrollment Forecast", error.reason, "error");
    } );
  },
  'click #btnSaveClassList': function(e) {
    e.preventDefault();
    const personId = FlowRouter.current().params._id;
    const defaultDays = {
      M: $("#defaultMondayVal").val() || 0,
      T: $("#defaultTuesdayVal").val() || 0,
      W: $("#defaultWednesdayVal").val() || 0,
      R: $("#defaultThursdayVal").val() || 0,
      F: $("#defaultFridayVal").val() || 0,
    };
    
    const transitionDays = {
      M: $("#transitionMondayVal").val() || 0,
      T: $("#transitionTuesdayVal").val() || 0,
      W: $("#transitionWednesdayVal").val() || 0,
      R: $("#transitionThursdayVal").val() || 0,
      F: $("#transitionFridayVal").val() || 0,
    };
    
    const transitionGroupId = $("#transitionDefaultGroup option:selected").val()
    const transitionMoment = new moment($("#transitionGroupDate").val(), "YYYY-MM-DD");
    let transitionGroupDate = null;
    if (transitionMoment.isValid()) {
      transitionGroupDate = transitionMoment.valueOf();
    }
    
    Meteor.callAsync('updatePersonClassList', { personId, defaultDays, transitionDays, transitionGroupId, transitionGroupDate }).then((result) => {
      mpSwal.fire("Success", "Enrollment Forecast Changes Saved", "success");
    }).catch((error) => {
      mpSwal.fire("Error Saving Enrollment Forecast Changes", error.reason, "error");
    });
  },
})
