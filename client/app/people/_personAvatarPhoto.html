<template name="_personAvatarPhoto">
    {{# unless person.hasAvatar }}
        <div class="d-flex avatar-circle align-items-center justify-content-center"
             style="background-color: {{ getAvatarBackground person.personInitials }}; height: {{ getHeight }}; width: {{ getWidth }};">
            <span data-cy="initials" class="initials">{{ person.personInitials }}</span>
        </div>
    {{ else }}
        <div class="people-list-user-img"
             style="background-image:url({{ person.getAvatarUrl }}); height: {{ getHeight }}; width: {{ getWidth }};"></div>
    {{/ unless }}
</template>