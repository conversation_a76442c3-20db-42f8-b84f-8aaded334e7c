import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_addImmunizationModal.html';
import _ from '../../../lib/util/underscore';
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal } from '../main';

Template._addImmunizationModal.created = function() {
	this.immunizationDateEntries = ReactiveVar(0);
};

Template._addImmunizationModal.rendered = function() {
  $("#newImmunizationDate").datepicker({ autoclose: true, todayHighlight: true });
}

Template.immunizationDateField.rendered = function() {
  $(`#newImmunizationDate-${this.data.num}`).datepicker({ autoclose: true, todayHighlight: true });
}

Template._addImmunizationModal.helpers({
  availableImmunizations: function() {
		return _.sortBy(Orgs.current().availableImmunizations(), "description");
	},
  getImmunizationDateEntryFields: function() {
    const entryTotal = Template.instance().immunizationDateEntries.get();
    const resultMap = [];
    for (let x =0; x< entryTotal; x++) {
      resultMap.push({ num: x });
    }
    return resultMap;
  },
})

Template._addImmunizationModal.events({
  'click #btnAddImmunizationDate': function(e) {
    e.preventDefault();
    let currentVal = Template.instance().immunizationDateEntries.get();
    Template.instance().immunizationDateEntries.set(++currentVal);
  },
  'click #btnSaveImmunization': function(e) {
    e.preventDefault();
    
    const personId = Template.instance().data.personId;
    const immunizationType = $("#newImmunizationType").val();
    const immunizationDefinitionId = $("#newImmunizationType").find('option:selected').data('definitionid');
    // const immunizationDateVal = $("#newImmunizationDate").val();
    // const mResult = immunizationDateVal.match(/\d{2}\/\d{2}\/\d{4}/g);
    // if (mResult == null) {
    // 	return swal({type:"error", title:"Invalid Date Format", text: "Enter a date with the format MM/DD/YYYY"});
    // }
    
    const extraDateEntries = Template.instance().immunizationDateEntries.get();
    const extraDates = [];
    for (let x = 0; x < extraDateEntries; x++) {
      let momentDate = new moment($(`#newImmunizationDate-${x}`).val(), "MM/DD/YYYY");
      if (momentDate.isValid()) {
        extraDates.push(momentDate.valueOf());
      }
    }
    
    const immunizationDate = new moment($("#newImmunizationDate").val(), "MM/DD/YYYY");
    const immunizationExempt = $("#immunizationExempt").prop("checked");
    const immunizationExemptDose = $("#immunizationExemptDose").prop("checked");

    const callInsertImmunization = function(passedOptions) {
      Meteor.callAsync("insertImmunization", passedOptions).then((response)=>{
        hideModal("#_addImmunizationModal");
      }).catch((error)=>{
        mpSwal.fire("Error", error.reason, "error")
      })
    }

    const options = {
      personId,
      immunizationDefinitionId,
      immunizationType,
      immunizationDate: immunizationDate.valueOf(),
      immunizationExemptDose,
      extraDates,
    };

    if (immunizationExempt) {
      mpSwal.fire({
        title: "Add Immunization Exemption",
        text: "This will suppress future overdue warnings for this immunization type. Please choose a reason:",
        type: "warning",
        input: "select",
        inputOptions: Orgs.current().immunizationExemptionOptions(),
        showCancelButton: true
      }).then( (result) => {
        if (result.value) {
          options.immunizationExemption = Orgs.current().immunizationExemptionOptions()[result.value];
          callInsertImmunization(options);
        }
      });
    } else {
      if (!immunizationDate.isValid()) return mpSwal.fire("Error", "Invalid date selected", "error");
      callInsertImmunization(options);
    }
    
  },
})
