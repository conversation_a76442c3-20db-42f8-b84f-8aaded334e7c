import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_profileScheduling.html';

Template._profileScheduling.helpers({
  canManageProfileSchedule: function(){
    const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
    return currentPerson && this.type == "person" && currentPerson.type=="admin";
  },
})

Template._profileScheduling.events({
  'click #btnManageProfileSchedule': function() {
		const recurringSchedule = this.getRecurringSchedule();
		const recurringDays = (recurringSchedule && recurringSchedule.recurringDays) || [];
		const personId = this._id;
		const scheduledTime = (recurringSchedule && recurringSchedule.scheduledTime && new moment(recurringSchedule.scheduledTime, "h:mm a").format("HH:mm"));
		const scheduledEndTime = (recurringSchedule && recurringSchedule.scheduledEndTime && new moment(recurringSchedule.scheduledEndTime, "h:mm a").format("HH:mm"));
		mpSwal.fire({
			title: "Manage Recurring Schedule",
			html: '<div class="popupRecurringSchedule checkbox-list">'+
			'<label class="checkbox checkbox-primary"><input class="chkRecurringDay" value="mon" type="checkbox" ' + (recurringDays.includes("mon") ? 'checked' : '') + '><span></span> Monday</label>' +
			'<label class="checkbox checkbox-primary"><input class="chkRecurringDay" value="tue" type="checkbox" ' + (recurringDays.includes("tue") ? 'checked' : '') + '><span></span> Tuesday</label>' +
			'<label class="checkbox checkbox-primary"><input class="chkRecurringDay" value="wed" type="checkbox" ' + (recurringDays.includes("wed") ? 'checked' : '') + '><span></span> Wednesday</label>' +
			'<label class="checkbox checkbox-primary"><input class="chkRecurringDay" value="thu" type="checkbox" ' + (recurringDays.includes("thu") ? 'checked' : '') + '><span></span> Thursday</label>'+
			'<label class="checkbox checkbox-primary"><input class="chkRecurringDay" value="fri" type="checkbox" ' + (recurringDays.includes("fri") ? 'checked' : '') + '><span></span> Friday</label><br/>'+
      '</div>' +
      '<div>' +
			'<label>Start Time:</label> <input type="time" name="startTime" value="' + scheduledTime + '"><br/>'+
			'<label>End Time:</label> <input type="time" name="endTime" value="' + scheduledEndTime + '"></div>',
			focusConfirm: false,
			showCancelButton: true,
			confirmButtonText: "Save Changes",
			preConfirm: function () {
				return new Promise(function (resolve) {
					resolve([
						$(".chkRecurringDay:checked").map(function(i) { return $(this).val();}).get(),
						$("input[name='startTime']").val(),
						$("input[name='endTime']").val()
					])
				});
			}
		}).then(async (result) => {
			if (result.value) {
				var reservationData = {};
				reservationData.scheduledDate = new moment().startOf("day").valueOf();				
				reservationData.selectedPerson = personId;
				reservationData.recurringDays = result.value[0];
				reservationData.recurringFrequency = 1;
				reservationData.recurringProfileSchedule = true;
				reservationData.scheduledTime = (result.value[1] && result.value[1].length > 0) ? new moment(result.value[1], "HH:mm").format("h:mm a") : "";
				reservationData.scheduledEndTime = (result.value[2] && result.value[2].length > 0) ? new moment(result.value[2], "HH:mm").format("h:mm a") : "";
				if (recurringSchedule) {
					reservationData.reservationId = recurringSchedule._id;
					await Meteor.callAsync("updateReservation", reservationData);
				}
				else
					await Meteor.callAsync("insertReservation", reservationData);
			}
		})
	},
})
