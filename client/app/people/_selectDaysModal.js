import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_selectDaysModal.html';
import moment from 'moment-timezone';
import { ITEM_TYPE, PUNCH_CARD_TYPE } from '../../../lib/constants/billingConstants';
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import { BillingUtils } from '../../../lib/util/billingUtils';
import { AvailableCustomizations } from '../../../lib/customizations';
import { EnrollmentUtils } from '../../../lib/util/enrollmentUtils';
import { Orgs } from '../../../lib/collections/orgs';
import '../registrationFlow/_planDescription';
import '../registrationFlow/_daySelection';
import '../registrationFlow/_effectiveDate';
import { showModal } from '../main';
Template.selectDaysModal.onCreated(function() {
    const { reservation, linkedPlan, bundleReservation, bundleLinkedPlan } = Template.instance().data;

    const bundleChangeInDays = new ReactiveVar(0);
    const bundleEffectiveDate = new ReactiveVar(null);
    const bundlePlan = new ReactiveVar(null);
    const bundlePlanAmount = new ReactiveVar(0.00);
    const bundleSelectedDays = new ReactiveVar([]);
    const changeInDays = new ReactiveVar(0);
    const effectiveDate = new ReactiveVar(null);
    const planAmount = new ReactiveVar(0.00);
    const selectedDays = new ReactiveVar(reservation.recurringDays ?? []);
    const validNext = new ReactiveVar(true);

    let amount = 0;
    if (linkedPlan.planDetails.scaledAmounts.length || linkedPlan.bundlePlanId) {
         amount = getTotalAmountForScaledPlans(this, linkedPlan, selectedDays.get().length);
    } else {
        amount = linkedPlan.planDetails.amount;
    }

    if (bundleLinkedPlan && bundleReservation) {
        bundleSelectedDays.set(bundleReservation.recurringDays ?? []);
        bundlePlanAmount.set(getTotalAmountForScaledPlans(this, bundleLinkedPlan, bundleSelectedDays.get().length));
        bundlePlan.set(bundleLinkedPlan);
    }

    planAmount.set(amount);

    this.bundleChangeInDays = bundleChangeInDays;
    this.bundleEffectiveDate = bundleEffectiveDate;
    this.bundleOriginalSelectedDays = bundleSelectedDays.get();
    this.bundlePlan = bundlePlan;
    this.bundlePlanAmount = bundlePlanAmount;
    this.bundleSelectedDays = bundleSelectedDays;
    this.changeInDays = changeInDays;
    this.effectiveDate = effectiveDate;
    this.originalSelectedDays = selectedDays.get();
    this.planAmount = planAmount;
    this.selectedDays = selectedDays;
    this.validNext = validNext;
});

Template.selectDaysModal.helpers({
    plan: () => {
        const { linkedPlan } = Template.instance().data;
        return linkedPlan;
    },
    bundle: () => {
          return Template.instance().bundlePlan.get();
    },
    planAmount: (bundle) => {
        if (bundle) {
            return Template.instance().bundlePlanAmount.get().toFixed(2) ?? '0.00';
        }
        return Template.instance().planAmount.get().toFixed(2) ?? '0.00';
    },
    isScaledAmount : (plan) => {
        return plan.planDetails.scaledAmounts.length > 0;
    },
    isAllDaysSelected: (plan, isBundle) => {
        const enrollmentMax = plan.masterPlanInfo?.requiredEnrollmentMax;
        const scaledAmounts = plan.planDetails.scaledAmounts.length;
        const maxAmountOfDays = enrollmentMax ? enrollmentMax : scaledAmounts;

        if (isBundle) {
            return Template.instance().bundleSelectedDays.get().length < maxAmountOfDays;
        }

        return Template.instance().selectedDays.get().length < maxAmountOfDays;
    },
    decreaseInDays: (plan) => {
        let hasDecreased = false;
        const linkedPlan = Template.instance().data.linkedPlan;
        if (plan._id !== linkedPlan._id) {
            hasDecreased = Template.instance().bundleChangeInDays.get() < 0;
        } else {
            hasDecreased = Template.instance().changeInDays.get() < 0;
        }

        if (hasDecreased) {
            const org = Orgs.current();
            const timezone = org.getTimezone();
            const child = Template.instance().data.selectedPersonObj;
            const today = moment.tz(timezone).startOf('day').valueOf();
            // earliest allowable start date for the plan, could be today / enrollment date / time period start date; whichever happens last
            const earliestEffectiveDate = prefillStartDate(plan);
            // current position in the billing cycle based on today's date
            const billingCycle = BillingUtils.getPositionInBillingCycle(plan, today, org, child);
            // plan enrollment date (could be same as earliestEffectiveDate)
            const planStart = moment.tz(plan.enrollmentDate, timezone);
            // next billing start date
            const nextBillingStartDate = moment.tz(billingCycle.period.start, 'MM/DD/YYYY', timezone);
            // if the earliest effective date is after the next billing start date, then use the earliest effective date as the date when days are decreased
            const newEffectiveDate = moment(earliestEffectiveDate, 'MM/DD/YYYY').isAfter(nextBillingStartDate) ? earliestEffectiveDate : billingCycle.period.start;
            // only change the date to the new effective date if the effective date happens after the plan enrollment date (this is likely redundant due to prefillStartDate)
            if(moment(newEffectiveDate, 'MM/DD/YYYY').isAfter(planStart)) {
                $(`.date-picker[data-id=${plan._id}]`).val(newEffectiveDate);
           }
        }

        return hasDecreased;
    },
    showSavings: () => {
        const plan = Template.instance().data.linkedPlan;
        // return RegistrationUtils.isEnrolledInBundle(Template.instance().data.selectedPersonObj, plan);
        // not sure they want this so commenting out for now
        return false;
    },
    regularPrice: () => {
        const { linkedPlan, bundleLinkedPlan } = Template.instance().data;
        const selectedDays = Template.instance().selectedDays.get().length;
        const secondSelectedDays = Template.instance().bundleSelectedDays.get().length;
        if (secondSelectedDays === 0 || selectedDays === 0) {
            return 0;
        }
        const firstOriginalAmount = RegistrationUtils.getOriginalAmount(linkedPlan, selectedDays);
        const secondOriginalAmount = RegistrationUtils.getOriginalAmount(bundleLinkedPlan, secondSelectedDays);
        return (firstOriginalAmount + secondOriginalAmount).toFixed(2);
    },
    bundlePrice: () => {
        const plan = Template.instance().data.linkedPlan;
        const selectedDays = Template.instance().selectedDays.get().length;
        const secondSelectedDays = Template.instance().bundleSelectedDays.get().length;
        if (secondSelectedDays === 0 || selectedDays === 0) {
            return 0;
        }
        const bundlePlan = RegistrationUtils.getBundle(plan.bundlePlanId);
        return bundlePlan.scaledAmounts[secondSelectedDays - 1][selectedDays - 1];
    },
    savings: () => {
        const { linkedPlan, bundleLinkedPlan } = Template.instance().data;
        const selectedDays = Template.instance().selectedDays.get().length;
        const secondSelectedDays = Template.instance().bundleSelectedDays.get().length;
        if (secondSelectedDays === 0 || selectedDays === 0) {
            return 0;
        }
        const bundlePlan = RegistrationUtils.getBundle(linkedPlan.bundlePlanId);
        const firstOriginalAmount = RegistrationUtils.getOriginalAmount(linkedPlan, selectedDays);
        const secondOriginalAmount = RegistrationUtils.getOriginalAmount(bundleLinkedPlan, secondSelectedDays);
        const originalAmount = (firstOriginalAmount + secondOriginalAmount).toFixed(2);
        const bundleAmount = bundlePlan.scaledAmounts[secondSelectedDays - 1][selectedDays - 1];;
        return (originalAmount - bundleAmount).toFixed(2);
    },
    parentData: () => {
        const org = Orgs.current();
        const orgHasWeekends = org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);
        const enrolledPlans = Template.instance().data.selectedPersonObj.billing?.enrolledPlans;
        const filteredEnrolledPlans =  enrolledPlans.filter(plan => plan.type !== ITEM_TYPE && plan.type !== PUNCH_CARD_TYPE) ?? [];
        return {
            selectedPlans: filteredEnrolledPlans,
            availablePlans: org?.availableBillingPlans(true, false, true, false),
            bundles: org?.availableBundles(),
            orgTimezone: org?.getTimezone(),
            availableTimePeriods: org?.billing?.timePeriods ?? [],
            availableBillingFrequencies: org?.availableBillingFrequencies(),
            effectiveDate: Template.instance().effectiveDate,
            bundleEffectiveDate: Template.instance().bundleEffectiveDate,
            selectedDays: Template.instance().selectedDays,
            bundleSelectedDays: Template.instance().bundleSelectedDays,
            showWeekends: orgHasWeekends,
            prefillEnrolledStartDate: (plan) => {
                return prefillStartDate(plan);
            },
            getAvailability: (plan, date) => {
                getAvailability(plan, date);
            }
        }
    },
    saveDisabled: () => {
        return !Template.instance().validNext.get();
    },
    validNext: () => {
        return Template.instance().validNext;
    }
})

Template.selectDaysModal.events({
    'click .day-checkbox': function (e, i) {
        const planId = $(e.currentTarget).data('id');
        const { linkedPlan, bundleLinkedPlan } = i.data;
        if (linkedPlan._id !== planId) {
            const newAmount = getTotalAmountForScaledPlans(
                i,
                bundleLinkedPlan,
                RegistrationUtils.numOfCheckedBoxesInPlan(planId),
                RegistrationUtils.numOfCheckedBoxesInPlan(linkedPlan._id)
            );
            i.bundlePlanAmount.set(newAmount);
            i.planAmount.set(newAmount);
            i.bundleChangeInDays.set(getSelectedDays(planId).length - i.bundleOriginalSelectedDays.length);
            i.bundleSelectedDays.set(getSelectedDays(planId));
        } else {
            //change amounts based on scaling if scaled plan or bundle
            if (linkedPlan.planDetails.scaledAmounts.length || linkedPlan.bundlePlanId) {
                const newAmount = getTotalAmountForScaledPlans(
                    i,
                    linkedPlan,
                    RegistrationUtils.numOfCheckedBoxesInPlan(planId),
                    RegistrationUtils.numOfCheckedBoxesInPlan(bundleLinkedPlan?._id ?? 0) || undefined
                );
                i.planAmount.set(newAmount);
                if (linkedPlan.bundlePlanId) {
                    i.bundlePlanAmount.set(newAmount);
                }
            }
            i.changeInDays.set(getSelectedDays(planId).length - i.originalSelectedDays.length);
            i.selectedDays.set(getSelectedDays(planId));
        }
    },
    'input .date-picker': function (e, i) {
        const { linkedPlan, bundleLinkedPlan } = i.data;
        const planId = $(e.currentTarget).data('id');
        const date = $(e.currentTarget).val();
        if (linkedPlan._id !== planId) {
            i.bundleEffectiveDate.set(date);
            getAvailability(bundleLinkedPlan, date);
        } else {
            i.effectiveDate.set(date);
            getAvailability(linkedPlan, date);
        }
    },
    'click #saveButton': function (e, i) {
        handleSubmit(e, i);
    },
    'submit #editPlanEnrollmentFrm': function (event, template) {
        handleSubmit(event, template);
    },
    "click .cancelProgram": function (event, instance) {
        event.preventDefault();
        const org = Orgs.current();
        showModal( "cancelProgramModal",
            {
                longName: org.getLongName(),
                chatSupportUrl: org.getChatSupportUrl(),
                currentPerson: instance.data,
                onFinalDateSelected: (reservationId, finalDate, cancelReasonId) => {
                    instance.data.onFinalDateSelectDaysModal(reservationId, finalDate, cancelReasonId);
                }
            }, "#cancelProgramModal");
    }
})

/**
 * Returns the total amount for a plan based on the number of days attending.
 * @param {object} i - The instance object.
 * @param {object} plan - The plan object.
 * @param {number} numCheckedDays - The number of checked days.
 * @param {number | undefined} secondPlanNumCheckedDays - The number of checked days for the second plan in the bundle.
 * @returns {number} - The total amount.
 */
function getTotalAmountForScaledPlans(i, plan, numCheckedDays, secondPlanNumCheckedDays) {
    if (numCheckedDays === 0 || !plan) {
        return 0;
    }

    const { bundlePlanId } = plan;
    const enrolledInBundle = RegistrationUtils.isEnrolledInBundle(i.data.selectedPersonObj, plan);

    if (enrolledInBundle) {
        const bundlePlan = RegistrationUtils.getBundle(bundlePlanId);
        const secondPlan = RegistrationUtils.getSecondPlan(i.data.selectedPersonObj, bundlePlan, plan);
        return RegistrationUtils.getBundleAmount(bundlePlan, secondPlan, numCheckedDays, secondPlanNumCheckedDays);
    } else {
        return RegistrationUtils.getOriginalAmount(plan, numCheckedDays);
    }
}

/**
 * Gets an array of selected days from checkboxes.
 * @returns {string[]} - Array of selected days.
 */
function getSelectedDays(planId) {
    return Array.from(document.querySelectorAll(`.day-checkbox[data-id="${ planId }"]:checked`)).map(checkbox => checkbox.name) || [];
}

/**
 * Handles form submit or save button click.
 */
const handleSubmit = function(event, template) {
    event.preventDefault();
    let formFieldData = $('#editPlanEnrollmentFrm').serializeArray();
    let bundledPlanData = null;
    let changeInNumberOfDays = template.changeInDays.get();
    if (template.bundlePlan.get()) {
        const index = formFieldData.findIndex(field => field.name === 'startDate');
        bundledPlanData = formFieldData.slice(index + 1);
        formFieldData = formFieldData.slice(0, index + 1);
        changeInNumberOfDays += template.bundleChangeInDays.get();
    }
    template.data.onSave(event, template, formFieldData, bundledPlanData, changeInNumberOfDays);
}

const prefillStartDate = function(plan) {
    const org = Orgs.current();
    const timezone = org?.getTimezone();
    const details = plan.planDetails;
    const availablePrograms = org?.availablePrograms();
    const selectedProgram = availablePrograms?.find(program => program._id === details?.program);
    // if enrolled in the future, start date is enrollment date
    const enrollmentDate = moment.tz(plan.enrollmentDate, timezone).startOf('day');
    const today = moment.tz(timezone).startOf('day');
    const startDate = enrollmentDate.isBefore(today) ? today : enrollmentDate;
    // if enrollment is further away than next week just use enrollment date
    const nextWeek = today.clone().add(7, 'days');
    let defaultStartDate = selectedProgram?.isRequiredAdvanceNotice
        ? (startDate.isAfter(nextWeek) ? startDate.format('MM/DD/YYYY') : startDate.add(7, 'days').format('MM/DD/YYYY'))
        : (startDate.isAfter(today) ? startDate.format('MM/DD/YYYY') : startDate.add(1, 'days').format('MM/DD/YYYY'))
    const availableTimePeriods = org?.billing.timePeriods ?? [];
    if (details && details.timePeriod) {
        const planTimePeriod = availableTimePeriods.find(tp => tp._id === details.timePeriod);
        if (planTimePeriod) {
            const timePeriodStart = moment.tz(planTimePeriod.startDate, timezone);
            // if the start date (either today or enrollment date) is before the time period start date, use the time period start date
            defaultStartDate = timePeriodStart.isBefore(startDate) ? startDate.format('MM/DD/YYYY') : timePeriodStart.format('MM/DD/YYYY');
        }
    }
    return defaultStartDate;
}

const getAvailability = function (plan, date) {
    const currentOrg = Orgs.current();
    const hasWeekends = currentOrg.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);
    Meteor.callAsync('getOrgScheduleTypesAvailabilities', currentOrg._id, date, plan.details?.timePeriod ?? null).then(result => {
        handleAvailabilityResults(result, plan, hasWeekends);
    }).catch(err => {
        console.error(err);
    });
}

const handleAvailabilityResults = function (result, plan, hasWeekends) {
    if (result) {
        const selectedDays = getSelectedDays(plan._id);
        const availabilities = result[plan.planDetails.details?.scheduleType ?? '0'] ?? EnrollmentUtils.getDefaultAvailabilities(hasWeekends)

        EnrollmentUtils.adjustAvailabilitiesBasedOnSelectedDays(selectedDays, availabilities, hasWeekends);

        const elements = document.querySelectorAll('[data-type="days"][data-id="' + plan._id + '"]');
        const numberOfDays = hasWeekends ? 7 : 5;
        const indexOffset = hasWeekends ? 0 : 1;
        for (let i = 0; i < numberOfDays; i++) {
            // If already disabled because of plan offering, don't re-enable it due to availability.
            if (!elements[i] || elements[i].disabled) {
                continue;
            }

            if (availabilities[i + indexOffset] <= 0) {
                elements[i].checked = false;
            }

            elements[i].disabled = availabilities[i + indexOffset] <= 0;
            const note = document.querySelectorAll('[data-type="unavailable-note"][data-id="' + plan._id + '"]')[i];
            if (elements[i].disabled && note) {
                note.classList.remove('d-none');
            } else if (note) {
                note.classList.add('d-none');
            }
        }
    }
}

Template.cancelProgramModal.created = function () {
    this.cancelPrograms = new ReactiveVar(true);
    this.isFormFilled = new ReactiveVar(false);
    this.cancelDate = new ReactiveVar('');
    this.cancelReason = new ReactiveVar('');
    this.cancelReasonId = new ReactiveVar('');

    // BUGS-2768 set default end date to today.
    const timezone = Orgs.current().getTimezone();
    const defaultEndDate = moment.tz(timezone).format('MM/DD/YYYY');
    this.cancelDate.set(defaultEndDate);
};
Template.cancelProgramModal.helpers({
    getContinueButton: () => {
        const template = Template.instance();
        const cancelDate = template.cancelDate.get();
        const cancelReason = template.cancelReason.get();

        template.isFormFilled.set(cancelDate && cancelReason);
        return (template.cancelPrograms.get() === true);
    },
    cancelReason: () =>{
        return Template.instance().cancelReason.get();
    },
    cancelDate: () =>{
        return Template.instance().cancelDate.get();
    },
    getPrevCancelReason: (id) =>{
        const cancelReasonId = Template.instance().cancelReasonId.get();
        return cancelReasonId === id ? 'selected' : '';
    },
    cancellationReasons: () =>{
        return Orgs.current().cancellationReasons?.filter((reason) => reason.archived === false);
    },
    continueDisabled: () => {
        const cancelReasonValue = Template.instance().cancelReason.get();
        const endDateValue = Template.instance().cancelDate.get();

        const isFormFilled = cancelReasonValue && endDateValue;
        Template.instance().isFormFilled.set(isFormFilled);

        return isFormFilled ? '' : 'disabled';
    },
    cancelProgramInitDatePicker: function () {
        const org = Orgs.current();
        const timezone = org?.getTimezone();
        const startDate = moment.tz(timezone).format('MM/DD/YYYY'); // BUGS-2768 if default is 'today', we can't make tomorrow the first selectable day.
        const datepickerInstance = Template.instance().cancelDate.get();
        setTimeout(() => {
            $('.date-picker').datepicker({
                autoclose: true,
                startDate: startDate
            });

            if (datepickerInstance) {
                const datepicker = $('input[name="cancelProgramDate"]').data('datepicker');
                datepicker.setDate(datepickerInstance);
            }
        }, 400);
    }
});
Template.cancelProgramModal.events({
    "click #continueBtn": function (event, instance) {
        event.preventDefault();
        const { cancelPrograms } = instance;
        const cancelDateVal = $("input[name='cancelProgramDate']").val();
        const cancelReasonVal = $(".cancelReason").val();

        if (!cancelDateVal) {
            mpSwal.fire('Last day is required.', '', 'error');
            return;
        }

        if (!cancelReasonVal) {
            mpSwal.fire('Cancellation reason is required.', '', 'error');
            return;
        }

        cancelPrograms.set(false);
        instance.cancelDate.set(cancelDateVal);
        instance.cancelReason.set(cancelReasonVal);
    },
    "change #cancelReasonsSelect": function (event, instance) {
        const id = $(event.target).find(":selected").attr('id');
        instance.cancelReasonId.set(id);
    },
    "click .backBtn": function (event, instance) {
        const {cancelPrograms} = instance;
        cancelPrograms.set(true);
    },
    "click .btnCancelPlan": async function (event, instance) {
        const { currentPerson } = instance.data;
        const finalDate = moment.tz(instance.cancelDate.get(), Orgs.current().getTimezone()).add(1, 'days').valueOf();
        const reservationId = currentPerson.reservation._id;
        const cancelReasonId = instance.cancelReasonId.get();

        await Meteor.callAsync('updatePlanEndDate', reservationId, currentPerson.selectedPersonObj._id, finalDate);

        instance.data.onFinalDateSelected(reservationId, finalDate, cancelReasonId);
        $("#selectDaysModal").modal('hide');
        $("#cancelProgramModal").modal('hide');
    }
});
