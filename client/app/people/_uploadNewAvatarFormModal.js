import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_uploadNewAvatarFormModal.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { hideModal } from '../main';
import { remapDownloadUrl } from '../../../mpweb';

Template._uploadNewAvatarFormModal.events({
  'change #avatarInputFile': function(event) {
    var file = $('#avatarInputFile')[0].files[0].name;
    $("#fileLabelSpan").text(file);
  },
	'click #btnAvatarSave': function(event) {
		var currentPersonId = FlowRouter.current().params._id;

		event.preventDefault();
		$("#btnAvatarSave").prop('disabled', true);

		var uploadFile = document.getElementById("avatarInputFile");
		if (uploadFile && uploadFile.files.length > 0) {
			var metaContext = {tokenId: tokenString()};

			var uploader = new Slingshot.Upload("myFileUploads", metaContext);

			uploader.send(uploadFile.files[0], function (error, downloadUrl) {
			  if (error) {
			    console.error('Error uploading', uploader.xhr.response);
			    alert (error);
			    $("#btnAvatarSave").prop('disabled', false);
			  }
			  else {
				downloadUrl = remapDownloadUrl (downloadUrl);
			  	console.log("download = " + downloadUrl);
			  	var mediaType = uploadFile.files[0].type;

				Meteor.callAsync("associateAvatarImage", currentPersonId, metaContext.tokenId).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			  	hideModal("#_uploadNewAvatarFormModal");
			  	$("#btnAvatarSave").prop('disabled', false);
			  }
			});
		}

	}
});

var tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};
