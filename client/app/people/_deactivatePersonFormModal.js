import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_deactivatePersonFormModal.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../lib/collections/people';
import { hideModal } from '../main';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';

Template._deactivatePersonFormModal.events({
	'click #btnSubmitDeactivation': function(event){
		event.preventDefault();
		var personId = FlowRouter.current().params._id;
		var deactivationReason = $("#deactivationReason").val();
		var withdrawDate = $("#withdraw-date").val();

		const options = {personId, deactivationReason, withdrawDate};
		const callDeactivatePerson = function () {

			$("#btnSubmitDeactivation").val("Deactivating").prop("disabled", true);
			Meteor.callAsync('deactivatePerson', options).then((result) => {
				if (result && result.couldDeactivateRelationships) {
					mpSwal.fire({
						title: "Deactivate related people?",
						text: "Choosing 'Yes' will deactivate the following related people:\n\n" + _.map(result.couldDeactivateRelationships, (rel) => rel.relatedPerson.firstName + " " + rel.relatedPerson.lastName + "\n"),
						icon: "warning",
						showCancelButton: true,
						cancelButtonText: "No",
						confirmButtonText: "Yes",
						closeOnConfirm:false,
					}).then(result => {
						
						if (result.value) {
							options.deactivateAssociatedRelationships = true;
							callDeactivatePerson();
						} else if (result.dismiss && result.dismiss == "cancel") {
							options.suppressSuggestedDeactivations = true;
							callDeactivatePerson();
						} else {
							$("#btnSubmitDeactivation").val("Deactivate Person").prop("disabled", false);
						}
					});
				} else if (result) {
					$("#btnSubmitDeactivation").val("Deactivate Person").prop("disabled", false);
					hideModal("#_deactivatePersonFormModal");
					mpSwal.fire("Success", "Person deactivated.", "success");
				}
			}).catch((error) => {			
				mpSwal.fire('Error', error.reason, 'error');
				$("#btnSubmitDeactivation").val("Deactivate Person").prop("disabled", false);
			});
		}
		callDeactivatePerson();
	}
});

Template._deactivatePersonFormModal.helpers({
	'deactivationReasons': function() {
		var personId = FlowRouter.current().params._id;
		var person = People.findOne(personId);
		const currentOrg = Orgs.current(), customStaffDeavtivationReasons = currentOrg && _.deep(currentOrg, "valueOverrides.deactivationReasonsStaff");
		if (person && (person.type == "staff" || person.type == "admin")) {
			return customStaffDeavtivationReasons || ["Transferred", "Resigned", "Terminated"];
		}
		
		const customDeactivationReasons = currentOrg && _.deep(currentOrg, "valueOverrides.deactivationReasons");
		return customDeactivationReasons || 
			["", "Withdrawn - Behavior", "Withdrawn - Financial", "Withdrawn - Relocation", "Withdrawn - Other Arrangements", "Never Enrolled", "Related Person Was Withdrawn"];
	},
	'showWithdrawDate'() {
		const person = Template.instance().data.person;
		return person?.type == "person";
	},
	isVendor() {
		const person = Template.instance().data.person;
		return person?.type === 'vendor';
	},
	'withdrawDate'() {
		const person = Template.instance().data.person,
			currentWithdrawDate = person?.getWithdrawDate();
		
		if (currentWithdrawDate) {
			const withdrawDateFormatted = new moment(currentWithdrawDate).format("YYYY-MM-DD");
			return withdrawDateFormatted;
		}
	}
});
