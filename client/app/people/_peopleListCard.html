<template name="_peopleListCard">
  <!--begin::Card-->
	<div data-cy="people-card" class="card card-custom clickable-row gutter-b mr-20" data-id="{{person._id}}" id="{{person._id}}-list-card">
		<div class="card-body">
			<!--begin::Top-->
			<div class="d-flex flex-column align-items-center justify-content-center">
				<!--begin::Pic-->
          
        {{#unless person.hasAvatar}}
            <div class="d-flex card-avatar-circle align-items-center justify-content-center"
                 style="background-color:{{getAvatarBackground person.personInitials}}">
                <span data-cy="initials" class="initials">{{person.personInitials}}</span>
            </div>
        {{else}}
          <div class="people-card-user-img" style="background-image:url({{person.getAvatarUrl}})"></div>
        {{/unless}}
        <div class="mt-10 align-items-center h5">
          {{person.firstName}} {{person.lastName}}
        </div>
          {{#if person.isAbsentToday}}
                <div style="max-width: 230px; text-align: center; margin-bottom: 10px">
                    <span style="word-wrap: break-word">{{personAbsentText person}}</span>
                </div>
                {{/if}}
        <div class="my-lg-0 my-1 checkin-wrapper">
          {{#if person.isCheckInAble}}
            <div class="btn {{getCheckButtonColor}} font-weight-bolder btn-text-white min-w-175px {{#unless onlyGridViewActive}}disabled{{/unless}}" data-id="{{person._id}}" id="card-checkin-button">
              <i class="fad-regular fad {{getCheckButtonIcon}} fa-swap-opacity mr-2" style="color:#fff"></i>{{getCheckButtonText}}
            </div>
            <!-- <a href="#" class="btn btn-sm btn-light-primary font-weight-bolder text-uppercase mr-2">Check-IN</a> -->
          {{/if}}
        </div>
      </div>
		</div>
	</div>
	<!--end::Card-->
</template>
