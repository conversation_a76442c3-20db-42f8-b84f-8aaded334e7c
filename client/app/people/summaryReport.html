<template name="summaryReport">

	<div style="margin:0 auto;max-width:511px;width:511px;overflow:hidden;background-color:#eeeeee;">
		<div style="width:100%;background-color:var(--primary);text-align:center;padding-top:10px;padding-bottom:10px">
			<img src="/img/momentpath.png" border=0>
		</div>
		<div style="padding-left:10px;padding-right:10px;overflow:hidden">
			<h2 style="font-size:20px">Summary Report for {{reporteeName}}</h2>
			{{#each sections}}
				<div style="background-color:#fff;border:1px solid #dddddd;margin-bottom:5px">
					<h4 style="font-size:14px;text-transform:uppercase;color;color:#666;padding-left:10px">{{prettyDate}}</h4>
					<hr noshade style="margin: 0px 5px 0px 5px"/>
					{{#each availableSections}}
						<h3 style="font-size:18px;color:var(--primary);margin-left:25px;margin-right:10px;text-transform: capitalize;"><i class="fad fad-primary {{iconForType this}}" aria-hidden="true" style="padding-right:5px"></i>{{this}}</h3>
						<div style="margin-left:50px;margin-right:10px">
						{{#each momentsForSection this}}
							<div style="border-left: 2px solid var(--primary);padding-left:5px">
							{{> Template.dynamic template=momentSummaryDisplayTemplate}}
							</div>
						{{/each}}
						</div>
						<hr noshade style="margin: 0px 5px 0px 5px"/>
					{{/each}}
				</div>
			{{/each}}
		</div>
	</div>
</template>

<template name="commentSummaryDisplayTemplate">

	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>
	{{comment}}
	</p>
</template>

<template name="foodSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>
		{{#if foodType}}<strong>{{foodType}}:</strong>{{/if}} <em>{{formatTimeUppercase time}}</em> {{foodAmountDescription}}{{#if foodAmountPercent}} - {{foodAmountPercent}}{{/if}} {{comment}}
	</p>
</template>

<template name="pottySummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>
		<strong>{{#if pottyTypeContinence}}{{pottyTypeContinence}} {{/if}}{{printWithColon pottyType}}</strong> {{formatTimeUppercase time}} {{#if comment}}- {{comment}}{{/if}}
	</p>
</template>

<template name="sleepSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>{{formatTimeUppercase time}} - slept for {{durationDescription}} - {{comment}}</p>
</template>

<template name="moodSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>{{formatTimeUppercase time}} <br/>{{#if moodLevel}}<img src="{{appUrl}}img/mood-icon-{{moodIcon}}-o.png"> {{/if}} {{#if comment }}- {{comment}} {{/if}}</p>
</template>

<template name="checkoutSummaryDisplayTemplate">
	<p>{{formatUnixTime sortStamp}} <br/>{{#if mood}}<img src="{{appUrl}}img/mood-icon-{{moodIcon}}-o.png"><br/> {{/if}} {{#if checkOutTransportation}}<br/><b>Transportation:</b> {{checkOutTransportation}}{{/if}} {{comment}}</p>	
</template>

<template name="learningSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>{{formatTimeUppercase time}} <br/>{{#if learningType}}Type: {{learningType}}<br/> {{/if}} {{comment}}</p>
</template>

<template name="incidentSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>{{formatTimeUppercase time}} <br/>{{#if incidentNature}}Nature: {{incidentNature}}<br/>{{/if}}
                                        {{#if incidentLocation}}Location: {{incidentLocation}}<br/>{{/if}}
                                        {{#if incidentActionTaken}}Action Taken: {{incidentActionTaken}}<br/>{{/if}} {{comment}}</p>
</template>

<template name="illnessSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>{{formatTimeUppercase time}} <br/>Symptoms: {{illnessSymptomDisplayString}}<br/> {{comment}}</p>
</template>

<template name="ouchSummaryDisplayTemplate">
	{{#each attachedMedia}}
	  <a href="{{mediaRedirectorPath}}">
	  <img src="{{getTimelinePhoto}}" width="" valign="top" class="email_image" style="margin-bottom: 10px;margin: 5px;padding: 0;">
	  </a>
	{{/each}}
	<p>{{formatTimeUppercase time}} <br/>
			{{#if ouchDescription}}Injury/Accident Description: {{ouchDescription}}<br/>{{/if}}
            {{#if ouchCare}}Care Provided: {{ouchCare}}<br/>{{/if}}
            {{#if ouchCalledParent}}Parent Called {{#if ouchCalledParentTime}}at {{ouchCalledParentTime}}{{/if}}<br/>{{/if}}
            {{#if ouchCalledDoctor}}Doctor Called {{#if ouchCalledDoctorTime}}at {{ouchCalledDoctorTime}}{{/if}}<br/>{{/if}}
            {{#if ouchNurseNotified}}Nurse Notified<br/>{{/if}}
            {{#if ouchProfessionalMedication}}Professional Medication Provided<br/>{{/if}} {{comment}}</p>
</template>
