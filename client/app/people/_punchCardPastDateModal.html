<template name="punchCardPastDateModal">
    <div id="punchCardPastDateModal" class="modal fade">
        <div class="modal-dialog modal-dialog-scrollable modal-lg" >
            <div class="modal-content" style="height:auto;min-height: 100%;">
                <div class="modal-header">
                    <div>
                        <h5 class="modal-title" id="exampleModalLabel">Add past date</h5>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                            <span class="fad-regular fad-primary fad fa-times"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-body bg-white pb-6">
                    <div class="form-group row">
                        <label class="col-xl-3 col-lg-3 text-right col-form-label">Past Date</label>
                        <div class="col-lg-9 col-xl-6">
                            <input class="form-control validate-input" type="text" id="pastDatePicker" value="{{dateFormatted}}" required/>
                        </div>
                    </div>
                    {{#if scheduleTypes}}
                    <div class="form-group row">
                        <label class="col-xl-3 col-lg-3 text-right col-form-label">Schedule Type(s)</label>
                        <div class="col-lg-9 col-xl-6">
                            <select name="scheduleTypes" class="form-control validate-input" multiple required>
                                {{#each scheduleTypes}}
                                <option value="{{_id}}">{{type}}</option>
                                {{/each}}
                            </select>
                        </div>
                    </div>
                    {{/if}}
                    {{#each type in chosenScheduleTypes}}
                    <div style="border: 1px solid var(--primary); border-radius: 5px; padding: 5px" class="mb-2">
                        <h5 class="m-1">{{type.type}}</h5>
                        <div class="form-group row">
                            <label class="col-xl-3 col-lg-3 text-right col-form-label">Check In Time</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="time" class="form-control form-control-lg validate-input" name="timePickerStart" required data-id="{{type._id}}">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-xl-3 col-lg-3 text-right col-form-label">Check Out Time</label>
                            <div class="col-lg-9 col-xl-6">
                                <input type="time" class="form-control form-control-lg validate-input" name="timePickerEnd" required data-id="{{type._id}}">
                            </div>
                        </div>
                    </div>
                    {{/each}}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary font-weight-bolder mr-2 createPastDate" id="createPastDate" data-stayopen="false">Save</button>
                    <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</template>
