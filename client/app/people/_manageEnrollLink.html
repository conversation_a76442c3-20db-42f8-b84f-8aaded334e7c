<template name="_manageEnrollLink">
  <div id="_manageEnrollLink" class="modal fade">
  	<div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg" >
  		<div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Manage Enroll Link</h5>
              <div class="d-flex align-items-center">
                <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                  <span class="fad-regular fad-primary fad fa-times"></span>
                </div>
              </div>
            </div>
            <div class="modal-body bg-white">
                <div class="container d-flex flex-row justify-content-center mb-4">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>&nbsp;</th>
                                <th>This Record</th>
                                <th>Linked Enroll Record</th>
                            </tr>
                        </thead>
                        <tr>
                            <td>Record ID</td>
                            <td>{{ manageRecord._id }}</td>
                            <td>
                                {{ enrollRecord.id }}
                                &nbsp;&nbsp;&nbsp;
                                <i id="melSearch" class="fa fa-search cursor-pointer"></i>
                                {{#if showDelete }}
                                    &nbsp;&nbsp;&nbsp;
                                    <i id="melDelete" class="fa fa-trash cursor-pointer"></i>
                                {{/if}}
                            </td>
                        </tr>
                        <tr>
                            <td>First Name</td>
                            <td>{{ manageRecord.firstName }}</td>
                            <td>{{ enrollRecord.firstName }}</td>
                        </tr>
                        <tr>
                            <td>Last Name</td>
                            <td>{{ manageRecord.lastName }}</td>
                            <td>{{ enrollRecord.lastName }}</td>
                        </tr>
                        {{#if isChild }}
                            <tr>
                                <td>Birthday</td>
                                <td>{{ manageRecord.birthday }}</td>
                                <td>{{ enrollRecord.birthday }}</td>
                            </tr>
                        {{/if}}
                        {{#if isFamily }}
                            <tr>
                                <td>Email</td>
                                <td>{{ manageRecord.email }}</td>
                                <td>{{ enrollRecord.email }}</td>
                            </tr>
                            <tr>
                                <td>Phone</td>
                                <td>{{ manageRecord.phone }}</td>
                                <td>{{ enrollRecord.phone }}</td>
                            </tr>
                        {{/if}}
                        <tr>
                            <td>Enroll Documents</td>
                            <td>n/a</td>
                            <td>{{enrollRecord.docCount}}</td>
                        </tr>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                {{# if differentRecord }}
                    <div class="row text-danger font-weight-bold">
                        <div class="col-8 offset-2 border border-dark px-2 py-2">
                            Warning--you are about to replace the previous link with this new link and if there are any
                            differences in the data, the Enroll data will be replaced with the data from Manage. Do you
                            want to proceed? | (If so, consider updating links on other family members.)
                        </div>
                    </div>
                {{/if}}
            </div>
            <div class="modal-footer d.flex justify-content-center align-content-center">
                {{#if differentRecord }}
                    <button type="button" class="btn btn-primary font-weight-bolder mr-2" id="btnSubmitMel" disabled="{{cantSave}}">
                        Confirm
                    </button>
                {{else}}
                    <button type="button" data-dismiss="modal" class="btn btn-primary font-weight-bolder mr-2" disabled="{{cantSave}}">
                        Save
                    </button>
                {{/if}}
                <div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Cancel</div>
            </div>
  		</div>
  	</div>
  </div>
</template>
