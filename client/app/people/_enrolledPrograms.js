import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_enrolledPrograms.html';
import moment from 'moment-timezone';
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import { BillingUtils } from '../../../lib/util/billingUtils';
import { cloneDeep } from 'lodash';
import { EnrollmentUtils } from '../../../lib/util/enrollmentUtils';
import { Orgs } from '../../../lib/collections/orgs';
import './_reservationCard';

Template._enrolledPrograms.onCreated(async function() {
    const self = this;
    this.enrolledPrograms = new ReactiveVar([]);
    this.enrolledSelectiveWeekPlans = new ReactiveVar([]);
    this.enrolledItems = new ReactiveVar([]);
    
    const { _id, defaultGroupId } = this.data;

    const org = Orgs.current();
    const timezone = org.getTimezone();
    const todayStamp = moment().tz(timezone).startOf("day").valueOf();
    
    const enrolledPlans = this.data.enabledBillingPlans(todayStamp);
    await Meteor.callAsync("getEnrolledPrograms", _id, defaultGroupId).then((result) => {        
        const {reservations, selectiveWeekPlans} = result
        this.enrolledPrograms.set(reservations.filter(reservations => !reservations.isSelectiveWeeks && reservations.program));
        this.enrolledSelectiveWeekPlans.set(selectiveWeekPlans);
    })
    
    const items = this.data.billing?.enrolledItems || []; // The enrolledItems contains ids that we can use to get the reservations
    this.onHoldPrograms = new ReactiveVar([]);
    
    await Meteor.callAsync("getOnHoldRegistrationsWithChild", Orgs.current()._id, _id).then(async registrations => {
        if (registrations) {
            const onHoldPrograms = EnrollmentUtils.buildOnHoldPrograms(registrations, _id);
            const filterOnHoldPrograms = EnrollmentUtils.filterOnHoldPrograms(onHoldPrograms, enrolledPlans)
            const setOnHoldPrograms = [];
            for(const p of filterOnHoldPrograms) {
                const mapPlanAsReservation = await EnrollmentUtils.mapPlanAsReservation(p, _id, self.data.defaultGroupId);
                setOnHoldPrograms.push(mapPlanAsReservation);
            };
            self.onHoldPrograms.set(setOnHoldPrograms.flat());

            
            setTimeout(() => {
                $(function () {
                    $('[data-toggle="tooltip"]').tooltip()
                })
            }, 500);
        }
    });

    Meteor.callAsync("getNonRecurringSchedulesForChild", _id, org).then(async nonRecurringReservations => {
        if (nonRecurringReservations) {
            const activeItems = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations); // This should filter out items that have all passed and apply strike through to those that have passed but not all dates have passed
            self.enrolledItems.set(activeItems);
        }
    })
});

Template._enrolledPrograms.helpers({
    reservations() {
        const reservations = Template.instance().enrolledPrograms.get();
        const onHoldPrograms = Template.instance().onHoldPrograms.get().filter(onHoldProgram => !onHoldProgram.isSelectiveWeeks);
        // I think? we only want program related reservations. Also, split out selective weeks reservations because they are handled in selectiveWeekReservations
        return reservations.concat(onHoldPrograms);
    },
    selectiveWeekReservations() {
        const onHoldGroups = Template.instance().onHoldPrograms.get().filter(onHoldProgram => onHoldProgram.isSelectiveWeeks);
        const groupedReservations = Template.instance().enrolledSelectiveWeekPlans.get();
        const combinedArray = groupedReservations.concat(onHoldGroups);
        const org = Orgs.current();
        return combinedArray.filter(groupReservation => org.billing.plansAndItems.find(plan => plan._id === groupReservation.planId)?.program !== undefined);
    },
    hasResults() {
        const reservations = Template.instance().enrolledPrograms.get();
        const onHoldReservations = Template.instance().onHoldPrograms.get().filter(onHoldProgram => onHoldProgram.isSelectiveWeeks);
        const selectiveWeekReservations = Template.instance().enrolledSelectiveWeekPlans.get();
        const items = Template.instance().enrolledItems.get();
        return reservations.length + selectiveWeekReservations.length + onHoldReservations.length + items.length > 0;
    },
    parentData() {
        return Template.instance().data;
    },
    enrolledPrograms() {
        return Template.instance().enrolledPrograms;
    },
    enrolledSelectiveWeekPlans() {
        return Template.instance().enrolledSelectiveWeekPlans;
    },
    enrolledItems() {
        return Template.instance().enrolledItems.get();
    },
    sortedProgramsAndItems() {
        const instance = Template.instance();
        const reservations = instance.enrolledPrograms.get();
        const onHoldPrograms = instance.onHoldPrograms.get().filter(onHoldProgram => !onHoldProgram.isSelectiveWeeks);
        const items = instance.enrolledItems.get();
        
        // Convert items to a compatible format for sorting
        const formattedItems = items.filter(item => item.schedules.length > 0)
        .map(item => {
            // Find the earliest non-striked date, or use the earliest date if all are striked
            const earliestSchedule = item.schedules
            .sort((a, b) => a.scheduledDate - b.scheduledDate)[0];
            
            return {
                _id: item._id,
                isItem: true,
                originalItem: item.originalItem,
                itemData: item,
                schedules: item.schedules,
                scheduledDate: earliestSchedule.scheduledDate
            };
        });
        
        // Combine all programs and items
        const combined = [...reservations, ...onHoldPrograms, ...formattedItems];
        
        // Sort by start date, with plans before items if dates are the same
        return combined.sort((a, b) => {
            const aDate = a.isItem ? a.scheduledDate : (a.scheduledDate || 0);
            const bDate = b.isItem ? b.scheduledDate : (b.scheduledDate || 0);
            
            if (aDate === bDate) {
                // If dates are equal, place plans before items
                if (!a.isItem && b.isItem) {
                    return -1;
                }
                if (a.isItem && !b.isItem) {
                    return 1;
                }
            }
            
            return aDate - bDate;
        })
    }
})
