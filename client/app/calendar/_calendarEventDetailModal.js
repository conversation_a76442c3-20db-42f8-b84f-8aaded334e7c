import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from 'moment-timezone';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import _ from '../../../lib/util/underscore';
import './_calendarEventDetailModal.html';
import '../reservations/reservationFormModal';
import '../reservations/reservationCancelFormModal';
import '../moments/partials/_checkinFormModal';
import '../moments/partials/_checkoutFormModal';
import { hideModal, showModal } from '../main';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { Log } from '../../../lib/util/log';

Template._calendarEventDetailModal.onCreated(function () {
	this.autorun(() => {
		const personId = Session.get("eventDetailModalData").eventDataId;

		if (!personId) {
			Session.set('calendarPersonDataObj', null);
			return;
		}

		Meteor.callAsync("getPeopleById", { _id: personId }).then((person) => {
			if (person) {
				Session.set('calendarPersonDataObj', person);
			}
		}).catch((err) => {
			Log.info('Error fetching person by ID:', err);
		});
	})
})

Template._calendarEventDetailModal.helpers({
	"eventData": function() {
		return Session.get("eventDetailModalData");
	},
	"showGoToButton": function() {
		const currentPerson = Meteor.user()?.fetchPerson();
		if (!currentPerson) {
			return;
		}
		const eventData = Session.get("eventDetailModalData");
		const eventIsHoliday = eventData.eventType === "holiday"; // Nowhere to go for holidays
		return !eventIsHoliday && [USER_TYPES.ADMIN, USER_TYPES.STAFF].includes(currentPerson.type);
	},
	"showCheckInButton": function() {
		const person = Session.get('calendarPersonDataObj');
		if (person) {
			return (Session.get("eventDetailModalData").scheduledFor == moment().format("MM/DD/YYYY") && Session.get("eventDetailModalData").eventType == "reservationSlot" && person && !person.checkedIn);
		}
		return false;
	},
	"showEditButton": function() {
		return (Session.get("eventDetailModalData").eventType == "reservationSlot");
	},
	"showCancelButton": function() {
		return (Session.get("eventDetailModalData").eventType == "reservationSlot");
	},
	"showCheckOutButton": function() {
		const person = Session.get('calendarPersonDataObj');
		if (person) {
			return (Session.get("eventDetailModalData").scheduledFor == moment().format("MM/DD/YYYY") && Session.get("eventDetailModalData").eventType == "reservationSlot" && person && person.checkedIn);
		}
		return false;
	},
	"showAttachments": function() {
		var attachments = Session.get("eventDetailModalData").eventAttachments;
		return attachments && attachments.length > 0;
	}
});
Template._calendarEventDetailModal.events({
	"click #gotoDetail": function(event) {
    	hideModal("#_calendarEventDetailModal");
		var gotoTarget = $(event.target).data("target");

		if (gotoTarget == "scheduling") {
			const dest = "/people/" + Session.get("eventDetailModalData").eventDataId + "#reservations";
			console.log("dest", dest);
			FlowRouter.go( dest );
		} else if (gotoTarget == "activities" && Session.get("eventDetailModalData").eventSubType == "theme") {
			FlowRouter.go("activities.theme.show", {_id: Session.get("eventDetailModalData").eventItemId });
		} else if (gotoTarget == "activities") {
			FlowRouter.go("activities.show", {_id: Session.get("eventDetailModalData").eventItemId });
		} else if (gotoTarget == "food") {
			FlowRouter.go("food.show", {_id: Session.get("eventDetailModalData").eventItemId });
		} else if (_.contains(["announcements"], gotoTarget)) {
			FlowRouter.go(gotoTarget, {_id: Session.get("eventDetailModalData").eventItemId });
		} else 
			FlowRouter.go(gotoTarget);
	},
	"click #checkIn": function(event) {
    	hideModal("#_calendarEventDetailModal");
		var personId = Session.get("eventDetailModalData").eventDataId;
		if (personId) {
			getPeopleById({_id: personId}).then((person) => {
				if (person) {
					Session.set("currentGroupId", person.defaultGroupId);
					showModal("_checkinFormModal", { personId }, "#_checkinFormModal");
				}
			}).catch(err => {
				console.log(err);
			});
		}
	},
	"click #checkOut": function(event) {
    	hideModal("#_calendarEventDetailModal");
		var personId = Session.get("eventDetailModalData").eventDataId;
		if (personId) {
			getPeopleById({_id: personId}).then((person) => {
				if (person) {
					showModal("_checkoutFormModal", { personId }, "#_checkoutFormModal");
				}
			}).catch(err => {
				console.log(err);
			});
		}
	},
	"click #cancelReservation": function(event) {
    	hideModal("#_calendarEventDetailModal");
		var reservationId = Session.get("eventDetailModalData").eventItemId;
	
		const params = {reservationId: reservationId, reservationInstanceDate: Session.get("eventDetailModalData").eventItemDate};
		showModal("reservationCancelFormModal", params, "#reservationCancelFormModal");
	},
	"click #edit": function(event) {
		hideModal("#_calendarEventDetailModal");
		var reservationId = Session.get("eventDetailModalData").eventItemId;
	
		showModal("reservationFormModal", {reservationId}, "#reservationFormModal");
	}
});

function getPeopleById(query) {
	return new Promise((resolve, reject) => {
		Meteor.callAsync("getPeopleById", query).then((res) => {
			resolve(res);
		}).catch((err) => {
			reject('error in get people: ', err);
		});
	})
	
}