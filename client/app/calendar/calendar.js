import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs.js';
import './calendar.html';
import './header/_calendarHeader';
import './views/_calendarDayView.html';
import './views/_calendarWeekView.html';
import './views/_calendarMonthView.html';
import './views/calendarViewHelpers.js';
import './event/_calendarEventItem';
import '../calendar/_calendarEventDetailModal.js';
import { CalendarService } from './calendarService';
import { getShowSections } from '../../../lib/calendar/calendarUtils';
import { CALENDAR_VIEWS } from '../../../lib/calendar/calendarConstants';
import { Log } from '../../../lib/util/log';
import { ReactiveVar } from 'meteor/reactive-var';

// Global reference to the current calendar service
window.CurrentCalendarService = new ReactiveVar(null);

Template.calendar.onCreated(function () {
    const timezone = Orgs.current().getTimezone();
    const currentUser = Meteor.user();
    const currentPerson = currentUser?.fetchPerson();
    const filterOptions = JSON.parse(JSON.stringify(currentUser?.uiOptions?.calendarFilters || {}));
    const showSections = getShowSections(this.data || {});

    this.calendarService = new CalendarService({
        timezone,
        showSections,
        filterOptions,
        currentPerson,
        data: this.data || {}
    });

    // Set the global reference
    window.CurrentCalendarService.set(this.calendarService);

    this.calendarService.recalculateRange(0);

    this.autorun(() => {
        this.calendarService.loadData();
    });

    this.autorun(() => {
        if (this.subscriptionsReady()) {
            (async () => {
                try {
                    await this.calendarService.getPeopleForReservations();
                } catch (error) {
                    mpSwal.fire({
                        icon: 'error',
                        title: 'Error loading people data',
                        text: error.reason || error.message || 'An unexpected error occurred while loading people data: ' + error,
                    });
                }
            })();

            this.calendarService.initDatepicker("btnCalendar");
        }
    });
});

// Clean up on destroy
Template.calendar.onDestroyed(function () {
    window.CurrentCalendarService.set(null);
});

Template.calendar.rendered = function () {
    const calendarHeader = {
        left: null,
        center: 'title',
        right: null
    };

    if (FlowRouter.current().route.name !== "calendar/print") {
        calendarHeader.left = 'prev,next today';
        calendarHeader.right = 'month,basicWeek,basicDay';
    }
};

Template.calendar.helpers({
    viewStyle() {
        const selectedStyle = Template.instance().calendarService.viewStyle.get();
        switch (selectedStyle) {
            case CALENDAR_VIEWS.DAY:
                return {style: CALENDAR_VIEWS.DAY, layoutStyle: "list"};
            case CALENDAR_VIEWS.THREE_DAY:
            case CALENDAR_VIEWS.WEEK:
                return {style: CALENDAR_VIEWS.WEEK, layoutStyle: "week"};
            default:
                return {style: CALENDAR_VIEWS.MONTH, layoutStyle: "grid"};
        }
    },
    isPrintMode() {
        return FlowRouter.current().queryParams['view'] === 'print';
    },
    pageTitle() {
        switch (FlowRouter.getQueryParam('showOnly')) {
            case 'food':
                return 'Weekly Menu';
            case 'activities':
                return 'Weekly Lesson Plans';
            default:
                return '';
        }
    },
    whiteLabelLogoOverride() {
        const host = window.location.host.split(".")[0];
        const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
        if (enabledSites && enabledSites.indexOf(host) > -1) {
            return {
                logo: Meteor.settings.public.whitelabel[host].header_logo
            };
        }
    },
    showLoaderPeopleList() {
        return Template.instance().calendarService.allReservationsPeopleLoader.get()
    },
    isReadyToShow() {
        return !Template.instance().calendarService.showContent.get();
    }
});

Template.calendar.events({
    "click #btnPrintMenu": function (event, instance) {
        event.preventDefault();
        const rangeStart = instance.calendarService.rangeStart.get();
        const rangeEnd = instance.calendarService.rangeEnd.get();
        const selectedGroup = instance.calendarService.selectedGroup.get();

        window.open(`/calendar?view=print&showOnly=food&viewStyle=week&rangeStart=${rangeStart}&rangeEnd=${rangeEnd}&group=${selectedGroup}`, '', 'left=0,top=0,width=1250,height=900,toolbar=0,scrollbars=0,status=0');
    },

    "click #btnPrintLessons": function (event, instance) {
        event.preventDefault();
        const rangeStart = instance.calendarService.rangeStart.get();
        const rangeEnd = instance.calendarService.rangeEnd.get();
        const selectedGroup = instance.calendarService.selectedGroup.get();

        window.open(`/calendar?view=print&showOnly=activities&viewStyle=week&rangeStart=${rangeStart}&rangeEnd=${rangeEnd}&group=${selectedGroup}`, '', 'left=0,top=0,width=1250,height=900,toolbar=0,scrollbars=0,status=0');
    },

    "click #btnPrintLessonsSummary": function (event, instance) {
        event.preventDefault();
        const service = instance.calendarService;
        const org = Orgs.current();
        if (!org) {
            return;
        }
        const options = {
            org,
            selectedGroup: service.selectedGroup.get() || null,
            periodStart: service.rangeStart.get(),
            periodEnd: service.rangeEnd.get()
        };

        Meteor.callAsync('generateLessonPlansSummaryHtml', options).then((res) => {
            if (res) {
                const wnd = window.open("Lesson Plan - Summary", "", "_blank");
                wnd.document.write(res);
                wnd.document.close();
                wnd.focus();
                wnd.onload = function () {
                    wnd.print();
                };
            }
        }).catch((err) => {
            Log.error(err);
        });
    }
});
