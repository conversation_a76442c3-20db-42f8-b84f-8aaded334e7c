<template name="announcementOrgsField">
	{{#if availableOrgs}}
	{{> announcementOrgsFieldDropdown selectedOrgs=selectedOrgOptions orgs=availableOrgs }}
	{{else if isLoading}}
	<i>Loading...</i>
	{{else}}
	Current org only
	{{/if}}
</template>

<template name="announcementOrgsFieldDropdown">
	<select data-cy="announcement-orgs" name='announcementOrgs' id='announcementOrgs' multiple class="form-control announcement-orgs">
		{{#each orgs}}
			<option value="{{_id}}" {{selectedIfContains selectedOrgOptions _id }}>{{name}}</option>
		{{/each}}
	</select>
</template>
