<template name="announcementFormModal">
  <div id="announcementFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-xl" style="width: 100%;height: 100%;">
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Announcement</h5>
          <div class="d-flex align-items-center">
            <div data-cy="close-announcement-modal" class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-gray-100">
          {{#with announcement}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Title</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <input data-cy="announcement-title-input" type="text" class="form-control" id="inputHeadline" placeholder="Title" value="{{headline}}">
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <textarea data-cy="announcement-description-input" class="form-control" rows="3" placeholder="Enter detailed description" id="inputMessage">{{message}}</textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Start Date</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <input data-cy="announcement-start-date-input" class="form-control" type="text" id="announcementStartDate" value="{{formattedAnnouncementStartDate}}">
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">End Date</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <input data-cy="announcement-end-date-input" class="form-control" type="text" id="announcementEndDate" value="{{formattedAnnouncementEndDate}}">
              </div>
            </div>
            {{#if showOrgSelection}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Send to Org(s):</label>
                <div class="col-lg-6 col-md-9 col-sm-12">
                  {{> announcementOrgsField orgIds=selectedOrgs }}
                </div>
              </div>
            {{/if}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right">Audience</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <div class="radio-list">
                  <label class="radio radio-primary">
                    <input data-cy="audience-all" type="radio" name="sendTo" value="allGroups" checked={{checkedIfGroups "empty"}}>
                    <span></span>
                    All
                  </label>
                  <label class="radio radio-primary">
                    <input data-cy="audience-selected-roles" type="radio" name="sendTo" value="selectRoles" id='audience-role' checked={{checkedIfGroups "roles"}} >
                    <span></span>
                    Selected Roles
                  </label>
                  <div class="form-group row checkbox-inline announcement-roles-group ml-10">
                    <label class="checkbox">
                        <input data-cy="all-admins" type="checkbox" value="admin" id='roleAdmin' checked={{checkedIfRole "admin"}}>
                        <span></span>
                        All Admins
                    </label>
                    <label class="checkbox">
                        <input data-cy="all-staffs" type="checkbox"  value="staff" id='roleStaff' checked={{checkedIfRole "staff"}}>
                        <span></span>
                        All Staff
                    </label>
                    <label class="checkbox">
                        <input data-cy="all-families" type="checkbox"  value="family" id='roleFamily' checked={{checkedIfRole "family"}}>
                        <span></span>
                        All Families
                    </label>
                  </div>
                  {{#if showGroupSelection}}
                      <label class="radio radio-primary">
                        <input data-cy="selected-groups" type="radio" name="sendTo" value="selectGroups" id='audience-group' checked={{checkedIfGroups "groups"}} >
                        <span></span>
                        Selected Groups
                      </label>
                      <div class="form-group row ml-10">
                        <div class="col-lg-12 col-md-9 col-sm-12">
                          <select data-cy="select-groups-input" multiple class="form-control" id="selectedGroups">
                            {{#each groups}}
                              <option value="{{_id}}">{{name}}</option>
                            {{/each}}
                          </select>
                        </div>
                      </div>
                  {{/if}}
                </div>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right">Reminders</label>
              <div class="col-lg-6 col-md-9 col-sm-12">
                <div data-cy="add-reminder-btn" id="btnAddAnnouncementReminder" class="btn btn-primary font-weight-bolder ml-4 mb-6"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add Reminder</div>
                <div class="row ml-4">
                  <form id="frmAddAnnouncementReminder" style="display:none" class="row mb-6">
                    <div class="row col-md-12">
                      <div class="col-md-6">
                        <label>Date:</label> 
                        <input data-cy="reminder-date-input" type="text" class="form-control" id="frmAddAnnouncementReminder-Date" required>
                      </div>
                      <div class="col-md-6">
                        <label>Time:</label>
                        <input data-cy="reminder-time-input" type="time" class="form-control" id="frmAddAnnouncementReminder-Time" required>
                      </div>
                      <div class="col-md-6 pt-8">
                        <label>Channels:</label>
                        <div class="checkbox-list">
                          <label class="checkbox checkbox-primary">
                            <input type="checkbox" id="frmAddAnnouncementReminder-Email" value="email" checked="checked">
                            <span></span>
                            Email
                          </label>
                          <label class="checkbox checkbox-primary">
                            <input type="checkbox" id="frmAddAnnouncementReminder-Push" value="push">
                            <span></span>
                            Push
                          </label>
                        </div>
                      </div>
                      <div class="col-md-6 pt-8">
                        <div data-cy="set-reminder-btn" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveAnnouncementReminder">Set</div>
                        <div data-cy="cancel-reminder-btn" class="btn btn-secondary font-weight-bolder" id="btnCancelAnnouncementReminder">Cancel</div>
                      </div>
                    </div>
                  </form>
                </div>
                {{#if announcementReminders}}
                  <div class="row col-md-12">
                    <table class="table">
                      <tr>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Channels</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                      {{#each r in announcementReminders}}
                        <tr>
                          <td data-cy="reminder-date">{{r.date}}</td>
                          <td data-cy="reminder-time">{{r.time}}</td>
                          <td data-cy="reminder-channels">{{formatChannels r}}</td>
                          <td data-cy="reminder-status">{{r.status}}</td>
                          <td data-cy="delete-reminder">{{#if r._id}}<span class="btnDeleteAnnouncementReminder text-primary" style="cursor:pointer;" data-id="{{r._id}}">Delete</span>{{/if}}</td>
                        </tr>
                      {{/each}}
                    </table>
                  </div>
                {{/if}}
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
              <div class="col-lg-9 col-xl-6">
                <label id="announcementInputLabel" for="announcementInputFile" class="btn btn-primary font-weight-bolder">
                  <input type="file" id="announcementInputFile" style="display:none;"> 
                  <i class="fad fa-cloud-upload"></i> Attach Media/File
                </label>
                <span id="fileLabelSpan"></span>
                {{#if uploadProgress}}     
                  <span>Upload progress: {{uploadProgress}}%</span>
                {{/if}}
              </div>
            </div>
            {{#if mediaFiles}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Current Attachments</label>
                <div class="col-lg-6 col-md-9 col-sm-12">
                  <ul class="announcement-media-files">
                  {{#each mediaFiles}}
                    <li class="mb-6"> <a href="#" onclick="window.open('{{attachmentHandler mediaUrl}}', '_blank');" ><img class="max-h-100px max-w-100px" src="{{previewFile}}"> </a>
                      <a href="#" onclick="window.open('{{attachmentHandler mediaUrl}}', '_blank');" >{{mediaName}}</a> <br/>
                      <!--<a href="#" class="remove-attachment" data-id="mediaName"> Remove</a>-->
                    </li>
                  {{/each}}
                  </ul>
                </div>
              </div>
            {{/if}}
          {{/with}}
        </div>
        <div class="modal-footer">
          <button data-cy="save-announcement-btn" type="button" class="btn btn-primary font-weight-bolder mr-2" id="announcementFormSave">Save</button>
          <button data-cy="cancel-announcement-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
