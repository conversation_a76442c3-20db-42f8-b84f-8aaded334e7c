import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Announcements } from '../../../lib/collections/announcements';
import _ from '../../../lib/util/underscore';
import { Session } from 'meteor/session';
import './announcementFormModal.html';
import './_announcementOrgsField';
import { hideModal } from "../main";
import { remapDownloadUrl } from '../../../mpweb';
import $ from 'jquery';

var fileUploader = new ReactiveVar();

const moment = require('moment-timezone');

Template.announcementFormModal.events({
	'click #announcementFormSave': function(event, instance) {
		event.preventDefault();
		$("#announcementFormSave").prop('disabled', true);

		var announcementData = {};
		announcementData.headline = $("#inputHeadline").val();
		announcementData.message = $("#inputMessage").val();

		announcementData.scheduledDate = $("#announcementStartDate").val();
		announcementData.scheduledEndDate = $("#announcementEndDate").val();
	    announcementData.selectedGroups = null;
    	announcementData.selectedRoles = null;

		const audienceType = $("input[name='sendTo']:checked").val();

		if ( audienceType === "selectGroups") {
			announcementData.selectedGroups = [];
			$("#selectedGroups option:selected").each(function(i, selected) {
				announcementData.selectedGroups.push($(selected).val());
			});
		} else if (audienceType === "selectRoles") {
			announcementData.selectedRoles = [];
			$('.announcement-roles-group input:checked').each(function() {
				announcementData.selectedRoles.push( this.value);
			});
		} else {
			announcementData.selectedGroups = [];
		}
		
		announcementData.selectedOrgs = $("#announcementOrgs").val() || [];
		

		announcementData.newMediaFiles = instance.newFiles.get();
		announcementData.reminders = instance.reminders.get();


		$("#announcementFormSave").prop('disabled', false);

		const announcementId =  Template.instance().data && Template.instance().data.announcementId;
		if (announcementId) {
			announcementData.announcementId = announcementId;
			Meteor.callAsync('updateAnnouncement', announcementData).then((result) => {
				$("#announcementFormSave").prop('disabled', false);
				if (result) {
					const text = "<strong>The following reminders are scheduled for times that are in the past in that Org's timezone, as a result no reminder will be sent for them:</strong>";
					const orgText = result.map(r => {
						const formattedDateTime = new moment(`${r.reminderDateTime}`, "YYYY-MM-DD hh:mm").format("MMM Do h:mma");
						return `${r.orgName} - ${formattedDateTime} <br>`;
					}).join(' ');
					mpSwal.fire({
						title: "Attention",
						html: `${text} <br> ${orgText}`
					});
				}
				hideModal("#announcementFormModal");
			}).catch((error) => {
				$("#announcementFormSave").prop('disabled', false);
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			Meteor.callAsync('insertAnnouncement', announcementData).then((result) => {
				$("#announcementFormSave").prop('disabled', false);
				if (result) {
					const text = "<strong>The following reminders are scheduled for times that are in the past in that Org's timezone, as a result no reminder will be sent for them:</strong>";
					const orgText = result.map(r => {
						const formattedDateTime = new moment(`${r.reminderDateTime}`, "YYYY-MM-DD hh:mm").format("MMM Do h:mma");
						return `${r.orgName} - ${formattedDateTime} <br>`;
					}).join(' ');
					mpSwal.fire({
						title: "Attention",
						html: `${text} <br> ${orgText}`
					});
				}
				hideModal("#announcementFormModal");
			}).catch((error) => {
				$("#announcementFormSave").prop('disabled', false);
				mpSwal.fire("Error", error.reason, "error");
			});
		}

	},
  'change #announcementInputFile': function(event, instance) {
		event.preventDefault();
		var uploadFiles = $(event.target);
		var fieldId = uploadFiles.first().data("fieldid");
		var uploadFile = uploadFiles[0];

		if (uploadFile && uploadFile.files.length > 0 ) {
      $("#fileLabelSpan").text(uploadFile.files[0].name);
			$("#announcementInputFile").prop('disabled', true);
			$("#announcementFormSubmit").prop('disabled', true);

			var metaContext = {tokenId: tokenString()};

			var uploader = new Slingshot.Upload("myAnnouncementUploads", metaContext);

			uploader.send(uploadFile.files[0], function (error, downloadUrl) {
			  if (error) {
			    alert (error);
			  }
			  else {
				downloadUrl = remapDownloadUrl(downloadUrl);
			  	console.log("download = " + downloadUrl);
			  	var mediaType = uploadFile.files[0].type;

			  	var uploadedFile = {
			  		mediaName: uploadFile.files[0].name,
			  		mediaUrl: downloadUrl,
			  		mediaToken: metaContext.tokenId,
			  		mediaFileType: mediaType,
					mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId,
					newFile: true
			  	};

				const newFiles = instance.newFiles.get() || [];
				newFiles.push(uploadedFile);
				instance.newFiles.set(newFiles);

				$("#announcementInputFile").val('');
				$("#announcementInputFile").prop('disabled', false);
				$("#announcementFormSubmit").prop('disabled', false);
				fileUploader.set(null);
        $("#fileLabelSpan").text("");

			  }
			});
			fileUploader.set(uploader);

		}
	},
  "change #announcementOrgs": function (ev) {
    var orgIds = $("#announcementOrgs").val() || [];
    const template = Template.instance();

    if (!isVisibleForCurrentOrg(orgIds, Orgs.current()._id)) {
      template.hideGroups.set(true);

      $('input[name="sendTo"][value="allGroups"]').prop('checked', true);
    } else {
      template.hideGroups.set(false);

      Meteor.setTimeout(function() {
        $("#selectedGroups").select2({
          multiple: true,
          selectionCssClass: "form-control form-control-lg form-control-solid"
        });
      }, 200);
    }
  },
	"click .announcement-roles-group input[type='checkbox']": function(ev, template) {
		$("#audience-role").prop('checked', true);
	},
	"click #btnAddAnnouncementReminder": function(e) {
		e.preventDefault();
		$("#frmAddAnnouncementReminder").show();
	},
	"click #btnCancelAnnouncementReminder": function(e) {
		e.preventDefault();
		$("#frmAddAnnouncementReminder").hide();
	},
	"click #btnSaveAnnouncementReminder": function(e, instance) {
		let date = $("#frmAddAnnouncementReminder-Date").val();
		let time = $("#frmAddAnnouncementReminder-Time").val();
		let email = $("#frmAddAnnouncementReminder-Email").prop("checked");
		let push = $("#frmAddAnnouncementReminder-Push").prop("checked");

		if (!email && !push) {
			return mpSwal.fire("Error", "You must select a reminder channel", "error");
		}

		if (!date || !time) {
			return mpSwal.fire("Error", "You must enter a date and time", "error");
		}

		const reminder = {
			date: new moment.tz(date, "MM/DD/YYYY", Orgs.current().getTimezone()).format("YYYY-MM-DD"),
			time,
			email,
			push,
			status: "not saved",
		}

		const newReminders = instance.reminders.get() || [];
		newReminders.push(reminder);
		instance.reminders.set(newReminders);
		$("#frmAddAnnouncementReminder").hide();
		$("#frmAddAnnouncementReminder")[0].reset();
	},
	"click .btnDeleteAnnouncementReminder": function(e) {
		const announcementId =  Template.instance().data && Template.instance().data.announcementId;
		const reminderId = $(e.target).attr("data-id");
		Meteor.callAsync("deleteAnnouncementReminder", { announcementId, reminderId }).then((result) => {
			console.log("Announcement reminder deleted successfully:", result);
		}).catch((error) => {
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});

Template.announcementFormModal.created = function() {
	this.reminders = new ReactiveVar([])
	this.newFiles = new ReactiveVar();
	this.hideGroups = new ReactiveVar(false);
  this.audienceRoleSelected = new ReactiveVar(false);
}

Template.announcementFormModal.rendered = function() {
  $("#selectedGroups").select2({
    multiple: true,
    selectionCssClass:"form-control form-control-lg form-control-solid"
  });

	$("#announcementStartDate").datepicker({ autoclose: true, todayHighlight: true });
	$("#announcementEndDate").datepicker({ autoclose: true, todayHighlight: true });
	$("#frmAddAnnouncementReminder-Date").datepicker({ autoclose: true, todayHighlight: true });

  $("#selectedGroups").on("change", function() {
    $("#audience-group").prop('checked', true);
  })
	var self = this;
	self.autorun(function() {
		if (!Template.instance()) return;
    const currentOrg = Orgs.current();
		const announcementId = Template.instance().data && Template.instance().data.announcementId,
		announcement = Announcements.findOne({_id: announcementId});


		if (announcement) {
      if (announcement.selectedGroups && announcement.selectedGroups.length > 0) {
        $("#selectedGroups").val(announcement.selectedGroups).trigger('change');
      }

      if (!isVisibleForCurrentOrg(announcement.selectedOrgs, currentOrg._id)) {
				Template.instance().hideGroups.set(true);
			}
		}
	});

};

Template.announcementFormModal.helpers({
	"groups": function() {
		return Groups.find({
			orgId: Meteor.user()["orgId"]
		}, {sort: {"name" : 1}});
	},
	"announcement": function() {
		const announcementId = Template.instance().data && Template.instance().data.announcementId,
			announcement = Announcements.findOne({_id: announcementId});

		return announcement || {};
	},
	"checkedIfScheduledDate": function(dateVal) {
		if (dateVal=="today")
			return (this.scheduledDate == new Date().setHours(0,0,0,0)) ? true : false;
		else
			return (this.scheduledDate != new Date().setHours(0,0,0,0)) ? true : false;
	},
	"formattedAnnouncementStartDate": function() {
		return moment.tz(this.scheduledDate, Orgs.current().getTimezone()).format("MM/DD/YYYY");
	},
	"formattedAnnouncementEndDate": function() {
		return moment.tz(this.scheduledEndDate, Orgs.current().getTimezone()).format("MM/DD/YYYY");
	},
	"checkedIfGroups": function(groupsValue) {
		const announcementId = Template.instance().data && Template.instance().data.announcementId,
			announcement = Announcements.findOne({_id: announcementId});
		if (!announcement) return (groupsValue == "empty");
		if (groupsValue == "groups"  && (announcement.selectedGroups || []).length > 0) return true;
		if (groupsValue == "roles" && (announcement.selectedRoles || []).length > 0) return true;
		return (groupsValue == "empty");
	},
	"checkedIfRole": function(role) {
		const announcementId = Template.instance().data && Template.instance().data.announcementId,
			announcement = Announcements.findOne({_id: announcementId});
		return !announcement ? false : _.contains(announcement.selectedRoles, role);
	},
	"isGroupIdSelected": function(selectedGroups, groupId) {
		return (selectedGroups.indexOf(groupId) >= 0) ? true : false;
	},
	"isNewAnnouncement": function() {
		return (Session.equals("announcementId", "") || Session.equals("announcementId", null)) ? true : false;
	},
	"showOrgSelection": function() {
		const user = Meteor.user();
		const userPerson = (user) ? user.fetchPerson() : {};
		return userPerson.isMasterAdmin();
	},
	"showGroupSelection": function() {
		const hideGroups = Template.instance().hideGroups.get();
		return !hideGroups;
	},
	"announcementReminders": function() {
		const announcementId = Template.instance().data && Template.instance().data.announcementId,
		announcement = Announcements.findOne({_id: announcementId});

		let currentReminders = Template.instance().reminders.get() || [];
		if (announcement && announcement.reminders) currentReminders = currentReminders.concat(announcement.reminders);
		return currentReminders.length > 0 ? currentReminders : null;
	},
	"mediaFiles": function() {
		const announcementId = Template.instance().data && Template.instance().data.announcementId,
		announcement = Announcements.findOne({_id: announcementId});
		let currentUploads = Template.instance().newFiles.get() || [];
		if (announcement && announcement.mediaFiles) currentUploads = currentUploads.concat( announcement.mediaFiles );
		currentUploads.forEach( (cu) => {
			if (cu.mediaFileType == "application/pdf")
				cu.previewFile = "https://assets.momentpath.com/app/pdf-icon.png";
			else if (cu.mediaFileType.startsWith("video"))
				cu.previewFile = "https://assets.momentpath.com/app/video-icon.png";
			else
				cu.previewFile = cu.mediaUrl;
		});
		return currentUploads;
	},
	"uploadProgress": function() {
		var upload = fileUploader.get();
        if (upload)
            return Math.round(upload.progress() * 100);
	},
	"formatChannels": function(reminder) {
		let channels = []
		if (reminder.email) {
			channels.push("Email")
		}

		if (reminder.push) {
			channels.push("Push");
		}

		return channels.join(', ')
	}
});

var tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};

/**
 * Checks if an announcement should be visible for the current organization
 * Returns true if:
 * - selectedOrgs is undefined/null
 * - selectedOrgs is an empty array
 * - selectedOrgs contains only the current org's ID
 *
 * @param {Array} selectedOrgs - Array of organization IDs
 * @param {String} currentOrgId - The current organization's ID
 * @return {Boolean} - Whether the announcement should be visible
 */
function isVisibleForCurrentOrg(selectedOrgs, currentOrgId) {
  return !selectedOrgs ||
    selectedOrgs.length === 0 ||
    (selectedOrgs.length === 1 && selectedOrgs[0] === currentOrgId);
}