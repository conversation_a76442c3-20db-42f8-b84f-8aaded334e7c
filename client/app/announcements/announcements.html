<template name="announcements">
  <div class="container d-flex flex-row justify-content-end mb-4">
    <div class="mr-4 d-flex flex-column justify-content-end">
      <label>Group:</label>
      <select data-cy="select-group-announcements" class="form-control min-w-150px" id="filterGroup">
        <option value=""></option>
        {{#each groups}}
          <option value="{{_id}}">{{name}}</option>
        {{/each}}
      </select>
    </div>
    <div class="mr-4 d-flex flex-column justify-content-end">
      <label>Role:</label>
      <select data-cy="select-role-announcements" class="form-control min-w-150px" id="filterRole">
        <option value=""></option>
        <option value="admin">Admin</option>
        <option value="staff">Staff</option>
        <option value="family">Family</option>
      </select>
    </div>
    <div class="mr-4 d-flex flex-column justify-content-end">
      <label>Start Date:</label>
      <input data-cy="start-date-announcements" type="text" class="form-control" id="announcementsStartDate" value="{{formattedAnnouncementStartDate}}" >
    </div>
    <div class="mr-4 d-flex flex-column justify-content-end">
      <label>End Date:</label>
      <input data-cy="end-date-announcements" type="text" class="form-control" id="announcementsEndDate" value="{{formattedAnnouncementEndDate}}" >
    </div>
    <div class="{{#if userCanAddAnnouncement}}mr-4{{/if}} d-flex flex-column justify-content-end">
      <div data-cy="update-announcements-btn" class="form-control btn btn-primary font-weight-bolder btn-text-white" id="update-filters">
        Update
      </div>
    </div>
    {{#if userCanAddAnnouncement}}
      <div class="d-flex flex-column justify-content-end">
        <div data-cy="add-announcements-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="newAnnouncementLink">
          <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add
        </div>
      </div>
    {{/if}}
  </div>
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#unless notEmptyArray announcements}}
        <div class="d-flex flex-row align-items-center justify-content-center mt-12">
          <div class="alert alert-custom alert-notice alert-light-info fade show max-w-600px" id="no-announcement-alert" role="alert">
            <div class="alert-icon"><i class="fad fa-bullhorn"></i></div>
            <div data-cy="no-announcement-alert" class="alert-text"><h3>Currently, no announcements are scheduled.</h3><br/>Add announcements to inform families or caregivers. Announcements will appear at the top of daily email summaries and in-app within the feed.</div>
            <div class="alert-close">
              <button data-cy="no-announcement-close-alert" type="button" class="close" data-dismiss="alert" aria-label="Close" id="no-announcement-close-alert">
                <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
              </button>
            </div>
          </div>
        </div>
      {{/unless}}
      {{#each announcements}}
        <div class="card card-custom gutter-b" data-id="{{_id}}">
          <div class="card-body">
            <div class="row">
              <div class="d-flex align-items-center col-3">
                <span data-cy="announcement-title" class="font-weight-bolder font-size-h5 text-primary">{{headline}}</span>
              </div>
              <div class="d-flex align-items-center col-3">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-calendar-check"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Scheduled</span>
                    <span data-cy="scheduled-announcement-date" class="font-weight-bolder font-size-h5">{{scheduledDateFormatted}}</span>
                  </div>
                </div>
              </div>
              <div class="d-flex align-items-center col-3">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-users"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Recipient(s)</span>
                    <span data-cy="recipients-announcement" class="font-weight-bolder font-size-h5">
                      {{#if findRecipientGroups}}
                        {{getRecipientGroupList findRecipientGroups}}
                      {{else if findRecipientRoles}}
                        {{getRecipientRoleList findRecipientRoles}}
                      {{else}}
                        All
                      {{/if}}
                    </span>
                  </div>
                </div>
              </div>
              <div class="d-flex align-items-center col-2">
                <div class="d-flex align-items-center justify-content-start">
                  <span class="mr-4">
                    <i class="icon-2x text-bright-blue fad fa-clock"></i>
                  </span>
                  <div class="d-flex flex-column text-dark-75">
                    <span class="font-weight-bolder font-size-sm">Next Reminder</span>
                    <span data-cy="next-reminder-announcement" class="font-weight-bolder font-size-h5">
                      {{nextReminderFormatted}}
                    </span>
                  </div>
                </div>
              </div>
              {{#if showOptions masterAdminEditId}}
              {{#if userCanAddAnnouncement}}
                <div class="d-flex justify-content-end align-items-center col-1">
                  <div class="d-flex flex-row align-items-center justify-content-end">
                    <div class="dropdown" id="announcement-list-dropdown">
                      <div data-cy="announcements-options" class="btn btn-icon btn-clean" data-toggle="dropdown" >
                          <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                      </div>
                      <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                        <span data-cy="edit-announcement" class="dropdown-item clickable-row editAnnouncementLink" data-id="{{_id}}" >Edit</span>
                        <span data-cy="delete-announcement" class="dropdown-item clickable-row deleteAnnouncementLink" data-id="{{_id}}">Delete</span>
                      </div>
                    </div>
                  </div>
                </div>
                {{/if}}
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>
