import { Template } from 'meteor/templating';
import './helpRedirect.html';
import './loading';

Template.helpRedirect.onRendered(async function() {
  var user = Meteor.user();
  const org = user && user.fetchOrg(), person = user && user.fetchPerson(), personType = person && person.type;
  let accountType = (personType == "family" ? "family" : "provider");
  var _hsq = window._hsq = window._hsq || [];
    _hsq.push(["identify",{
        first_name: `${person.firstName}`,
        last_name: `${person.lastName}`,
        app_full_name: `${person.firstName} ${person.lastName}`,
        app_account_type: accountType,
        app_account_level: person.type,
        app_initital_user: person.initialOrgUser || false,
        email: person.getEmailAddress(),
        person_id: person._id,
        org_id: org._id,
        org_name: org.name,
        company_name: org.name,
    }]);
    _hsq.push(['setPath', '/help']);
    _hsq.push(['trackPageView']);
  
  
  setTimeout(function(){window.open(Meteor.settings.public.supportSite, "_self");}, 1500)
  
})
