<template name="_quickpaneluser">
  
<!-- begin::User Panel-->
		<div id="kt_quick_user" class="offcanvas offcanvas-right p-10">

			<!--begin::Header-->
			<div class="offcanvas-header d-flex align-items-center justify-content-between pb-5">
				<h3 class="font-weight-bold m-0">User Profile
					<!-- <small class="text-muted font-size-sm ml-2">12 messages</small> -->
				</h3>
        <div data-cy="quick-user-close" class="btn btn-icon btn-clean btn-lg" id="kt_quick_user_close" >
          <span class="fad-regular fad-primary fad fa-times"></span>
        </div>
			</div>

			<!--end::Header-->

			<!--begin::Content-->
			<div class="offcanvas-content pr-5 mr-n5">

				<!--begin::Header-->
				<div class="d-flex align-items-center">
					<div class="symbol symbol-100 mr-5">
						<div class="people-list-user-img" style="background-image:url({{currentUser.fetchPerson.getAvatarUrl}})"></div>
					</div>
					<div class="d-flex flex-column">
						<a data-cy="user-profile-name" href="/people/{{currentUser.fetchPerson._id}}" class="font-weight-bold font-size-h5 text-dark-75 text-hover-primary">{{ currentUser.fetchPerson.firstName }} {{ currentUser.fetchPerson.lastName }}</a>
						<div data-cy="user-profile-org-user-type" class="text-muted mt-1">{{currentOrgName}} | {{ currentUser.fetchPerson.type.capitalizeFirstLetter }}</div>
						<div class="navi mt-1">
							<span class="navi-item">
								<span class="navi-link p-0 pb-2">
									<span data-cy="user-profile-email" class="navi-text text-muted">{{currentUser.fetchPerson.getEmailAddress}}</span>
								</span>
							</span>
							<!-- <a href="#" class="btn btn-sm btn-light-primary font-weight-bolder py-2 px-5">Sign Out</a> -->
						</div>
						{{#if isSuperAdmin}}
							<!-- Show the current org id: -->
							<div data-cy="user-profile-org-id" class="text-muted mt-1">Org ID: {{currentOrgId}}</div>
						{{/if}}
					</div>
				</div>

				<!--end::Header-->

				<!--begin::Separator-->
				<div class="separator separator-dashed my-6"></div>

				<!--end::Separator-->
        {{#if showSwitchMembership}}
          <div class="d-flex flex-row align-items-end mb-4">
            <div data-cy="switch-community-btn" class="btn btn-primary font-weight-bolder btn-text-white flex-grow-1" id="btn-user-switch-membership">
              <i class="fad-regular fad fa-exchange mr-2" style="color:#fff"></i>Switch Community
            </div>
          </div>
        {{/if}}
        {{#if showSwitchOrg}}
          <div class="d-flex flex-row align-items-end mb-4">
            <div data-cy="switch-site-btn" class="btn btn-primary font-weight-bolder btn-text-white flex-grow-1" id="btn-admin-switch-org">
              <i class="fad-regular fad fa-exchange mr-2" style="color:#fff"></i>Switch Site
            </div>
          </div>
        {{/if}}
        <div class="d-flex flex-row align-items-end mb-4">
          <a href="/sign-out" class="btn btn-primary font-weight-bolder btn-text-white flex-grow-1" id="switch-group-button" data-cy="sign-out-button">
            <i class="fad-regular fad fa-sign-out mr-2" style="color:#fff"></i>Sign Out
          </a>
        </div>

				{{#if hasOrgSettingChanges}}
					<div class="separator separator-dashed my-6"></div>
					<h3 class="font-weight-bold m-0">Org Setting Changes</h3>
					<div class="my-2">
						{{> announcementOrgsField childOnly=true}}
					</div>
					{{#each val in orgSettingChanges}}
						<div class="border-bottom py-4 align-items-center justify-content-between border-1 d-flex">
							<div class="d-flex flex-column">
								<span class="font-weight-bolder text-dark font-size-h5">{{formatBillingChargeName val.dataTypeNameAndDescription.name}}</span>
								<span class="text-dark-25">{{val.dataTypeNameAndDescription.description}}</span>
							</div>
							<div class="d-flex flex-row">
								<span class="btn btn-sm btn-primary font-weight-bolder btn-text-white btn-apply-setting mr-2" data-id="{{val._id}}">
									Apply
								</span>
								<span class="btn btn-sm btn-secondary font-weight-bolder btn-cancel-setting" data-id="{{val._id}}">
									Cancel
								</span>
							</div>
						</div>
					{{/each}}
				{{/if}}
			</div>

			<!--end::Content-->
		</div>

		<!-- end::User Panel-->
</template>
