import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { PUNCH_CARD_TYPE } from "../../../../lib/constants/billingConstants";
import { OrgSettingChanges } from '../../../../lib/collections/orgSettingChanges';
import { Orgs } from '../../../../lib/collections/orgs';
import { processPermissions } from '../../../../lib/permissions.js';
import { showModal } from '../../../../client/app/main.js';
import { User } from '../../../../lib/collections/users.js';
import './_quickpaneluser.html';
import '../../../app/announcements/_announcementOrgsField.js';
import '../../../layout/application/_switchSite.js'

Template._quickpaneluser.created = function () {
  this.memberships = new ReactiveVar(false);
  let instance = this;
  // Checking permissions here to help limit open subscriptions to the collection
  instance.autorun(function () {
    instance.subscribe('theOrgSettingChanges')
    let query = OrgSettingChanges.find();
    const handle = query.observeChanges({
      added: function (id, fields) {
        mpToastr("Pending Org Setting Change", {
          onclick: function () {
            $("#kt_quick_user_toggle").trigger('click')
          }
        })
      }
    })
  });

}

Template._quickpaneluser.rendered = function () {
  var self = this;
  Meteor.callAsync('getSwitchableMemberships').then((res) => {
    if (res && res.length > 0) {
      self.memberships.set(res);
    }
  }).catch((err) => {
    console.error('Error fetching switchable memberships:', err);
  });
}

Template._quickpaneluser.helpers({
  'currentOrgId': function () {
    return Orgs.current()?._id;
  },
  'showSwitchOrg': function () {
    const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson(), org = Orgs.current();
    return org && currentPerson && currentPerson.type === "admin" && (currentPerson.masterAdmin || currentPerson.superAdmin) && (org.parentOrgId || org.enableSwitchOrg);
  },
  'showSwitchMembership': function () {
    const m = Template.instance().memberships.get();
    return _.isArray(m) && m.length > 0;
  },
  'hasOrgSettingChanges': function () {
    const permission = processPermissions({
      assertions: [{ context: "org/propagateSettings", action: "edit" }],
      evaluator: (person) => person.superAdmin == true,
    });

    return permission && OrgSettingChanges.find().count() > 0;
  },
  'orgSettingChanges': function () {
    return OrgSettingChanges.find().fetch();
  },
  'formatBillingChargeName': function (name) {
    let words = name.split(" ")
    if (words[1] == PUNCH_CARD_TYPE) {
      return words[0] + " " + 'Punch Card'
    } else {
      return words[0] + " " + words[1].capitalizeFirstLetter()
    }
  }
});

Template._quickpaneluser.events({
  "click .btn-apply-setting": function (e, i) {
    e.preventDefault();
    const orgIds = $("#announcementOrgs").val() || [];
    const id = $(e.target).data("id");
    Meteor.callAsync("applySettingChange", { orgSettingChangeId: id, destinationOrgIds: orgIds }).then((result) => {
      mpSwal.fire("Success", "Setting Applied", "success");
    }).catch((error) => {
      mpSwal.fire("Error", error.reason, "error");
    });
  },
  "click .btn-cancel-setting": function (e, i) {
    e.preventDefault();
    const id = $(e.target).data("id");
    Meteor.callAsync("cancelSettingChange", { orgSettingChangeId: id }).then((result) => {
      // Handle success if needed
    }).catch((error) => {
      mpSwal.fire("Error", error.reason, "error");
    });
  },
  "click #btn-admin-switch-org": function () {
    showModal('_switchSite', { }, "#_switchSite");
  },
  "click #btn-user-switch-membership": function () {
    const memberships = Template.instance().memberships.get();
    const opts = {};
    _.each(memberships, (m) => { opts[m._id] = m.name; });
    mpSwal.fire({
      title: "Switch Community",
      input: 'select',
      inputOptions: opts,
      inputPlaceholder: 'Select a Community',
      showCancelButton: true
    }).then((result) => {
      if (result.value) {
        Meteor.callAsync('switchUserMembership', result.value).then((result) => {
          location.replace("/loading?setLoc=home");
        }).catch((error) => {
          mpSwal.fire("Error", error.reason, "error");
        });
      }
    })
  },
})
