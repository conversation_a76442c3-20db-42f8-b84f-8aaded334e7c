import { ClientStorage } from 'ClientStorage';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { AvailableCustomizations } from '../../../../lib/customizations';
import './_header.html';
import { MethodDataManager, MethodDataTracker } from '../../../../lib/methodDataManager';
import { Orgs, Org } from '../../../../lib/collections/orgs';
import { User } from '../../../../lib/collections/users';
import { NewMessages } from '../../../../lib/collections/newMessages';
import '../../../app/dashboard/dashboardHeader';
import '../../../app/reports/reportsHeader';
import '../../../app/messages/messagesHeader';
import '../../../app/calendar/calendar';
import '../../../app/people/peopleHeader';
import '../../../app/curriculum/curriculumHeaders';
import '../../../app/billing/billingAdminHeader';
import '../../../app/admin/orgHeader';
import '../../../app/moments/momentFormModal.js';
import { isCheckinRequired } from '../../../../lib/momentHelpers';
import { setupNewMomentSession } from '../../../../mpweb';
import '../../../app/admin/orgHeader';
import '../../../app/billing/billingAdminHeader';
import '../../../app/curriculum/curriculumHeaders';
import Swal from 'sweetalert2/dist/sweetalert2';
import '../../../app/search/search.js'
import { showModal } from '../../../app/main.js';
import '../../../app/search/search.js'
import '../../../app/calendar/calendarHeader.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import '../../../app/dashboard/dashboardFamilyAdvanced/dashboardFamilyAdvancedHeader';

const IDLE_WHITELIST = [
  '/kiosk/pinCodeCheckin',
  '/pinCodeCheckin'
];

const clientStorage = new ClientStorage();

Template.calendarHeader.onCreated(function () {
    let headerLinked = {
        'calendar': true,
        'list': false,
        'staff': false
    }
    this.headerLinkClicked = new ReactiveVar(headerLinked);
    console.log("in created ...")
})

Template.calendarHeader.helpers({
    routeContainsHashLocal(str) {
        const links = Template.instance().headerLinkClicked.get();
        return links[str];
    }
})

Template.calendarHeader.events({
    'click .menu-item': function(e, i) {
      const headerLinks = Template.instance().headerLinkClicked.get();
      for (let k of Object.keys(headerLinks)) {
        headerLinks[k] = k === e.currentTarget.id ? true : false;   
      }
      Template.instance().headerLinkClicked.set(headerLinks);
    }
});


Template._header.onRendered(function() {
  $('#kt_quick_search_toggle').on('show.bs.dropdown', function () {
    $("input#qsearch").val("");
    Session.set("searchParams", "");
    setTimeout(function() {$("input#qsearch").focus();},500);
  });
  let intercomUserId = null;
  this.autorun( function() {
    const intercomHash = Session.get("intercomHash");
    if (intercomHash && window.hasOwnProperty("Intercom")) {
      const meteorUserId = Meteor.user()?._id;
      if (meteorUserId != intercomUserId && (!meteorUserId || Meteor.user().fetchPerson())) {
        const user = Meteor.user(), userPerson = user?.fetchPerson(), org = Orgs.current();
        const updateObj = {
          name: userPerson && `${userPerson.firstName} ${userPerson.lastName}`,
          email: userPerson && userPerson.getEmailAddress(),
          company: org && org.name,
          user_type: userPerson && userPerson.type,
          long_company_name: org?.longName || ""
        };
        Intercom("update", updateObj);
        intercomUserId = meteorUserId
      }
    }
  })
  
})


Template._header.onCreated(function() {
  this.appHelpBadge = new ReactiveVar(false);
  this.webReleaseUrl = new ReactiveVar("");

  // intital fetch of message notifications
  MethodDataManager.fetchMessageNotifications();

  // set the interval and save interval id
  const intervalId = Meteor.setInterval(function() {
    MethodDataManager.fetchMessageNotifications();
  }, MethodDataManager.MESSAGE_NOTIFICATION_DELAY);

  MethodDataTracker.messageNotificationIntervals.push(intervalId);
});

Template._header.onDestroyed(function() {
  MethodDataTracker.messageNotificationIntervals.forEach(function(interval) {
    Meteor.clearInterval(interval);
  });
});

Template._header.rendered = function() {
  const lastCheckedTime = clientStorage.get("webHelpBadgeLastChecked");
  var self = this;
  Meteor.callAsync("webHelpBadge").then(result => {
    const releaseData = _.find(result?.data, function(d){ return d.type == "releaseInfo" });
    if (releaseData) self.webReleaseUrl.set(releaseData.webLink);
    const releaseDate = new moment(result?.webReleaseDate);
    if (lastCheckedTime) {
      const lastCheckedMoment = new moment(lastCheckedTime);
      if (releaseDate.isAfter(lastCheckedMoment)) {
        self.appHelpBadge.set(true);
      }
    } else if (releaseDate.isValid() && !lastCheckedTime) {
      self.appHelpBadge.set(true);
    }
  });

	// Disable Idle Timer
	let inactivityTime = function () {
    const swalTimer = 30000
        let timeOut = 59.5*60;
    let time;
		window.onload = resetTimer;
        document.addEventListener('click', function (event) {
                resetTimer();
        });
        document.addEventListener('input', function(event) {
                resetTimer();
        })
  
		function logout() {
			let timerInterval;
      const bypassFlag = IDLE_WHITELIST.find(
        whiteListItem => window.location.pathname === whiteListItem
      );
      if (bypassFlag) return;
			mpSwal.fire({
				title: 'You have been idle for a while.',
				showCancelButton: true,
				showDenyButton: true,
				denyButtonText: 'Stay Signed In',
				cancelButtonText: 'Sign Out',
				reverseButtons: true,
				html: `If you would like to stay logged in, please click:<br> "Stay Signed In".<br>
			<div class="single-chart">
				<svg viewBox="0 0 36 36" class="circular-chart pink">
				<path class="circle-bg"
					d="M18 2.0845
					a 15.9155 15.9155 0 0 1 0 31.831
					a 15.9155 15.9155 0 0 1 0 -31.831"
				/>
				<path id="progress-bar" class="circle"
					stroke-dasharray="100, 100"
					d="M18 2.0845
					a 15.9155 15.9155 0 0 1 0 31.831
					a 15.9155 15.9155 0 0 1 0 -31.831"
				/>
				<text x="18" y="20.35" class="percentage"></text>
				</svg>
			</div>`,
				timer: swalTimer,
				didOpen: () => {
					mpSwal.showLoading()
					const b = mpSwal.getHtmlContainer().querySelector('text')
					timerInterval = setInterval(() => {
            if(document.getElementById("progress-bar"))
						  document.getElementById("progress-bar").setAttribute("stroke-dasharray", ((((mpSwal.getTimerLeft() / swalTimer) * 100)) + ", 100"))
						b.textContent = millisToMinutesAndSeconds(mpSwal.getTimerLeft())
					}, 100)
				},
				willClose: () => {
					clearInterval(timerInterval)
				}
			}).then((result) => {
				if (result.isDenied) {
					mpSwal.fire(
						'Perfect!',
						'Now you have more time to surf.',
						'success'
					)
				} else if (result.dismiss === Swal.DismissReason.cancel) {
					window.location.href = "/sign-out"
				}
				if (result.dismiss === Swal.DismissReason.timer) {
          window.location.href = "/sign-out"
					console.log('I was closed by the timer')
				}
			})

		}
		function resetTimer() {
			clearTimeout(time);
			time = setTimeout(logout, timeOut * 1000)
		}
		function millisToMinutesAndSeconds(millis) {
			var minutes = Math.floor(millis / 60000);
			var seconds = ((millis % 60000) / 1000).toFixed(0);
			return minutes + ":" + (seconds < 10 ? '0' : '') + seconds;
		}
	};
	inactivityTime();
}



Template._header.events({
  'click .search-people': function() {
    showModal("search", { shouldFade: true }, "#searchModal");
  },
  'click .header-registration': function() {
    const currentPerson = Meteor.user().fetchPerson(), currentOrg = Orgs.current();
    if (!currentPerson || !currentOrg) {
      return false;
    }
    window.open(`/registration?orgId=${currentOrg._id}&personId=${currentPerson._id}`, '_blank');
  },
  'click .headerNewMoment': function() {
    if(isCheckinRequired()){
      mpSwal.fire("Error", 'Staff and admins must be checked in to post moments. Please visit your profile to checkin.', "error");
    } else {
      setupNewMomentSession();
      showModal("momentFormModal", {shouldFade: true}, "#momentFormModal");
    }
  },
  "click #whats-new": function(e, i) {
    clientStorage.set("webHelpBadgeLastChecked", new Date());
    i.appHelpBadge.set(false);
  },
  "click #launch-intercom": function() {
    Intercom("show");
  }
})

Template._header.helpers({
  isActiveRoute() {
    const currentRouteName = FlowRouter.getRouteName() || '';
    const options = Array.from(arguments);

    return options.some(option => {
      if (typeof option !== 'string') {
        return false;
      }
      return currentRouteName.startsWith(option);
    });
  },
  "getLearningPathLink": function() {
    return Session.get("learningPathLink");
  },
  "webReleaseUrl": function() {
    return Template.instance().webReleaseUrl.get();
  },
  'newAppHelpBadge': function() {
    return Template.instance().appHelpBadge.get();
  },
  'newMessageAvailable': function() {
    const meteorUser = Meteor.user();
    if (meteorUser && meteorUser._id) {
      //the "fake" collection maps results to meteor userId
      var doc = NewMessages.findOne({});
      return doc && doc.hasNotification;
    }
    return false;
  },
  'showAddMoment': function() {
    const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson(),
      userType = currentPerson && currentPerson.type;
    
    return userType=="admin" || userType == "staff";
  },
  'showMessageCenter': function() {
    const currentPerson = Meteor.user()?.fetchPerson(), currentOrg = Orgs.current();
    if (!currentPerson || !currentOrg) {
      return false;
    }
    return !(currentOrg.hasCustomization('messages/disableStaffMessages/enabled') && currentPerson.type === 'staff');
  },
  'hasChatSupport': function () {
      const org = Orgs.current();
      return org && org.hasCustomization("people/chatSupport/enabled");
  },
  "showFamilyAdvancedDashboardHeader": function() {
    const redirectWorkaround = FlowRouter.current().route.name.startsWith("dashboard") && 
        Orgs.current()?.hasCustomization(AvailableCustomizations.FAMILY_ADVANCED_DASHBOARD) &&
        Meteor.user()?.fetchPerson()?.type === "family";

    return FlowRouter.current().route.name === "familyAdvancedDashboard" || redirectWorkaround;
  }
})
