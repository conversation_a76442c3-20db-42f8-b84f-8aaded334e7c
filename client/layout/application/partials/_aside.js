import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_aside.html';
import { processPermissions } from '../../../../lib/permissions';
import { ChildcareCrmAccounts } from '../../../../lib/collections/childcareCrmAccounts.js'

import { User } from '../../../../lib/collections/users.js';
import {Orgs, Org} from '../../../../lib/collections/orgs.js';
import _ from '../../../../lib/util/underscore.js';

Template._aside.onCreated(function () {
	this.autorun(() => {
		this.subscribe("orgHasEnroll");
	})
})

Template._aside.helpers({
	canSeeContent: function () {
		const meteorUser = Meteor.user();
		return (meteorUser && (meteorUser.canSeeNavItem("announcements")
			|| meteorUser.canSeeNavItem("curriculum")
			|| meteorUser.canSeeNavItem("food")
			|| meteorUser.canSeeNavItem("forms")));
	},
	isCurrentPage: function (pageName) {
		let currentPage = FlowRouter.getRouteName();
		if (currentPage.startsWith("billingAdmin")) currentPage = "billingAdmin";
		if (currentPage == "groups" || currentPage == "people" || currentPage == "person" || currentPage == "inquiries") currentPage = "manage";
		if (currentPage == "dashboardSite" || currentPage == "expressDriveUp" || currentPage == "mediaReview" || currentPage == "mediaGallery") currentPage = "dashboard";
	    if (currentPage == "announcements" || currentPage == "activities" || currentPage == "documents" || currentPage == "food" || currentPage == "food.show" || currentPage == "activities.show" || currentPage == "activities.builder" || currentPage == "activities.theme.show" ) currentPage = "content";
    	if (currentPage == "scheduling" || currentPage == "calendar") currentPage = "time";
		return currentPage == pageName;
	},
	isCurrentSubPage: function (pageName) {
		let currentPage = FlowRouter.getRouteName();
		if (currentPage === "person") currentPage = "people";
		if (currentPage === "food.show") currentPage = "food";
		if (currentPage === "activities.show" || currentPage === "activities.builder" || currentPage === "activities.theme.show") currentPage = "activities";

		return currentPage === pageName;
	},
	canSee: function (tabName) {
		const meteorUser = Meteor.user();
		return (meteorUser && meteorUser.canSeeNavItem(tabName));
	},
	showGettingStarted: function () {
		const user = Meteor.user(), person = user && user.fetchPerson();
		return person && person.initialOrgUser && person.onboardingData && person.onboardingData.v2Started;
	},
	billingUnactivated: function () {
		const org = Orgs.current(), billingStatus = org && org.billingStatus();
		return !billingStatus || !(billingStatus.status === "active" || billingStatus.status === "invoice_only");
	},
  whiteLabelLogoOverride: function() {
	  const host = window.location.host.split(".")[0];
	  const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
	  const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
	  if (currentOrg && _.deep(currentOrg, "whiteLabel.assets.appLogo")) {
		  return {
			  small: currentOrg.whiteLabel.assets.appLogoSmall,
			  large: currentOrg.whiteLabel.assets.appLogo
		  };
	  } else if (enabledSites && enabledSites.indexOf(host) > -1) {
		  return {
			  small: Meteor.settings.public.whitelabel[host].small_logo,
			  large: Meteor.settings.public.whitelabel[host].large_logo
		  }
	  }
  },
	hasEnroll: function () {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
		if (!person || Meteor.user()?.fetchOrg()?.hasCustomization('enrollButton/hidden') || !(person && person.type === "admin" || person.type === 'staff')) {
			return false;
		}
		return ChildcareCrmAccounts.find().fetch().length > 0;
	},
	billingRoute: function () {
		if(processPermissions({
			assertions: [{ context: "billing/reports", action: "read"}],
			evaluator: (person) => person.type=="admin"
		})) {
			return '/billing/admin'
		} else {
			return '/billing/admin/configuration'
		}
	}
});
