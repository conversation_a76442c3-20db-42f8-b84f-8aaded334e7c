<template name="_header">
<!--begin::Header-->

<!--begin::Header-->
					<div id="kt_header" class="header header-fixed header-box-shadow">

						<!--begin::Container-->
						<div class="container-fluid d-flex align-items-stretch justify-content-between">

							<!--begin::Header Menu Wrapper-->
							<div class="header-menu-wrapper header-menu-wrapper-left" id="kt_header_menu_wrapper">

								<!--begin::Header Menu-->
								<div id="kt_header_menu" class="header-menu header-menu-mobile header-menu-layout-default" data-cy="kt-header-nav-menu">
									{{#if showFamilyAdvancedDashboardHeader}}
										{{ > dashboardFamilyAdvancedHeader }}
									{{else}}
										{{#if (isActiveRoute "people" "person")}}
											{{ > peopleHeader }}
										{{/if}}
										{{#if (isActiveRoute "activities")}}
											{{ > curriculumHeader }}
										{{/if}}
										{{#if (isActiveRoute "billingAdmin")}}
											{{ > billingAdminHeader }}
										{{/if}}
										{{#if (isActiveRoute "org")}}
											{{ > orgHeader }}
										{{/if}}
										{{#if (isActiveRoute "integrations")}}
											{{ > orgHeader }}
										{{/if}}
										{{#if (isActiveRoute "scheduling")}}
											{{ > calendarHeader }}
										{{/if}}
										{{#if (isActiveRoute "messages")}}
											{{ > messagesHeader }}
										{{/if}}
										{{#if (isActiveRoute "reports")}}
											{{ > reportsHeader }}
										{{/if}}
										{{#if (isActiveRoute "dashboard" "dashboardSite" "expressDriveUp" "mediaReview" "mediaGallery" "explore")}}
											{{ > dashboardHeader }}
										{{/if}}
									{{/if}}


										<!--end::Header Nav-->
								</div>

								<!--end::Header Menu-->
							</div>

							<!--end::Header Menu Wrapper-->

					<!--begin::Topbar-->
					<div class="topbar">
						<!--begin::Sync-->
								<!--begin::Search-->
                                <div class="topbar-item">
                                    <div class="btn btn-icon btn-clean search-people btn-lg mr-1">
                                        <span class="svg-icon svg-icon-xl svg-icon-primary" data-cy="general-search-icon">

                                            <!--begin::Svg Icon | path:assets/media/svg/icons/General/Search.svg-->
                                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <rect x="0" y="0" width="24" height="24" />
                                                    <path d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />
                                                    <path d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z" fill="#000000" fill-rule="nonzero" />
                                                </g>
                                            </svg>

                                            <!--end::Svg Icon-->
                                        </span>
                                    </div>
                                </div>

								<!--end::Search-->

								<!--begin::Notifications-->
								<!-- <div class="dropdown">
									<div class="topbar-item" data-toggle="dropdown" data-offset="10px,0px">
										<div class="btn btn-icon btn-clean btn-dropdown btn-lg mr-1 pulse pulse-primary">
											<span class="svg-icon svg-icon-xl svg-icon-primary">
												<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
														<rect x="0" y="0" width="24" height="24" />
														<path d="M2.56066017,10.6819805 L4.68198052,8.56066017 C5.26776695,7.97487373 6.21751442,7.97487373 6.80330086,8.56066017 L8.9246212,10.6819805 C9.51040764,11.267767 9.51040764,12.2175144 8.9246212,12.8033009 L6.80330086,14.9246212 C6.21751442,15.5104076 5.26776695,15.5104076 4.68198052,14.9246212 L2.56066017,12.8033009 C1.97487373,12.2175144 1.97487373,11.267767 2.56066017,10.6819805 Z M14.5606602,10.6819805 L16.6819805,8.56066017 C17.267767,7.97487373 18.2175144,7.97487373 18.8033009,8.56066017 L20.9246212,10.6819805 C21.5104076,11.267767 21.5104076,12.2175144 20.9246212,12.8033009 L18.8033009,14.9246212 C18.2175144,15.5104076 17.267767,15.5104076 16.6819805,14.9246212 L14.5606602,12.8033009 C13.9748737,12.2175144 13.9748737,11.267767 14.5606602,10.6819805 Z" fill="#000000" opacity="0.3" />
														<path d="M8.56066017,16.6819805 L10.6819805,14.5606602 C11.267767,13.9748737 12.2175144,13.9748737 12.8033009,14.5606602 L14.9246212,16.6819805 C15.5104076,17.267767 15.5104076,18.2175144 14.9246212,18.8033009 L12.8033009,20.9246212 C12.2175144,21.5104076 11.267767,21.5104076 10.6819805,20.9246212 L8.56066017,18.8033009 C7.97487373,18.2175144 7.97487373,17.267767 8.56066017,16.6819805 Z M8.56066017,4.68198052 L10.6819805,2.56066017 C11.267767,1.97487373 12.2175144,1.97487373 12.8033009,2.56066017 L14.9246212,4.68198052 C15.5104076,5.26776695 15.5104076,6.21751442 14.9246212,6.80330086 L12.8033009,8.9246212 C12.2175144,9.51040764 11.267767,9.51040764 10.6819805,8.9246212 L8.56066017,6.80330086 C7.97487373,6.21751442 7.97487373,5.26776695 8.56066017,4.68198052 Z" fill="#000000" />
													</g>
												</svg>
											</span>
											<span class="pulse-ring"></span>
										</div>
									</div>
									<div class="dropdown-menu p-0 m-0 dropdown-menu-right dropdown-menu-anim-up dropdown-menu-lg">
										<form>

											[html-partial:include:{"file":"partials/_extras/dropdown/notifications.html"}]
										</form>
									</div>
								</div> -->

								<!--end::Notifications-->

								<!--begin::Quick Actions-->
								<!-- <div class="dropdown">

									<div class="topbar-item" data-toggle="dropdown" data-offset="10px,0px">
										<div class="btn btn-icon btn-clean btn-dropdown btn-lg mr-1">
											<span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-plus-square"></span>
										</div>
									</div>
									<div class="dropdown-menu p-0 m-0 dropdown-menu-right dropdown-menu-anim-up dropdown-menu-lg">
										{{ > _quickActions }}
									</div>
								</div> -->

								<!--end::Quick Actions-->
								{{#if showAddMoment}}
									<div class="topbar-item">
										<div class="btn btn-icon btn-clean btn-lg mr-1">
											<a class="headerNewMoment">
												<span data-cy="new-moment-button" class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-plus-square"></span>
											</a>
										</div>
									</div>
								{{/if}}


								<!--begin::Messages-->
                                {{# if showMessageCenter }}
                                    <div class="topbar-item">
                                        <div class="btn btn-icon btn-clean btn-lg mr-1" style="position:relative;" >
                                            <a data-cy="message-center-btn" href="/messages#myInbox" class="svg-icon svg-icon-xl svg-icon-primary">

                                                <!--begin::Svg Icon | path:assets/media/svg/icons/Communication/Group-chat.svg-->
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                        <rect x="0" y="0" width="24" height="24" />
                                                        <path d="M16,15.6315789 L16,12 C16,10.3431458 14.6568542,9 13,9 L6.16183229,9 L6.16183229,5.52631579 C6.16183229,4.13107011 7.29290239,3 8.68814808,3 L20.4776218,3 C21.8728674,3 23.0039375,4.13107011 23.0039375,5.52631579 L23.0039375,13.1052632 L23.0206157,17.786793 C23.0215995,18.0629336 22.7985408,18.2875874 22.5224001,18.2885711 C22.3891754,18.2890457 22.2612702,18.2363324 22.1670655,18.1421277 L19.6565168,15.6315789 L16,15.6315789 Z" fill="#000000" />
                                                        <path d="M1.98505595,18 L1.98505595,13 C1.98505595,11.8954305 2.88048645,11 3.98505595,11 L11.9850559,11 C13.0896254,11 13.9850559,11.8954305 13.9850559,13 L13.9850559,18 C13.9850559,19.1045695 13.0896254,20 11.9850559,20 L4.10078614,20 L2.85693427,21.1905292 C2.65744295,21.3814685 2.34093638,21.3745358 2.14999706,21.1750444 C2.06092565,21.0819836 2.01120804,20.958136 2.01120804,20.8293182 L2.01120804,18.32426 C1.99400175,18.2187196 1.98505595,18.1104045 1.98505595,18 Z M6.5,14 C6.22385763,14 6,14.2238576 6,14.5 C6,14.7761424 6.22385763,15 6.5,15 L11.5,15 C11.7761424,15 12,14.7761424 12,14.5 C12,14.2238576 11.7761424,14 11.5,14 L6.5,14 Z M9.5,16 C9.22385763,16 9,16.2238576 9,16.5 C9,16.7761424 9.22385763,17 9.5,17 L11.5,17 C11.7761424,17 12,16.7761424 12,16.5 C12,16.2238576 11.7761424,16 11.5,16 L9.5,16 Z" fill="#000000" opacity="0.3" />
                                                    </g>
                                                </svg>
                                                {{#if newMessageAvailable}}
                                                    <span class="label bg-bright-blue text-white font-weight-bolder" style="position:absolute;top:0px;right:0px;"></span>
                                                {{/if}}

                                                <!--end::Svg Icon-->
                                            </a>
                                        </div>
                                    </div>
                                {{/if}}
								<!--end::Messages-->

								<div class="topbar-item">
									<div class="dropdown">
										<div data-cy="help-badge-btn" class="btn btn-icon btn-clean btn-lg mr-1" style="position:relative;" data-toggle="dropdown">
											<span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-info-circle"></span>
											{{#if newAppHelpBadge}}
												<span class="label bg-bright-blue text-white font-weight-bolder" style="position:absolute;top:0px;right:0px;"></span>
											{{/if}}
										</div>
										<div data-cy="dropdown-menu-btn" class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
											<a data-cy="whats-new" href="{{webReleaseUrl}}" target="_blank" class="dropdown-item clickable-row align-items-center {{#if newAppHelpBadge}}text-bright-blue font-weight-bolder{{/if}}" id="whats-new"><span class="svg-icon svg-icon-primary fad fad-regular {{#if newAppHelpBadge}}text-bright-blue{{else}}fad-primary{{/if}} fa-gift mr-2"></span>What's New</a>
											<a data-cy="docs-knowledge-base" href="http://educate.lineleader.com/" target="_blank" class="dropdown-item clickable-row align-items-center"><span class="svg-icon svg-icon-primary fad fad-regular fad-primary fa-question-circle mr-2"></span> Docs &amp; Knowledge Base</a>
											<a data-cy="learning-path" href="{{getLearningPathLink}}" target="_blank" class="dropdown-item clickable-row align-items-center"><span class="svg-icon svg-icon-primary fad fad-regular fad-primary fa-graduation-cap mr-2"></span> LearningPath</a>
                      {{#if hasChatSupport }}
                        <a data-cy="chat-with-the-school" href={{getChatSupportChatUrl}} target="_blank" class="dropdown-item clickable-row align-items-center" id="launch-chat-support"><span class="svg-icon svg-icon-primary fad fad-regular fad-primary fa-comment-alt-dots mr-2"></span> {{getChatSupportMenuVerbiage}}</a>
                      {{/if}}
											<a data-cy="app-support" href="#" class="dropdown-item clickable-row align-items-center" id="launch-intercom"><span class="svg-icon svg-icon-primary fad fad-regular fad-primary fa-comments-alt mr-2"></span> App Support</a>
										</div>
									</div>
								</div>

								<!--begin::User-->
								<div class="topbar-item" style="border-left: solid 1px #ccc;padding-left:8px;">
									<div class="btn btn-icon w-auto btn-clean d-flex align-items-center btn-lg px-2" id="kt_quick_user_toggle" data-cy="quick-user-toggle">
                                        {{#unless currentUser.fetchPerson.hasAvatar}}
                                            <div class="d-flex user-avatar header-avatar-circle align-items-center justify-content-center mr-1">
                                                <span>{{currentUser.fetchPerson.personInitials}}</span>
                                            </div>
                                            {{else}}
										    <div class="people-list-user-img" style="width:35px;height:35px;background-image:url({{currentUser.fetchPerson.getAvatarUrl}})"></div>
                                            {{/unless}}
										<div class="d-flex flex-column align-items-start ml-2">
											<span data-cy="current-user-name" class="text-primary font-weight-bolder font-size-base d-none d-md-inline">{{currentUser.fetchPerson.firstName}} {{currentUser.fetchPerson.lastName}}</span>
											<span data-cy="current-user-org" class="text-dark-25 font-size-sm d-none d-md-inline">{{currentUser.fetchOrg.name}}</span>
										</div>
										<span class="svg-icon svg-icon-primary fad-regular fad-secondary fad fa-ellipsis-v ml-8"></span>
									</div>
								</div>

								<!--end::User-->
							</div>

							<!--end::Topbar-->
						</div>

						<!--end::Container-->
					</div>

					<!--end::Header-->
</template>
					<!--end::Header-->
