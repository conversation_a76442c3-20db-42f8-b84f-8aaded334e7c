import $ from 'jquery';
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal } from '../../app/main';
import './_switchSite.html';
import { apiService } from '../../services/apiService';

async function fetchSwitchableSites(self) {
    const org = Orgs.current();
    const person = Meteor.user()?.fetchPerson();

    if (!org || !person) {
        console.error("Missing org or person details.");
        return;
    }

    try {
        const json = await apiService.get(`/api/switchable-sites?org=${org._id}&person=${person._id}`);
        self.orgList.set(json);
    } catch (error) {
        console.error("Error fetching switchable sites:", error.message);
    }
}

async function switchOrganization(orgId) {
    if (!orgId) {
        mpSwal.fire("Error", "Please select an organization.", "warning");
        return;
    }

    try {
        await apiService.post('/api/v2/switch-org', { orgId });
        location.replace("/my-site");
    } catch (error) {
        mpSwal.fire("Error", error.message, "error");
    }
}

Template._switchSite.created = function() {
    var self = this;
    self.orgList = new ReactiveVar(Session.get('switchableSites') || []);
    fetchSwitchableSites(self);
};

Template._switchSite.rendered = function() {
    $("#orgsList").select2({
        selectionCssClass: "form-control form-control-lg form-control-solid",
        placeholder: 'Select an org',
        width: '400px'
    });

    $('#orgsList').on('select2:select', (e) => {
        if (e.params?.originalEvent?.type !== 'mouseup') {
            doSwitch();
        }
    });
};

Template._switchSite.helpers({
    "orgs": function() {
        return Template.instance().orgList.get();
    }
});

Template._switchSite.events({
    'click #btnSwitchSite': function(event) {
        event.preventDefault();
        doSwitch();
        hideModal('#_manageEnrollLink');
    }
});

function doSwitch() {
    const org = document.getElementById('orgsList').value;
    if (!org) {
        console.error("No organization selected.");
        return;
    }
    switchOrganization(org);
}