import { Template } from 'meteor/templating';
import { Orgs } from "../../lib/collections/orgs";
import './help.html';

Template.help.events({
	"click #goToSupport": function(e, i) {
		var user = Meteor.user();
		const org = user && user.fetchOrg(), person = user && user.fetchPerson(), personType = person && person.type;
		let accountType = (personType == "family" ? "family" : "provider");
		var _hsq = window._hsq = window._hsq || [];
			_hsq.push(["identify",{
					app_full_name: `${person.firstName} ${person.lastName}`,
					app_account_type: accountType,
					app_account_level: person.type,
					app_initital_user: person.initialOrgUser || false,
					email: person.getEmailAddress(),
					person_id: person._id,
					org_id: org._id,
					org_name: org.name,
			}]);
			_hsq.push(['setPath', '/help']);
			_hsq.push(['trackPageView']);

		setTimeout(function(){window.open(Meteor.settings.public.supportSite, "_blank");}, 500)
	},
});
Template.help.helpers({
	"getLearningPathLink": () => {
		return Session.get("learningPathLink");
	},
	"showDemoRequest": () => {
		return Orgs.current() && Orgs.current().hasActiveTrial();
	}
});
