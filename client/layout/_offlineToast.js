import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Syncs } from '../../lib/collections/syncs';
import './_offlineToast.html';

Template.offlineToast.events({
	"click #reconnect": function(event) {
		event.preventDefault();
		Meteor.reconnect();
	}
});
Template.offlineToast.helpers({
	"filesQueueCount": function() {
		return (Syncs && Syncs.find().fetch().length);
	}
});
Template.offlineToast.rendered = function() {
	setTimeout(function() {
		$("#offline-toast-wrapper").show();
	}, 10000);
};
