<template name="offlineToast">
	<div class="d-flex align-items-center justify-content-center connection-status-wrapper" style='display:none' id='offline-toast-wrapper'>
    <div class="row col-md-12 align-items-center py-2">
      <div class="col-md-8">
        MomentPath is offline {{#if filesQueueCount}} ({{filesQueueCount}} files waiting to send) {{/if}}
      </div>
      <div class="col-md-4">
        <button id="reconnect" class="btn btn-default font-weight-bolder">Re-connect</button>
      </div>
    </div>
  </div>
</template>
