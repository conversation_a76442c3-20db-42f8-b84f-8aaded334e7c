export class Validation {
    static validate(type, value) {
        if (!value) {
            return true;
        }
        if (type === 'currency') {
            // courtesy https://stackoverflow.com/a/354276
            return value.match(/^\$?-?([1-9]{1}[0-9]{0,2}(,\d{3})*(\.\d{0,2})?|[1-9]{1}\d{0,}(\.\d{0,2})?|0(\.\d{0,2})?|(\.\d{1,2}))$|^-?\$?([1-9]{1}\d{0,2}(,\d{3})*(\.\d{0,2})?|[1-9]{1}\d{0,}(\.\d{0,2})?|0(\.\d{0,2})?|(\.\d{1,2}))$|^\(\$?([1-9]{1}\d{0,2}(,\d{3})*(\.\d{0,2})?|[1-9]{1}\d{0,}(\.\d{0,2})?|0(\.\d{0,2})?|(\.\d{1,2}))\)$/);
        }
        return false;
    }

    static normalize(type, value) {
        if (!value) {
            return;
        }
        if (type === 'currency') {
            return value.replace(/[^0-9.]/,'');
        }
        return value;
    }

    static msg(type, desc) {
        if (type === 'currency') {
            return `${desc} must be a valid monetary amount.`;
        }
        return `${desc} has an invalid value.`;
    }
}