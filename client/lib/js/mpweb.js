showme = function (elem)
{
  if ($(elem).data("hasvideo") && 1==0 ) {
    var curPlayer = $(elem).siblings(".video-player");
    if (curPlayer) {
      curPlayer[0].load();
      curPlayer.fadeIn();
    }
  }
  else {
    $(elem).fadeIn();
  }
  $(elem).parent().parent().find(".timelinePhotoSpinner").hide();
};

imgError = function(image) {
  $(image).hide();
  if (!image.hasOwnProperty('retryCount')){
    image.retryCount = 0;
    $(image).siblings(".timelinePhotoSpinner").show();
  }

  if (image.retryCount < 20){
    var retryTimeout = (image.retryCount < 5) ? 2000 : 5000;
    setTimeout(function (){
        var oldImgSrc = image.src;
        if (oldImgSrc.indexOf("?")>=0) oldImgSrc = oldImgSrc.substr(0, oldImgSrc.indexOf("?"));
        image.src = oldImgSrc + '?' +new Date;
        image.retryCount += 1;
    }, retryTimeout);
  }
};

// variation based on https://stackoverflow.com/a/25267697
rollupToggle = function(e, changeText = false) {
  const tgtId = e.dataset.controlId;
  const tgtRow = document.querySelector('[data-display-id="' + tgtId + '"]');
  const currentlyHidden = tgtRow.dataset.state === 'closed';
  if (currentlyHidden) {
    if (changeText) {
      e.querySelector('.toggle-text-more').classList.add('d-none');
      e.querySelector('.toggle-text-less').classList.remove('d-none');
    } else {
      e.querySelector('.fa-caret-down').classList.remove('d-none');
      e.querySelector('.fa-caret-right').classList.add('d-none');
    }
  } else {
    if (changeText) {
      e.querySelector('.toggle-text-more').classList.remove('d-none');
      e.querySelector('.toggle-text-less').classList.add('d-none');
    } else {
      e.querySelector('.fa-caret-down').classList.add('d-none');
      e.querySelector('.fa-caret-right').classList.remove('d-none');
    }
  }
  const children = document.querySelectorAll('[data-parent-id="' + tgtId + '"]');
  children.forEach((node) => {
    node.hidden = !currentlyHidden;
    rollupToggleChildren(node, currentlyHidden);
  })
  tgtRow.dataset.state = currentlyHidden ? 'open': 'closed';
}

rollupToggleChildren = function(node, show) {
  const tgtId = node.dataset.displayId;
  const children = document.querySelectorAll('[data-parent-id="' + tgtId + '"]');
  children.forEach((child) => {
    if (node.dataset.state === 'open' && !show) {
      child.hidden = true;
    }
    if (node.dataset.state === 'open' && show) {
      child.hidden = false;
    }
    rollupToggleChildren(child, show);
  })
}