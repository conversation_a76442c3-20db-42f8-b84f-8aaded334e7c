import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

export const setupRouteTracking = () => {
    let currentRoute = null;
    let routeEntryTime = null;

    try {
        if (FlowRouter) {
            // Track route entry
            FlowRouter.triggers.enter([(context) => {
                try {
                    currentRoute = context.path;
                    routeEntryTime = Date.now();

                    console.log(`[Performance Monitor] Entering route: ${currentRoute}`);

                    // Record the route visit
                    Meteor.call('recordRouteVisit', currentRoute, (error) => {
                        if (error) {
                            console.error('[Performance Monitor] Error recording route visit:', error);
                        }
                    });
                } catch (e) {
                    console.error('[Performance Monitor] Error in route enter trigger:', e);
                }
            }]);

            // Track route exit
            FlowRouter.triggers.exit([() => {
                try {
                    if (currentRoute && routeEntryTime) {
                        const duration = Date.now() - routeEntryTime;

                        // Record route duration
                        Meteor.call('recordRouteDuration', currentRoute, duration, (error) => {
                            if (error) {
                                console.error('[Performance Monitor] Error recording route duration:', error);
                            }
                        });

                        console.log(`[Performance Monitor] Exiting route: ${currentRoute}, duration: ${duration}ms`);

                        currentRoute = null;
                        routeEntryTime = null;
                    }
                } catch (e) {
                    console.error('[Performance Monitor] Error in route exit trigger:', e);
                }
            }]);

            console.log('[Performance Monitor] Route tracking initialized');
        }
    } catch (e) {
        console.error('[Performance Monitor] Error setting up route tracking:', e);
    }
};