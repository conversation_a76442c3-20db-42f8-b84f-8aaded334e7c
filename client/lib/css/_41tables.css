/* Generic LineLeader styled table */

.lineleader-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-bottom: 20px;
}

.lineleader-table th,
.lineleader-table td,
.lineleader-table thead th,
.lineleader-table tbody + tbody {
    border: 0;
}

.lineleader-table thead,
.lineleader-table tbody,
.lineleader-table tbody tr {
    border-bottom: 1px solid #E5E5EA !important;
}

.lineleader-table thead th {
    font-weight: bold;
    height: 40px;
}

.lineleader-table th,
.lineleader-table td {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    padding-left: 0.375rem !important;
    padding-right: 0.375rem !important;
    vertical-align: middle !important;
}

/* Location information tables */

@media only screen and (min-width: 1904px) {
    .lineleader-table.location-info-table .location-col-header-name {
        width: 240px;
    }
}

@media only screen and (max-width: 1903.99px) {
    .lineleader-table.location-info-table .location-col-header-name {
        width: 240px;
    }
}

@media only screen and (max-width: 1535.99px) {
    .lineleader-table.location-info-table .location-col-header-name {
        width: 200px;
    }
}

@media only screen and (max-width: 1263.99px) {
    .lineleader-table.location-info-table .location-col-header-name {
        width: 225px;
    }
}

@media only screen and (min-width: 1904px) {
    .lineleader-table.location-info-table .location-col-header-street {
        width: 180px;
    }
}

@media only screen and (max-width: 1903.99px) {
    .lineleader-table.location-info-table .location-col-header-street {
        width: 180px;
    }
}

@media only screen and (max-width: 1535.99px) {
    .lineleader-table.location-info-table .location-col-header-street {
        width: 180px;
    }
}

@media only screen and (max-width: 1263.99px) {
    .lineleader-table.location-info-table .location-col-header-street {
        width: 125px;
    }
}

.lineleader-table.location-info-table .location-col-header-city {
    width: 80px;
}

.lineleader-table.location-info-table .location-col-header-state {
    width: 55px;
}

.lineleader-table.location-info-table .location-col-header-postalcode {
    width: 55px;
}

@media only screen and (min-width: 1904px) {
    .lineleader-table.location-info-table .location-col-header-phone {
        width: 90px;
    }
}

@media only screen and (max-width: 1903.99px) {
    .lineleader-table.location-info-table .location-col-header-phone {
        width: 100px;
    }
}

@media only screen and (max-width: 1535.99px) {
    .lineleader-table.location-info-table .location-col-header-phone {
        width: 120px;
    }
}

@media only screen and (max-width: 1263.99px) {
    .lineleader-table.location-info-table .location-col-header-phone {
        width: 120px;
    }
}

@media only screen and (min-width: 1904px) {
    .lineleader-table.location-info-table .location-col-header-tuition {
        width: 50px;
    }
}

@media only screen and (max-width: 1903.99px) {
    .lineleader-table.location-info-table .location-col-header-tuition {
        width: 50px;
    }
}

@media only screen and (max-width: 1535.99px) {
    .lineleader-table.location-info-table .location-col-header-tuition {
        width: 60px;
    }
}

@media only screen and (max-width: 1263.99px) {
    .lineleader-table.location-info-table .location-col-header-tuition {
        width: 70px;
    }
}

@media only screen and (min-width: 1904px) {
    .lineleader-table.location-info-table .location-col-header-reporting-lvl, .lineleader-table.location-info-table .location-col-header-timezone {
        width: 140px;
    }
}

@media only screen and (max-width: 1903.99px) {
    .lineleader-table.location-info-table .location-col-header-reporting-lvl, .lineleader-table.location-info-table .location-col-header-timezone {
        width: 140px;
    }
}

@media only screen and (max-width: 1535.99px) {
    .lineleader-table.location-info-table .location-col-header-reporting-lvl, .lineleader-table.location-info-table .location-col-header-timezone {
        width: 120px;
    }
}

@media only screen and (max-width: 1263.99px) {
    .lineleader-table.location-info-table .location-col-header-reporting-lvl, .lineleader-table.location-info-table .location-col-header-timezone {
        width: 120px;
    }
}

.lineleader-table.location-info-table .location-col-header-status {
    width: 65px;
}

.lineleader-table.location-info-table .location-col-header-action {
    width: 70px;
}

