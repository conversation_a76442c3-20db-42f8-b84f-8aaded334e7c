.registration-flow-wrapper
{
    background-color: white;
}

.registration-flow-wrapper .btn
{
    font-weight: bold;
}

.registration-flow-wrapper .btn-secondary:not(#btnRemovePlan),
.registration-flow-wrapper .btn-secondary:not(#btnRemovePlan):hover
{
    color: red !important;
}

.registration-flow-wrapper input[type=text]
{
    background-color: white !important;
}

.registration-flow-wrapper input.is-valid
{
    border-color: var(--success) !important;
}

.registration-flow-wrapper .select-single.is-valid,
.registration-flow-wrapper .select-single.is-invalid
{
    background-position: right 1.5rem center !important;
}

.registration-flow-wrapper input.is-invalid
{
    border-color: var(--danger) !important;
}

.registration-flow-wrapper .form-control,
.registration-flow-wrapper input[type="text"]:not(.search-box-dynamic)
{
    background-color: var(--secondary) !important;
}

.registration-flow-wrapper .fad
{
    color: var(--primary);
}

.registration-flow-wrapper .registration-card:not(.active)
.registration-flow-wrapper .registration-card:not(.selective-weeks)
{
    background-color: var(--lighter-primary) !important;
}

.registration-flow-wrapper .registration-card.active
{
    background-color: var(--light-primary) !important;
}

.registration-flow-wrapper .registration-card.selective-weeks
{
    background-color: white !important;
}

.registration-flow-wrapper .registration-card.selective-weeks .week-card:not(.active)
{
    background-color: var(--lighter-primary) !important;
}

.registration-flow-wrapper .registration-card.selective-weeks .week-card.active
{
    background-color: var(--light-primary) !important;
}

{
    background-color: white !important;
}


.big-checkbox {
    width: 20px;
    height: 20px;
}

#cancellationReasonsList {
    list-style: none;
}

.program-details {
    border-bottom: 1px solid black;
    margin: 0em 0em 1em 0em;
    padding: 0em;
    height: auto;
}