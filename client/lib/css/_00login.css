.login.login-1 .login-signin,
.login.login-1 .login-signup,
.login.login-1 .login-payment-statement,
.login.login-1 .login-forgot {
  display: none; }

.login.login-1.login-signin-on .login-signup {
  display: none; }

.login.login-1.login-signin-on .login-signin {
  display: block; }

.login.login-1.login-signin-on .login-forgot {
  display: none; }

.login.login-1.login-signin-on .login-payment-statement {
  display: none; }

.login.login-1.login-signup-on .login-signup {
  display: block; }

.login.login-1.login-signup-on .login-signin {
  display: none; }

.login.login-1.login-signup-on .login-payment-statement {
  display: none; }

.login.login-1.login-signup-on .login-forgot {
  display: none; }

.login.login-1.login-forgot-on .login-signup {
  display: none; }

.login.login-1.login-forgot-on .login-signin {
  display: none; }

.login.login-1.login-forgot-on .login-payment-statement {
  display: none; }

.login.login-1.login-forgot-on .login-forgot {
  display: block; }

.login.login-1.login-payment-statement-on .login-signup {
  display: none; }

.login.login-1.login-payment-statement-on .login-signin {
  display: none; }

.login.login-1.login-payment-statement-on .login-forgot {
  display: none; }

.login.login-1.login-payment-statement-on .login-payment-statement {
  display: block; }

@media (min-width: 992px) {
  .login.login-1 .login-aside {
    width: 100%; }
  .login.login-1 .login-content {
    width: 100%;
    max-width: 500px; }
  .login.login-1 .login-content .login-form {
      width: 100%; } }

@media (max-width: 991.99px) {
  .login.login-1 .login-aside {
    width: 100%;
    max-width: 100% !important;
    min-height: 50%; }
  .login.login-1 .login-content .login-form {
    width: 100%;
    max-width: 400px; } }

@media (max-width: 575.98px) {
  .login.login-1 .login-content .login-form {
    width: 100%;
    max-width: 100%; } }

.login-top-left-panel {
  background-color: #AC52DB;
  max-height: 50%;}
  @media only screen and (max-width: 1534.99px) {
    .login-top-left-panel {
      max-height: 45% } }
  @media only screen and (max-width: 991.99px) {
    .login-top-left-panel {
      max-height: 40% } }

.login-bottom-left-panel {
  max-height: 50%;
  background-color: #100037;
  background-image: url(/media/illustrations/ll_pattern.svg);
  background-size: 50%; }
  @media only screen and (max-width: 1534.99px) {
    .login-bottom-left-panel {
      max-height: 55%;
      background-size: 60%; } }
  @media only screen and (max-width: 1263.99px) {
    .login-bottom-left-panel {
      background-size: 70%; } }
  @media only screen and (max-width: 991.99px) {
    .login-bottom-left-panel {
      max-height: 60%;
      background-size: 25%; } }
  @media only screen and (max-width: 767.99px) {
    .login-bottom-left-panel {
      max-height: 60%;
      background-size: 30%; } }
  @media only screen and (max-width: 575.99px) {
    .login-bottom-left-panel {
      background-size: 40%; } }

.panel {
  color: white;
  padding-bottom: 48px; }
  @media only screen and (max-width: 991.99px) {
    .panel {
      padding-bottom: 24px; } }
  @media only screen and (max-width: 767.99px) {
    .panel {
      padding-bottom: 12px; } }
  @media only screen and (max-width: 575.99px) {
    .panel {
      padding-bottom: 6px; } }

.panel-title {
  font-size: 44px;
  font-weight: 500 !important;
  line-height: 44px; }
  @media only screen and (max-width: 991.99px) {
    .panel-title {
      font-size: 34px;
      line-height: 34px; } }
  @media only screen and (max-width: 767.99px) {
    .panel-title {
      font-size: 24px;
      line-height: 24px; } }


.panel-subtitle {
  font-size: 14px;
  letter-spacing: 0.12rem;
  line-height: 1.8rem; }

.login-btn {
  background-color: #4921DE; 
  border-color: #4921DE;
  color: #FFFFFF !important;
  font-size: 12px !important;
  font-weight: bolder !important;
  letter-spacing: 0.07rem !important;
  line-height: normal;
  height: 36px !important;
  min-width: 64px !important;
  min-height: 36px !important; }

.login-btn:hover {
  color: #FFFFFF !important;
  background-color: var(--dark-primary) !important;
  border-color: var(--dark-primary) !important; }

.login-banner {
  line-height: 2.5rem !important;
  letter-spacing: 0.0125em !important; }

.login-text {
  letter-spacing: 0.06rem;
  color: var(--primary) !important; }

.login-text:hover {
  opacity: 0.75 !important;
}