import ApexCharts from 'apexcharts';
import { KTUtil, KTApp } from '../app/bundles/KTlibs.js';
import _ from '../../lib/util/underscore.js';
export class RenderApexCharts {
	static initApexPieChart (passedOptions) {
		var element = document.getElementById(passedOptions.elementId);
		var height = parseInt(KTUtil.css(element, 'height'));

		var options = {
			series: [57, 43],
			height,
			chart: {
				width: 380,
				type: 'pie',
			},
			labels: ['Open', 'Collected'],
			responsive: [{
				breakpoint: 480,
				options: {
					chart: {
						width: 200
					},
					legend: {
						position: 'bottom'
					}
				}
			}],
			colors: [
				KTApp.getSettings()['colors']['theme']['base']['info'],
				KTApp.getSettings()['colors']['theme']['base']['danger'],
				KTApp.getSettings()['colors']['theme']['base']['success'],
				KTApp.getSettings()['colors']['theme']['base']['primary']
			],
			
		};

		var chart = new ApexCharts(element, options);
		chart.render();
	}
	static initApexBarChart (passedOptions) {
		const element = document.getElementById(passedOptions.elementId);
		const height = parseInt(KTUtil.css(element, 'height'));

		if (!element) {
			return;
		}
		

		const options = {
			chart: {
				type: 'bar',
				height,
				toolbar: {
					show: false
				},
				id: passedOptions.elementId,
				stacked: !!passedOptions.item.dataSeries
			},
			plotOptions: {
				bar: {
					horizontal: !passedOptions.item.verticalBars,
					dataLabels: {
						position: "top"
					}
				}
			},
			legend: {
				show: false
			},
			dataLabels: {
				enabled: true,
				style: {
					colors: ['#333']
				},
				horizontal: true,
				formatter: passedOptions.item.dataSeries ? function (_val, opt) {
					let series = opt.w.config.series
					let idx = opt.dataPointIndex
					const total = series.reduce((total, self) => total + self.data[idx], 0)
					return passedOptions.item.isCurrency ? numeral(total).format("$0,000") : value;
				} : function (value) {
					return passedOptions.item.isCurrency ? numeral(value).format("$0,000") : value;
				},
				offsetY: passedOptions.item.verticalBars ? -20 : -2,
				offsetX: !passedOptions.item.verticalBars ? 30 : 0,
			},
			grid: {
				padding: {
					top: -10,
					bottom: -10
				},
				borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],
                strokeDashArray: 4,
                yaxis: {
                    lines: {
                        show: true
                    }
                }
			},
			xaxis: {
				axisTicks: {
					show: false,
				},
				axisBorder: {
					show: false,
				},
				labels: {
					show: passedOptions.item.showLabels || false,
					style: {
						colors: KTApp.getSettings()['colors']['gray']['gray-500'],
						fontSize: '12px',
						fontFamily: KTApp.getSettings()['font-family']
					}
				}
			},
			yaxis: {
				axisTicks: {
					show: false,
				},
				axisBorder: {
					show: false,
				},
				labels: {
					formatter: passedOptions.item.verticalBars && function (value) {
					  return passedOptions.item.isCurrency ? numeral(value).format("$0,000") : value;
					}
				}
			},
			colors: [KTApp.getSettings()['colors']['theme']['base']['primary']]
		};
		if (passedOptions.item.dataSeries) {
			const categories = passedOptions.item.dataSeries[0]?.data?.map(di => di.label) || [];
			options["xaxis"]["categories"] = categories;

			if (categories.length > 25) {
				// Rotate labels if there are more than 25 categories
				options["xaxis"]["labels"]["rotate"] = -45;
				options["xaxis"]["labels"]["rotateAlways"] = true;

			} else {
				// Revert to default label settings when there are fewer categories
				options["xaxis"]["labels"]["rotate"] = 0;
				options["xaxis"]["labels"]["rotateAlways"] = false;
			}

			options["series"] = passedOptions.item.dataSeries.map(series => ({
				name: series.name,
				data: _.map(series.data, di => di.amount)
			}));
			options["dataLabels"]["enabledOnSeries"] = [options["series"].length - 1];
		} else {
			options["xaxis"]["categories"] = _.map(passedOptions.item.dataItems, di => di.label);
			options["series"] = [{
				name:"",
				data: _.map(passedOptions.item.dataItems, di => di.amount)
			}];
		}
		

		const existsAlready = $(`#${passedOptions.elementId} .apexcharts-canvas`).length > 0;
		if (existsAlready) {
			ApexCharts.exec(passedOptions.elementId, 'updateOptions', options);
		} else {
			const chart = new ApexCharts(element, options);
			chart.render();
		}
	}
	static initApexLineChart (passedOptions) {
		
		var element = document.getElementById(passedOptions.elementId);
		var height = parseInt(KTUtil.css(element, 'height'));

		if (!element) {
			return;
		}
		
		var options = {
			series: [{
				name: "",
				data: _.map(passedOptions.item.dataItems, di => di.amount)
			}],
			grid: {
				padding: {
					top: 5
				}
			},
			chart: {
				type: 'area',
				height: height,
				toolbar: {
					show: false
				},
				zoom: {
					enabled: false
				},
				sparkline: {
					enabled: true
				},
				id: passedOptions.elementId
			},
			plotOptions: {},
			legend: {
				show: false
			},
			dataLabels: {
				enabled: false
			},
			fill: {
				type: 'gradient',
				opacity: 1,
				gradient: {
					type: "vertical",
					shadeIntensity: 0.5,
					gradientToColors: undefined,
					inverseColors: true,
					opacityFrom: 0.6,
					opacityTo: 0.15,
					stops: [25, 100],
					colorStops: []
				}
			},
			stroke: {
				curve: 'smooth',
				show: true,
				width: 3,
				colors: [KTApp.getSettings()['colors']['theme']['base']['primary']]
			},
			xaxis: {
				categories: _.map(passedOptions.item.dataItems, di => di.label),
				axisBorder: {
					show: false,
				},
				axisTicks: {
					show: false
				},
				labels: {
					show: false,
					style: {
						colors: KTApp.getSettings()['colors']['gray']['gray-500'],
						fontSize: '12px',
						fontFamily: KTApp.getSettings()['font-family']
					}
				},
				crosshairs: {
					show: false,
					position: 'front',
					stroke: {
						color: KTApp.getSettings()['colors']['gray']['gray-300'],
						width: 1,
						dashArray: 3
					}
				},
				tooltip: {
					enabled: true,
					formatter: undefined,
					offsetY: 0,
					style: {
						fontSize: '12px',
						fontFamily: KTApp.getSettings()['font-family']
					}
				}
			},
			yaxis: {
				min: 0,
				//max: 65,
				labels: {
					show: false,
					style: {
						colors: KTApp.getSettings()['colors']['gray']['gray-500'],
						fontSize: '12px',
						fontFamily: KTApp.getSettings()['font-family']
					}
				}
			},
			states: {
				normal: {
					filter: {
						type: 'none',
						value: 0
					}
				},
				hover: {
					filter: {
						type: 'none',
						value: 0
					}
				},
				active: {
					allowMultipleDataPointsSelection: false,
					filter: {
						type: 'none',
						value: 0
					}
				}
			},
			tooltip: {
				style: {
					fontSize: '12px',
					fontFamily: KTApp.getSettings()['font-family']
				},
				y: {
					formatter: function (val) {
						return val
					}
				}
			},
			colors: [KTApp.getSettings()['colors']['theme']['base']['primary']],
			markers: {
				colors: [KTApp.getSettings()['colors']['theme']['light']['primary']],
				strokeColor: [KTApp.getSettings()['colors']['theme']['base']['primary']],
				strokeWidth: 3
			}
		};

		const existsAlready = $(`#${passedOptions.elementId} .apexcharts-canvas`).length > 0;
		if (existsAlready) {
			ApexCharts.exec(passedOptions.elementId, 'updateOptions', options);
		} else {
			var chart = new ApexCharts(element, options);
			chart.render();
		}
	}

	static initApexVerticalBar = function (passedOptions) {
		var element = document.getElementById(passedOptions.elementId);

		if (!element) {
			return;
		}

		var options = {
			series: [{
				name: 'Invoiced',
				data: [40, 50, 65, 70, 50, 30]
			}, {
				name: 'Received',
				data: [-30, -40, -55, -60, -40, -20]
			}],
			chart: {
				type: 'bar',
				stacked: true,
				height: 350,
				toolbar: {
					show: false
				}
			},
			plotOptions: {
				bar: {
					horizontal: false,
					columnWidth: ['12%'],
					endingShape: 'rounded'
				},
			},
			legend: {
				show: false
			},
			dataLabels: {
				enabled: false
			},
			stroke: {
				show: true,
				width: 2,
				colors: ['transparent']
			},
			xaxis: {
				categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
				axisBorder: {
					show: false,
				},
				axisTicks: {
					show: false
				},
				labels: {
					style: {
						colors: KTApp.getSettings()['colors']['gray']['gray-500'],
						fontSize: '12px',
						fontFamily: KTApp.getSettings()['font-family']
					}
				}
			},
			yaxis: {
				min: -80,
				max: 80,
				labels: {
					style: {
						colors: KTApp.getSettings()['colors']['gray']['gray-500'],
						fontSize: '12px',
						fontFamily: KTApp.getSettings()['font-family']
					}
				}
			},
			fill: {
				opacity: 1
			},
			states: {
				normal: {
					filter: {
						type: 'none',
						value: 0
					}
				},
				hover: {
					filter: {
						type: 'none',
						value: 0
					}
				},
				active: {
					allowMultipleDataPointsSelection: false,
					filter: {
						type: 'none',
						value: 0
					}
				}
			},
			tooltip: {
				style: {
					fontSize: '12px',
					fontFamily: KTApp.getSettings()['font-family']
				},
				y: {
					formatter: function (val) {
						return val
					}
				}
			},
			colors: ["#c199d4", "#34C759"],
			grid: {
				borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],
				strokeDashArray: 4,
				yaxis: {
					lines: {
						show: true
					}
				}
			}
		};

		const existsAlready = $(`#${passedOptions.elementId} .apexcharts-canvas`).length > 0;
		if (existsAlready) {
			ApexCharts.exec(passedOptions.elementId, 'updateOptions', options);
		} else {
			var chart = new ApexCharts(element, options);
			chart.render();
		}
	}

	}
