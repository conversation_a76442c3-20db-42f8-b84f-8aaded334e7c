import _ from "../../lib/util/underscore";

export class ReportProfileMethods {
  constructor(
    selectedTypes,
    selectedPersonType,
    selectedOrgs,
    collapsedProfileFieldListLocalType,
    selectedChildFields,
    collapsedProfileFieldListPerson
  ) {
    this.selectedTypes = selectedTypes;
    this.selectedPersonType = selectedPersonType;
    this.selectedOrgs = selectedOrgs;
    this.collapsedProfileFieldListLocalType = collapsedProfileFieldListLocalType;
    this.selectedChildFields = selectedChildFields;
    this.collapsedProfileFieldListPerson = collapsedProfileFieldListPerson;
  }

  getColumnList() {
    const { selectedTypes, selectedPersonType, selectedOrgs, collapsedProfileFieldListLocalType, selectedChildFields } =
      this;
    const columns = _.chain(collapsedProfileFieldListLocalType)
      .filter((f) => _.contains(selectedTypes, f.prefixedName))
      .sortBy('prefixedLabel')
      .value();

    if (selectedPersonType === 'familyAndChild') {
      const childColumns = this.getChildColumns();
      columns.push(...childColumns);

      if (selectedChildFields.includes('fcRelationship')) {
        columns.unshift({ prefixedName: 'child:fcRelationship', prefixedLabel: 'Relationship' });
      }

      if (selectedChildFields.includes('childId')) {
        columns.unshift({ prefixedName: 'childId', prefixedLabel: 'Child ID Number' });
      }
    }

    if (selectedTypes.includes('familyChildren')) {
      columns.unshift({ prefixedName: 'familyChildren', prefixedLabel: 'Child(ren)' });
    }

    if (selectedTypes.includes('orgId')) {
      columns.unshift({ prefixedName: '_id', prefixedLabel: 'ID Number' });
    }

    if (selectedOrgs.length > 1) {
      columns.unshift({ prefixedName: 'orgName', prefixedLabel: 'Org' });
    }
    console.log(columns, typeof columnList);

    return columns;
  }

  getChildColumns() {
    const { selectedChildFields, collapsedProfileFieldListPerson } = this;
    const childColumns = collapsedProfileFieldListPerson
      .filter((f) => selectedChildFields.includes(f.prefixedName))
      .map((f) => ({ prefixedName: 'child:' + f.prefixedName, prefixedLabel: 'Child ' + f.prefixedLabel }));
    childColumns.sort((a, b) => a.prefixedLabel.localeCompare(b.prefixedLabel));
    return childColumns;
  }
}
