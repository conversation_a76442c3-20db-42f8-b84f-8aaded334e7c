module.exports = {
    servers: {
        one: {
            host: '<SCHEDULER_EC2_HOST>',
            username: 'ec2-user',
            pem: 'scheduler.pem'
        }
    },

    app: {
        name: 'production-scheduler',
        path: '../',

        servers: {
            one: {}
        },

        buildOptions: {
            serverOnly: true,
        },

        env: {
            ROOT_URL: 'https://production-scheduler.momentpath.com',
            MONGO_URL: '<MONGO_URL>',
            TOOL_NODE_FLAGS: '--max-old-space-size=4096',
            MONGO_OPLOG_URL:'<MONGO_OPLOG_URL>'
        },

        docker: {
            image: 'zodern/meteor:latest',
        },
        log: {
            driver: 'awslogs',
            opts: {
                'awslogs-region': 'us-east-1',
                'awslogs-group': 'MPProdScheduler'
            }
        },

        // Show progress bar while uploading bundle to server
        // You might need to disable it on CI servers
        enableUploadProgressBar: true
    },
};
