#!/usr/bin/env bash

# Script: AppConfig Version Label Retriever and Hosting Checker
# Description: This script retrieves the VersionLabel of an AWS AppConfig configuration,
#              checks if it's hosted, and shows where it's hosted if not.

# Exit immediately if a command exits with a non-zero status
set -e

# Set default values for AWS AppConfig identifiers

# Default version label
DEFAULT_VERSION_LABEL="1"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for required commands
for cmd in aws jq; do
    if ! command_exists "$cmd"; then
        echo "Error: $cmd is required but not installed. Aborting."
        exit 1
    fi
done

# Function to handle errors
handle_error() {
    echo "Error: $1" >&2
    exit 1
}

echo "Starting AppConfig Version Label retrieval and hosting check..."

# Start a new configuration session
echo "Starting configuration session..."
envconfig=$(aws appconfigdata start-configuration-session \
    --application-identifier "$AC_APP_ID" \
    --configuration-profile-identifier "$AC_CONFIG_ID" \
    --environment-identifier "$AC_ENV_ID") || handle_error "Failed to start configuration session"

# Extract the initial configuration token
token=$(echo "$envconfig" | jq -r '.InitialConfigurationToken') || handle_error "Failed to extract initial token"

# Fetch the latest configuration
echo "Fetching latest configuration..."
config_response=$(aws appconfigdata get-latest-configuration --configuration-token "$token" settings.json) || handle_error "Failed to fetch latest configuration"

# Extract the NextPollConfigurationToken from the response
next_token=$(echo "$config_response" | jq -r '.NextPollConfigurationToken') || handle_error "Failed to extract next token"

# Use the next token to fetch the version label
echo "Retrieving version label..."
version_label=$(aws appconfigdata get-latest-configuration \
    --configuration-token "$next_token" \
    --query "VersionLabel" \
    --output text versionlabel.txt) || handle_error "Failed to retrieve version label"

# Check if version_label is empty, "None", or null and set default if necessary
if [ -z "$version_label" ] || [ "$version_label" = "None" ] || [ "$version_label" = "null" ]; then
    echo "Version label is empty, None, or null. Setting default value."
    version_label="$DEFAULT_VERSION_LABEL"
fi

echo "The current configuration version label is: $version_label"

# Retrieve the configuration profile details
echo "Retrieving configuration profile details..."
profile_details=$(aws appconfig get-configuration-profile \
        --application-id "$AC_APP_ID" \
        --configuration-profile-id "$AC_CONFIG_ID") || handle_error "Failed to get configuration profile details"
    
# Extract the LocationUri
location_uri=$(echo "$profile_details" | jq -r '.LocationUri') || handle_error "Failed to extract LocationUri"
    
echo "The configuration is hosted at: $location_uri"

echo "Script execution completed successfully."
