## ECS Deployment Infrastructure
Documentation for deployed AWS resources

### Docker Containers

#### manage-meteor
[manage-meteor](https://us-east-1.console.aws.amazon.com/ecr/repositories/private/799420973518/manage-meteor?region=us-east-1) contains hosted `web` and `scheduler` images created during build phase of code pipeline execution

#### manage-nginx
[manage-nginx](https://us-east-1.console.aws.amazon.com/ecr/repositories/private/799420973518/manage-nginx?region=us-east-1) registers a sidecar container in the web deployments ECS task definition. It provides healthcheck capabilities for target group placement and reverse proxies all requests to the running meteor continer  

You can build this container from the top level `.nginx` directory with the following commands

```sh
# from the .nginx directory
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 799420973518.dkr.ecr.us-east-1.amazonaws.com
docker build -t manage-ngnix . 
docker tag manage-nginx:latest 799420973518.dkr.ecr.us-east-1.amazonaws.com/manage-nginx:production
docker push 799420973518.dkr.ecr.us-east-1.amazonaws.com/manage-nginx:production
```

#### manage-nodejs
[manage-nodejs](https://us-east-1.console.aws.amazon.com/ecr/repositories/private/799420973518/manage-nodejs?region=us-east-1) serves as the base image for node installation to prevent network egress errors over dockerhub image pull requests during build time  

You can build this container within the `nodejs` directory with the following commands
```sh
# from the nodejs directory
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 799420973518.dkr.ecr.us-east-1.amazonaws.com
docker build -t manage-nodejs . 
docker tag manage-nodejs:latest 799420973518.dkr.ecr.us-east-1.amazonaws.com/manage-nodejs:v14
docker push 799420973518.dkr.ecr.us-east-1.amazonaws.com/manage-nodejs:v14
```

### Production WEB Resources

[ECS Cluster](https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/production-manage/services?region=us-east-1)  
[ECS Task Definition](https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions/production-manage-meteor?status=ACTIVE&region=us-east-1)  
[EC2 Autoscaling Group](https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#AutoScalingGroupDetails:id=Infra-ECS-Cluster-production-manage-22a334ec-ECSAutoScalingGroup-12J1SYYHUO4EM;view=details)  
[EC2 Target Group](https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#TargetGroup:targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:799420973518:targetgroup/production-manage-ecs/d41dfff69f558b46)  
[Application Load Balancer](https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#LoadBalancer:loadBalancerArn=arn:aws:elasticloadbalancing:us-east-1:799420973518:loadbalancer/app/production-manage-ecs/9c51b444a072e8d3;tab=listeners)  
[App Config](https://us-east-1.console.aws.amazon.com/systems-manager/appconfig/applications/xdnufj3/environments/0bsav27?region=us-east-1)  
[CloudWatch Log Group](https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/$252Fecs$252Fproduction-manage-meteor)  

### Production SCHEDULER Resources

[ECS Cluster](https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/production-scheduler/services?region=us-east-1)  
[ECS Task Definition](https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions/production-manage-scheduler?status=ACTIVE&region=us-east-1)  
[EC2 Autoscaling Group](https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#AutoScalingGroupDetails:id=Infra-ECS-Cluster-production-scheduler-96de8ee1-ECSAutoScalingGroup-1K5K6TPYBMFHR;view=details)  
[App Config](https://us-east-1.console.aws.amazon.com/systems-manager/appconfig/applications/xdnufj3/environments/bwmw8h4?region=us-east-1)  
[CloudWatch Log Group](https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/$252Fecs$252Fproduction-manage-scheduler)

### Deployment via CodePipeline
Pipelines connect to Bitbucket as the source. Source branch can be managed within each pipeline's settings.  

Build steps provide additional ENVIRONMENT VARIABLES that define the App Config to pull and the build-tag for ECR docker image push.

You can start a pipeline from the AWS command line with

```sh
# scheduler pipeline command
aws codepipeline start-pipeline-execution --name manage-production-scheduler

# web pipeline command
aws codepipeline start-pipeline-execution --name manage-production
```

[web pipeline](https://us-east-1.console.aws.amazon.com/codesuite/codepipeline/pipelines/manage-production/view?region=us-east-1)  
`web pipeline` deploys 50% of containers at-a-time

[scheduler pipeline](https://us-east-1.console.aws.amazon.com/codesuite/codepipeline/pipelines/manage-production-scheduler/view?region=us-east-1)  
`scheduler pipeline` deploys the single container all-at-once meaning there is a brief downtime which matches the current beanstalk deployment environment
