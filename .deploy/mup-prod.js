module.exports = {
    app: {
        // Tells mup that the AWS Beanstalk plugin will manage the app
        type: 'aws-beanstalk',
        name: '<MUP_NAME>',
        path: '../',
        env: {
            NODE_OPTIONS: '<NODE_OPTIONS>',
            SERVER_NODE_OPTIONS: '<NODE_OPTIONS>',
            ROOT_URL: '<APP_URL>',
            MONGO_URL: '<MONGO_URL>',
            MONGO_OPLOG_URL: '<MONGO_OPLOG_URL>',
            TOOL_NODE_FLAGS: '<TOOL_NODE_FLAGS>',
            HTTP_FORWARDED_COUNT: "2"
        },
        auth: {
            id: '<AWS_ELB_ACCESS_KEY>',
            secret: "<AWS_ELB_ACCESS_SECRET>"
        },
        longEnvVars: true,
        minInstances: <ELB_MIN_INSTANCES>,
        instanceType: '<ELB_INSTANCE_TYPE>',
        sslDomains: ['*.momentpath.com'],
        forceSSL: true,
        keyName: '<AWS_KEY_PAIR_NAME>', // Add your AWS Key Pair name here
        customBeanstalkConfig: [
                {
                    namespace: 'aws:elasticbeanstalk:command',
                    option: 'DeploymentPolicy',
                    value: 'RollingWithAdditionalBatch'
                },
            {
                    namespace: 'aws:elasticbeanstalk:cloudwatch:logs',
                    option: 'StreamLogs',
                    value: 'true'
            },
            {
                    namespace: 'aws:autoscaling:launchconfiguration',
                    option: 'EC2KeyName',
                    value: '<AWS_KEY_PAIR_NAME>' // Add the key pair for EC2 instances
        }
        ]
    },
    plugins: ['mup-aws-beanstalk']
};
