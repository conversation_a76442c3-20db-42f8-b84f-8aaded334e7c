{
    "debug": false,
  "accountingEmail": "<ACCOUNTING_EMAIL>",
  "awsBucket": "my-example-staging",
  "AWSAccessKeyId": "<TENDLY_IAM_KEY>",
  "AWSSecretAccessKey": "<TENDLY_IAM_SECRET>",
  "mpAWSaccessKey": "<AWS_ACCESS_KEY_ID>",
  "mpAWSsecretKey": "<AWS_SECRET_ACCESS_KEY>",
  "AWSRegion": "<AWS_REGION>",
  "awsBillingKey": "",
  "awsBillingSecret": "",
  "awsBillingRegion": "<AWS_BILLING_REGION>",
  "awsBillingEndpoint": "",
  "singleInvoiceQueueUrl": "<SINGLE_INVOICE_QUEUE_URL>",
    "singleInvoiceLambdaName": "<SINGLE_INVOICE_LAMBDA_NAME>",
    "payInvoiceUrl": "<PAY_INVOICE_URL>",
  "invoiceOrgQueueUrl": "<INVOICE_ORG_QUEUE_URL>",
  "autoPayQueueUrl": "<AUTOPAY_QUEUE_URL>",
  "completeAdyenBalancePlatformOnboardingUrl": "<COMPLETE_ADYEN_BALANCE_PLATFORM_ONBOARDING_URL>",
  "billingOrgsFunctionName": "<BILLING_ORGS_FUNCTION_NAME>",
  "cognitoUserPoolId": "<COGNITO_USER_POOL_ID>",
  "cognitoAccessKey": "<COGNITO_ACCESS_KEY_ID>",
  "cognitoClientId": "<COGNITO_CLIENT_ID>",
  "cognitoSecretKey": "<COGNITO_SECRET_KEY_ID>",
  "cognitoEndpoint": "",
  "cognitoRegion": "<COGNITO_REGION>",
  "kmsKeyId": "<KMS_KEY_ID>",
  "kmsAccessKey": null,
  "kmsSecretKey": null,
  "kmsRegion": "<KMS_REGION>",
  "kmsEndpoint": "",
  "kmsSigningAlgo": "<KMS_SIGNING_ALGO>",
  "kmsAlgoId": "<KMS_ALGO_ID>",
  "mpEntityKey": "<MP_ENTITY_KEY>",
  "mailchimpKey": "<MAILCHIMP_KEY>",
  "mpSMSQueueUrl": "https://sqs.us-east-1.amazonaws.com/799420973518/sms-processing-queue.fifo",
  "mpEventBridgeQueueUrl": "https://sqs.us-east-1.amazonaws.com/799420973518/production_eventbridge.fifo",
  "mpMomentEventBridgeQueueUrl": "https://sqs.us-east-1.amazonaws.com/799420973518/production_moments_eventbridge.fifo",
  "wmgPresenceUrl": "https://presence.internal.watchmegrow.com",
  "mpEventBridgeMessageGroup": "production",
  "defaultOriginationNumber": "18336591278",
  "childcareCrmUrl": "https://live.childcarecrm.com",
  "childcareCrmUIUrl": "https://my.childcarecrm.com/#",
  "idpBaseUrl": "https://login.lineleader.com",
  "idpClientSecret": "<IDP_CLIENT_SECRET>",
  "kinderConnectUrl": "https://ccbis.mo.gov/WSCtecMobile/CtecMobileService.svc",
  "adpEmail1": "",
  "adpEmail2": "",
  "rightAtSchoolUrl": "<RAS_URL>",
  "rightAtSchoolWebhookUrl": "<RAS_WEBHOOK_URL>",
  "zkTecoUrl": "<ZKTECO_URL>",
  "zkTecoApiToken": "<ZKTECO_KEY>",
  "mobileApiKey": "<MOBILE_API_KEY>",
  "intercomSecret": "<INTERCOM_SECRET>",
  "lspHubspotKey": "<LSP_HUBSPOT_KEY>",
  "public": {
      "debug": false,
    "supportSite": "https://help-beta.momentpath.com/knowledge",
    "AWSAccessKeyId": "<TENDLY_IAM_KEY>",
    "stripe": {
      "publishableKey": "<STRIPE_PUBLISHABLE_KEY>"
    },
    "awsBucketUrl": "https://my-meteor-example.s3.amazonaws.com",
    "environment": "staging",
    "photoBaseUrl": "https://cfm.momentpath.com/",
    "adyen": {
      "environment": "<ADYEN_ENV>",
      "clientKey": "<ADYEN_CLIENT_KEY>",
      "environmentPrefix": "<ADYEN_ENV_PREFIX>"
    },
    "whitelabel": {
      "enabled_sites": [
        "lightbridge",
        "connect",
        "luvnotes",
        "mysunshinehouse",
        "myfoundations",
        "myquestzone",
        "dayearlylearning",
        "cnmoments",
        "rightatschool",
        "mswmoments",
        "spsmoments",
        "dramaticed",
        "kidsinthegame",
        "discover",
        "ikids",
        "apec",
        "my"
      ],
      "lightbridge": {
        "large_logo": "/img/lb_logo-white.svg",
        "small_logo": "/img/lb_icon-white.svg",
        "header_logo": "https://assets.momentpath.com/customers/lightbridge/summaryemailv2019/logo.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#218ACA"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#218ACA"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#8BC53D"
          },
          {
            "template": "--dark-primary",
            "color": "#0e3852"
          },
          {
            "template": "--light-primary",
            "color": "#b8d2e3"
          },
          {
            "template": "--lighter-primary",
            "color": "#dbf1ff"
          }
        ],
        "sessionVars": [
          {
            "name": "wmgLabel",
            "value": "ParentView® powered by WatchMeGrow"
          }
        ]
      },
      "connect": {
        "large_logo": "/img/bklogo.svg",
        "small_logo": "/img/bklogo.svg",
        "header_logo": "https://assets.momentpath.com/customers/buildingkidz/bk.png",
        "title": "BuildingKidz Connect",
        "flex_key" : "<FLEXMONSTER_BK_LICENSE_KEY>",
        "favicon": "/bkfav.ico",
        "colors": [
          {
            "template": "--primary",
            "color": "#560F2D"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#560F2D"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#939598"
          },
          {
            "template": "--dark-primary",
            "color": "#0E3852"
          },
          {
            "template": "--light-primary",
            "color": "#DDCFD5"
          },
          {
            "template": "--lighter-primary",
            "color": "#EFE7EA"
          }
        ],
        "sessionVars": [
          {
            "name": "learningPathLink",
            "value": "https://training.buildingkidzschool.com"
          }
        ]
      },
      "luvnotes": {
        "large_logo": "/img/lsp_icon.png",
        "small_logo": "/img/lsp_icon.png",
        "header_logo": "https://assets.momentpath.com/customers/littlesunshine/luvnotes.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#538177"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#538177"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#C67D6D"
          },
          {
            "template": "--dark-primary",
            "color": "#09473a"
          },
          {
            "template": "--light-primary",
            "color": "#a6ded2"
          },
          {
            "template": "--lighter-primary",
            "color": "#cffcf3"
          }
        ],
        "sessionVars": [
          {
            "name": "eduTitle",
            "value": "Red Carpet Experience"
          }
        ]
      },
      "mysunshinehouse": {
        "large_logo": "/img/sshouse.png",
        "small_logo": "/img/sshouse.png",
        "header_logo": "https://assets.momentpath.com/customers/sshouse/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#41748D"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#41748D"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#54585A"
          },
          {
            "template": "--dark-primary",
            "color": "#012b40"
          },
          {
            "template": "--light-primary",
            "color": "#bde0f2"
          },
          {
            "template": "--lighter-primary",
            "color": "#cfecfa"
          }
        ],
        "sessionVars": []
      },
      "myfoundations": {
        "large_logo": "/img/foundations.png",
        "small_logo": "/img/foundations.png",
        "header_logo": "https://assets.momentpath.com/customers/foundations/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#2A317D"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#2A317D"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#81BD41"
          },
          {
            "template": "--dark-primary",
            "color": "#000430"
          },
          {
            "template": "--light-primary",
            "color": "#989feb"
          },
          {
            "template": "--lighter-primary",
            "color": "#c9ceff"
          }
        ],
        "sessionVars": []
      },
      "myquestzone": {
        "large_logo": "/img/questzone.png",
        "small_logo": "/img/questzone.png",
        "header_logo": "https://assets.momentpath.com/customers/questzone/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#4D008C"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#4D008C"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#F18A00"
          },
          {
            "template": "--dark-primary",
            "color": "#10011c"
          },
          {
            "template": "--light-primary",
            "color": "#b98fdb"
          },
          {
            "template": "--lighter-primary",
            "color": "#e4c5fc"
          }
        ],
        "sessionVars": []
      },
      "dayearlylearning": {
        "large_logo": "/img/dayearlylearning.png",
        "small_logo": "/img/dayearlylearning.png",
        "header_logo": "https://assets.momentpath.com/customers/dayearlylearning/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#2A6AA6"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#0C2338"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#EC7D30"
          },
          {
            "template": "--dark-primary",
            "color": "#0C2338"
          },
          {
            "template": "--light-primary",
            "color": "#5D9BD3"
          },
          {
            "template": "--lighter-primary",
            "color": "#7ABCF7"
          }
        ],
        "sessionVars": []
      },
      "cnmoments": {
        "large_logo": "/img/cnmoments.png",
        "small_logo": "/img/cnmoments.png",
        "header_logo": "https://assets.momentpath.com/customers/cnmoments/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#007DC3"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#007DC3"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#FFC425"
          },
          {
            "template": "--dark-primary",
            "color": "#01314D"
          },
          {
            "template": "--light-primary",
            "color": "#59B5EB"
          },
          {
            "template": "--lighter-primary",
            "color": "#ADDCF7"
          }
        ],
        "sessionVars": []
      },
      "rightatschool": {
        "large_logo": "/img/ras.png",
        "small_logo": "/img/ras.png",
        "header_logo": "https://assets.momentpath.com/customers/ras/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#0086AB"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#0086AB"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#6DBABE"
          },
          {
            "template": "--dark-primary",
            "color": "#023a4a"
          },
          {
            "template": "--light-primary",
            "color": "#25CAF7"
          },
          {
            "template": "--lighter-primary",
            "color": "#A1EBFF"
          }
        ],
        "sessionVars": []
      },
      "mswmoments": {
        "large_logo": "/img/msw.png",
        "small_logo": "/img/msw.png",
        "header_logo": "https://assets.momentpath.com/customers/msw/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#3C1053"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#3C1053"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#307FE2"
          },
          {
            "template": "--dark-primary",
            "color": "#33004F"
          },
          {
            "template": "--light-primary",
            "color": "#B556A8"
          },
          {
            "template": "--lighter-primary",
            "color": "#DDADF7"
          }
        ],
        "sessionVars": []
      },
      "spsmoments": {
        "large_logo": "/img/sps.png",
        "small_logo": "/img/sps.png",
        "title": "SPS Moments",
        "favicon": "/spsfav.ico",
        "header_logo": "https://assets.momentpath.com/customers/sps/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#05BDDE"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#FFB500"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#FFB500"
          },
          {
            "template": "--dark-primary",
            "color": "#FFB500"
          },
          {
            "template": "--light-primary",
            "color": "#05BDDE"
          },
          {
            "template": "--lighter-primary",
            "color": "#dfeff7"
          }
        ],
        "sessionVars": []
      },
      "dramaticed": {
        "large_logo": "/img/dramatic.png",
        "small_logo": "/img/dramatic.png",
        "title": "Dramatic Education",
        "favicon": "/dramatic.ico",
        "header_logo": "https://assets.momentpath.com/customers/dramatic/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#d92727"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#81BD41"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#FFB500"
          },
          {
            "template": "--dark-primary",
            "color": "#992222"
          },
          {
            "template": "--lighter-primary",
            "color": "rgba(226, 226, 226, 0.71)"
          },
          {
            "template": "--info",
            "color": "#dfeff7"
          }
        ],
        "sessionVars": []
      },
      "kidsinthegame": {
        "large_logo": "/img/kig.png",
        "small_logo": "/img/kig.png",
        "title": "Kids in the Game",
        "favicon": "/kigfav.ico",
        "header_logo": "https://assets.momentpath.com/customers/kidsinthegame/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#0025FF"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#31B1FF"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#31B1FF"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#31B1FF"
          },
          {
            "template": "--dark-primary",
            "color": "#31B1FF"
          },
          {
            "template": "--danger",
            "color": "#31B1FF"
          }
        ]
      },
      "discover": {
        "large_logo": "/img/discover.png",
        "small_logo": "/img/discover.png",
        "title": "Discover After School",
        "favicon": "/discover.ico",
        "header_logo": "https://assets.momentpath.com/customers/discover/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#03989e"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#ff7201"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#ff7201"
          },
          {
            "template": "--dark-primary",
            "color": "#ff7201"
          },
          {
            "template": "--light-primary",
            "color": "#ffd41d"
          },
          {
            "template": "--light-primary",
            "color": "#ffd41d"
          }
        ],
        "sessionVars": []
      },
      "ikids": {
        "large_logo": "/img/ikids.png",
        "small_logo": "/img/ikids.png",
        "title": "iKids U",
        "favicon": "/ikidsfav.ico",
        "header_logo": "https://assets.momentpath.com/customers/ikids/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#007bff"
          },
          {
            "template": "--secondary",
            "color": "#E6252B"
          },
          {
            "template": "--danger",
            "color": "#E6252B"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#E6252B"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#8BC540"
          },
          {
            "template": "--dark-primary",
            "color": "#0861cf"
          }
        ]
      },
      "apec": {
        "large_logo": "/img/apec.png",
        "small_logo": "/img/apec.png",
        "title": "APEC Family Foundation",
        "favicon": "/apec.ico",
        "header_logo": "https://assets.momentpath.com/customers/apec/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#FFD700"
          },
          {
            "template": "--dark-primary",
            "color": "#a18903"
          },
          {
            "template": "--dark-primary",
            "color": "#a18903"
          },
          {
            "template": "--white",
            "color": "#000"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#000"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#000"
          }
        ]
      },
      "my":  {
        "large_logo": "/img/champions.png",
        "small_logo": "/img/champions.png",
        "title": "Discover Champions",
        "favicon": "/champions.ico",
        "header_logo": "https://assets.momentpath.com/customers/champions/header.png",
        "colors": [
          {
            "template": "--primary",
            "color": "#EB0202"
          },
          {
            "template": "--people-ratio-template-color",
            "color": "#EB0202"
          },
          {
            "template": "--people-ratio-staff-template-color",
            "color": "#00BCDF"
          },
          {
            "template": "--dark-primary",
            "color": "#EB0202"
          },
          {
            "template": "--light-primary",
            "color": "#F15858"
          },
          {
            "template": "--light-primary",
            "color": "#F36C6C"
          }
        ]
      }
    },
    "rollbarClientToken": "<ROLLBAR_CLIENT>",
    "flexmonster": {
      "key" : "<FLEXMONSTER_LICENSE_KEY>",
      "componentFolderUrl" : "https://cdn.flexmonster.com/"
    }
  },
  "stripe": {
    "secretKey": "<STRIPE_SECRET_KEY>"
  },
  "tinymceKey": "<TINYMCE_API_KEY>",
  "rollbarServerToken": "<ROLLBAR_SERVER>",
  "TWILIO_ACCOUNT_SID": "<TWILIO_ACCOUNT_SID>",
  "TWILIO_AUTH_TOKEN": "<TWILIO_AUTH_TOKEN>",
  "TWILIO_NUMBER": "+***********",
  "adyen": {
    "apiKey": "<ADYEN_API_KEY>",
    "balancePlatformApiKey": "<ADYEN_BALANCE_PLATFORM_API_KEY>",
    "legalEntityManagementApiKey": "<ADYEN_LEGAL_ENTITY_MANAGEMENT_API_KEY>",
    "adyenAccountId": "<ADYEN_ACCOUNT_ID>",
    "liableBalanceAccountId": "<ADYEN_LIABLE_BALANCE_ACCOUNT_ID>",
    "paymentMethods": "visa,mc,amex,discover",
    "salesChannels": "eCommerce",
    "notificationUsername": "<ADYEN_NOTIFY_USERNAME>",
    "notificationPassword": "<ADYEN_NOTIFY_PASSWORD>",
    "mpapipw": "<ADYEN_MANAGE_API_PW>",
    "mpapiuser": "<ADYEN_MANAGE_API_USER>",
    "reportingPassword": "<ADYEN_REPORT_PW>",
    "reportingUsername": "<ADYEN_REPORT_USER>",
    "defaultAccountCode": "<ADYEN_DEFAULT_ACCOUNT_CODE>",
      "reportDownloadUrlPrefix": "<ADYEN_REPORT_DOWNLOAD_URL_PREFIX>",
      "transferFundsUrl": "<ADYEN_TRANSFER_FUNDS_URL>"
  },
  "monti": {
    "appId": "<MONTI_APP_ID>",
    "appSecret": "<MONTI_APP_SECRET>"
  },
  "transloaditKey": "<TRANSLOADIT_KEY>",
  "suppressJobs": <SUPPRESS_JOBS>,
  "applicationJobs": <APPLICATION_JOBS>,
  "airSlate": {
    "bucketLambdaApiKey":"<AIRSLATE_LAMBDA_API_KEY>",
    "url": "https://bots.airslate.com"
  },
  "emails": {
        "billingErrors": "<BILLING_ERRORS_EMAIL>",
        "groupSyncErrors": "<GROUP_SYNC_ERRORS_EMAIL>",
        "checkinAsyncAlert":"<CHECKIN_ASYNC_ALERT_EMAIL>"
  },
  "KinderConnect":{
    "notificationEmail":"<KINDERCONNECT_NOTIFICATION_EMAIL>"
  },
  "redshift" : {
    "dbHost": "<REDSHIFT_DB_HOST>",
    "dbPort": <REDSHIFT_DB_PORT>,
    "dbName": "<REDSHIFT_DB_NAME>",
    "dbUser": "<REDSHIFT_DB_USER>",
    "dbPassword": "<REDSHIFT_DB_PASSWORD>"
  },
  "redisOplog": {
		"redis": {
			"cluster": true,
			"port": 6379,
			"host": "<REDIS_HOST>",
			"password": "<REDIS_AUTH_TOKEN>",
			"nodes": [
				{
				"host": "<REDIS_HOST>",
				"port": 6379
				}
			],
			"options": {
				"redisOptions": {
				"tls": {
					"rejectUnauthorized": true,
					"servername": "<REDIS_HOST>"
				},
				"password": "<REDIS_AUTH_TOKEN>",
				"maxRetriesPerRequest": 50
				},
				"enableOfflineQueue": true
			}
		},
		"debug": "<REDIS_DEBUG>",
		"logLevel": "debug",
		"logPrefix": "RedisSubscriptionManager",
		"mutationDefaults": {
		"optimistic": true,
		"pushToRedis": true
		}
	}
}
