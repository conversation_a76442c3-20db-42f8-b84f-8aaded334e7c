module.exports = {
	app: {
	  // Tells mup that the AWS Beanstalk plugin will manage the app
	  type: 'aws-beanstalk',
	  name: '<BEANSTALK_NAME>',
	  path: '../',
	  env: {
	    ROOT_URL: '<ROOT_URL>',
		NODE_OPTIONS: '<NODE_OPTIONS>',
        SERVER_NODE_OPTIONS: '<NODE_OPTIONS>',
        TOOL_NODE_FLAGS: '<TOOL_NODE_FLAGS>',
		MONGO_URL: '<MONGO_URL_TEST>',
		HTTP_FORWARDED_COUNT: "2"
	  },
	  auth: {
		id: '<AWS_DEPLOY_KEY>',
		secret: '<AWS_DEPLOY_SECRET>'
	  },
	  longEnvVars: true,
	  minInstances: 1,
	  sslDomains: ['*.momentpath.com'],
	  forceSSL: true,
	  instanceType: '<INSTANCE_TYPE>',
	  keyName: '<AWS_KEY_PAIR_NAME>', // Add your AWS Key Pair name here
	  customBeanstalkConfig: [{
		  namespace: 'aws:elasticbeanstalk:command',
		  option: 'DeploymentPolicy',
		  value: 'AllAtOnce'
		},
		{
		  namespace: 'aws:elasticbeanstalk:cloudwatch:logs',
		  option: 'StreamLogs',
		  value: 'true'
		},
		{
          namespace: 'aws:autoscaling:launchconfiguration',
          option: 'EC2KeyName',
          value: '<AWS_KEY_PAIR_NAME>' // Add the key pair for EC2 instances
        }
	  ]
	},
	plugins: ['mup-aws-beanstalk']
  };

