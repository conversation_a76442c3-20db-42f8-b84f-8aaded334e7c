import * as FoodTypes from "./foodTypes";
import moment from 'moment-timezone';
import { Groups } from "./groups";
import { Orgs } from "./orgs";
import _ from '../../lib/util/underscore';
import { Mongo } from 'meteor/mongo';

const Food = function (doc) {
	/** @type {Food} doc */
	_.extend(this, doc);
};

_.extend(
	Food.prototype,
	{
		sendToAll: function () {
			return (!this.selectedGroups || this.selectedGroups.length === 0);
		},
		//This proto method used only in client side so no mongo call changes requires for this method
		findRecipientGroups: function () {
			return Groups.find({ _id: { $in: this.selectedGroups } }).fetch();
		},
		processedDescription() {
			return (this.foodItems || []).length > 0 ?
				this.foodItems.join(', ') :
				this.description;
		}
	}
);

/** @type {Collection} */
export const Foods = new Mongo.Collection(
	'food',
	{
		transform: function (doc) {
			/** @type {Food} doc */
			return new Food(doc);
		}
	}
);

const getQuery = (options, org) => {
	const timezone = org.getTimezone();
	let query = options.query || {};
	let rangeStart = options.startDate ?
		new moment.tz(options.startDate, 'MM/DD/YYYY', timezone).startOf('day').valueOf() :
		new moment.tz(timezone).startOf('day').valueOf();
	let rangeEnd = options.endDate ?
		new moment.tz(options.endDate, 'MM/DD/YYYY', timezone).startOf('day').valueOf() :
		new moment.tz(timezone).add(1, 'day').startOf('day').valueOf();

	// Synced Cron passes in own values for the food query.
	if (options.startDateValue) {
		rangeStart = options.startDateValue;
	}

	if (options.endDateValue) {
		rangeEnd = options.endDateValue;
	}

	const rangeQuery = {
		'$or': [
			{
				recurringFrequency: { $exists: true },
				'$or': [
					{ scheduledEndDate: { $exists: false } },
					{ scheduledEndDate: '' },
					{ scheduledEndDate: { '$gte': rangeStart } }
				]
			},
			{ scheduledDate: { '$gte': rangeStart, '$lt': rangeEnd } }
		]
	};

	if (query['$and']) {
		query['$and'].push(rangeQuery);
	} else if (!_.isEmpty(query)) {
		query = { '$and': [rangeQuery, query] };
	} else {
		query = rangeQuery;
	}
	return {query,rangeStart,rangeEnd};
}
const findWithRecurrence = (options) => {
	const foods = options.foods;
	const rangeStart = options.rangeStart;
	const rangeEnd = options.rangeEnd;
	const timezone = options.timezone;
	const numWeeks = new moment(rangeEnd).diff(new moment(rangeStart), 'week');
	const weekdays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
	let projectedFoods = [];
	_.each(
		_.filter(
			foods, (f) => {
				return f.recurringFrequency;
			}
		),
		(f) => {
			for (let i = 0; i <= numWeeks; ++i) {
				const weeksSince = new moment
					.tz(rangeStart, timezone)
					.startOf('week')
					.diff(new moment.tz(f.scheduledDate, timezone).startOf('week'), 'week');
				if ((weeksSince + i >= 0) && (weeksSince + i) % f.recurringFrequency === 0) {
					for (let j = 0; j < _.size(f.recurringDays); ++j) {
						const d = f.recurringDays[j];
						const newFoodObj = { ...f };
						const newFood = new Food(newFoodObj);
						const dayIndex = _.indexOf(weekdays, d);

						newFood.scheduledDate = new moment
							.tz(rangeStart, timezone)
							.add(i, 'weeks')
							.startOf('week')
							.add(dayIndex, 'days')
							.valueOf();
						newFood.originalId = newFood._id;
						newFood._id = Random.id();

						const existingRealFood = _.find(
							foods,
							(checkFood) => {
								return checkFood.scheduledDate === newFood.scheduledDate &&
									checkFood.recurrenceId === newFood.originalId &&
									!checkFood.recurringFrequency;
							}
						);

						if (
							!existingRealFood &&
							newFood.scheduledDate >= f.scheduledDate &&
							(!newFood.scheduledEndDate || newFood.scheduledDate <= f.scheduledEndDate) &&
							newFood.scheduledDate >= rangeStart &&
							newFood.scheduledDate < rangeEnd &&
							!_.contains(f.recurringExceptions, newFood.scheduledDate)
						) {
							projectedFoods.push(newFood);
							//console.log('pushed', newFood);
						}
					}
				}
			}
		}
	);

	projectedFoods = projectedFoods.concat(
		_.filter(
			foods,
			(f) => {
				return !f.recurringFrequency;
			}
		)
	);
	return _.sortBy(projectedFoods, 'scheduledDate');
}

_.extend(
	Foods,
	{
		'findWithRecurrence': (options) => {
			if(Meteor.isServer){
				return (async ()=>{
					const org = (options.query && options.query.orgId)
					? await Orgs.findOneAsync(options.query.orgId)
					: await Orgs.current()
					const {query,rangeStart,rangeEnd} = getQuery(options,org);
					let foods = await Foods.find(query, options.options).fetchAsync();
					options.foods = foods;
					options.rangeStart = rangeStart;
					options.rangeEnd = rangeEnd;
					options.timezone = org.getTimezone();
					return findWithRecurrence(options);
				})();
			} else {
				const org = (options.query && options.query.orgId)
				? Orgs.findOne(options.query.orgId)
				: Orgs.current()
				const {query,rangeStart,rangeEnd} = getQuery(options,org);
				let foods = Foods.find(query, options.options).fetch();
				options.foods = foods;
				options.rangeStart = rangeStart;
				options.rangeEnd = rangeEnd;
				options.timezone = org.getTimezone();
				return findWithRecurrence(options);
			}
		}
	}
);
