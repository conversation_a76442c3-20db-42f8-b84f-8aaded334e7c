const moment = require('moment-timezone');
import { Mongo } from "meteor/mongo";
import _ from "../util/underscore";
import { People } from "./people";
import { Moments } from "./moments";

const TimeCard = function (doc) {
  _.extend(this, doc)
};

_.extend(TimeCard.prototype, {
  async name() {
    const p = await People.findOneAsync({ _id: this.personId });
    if (p) return `${p.firstName} ${p.lastName}`;
    return "";
  },
  payTypeLabel() {
    //TODO: build out
    return "Standard";
  }
});

export const TimeCards = new Mongo.Collection('timeCards', {
  transform: function (doc) {
    return new TimeCard(doc);
  }
});

/*

// One day we'll use TypeScript. For now, here's the signature:

interface Options {
  startDate?: string;
  endDate?: string;
  peopleIds?: string[];
  confirmedFilter?: boolean;
}

*/

TimeCards.queryWithOptions = async function (org, defaultOptions) {
  const options = _.extend({}, defaultOptions);
  const query = { orgId: org._id };

  if (options.startDate) {
    const startRange = new moment.tz(options.startDate, "MM/DD/YYYY", "utc").startOf('day').valueOf();
    query.timeCardDateStamp = { $gte: startRange };
    if (options.endDate) {
      const endRange = new moment.tz(options.endDate, "MM/DD/YYYY", "utc").endOf('day').valueOf();
      query.timeCardDateStamp["$lt"] = endRange
    }
  }

  const hasChildFilter = org.hasCustomization("integration/kinderConnect/enabled");
  const peopleQuery = { orgId: org._id, type: { $in: ["staff", "admin"] }, inActive: { $ne: true } };

  if (hasChildFilter) {
    delete peopleQuery.type;
  }

  const peopleIds = (options.peopleIds && options.peopleIds.length > 0) ? options.peopleIds : await People.find(peopleQuery, { fields: { _id: 1 } }).mapAsync(p => p._id);
  query.personId = { $in: peopleIds };

  if (options.confirmedFilter) {
    query.timeCardConfirmed = { $ne: true };
  }
  if(options.autoCheckoutFilter) {
    query['originalCheckOutMoment.createdBy'] = 'SYSTEM';
    query['updateLog'] = { $not: { $elemMatch: { modifiedDoc: 'TimeCard' }}}
  }

  if (options.isEditedFilter) {
    query['updateLog'] = { $elemMatch: { modifiedDoc: 'TimeCard' }};
  }
  if (options.autoCheckoutFilter && options.isEditedFilter) {
    return [];
  }

  return TimeCards.find(query);
}

/*

// One day we'll use TypeScript. For now, here's the signature:

interface VoidWithReasonOptions {
  timeCardId: string;
  voidedByUserId: any;
  voidedByPersonName: string;
  voidReason?: string;
}

*/

TimeCards.voidWithReason = async function (options) {
  // Should we get the TimeCard by ID to make sure this is valid?

  if (!options.timeCardId) {
    throw new Meteor.Error("timeCardId is required");
  }

  if (!options.voidedByUserId) {
    throw new Meteor.Error("voidedByUserId is required");
  }

  if (!options.voidedByPersonName) {
    throw new Meteor.Error("voidedByPersonName is required");
  }

  await TimeCards.updateAsync({
    _id: options.timeCardId
  }, {
    $set: {
      void: true,
      voidedByUser: options.voidedByUserId,
      voidedByPersonName: options.voidedByPersonName,
      voidReason: options.voidReason ? options.voidReason : null,
      voidedAt: new Date().valueOf()
    }
  });
}


/*

// One day we'll use TypeScript. For now, here's the signature:

interface ApproveForPersonOptions {
  orgId: string;
  personId: string;
  timecardIds: string[];
  approvedByPersonId: string;
}

*/

TimeCards.approveForPerson = async function (options) {

  if (!options.orgId) {
    throw new Meteor.Error("orgId is required");
  }

  if (!options.personId) {
    throw new Meteor.Error("personId is required");
  }

  if (!options.timecardIds || !Array.isArray(options.timecardIds)) {
    throw new Meteor.Error("timecardIds must be an array");
  }

  if (!options.approvedByPersonId) {
    throw new Meteor.Error("approvedByPersonId is required");
  }

  await TimeCards.updateAsync({
    orgId: options.orgId,
    personId: options.personId,
    _id: { "$in": options.timecardIds }
  }, {
    "$set": {
      approvedAt: new Date().valueOf(),
      approvedByPersonId: options.approvedByPersonId,
      timeCardConfirmed: true
    }
  }, { multi: true });
}

/*

// One day we'll use TypeScript. For now, here's the signature:

interface UpdateTimeCardOptions {
  org: Org;
  currentPerson: Person;
  currentUser: User;
  timeCardId: string;
  checkInTime: number;
  checkOutTime: number;
  selectedPayTypeId: string;
  description: string;
}

// I don't love passing org, person, user, etc right now because this
// now feels complicated. But it's a direct lift of existing code, for now.

*/

TimeCards.updateTimeCard = async function (options) {

  if (!options.org) {
    throw new Meteor.Error("org is required");
  }

  if (!options.currentPerson) {
    throw new Meteor.Error("currentPerson is required");
  }

  if (!options.currentUser) {
    throw new Meteor.Error("currentUser is required");
  }

  if (!options.timeCardId) {
    throw new Meteor.Error("timeCardId is required");
  }

  if (!options.checkInTime) {
    throw new Meteor.Error("checkInTime is required");
  }

  if (!options.checkOutTime) {
    throw new Meteor.Error("checkOutTime is required");
  }

  if (!options.selectedPayTypeId) {
    throw new Meteor.Error("selectedPayTypeId is required");
  }

  if (!options.description) {
    throw new Meteor.Error("description is required");
  }

  const org = options.org;
  const currentPerson = options.currentPerson;
  const currentUser = options.currentUser;
  const timeCardId = options.timeCardId;
  const checkInTime = options.checkInTime;
  const checkOutTime = options.checkOutTime;
  const selectedPayTypeId = options.selectedPayTypeId;
  let description = options.description;

  const modifiedByName = `${currentPerson.firstName} ${currentPerson.lastName}`;

  const timeCard = await TimeCards.findOneAsync({ orgId: org._id, _id: timeCardId });

  if (!timeCard) {
    throw new Meteor.Error("timeCard not found");
  }

  const checkInMoment = await Moments.findOneAsync({ _id: timeCard.checkInMomentId });
  if (checkInMoment && (checkInMoment.time != checkInTime || selectedPayTypeId != checkInMoment.selectedPayTypeId)) {
    description += "checkIn moment updated: ";
    await Moments.updateAsync({ _id: timeCard.checkInMomentId, orgId: org._id }, { $set: { time: checkInTime, selectedPayTypeId, modifiedByName, modifiedBy: currentPerson._id } });
  }

  const checkOutMoment = (timeCard.checkOutMomentId) ? await Moments.findOneAsync({ _id: timeCard.checkOutMomentId }) : null;
  if (checkOutMoment && checkOutTime != checkOutMoment.time) {
    description += "checkOut moment updated: ";
    await Moments.updateAsync({ _id: timeCard.checkOutMomentId, orgId: org._id }, { $set: { time: checkOutTime, modifiedByName, modifiedBy: currentPerson._id } });
  } else if (!checkOutMoment && checkOutTime != null) {
    description += "checkOut moment created: ";
    const nowDate = new Date().valueOf();
    const newCheckOutMoment = {
      createdAt: nowDate,
      sortStamp: nowDate,
      createdBy: currentUser._id,
      createdByPersonId: currentPerson._id,
      orgId: currentUser.orgId,
      owner: timeCard.personId,
      taggedPeople: [timeCard.personId],
      momentType: "checkout",
      momentTypePretty: "Check Out",
      time: checkOutTime,
      date: timeCard.checkInDate,
      checkedOutById: currentPerson._id,
      comment: `Checked out by ${currentPerson.firstName} ${currentPerson.lastName}`,
      mood: "",
    };

    await Moments.insertAsync(newCheckOutMoment);
  }

  const set = { checkInTime, checkOutTime, selectedPayTypeId };
  if (checkOutTime && !timeCard.checkOutDate) {
    set.checkOutDate = timeCard.checkInDate;
  }
  await TimeCards.updateAsync({ _id: timeCard._id }, { $set: set });
  await TimeCards.updateAsync({ _id: timeCard._id }, {
    $addToSet: {
      updateLog: {
        checkInTime,
        checkOutTime,
        selectedPayTypeId,
        modifiedBy: currentPerson._id,
        processedTime: new moment().valueOf(),
        modifiedDoc: 'TimeCard',
        description
      }
    }
  });
}