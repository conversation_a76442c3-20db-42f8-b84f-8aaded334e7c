import _ from '../util/underscore';
import { Mongo } from 'meteor/mongo';

const CustomerBillingPlanMapping = function (doc) {
    _.extend(this, doc)
};

export const CustomerBillingPlanMappings = new Mongo.Collection('customerBillingPlanMappings', {

    /** interface: {
     * _id: string;
     * programName: string;
     * sessionTypeName: string;
     * billingPlan: string;
     }; */

    transform: function (doc) {
        return new CustomerBillingPlanMapping(doc);
    }
});