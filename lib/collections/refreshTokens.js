import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

/**
 * @typedef {Object} RefreshToken
 * @property {string} _id
 * @property {string} userId
 * @property {string} token
 * @property {number} expiresAt
 */
const RefreshToken = function(doc) {
	_.extend(this, doc)
};

export const RefreshTokens = new Mongo.Collection('refreshTokens', {
	transform: function(doc) {
		return new RefreshToken(doc);
	}
});
