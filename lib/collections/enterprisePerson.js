import _ from '../../lib/util/underscore';
import { Mongo } from 'meteor/mongo';

const EnterprisePerson = function(doc) {
    _.extend(this, doc);
};

_.extend(EnterprisePerson.prototype, {

});

export const EnterprisePeople = new Mongo.Collection('enterprisePeople', {
    transform: function(doc) {
        return new EnterprisePerson(doc);
    }
});


EnterprisePeople.findOneByPersonId = async function(personId) {
	return EnterprisePeople.findOneAsync({"orgsPeople.personId":personId});
};