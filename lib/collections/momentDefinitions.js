import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

global.MomentDefinition = function(doc) {
	_.extend(this, doc);
};

_.extend(MomentDefinition.prototype, {

});


export const MomentDefinitions = new Mongo.Collection('momentDefinitions', {
	transform: function(doc) {
		return new MomentDefinition(doc);
	}
});


_.extend(MomentDefinitions, {
	'availableForOrg': function() {
		return testDefs;
	},
	'findByMomentType': function(momentType) {
		return this.findOne({momentType: momentType});
	},
	'templateDefinition': function() {
		return templateMomentDefinition;
	}
	
});

var templateMomentDefinition = 
	{
		momentType: "",
		momentTypePretty: "",
		icon: "",
		inherits: "",
		momentFields: [
			{
				fieldType: "",
				label: "",
				dataId: "",
				inheritAction: ""	
			}
		],
		allowMediaAttachments: true,
		enabled: true,
		includesInSummaryReport: true,
		availableForRealtime: false
	}
;

var testDefs = [
	{
		"_id":"1",
		"momentType": "chargeableItems",
		"momentTypePretty": "Chargeable Item",
		icon: "fa-money",
		inherits: "",
		momentFields: [
			{
				fieldType: "select",
				label: "Item Type",
				dataId: "itemType",
				allowBlankOption: false,
				dataSource: "list",
				fieldValues: [
					{ fieldValue: "Beauty Salon", fieldValueLabel: "Beauty Salon"},
					{ fieldValue: "Shower", fieldValueLabel: "Shower"}
				]
			}
		],
		allowMediaAttachments: false,
		enabled: false,
		includesInSummaryReport: true,
		availableForRealtime: false
	},
	{
		_id: "2",
		momentType: "incidentClinical",
		momentTypePretty: "Incident (Clinical)",
		icon: "fa-exclamation-circle",
		inherits: "",
		momentFields: [
			{
				fieldType: "string",
				label: "Location",
				dataId: "location"
			},
			{
				fieldType: "text",
				label: "Description",
				dataId: "description"
			},
			{
				fieldType: "text",
				label: "First Aid Given",
				dataId: "firstAidGiven"
			},
			{
				fieldType: "peopleSelect",
				allowBlankOption: false,
				label: "Administered By",
				dataId: "administeredBy"
			},
			{
				fieldType: "select",
				allowBlankOption: true,
				multi: true,
				label: "Marking",
				dataId: "marking",
				dataSource: "list",
				fieldValues: [
					{fieldValue: "Abrasion", fieldValueLabel: "Abrasion"},
					{fieldValue: "Bite", fieldValueLabel: "Bite"},
					{fieldValue: "Bruise", fieldValueLabel: "Bruise"},
					{fieldValue: "Bump", fieldValueLabel: "Bump"},
					{fieldValue: "Cut/tear", fieldValueLabel: "Cut/tear"},
					{fieldValue: "Fracture (suspected)", fieldValueLabel: "Fracture (suspected)"},
					{fieldValue: "Red Mark", fieldValueLabel: "Red Mark"},
					{fieldValue: "Rug Burn", fieldValueLabel: "Rug Burn"},
					{fieldValue: "Scratch", fieldValueLabel: "Scratch"},
					{fieldValue: "Sprain (suspected)", fieldValueLabel: "Sprain (suspected)"}
				]
			},
			{
				fieldType: "select",
				label: "Appendage(s)",
				dataId: "appendages",
				dataSource: "list",
				multi: true,
				allowBlankOption: true,
				fieldValues: [ 
					{fieldValue:"Ankle - Left", fieldValueLabel: "Ankle - Left"}, 
					{fieldValue:"Ankle - Right", fieldValueLabel: "Ankle - Right"},
					{fieldValue:"Arm - Left", fieldValueLabel: "Arm - Left"}, 
					{fieldValue:"Arm - Right", fieldValueLabel: "Arm - Right"},
					{fieldValue:"Back - Left", fieldValueLabel: "Back - Left"}, 
					{fieldValue:"Back - Right", fieldValueLabel: "Back - Right"},
					{fieldValue:"Buttock - Left", fieldValueLabel: "Buttock - Left"}, 
					{fieldValue:"Buttock - Right", fieldValueLabel: "Buttock - Right"},
					{fieldValue:"Cheek - Left", fieldValueLabel: "Cheek - Left"}, 
					{fieldValue:"Cheek - Right", fieldValueLabel: "Cheek - Right"},
					{fieldValue:"Chin - Left", fieldValueLabel: "Chin - Left"}, 
					{fieldValue:"Chin - Right", fieldValueLabel: "Chin - Right"},
					{fieldValue:"Ear - Left", fieldValueLabel: "Ear - Left"}, 
					{fieldValue:"Ear - Right", fieldValueLabel: "Ear - Right"},
					{fieldValue:"Elbow - Left", fieldValueLabel: "Elbow - Left"}, 
					{fieldValue:"Elbow - Right", fieldValueLabel: "Elbow - Right"},
					{fieldValue:"Eye - Left", fieldValueLabel: "Eye - Left"}, 
					{fieldValue:"Eye - Right", fieldValueLabel: "Eye - Right"},
					{fieldValue:"Finger - Left", fieldValueLabel: "Finger - Left"}, 
					{fieldValue:"Finger - Right", fieldValueLabel: "Finger - Right"},
					{fieldValue:"Forehead - Left", fieldValueLabel: "Forehead - Left"}, 
					{fieldValue:"Forehead - Right", fieldValueLabel: "Forehead - Right"},
					{fieldValue:"Hand - Left", fieldValueLabel: "Hand - Left"}, 
					{fieldValue:"Hand - Right", fieldValueLabel: "Hand - Right"},
					{fieldValue:"Head - Left", fieldValueLabel: "Head - Left"}, 
					{fieldValue:"Head - Right", fieldValueLabel: "Head - Right"},
					{fieldValue:"Heel - Left", fieldValueLabel: "Heel - Left"}, 
					{fieldValue:"Heel - Right", fieldValueLabel: "Heel - Right"},
					{fieldValue:"Hip - Left", fieldValueLabel: "Hip - Left"}, 
					{fieldValue:"Hip - Right", fieldValueLabel: "Hip - Right"},
					{fieldValue:"Knee - Left", fieldValueLabel: "Knee - Left"}, 
					{fieldValue:"Knee - Right", fieldValueLabel: "Knee - Right"},
					{fieldValue:"Leg - Left", fieldValueLabel: "Leg - Left"}, 
					{fieldValue:"Leg - Right", fieldValueLabel: "Leg - Right"},
					{fieldValue:"Lips - Left", fieldValueLabel: "Lips - Left"}, 
					{fieldValue:"Lips - Right", fieldValueLabel: "Lips - Right"},
					{fieldValue:"Neck - Left", fieldValueLabel: "Neck - Left"}, 
					{fieldValue:"Neck - Right", fieldValueLabel: "Neck - Right"},
					{fieldValue:"Nose - Left", fieldValueLabel: "Nose - Left"}, 
					{fieldValue:"Nose - Right", fieldValueLabel: "Nose - Right"},
					{fieldValue:"Penis - Left", fieldValueLabel: "Penis - Left"}, 
					{fieldValue:"Penis - Right", fieldValueLabel: "Penis - Right"},
					{fieldValue:"Shoulder - Left", fieldValueLabel: "Shoulder - Left"}, 
					{fieldValue:"Shoulder - Right", fieldValueLabel: "Shoulder - Right"},
					{fieldValue:"Stomach - Left", fieldValueLabel: "Stomach - Left"}, 
					{fieldValue:"Stomach - Right", fieldValueLabel: "Stomach - Right"},
					{fieldValue:"Teeth - Left", fieldValueLabel: "Teeth - Left"}, 
					{fieldValue:"Teeth - Right", fieldValueLabel: "Teeth - Right"},
					{fieldValue:"Toe - Left", fieldValueLabel: "Toe - Left"}, 
					{fieldValue:"Toe - Right", fieldValueLabel: "Toe - Right"},
					{fieldValue:"Tongue - Left", fieldValueLabel: "Tongue - Left"}, 
					{fieldValue:"Tongue - Right", fieldValueLabel: "Tongue - Right"},
					{fieldValue:"Vagina - Left", fieldValueLabel: "Vagina - Left"}, 
					{fieldValue:"Vagina - Right", fieldValueLabel: "Vagina - Right"},
					{fieldValue:"Wrist - Left", fieldValueLabel: "Wrist - Left"}, 
					{fieldValue:"Wrist - Right", fieldValueLabel: "Wrist - Right"}
				]
			},
			{
				fieldType: "text",
				label: "Medical/Hygiene Given",
				dataId: "medicalHygieneGiven"
			},
			{
				fieldType: "text",
				label: "Medical - Desc. of Concern",
				dataId: "medicalHygieneConcern"
			},
			{
				fieldType: "text",
				label: "Behavior - Desc. of Concern",
				dataId: "behaviorConcern"
			},
			{
				fieldType: "string",
				label: "911 Called Time",
				dataId: "callTime911"
			},
			{
				fieldType: "string",
				label: "Coordinate Called Time",
				dataId: "callTimeCoordinate"
			},
			{
				fieldType: "checkbox",
				label: "Coordinate Reached",
				dataId: "reachedCoordinate"
			},
			{
				fieldType: "checkbox",
				label: "Coordinate Message Left",
				dataId: "messageLeftCoordinate"
			},
			{
				fieldType: "string",
				label: "Physician Called Time",
				dataId: "callTimePhysician"
			},
			{
				fieldType: "checkbox",
				label: "Physician Reached",
				dataId: "reachedPhysician"
			},
			{
				fieldType: "checkbox",
				label: "Physician Message Left",
				dataId: "messageLeftPhysician"
			},
			{
				fieldType: "select",
				label: "Transportation Used",
				allowBlankOption: true,
				multi: false,
				dataId: "transportationUsed",
				dataSource: "list",
				fieldValues: [
					{fieldValue: "Ambulance", fieldValueLabel: "Ambulance"}
				]
			},
			{
				fieldType: "string",
				label: "By Whom",
				dataId: "transportationByWhom"
			},
			{
				fieldType: "string",
				label: "Destination",
				dataId: "transportationDestination"
			},
			{
				fieldType: "checkbox",
				label: "PCCS Nurse Notified",
				dataId: "pccsNurseNotified"
			}
		]
	},
	{
		_id: "3",
		momentType: "neuroCheck",
		momentTypePretty: "Neuro Check",
		icon: "fa-stethoscope",
		inherits: "",
		momentFields: [
			{
				fieldType:"select",
				label: "Alert and Oriented",
				allowBlankOption: true,
				multi:true,
				dataId: "alertAndOriented",
				dataSource: "list",
				fieldValues: [
					{fieldValue:"Person", fieldValueLabel: "Person"},
					{fieldValue:"Place", fieldValueLabel: "Place"},
					{fieldValue:"Time", fieldValueLabel: "Time"}
				]
			},
			{
				fieldType: "buttons",
				label: "LOC",
				dataId: "loc",
				fieldValues: [ {fieldValue:"WNL"}, {fieldValue:"Lethargic"}]
			},
			{
				fieldType: "buttons",
				label: "Speech Clarity",
				dataId: "speechClarity",
				fieldValues: [ {fieldValue:"WNL"}, {fieldValue:"Other"}]
			},
			{
				fieldType: "buttons",
				label: "Facial Symmetry (Smile)",
				dataId: "facialSymmetry",
				fieldValues: [ {fieldValue:"WNL"}, {fieldValue:"Asymmetry"}]
			},
			{
				fieldType: "buttons",
				label: "Tongue Midline",
				dataId: "tongueMidline",
				fieldValues: [ {fieldValue:"WNL"}, {fieldValue:"Deviation to One Side"}]
			},
			{
				fieldType: "buttons",
				label: "Grasp Strength",
				dataId: "graspStrength",
				fieldValues: [ {fieldValue:"Strong and Equal"}, {fieldValue:"Weakness L"}, {fieldValue:"Weakness R"}]
			},
			{
				fieldType: "buttons",
				label: "Upper Extremities",
				dataId: "upperExtremities",
				fieldValues: [ {fieldValue:"WNL"}, {fieldValue:"Weakness L"}, {fieldValue:"Weakness R"}]
			},
			{
				fieldType: "buttons",
				label: "Lower Extremities",
				dataId: "lowerExtremities",
				fieldValues: [ {fieldValue:"WNL"}, {fieldValue:"Weakness L"}, {fieldValue:"Weakness R"}]
			},
			{
				fieldType: "buttons",
				label: "Pupil",
				dataId: "pupil",
				fieldValues: [ {fieldValue:"PERRLA"}, {fieldValue:"Unequal"}, {fieldValue:"Pinpoint"}, {fieldValue:"Nonreactive"}]
			}
		]
	},
	{
		_id: "4",
		momentType: "skinCheck",
		momentTypePretty: "Skin Check",
		icon: "fa-stethoscope",
		inherits: "",
		momentFields: [
			{
				fieldType: "buttons",
				label: "Head",
				dataId: "head",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Dry"}, {fieldValue:"Scabbed Areas"}, {fieldValue: "Red Patches"}, {fieldValue: "Missing Hair"}]
			},
			{
				fieldType: "buttons",
				label: "Ears",
				dataId: "ears",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Red/Dry Area"}, {fieldValue:"Excess Ear Wax"}, {fieldValue:"Scaly Patches"}]
			},		
			{
				fieldType: "buttons",
				label: "Eyes",
				dataId: "eyes",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Drainage"}, {fieldValue:"Puffy"}, {fieldValue: "Crust-like Material"}]
			},
			{
				fieldType: "buttons",
				label: "Nose",
				dataId: "nose",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Discharge"}, {fieldValue:"Swollen"}]
			},
			{
				fieldType: "buttons",
				label: "Mouth",
				dataId: "mouth",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Bad Breath"}, {fieldValue:"Canker Sores"}, {fieldValue:"Chapped Lips"}, {fieldValue:"Dry Tongue"}]
			},
			{
				fieldType: "buttons",
				label: "Arms",
				dataId: "arms",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Bruises"}, {fieldValue:"Dry/patchy Areas"}, {fieldValue:"Scabs"}, {fieldValue:"Red/rash Areas"}]
			},
			{
				fieldType: "buttons",
				label: "Abdomen/Breasts",
				dataId: "abdomenBreasts",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Odor"}, {fieldValue:"Red/rash Areas"}, {fieldValue:"Rigid Belly"}, {fieldValue:"Swelling"} ]
			},
			{
				fieldType: "buttons",
				label: "Peri Area - Male",
				dataId: "periAreaMale",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Discharge"}, {fieldValue:"Odor"}, {fieldValue:"Red/rash scrotum"} ]
			},
			{
				fieldType: "buttons",
				label: "Peri Area - Female",
				dataId: "periAreaFemale",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Discharge"}, {fieldValue:"Menstruating"}, {fieldValue:"Odor"}, {fieldValue:"Red/rash Area"} ]
			},
			{
				fieldType: "buttons",
				label: "Legs",
				dataId: "legs",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Bruising"}, {fieldValue:"Red/rash Area"}, {fieldValue:"Scabs"}, {fieldValue:"Swelling"} ]
			},
			{
				fieldType: "buttons",
				label: "Feet",
				dataId: "feet",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Dry/cracked Between Toes"}, {fieldValue:"Open Areas"}, {fieldValue: "Red Areas"}, {fieldValue:"Swelling"} ]
			},
			{
				fieldType: "buttons",
				label: "Back",
				dataId: "back",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Bruises"}, {fieldValue:"Open Areas"}, {fieldValue: "Red/rash Area"}]
			},
			{
				fieldType: "buttons",
				label: "Buttocks",
				dataId: "back",
				fieldValues: [ {fieldValue:"Normal"}, {fieldValue:"Open Areas"}, {fieldValue: "Red/rash Area"}]
			},
			{
				fieldType: "checkbox",
				label: "Full Body Check",
				dataId: "fullBodyCheck"
			}
		]
	},
	{
		_id: "5",
		momentType: "stopAndWatchAlert",
		momentTypePretty: "Stop/Watch Alert",
		icon: "fa-eye",
		inherits: "",
		momentFields: [
			{
				fieldType:"select",
				label: "Observation(s)",
				allowBlankOption: false,
				multi:true,
				dataId: "observations",
				dataSource: "list",
				fieldValues: [
					"Seems different than usual", "Talks or communicates less", "Overall needs more help", "Pain - new or worsening", 
					"Participate less in activities", "Ate less", "No bowel movement in 3 days or diarrhea", "Drank less", "Weight change", 
					"Agitated or nervous more than usual", "Tired, weak, confused, or drowsy", "Change in skin color or condition", 
					"Help with walking, transferring, toileting more than usual"
				]
			}
		]
	},
	{
		_id: "6",
		momentType: "bathing",
		momentTypePretty: "Bathing",
		icon: "fa-shower",
		inherits: "",
		hideComment: true,
		momentFields: [
			{
				fieldType:"select",
				label: "Showers",
				allowBlankOption: false,
				multi: false,
				dataId: "showers",
				dataSource: "list",
				fieldValues: [ "Yes", "No"]
			},
			{
				fieldType:"select",
				label: "Assistance",
				allowBlankOption: true,
				multi: false,
				dataId: "assitance",
				dataSource: "list",
				fieldValues: [ "Independent", "1 Assist", "2 Assist"]
			},
				
		]
	}
];