import _ from '../util/underscore';
import { Mongo } from 'meteor/mongo';
/**
 * Structure of ReportHashes:
 * {
 *     personId: string (the user who saved the report)
 *     name: string
 *     createdOn: datetime
 *     index: report identifier
 *     reportObject: object (the actual report object that was saved)
 * }
 *
 * @param doc
 * @constructor
 */
const ReportSave = function(doc) {
	_.extend(this, doc)
};

export const ReportSaves = new Mongo.Collection('reportSaves', {
	transform: function(doc) {
		return new ReportSave(doc);
	}
});
