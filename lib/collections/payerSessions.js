import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

/**
 * Structure of payerSessions:
 * {
 *     personId: string,
 *     orgId: string,
 *     startedTime: timestamp,
 *     startDate: string,
 *     endDate: string,
 *     payer: string
 * }
 *
 * @param doc
 * @constructor
 */
global.PayerSession = function(doc) {
	_.extend(this, doc)
};

export const PayerSessions = new Mongo.Collection('payerSessions', {
	transform: function(doc) {
		return new PayerSession(doc);
	}
});
