import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';
/**
 * Structure of ManualMigrationsLog
 * {
 *     version: number
 *     runTime: datetime
 *     orgId: string | null
 * }
 *
 * @param doc
 * @constructor
 */
global.ManualMigrationLog = function(doc) {
	_.extend(this, doc)
};

export const ManualMigrationLogs = new Mongo.Collection('manualMigrationLogs', {
	transform: function(doc) {
		return new ManualMigrationLog(doc);
	}
});
