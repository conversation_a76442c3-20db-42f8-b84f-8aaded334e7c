import { Meteor } from 'meteor/meteor';
import _ from "../util/underscore";
import { People } from "./people";
import { processPermissions } from "../permissions";
import { Orgs, Org } from "./orgs";

export const User = function (doc) {
	_.extend(this, doc);
};

_.extend(User.prototype, {
	fetchPrimaryEmail: function () {
		if (this.emails && this.emails.length > 0) {
			return this.emails[0].address;
		}
		return null;
	},
	fetchPerson: function () {
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				return await People.findOneAsync(self.personId);
			})();
		} else {
			return People.findOne(this.personId);
		}
	},
	/**Use await while using in server side */
	fetchOrg: function () {
		if(Meteor.isServer){
			return (async()=>{
				return await Orgs.findOneAsync(this.orgId);
			})()
		}else{
			return Orgs.findOne(this.orgId);
		}
	},
	//only used in client so not async changes needed
	canSeeNavItem: function (navItem) {
		//console.log("org current", Orgs.current(navItem));
		switch (navItem) {
			case "food":
				if (Orgs.current() && Orgs.current()?.hasCustomization("customer/misc/VillageProjectAfrica") ||
					Orgs.current()?.hasCustomization("modules/food/hidden")) return false;
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff")
				);
			case "announcements":
				if (Orgs.current() && Orgs.current()?.hasCustomization("customer/misc/VillageProjectAfrica")) return false;
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff")
				);
			case "curriculum":
				if (Orgs.current() && (Orgs.current()?.hasCustomization("customer/misc/VillageProjectAfrica") ||
					Orgs.current()?.hasCustomization("modules/curriculum/hidden"))) return false;
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff")
				);
			case "reservations":
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff") &&
					Orgs.current()?.hasCustomization("reservations/enabled")
				);

			case "forms":
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff") &&
					Orgs.current()?.hasCustomization("forms/enabled")
				);
			case "reports":
				return processPermissions({
					assertions: [{ context: "reports/standard", action: "read" }],
					evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff"
				});
			case "org":

				const specificAdminContext = processPermissions({
					assertions: [{ context: "admin/configuration", action: "read" }],
					evaluator: (thisPerson) => thisPerson.type == "admin"
				});

				const generalAdminContext = (!Meteor.isCordova && this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin"));

				return specificAdminContext && generalAdminContext;
			case "documents":
				return (!Meteor.isCordova && this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin")
				);
			case "inquiries":
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff") &&
					Orgs.current()?.hasCustomization("inquiries/enabled")
				);
			case "groups":
				return (this && this.fetchPerson() &&
					((this.fetchPerson())?.type == "admin" || (this.fetchPerson())?.type == "staff")
				);
			case "billing":
				return Orgs.current() && (Orgs.current()?.hasCustomization("billing/enabled") || Orgs.current()?.registrationGuided) &&
					processPermissions({
						assertions: [{ context: "billing/invoices", action: "read" }],
						evaluator: (person) => person.type == "admin"
					});
			case "calendar":
				if (Orgs.current() && Orgs.current()?.hasCustomization("customer/misc/VillageProjectAfrica")) return false;
				return true;
			case "timeCards":
				return (this && this.fetchPerson() && (this.fetchPerson())?.type == "admin");
			default:
				return true;
		}
	},
	//availableNavItems is never used in code base
	availableNavItems: async function () {
		let currentUser = await Meteor.userAsync();
		if (currentUser && await Orgs.current()){
			return _.filter((await Orgs.current()).availableNavItems(), async function (i) { return currentUser?.canSeeNavItem(i.route); });
		}else {
			return [];
		}
	}
});

Meteor.users._transform = function (user) {
	return new User(user);
};