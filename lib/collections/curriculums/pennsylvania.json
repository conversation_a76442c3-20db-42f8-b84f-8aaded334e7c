[{"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Explore books in a variety of ways.", "standardId": "1.1 I.A."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Demonstrate interest in books that have color, pattern, and contrast.", "standardId": "1.1 I.B."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Respond to sounds in the environment", "standardId": "1.1 I.C."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Attend to a picture in a text when reading with an adult.", "standardId": "1.2 I.B."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Use single words to identify family members and familiar objects.", "standardId": "1.2 I.J."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Engage in reading activities", "standardId": "1.2 I.L."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Use single words to identify family members and familiar objects.", "standardId": "1.3 I.J."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Engage in reading activities.", "standardId": "1.3 I.K."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Make marks with writing and drawing tools.", "standardId": "1.4 I.R."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Use sounds and gestures as a form of reciprocal communication.", "standardId": "1.5 I.A."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Respond in ways that indicate understanding of what is being communicated.", "standardId": "1.5 I.C."}, {"ageGroup": "Infant", "category": "Language and Literacy Development", "benchmark": "Babble and begin to use single words and/or signs.", "standardId": "1.5 I.D/E."}, {"ageGroup": "Infant", "category": "Mathematical Thinking and Expression", "benchmark": "Explore objects.", "standardId": "2.1 I.A.1."}, {"ageGroup": "Infant", "category": "Mathematical Thinking and Expression", "benchmark": "Explore relationships between objects.", "standardId": "2.1 I.A.3."}, {"ageGroup": "Infant", "category": "Mathematical Thinking and Expression", "benchmark": "Engage in numerical play", "standardId": "2.1 I.MP."}, {"ageGroup": "Infant", "category": "Mathematical Thinking and Expression", "benchmark": "Explore objects of different sizes and shapes", "standardId": "2.3 I.A.1."}, {"ageGroup": "Infant", "category": "Mathematical Thinking and Expression", "benchmark": "Engage in geometric play.", "standardId": "2.3 I.MP."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Imitate use of various technologies in play.", "standardId": "15.4 I.A."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Explore pictures and objects that represent workplace technologies.", "standardId": "15.4 I.M."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Show interest in the natural world.", "standardId": "3.1 I.A.1."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Explore parts of living things in their environment.", "standardId": "3.1 I.A.5."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of living and nonliving things.", "standardId": "3.1 I.A.9."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Recognize self and family members.", "standardId": "3.1 I.B.1."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of physical characteristics of living things.", "standardId": "3.1 I.B.6."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Explore objects with varying characteristics.", "standardId": "3.2 I.A.1."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Observe activities involving changes in matter.", "standardId": "3.2 I.A.3."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of matter.", "standardId": "3.2 I.A.6."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Explore the motion of objects.", "standardId": "3.2 I.B.1."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Respond to sounds in the environment.", "standardId": "3.2 I.B.5."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of motion and sound.", "standardId": "3.2 I.B.7."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Explore earth materials.", "standardId": "3.3 I.A.1."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Explore water.", "standardId": "3.3 I.A.4."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Observe weather conditions.", "standardId": "3.3 I.A.5."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of earth materials, processes, and cycles.", "standardId": "3.3 I.A.7."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Show interest in the environment.", "standardId": "4.1 I.A."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Observe weather conditions.", "standardId": "4.1 I.E."}, {"ageGroup": "Infant", "category": "Scientific Thinking and Technology", "benchmark": "Observe basic gardening tools being used.", "standardId": "4.3 I.D."}, {"ageGroup": "Infant", "category": "Social Studies Thinking", "benchmark": "Respond to adult guidance about behavior.", "standardId": "5.1 I.A."}, {"ageGroup": "Infant", "category": "Social Studies Thinking", "benchmark": "Express emotion relating to a conflict.", "standardId": "5.2 I.B."}, {"ageGroup": "Infant", "category": "Social Studies Thinking", "benchmark": "Explore costumes and props that represent community workers.", "standardId": "5.2 I.C."}, {"ageGroup": "Infant", "category": "Social Studies Thinking", "benchmark": "Demonstrate preference for specific objects and people.", "standardId": "6.1 I.D."}, {"ageGroup": "Infant", "category": "Social Studies Thinking", "benchmark": "Anticipate next step of a familiar routine or activity.", "standardId": "8.1 I.A."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Act out familiar scenarios.", "standardId": "9.1.D I.B.  "}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Respond to music.", "standardId": "9.1.M I.B."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Express self through music and dance.", "standardId": "9.1.M I.E."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Use technologies for producing music.", "standardId": "9.1.M I.J."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Combine a variety of materials to engage in the process of art.", "standardId": "9.1.V I.B."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Use technologies in the process of creating art.", "standardId": "9.1.V I.J."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Explore a variety of art forms.", "standardId": "9.3 I.F."}, {"ageGroup": "Infant", "category": "Creative Thinking and Expression", "benchmark": "Respond to various art forms.", "standardId": "9.3 I.G."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Locate basic body parts when named by an adult.", "standardId": "10.1 I.B."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Participate in fundamental practices for good health.", "standardId": "10.2 I.A."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Respond to basic safety words.", "standardId": "10.3 I.A."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Develop control of the body.", "standardId": "10.4 I.A."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Exhibit strength and balance in stationary body movements.", "standardId": "10.4 I.B."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Use fingers and hands to accomplish actions.", "standardId": "10.5 I.A."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Use eye and hand coordination to perform a task.", "standardId": "10.5 I.B."}, {"ageGroup": "Infant", "category": "Health, Wellness, and Physical Development", "benchmark": "Manipulate basic tools.", "standardId": "10.5 I.C. "}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Demonstrate an emotional response to the environment.", "standardId": "16.1 I.A."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Demonstrate preference for specific objects and people.", "standardId": "16.1 I.B."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Use comfort of familiar experiences to explore new activities and experiences.", "standardId": "16.1 I.C."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Respond to self in mirror.", "standardId": "16.1 I.E."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Show affection and bond with familiar adults.", "standardId": "16.2 I.A."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Notice differences in others.", "standardId": "16.2 I.B."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Use sounds and gestures as a form of reciprocal communication.", "standardId": "16.2 I.C."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Express emotion relating to a conflict.", "standardId": "16.2 I.D."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Indicate needs through vocalizations and body movements.", "standardId": "16.2 I.E."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Demonstrate preference for specific objects and people.", "standardId": "16.3 I.A."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "Respond to adult guidance about behavior.", "standardId": "16.3 I.B."}, {"ageGroup": "Infant", "category": "Social and Emotional Development", "benchmark": "React to others' expressions of emotion.", "standardId": "16.3 I.C."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Families are supported in times of need.", "standardId": "PL 1."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Families experience relationships with early care and education programs that are affirming, reciprocal, and build upon their strengths.", "standardId": "PL 2."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Families have the support and information they need to encourage their children's learning and development.", "standardId": "PL 3."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Family members have support from other families.", "standardId": "PL 4."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Families have goals of their own and benefit from having supportive partners to help reach their goals.", "standardId": "PL 5."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Families grow in their leadership and use these skills in many different ways.", "standardId": "PL 6."}, {"ageGroup": "Infant", "category": "Partnerships for Learning", "benchmark": "Families are supported in times of transition.", "standardId": "PL 7."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Respond to music, art, and stories.", "standardId": "Al. I.A/B."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Use the senses as a primary means to explore and learn from the environment.", "standardId": "AL.1 I.A."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Explore in the comfort of a familiar surrounding or adult.", "standardId": "AL.1 I.B."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Engage in parallel play.", "standardId": "AL.1 I.C."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Interact with others, objects, or activities for short periods of time.", "standardId": "AL.2 I.A."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Anticipate next step of a familiar routine or activity.", "standardId": "AL.2 I.B."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Engage with an object in more than one way.", "standardId": "AL.2 I.C."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Recognize and respond to familiar adults and routines", "standardId": "AL.2 I.E."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Use a variety of materials to create.", "standardId": "Al.3 I.C."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Demonstrate comfort in routines, objects, and materials that reflect home experiences.", "standardId": "AL.4 I.A."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Use comfort of familiar experiences to explore new activities and experiences.", "standardId": "AL.4 I.B."}, {"ageGroup": "Infant", "category": "Approaches to Learning Through Play", "benchmark": "Play with a variety of objects to determine similar and different outcomes.", "standardId": "AL.4 I.C."}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Show interest in various environmental stimuli.", "standardId": "AL.1 YT.A"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Explore the environment in close proximity to and in the constant sight of familiar adult. ", "standardId": "AL.1 YT.B"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Engage in associative play", "standardId": "AL.1 YT.C"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Engage with others, focus attention and participate in activities for longer periods of time.", "standardId": "AL.2 YT.A"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Know the sequence of familiar routines.", "standardId": "AL.2 YT.B"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Attempt to accomplish challenging tasks", "standardId": "AL.2 YT.C"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Recognize simple patterns in the environment.", "standardId": "AL.2 YT.D"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Recall information from previous experiences.", "standardId": "AL.2 YT.E"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Express self through simple actions, gestures, and words", "standardId": "AL.3 YT.A/B"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Use a variety of materials to represent familiar objects.", "standardId": "AL.3 YT.C"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Relay experience from on setting to another.", "standardId": "AL.4 YT.A"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Repeat familiar activity to gain comfort and confidence.", "standardId": "AL.4 YT.B"}, {"ageGroup": "<PERSON>", "category": "Approaches to Learning Through Play", "benchmark": "Solve simple problems independently.", "standardId": "AL.4 YT.C"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Demonstrate beginning book-handling skills.", "standardId": "1.1 YT.A"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Demonstrate interest in pictures and text", "standardId": "1.1 YT.B"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Identify and imitate familiar sound in the environment.", "standardId": "1.1 YT.C"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Response to simple questions about a text.", "standardId": "1.2 YT.B"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Relate familiar objects in a text to personal experience.", "standardId": "1.2 YT.C"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Identify a favorite book by its cover.", "standardId": "1.2 YT.E"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Use new vocabulary in everyday speech.", "standardId": "1.2 YT.J"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Actively engage in reading activities for short periods of time.", "standardId": "1.2 YT.L"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Respond to simple questions about a story.", "standardId": "1.3 YT.B"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Use new vocabulary in everyday speech.", "standardId": "1.3 YT.J"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Scribble with writing and drawing tools", "standardId": "1.4 YT.R"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Use sounds, gestures, and words as forms of recoprocal communication.", "standardId": "1.5 YT.A"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Respond to questions, comments, or directions", "standardId": "1.5 YT.C"}, {"ageGroup": "<PERSON>", "category": "Language and Literacy Development", "benchmark": "Use 1-2 words and/or signs to communicate.", "standardId": "1.5 YT.D/E"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Imitate rote counting using some names of numbers", "standardId": "2.1 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Explore simple comparisons of quantity.", "standardId": "2.1 YT.A.3"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Engage and persist in numerical play", "standardId": "2.1 YT.MP"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Engage and persist when measuring and sorting objects.", "standardId": "2.1 YT.MP"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Sort manipulative into sets.", "standardId": "2.2 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Match identical shapes.", "standardId": "2.3 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Engage and persist in geometric play.", "standardId": "2.3 YT.MP"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Engage in measurement experiences", "standardId": "2.4 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Mathematical Thinking and Expression", "benchmark": "Participate in sorting objects.", "standardId": "2.4 YT.A.4"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Respond to questions, comments, or directions.", "standardId": "1.5 YT.C"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Imitate use of various technologies in play.", "standardId": "15.4 YT.A"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Engage with objects that represent workplace technologies.", "standardId": "15.4 YT.M"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Imitate rote counting using some names of numbers.", "standardId": "2.1 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Explore the characteristics of living things", "standardId": "3.1 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Explore the basic needs of plants and animals", "standardId": "3.1 YT.A.2"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Recognize parts of living things in their environment.", "standardId": "3.1 YT.A.5"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations to observe living and non-living things", "standardId": "3.1 YT.A.9"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Recognize familiar animals and their babies.", "standardId": "3.1 YT.B.1"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations to observe physical characteristics of living things. ", "standardId": "3.1 YT.B.6"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Engage with objects to learn about their characteristics.", "standardId": "3.2 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Engage in activities involving changes in matter.", "standardId": "3.2 YT.A.3"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations to observe changes in matter.", "standardId": "3.2 YT.A.6"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Recognize and explore how objects move.", "standardId": "3.2 YT.B.1"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Identify and Imitate familiar sounds in the environment.", "standardId": "3.2 YT.B.5"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations to observe motion and sound.", "standardId": "3.2 YT.B.7"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Engage with earth materials", "standardId": "3.3 YT.A.1"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Engage with water", "standardId": "3.3 YT.A.4"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Observe and begin to label weather conditions", "standardId": "3.3 YT.A.5"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Particiapte in simple investigations of earth materials, processes, and cycles", "standardId": "3.3 YT.A.7"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Explore the characteristics of living and non-living things", "standardId": "4.1 YT.A"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Explore the basic needs of plants and animals.", "standardId": "4.1 YT.D"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Observe and begin to label weather conditions.", "standardId": "4.1 YT.E"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Explore the basic needs of plants and animals", "standardId": "4.3 YT.A"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Imitate the use of basic gardening tools", "standardId": "4.3 YT.D"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Use appropriate trash receptacles", "standardId": "4.4 YT.D"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Recall information from previous experiments.", "standardId": "AL.2 YT.E"}, {"ageGroup": "<PERSON>", "category": "Scientific Thinking and Technology", "benchmark": "Relay experience from one setting to another", "standardId": "AL.4 YT.A"}, {"ageGroup": "<PERSON>", "category": "Social Studies Thinking", "benchmark": "Demonstrate basic understanding of rules.", "standardId": "5.1 YT.A"}, {"ageGroup": "<PERSON>", "category": "Social Studies Thinking", "benchmark": "Demonstrate sense of belonging to a group such as a class or family", "standardId": "5.2 YT.A"}, {"ageGroup": "<PERSON>", "category": "Social Studies Thinking", "benchmark": "Express emotion relating to a conflict.", "standardId": "5.2 YT.B"}, {"ageGroup": "<PERSON>", "category": "Social Studies Thinking", "benchmark": "Engage with costumes and props that represent community workers", "standardId": "5.2 YT.C"}, {"ageGroup": "<PERSON>", "category": "Social Studies Thinking", "benchmark": "Make simple choices", "standardId": "6.1 YT.D"}, {"ageGroup": "<PERSON>", "category": "Social Studies Thinking", "benchmark": "Know the sequence of familiar routines.", "standardId": "8.1 YT.A"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Act out familiar scenarios using objects with intended purpose.", "standardId": "9.1.D YT.B"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Demonstrate and understanding of basic elements of music and movement", "standardId": "9.1.M YT.A"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Respond to music.", "standardId": "9.1.m YT.B"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Respond to music and dance.", "standardId": "9.1.M YT.E"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Use a variety of technologies for producing music or performing movements.", "standardId": "9.1.M YT.J"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Demonstrate an understanding of basic elements of visual arts.", "standardId": "9.1.V YT.A"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Combine a variety of materials to engage in the process of art", "standardId": "9.1.V YT.B"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Use a variety of technologies in the process of creating art.", "standardId": "9.1.V YT.J"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Engage with a variety of art forms.", "standardId": "9.3 YT.F"}, {"ageGroup": "<PERSON>", "category": "Creative Thinking and Expression", "benchmark": "Show interest in others' artistic expressions.", "standardId": "9.3 YT.G"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": " Locate basic body parts when asked.", "standardId": "10.1 YT.B "}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Engage in fundamental practices for good health.", "standardId": "10.2 YT.A"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Cooperate with basic safety practices.", "standardId": "10.3 YT.A"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Control and coordinate the movement of the body", "standardId": "10.4 YT.A"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Exhibit balance and strength when moving from place to place.", "standardId": "10.4 YT.B"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Coordinate use of fingers, hands and wrists to accomplish actions.", "standardId": "10.5 YT.A"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Coordinate eye and hand movements to perform a task.", "standardId": "10.5 YT.B"}, {"ageGroup": "<PERSON>", "category": "Health, Wellness, and Physical Development", "benchmark": "Use Basic tools.", "standardId": "10.5 YT.C"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Demonstrate and emotional response in reaction to an experience.", "standardId": "16.1 YT.A"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Demonstrate preference for specific objects or activities.", "standardId": "16.1 YT.B"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Repeat familiar activity to gain comfort and confidence.", "standardId": "16.1 YT.C"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Convey ownership of objects and people.", "standardId": "16.1 YT.E"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": " Use trusted adult as a secure base from which to explore the environment.", "standardId": "16.2 YT.A"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Recognize similarities and differences between self and others.", "standardId": "16.2 YT.B"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Use sounds, gestures, and words as a form of reciprocal communication.", "standardId": "16.2 YT.C"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Express emotion relating to a conflict.", "standardId": "16.2 YT.D"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Communicate needs.", "standardId": "16.2 YT.E"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Make simple choices.", "standardId": "16.3 YT.A"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Demonstrate basic understanding of rules", "standardId": "16.3 YT.B"}, {"ageGroup": "<PERSON>", "category": "Social and Emotional Development", "benchmark": "Engage in empathy and compassion in some situations. ", "standardId": "16.3 YT.C"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Families are supported in times of need", "standardId": "PL 1"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Families experience relationships with early care and education programs that are affirming, reciprocal, and build upon their strengths.", "standardId": "PL 2"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Families have the support and information they need to encourage their children's learning and development.", "standardId": "PL 3"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Family members have support from other families", "standardId": "PL 4"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Families have goals of their own and benefit from having supportive partners to help reach their goals.", "standardId": "PL 5"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Families grow in their leadership and use these skills in many different ways.", "standardId": "PL 6"}, {"ageGroup": "<PERSON>", "category": "Partnerships for Learning", "benchmark": "Families are supported in times of transition.", "standardId": "PL 7"}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Explore characteristics of and ask questions about objects, people, activities, and environments.", "standardId": "AL.1 OT.A."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Explore the environment independently seeking occasional approval from adults.", "standardId": "AL.1 OT.B."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Engage with others in simple cooperative play.", "standardId": "AL.1 OT.C."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Focus attention and participate in task-oriented activities.", "standardId": "AL.2 OT.A."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Identify and complete the sequence of familiar routines and tasks.", "standardId": "AL.2 OT.B."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Attempt to accomplish challenging tasks by employing familiar strategies.", "standardId": "Al.2 OT.C."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Recognize and create simple patterns.", "standardId": "AL.2 OT.D."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Retain and recall information from previous experiences.", "standardId": "AL.2 OT.E."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Construct music, art, and stories as a means of self-expression.", "standardId": "AL.3 OT A/B."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Experiment with materials to represent objects.", "standardId": "Al.3 OT.C."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Notice similarities and differences between settings.", "standardId": "AL.4 OT.A."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Approach new experiences with confidence", "standardId": "Al.4 OT.B."}, {"ageGroup": "Older Toddler", "category": "Approaches to Learning through Play", "benchmark": "Attempt problem solving activities to achieve a positive outcome.", "standardId": "Al.4 OT.C."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Demonstrate beginning book-handling skills.", "standardId": "1.1 OT.A."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Recognize that print has meaning.", "standardId": "1.1 OT.B."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Categorize familiar sounds.", "standardId": "1.1 OT.C."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Recognize familiar environmental print.", "standardId": "1.1 OT.C."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Answer simple questions about a text.", "standardId": "1.2 OT.B."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Relate text to personal experiences when asked.", "standardId": "1.2 OT.C."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Identify a text by the front cover.", "standardId": "1.2 OT.E."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Notice details in illustration or picture.", "standardId": "1.2 OT.G."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Use expanded vocabulary in everyday speech.", "standardId": "1.2 OT.J. "}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Actively engage in small group reading activities.", "standardId": "1.2 OT.L."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Recall an event from a story.", "standardId": "1.3 OT.A."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Answer simple questions about a story.", "standardId": "1.3 OT.B."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Recognize pictures of familiar characters in a book.", "standardId": "1.3 OT.C."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Notice details in illustration or picture.", "standardId": "1.3 OT.G."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Use expanded vocabulary in everyday speech.", "standardId": "1.3 OT.J."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Actively engage in small group reading activities.", "standardId": "1.3 OT.K."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Tell a story about a picture.", "standardId": "1.4 OT.M."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Intentionally make marks with writing and drawing tools.", "standardId": "1.4 OT.R."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Ask questions about topics of personal interest to gain information.", "standardId": "1.4 OT.V."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Engage in reciprocal conversations and interactions with peers and adults.", "standardId": "1.5 OT.A."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Respond to questions, comments, or directions.", "standardId": "1.5 OT.C."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Use simple sentences, communicating clearly enough to be understood by familiar adults.", "standardId": "1.5 OT.D/E."}, {"ageGroup": "Older Toddler", "category": "Language and Literacy Development", "benchmark": "Demonstrate command of the conventions of standard English when speaking, based on Older Toddler development.", "standardId": "1.5 OT.G."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Know some number names and the count sequence.", "standardId": "2.1 OT.A.1."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Count to tell the number of objects.", "standardId": "2.1 OT.A.2."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Use comparative language to show understanding of more or less.", "standardId": "2.1 OT.A.3."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Use mathematical processes when quantifying, comparing, and representing numbers.", "standardId": "2.1 OT.MP."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Add to and take apart sets.", "standardId": "2.2 OT.A.1."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Recognize and identify basic shapes in the environment.", "standardId": "2.3 OT.A.1."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Create and compose simple shapes.", "standardId": "2.3 OT.A.2."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Use mathematical processes when creating and composing shapes.", "standardId": "2.3 OT.MP. "}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Use basic measurement vocabulary.", "standardId": "2.4 OT.A.1."}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Classify objects and count  the number of objects in each category.", "standardId": "2.4 OT.A.4. "}, {"ageGroup": "Older Toddler", "category": "Mathematical Thinking and Expression", "benchmark": "Use mathematical processes when measuring and organizing data.", "standardId": "2.4 OT.MP."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Communicate about technology in their environment.", "standardId": "15.4 OT.A."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Communicate about workplace technologies and their uses.", "standardId": "15.4 OT.M. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Identify basic characteristics of some living and nonliving  things.", "standardId": "3.1 OT.A.1. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that plants and animals have basic needs.", "standardId": "3.1 OT.A.2. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Notice plants and animals growing and changing.", "standardId": "3.1 OT.A.3."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Identify basic parts of living things.", "standardId": "3.1 OT.A.5. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of living and nonliving things to answer a question.", "standardId": "3.1 OT.A.9. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Notice similarities and differences between living things from the same species.", "standardId": "3.1 OT.B.1."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of physical characteristics of living things from same species to answer a question.", "standardId": "3.1 OT.B.6. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Notice changes that occur in animals.", "standardId": "3.1 OT.C.3. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of changes that occur in animals.", "standardId": "3.1 OT.C.4."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Describe objects by their characteristics.", "standardId": "3.2 OT.A.1. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Notice changes in matter.", "standardId": "3.2 OT.A.3. "}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of matter to answer a question.", "standardId": "3.2 OT.A.6"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Communicate about the motion of objects.", "standardId": "3.2 OT.B.1"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Categorize familiar sounds", "standardId": "3.2 OT.B.5."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of motion and sound to answer a question.", "standardId": "3.2 OT.B.7"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Describe earth materials.", "standardId": "3.3 OT.A.1"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Discuss basic uses of water.", "standardId": "3.3 OT.A.4"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Describe changes in weather conditions, and discuss how weather affects daily life.", "standardId": "3.3 OT.A.5"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of earth materials, processes, and cycles to answer a question.", "standardId": "3.3 OT.A.7"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Identify basic characteristics of some living and nonliving things.", "standardId": "4.1 OT.A"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that plants and animals have basic needs.", "standardId": "4.1 OT.D"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Describe changes in weather conditions, and discuss how weather affects daily life.", "standardId": "4.1 OT.E"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Participate in discussions about water in the environment.", "standardId": "4.2 OT.A"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Discuss different places animals can live.", "standardId": "4.2 OT.C"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that plants and animals have basic needs.", "standardId": "4.3 OT.A."}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Notice plants and animals growing and changing", "standardId": "4.3 OT.C"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Label basic garden tools.", "standardId": "4.3 OT.D"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Communicate about the basic needs of people.", "standardId": "4.4 OT.A"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Communicate about insects and animals that can be harmful.", "standardId": "4.4 OT.B"}, {"ageGroup": "Older Toddler", "category": "Scientific Thinking and Technology", "benchmark": "Communicate about and use appropriate trash receptacles", "standardId": "4.4 OT.D"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Follow basic rules.", "standardId": "5.1 OT.A"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Communicate a sense of belonging to a group such as a class or family.", "standardId": "5.2 OT.A"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Communicate about a conflict and seek help to solve.", "standardId": "5.2 OT.B."}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Recognize community workers through their uniforms and equipment.", "standardId": "5.2 OT.C"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Communicate about a choice based on individual interest.", "standardId": "6.1 OT.D"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Communicate about products produced locally.", "standardId": "6.3 OT.D"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Communicate about local businesses.", "standardId": "6.5 OT.C"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Use maps in play", "standardId": "7.1 OT.A"}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Describe the characteristics of home to gain understanding of physical features", "standardId": "7.2 OT.A."}, {"ageGroup": "Older Toddler", "category": "Social Studies Thinking", "benchmark": "Identify and complete the sequence of familiar routines and tasks.", "standardId": "8.1 OT.A"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Explore real or make-believe scenarios through dramatic play.", "standardId": "9.1.D OT.B"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Use imagination and creativity to express self through dramatic play.", "standardId": "9.1.D OT.E."}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": " Know and use basic elements of music and movement", "standardId": "9.1.M OT.A"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Respond to and communicate about music.", "standardId": "9.1.M OT.B"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Respond to and communicate about music and dance", "standardId": "9.1.M OT.E"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Use a variety of technologies for producing music or performing movements", "standardId": "9.1.M OT.J"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Know and use basic elements of visual arts", "standardId": "9.1.V OT.A"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Combine a variety of material to engage in the process of art", "standardId": "9.1.V OT.B"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Use imagination and creativity to express self through the process of art.", "standardId": "9.1.V OT.E"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Use a variety of technologies in the process of creating art.", "standardId": "9.1.V OT.J"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Communicate about a variety of art forms.", "standardId": "9.3 OT.F"}, {"ageGroup": "Older Toddler", "category": "Creative Thinking and Expression", "benchmark": "Comment on characteristics of others' artistic expressions", "standardId": "9.3 OT.G"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify and locate basic body parts.", "standardId": "10.1 OT.B"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Participate in experiences related to healthy food choices", "standardId": "10.1 OT.C"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Discuss fundamental practices for good health.", "standardId": "10.2 OT.A"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Use and communicate about basic safety practices", "standardId": "10.3 OT.A"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Combine and coordinate body movement.", "standardId": "10.4 OT.A"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Demonstrate balance and strength when performing gross motor activities.", "standardId": "10.4 OT.B"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Coordinate use of fingers, hands, and wrists to accomplish actions", "standardId": "10.5 OT.A"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Coordinate eye and hand movements to perform a task.", "standardId": "10.5 OT.B"}, {"ageGroup": "Older Toddler", "category": "Health, Wellness, and Physical Development", "benchmark": "Use basic tools demonstrating refined skills.", "standardId": "10.5 OT.C"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Make connections between emotions and behavior.", "standardId": "16.1 OT.A"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Communicate preference for people, objects, and activities.", "standardId": "16.1 OT.B"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Approach new experiences with confidence.", "standardId": "16.1 OT.C"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Establish goals independently.", "standardId": "16.1 OT.D"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Use language that indicates knowledge of self.", "standardId": "16.1 OT.E"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Engage in reciprocal conversations and interactions with peers and adults.", "standardId": "16.2 OT. C"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Communicate about a conflict and seek help to solve.", "standardId": "16.2 OT. D"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Develop relationships that extend beyond trusted adults", "standardId": "16.2 OT.A"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Communicate about similarities and differences between self and others.", "standardId": "16.2 OT.B"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Ask for help when needed", "standardId": "16.2 OT.E"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Recognize the consequences of choices.", "standardId": "16.3 OT.A"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Follow basic rules", "standardId": "16.3 OT.B"}, {"ageGroup": "Older Toddler", "category": "Social and Emotional Development", "benchmark": "Demonstrate empathy and compassion for others", "standardId": "16.3 OT.C"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Families are supported in times of need", "standardId": "PL.1"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Families experiences relationship with early care and educational programs that are affirming, reciprocal, and build upon their strengths.", "standardId": "PL.2"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Families have the support and information they need to encourage their children's learning and development.", "standardId": "PL.3"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Family members have support from other families", "standardId": "PL.4"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Families have goals of their own and benefit from having supportive parents to help reach their goals", "standardId": "PL.5"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Families grow in their leadership and use these skills in many different ways", "standardId": "PL.6"}, {"ageGroup": "Older Toddler", "category": "Partnerships for Learning", "benchmark": "Families are supported in times of transition", "standardId": "PL.7"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Explore and ask questions to seek meaningful information about a growing range of topics, ideas, and tasks.", "standardId": "AL.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Demonstrate a willingness to participate in new and challenging experiences.", "standardId": "AL.1 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Engage in complex play sequences with two or more children.", "standardId": "AL.1 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Work toward completing a task, even if challenging, and despite interruptions.", "standardId": "AL.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Independently break simple tasks into steps and complete them one at a time.", "standardId": "AL.2 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Attempt to accomplish challenging tasks by employing familiar and new strategies as needed. ", "standardId": "AL.2 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Recognize and extend simple patterns.", "standardId": "AL.2 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Retain and recall information presented over a short period of time.", "standardId": "AL.2 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Use music, art, and/or stories to express ideas, thoughts, and feelings.", "standardId": "AL.3 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Produce and explain the purpose for a new creation.", "standardId": "AL.3 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Use materials and objects to represent new concepts.", "standardId": "AL.3 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Relate knowledge learned from one experience to a similar experience in a new setting.", "standardId": "AL.4 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Recognize that everyone makes mistakes and that using positive coping skills can result in learning from the experience.", "standardId": "AL.4 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Approaches to Learning through Play", "benchmark": "Attempt problem solving activities to achieve a positive outcome.", "standardId": "AL.4 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Identify basic American symbols. (e.g., American Flag)", "standardId": "5.1 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Recreate a dramatic play experience for an audience.", "standardId": "9.1.D PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Use imagination and creativity to express self through dramatic play.", "standardId": "9.1.D PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Know and use basic elements and principles of music and movement.", "standardId": "9.1.M PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Respond to different types of music and dance through participation and discussion.", "standardId": "9.1.M PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Use a variety of technologies for producing or performing works of art.", "standardId": "9.1.M PK.J"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Know and use basic elements of visual arts.", "standardId": "9.1.V PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Combine a variety of materials to create a work of art.", "standardId": "9.1.V PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Use imagination and creativity to express self through visual arts.", "standardId": "9.1.V PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Use a variety of technologies for producing works of art.", "standardId": "9.1.V PK.J"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Explain that instruments or art forms represent cultural perspectives.", "standardId": "9.2 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Recognize and name a variety of art forms.", "standardId": "9.3 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Formulate and share an opinion about others' art products.", "standardId": "9.3 PK.G"}, {"ageGroup": "Preschool & Pre-K", "category": "Creative Thinking & Expression ", "benchmark": "Demonstrate an emotional response to viewing or creating various art works.", "standardId": "9.4 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify and locate body parts.", "standardId": "10.1 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify foods that keep our body healthy.", "standardId": "10.1 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify and discuss the purposes of medicine.", "standardId": "10.1 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify and discuss common health problems.", "standardId": "10.1 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify fundamental practices for good health.", "standardId": "10.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": " Identify environmental factors that affect health.", "standardId": "10.2 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Recognize safe and unsafe practices. ", "standardId": "10.3 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Recognize emergency situations and discuss appropriate responses.", "standardId": "10.3 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Demonstrate coordination of body movements in active play.", "standardId": "10.4 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Exhibit balance while moving on the ground or using equipment.", "standardId": "10.4 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Use hands, fingers, and wrists to manipulate objects.", "standardId": "10.5 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Coordinate eye and hand movements to perform a task.", "standardId": "10.5 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Health, Wellness, and Physical Development", "benchmark": "Use tools that require use of fingers, hands, and/or wrists to accomplish a task.", "standardId": "10.5 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Practice appropriate book handling skills.", "standardId": "1.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Identify basic features of print.", "standardId": "1.1 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Demonstrate understanding of spoken words, syllables, and sounds (phonemes).", "standardId": "1.1 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "<PERSON><PERSON><PERSON> beginning phonics and word skills.", "standardId": "1.1 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "EMERGING TO... Read emergent reader text with purpose and understanding.", "standardId": "1.1 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, retell key details of text that support a provided main idea.", "standardId": "1.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Answer Questions about a text.", "standardId": "1.2 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, make connections between information in a text and personal experience.", "standardId": "1.2 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Identify the front cover, back cover, and title page of a book.", "standardId": "1.2 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": " With prompting and support, answer questions about unfamiliar words read aloud from a text.", "standardId": "1.2 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, answer questions to connect illustrations to the written word.", "standardId": "1.2 PK.G"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, identify basic similarities and differences between two texts read aloud on the same topic.", "standardId": "1.2 PK.I"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Use new vocabulary and phrases acquired in conversations and being read to.", "standardId": "1.2 PK.J"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, clarify unknown words or phrases read aloud.", "standardId": "1.2 PK.K"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, actively engage in group reading activities with purpose and understanding.", "standardId": "1.2 PK.L"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, retell a familiar story in sequence with picture support.", "standardId": "1.3 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Answer questions about a particular story (who, what, how, when, and where).", "standardId": "1.3 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, answer questions to identify characters, settings, and major events in a story.", "standardId": "1.3 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, name the author and illustrator of a story.", "standardId": "1.3 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, recognize common types of text.", "standardId": "1.3 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Answer questions about unfamiliar words read around from a story.", "standardId": "1.3 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Describe pictures in a book using detail.", "standardId": "1.3 PK.G"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Answer questions to compare and contrast the adventures and experiences of characters in familiar stories.", "standardId": "1.3 PK.H"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, clarify unknown words or phrases read aloud.", "standardId": "1.3 PK.I"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Use new vocabulary and phrases acquired in conversations and being read to.", "standardId": "1.3 PK.J"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, actively engage in group reading activities with purpose and understanding.", "standardId": "1.3 PK.K"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Draw/dictate to compose informative/explanatory texts examining a topic.", "standardId": "1.4 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, draw/dictate about one specific topic.", "standardId": "1.4 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, generate ideas to convey information.", "standardId": "1.4 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, make logical connections between drawing and dictation.", "standardId": "1.4 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Emerging to... Spell simple words phonetically.", "standardId": "1.4 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Dictate narratives to describe real or imagined experiences or events.", "standardId": "1.4 PK.M"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Establish \"who\" and \"what\" the narrative will be about.", "standardId": "1.4 PK.N"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With prompting and support, describe experiences and events.", "standardId": "1.4 PK.O"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Recount a single event and tell about the events in the order in which they occurred.", "standardId": "1.4 PK.P"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Emerging to... Spell simple words phonetically.", "standardId": "1.4 PK.R"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With guidance and support from adults and peers, respond to questions and suggestions, add details as needed.", "standardId": "1.4 PK.T"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Ask questions about topics of personal interest to gain information, with teacher guidance and support, locate information on the chosen topic.", "standardId": "1.4 PK.V"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "With guidance and support, recall information from experiences or books.", "standardId": "1.4 PK.W"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Emerging to... Write routinely over short time frames.", "standardId": "1.4 PK.X"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Participate in collaborative conversations with peers and adults in small and larger groups.", "standardId": "1.5 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Answer questions about key details in a text read aloud or information presented orally or through other media.", "standardId": "1.5 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Respond to what a speaker says to follow directions, seek help, or gather information.", "standardId": "1.5 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Use simple sentences; share stories, familiar experiences, and interests, speaking clearly enough to be understood by most audiences.", "standardId": "1.5 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Use simple sentences; express thoughts, feelings, and ideas, speaking clearly enough to be understood by most audiences. ", "standardId": "1.5 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Language and Literacy Development", "benchmark": "Demonstrate command of the conventions of standard English when speaking based on pre-kindergarten level and content.", "standardId": "1.5 PK.G"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Know number names and the count sequence.", "standardId": "2.1 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Count to tell the number of objects.", "standardId": "2.1 PK.A.2"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Compare numbers.", "standardId": "2.1 PK.A.3"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Use mathematical processes when quantifying, comparing, representing, and modeling numbers.", "standardId": "2.1 PK.MP"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Understand addition as putting together and adding to, and understand subtraction as taking apart and taking from.", "standardId": "2.2 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Use Mathematical processes when representing relationships.", "standardId": "2.2 PK.MP"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Identify and describe shapes.", "standardId": "2.3 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Analyze, compare, create, and compose shapes.", "standardId": "2.3 PK.A.2"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Use mathematical processes when drawing, constructing, modeling, and representing shapes.", "standardId": "2.3 PK.MP"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Describe and compare measurable attributes of length and weights of everyday objects.", "standardId": "2.4 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Classify objects and count the number of objects in each category.", "standardId": "2.4 PK.A.4"}, {"ageGroup": "Preschool & Pre-K", "category": "Mathematical Thinking and Expression", "benchmark": "Use mathematical processes when measuring; representing, organizing, and understanding data.", "standardId": "2.4 PK.MP"}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Families are supported in times of need.", "standardId": "PL 1."}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Families experience relationships with early care and education programs that are affirming, reciprocal, and build upon their strengths.", "standardId": "PL 2."}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Families have the support and information they need to encourage their children's learning and development.", "standardId": "PL 3."}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Family members have support from other families.", "standardId": "PL 4."}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Families have goals of their own and benefit from having supportive partners to reach their goals.", "standardId": "PL 5."}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Families grow in their leadership and use these skills in many different ways.", "standardId": "PL 6."}, {"ageGroup": "Preschool & Pre-K", "category": "Partnerships for Learning ", "benchmark": "Families are supported in times of transition.", "standardId": "PL 7."}, {"ageGroup": "Preschool & Pre-K", "category": "Phonemic Awareness", "benchmark": "Demonstrate understanding of spoke words, syllables, and sounds (phonemes).", "standardId": "1.1 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Phonemic Awareness", "benchmark": "<PERSON><PERSON><PERSON> beginning phonics and word skills.", "standardId": "1.1 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Phonemic Awareness", "benchmark": "EMERGING TO... Read emergent reader text with purpose and understanding.", "standardId": "1.1 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify various technologies used in the classroom and at home.", "standardId": "15.4 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Demonstrate responsible use of technology and equipment.", "standardId": "15.4 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "With prompting and support, identify peripheral devices of computer system including input and output devices.", "standardId": "15.4 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Demonstrate the correct use of dimple input technologies (e.g., mouse, touch screen, microphone, etc.).", "standardId": "15.4 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, elect and use various software/ applications for an intended purpose.", "standardId": "15.4 PK.G"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, identify similarities and differences between text, graphics, audio, animation, and video.", "standardId": "15.4 PK.K"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, use web browser to locate content-specific websites. ", "standardId": "15.4 PK.L"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "With help and support identify various technologies used in the workplace.", "standardId": "15.4 PK.M"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Recognize the difference between living and non-living things.", "standardId": "3.1 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify basic needs of plants (water and light) and animals (food, water, and light).", "standardId": "3.1 PK.A.2"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that plants and animals grow and change.", "standardId": "3.1 PK.A.3"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Name basic parts of living things.", "standardId": "3.1 PK.A.5"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations about living and/ or non-living things to answer a question or to test a prediction.", "standardId": "3.1 PK.A.9"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Recognize and compare physical characteristics of living things from same species.", "standardId": "3.1 PK.B.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of physical characteristics of living things from same species to answer a question or to test a prediction.", "standardId": "3.1 PK.B.6"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Describe changes that occur in animals.", "standardId": "3.1 PK.C.3"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Sort and describe objects according to size, shape, color, and texture.", "standardId": "3.2 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Notice change in matter.", "standardId": "3.2 PK.A.3"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that everything is made of matter.", "standardId": "3.2 PK.A.5"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of matter to answer a question or to test a prediction.", "standardId": "3.2 PK.A.6"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Explore and describe the motion of toys and objects.", "standardId": "3.2 PK.B.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Create and describe variations of sound.", "standardId": "3.2 PK.B.5"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that light from the sun is an important source of energy for living and non-living systems and some source of energy is needed for all organisms to stay alive and grow.", "standardId": "3.2 PK.B.6"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of energy and motion to answer a question or to test a prediction.", "standardId": "3.2 PK.B.7"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Sort different types of earth materials.", "standardId": "3.3 PK.A.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify a variety of uses for water.", "standardId": "3.3 PK.A.4"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify seasons that correspond with observable conditions and identify how weather affects daily life.", "standardId": "3.3 PK.A.5"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations to earth structure, processes, and cycles to answer a question or to test a prediction.", "standardId": "3.3 PK.A.7"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify objects that can be found in the day or night sky.", "standardId": "3.3 PK.B.1"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of the objects found in the day or night sky to answer a question or to test a prediction.", "standardId": "3.3 PK.B.3"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify living and non-living things in the immediate and surrounding environment.", "standardId": "4.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify that plants need the sun to grow.", "standardId": "4.1 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify basic needs of living things.", "standardId": "4.1 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify the change of seasons in the environment.", "standardId": "4.1 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify various types of moving water in Pennsylvania.", "standardId": "4.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify a wetland as an ecosystem in Pennsylvania.", "standardId": "4.2 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Describe an aquatic (water) and terrestrial (land) habitat.", "standardId": "4.2 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify how the environment provides for the needs of people in their daily lives.", "standardId": "4.3 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify natural resources available to people in their daily lives.", "standardId": "4.3 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify what plants and animals need to grow.", "standardId": "4.4 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that plants and animals grow and change.", "standardId": "4.4 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify basic tools used in gardening at home and at school.", "standardId": "4.4 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify what people need to survive.", "standardId": "4.5 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify things in the natural environment that can be harmful to people, pets, and the living things.", "standardId": "4.5 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Identify ways people pollute the environment.", "standardId": "4.5 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Scientific Thinking and Technology", "benchmark": "Describe how everyday human activities generate waste", "standardId": "4.5 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Distinguish between emotions and identify socially accepted ways to express them.", "standardId": "16.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Recognize that everyone has personal traits which guide behavior and choices.", "standardId": "16.1 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Recognize that everyone makes mistakes and that using positive coping skills can result in learning from experience. ", "standardId": "16.1 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Establish goals independently and recognize their influence on choices.", "standardId": "16.1 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Interact with peers and adults in a socially acceptable manner.", "standardId": "16.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Identify similarities and differences between self and others.", "standardId": "16.2 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Engage in reciprocal communication with adults and peers.", "standardId": "16.2 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Recognize that conflict occurs and distinguish between appropriate and inappropriate ways to resolve conflict. *See also 5.2 PK.B", "standardId": "16.2 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Ask for and accept offers of help when needed or appropriate.", "standardId": "16.2 PK.E"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Interpret the consequences of choices.", "standardId": "16.3 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Recognize there are socially acceptable ways to behave in different places.", "standardId": "16.3 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Social and Emotional ", "benchmark": "Actively engage in assisting others when appropriate.", "standardId": "16.3 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "State Rules and their consequences. ", "standardId": "5.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Identify basic American symbols. (e.g., American Flag)", "standardId": "5.1 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Identify self-membership of a group such as the class or family.", "standardId": "5.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": " Identify a problem and discuss possible solutions with adult assistance. *See also 16.2 PK.D", "standardId": "5.2 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Emerging to... Identify classroom projects/activities that support leadership and service.", "standardId": "5.2 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": " Identify community workers through their uniforms and equipment.", "standardId": "5.3 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Identify appropriate behaviors for responsible classroom citizens.", "standardId": "5.3 PK.F"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Emerging to... Identify how scarcity influences choices.", "standardId": "6.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Emerging to... Identify family wants and needs.", "standardId": "6.1 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": " Identify a choice based on individual interest.", "standardId": "6.1 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Emerging to... Identify advertisements that encourages us to buy things.", "standardId": "6.2 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Explain how money is used.", "standardId": "6.2 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Identify products produced locally.", "standardId": "6.3 PK.D"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Differentiate between work and play.", "standardId": "6.5 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Identify local businesses.", "standardId": "6.5 PK.C"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Explain how a map is a representation of places.", "standardId": "7.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": " Emerging to... Describe the location of place in the home, school, and community to gain an understanding of relative location.", "standardId": "7.1 PK.B"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Describe the characteristics of home and frequently visited locations to gain an understanding of physical features.", "standardId": "7.2 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Identify a sequence of events through a day.", "standardId": "8.1 PK.A"}, {"ageGroup": "Preschool & Pre-K", "category": "Social Studies Thinking ", "benchmark": "Understand that information comes from many sources such as books, computers, and newspapers.", "standardId": "8.1 PK.C"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Explore and ask questions to seek meaningful information about a growing range, of topics, ideas, and tasks.", "standardId": "AL.1 K.A"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Demonstrate a willingness to participate in a new and challenging experiences.", "standardId": "AL.1 K.B"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Engage in elaborate, interactive play sequences that include acting out roles and negotiating play themes.", "standardId": "AL.1 K.C"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Complete a task, despite interruptions or classroom disruptions.", "standardId": "AL.2 K.A"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Complete multi-step tasks with independence.", "standardId": "AL.2 K.B"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Accomplish challenging tasks by employing familiar and new strategies as needed.", "standardId": "AL.2 K.C"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Recognize and create simple patterns.", "standardId": "AL.2 K.D"}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Retain and recall information presented over a short period of time.", "standardId": "AL.2 K.E."}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Use music, art, and/or stories to express ideas, thoughts, and feelings.", "standardId": "AL.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Create an object to serve a functional purpose.", "standardId": "AL.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Use material and objects to represent new concepts.", "standardId": "AL.3 K.C."}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Relate knowledge learned from one experience to a similar experience in a new setting.", "standardId": "AL.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Recognize that everyone makes mistakes and that using positive coping skills can result in learning from the experience.", "standardId": "AL.4 K.B."}, {"ageGroup": "Kindergarten", "category": "Approaches to Learning through Play", "benchmark": "Use problem-solving strategies to achieve a positive outcome.", "standardId": "AL.4. K.C."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use book-handling skills.", "standardId": "1.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Demonstrate understanding of the organization and basic features of print.", "standardId": "1.1 K.B. "}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Demonstrate understanding of spoken words, syllables, and sounds (phonemes).", "standardId": "1.1 K.C. "}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Know and apply grade-level phonics and word analysis skills in decoding words.", "standardId": "1.1 K.D. "}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Read emergent-reader text with purpose and understanding.", "standardId": "1.1 K.E."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, identify the main idea and retell key details of text.", "standardId": "1.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, answer questions about key details in a text.", "standardId": "1.2 K.B."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, make a connection between two individuals, events, ideas, or pieces of information in a text.", "standardId": "1.2 K.C."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Identify parts of a book (title, author) and parts of a text (beginning, details, and end).", "standardId": "1.2 K.E."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, ask and answer questions about unknow words in a text.", "standardId": "1.2 K.F."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Answer questions to describe the relationship between illustrations and the text in which they appear.", "standardId": "1.2 K.G."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, identify the reasons an author gives to support points in a text.", "standardId": "1.2 K.H."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, identify basic similarities and differences between two texts (read or read aloud) on the same topic.", "standardId": "1.2 <PERSON>.I."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use words and phrases acquired through conversations, reading, and being read to, and respond to texts.", "standardId": "1.2 K.J."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, determine and clarify the meaning of unknown or multiple-meaning words and phrases based upon grade-level reading and content.", "standardId": "1.2 K.K."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Actively engage in group reading activities with purpose and understanding.", "standardId": "1.2 K.L."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, retell familiar stories including keys details.", "standardId": "1.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Answer questions about key details in a text.", "standardId": "1.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, identify characters, settings, and major events in a story.", "standardId": "1.3 K.C."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Name the author and illustrator of story and define the role of each  in telling the story.", "standardId": "1.3 K.D."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Recognize common types of text.", "standardId": "1.3 K.E."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Ask and answer questions about unknown words in a text.", "standardId": "1.3 K.F."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Make connections between the illustrations and the text in a story (read or read aloud).", "standardId": "1.3 K.G."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Compare and contrast the adventures and experiences of characters in familiar stories.", "standardId": "1.3 K.H."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Determine or clarify the meaning of unknown or multiple-meaning words and phrases based upon grade-level reading and content.", "standardId": "1.3 <PERSON><PERSON>I."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use words and phrases acquired through conversations, reading, and being read to, and respond to texts.", "standardId": "1.3 K.J."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Actively engage in group reading activities with purpose and understanding.", "standardId": "1.3 K.K."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use a combination of drawing, and writing to compose informative/explanatory texts.", "standardId": "1.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use a combination of drawing, dictating, and writing to focus on one specific topic.", "standardId": "1.4 K.B."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, generate ideas and details to convey information that relates to the chosen topic.", "standardId": "1.4 K.C."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Make logical connections between drawing and dictation/writing.", "standardId": "1.4 K.D."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With prompting and support, illustrate using details and dictate/write using descriptive words.", "standardId": "1.4 K.E."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Demonstrate a grade-appropriate command of the conventions of standard English grammar, usage, capitalization, punctuation, and spelling.", "standardId": "1.4 K.F."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use a combination of drawing, dictating, and writing to compose opinion pieces on familiar topics.", "standardId": "1.4 K.G."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Form an opinion by choosing between two given topics.", "standardId": "1.4 K.H."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Support the opinion with reasons.", "standardId": "1.4 <PERSON>.I."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Make logical connections between drawing and writing.", "standardId": "1.4 K.J."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Demonstrate a grade-appropriate command of the conventions of standard English grammar, usage, capitalization, punctuation, and spelling.", "standardId": "1.4 K.L."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Use a combination of drawing, dictating, and writing to compose narratives that describe real or imagined experiences or events.", "standardId": "1.4 K.M."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Establish who and what the narrative will be about.", "standardId": "1.4 K.N."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Describe experiences and events", "standardId": "1.4 K.O."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Recount a single event or several loosely linked events, tell about the events in the order in which they occurred, and provide a reaction to what happened.", "standardId": "1.4 K.P."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Demonstrate a grade-appropriate command of the conventions of standard English grammar, usage, capitalization, punctuation, and spelling.", "standardId": "1.4 K.R."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "with guidance and support from adults and peers, respond to questions and suggestions from peers, and add details to strengthen writing as needed.", "standardId": "1.4 K.T."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With guidance and support, explore a variety of digital tools to produce and publish writing in collaboration with peers.", "standardId": "1.4 K.U."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Participate in individual or shared research projects on a topic of interest.", "standardId": "1.4 K.V."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "With guidance and support, recall information from experiences or gather information from provided sources to answer a question.", "standardId": "1.4 K.W."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Write routinely over short time frames.", "standardId": "1.4 K.X."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "participate in collaborative conversations with peers and adults in small and larger groups.", "standardId": "1.5 K.A."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Ask and answer questions about key details in a text read aloud or information presented orally or through other media.", "standardId": "1.5 K.B."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Ask and answer questions to seek help, get information, or clarify something that is not understood.", "standardId": "1.5 K.C."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "share stories, familiar experiences, and interests, speaking clearly enough to be understood by all audiences using appropriate volume.", "standardId": "1.5 K.D."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Speak audibly and express thoughts, feelings, and ideas clearly.", "standardId": "1.5 K.E."}, {"ageGroup": "Kindergarten", "category": "Language and Literacy Development", "benchmark": "Demonstrate command of the conventions of standard English when speaking, based on Kindergarten level and content.", "standardId": "1.5 K.G."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Know number names and write and recite the count sequence.", "standardId": "2.1 K.A.1."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Apply one-to-one correspondence to count the number of objects.", "standardId": "2.1 K.A.2."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Apply the concept of magnitude to compare numbers and quantities.", "standardId": "2.1 K.A.3."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Use place-value to compose and decompose numbers within 19.", "standardId": "2.1 K.B.1."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Extend the concepts of putting together and taking apart to add and subtract within 10.", "standardId": "2.2 K.A.1."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Identify and describe two- and three-dimensional shapes.", "standardId": "2.3 K.A.1."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Analyse, compare, create, and compose two- and three-dimensional shapes.", "standardId": "2.3 K.A.2."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Describe and compare attributes of length, area, weight, and capacity of everyday objects. Measure using appropriate tools", "standardId": "2.4 K.A.1."}, {"ageGroup": "Kindergarten", "category": "Mathematical Thinking and Expression", "benchmark": "Classify objects and count the number of objects in each category.", "standardId": "2.4 K.A.4."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify various technologies used in the classroom and at home.", "standardId": "15.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Demonstrate responsible use of technology and equipment.", "standardId": "15.4 K.B."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "With prompting and support, identify peripheral devices of computer system including input and output devices.", "standardId": "15.4 K.C."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Demonstrate the correct use of simple input technologies, (e.g., mouse, touch screen, ", "standardId": "15.4 K.D."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, select and use various software/applications for an intended purpose.", "standardId": "15.4 K.G."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, identify similarities and differences between text, graphics, audio, animation, and video.", "standardId": "15.4 K.K."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, use web browser to locate content-specific websites.", "standardId": "15.4 K.L."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "With help and support, identify various technologies used in the workplace.", "standardId": "15.4 K.M."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify the similarities and differences of living and nonliving things.", "standardId": "3.1 K.A.1"}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "EMERGING TO ... Investigate the dependence of living things on the sun's energy, water, food/nutrients, air, living space, and shelter.", "standardId": "3.1 K.A.2."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Observe, compare, and describe stages of life cycles for plants and/or animals.", "standardId": "3.1 K.A.3."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Observe and describe structures and behaviors of a variety of common animals.", "standardId": "3.1 K.A.5."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in investigations about living and/or nonliving things to answer a question or to test a prediction.", "standardId": "3.1 K.A.9."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Observe and describe how young animals resemble their parents and other animals of the same kind.", "standardId": "3.1 K.B.1."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of physical characteristics of living things from same species to answer a question or test a prediction.", "standardId": "3.1 K.B.6."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Describe changes animals and plants undergo throughout the seasons.", "standardId": "3.1 K.C.2."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "describe changes that occur as a result of climate.", "standardId": "3.1 K.C.3."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of changes in animals to answer a question or test a prediction.", "standardId": "3.1 K.C.4."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify and classify objects by observable properties of matter. Compare kinds of materials and discuss their uses.", "standardId": "3.2 K.A.1."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Describe the way matter can change.", "standardId": "3.2 K.A.3."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that everything is made of matter.", "standardId": "3.2 K.A.5."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of matter to answer a question or to test a prediction.", "standardId": "3.2 K.A.6."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Describe how temperature can affect the body.", "standardId": "3.2 K.B.3."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Recognize that light from the sun is an important source of energy for living and nonliving systems and some source of energy is needed for all organisms to stay alive and grow.", "standardId": "3.2 K.B.6."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of energy and motion to answer a question or to test a prediction.", "standardId": "3.2 K.B.7."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Distinguish between three types of earth materials - rock, soil and sand.", "standardId": "3.3 K.A.1."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "identify sources of water for human consumption and use.", "standardId": "3.3 K.A.4."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Record daily weather conditions using simple charts and graphs. Identify seasonal changes in the environment. Distinguish between types of precipitation.", "standardId": "3.3 K.A.5."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of earth structures, processes, and cylces to answer a question or to test a prediction.", "standardId": "3.3 K.A.7."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Participate in simple investigations of the objects found in the day or night sky to answer a question or to test a prediction.", "standardId": "3.3 K.B.3."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify the similarities and differences of living and nonliving things within the immediate and surrounding environment.", "standardId": "4.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Observe and describe what happens to living things when needs are met.", "standardId": "4.1 K.D."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify how the changes of seasons affect their local environment.", "standardId": "4.1 K.E."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify components of a water cycle.", "standardId": "4.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Differentiate between terrestrial, aquatic, and wetland ecosystems in Pennsylvania.", "standardId": "4.2 K.B."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify that there are living and nonliving components in an aquatic system.", "standardId": "4.2 K.C."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify some renewable resources used in the classroom.", "standardId": "4.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Recognize the importance of conserving natural resources.", "standardId": "4.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify common plants and animals found in Pennsylvania agricultural systems.", "standardId": "4.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify tools and machinery commonly used in agriculture.", "standardId": "4.4 K.D."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify what people use in their every day life.", "standardId": "4.5 K.A."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify common pests in our homes, gardens, and neighborhoods.", "standardId": "4.5 K.B."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify different types of pollution (land, water or air) and their sources.", "standardId": "4.5 K.C."}, {"ageGroup": "Kindergarten", "category": "Scientific Thinking and Technology", "benchmark": "Identify waste and practice ways to reduce, reuse and recycle.", "standardId": "4.5 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Explain the purpose of rules.", "standardId": "5.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Explain the need for rules.", "standardId": "5.1 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Define respect for self and others.", "standardId": "5.1 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Demonstrate responsibilities in the classroom.", "standardId": "5.1 K.E."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify significant American holidays and their symbols.", "standardId": "5.1 K.F."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify responsibilities at school.", "standardId": "5.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify a problem and discuss possible solutions.", "standardId": "5.2 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify classroom projects/activities that support leadership and service.", "standardId": "5.2 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": " Explain responsible classroom behavior.", "standardId": "5.2 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify the role of adults in authority at home or in school.", "standardId": "5.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify roles of firefighters, police officers, and emergency workers.", "standardId": "5.3 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify and explain behaviors for responsible classroom citizens.", "standardId": "5.3 K.F."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify conflict in the classroom.", "standardId": "5.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify how students can work together.", "standardId": "5.4 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify how scarcity influences choice.", "standardId": "6.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify family wants and needs.", "standardId": "6.1 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify choices to meet needs.", "standardId": "6.1 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify a choice based on family interest.", "standardId": "6.1 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify goods and consumers.", "standardId": "6.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify advertisements that encourage us to buy things.", "standardId": "6.2 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify currency and how it is used.", "standardId": "6.2 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify products produced in the region or state.", "standardId": "6.3 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify the specialized role performed by each member of the family.", "standardId": "6.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify individuals wants and needs.", "standardId": "6.4 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify individuals in the community who volunteer.", "standardId": "6.5 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify goods and services provided by local businesses.", "standardId": "6.5 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Interpret a simple map of a known environment.", "standardId": "7.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Describe the location of places in the home, school, and community to gain an understanding of relative location.", "standardId": "7.1 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Describe the characteristics of homes ans businesses locationed in the community to gains an understanding of physical features.", "standardId": "7.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify land and water forms.", "standardId": "7.2 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Describe how weather affects daily life.", "standardId": "7.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify local bodies of water and landforms to gain an understanding of their impact on the local community.", "standardId": "7.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify chronological sequence through days, weeks, months, and years (calendar time).", "standardId": "8.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "With guidance and support, differentiate facts from opinions as related to an event.", "standardId": "8.1 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Explain how to locate information in a source.", "standardId": "8.1 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify people in authority.", "standardId": "8.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Examine photographs of documents, artifacts, and places unique to Pennsylvania.", "standardId": "8.2 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Demonstrate an understanding of conflict.", "standardId": "8.2 K.D."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify american people related to national holidays.", "standardId": "8.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify documents and artifacts important to the classroom community.", "standardId": "8.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Demonstrate an understanding of time order.", "standardId": "8.3 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Explain how cultures celebrate.", "standardId": "8.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Identify different celebrations of different cultures from around the world.", "standardId": "8.4 K.C."}, {"ageGroup": "Kindergarten", "category": "Social Studies Thinking ", "benchmark": "Demonstrate an understanding of conflict and cooperation.", "standardId": "8.4 K.D."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Recreate a dramatic play experience for an audience.", "standardId": "9.1.D K.B."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Use imagination and creativity to express self through dramatic play.", "standardId": "9.1.D K.E."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Know and use basic elements and principles of music and movement.", "standardId": "9.1.M K.A."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Respond to different types of music and dance through participation and discussion.", "standardId": "9.1.M K.B."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Use imagination and creativity to express self through music and dance.", "standardId": "9.1.M K.E."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Use a variety of technologies for producing or performing works of art.", "standardId": "9.1.<PERSON> K.J."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Know and use basic elements of visual arts.", "standardId": "9.1.V K.A."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Combine a variety of materials to create a work of art.", "standardId": "9.1.V K.B."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Use imagination and creativity to express self through visual arts.", "standardId": "9.1.V K.E."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Use a variety of technologies for producing works of art.", "standardId": "9.1.V K.J."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Explain that instruments or art forms represent cultural perspectives.", "standardId": "9.2 K.D."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Recognize and name a variety of art forms.", "standardId": "9.3 K.F."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Formulate and share opinion about one's own works and that of others.", "standardId": "9.3 K.G."}, {"ageGroup": "Kindergarten", "category": "Creative Thinking & Expression ", "benchmark": "Demonstrate an emotional response to viewing or creating various works of art.", "standardId": "9.4 K.B."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify and describe function of basics body parts and organs.", "standardId": "10.1 K.B."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify foods that keep our bodies healthy.", "standardId": "10.1 K.C."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Distinguish between healthy and unhealthy behaviors.", "standardId": "10.1 K.D."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify and discuss common health problems and risk factors.", "standardId": "10.1 K.E."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify fundamental practices for good health.", "standardId": "10.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Identify environmental factors that affect health.", "standardId": "10.2 K.E."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Recognize safe and unsafe practices.", "standardId": "10.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Recognize emergency situations and discuss appropriate responses.", "standardId": "10.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Demonstrate coordination of purposeful body movements.", "standardId": "10.4 K.A."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Exhibit balance, strength, stamina, and agility.", "standardId": "10.4 K.B."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Use dexterity and strength to manipulate objects.", "standardId": "10.5 K.A."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Coordinate eye and hand movements to perform an advanced task.", "standardId": "10.5 K.B."}, {"ageGroup": "Kindergarten", "category": "Health, Wellness, and Physical Development", "benchmark": "Use tools with control and skill to perform tasks.", "standardId": "10.5 K.C."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Distinguish between emotions and identify socially accepted ways to express them.", "standardId": "16.1 K.A."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Recognize that everyone has personal traits which guide behaviors and choices.", "standardId": "16.1 K.B."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Recognize that everyone make mistakes and that using positive coping skills can result in learning from the experience.", "standardId": "16.1 K.C."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Establish goals independently and recognize their influence on choices.", "standardId": "16.1 K.D."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Interact with peers and adults in a socially acceptable manner.", "standardId": "16.2 K.A."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Identify similarities and differences between self and others.", "standardId": "16.2 K.B."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Engage in reciprocal communication with adults and peers.", "standardId": "16.2 K.C."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Recognize that conflict occurs and distinguish between appropriate and inappropriate ways to resolve conflict. *See also 5.2 K.B.", "standardId": "16.2 K.D."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Ask for and accept offers of help when needed or appropriate.", "standardId": "16.2 K.E."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Interpret the consequences of choices.", "standardId": "16.3 K.A."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Recognize there are socially acceptable ways to behave in different places.", "standardId": "16.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Social and Emotional ", "benchmark": "Actively engage in assisting others when appropriate.", "standardId": "16.3 K.B."}, {"ageGroup": "Kindergarten", "category": "Partnerships for Learning ", "benchmark": "Families are supported and connected to community resources.", "standardId": "PL.1."}, {"ageGroup": "Kindergarten", "category": "Partnerships for Learning ", "benchmark": "School communities and families build partnerships that are affirming, reciprocal, and respectful.", "standardId": "PL.2."}, {"ageGroup": "Kindergarten", "category": "Partnerships for Learning ", "benchmark": "Families have the knowledge and understanding to encourage and support their students'learning and development.", "standardId": "PL.3."}, {"ageGroup": "Kindergarten", "category": "Partnerships for Learning ", "benchmark": "Family members have support from other families empowering and strengthening the entire school community.", "standardId": "PL.4."}, {"ageGroup": "Kindergarten", "category": "Partnerships for Learning ", "benchmark": "Families are supported by the school community to develop and use leadership and advocacy skills.", "standardId": "PL.5."}, {"ageGroup": "Kindergarten", "category": "Partnerships for Learning ", "benchmark": "Families are supported in times of transition.", "standardId": "PL.6."}, {"ageGroup": "Kindergarten", "category": "Circle Time ", "benchmark": "Demonstrate command of the conventions of standard English when speaking, based on Kindergarten level and content.", "standardId": "1.5 K.G"}, {"ageGroup": "Kindergarten", "category": "Circle Time ", "benchmark": "Know number names and write and recite the count sequence.", "standardId": "2.1 K.A.1"}, {"ageGroup": "Kindergarten", "category": "Circle Time ", "benchmark": "Record Daily weather conditions using simple charts and graphs. Identify seasonal changes in the environment. Distinguish between types of precipitation.", "standardId": "3.3 K.A.5"}, {"ageGroup": "Kindergarten", "category": "Circle Time ", "benchmark": "Identify significant American holidays and their symbols.", "standardId": "5.1 K.F"}, {"ageGroup": "Kindergarten", "category": "Circle Time ", "benchmark": "Identify chronological sequence through days, weeks, months, and years (calendar time).", "standardId": "8.1 K.A"}, {"ageGroup": "Kindergarten", "category": "Phonemic Awareness", "benchmark": "Demonstrate understanding of spoken words, syllables, and sounds (phonemes).", "standardId": "1.1 K.C"}, {"ageGroup": "Kindergarten", "category": "Phonemic Awareness", "benchmark": "Know and apply grade-level phonics and word analysis skills in decoding words.", "standardId": "1.1 K.D"}, {"ageGroup": "Kindergarten", "category": "Phonemic Awareness", "benchmark": "Read emergent- reader text with purpose and understanding.", "standardId": "1.1 K.E "}]