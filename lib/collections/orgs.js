import { Meteor } from 'meteor/meteor';
import { orgLanguageTranformationUtil } from '../util/orgLanguageTranformationUtil';
import { CardProvidersShared } from '../../imports/card_providers_shared/cardProvidersShared';
import {
	BillingFrequencies,
	ITEM_TYPE,
	PUNCH_CARD_TYPE,
} from '../constants/billingConstants';
import { AvailableCustomizations } from '../customizations';
import { updateCacheOnOrgChange } from '../updateCache';
import { OrgsLib } from "../orgsLib";
import { CustomizationsHooksService } from "../customizationsHooksService";
import { DEFAULT_PROGRAM_DAYS } from '../constants/enrollmentConstants';
import { OrgsUtil } from '../util/orgsUtil';
import { Mongo } from 'meteor/mongo';
import _ from '../../lib/util/underscore';
import { People } from './people';
import { Groups } from './groups';
import { MomentDefinitions } from './momentDefinitions';
import { PermissionsRoles } from './permissionsRoles';
const moment = require('moment-timezone');
import { User } from './users';

/**
 * @class
 * @constructor
 * @param {Mongo.Document} doc
 */
export const Org = function (doc) {
	_.extend(this, doc);
};

function mapCurriculumTypes(types, prefix, expanded) {
	let output = [];
	const curPrefix = prefix ? prefix + "`" : "";
	_.each(types, (t) => {
		if (typeof t === 'object' && t !== null) {
			if (t.children) {
				if (expanded) output.push(curPrefix + t.label);
				output = output.concat(mapCurriculumTypes(t.children, curPrefix + t.label, expanded));
			} else {
				output.push(curPrefix + t.label);
			}
		} else {
			output.push(curPrefix + t);
		}
	});
	return output.sort();
}

const defaultImmunizationTypes = [
	{ type: "HepB", description: "Hepatitis B", monthsRequired: [0, 2, 18], exempt: false },
	{ type: "RV", description: "Rotavirus", monthsRequired: [2, 4, 6], exempt: false },
	{ type: "DTaP", description: "Diptheria, tetanus, & acellular pertussis", monthsRequired: [2, 4, 6, 18, 72], exempt: false },
	{ type: "Hib", description: "Haemophilus influenzae type b", monthsRequired: [2, 4, 6, 15], exempt: false },
	{ type: "PCV13", description: "Pneumococcal conjugate", monthsRequired: [2, 4, 6, 15], exempt: false },
	{ type: "IPV", description: "Inactivated poliovirus", monthsRequired: [2, 4, 18, 72], exempt: false },
	{ type: "IIV", description: "Influenza", monthsRequired: [6, 18, 30, 42, 54, 66, 78, 90, 102], exempt: false },
	{ type: "MMR", description: "Measles, mumps, rubella", monthsRequired: [15, 72], exempt: false },
	{ type: "VAR", description: "Varicella", monthsRequired: [15, 72], exempt: false },
	{ type: "HepA", description: "Hepatitis A", monthsRequired: [23, 23], exempt: false },
	{ type: "MenACWY", description: "Meningococcal", exempt: false },
	{ type: "PPSV23", description: "Pneumococcal polysaccharide", exempt: false },
	{ type: "UCHR", description: "UCHR", exempt: false },
	{ type: "H1N1", description: "H1N1", exempt: false }
];

function mapImmunizationOverrides(immunizationOverrides) {
	const defaultOverride = [];
	_.each(defaultImmunizationTypes, (dt) => {
		const override = _.find(immunizationOverrides || [], (i) => {
			if (i.type == dt.type) return true;
		})
		if (override) return defaultOverride.push(Object.assign({}, dt, override));
		defaultOverride.push(dt);
	});
	return defaultOverride;
}

_.extend(Org.prototype, {
	customizationExists: function (customizationName) {
		if (this.customizations && this.customizations[customizationName] !== undefined) return true;
		return false;
	},
	hasCustomization: function (customizationName) {
		return OrgsLib.orgHasCustomization(this, customizationName);
	},

	findMetric: async function (metricName) {
		if (!this.metrics) return 0;
		switch (metricName) {
			case "messagesSent":
				return (this.metrics.messagesSent) || 0;
			case "averageMoments":
				var staffCount = await People.find({
					orgId: this._id, inActive: { $ne: true },
					$or: [{ "type": "staff" }, { "type": "admin" }]
				}).countAsync();
				return ((this.metrics.momentsPosted || 0) / staffCount).toFixed(1);
		}
	},
	updateMetric: async function (metricName, amt) {
		var foo = {};
		foo["metrics." + metricName] = amt;
		await Orgs.updateAsync(this._id, { $inc: foo });

	},
	planInfo: function () {
		if (this.planDetails) {
			return this.planDetails;
		} else
			return {
				plan: "trial",
				title: "Trial",
				description: "Try all of MomentPath's features before deciding to purchase"
			};
	},
	getTimezone: function () {
		/** @return {string} */
		return (this.timezone || 'America/New_York');
	},
	getShortCode: function () {
		return (this.shortCode)
	},
	'availableCurriculumTypes': function (options) {
		//return ['','Creative Arts', 'Language & Literacy', 'Math & Numbers', 'Science & Sensory'];
		const types = (this.valueOverrides && this.valueOverrides.curriculumTypes)
			? this.valueOverrides.curriculumTypes
			:
			[
				'Assessment of Child Progress',
				'Community Relationships',
				'Curriculum',
				'Families',
				'Health',
				'Leadership and Management',
				'Physical Environment',
				'Relationship',
				'Teachers',
				'Teaching',
			];
		return options && options.mapped ? mapCurriculumTypes(types, null, options.expanded) : types;
	},
	'availableCheckInHealthCheckTypes': function () {
		return ['Eyes', 'Ears', 'Nose', 'Throat', 'Chest', 'Head', 'Leg', 'Asthma', 'Skin', 'Behavior Change'];
	},
	'availableNavItems': function () {
		var navItems = [
			{ name: "Dashboard", location: "/", icon: "tachometer", route: "dashboard" },
			{ name: "People", location: "/people", icon: "user", route: "people" },
			{ name: "Calendar", location: "/calendar", icon: "calendar", route: "calendar" },
			{ name: "Groups", location: "/groups", icon: "users", route: "groups" },
			{ name: "Announcements", location: "/announcements", icon: "bullhorn", route: "announcements" },
			{ name: "Curriculum", location: "/curriculum", icon: "graduation-cap", route: "curriculum" },
			{ name: "Food", location: "/food", icon: "cutlery", route: "food" },
			{ name: "Scheduling", location: "/scheduling", icon: "calendar-check-o", route: "scheduling" },
			{ name: "Inquiries", location: "/inquiries", icon: "address-book-o", route: "inquiries" },
			{ name: "Forms", location: "/forms", icon: "check-square-o", route: "forms" },
			{ name: "Reports", location: "/reports", icon: "bar-chart", route: "reports" },
			{ name: "Admin/Account", location: "/admin/org", icon: "building", route: "org" }
		];
		return navItems;
	},
	availableOrgMomentTypes: function (mds, availableTypes, isDynamic=false) {

		_.each(mds, (md) => {
			let pushType = true;
			if (this.customizationExists(`moments/${md.momentType}/enabled`)) pushType = this.hasCustomization(`moments/${md.momentType}/enabled`);
			if (pushType && !isDynamic) availableTypes.push({ momentType: md.momentType, prettyName: md.momentTypePretty, icon: md.icon, adminOnly: this.hasCustomization(`moments/${md.momentType}/adminOnly`) });
			if (pushType && isDynamic) availableTypes.push(md);
		});
	},
	'availableMomentTypes': function () {
		var availableTypes = [];
		availableTypes.push({ momentType: "comment", prettyName: "Comment", icon: "fa-comment" });
		if (this.hasCustomization("moments/potty/enabled"))
			availableTypes.push({ momentType: "potty", prettyName: orgLanguageTranformationUtil.getMomentTypesPottyByOrgLang(this.language), icon: "fa-child", adminOnly: this.hasCustomization("moments/potty/adminOnly") });
		if (this.hasCustomization("moments/food/enabled"))
			availableTypes.push({ momentType: "food", prettyName: "Food", icon: "fa-cutlery", adminOnly: this.hasCustomization("moments/food/adminOnly") });
		if (this.hasCustomization("moments/sleep/enabled"))
			availableTypes.push({ momentType: "sleep", prettyName: "Sleep", icon: "fa-bed", adminOnly: this.hasCustomization("moments/sleep/adminOnly") });
		if (this.hasCustomization("moments/activity/enabled"))
			availableTypes.push({ momentType: "activity", prettyName: "Activity", icon: "fa-bicycle", adminOnly: this.hasCustomization("moments/activity/adminOnly") });
		if (this.hasCustomization("moments/medical/enabled"))
			availableTypes.push({ momentType: "medical", prettyName: "Medical", icon: "fa-medkit", adminOnly: this.hasCustomization("moments/medical/adminOnly") });
		if (this.hasCustomization("moments/mood/enabled"))
			availableTypes.push({ momentType: "mood", prettyName: "Mood", icon: "fa-smile-o", adminOnly: this.hasCustomization("moments/mood/adminOnly") });
		if (this.hasCustomization("moments/incident/enabled"))
			availableTypes.push({ momentType: "incident", prettyName: "Incident", icon: 'fa-exclamation-circle', adminOnly: this.hasCustomization("moments/incident/adminOnly") });
		if (this.hasCustomization("moments/alert/enabled"))
			availableTypes.push({ momentType: "alert", prettyName: "Notification", icon: "fa-bell", adminOnly: this.hasCustomization("moments/alert/adminOnly") });
		if (this.hasCustomization("moments/supplies/enabled"))
			availableTypes.push({ momentType: "supplies", prettyName: "Supplies", icon: "fa-shopping-bag", adminOnly: this.hasCustomization("moments/supplies/adminOnly") });
		if (this.hasCustomization("moments/learning/enabled"))
			availableTypes.push({ momentType: "learning", prettyName: "Learning", icon: "fa-leanpub", adminOnly: this.hasCustomization("moments/learning/adminOnly") });
		if (this.hasCustomization("moments/illness/enabled"))
			availableTypes.push({ momentType: "illness", prettyName: "Illness", icon: "fa-thermometer-empty", adminOnly: this.hasCustomization("moments/illness/adminOnly") });
		if (this.hasCustomization("moments/ouch/enabled"))
			availableTypes.push({ momentType: "ouch", prettyName: "Ouch", icon: "fa-hand-stop-o", adminOnly: this.hasCustomization("moments/ouch/adminOnly") });
		if (this.hasCustomization("moments/portfolio/enabled"))
			availableTypes.push({ momentType: "portfolio", prettyName: "Portfolio", icon: "fa-leanpub", adminOnly: this.hasCustomization("moments/portfolio/adminOnly") });
		const orgMomentTypes = this.enabledMomentTypes;
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				if (orgMomentTypes && Array.isArray(orgMomentTypes) && orgMomentTypes.length > 0) {
					var mds = await MomentDefinitions.find({ momentType: { $in: orgMomentTypes } }).fetchAsync();
					self.availableOrgMomentTypes(mds, availableTypes);
				} else {
					unavailableOrgMomentTypes(availableTypes);
				}
				return _.sortBy(availableTypes, "prettyName");
			})();
		} else {
			if (orgMomentTypes && Array.isArray(orgMomentTypes) && orgMomentTypes.length > 0) {
				var mds = MomentDefinitions.find({ momentType: { $in: orgMomentTypes } }).fetch();
				this.availableOrgMomentTypes(mds, availableTypes);
			} else {
				unavailableOrgMomentTypes(availableTypes);
			}
			return _.sortBy(availableTypes, "prettyName");
		}
	},
	'availableDynamicMomentTypes': function () {
		var availableTypes = [];
		var orgMomentTypes = this.enabledMomentTypes || [];
		// if (this.hasCustomization("moments/covidHealth/enabled")) {
		// 	orgMomentTypes.push("covidHealth");
		// }

		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				if (orgMomentTypes && Array.isArray(orgMomentTypes) && orgMomentTypes.length > 0) {
					var mds = await MomentDefinitions.find({ momentType: { $in: orgMomentTypes } }).fetchAsync();
					self.availableOrgMomentTypes(mds, availableTypes, true);
				}
				return availableTypes;
			})();
		} else {
			if (orgMomentTypes && Array.isArray(orgMomentTypes) && orgMomentTypes.length > 0) {
				var mds = MomentDefinitions.find({ momentType: { $in: orgMomentTypes } }).fetch();
				this.availableOrgMomentTypes(mds, availableTypes, true);
			}
			return availableTypes;
		}
	},
	'availableDiscountTypes': function (showArchived, showNoManualEntry) {
		return OrgsLib.availableDiscountTypes(this, showArchived, showNoManualEntry);
	},
	'availablePayerSources': function (showArchived) {
		return OrgsLib.availablePayerSources(this, showArchived);
	},
	'availablePrograms': function (showInactive = false) {
		/** @return {Billing_Program[] | *[]} */
		return (this.billing?.programs ?? []).filter(program => showInactive || program.isActive);
	},
	'availableTimePeriods': function () {
		return (this.billing?.timePeriods ?? []).sort((a, b) => a.startDate - b.startDate);
	},
	'availableBundles': function (showArchived = false, doDesignationFilter = false, designation = null) {
		return OrgsLib.getAvailableBundles(this, showArchived, doDesignationFilter, designation);
	},
	'availableCouponCodes': function (showArchived = false) {
		/** @return {Billing_CouponCode[] | *[]} */
		return (this.billing?.couponCodes ?? [])
			.filter(plan => showArchived || !plan.archived)
			.sort((a, b) => a.regStartDate - b.regStartDate);
	},
	'availableCreditMemoTypes': function () {
		let creditMemoTypes = [];
		if (this.valueOverrides && this.valueOverrides.creditMemoTypes)
			creditMemoTypes = creditMemoTypes.concat(this.valueOverrides.creditMemoTypes);
		else {
			const excl = this.billing?.excludedManualPayTypes || [];
			if (!excl.includes('credit_card')) {
				creditMemoTypes.push({ type: "manualCard", description: "Manual Credit Card", paymentOffset: true });
			}
			if (!excl.includes('check')) {
				creditMemoTypes.push({ type: "check", description: "Check", paymentOffset: true });
			}
			if (!excl.includes('cash')) {
				creditMemoTypes.push({ type: "cash", description: "Cash", paymentOffset: true });
			}
			creditMemoTypes.push({ type: "refund", description: "Refund" });
			if (!excl.includes('ach')) {
				creditMemoTypes.push({ type: "manualAch", description: "Manual ACH", paymentOffset: true });
			}
			if (!excl.includes('payroll_deduction')) {
				creditMemoTypes.push({ type: "payrollDeduction", description: "Payroll Deduction", paymentOffset: true });
			}
			creditMemoTypes = creditMemoTypes.concat([
				{ type: "securityDepositAuto", description: "Security Deposit", paymentOffset: false }
			]);
		}
		creditMemoTypes = creditMemoTypes.concat(_.map(this.availablePayerSources(), (ps) => {
			return { type: "excess_" + ps.type, description: "Excess/Overpayment " + ps.description, paymentOffset: true };
		}));
		creditMemoTypes = creditMemoTypes.concat(_.map(this.availablePayerSources(), (ps) => {
			return { type: "prepaid_" + ps.type, description: "Prepaid " + ps.description, paymentOffset: true };
		}));
		creditMemoTypes.push({ type: "systemOverpayment", description: "Overpayment" });
		creditMemoTypes.push({ type: "other", description: "Other" });
		if (this.isLedgerExportable())
			creditMemoTypes = creditMemoTypes.filter(cmt => cmt.paymentOffset);
		return creditMemoTypes;
	},
	'nextCounterValue': async function (label, options) {
		if (!Meteor.isServer) return;
		const currentOrg = this;
		// Check if the counter should be reset
		if (options && options.daily && currentOrg.counters && (
			!currentOrg["counters"][label + "Timestamp"] ||
			currentOrg["counters"][label + "Timestamp"] < new moment.tz(currentOrg.getTimezone()).startOf("day").valueOf())) {
			const query = {
				$set: {
					[`counters.${label}`]: 0,
					[`counters.${label}Timestamp`]: new moment.tz(currentOrg.getTimezone()).valueOf()
				}
			};
			await Orgs.updateAsync({ _id: currentOrg._id }, query);
		}
		const incQuery = { $inc: { [`counters.${label}`]: 1 } };
		try {
			const result = await Orgs.rawCollection().findOneAndUpdate(
				{ _id: currentOrg._id },
				incQuery,
				{ returnDocument: 'after' }
			);
			return result.value.counters[label];
		} catch (error) {
			throw new Meteor.Error('Database update failed', error.message);
		}
	},
	
	'billingStatus': function () {
		let status = "inactive", message = "Inactive";
		if (this.billing && this.billing.enabled && this.billing.stripeInfo) {
			if (!this.billing.stripeInfo.charges_enabled) {
				status = "charges_disabled"; message = "Charges Disabled";
			} else if (this.billing.stripeInfo.external_accounts.data.length > 0) {
				const active_account = _.find(this.billing.stripeInfo.external_accounts.data, (a) => { return a.default_for_currency; });
				if (active_account && _.contains(["new", "validated", "verified"], active_account.status)) {
					status = "active"; message = "Active";
				} else {
					status = "bank_issue"; message = "Bank Account Issue";
				}
			} else {
				status = "missing_account"; message = "Needs Bank Account";
			}
		} else if (this.billing && this.billing.enabled && this.billing.adyenInfo) {
			status = "active"; message = "Active";
		} else if (this.billing && this.billing.enabled) {
			status = "invoice_only"; message = "Invoice Only";
		} else if (this.billing && !this.billing.enabled) {
			status = "client_disabled"; message = "Disabled";
		}

		return { status: status, message: message };
	},
	'billingCardProviderName': function () {
		if (this.billing && this.billing.enabled) {
			return this.billing.adyenInfo?.platform === "classic" ? "adyen" : "adyen_balance";
		}
	},
	'billingCardProviderShared': function () {
		return CardProvidersShared.get(null, this);
	},
	'hasActiveTrial': function () {
		return this.registrationSource == 'app' && !this.planDetails;
	},
	'trialDaysRemaining': function () {
		if (this.registrationSource == 'app' && !this.planDetails) {
			let trialLength = this.trialLength || 14;
			const elapsedDays = new moment().diff(new moment(this.createdAt), "days");
			let remainingDays = elapsedDays <= trialLength ? trialLength - elapsedDays : 0;
			return remainingDays;
		}
	},
	'pinCodeCheckinFields': function () {
		const overrideFields = _.deep(this, "valueOverrides.pinCodeCheckinFields");
		return overrideFields || [{ fieldType: "text", label: "Notes for Staff", dataId: "notes" },
		{
			fieldType: "buttons", label: "Mood", dataId: "mood",
			fieldValues: [
				{ fieldValue: "Happy", fieldValueIcon: "fa-smile-o" },
				{ fieldValue: "SoSo", fieldValueIcon: "fa-meh-o" },
				{ fieldValue: "Sad", fieldValueIcon: "fa-frown-o" }
			]
		}
		];
	},
	'availableCustomPayTypes': function () {
		const types = (this.customStaffPaySettings && this.customStaffPaySettings.types) ? this.customStaffPaySettings.types : [];
		const customTypes = _.reject(types, (t) => t.archived == true);
		return customTypes || [];
	},
	'getCustomPayTypes': function () {
		return (this.customStaffPaySettings && this.customStaffPaySettings.types) ? this.customStaffPaySettings.types : [];
	},
	'getHolidays': function (allowPast) {
		return OrgsLib.getHolidays(this, allowPast);
	},
	'getBusRoutes': function () {
		return this.busRoutes.sort((a, b) => a.name.localeCompare(b.name));
	},
	'getSchools': function () {
		const schoolField = this.valueOverrides.profileFields.find((field) => field.name == "school");
		if (schoolField && schoolField.values) {
			return schoolField.values.sort((a, b) => a.localeCompare(b));
		}
	},
	'getBrands': function () {
		if (this.brands && this.brands.length) {
			return this.brands.sort((a, b) => a.name.localeCompare(b.name));
		}
	},
	'getOpenDaysForPeriod': function (startDateNum, endDateNum, programOfferedOn = DEFAULT_PROGRAM_DAYS) {
		return OrgsUtil.getOpenDaysForPeriod(this, startDateNum, endDateNum, programOfferedOn);
	},
	'getGroupTypes': function () {
		return [
			"Infant",
			"Toddler",
			"Preschool",
			"Kindergarten",
			"After School",
			"School-age",
			"Pre-k"
		];
	},
	'getScheduleTypes': function () {
		return OrgsLib.getScheduleTypes(this);
	},
	'getCustomImmunizations': function () {
		return this.immunizationDefinitions || [];
	},
	getOvertimeFrequency() {
		if (!this.customStaffPaySettings || !this.customStaffPaySettings.frequency) return 'weekly';
		return this.customStaffPaySettings.frequency;
	},
	'availableStandardImmunizations': function () {
		return mapImmunizationOverrides(this.immunizationOverrides);
	},
	'availableImmunizations': function () {
		const defaultOverride = mapImmunizationOverrides(this.immunizationOverrides);
		const customTypes = _.reject(this.immunizationDefinitions || [], (i) => i.archived == true);
		return defaultOverride.concat(customTypes);
	},
	'immunizationExemptionOptions': function () {
		return [
			"Medical or religious exemptions only",
			"Philosophical exemptions expressly excluded",
			"Exempted student exclusion during outbreak",
			"Parental knowledge of student exclusion during outbreak in exemption application",
			"Exemptions not recognized during outbreak",
			"Parental notarization or affidavit required for exemptions",
			"Enhanced education for exemptions",
			"Medical exemptions expressly temporary or permanent"
		]
	},
	'queryData': function (queryName, queryData) {
		const self = this;
		switch (queryName) {
			case "staffList":
				console.log("a ===>");
				if (Meteor.isServer) {
					return (async () => {
						return _.map(await People.find({
								orgId: self._id,
								type: { "$in": ["staff", "admin"] },
								inActive: { $ne: true }
							}).fetchAsync(),
						(p) => { return { value: p.lastName + ", " + p.firstName, id: p._id } });
					})();
				} else {
					return _.map(People.find({ orgId: self._id, type: { "$in": ["staff", "admin"] }, inActive: { $ne: true } }).fetch(),
					(p) => { return { value: p.lastName + ", " + p.firstName, id: p._id } });
				}
			case "groupList":
				console.log("b ===>");
				if (Meteor.isServer) {
					return (async () => {
						return _.map(await Groups.find({ orgId: self._id }).fetchAsync(),
						(g) => { return { value: g.name, id: g._id } });
					})();
				} else {
					return _.map(Groups.find({ orgId: self._id }).fetch(),
					(g) => { return { value: g.name, id: g._id } });
				}
			case "primaryFamily":
				if (Meteor.isServer) {
					return (async () => {
						return await OrgsLib.queryDataForPrimaryFamilyAsync(queryData);
					})();
				} else {
					const person = queryData && queryData.personId && People.findOne(queryData.personId), relatedPeople = person && person.findOwnedRelationships({ noDuplicates: true });
					return _.map(relatedPeople,
						(r) => { const p = People.findOne(r.personId); if (p) return { value: p.firstName + ' ' + p.lastName, id: p._id } });
				}

			case "amBusRoute":
				const amBusRoutes = _.sortBy(_.filter(this.busRoutes, (r) => r.am), (r) => r.name);
				return _.map(amBusRoutes, (g) => { return { value: g.name, id: g._id } });
			case "pmBusRoute":
				const pmBusRoutes = _.sortBy(_.filter(this.busRoutes, (r) => r.pm), (r) => r.name);
				return _.map(pmBusRoutes, (g) => { return { value: g.name, id: g._id } });
		}
	},
	'foodUnits': function (foodType) {
		switch (foodType) {
			case 'babyFood':
				return (this.valueOverrides && this.valueOverrides["foodUnits"] && this.valueOverrides["foodUnits"]["babyFood"]) || "oz";
			case 'cereal':
				return (this.valueOverrides && this.valueOverrides["foodUnits"] && this.valueOverrides["foodUnits"]["cereal"]) || "tsp";
		}
	},
	'profileDataPrefix': function () {
		return (this.hasCustomization("people/types/advancedProfileFields") ||
			((this.hasCustomization("people/types/customerSpecificProfileFields") || this.hasCustomization("people/types/customerSpecificStaffProfileFields")) && this.createdAt && this.createdAt >= 1571702400000))
			&& "profileData";
	},
	'learningAssessmentLevels': function () {
		let levels = [];
		const customAssessmentLevels = this && _.deep(this, "valueOverrides.assessmentLevels");
		if (customAssessmentLevels)
			levels = customAssessmentLevels;
		else
			for (var i = 0; i <= 5; i++) { levels.push({ value: i, label: i }); }
		return levels;
	},
	"availableStaffTimeClassifications": function () {
		return (this.valueOverrides && this.valueOverrides["staffTimeClassifications"]) ||
			[{ id: "regularHours", name: "Regular Hours" },
			{ id: "paidTimeOff", name: "Paid Time Off" },
			{ id: "sick", name: "Sick", unpaid: true },
			{ id: "continuingEducation", name: "Continuing Education" },
			{ id: "juryDuty", name: "Jury Duty", unpaid: true },
			{ id: "otherUnpaid", name: "Unpaid / Other", unpaid: true }
			];
	},
	"nextBiWeeklyBillingTimestamp": function (overrideDayStamp) {
		const seedDateText = this.billing && this.billing.scheduling && this.billing.scheduling.generateBiWeeklyDate;
		if (seedDateText) {
			const seedDate = new moment.tz(seedDateText, "MM/DD/YYYY", this.getTimezone()),
				duration = moment.duration(new moment(overrideDayStamp).tz(this.getTimezone()).startOf("day").diff(seedDate)),
				durationAsDays = duration.asDays(), durationAsWeeks = Math.floor(duration.asWeeks()),
				nextDate = seedDate.clone().add(durationAsWeeks - (durationAsWeeks % 2), "weeks").add(durationAsDays % 14 == 0 ? 0 : 2, "weeks");
			return nextDate.valueOf();
		}
	},
	"activeDocumentDefinitions": function (showArchived) {
		return this.documentDefinitions ? _.filter(this.documentDefinitions, (dd) => { return showArchived || !dd.deletedAt; }) : [];
	},
	"requireRoles": function () {
		return this.hasCustomization("people/requireRoles");
	},
	"enabledRoles": function () {
		return _.deep(this, "valueOverrides.availablePermissionsRoles")?.sort() || [];
	},
	"enabledRolesDetails": function () {
		if(Meteor.isServer){
			const self = this;
			return (async () => {
				const roleNames = self.enabledRoles();
                return await OrgsUtil.getEnabledRolesDetails(roleNames);
            })();
			
		}else{
			const roleNames = this.enabledRoles(),
				dbRoles = PermissionsRoles.find().fetch(),
				mappedRoles = {};
			_.each(dbRoles, role => { mappedRoles[role.name] = role });
			const roleDefs = _.deep(this, "valueOverrides.roleDefinitions") || mappedRoles,
				output = [];
			roleNames.forEach(rn => {
				const rd = roleDefs[rn];
				if (rd) {
					rd.id = rn;
					output.push(rd);
				}
			});
			return output;
		}
	},
	"availablePermissionsContexts": function () {
		return _.deep(this, "valueOverrides.availablePermissionsContexts");
	},
	"isChildCare": function () {
		return _.contains(["translationsEnChildCare", "translationsEnLightbridge", "translationsEnMontessori"], this.language);
	},
	"getAvailableAssessmentLevels": function () {
		return this.availableAssessmentLevels ||
			[
				{
					"label": "Not Measured",
					"value": 0
				},
				{
					"label": "Introduced",
					"value": 1
				},
				{
					"label": "Meets",
					"value": 2
				},
				{
					"label": "Exceeds",
					"value": 3
				}
			];
	},
	getMappedLedgerAccount: function (lineItemType, lineItem, modifiedDiscount) {
		return OrgsLib.getMappedLedgerAccount(this, lineItemType, lineItem, modifiedDiscount);
	},
	getAccountLedgerCodes() {
		return this.billing && this.billing.ledgerAccountCodes;
	},
	cardFeeNotice(amount = null) {
		return OrgsLib.getCardFeeNotice(this, amount);
	},

	achFeeNotice() {
		return OrgsLib.getAchFeeNotice(this);
	},

	alternateServiceChargeFeeDescription() {
		return OrgsLib.getAlternateServiceChargeFeeDescription(this);
	},
	getLongName() {
		return this.longName || this.name;
	},
	getChatSupportUrl() {
		if (this && this.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW) && this.hasCustomization(AvailableCustomizations.PEOPLE_CHAT_SUPPORT_ENABLED)) {
			return this.chatSupportSettings?.chatUrl || null;
		}
		return null;
	},
	/** Use await while using it on server side */
	findAllRelatedOrgIds() {
		const id = this._id;
		if(Meteor.isServer){
			return (async()=>{
				return await findRelatedOrgIdsAsync(null, id);
			})()
		}else{
			return findRelatedOrgIds(null, id);
		}
	},
	async findTopOrg() {
		let top = await Orgs.findOneAsync({ _id: this._id });
		while (top.parentOrgId) {
			const potentialTop = await Orgs.findOneAsync({ _id: top.parentOrgId });
			if (potentialTop) {
				top = potentialTop;
			} else {
				break;
			}
		}
		return top;
	},
	/** it will return promise on server side */
	findScopedOrgs(topmostOrgId) {
		const id = this._id;
		if (Meteor.isServer) {
			return (async()=>{
				const orgIds = await findRelatedOrgIdsAsync(null, id, topmostOrgId);
				const orgs = await Orgs.find({ _id: { "$in": orgIds } }).fetchAsync();
				return orgs;
			})()
		} else {
			const orgIds = findRelatedOrgIds(null, id, topmostOrgId);
			const orgs = Orgs.find({ _id: { "$in": orgIds } }).fetch();
			return orgs;
		}
	},
	availableBillingFrequencies() {
		const frequencies = [
			{ type: BillingFrequencies.DAILY, description: 'Daily - Flat Rate' },
			{ type: BillingFrequencies.WEEKLY, description: 'Weekly - Flat Rate' },
			{ type: BillingFrequencies.SCALED_WEEKLY, description: 'Weekly - Scaled Pricing' },
			{ type: BillingFrequencies.WEEKLY_SCHEDULED_DAILY, description: 'Weekly - Charged Daily by Schedule' },
			{ type: BillingFrequencies.BIWEEKLY, description: 'Bi-weekly - Flat Rate' },
			{ type: BillingFrequencies.SCALED_BIWEEKLY, description: 'Bi-weekly - Scaled Pricing' },
			{ type: BillingFrequencies.SEMIMONTHLY, description: 'Semi-monthly (due 1st and 15th) - Flat Rate' },
			{ type: BillingFrequencies.MONTHLY, description: 'Monthly - Flat Rate' },
			{ type: BillingFrequencies.SCALED_MONTHLY, description: 'Monthly - Scaled Pricing' }
		];
		if (this.hasCustomization(AvailableCustomizations.VARIABLE_MONTHLY_SUBSIDIES)) {
			frequencies.push({
				type: BillingFrequencies.DAILY_CHARGED_MONTHLY,
				description: 'Monthly - Charged Daily by Schedule'
			});
		}
		frequencies.push(
			{ type: BillingFrequencies.BIMONTHLY, description: 'Bi-monthly - Flat Rate' }
		);
		return frequencies;
	},
	availableBillingFrequencyMultipliers() {
		//for enrollment forecasting - multipliers achieve dollars-per-month
		return {
			hourly: 730.08,
			daily: 21.65,
			weekly: 4.33,
			SCALED_WEEKLY_PLAN: 4.33,
			biweekly: 2.165,
			SCALED_BIWEEKLY_PLAN: 2.165,
			semimonthly: 2.0,
			monthly: 1.0,
			SCALED_MONTHLY_PLAN: 1.0,
			bimonthly: 0.5,
			weekly_scheduled_daily: 21.65
		};
	},
	billingMaps() {
		return OrgsLib.getBillingMaps(this);
	},
	async getCenterPayroll(options) {
		if (Meteor.isServer && !Meteor.isTest) {
			const { personPayCalculation, timeCardPersonPayCalculation } = require('../../server/classList');
			var additionalCenterPayrollQuery = {
				orgId: this._id,
				type: { $in: ["admin", "staff"] },
				inActive: { $ne: true },
				defaultGroupId: null,
			}
			var centerGroup = {
				name: "Additional Staff",
				weeklyPayroll: 0,
				weeklyLoadedPayroll: 0,
				monthlyLoadedPayroll: 0,
			};
			const useAttendance = options.useAttendance ?? false,
				startDate = options.startDate,
				endDate = options.endDate,
				overtimeThreshold = options.overtimeThreshold;
			const thisOrg = this;
			await People.find(additionalCenterPayrollQuery).forEachAsync(async function (p) {
				var payRate = p["payRate"];
				if (thisOrg.profileDataPrefix()) {
					payRate = 0;
					var profileDataObj = p[thisOrg.profileDataPrefix()];
					if (profileDataObj) {
						payRate = profileDataObj["payRate"] || 0;
					}
				}
				var staffForecast = p && p.classList && p.classList.staffForecast;
				if (!payRate || !staffForecast) return;
				const results = useAttendance ?
					await timeCardPersonPayCalculation({ payRate: payRate, startDate: startDate, endDate: endDate, overtimeThreshold: overtimeThreshold }) :
					personPayCalculation({ payRate, staffForecast });
				centerGroup.weeklyPayroll += results.weeklyPayroll;
				centerGroup.weeklyLoadedPayroll += results.weeklyLoadedPayroll;
			});

			centerGroup.monthlyLoadedPayroll = centerGroup.weeklyLoadedPayroll * 4.33;
			return centerGroup;
		}
	},
	isLedgerExportable() {
		return this.getAccountLedgerCodes() || _.deep(this, "billing.netSuite") || _.deep(this, "billing.sage") || this.hasCustomization("billing/requireLedgerAccountName/enabled");
	},
	availableBillingPlans(includeScaledPricing = true, showArchived = false, requirePrograms = false, includeItems = false, doDesignationFilter = false, designation = null) {
		var currentOrg = this;
		const todayDate = new moment.tz(currentOrg.getTimezone()).startOf("day").valueOf();
		return OrgsLib.getAvailableBillingPlans(this, todayDate, includeScaledPricing, showArchived, requirePrograms, includeItems, doDesignationFilter, designation);
	},
	availableOneTimeCharges(showArchived = false, requirePrograms = false) {
		var currentOrg = this;
		const todayDate = new moment.tz(currentOrg.getTimezone()).startOf("day").valueOf();
		return _.chain(this)
			.deep("billing.plansAndItems")
			.filter((p) => {
				return (p.type === ITEM_TYPE || p.type === PUNCH_CARD_TYPE) &&
					(!p.expires || (p.expires > todayDate)) &&
					(showArchived || !p.archived) &&
					(!requirePrograms || !!p.program);
			})
			.sortBy((p) => p.description.toLowerCase())
			.value();
	},
	enabledCurriculumBanks() {
		return OrgsLib.getEnabledCurriculumBanks(this);
	},
	/**
	 * Get the settings for family registration.
	 * Returns something like: {
	 *     requiredContactsCount: number,
	 *     questions: [
	 *         {
	 *             type: string,
	 *             question: string,
	 *             mappedTo: string,
	 *             includedLink: string,
	 *             isRequired: boolean,
	 *             choices: Array<string>
	 *         },
	 *         etc.
	 *     ]
	 * }
	 * @returns {*|{}}
	 */
	getFamilyRegistrationSettings() {
		return this.familyRegistrationSettings ?? {};
	},
	hasWaitListDesignation() {
		if (this.valueOverrides.designations && this.valueOverrides.designations.length) {
			return this.valueOverrides.designations.includes('Wait List');
		}
		return false;
	},
	"pinUniqueAcrossOrgs": function () {
		return this.hasCustomization(AvailableCustomizations.DOOR_LOCKS_ZKTECO) ||
			this.hasCustomization(AvailableCustomizations.WL_PIN_LOGIN);
	},
	"pinLength": function () {
		if (this.pinUniqueAcrossOrgs()) {
			return 6;
		}
		return 4;
	},
	getCheckInCheckOutQrCodesExpireAfterMinutesValue() {
		// Should this go directly on the org?
		return parseInt(this.checkInCheckOutQrCodesExpireAfterMinutes ?? 24 * 60, 10);
	},
	getGenerateWhen() {
		return this.billing?.scheduling?.generateWhen ?? null;
	},
	getGenerateDays() {
		return {
			month: parseInt(this.billing?.scheduling?.generateMonthDay) ?? null,
			week: this.billing?.scheduling?.generateDay ?? null
		}
	},
	getAvailableLedgerCodes() {
		if (!this.billing || !this.billing.billingMaps) {
			return [];
		}
		return Object.entries(this.billing.billingMaps).map(([name, value]) => ({
			name,
			...value
		}));
	}
});

export const Orgs = new Mongo.Collection('orgs', {
	transform: function (doc) {
		return new Org(doc);
	}
});

const defaultOrg = {
	'customizations': {
		"moments/incident/enabled": true,
		"moments/sleep/enabled": true,
		"moments/potty/enabled": true,
		"moments/alert/adminOnly": false,
		"people/types/customerSpecificProfileFields": true,
		"moments/activity/enabled": true,
		"moments/medical/enabled": true,
		"moments/supplies/enabled": true,
		"moments/ouch/enabled": true
	},
	'valueOverrides': {
		"profileFields": [
			{ "name": "birthday", "description": "Birthday", "type": "date" },
			{ "name": "enrollmentDate", "description": "Enrollment Date", "type": "date" },
			{ "name": "withdrawDate", "description": "Withdraw Date", "type": "date" },
			{ "name": "gender", "description": "Gender", "type": "select", "values": ["Male", "Female"] },
			{ "name": "medicalInformation", "description": "Medical Information", "type": "string" },
			{ "name": "allergies", "description": "Allergies", "type": "string" },
			{ "name": "allowMedia", "description": "Can child's photo be posted on social media?", "type": "select", "isFamilyEditable": true, "values": ["Yes", "No"] },
			{ "name": "atAGlanceNotes", "description": "At A Glance Notes", "type": "string" },
			{ "name": "householdInformation", "description": "Household Information", "type": "fieldGroup", "fields": [{ "name": "streetAddress", "description": "Child's Street Address", "type": "string", "isFamilyEditable": true }, { "name": "city", "description": "Child's City", "type": "string", "isFamilyEditable": true }, { "name": "state", "description": "Child's State", "type": "string", "isFamilyEditable": true }, { "name": "zip", "description": "Child's Zip Code", "type": "string", "isFamilyEditable": true }] },
			{ "name": "restrictedPickUp", "description": "Restricted Pick Up", "type": "fieldGroup", "fields": [{ "name": "restrictedFirstName", "description": "Restricted First Name", "type": "string" }, { "name": "restrictedLastName", "description": "Restricted Last Name", "type": "string" }, { "name": "restrictedDescription", "description": "Description", "type": "string" }] }
		]
	},
	'people': [
		{ "origId": "vi3B583Scv7rPNEpP", "firstName": "Jordan", "lastName": "Bing", "checkInGroupOrigId": "ZHZmDgkG59jDqZg92", "checkInGroupName": "Infants Classroom", "defaultGroupId": "ZHZmDgkG59jDqZg92", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1591761600000, "enrollmentDate": 1594008000000, "gender": "Female", "medicalInformation": "Has birthmark on left leg", "allergies": "Penicillin ", "allowMedia": "Yes", "atAGlanceNotes": "Jordan has an older brother, Logan, at our center", "householdInformation": { "streetAddress": "476 Friend Street", "city": "Central Perk", "state": "IN", "zip": "12372" } } },
		{ "origId": "uh7MZehZrt9EwdxzS", "firstName": "Isabella", "lastName": "Skywalker", "checkInGroupOrigId": "ZHZmDgkG59jDqZg92", "checkInGroupName": "Infants Classroom", "defaultGroupId": "ZHZmDgkG59jDqZg92", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1594699200000, "enrollmentDate": 1598241600000, "gender": "Female", "medicalInformation": "N/a", "allergies": "None", "allowMedia": "Yes", "atAGlanceNotes": "Isabella uses a pacifier.", "householdInformation": { "streetAddress": "950 Galaxy Lane", "city": "Nebula", "state": "IN", "zip": "12367" }, "restrictedPickUp": { "restrictedFirstName": "Annie", "restrictedLastName": "Skywalker", "restrictedDescription": "Uncle" } } },
		{ "origId": "mKRqKGaq4TJpnuM5Y", "firstName": "Abigail", "lastName": "Clampett", "checkInGroupOrigId": "wiZij2Dg4EdughpSg", "checkInGroupName": "Toddler Classroom", "defaultGroupId": "wiZij2Dg4EdughpSg", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1548997200000, "enrollmentDate": 1591588800000, "gender": "Female", "medicalInformation": "N/A", "allergies": "None", "allowMedia": "Yes", "householdInformation": { "streetAddress": "9867 Hawks Way", "city": "Westfield", "state": "IN", "zip": "12310" }, "restrictedPickUp": { "restrictedFirstName": "Allison", "restrictedLastName": "Jones", "restrictedDescription": "Previous step parent" } } },
		{ "origId": "kYrdhcF2b2oAbA2xS", "firstName": "Oliver", "lastName": "Keaton", "checkInGroupOrigId": "wiZij2Dg4EdughpSg", "checkInGroupName": "Toddler Classroom", "defaultGroupId": "wiZij2Dg4EdughpSg", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1558324800000, "enrollmentDate": 1592798400000, "gender": "Male", "medicalInformation": "Allergic to bees", "allergies": "Bee stings - Epipen on site", "allowMedia": "Yes", "householdInformation": { "streetAddress": "209 Berkley Road", "city": "Indianpolis", "state": "IN", "zip": "12308" } } },
		{ "origId": "jCW3pD8wZEhNhTBJb", "firstName": "Charlotte", "lastName": "Simpson", "checkInGroupOrigId": "wiZij2Dg4EdughpSg", "checkInGroupName": "Toddler Classroom", "defaultGroupId": "wiZij2Dg4EdughpSg", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1565236800000, "enrollmentDate": 1591588800000, "gender": "Female", "medicalInformation": "Has seasonal allergies", "allergies": "Seasonal allergies", "allowMedia": "No", "atAGlanceNotes": "", "householdInformation": { "streetAddress": "724 Evergreen Road", "city": "Indianapolis", "state": "IN", "zip": "12308" } } },
		{ "origId": "hMAJMtKLap72z64kA", "firstName": "Amelia", "lastName": "Connor", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileEmailAddress": null, "profileData": { "phonePrimary": "************" } },
		{ "origId": "h3oE73gNkrWwxEZsn", "firstName": "Mason", "lastName": "Aadams", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "fpBHYnkT4LH36Cxt7", "firstName": "Mark", "lastName": "Clampett", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileEmailAddress": null, "profileData": { "phonePrimary": "************" } },
		{ "origId": "fAtp68D5yvybqApoF", "firstName": "Elizabeth", "lastName": "Granger", "defaultGroupId": "fPStukNdsxYSium2u", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1459396800000, "enrollmentDate": 1586145600000, "gender": "Female", "medicalInformation": "N/A", "allergies": "None", "allowMedia": "Yes", "householdInformation": { "streetAddress": "23 W. Lincoln Way", "city": "Indianapolis", "state": "IN", "zip": "12306" } } },
		{ "origId": "bYBfu6PGqXNz6Dqno", "firstName": "William", "lastName": "Geller", "defaultGroupId": "ZHZmDgkG59jDqZg92", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1593576000000, "enrollmentDate": 1598846400000, "gender": "Male", "medicalInformation": "Has food allergies", "allergies": "Pears and Bananas", "allowMedia": "Yes", "householdInformation": { "streetAddress": "123 Main Street", "city": "Indianapolis", "state": "IN", "zip": "12308" } } },
		{ "origId": "ZpAkKCxD8eTW2toyn", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "sampleData": true, "firstName": "Mary", "lastName": "Cleaver", "profileEmailAddress": null, "profileData": { "phonePrimary": "************" } },
		{ "origId": "ZErgnHp4T9ubg3vQZ", "firstName": "Mia", "lastName": "Bing", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" }, "profileEmailAddress": null },
		{ "origId": "Z9CBZinjho27gdAeN", "firstName": "Jack", "lastName": "Teacher", "checkInGroupOrigId": "fPStukNdsxYSium2u", "checkInGroupName": "Pre-K Classroom", "defaultGroupId": "fPStukNdsxYSium2u", "type": "staff", "orgId": "wdHtbsqBZDN6a7pZ4" },
		{ "origId": "Yd6hK7SnrRBAe24Xh", "firstName": "Ava", "lastName": "Geller", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "XbZWSZnSN4GCgzHTt", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "sampleData": true, "firstName": "Logan", "lastName": "Bing", "checkInGroupOrigId": "fPStukNdsxYSium2u", "checkInGroupName": "Pre-K Classroom", "defaultGroupId": "fPStukNdsxYSium2u", "profileData": { "birthday": 1464840000000, "enrollmentDate": 1590984000000, "gender": "Male", "medicalInformation": "N/a", "allergies": "None", "allowMedia": "Yes", "householdInformation": { "streetAddress": "476 Friend Street", "city": "Central Perk", "state": "IN", "zip": "12372" } } },
		{ "origId": "TeJrwY4oXR4YZy4zK", "firstName": "Lucas", "lastName": "Connor", "defaultGroupId": "fPStukNdsxYSium2u", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1468987200000, "enrollmentDate": 1578891600000, "gender": "Female", "medicalInformation": "Food Allergy", "allergies": "Peanuts", "allowMedia": "Yes", "atAGlanceNotes": "", "householdInformation": { "streetAddress": "108 Sunbelt Avenue", "city": "Carmel", "state": "IN", "zip": "12309" } } },
		{ "origId": "TbcbwCrFJCz6qeLX3", "firstName": "Daniel", "lastName": "Granger", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "QhDgrHuE7i3omFKYQ", "firstName": "Liam", "lastName": "Skywalker", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "NNK3xXTCd9cnwo7f6", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "sampleData": true, "firstName": "Emily", "lastName": "Cleaver", "checkInGroupOrigId": "fPStukNdsxYSium2u", "checkInGroupName": "Pre-K Classroom", "defaultGroupId": "fPStukNdsxYSium2u", "profileData": { "birthday": 1458878400000, "enrollmentDate": 1586145600000, "gender": "Female", "medicalInformation": "N/A", "allergies": "None", "allowMedia": "Yes", "householdInformation": { "streetAddress": "2948 Butler Way", "city": "Indianapolis", "state": "IN", "zip": "12308" } } },
		{ "origId": "ATHJHmKcmwReS52Du", "firstName": "Harper", "lastName": "Aadams", "checkInGroupOrigId": "wiZij2Dg4EdughpSg", "checkInGroupName": "Toddler Classroom", "defaultGroupId": "wiZij2Dg4EdughpSg", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "birthday": 1560225600000, "enrollmentDate": 1583726400000, "gender": "Female", "medicalInformation": "N/a", "allergies": "None", "allowMedia": "Yes", "atAGlanceNotes": "", "householdInformation": { "streetAddress": "43879 Main St", "city": "Westfield", "state": "IN", "zip": "12310" } } },
		{ "origId": "6xfWMd5Y6bShsMaH5", "firstName": "Harper", "lastName": "Simpson", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "4nTfY2ozmWKzSJkNz", "firstName": "Logan", "lastName": "Darcy", "checkInGroupOrigId": "ZHZmDgkG59jDqZg92", "checkInGroupName": "Infants Classroom", "defaultGroupId": "ZHZmDgkG59jDqZg92", "type": "staff", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "4KPxwyNhHqiT3dyMG", "firstName": "Olivia", "lastName": "Potter", "type": "family", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } },
		{ "origId": "32WdcrjbqaMXnjS2e", "firstName": "Emma", "lastName": "Potter", "checkInGroupOrigId": "ZHZmDgkG59jDqZg92", "checkInGroupName": "Infants Classroom", "defaultGroupId": "ZHZmDgkG59jDqZg92", "type": "person", "orgId": "wdHtbsqBZDN6a7pZ4", "birthday": "", "profileData": { "birthday": 1596254400000, "enrollmentDate": 1600660800000, "gender": "Female", "allergies": "Peas", "allowMedia": "Yes", "medicalInformation": "Has allergies", "atAGlanceNotes": "Emma is a tummy sleeper", "householdInformation": { "streetAddress": "123 Privet Drive", "city": "Petunia", "state": "IN", "zip": "12345" }, "restrictedPickUp": { "restrictedFirstName": "Thomas", "restrictedLastName": "Riddler", "restrictedDescription": "Family neighbor" } } },
		{ "origId": "2q9KNGxKLRby6AMbX", "firstName": "Allyson", "lastName": "Hamilton", "checkInGroupOrigId": "wiZij2Dg4EdughpSg", "checkInGroupName": "Toddler Classroom", "defaultGroupId": "wiZij2Dg4EdughpSg", "type": "staff", "orgId": "wdHtbsqBZDN6a7pZ4", "profileData": { "phonePrimary": "************" } }
	],
	'groups': [
		{ "origId": "wiZij2Dg4EdughpSg", "name": "Toddler Classroom", "capacity": "12", "sortOrder": 1, "preferredCapacity": "10", "enrollmentGoal": "10", "ratio": "6", "ageGroup": { "begin": "1", "end": "3", "type": "years" }, "sleepCheckInterval": "10" },
		{ "origId": "fPStukNdsxYSium2u", "name": "Pre-K Classroom", "sortOrder": 2, "capacity": "16", "preferredCapacity": "14", "enrollmentGoal": "14", "ratio": "8", "sleepCheckInterval": "10", "ageGroup": { "begin": "4", "end": "5", "type": "years" } },
		{ "origId": "ZHZmDgkG59jDqZg92", "name": "Infants Classroom", "capacity": "10", "sortOrder": 0, "ratio": "4", "preferredCapacity": "9", "enrollmentGoal": "9", "ageGroup": { "begin": "0", "end": "12", "type": "months" }, "sleepCheckInterval": "5", "typeInfant": true }
	],
	'relationships': [
		{ "targetId": "NNK3xXTCd9cnwo7f6", "personId": "ZpAkKCxD8eTW2toyn", "relationshipType": "family", "origId": "jTwG4PdGYwnKG4iRR" },
		{ "personId": "hMAJMtKLap72z64kA", "targetId": "TeJrwY4oXR4YZy4zK", "relationshipType": "family", "relationshipDescription": "", "origId": "yEFGqbcXDoMWjoWTq" },
		{ "personId": "hMAJMtKLap72z64kA", "targetId": "kYrdhcF2b2oAbA2xS", "relationshipType": "family", "relationshipDescription": "Mother", "origId": "XkHfFMM5Nu3LtXZve" },
		{ "personId": "fpBHYnkT4LH36Cxt7", "targetId": "mKRqKGaq4TJpnuM5Y", "relationshipType": "family", "relationshipDescription": "", "origId": "zG3gzX4xgnWbkLMP4" },
		{ "personId": "4KPxwyNhHqiT3dyMG", "targetId": "32WdcrjbqaMXnjS2e", "relationshipType": "family", "relationshipDescription": "Mother", "origId": "9QyH8PfDHYqu8DYMv" },
		{ "personId": "QhDgrHuE7i3omFKYQ", "targetId": "uh7MZehZrt9EwdxzS", "relationshipType": "family", "relationshipDescription": "Father", "origId": "vNsJPJAgwrCpjfzr3" },
		{ "personId": "ZErgnHp4T9ubg3vQZ", "targetId": "vi3B583Scv7rPNEpP", "relationshipType": "family", "relationshipDescription": "Mother", "origId": "wadkmfKPphSMt8Xzq" },
		{ "personId": "ZErgnHp4T9ubg3vQZ", "targetId": "XbZWSZnSN4GCgzHTt", "relationshipType": "family", "relationshipDescription": "Mother", "origId": "m4JeD8hC98ENLTH42" },
		{ "personId": "Yd6hK7SnrRBAe24Xh", "targetId": "bYBfu6PGqXNz6Dqno", "relationshipType": "family", "relationshipDescription": "Mother", "origId": "Wov3jbwYaEcZE7xNC" },
		{ "personId": "6xfWMd5Y6bShsMaH5", "targetId": "jCW3pD8wZEhNhTBJb", "relationshipType": "family", "relationshipDescription": "Mother", "origId": "FqtWNnuM9SSvg28iq" },
		{ "personId": "h3oE73gNkrWwxEZsn", "targetId": "ATHJHmKcmwReS52Du", "relationshipType": "family", "relationshipDescription": "Father", "origId": "TNP2zg7GjEXXuXART" },
		{ "personId": "TbcbwCrFJCz6qeLX3", "targetId": "fAtp68D5yvybqApoF", "relationshipType": "family", "relationshipDescription": "Father", "origId": "32oCnTbM3eThpxSSH" },
	]
};

_.extend(Orgs, {
	'current': function (navItem) {
		//console.log("Meteor.isServer ==>", Meteor.isServer)
		if(Meteor.isServer){
			return (async()=>{
				const meteorUser = await Meteor.userAsync();
				if (meteorUser)
					return await Orgs.findOneAsync(meteorUser.orgId);
				return null;
			})();
		}else{
			const meteorUser = Meteor.user();
			if (meteorUser)
				return Orgs.findOne(meteorUser.orgId);
			return null;
		}
	},
	'defaultPlan': function () {
		return {
			plan: "20for20",
			title: "20 For 20",
			description: "$20 per month for up to 20 active care recipients",
			created: new Date().valueOf(),
			seatCount: 20
		};
	},
	'customizableMomentTypes': function () {
		let momentTypes = [
			{
				momentType: "potty",
				prettyName: orgLanguageTranformationUtil.getMomentTypesPottyByOrgLang(this.language),
				flag: "moments/potty/enabled",
				adminOnly: "moments/potty/adminOnly"
			},
			{
				momentType: "food",
				prettyName: "Food",
				flag: "moments/food/enabled",
				adminOnly: "moments/food/adminOnly"
			},
			{
				momentType: "sleep",
				prettyName: "Sleep",
				flag: "moments/sleep/enabled",
				adminOnly: "moments/sleep/adminOnly"
			},
			{
				momentType: "activity",
				prettyName: "Activity",
				flag: "moments/activity/enabled",
				adminOnly: "moments/activity/adminOnly"
			},
			{
				momentType: "medical",
				prettyName: "Medical",
				flag: "moments/medical/enabled",
				adminOnly: "moments/medical/adminOnly"
			},
			{
				momentType: "mood",
				prettyName: "Mood",
				flag: "moments/mood/enabled",
				adminOnly: "moments/mood/adminOnly"
			},
			{
				momentType: "incident",
				prettyName: "Incident",
				flag: "moments/incident/enabled",
				adminOnly: "moments/incident/adminOnly"
			},
			{
				momentType: "supplies",
				prettyName: "Supplies",
				flag: "moments/supplies/enabled",
				adminOnly: "moments/supplies/adminOnly"
			},
			{
				momentType: "learning",
				prettyName: "Learning",
				flag: "moments/learning/enabled",
				adminOnly: "moments/learning/adminOnly"
			},
			{
				momentType: "illness",
				prettyName: "Illness",
				flag: "moments/illness/enabled",
				adminOnly: "moments/illness/adminOnly"
			},
			{
				momentType: "ouch",
				prettyName: "Ouch",
				flag: "moments/ouch/enabled",
				adminOnly: "moments/ouch/adminOnly"
			},
			{
				momentType: "alert",
				prettyName: "Notification",
				flag: "moments/alert/enabled",
				adminOnly: "moments/alert/adminOnly"
			},
			{
				momentType: "portfolio",
				prettyName: "Portfolio",
				flag: "moments/portfolio/enabled",
				adminOnly: "moments/portfolio/adminOnly"
			}
		];

		// if (currentOrg && currentOrg.language != "translationsEnAdultCare" ) {
		// 	momentTypes.push({ momentType: "covidHealth", prettyName: "Health Check", flag: "moments/covidHealth/enabled", adminOnly: "moments/covidHealth/adminOnly"});
		// }

		const allRemainingMoments = this.enabledMomentTypes;
		if (Meteor.isServer) {
			return (async () => {
				if (allRemainingMoments && Array.isArray(allRemainingMoments) && allRemainingMoments.length > 0) {
					var mds = await MomentDefinitions.find({ momentType: { $in: allRemainingMoments } }).fetchAsync();
					customizableMomentTypesExtended(mds, momentTypes);
				}
		
				return momentTypes;
			})();
		} else {
			if (allRemainingMoments && Array.isArray(allRemainingMoments) && allRemainingMoments.length > 0) {
				var mds = MomentDefinitions.find({ momentType: { $in: allRemainingMoments } }).fetch();
				customizableMomentTypesExtended(mds, momentTypes);
			}
	
			return momentTypes;
		}
		
	},
	'defaultOrg': defaultOrg,
	'changeCustomizationValue': async function (orgId, key, value) {
		let updateData = {};
		updateData["customizations." + key] = value;
		await Orgs.updateAsync({ _id: orgId }, { "$set": updateData });
	},
	'updateCheckInCheckOutQrCodesExpireAfterMinutes': async function (orgId, updatedValue) {
		await Orgs.updateAsync({ _id: orgId }, { $set: { checkInCheckOutQrCodesExpireAfterMinutes: parseInt(updatedValue, 10) } });
	},
});

/**
 * Recursively finds related organization IDs based on the provided parameters.
 * @param {null} topmostId - The topmost organization ID.
 * @param {string} currentOrgId - The current organization ID.
 * @param {string} topmostOverrideId - The overridden topmost organization ID.
 * @returns {Array|string} - An array of related organization IDs or a single organization ID.
 */
function findRelatedOrgIds(topmostId, currentOrgId, topmostOverrideId, historicCurrentOrgId="") {
  // If topmostOverrideId is provided, call the function with the overridden ID and flatten the result.
  if (topmostOverrideId) {
    return _.flatten(findRelatedOrgIds(topmostOverrideId, topmostOverrideId, null, currentOrgId));
  } else if (!topmostId) {
    // If topmostId is not provided, retrieve the current and parent organizations and proceed accordingly.
    const currentOrg = Orgs.findOneAsync({ _id: currentOrgId });

		if (!currentOrg) {
			throw new Meteor.Error('invalid-data', 'The current organization not found.', `The Organization ID ${currentOrgId} is invalid.`);
		}

		const parentOrg = currentOrg.parentOrgId && Orgs.findOne({ _id: currentOrg.parentOrgId });

		if (parentOrg) {
			// If the parent organization exists, recursively call the function with the parent ID as current.
			return findRelatedOrgIds(null, parentOrg._id);
		} else {
			// If the parent organization doesn't exist, call the function with the current ID as topmost.
			return findRelatedOrgIds(currentOrgId);
		}

	} else if (!currentOrgId) {
		// If currentOrgId is not provided, call the function with topmostId and flatten the result.
		return _.flatten(findRelatedOrgIds(topmostId, topmostId));
	} else {
		// If both topmostId and currentOrgId are provided, retrieve the child organizations and proceed accordingly.
		const orgs = Orgs.find({ parentOrgId: currentOrgId }).fetch();

    if (orgs.length === 0) {
      // Include historicCurrentOrgId if it exists (not empty string) and is different from currentOrgId
      return (historicCurrentOrgId && historicCurrentOrgId !== currentOrgId)
        ? [currentOrgId, historicCurrentOrgId]
        : [currentOrgId];
    } else {
      // If child organizations exist, recursively call the function for each child and map the results.
      const results = orgs.map(org => findRelatedOrgIds(topmostId, org._id));
      // Include historicCurrentOrgId if it exists (not empty string) and is different from currentOrgId
      return (historicCurrentOrgId && historicCurrentOrgId !== currentOrgId)
        ? [currentOrgId, historicCurrentOrgId, ..._.flatten(results)]
        : [currentOrgId, ..._.flatten(results)];
    }
  }
}

/**
 * Recursively finds related organization IDs based on the provided parameters. This function will be called from server side.
 * @param {string} topmostId - The topmost organization ID.
 * @param {string} currentOrgId - The current organization ID.
 * @param {string} topmostOverrideId - The overridden topmost organization ID.
 * @returns {Array|string} - An array of related organization IDs or a single organization ID.
 */
async function findRelatedOrgIdsAsync(topmostId, currentOrgId, topmostOverrideId, historicCurrentOrgId="") {
  // If topmostOverrideId is provided, call the function with the overridden ID and flatten the result.
  if (topmostOverrideId) {
    return _.flatten(await findRelatedOrgIdsAsync(topmostOverrideId, topmostOverrideId, null, currentOrgId));
  } else if (!topmostId) {
    // If topmostId is not provided, retrieve the current and parent organizations and proceed accordingly.
    const currentOrg = await Orgs.findOneAsync({ _id: currentOrgId });

		if (!currentOrg) {
			throw new Meteor.Error('invalid-data', 'The current organization not found.', `The Organization ID ${currentOrgId} is invalid.`);
		}

		const parentOrg = currentOrg.parentOrgId && await Orgs.findOneAsync({ _id: currentOrg.parentOrgId });

		if (parentOrg) {
			// If the parent organization exists, recursively call the function with the parent ID as current.
			return await findRelatedOrgIdsAsync(null, parentOrg._id);
		} else {
			// If the parent organization doesn't exist, call the function with the current ID as topmost.
			return await findRelatedOrgIdsAsync(currentOrgId);
		}

	} else if (!currentOrgId) {
		// If currentOrgId is not provided, call the function with topmostId and flatten the result.
		return _.flatten(await findRelatedOrgIdsAsync(topmostId, topmostId));
	} else {
		// If both topmostId and currentOrgId are provided, retrieve the child organizations and proceed accordingly.
		const orgs = await Orgs.find({ parentOrgId: currentOrgId }).fetchAsync();

    if (orgs.length === 0) {
      // If no child organizations are found, return the current organization ID.
      // Include historicCurrentOrgId if it exists (not empty string) and is different from currentOrgId
      return (historicCurrentOrgId && historicCurrentOrgId !== currentOrgId)
        ? [currentOrgId, historicCurrentOrgId]
        : [currentOrgId];
    } else {
      // If child organizations exist, recursively call the function for each child and map the results.
      const results = await Promise.all(orgs.map(async org => await findRelatedOrgIdsAsync(topmostId, org._id)));
      // Include historicCurrentOrgId if it exists (not empty string) and is different from currentOrgId
      return (historicCurrentOrgId && historicCurrentOrgId !== currentOrgId)
        ? [currentOrgId, historicCurrentOrgId, ..._.flatten(results)]
        : [currentOrgId, ..._.flatten(results)];
    }
  }
}

if (Meteor.isServer) {
	Orgs.after.update(async function (userId, doc, fieldNames, modifier, options) {
		updateCacheOnOrgChange(fieldNames, doc._id);
		await CustomizationsHooksService.handleCustomizationChanges(doc, this.previous);
	}, { fetchPrevious: true });

	Orgs.after.insert(function (userId, doc) {
		const options = {
			userId,
			newOrgId: doc._id
		}
		if (!Meteor.isTest) {
			Meteor.defer(async function () {
				await Meteor.callAsync('sendAccountingEmail', options);
			});
		}
	});
}

function unavailableOrgMomentTypes(availableTypes) {
		//stock definitions
		_.each(MomentDefinitions.availableForOrg(), function (md) {
			availableTypes.push({ momentType: md.momentType, prettyName: md.momentTypePretty, icon: md.icon });
		});
}

function customizableMomentTypesExtended(mds, momentTypes) {
	_.each(mds, (md) => {
		momentTypes.push({ momentType: md.momentType, prettyName: md.momentTypePretty, flag: `moments/${md.momentType}/enabled`, adminOnly: `moments/${md.momentType}/adminOnly` });
	});
}