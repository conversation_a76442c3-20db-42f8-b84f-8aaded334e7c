import { Meteor } from 'meteor/meteor';
import { MomentTypes } from "../constants/momentTypeConstants";
import { orgLanguageTranformationUtil } from "../util/orgLanguageTranformationUtil";
import { Mongo } from 'meteor/mongo';
import { People } from "./people";
import { Relationship, Relationships } from "./relationships";
import { Orgs } from "./orgs";
import { MomentDefinitions } from "./momentDefinitions";
import { Curriculums } from "./curriculum";
import { Groups } from "./groups";
import _ from '../../lib/util/underscore';
import { MetaMoments } from "./metaMoments";

const Moment = function (doc) {
	_.extend(this, doc);
};

_.extend(Moment.prototype, {
	attachedMedia: function () {
		var outputMedia = [];

		if (this.mediaUrl && this.mediaUrl != "") {
			outputMedia.push({
				mediaUrl: this.mediaUrl,
				mediaToken: this.mediaToken,
				mediaFileType: this.mediaFileType,
				mediaPath: this.mediaPath
			});
		}
		if (this.mediaFiles) outputMedia = outputMedia.concat(this.mediaFiles);

		var baseUrl = Meteor.absoluteUrl();
		if (baseUrl.slice(baseUrl.length - 1) != "/") baseUrl += "/";
		var momentOrgId = this.orgId;
		var momentCreatedBy = this.createdBy;
		var momentId = this._id;

		_.each(outputMedia, function (m, i) {

			m.isVideo = (m.mediaFileType == "video");
			m.isOther = (m.mediaFileType == "application");
			m.isPhoto = (!m.isVideo && !m.isOther && m.mediaPath && m.mediaPath != "");

			m.mediaRedirectorPath = baseUrl + "m/" + momentId + "/" + i;
			if (m.mediaFileType == "video") {
				var mediaPath = momentOrgId + "/" + momentCreatedBy + "/" + m.mediaToken;
				var outputUrl = Meteor.settings.public.photoBaseUrl + "output/video/";
				m.mediaVideoPath = outputUrl + mediaPath;
				m.pathForVideoFormat = function (videoFormat) {
					var mediaPath = momentOrgId + "/" + momentCreatedBy + "/" + m.mediaToken;
					var outputUrl = Meteor.settings.public.photoBaseUrl + "output/video/";
					return outputUrl + mediaPath + "." + videoFormat;
				}
			}

			var mediaPath = m.mediaPath;

			if (typeof mediaPath == 'undefined') mediaPath = momentOrgId + "/" + momentCreatedBy + "/" + m.mediaToken;
			m.getTimelinePhoto = Meteor.settings.public.photoBaseUrl;
			if (m.mediaFileType == "image") {
				m.getTimelinePhoto += "resized/" + mediaPath + "-medium.jpg";
				m.originalPhoto = `${Meteor.settings.public.photoBaseUrl}resized/${mediaPath}-original.jpg`;
			} else if (m.mediaFileType == "video") {
				m.getTimelinePhoto += "output/thumbnails/" + mediaPath + "-00001.png";
			} else {
				m.getTimelinePhoto += "uploads/" + mediaPath;
			}

		});

		return outputMedia;
	},
	durationDescription: function () {
		var durationText = "";
		var startTime = new moment(this.time, "h:mm a");
		var endTime = new moment(this.endTime, "h:mm a");
		durationText += moment.duration(endTime - startTime).humanize() + " (" + this.time + " - " + this.endTime + ")";
		return durationText;
	},
	durationAmount: function () {
		var startTime = new moment(this.time, "h:mm a");
		var endTime = new moment(this.endTime, "h:mm a");

		if (startTime && endTime) {
			return moment.duration(endTime - startTime).asMinutes();
		}
		else
			return 0;
	},
	subscribers: async function () {
		var currentMoment = this;
		var outputSubscribers = [];
		const currentOrg = await Orgs.findOneAsync({ _id: currentMoment.orgId });
		let dynamicMoments;
		if (Meteor.isServer) {
			dynamicMoments = await currentOrg.availableDynamicMomentTypes();
		} else {
			dynamicMoments = currentOrg.availableDynamicMomentTypes();
		}

		const relationshipPeople = await Relationships.find({ targetId: { $in: this.taggedPeople }, relationshipType: "family" }).fetchAsync();
		for (const r of relationshipPeople) {
			const p = await People.findOneAsync(r.personId);
			const dynamicMoment = _.find(dynamicMoments, dm => dm.momentType == currentMoment.momentType);
			if (p && !p.inActive && (r.subscriptions &&
				r.subscriptions[currentMoment.momentType] &&
				(r.subscriptions[currentMoment.momentType]["email"] ||
					r.subscriptions[currentMoment.momentType]["sms"] ||
					r.subscriptions[currentMoment.momentType]["push"]) &&
				(!dynamicMoment || dynamicMoment.availableForRealtime)
			)) {
				p.subscriptions = r.subscriptions[currentMoment.momentType];
				p.activeRelationship = r;
				outputSubscribers.push(p);
			} else if (p && !p.inActive && currentMoment.momentType == "alert") {
				p.subscriptions = {};

				p.subscriptions.email = currentMoment.alertSendTypeEmail;
				p.subscriptions.sms = currentMoment.alertSendTypeText;
				p.subscriptions.push = currentMoment.alertSendTypePush;
				p.activeRelationship = r;
				outputSubscribers.push(p);
			}
		}
		if (currentMoment.momentType == "alert") {
			await People.find({ _id: { $in: this.taggedPeople }, inActive: { $ne: true }, type: { $in: ["staff", "admin"] } }).forEachAsync(function (p) {
				p.subscriptions = {
					"email": currentMoment.alertSendTypeEmail,
					"sms": currentMoment.alertSendTypeText,
					"push": currentMoment.alertSendTypePush
				};
				p.activeRelationship = new Relationship({ targetId: p._id });
				outputSubscribers.push(p);
			});
		}
		if (currentMoment.isDemo) {
			const recipientUser = await Meteor.users.findOneAsync({ "_id": currentMoment.createdBy });
			let recipientPerson = await recipientUser.fetchPerson();
			if (this.taggedPeople && this.taggedPeople.length > 0) {
				recipientPerson.subscriptions = { "email": true, "sms": true };
				recipientPerson.activeRelationship = new Relationship({ targetId: this.taggedPeople[0] });
				outputSubscribers.push(recipientPerson);
			}
		}
		var md = await MomentDefinitions.findOneAsync({ momentType: currentMoment.momentType, "notifications.required": true });
		if (md && md.notifications) {
			for (const notificationDef of md.notifications) {
				var subscriptions = {};
				_.each(notificationDef.channels, function (channel) { subscriptions[channel] = true; });

				for (const recipientRule of notificationDef.recipientMatch) {
					recipientRule["orgId"] = currentMoment.orgId;
					recipientRule["inActive"] = { $ne: true };

					await People.find(recipientRule).forEachAsync(function (p) {
						p.subscriptions = subscriptions;
						p.activeRelationship = new Relationship({ targetId: currentMoment.taggedPeople[0] });

						outputSubscribers.push(p);
					});
				};
			};
		}
		return outputSubscribers;
	},
	medicalAdministeredByName: async function () {
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				var p = await People.findOneAsync(self.medicalAdministeredBy);
				if (p)
					return p.firstName + ' ' + p.lastName;
			})();
		} else {
			var p = People.findOne(this.medicalAdministeredBy);
			if (p)
				return p.firstName + ' ' + p.lastName;
		}
	},
	/** while using foodAmountDescription function on server side use await it */
	foodAmountDescription: function () {
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				if (self.foodType == "Bottle" || self.foodType == "Baby Food" || self.foodType == "Cereal") {
					const org = await Orgs.findOneAsync(self.orgId);
					var bottleDesc = "";
					if (self.foodBottleAmount) bottleDesc += "Bottle Amount: " + self.foodBottleAmount + " oz.\n";
					if (self.foodBottleAmountBreastmilkOffered) bottleDesc += "Breastmilk Offered: " + self.foodBottleAmountBreastmilkOffered + " oz.\n";
					if (self.foodBottleAmountBreastmilkConsumed) bottleDesc += "Breastmilk Consumed: " + self.foodBottleAmountBreastmilkConsumed + " oz.\n";
					if (self.foodBottleAmountFormulaOffered) bottleDesc += "Formula Offered: " + self.foodBottleAmountFormulaOffered + " oz.\n";
					if (self.foodBottleAmountFormulaConsumed) bottleDesc += "Formula Consumed: " + self.foodBottleAmountFormulaConsumed + " oz.\n";
					if (self.foodBottleAmountMilkOffered) bottleDesc += "Milk Offered: " + self.foodBottleAmountMilkOffered + " oz.\n";
					if (self.foodBottleAmountMilkConsumed) bottleDesc += "Milk Consumed: " + self.foodBottleAmountMilkConsumed + " oz.\n";
					if (self.foodBottleAmountBabyFoodConsumed) bottleDesc += "Food Consumed: " + self.foodBottleAmountBabyFoodConsumed + " " + org.foodUnits("babyFood") + "\n";
					if (self.foodBottleAmountCerealConsumed) bottleDesc += "Ceral Consumed: " + self.foodBottleAmountCerealConsumed + " " + org.foodUnits("cereal") + "\n";
					return bottleDesc;
				}
				else if (self.foodType == "Tube") {
					return self.foodTubeAmount + " ml ";
				}
				else {
					if (self.foodItems && self.foodItems.length > 0) {
						return _.map(self.foodItems, (fi) => fi.name + (fi.amount ? " - " + fi.amount : "")).join(", ");
					} else {
						return self.foodAmount || "";
					}
				}
			})();
		} else {
			if (this.foodType == "Bottle" || this.foodType == "Baby Food" || this.foodType == "Cereal") {
				const org = Orgs.findOne(this.orgId);
				var bottleDesc = "";
				if (this.foodBottleAmount) bottleDesc += "Bottle Amount: " + this.foodBottleAmount + " oz.\n";
				if (this.foodBottleAmountBreastmilkOffered) bottleDesc += "Breastmilk Offered: " + this.foodBottleAmountBreastmilkOffered + " oz.\n";
				if (this.foodBottleAmountBreastmilkConsumed) bottleDesc += "Breastmilk Consumed: " + this.foodBottleAmountBreastmilkConsumed + " oz.\n";
				if (this.foodBottleAmountFormulaOffered) bottleDesc += "Formula Offered: " + this.foodBottleAmountFormulaOffered + " oz.\n";
				if (this.foodBottleAmountFormulaConsumed) bottleDesc += "Formula Consumed: " + this.foodBottleAmountFormulaConsumed + " oz.\n";
				if (this.foodBottleAmountMilkOffered) bottleDesc += "Milk Offered: " + this.foodBottleAmountMilkOffered + " oz.\n";
				if (this.foodBottleAmountMilkConsumed) bottleDesc += "Milk Consumed: " + this.foodBottleAmountMilkConsumed + " oz.\n";
				if (this.foodBottleAmountBabyFoodConsumed) bottleDesc += "Food Consumed: " + this.foodBottleAmountBabyFoodConsumed + " " + org.foodUnits("babyFood") + "\n";
				if (this.foodBottleAmountCerealConsumed) bottleDesc += "Ceral Consumed: " + this.foodBottleAmountCerealConsumed + " " + org.foodUnits("cereal") + "\n";
				return bottleDesc;
			}
			else if (this.foodType == "Tube") {
				return this.foodTubeAmount + " ml ";
			}
			else {
				if (this.foodItems && this.foodItems.length > 0) {
					return _.map(this.foodItems, (fi) => fi.name + (fi.amount ? " - " + fi.amount : "")).join(", ");
				} else {
					return this.foodAmount || "";
				}
			}
		}
	},
	illnessSymptomDisplayString: function () {
		var output = "";
		_.each(this.illnessSymptoms, function (i) {
			if (output != "") output += ", ";
			output += i;
		});
		return output;
	},
	"sleepDuration": function () {
		var durationText = "";
		var startTime = new moment(this.time, "h:mm a");
		var endTime = new moment(this.endTime, "h:mm a");
		const duration = moment.duration(endTime - startTime)
		durationText += duration.hours() + ":" + duration.minutes().toString().padStart(2, '0') + " (" + this.time + " - " + this.endTime + ")";
		return durationText;
	},
	assetMoodIcon: function () {
		switch (this.moodLevel || this.mood) {
			case "Happy":
				return "mood_happy";
			case "SoSo":
				return "mood_soso";
			case "Sad":
				return "mood_sad";
		}
	},
	moodIcon: function () {
		switch (this.moodLevel || this.mood) {
			case "Happy":
				return "fa-smile";
			case "SoSo":
				return "fa-meh";
			case "Sad":
				return "fa-frown-open";
		}
	},
	likedByPerson: function (personId) {
		return this.reactions && this.reactions[personId];
	},
	peopleList: async function (showInactives) {
		var outstring = "";
		const currentOrg = await Orgs.current();
		for (const d of this.taggedPeople) {
			var person = await People.findOneAsync({ orgId: currentOrg._id, _id: d });
			if (person) {
				if (outstring != "") outstring += "; ";
				outstring += person.lastName + ", " + person.firstName + (showInactives && person.inActive ? " (i)" : "");
			}
		}
		return outstring;
	},
	momentTypePrettyTranslation: function (orgLanguage = '') {
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				var i18NValue;
				if (this.momentType === 'potty') {
					i18NValue = orgLanguageTranformationUtil.getMomentTypesPotty('prettyName', orgLanguage || (await Orgs.current()).language);
				} else {
					i18NValue = self.momentTypePretty;
				}		
				return i18NValue;
			})();
		} else {
			var i18NValue;
			if (this.momentType === 'potty') {
				i18NValue = orgLanguageTranformationUtil.getMomentTypesPotty('prettyName', orgLanguage || Orgs.current().language);
			} else {
				i18NValue = this.momentTypePretty;
			}
			return i18NValue;
		}
	},
	formatRealTimeEmailFoodDescription: async function () {
		var commentText = (this.comment) ? `- ${this.comment}` : "";
		var foodTypeDesc = (this.foodType) ? this.foodType : "";
		if (this.foodAmount && this.foodAmount == "Not Offered") {
			return ` ${this.foodAmount} ${foodTypeDesc} ${commentText}`;
		}

		var percentAmount = (this.foodAmountPercent) ? `- ${this.foodAmountPercent}` : "";
		return `${(await this.foodAmountDescription())} ${foodTypeDesc} ${percentAmount} ${commentText}`;
	},
	formatRealTimeFoodDescription: async function (prefix) {
		if (this.foodAmount && this.foodAmount == "Not Offered") {
			return ` ${this.foodAmount} ${this.foodType}`;
		}
		return prefix + (await this.foodAmountDescription()) + " " + this.foodType + ": " + (this.comment ? this.comment : "");
	},
	formattedDescription: async function (outputType) {
		outputType = (typeof outputType !== 'undefined') ? outputType : "html";
		var output = "";
		var currentMoment = this;
		const currentOrg = await Orgs.findOneAsync({ _id: currentMoment.orgId });

		switch (this.momentType) {
			case "food":
				if (this.foodType || this.foodAmount) output += "Meal: ";
				if (this.foodAmount) {
					if (this.foodAmount == "Not Offered") {
						output += this.foodAmount
					} else {
						output += " Ate " + this.foodAmount;
					}
				}
				if (this.foodType) {
					if (this.foodAmount) {
						if (this.foodAmount == "Not Offered") {
							output += ` ${this.foodType}`
						} else {
							output += ` of ${this.foodType}`
						}
					} else {
						output += this.foodType
					}
				}
				if (output != "") output += "<br/>";
				if (this.foodBabyFoodType) output += "Type: " + this.foodBabyFoodType + "<br/>";
				if (this.foodType == "Bottle" || this.foodType == "Baby Food" || this.foodType == "Cereal") output += (await this.foodAmountDescription()).replace(/\n/g, "<br/>");
				if (this.foodType == "Tube" && this.foodTubeAmount) output += "Amount: " + this.foodTubeAmount + " ml<br/>";
				if (this.foodAmountPercent) output += "Amount: " + this.foodAmountPercent + "<br/>";
				if (this.foodItems) {
					this.foodItems.forEach((fi) => {
						output += fi.name + (fi.amount ? ": " + fi.amount : "") + "<br/>";
					});
				}
				break;
			case "potty":
				var pottyTypeLabel = orgLanguageTranformationUtil.getMomentTypesPotty(this.pottyType, currentOrg?.language);
				if (this.pottyType) output += "Type: " + pottyTypeLabel + "<br/>";
				if (this.pottyTypeContinence) output += this.pottyTypeContinence + "<br/>";
				if (this.pottyTraining) output += "Training: " + this.pottyTraining + "<br/>";
				if (this.pottyAppliedOintment) output += "Applied Ointment<br/>";
				break;
			case "sleep":
				if (this.endTime)
					output += "Slept for " + this.sleepDuration() + "<br/>";
				else
					output += "Nap started<br/>";
				if (this.sleepDidNotSleep) output += "Did not sleep.<br/>";

				let currentUser, currentPerson;
				try {
					currentUser = await Meteor.userAsync();
					currentPerson = await currentUser?.fetchPerson();
				} catch (e) {
					// Silently continue if we can't get user context
				}

				if (this.sleepChecks && !Meteor.isServer && currentUser && currentPerson && _.contains(['admin', 'staff'], currentPerson?.type)) {
					let sleepCheckDesc = "";
					_.chain(this.sleepChecks)
						.groupBy('personId')
						.map((personSleepChecks, pid) => ({ personId: pid, lastSleepCheckEntry: _.max(personSleepChecks, (psc) => psc.createdAt) }))
						.each((personWithLastSleepCheck) => {
							const person = People.findOne(personWithLastSleepCheck.personId),
								checkMoment = new moment(personWithLastSleepCheck.lastSleepCheckEntry.createdAt);
							const sleepPosition = (personWithLastSleepCheck.lastSleepCheckEntry.sleepPosition) ? `Sleep Position ${personWithLastSleepCheck.lastSleepCheckEntry.sleepPosition}` : "";
							const distressedSleep = (personWithLastSleepCheck.lastSleepCheckEntry.distressedSleep) ? `Signs of Distress: ${personWithLastSleepCheck.lastSleepCheckEntry.distressedSleep}<br/>` : "";
							if (person) sleepCheckDesc += `${person.firstName} ${person.lastName} at ${checkMoment.format("hh:mm a")} ${sleepPosition}<br/>${distressedSleep}`;
						});
					if (sleepCheckDesc != "") output += "<br/>Last sleep check(s):<br/>" + sleepCheckDesc;
				}
				break;
			case "activity":
				if (this.activityType) output += "Type: " + this.activityType + "<br/>";
				if (this.activityEngagement) output += "Engagement: " + this.activityEngagement + "<br/>";
				break;
			case "medical":
				if (this.medicalMedicineType) output += "Type: " + this.medicalMedicineType + "<br/>";
				if (this.medicalMedicationName) output += "Medication: " + this.medicalMedicationName + "<br/>";
				if (this.medicalMedicineAmount) output += "Amount: " + this.medicalMedicineAmount + "<br/>";
				if (this.medicalDoctorName) output += "Doctor: " + this.medicalDoctorName + "<br/>";
				if (this.medicalMedication) output += this.medicalMedication.name + " " + this.medicalMedication.dosage + "<br/>";
				output += "Administered By: " + await this.medicalAdministeredByName() + "<br/>";
				break;
			case "mood":
				if (this.moodLevel)
					output += "<i class=\"fad " + this.moodIcon() + " fa-2x\"></i><br/>";
				break;
			case "prospect":
				if (this.prospectType) output += "Type: " + this.prospectType + "<br/>";
				if (this.prospectMomentMessageSubject) output += "Subject: " + this.prospectMomentMessageSubject + "<br/>";
				if (this.prospectMomentMessageBody) output += "<br/>" + this.prospectMomentMessageBody;
				break;
			case "supplies":
				output += "Supplies Needed: " + this.supplyType + "<br/>";
				break;
			case "learning":
				if (this.learningType) output += "Type: " + this.learningType.replace("`", " > ") + "<br/>";
				if (this.learningCurriculum?.headline) output += "Activity: " + this.learningCurriculum?.headline.replace("`", " > ") + "<br/>";
				const assessmentLevels = currentOrg && currentOrg.learningAssessmentLevels();
				if (this.learningAssessmentLevel != null) {

					output += "Assessment: " + ((assessmentLevels && assessmentLevels.length >= this.learningAssessmentLevel) ? assessmentLevels[this.learningAssessmentLevel].label : this.learningAssessmentLevel) + "<br/>";
				}
				break;
			case "portfolio":
				if (this.portfolioCurriculum) {
					output += "Activity: " + this.portfolioCurriculum.headline + "<br/>";
				}
				const allStandards = await Curriculums.getStandards(),
					availableAssessmentLevels = currentOrg && currentOrg.getAvailableAssessmentLevels();
				_.each(this.portfolioAssessments, (assessment) => {
					const matchedStandard = _.find(allStandards, (s) => s.standardId == assessment.standardId),
						matchedAssessmentLevel = _.find(availableAssessmentLevels, (l) => l.value == assessment.value);
					if (matchedStandard && matchedAssessmentLevel) {
						output += (matchedStandard.category ? matchedStandard.category : matchedStandard.source) + ": " + matchedStandard.benchmark + "<br/>";
						output += matchedAssessmentLevel.label + "<br/>";
					}
				});
				break;
			case "incident":
				if (this.incidentNature) output += "Nature: " + this.incidentNature + "<br/>";
				if (this.incidentLocation) output += "Location: " + this.incidentLocation + "<br/>";
				if (this.incidentActionTaken) output += "Action Taken: " + this.incidentActionTaken + "<br/>";
				break;
			case "illness":
				output += "Symptoms: " + this.illnessSymptomDisplayString() + "<br/>";
				output += `<br/>
					IT IS REQUIRED TO KEEP OR TAKE A CHILD HOME WHEN THE CHILD:<br/>
					<br/>
					Is suffering from one or more of the above symptoms.<br/>
					Is not well enough to take part in the daily activities.<br/>
					A child will be symptom free, without the aid of symptom reducing medications, for a full 24 hours prior to returning to school. We do reserve the right to ask for a note from your family doctor, depending on the illness/disease.<br/>
					`;
				break;
			case "ouch":
				if (this.ouchDescription) output += "Injury/Accident Description: " + this.ouchDescription + "<br/>";
				if (this.ouchCare) output += "Care Provided: " + this.ouchCare + "<br/>";
				if (this.ouchCalledParent || this.ouchContactedParent) {
					output += "Parent Contacted: " + this.ouchContactedParent;
					if (this.ouchCalledParentTime) output += " at " + this.ouchCalledParentTime;
					output += "<br/>";
				}
				if (this.ouchCalledDoctor || this.ouchContactedDoctor) {
					output += "Doctor Contacted: " + this.ouchContactedDoctor;
					if (this.ouchCalledDoctorTime) output += " at " + this.ouchCalledDoctorTime;
					output += "<br/>";
				}
				if (this.ouchNurseNotified) output += "Nurse Notified<br/>";
				if (this.ouchProfessionalMedication) output += "Professional Medication Provided<br/>";
				break;
			default:
				if (this.isDynamic) {
					const momentDefinition = await MomentDefinitions.findOneAsync({ momentType: this.momentType });
					if (momentDefinition) {
						for (const momentField of momentDefinition.momentFields) {
							var fieldValue = this.dynamicFieldValues[momentField.dataId];
							var displayFieldValue = "";
							if (momentField.fieldType == "select") {
								var searchValues = momentField.multi ? fieldValue : [fieldValue];
								_.each(searchValues, function (sv) {
									const definitionDisplayFieldValue = _.find(momentField.fieldValues, function (dfv) { return dfv.fieldValue == sv; });
									if (definitionDisplayFieldValue)
										displayFieldValue += ((displayFieldValue == "") ? "" : ", ") + definitionDisplayFieldValue.fieldValueLabel;
								});
							} else {
								displayFieldValue = fieldValue;
							}
							if (fieldValue) output += momentField.label + ": " + displayFieldValue + "<br/>";
						}
					}
				}
		}
		return output;
	}
});

export const Moments = new Mongo.Collection('moments', {
	transform: function (doc) {
		return new Moment(doc);
	}
});

if (Meteor.isServer) {
	const { afterDeleteMomentTimeCard, afterUpdateMomentTimeCard, afterInsertMomentTimeCard } = require("../../server/timeCardMomentCallbacks");
	Moments.after.insert(async function(userId, doc) {
		const timestampData = {
			momentId: doc._id,
			timestamp: moment().valueOf()
		};
		switch (doc.momentType) {
			case MomentTypes.CHECK_IN:
				await Meteor.callAsync('afterInsertMomentTimeCard', doc);
				timestampData.groupId = doc.checkInGroupId || null;
				timestampData.oldGroupId = null;
				// Only update if there's valid group id and tagged people is not empty
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.groupId) {
					await People.updateAsync({ _id: { $in: doc.taggedPeople } }, {
						$set: {
							"timestamps.moveMomentTimestamp": timestampData
						}
					}, { multi: true });
				}
				break;
			case MomentTypes.CHECK_OUT:
				await Meteor.callAsync('afterInsertMomentTimeCard', doc);
				timestampData.groupId = null;
				timestampData.oldGroupId = doc.checkOutGroupId || null;
				// Only update if there's valid group id and tagged people is not empty
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.oldGroupId) {
					await People.updateAsync({ _id: { $in: doc.taggedPeople } }, {
						$set: {
							"timestamps.moveMomentTimestamp": timestampData
						}
					}, { multi: true });
				}
				break;
			case MomentTypes.MOVE:
				timestampData.groupId = doc.checkInGroupId || doc.checkOutGroupId || null;
				timestampData.oldGroupId = doc.checkOutGroupId || null;
				// Only update if there's valid group id and tagged people is not empty
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.groupId) {
					await People.updateAsync({ _id: { $in: doc.taggedPeople } }, {
						$set: {
							"timestamps.moveMomentTimestamp": timestampData
						}
					}, { multi: true }, function (error) {
						if (error) {
							console.log('error', error);
						}
					});
					const groupTimeStampsData = {
						momentId: doc._id,
						timestamp: timestampData.timestamp,
						taggedPeople: doc.taggedPeople
					}
					if (timestampData.oldGroupId) {
						groupTimeStampsData.moveToGroupId = timestampData.groupId;
						await Groups.updateAsync({ _id: timestampData.oldGroupId }, {
							$set: {
								"timestamps.moveMomentTimestamp": groupTimeStampsData
							}
						});

						delete groupTimeStampsData.moveToGroupId;
						groupTimeStampsData.moveFromGroupId = timestampData.oldGroupId;
						await Groups.updateAsync({ _id: timestampData.groupId }, {
							$set: {
								"timestamps.moveMomentTimestamp": groupTimeStampsData
							}
						});
					}

				}
				break;
			case MomentTypes.NAME_TO_FACE:
				timestampData.groupId = doc.nameToFaceGroupId || null;
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.groupId) {
					await People.updateAsync({ _id: { $in: doc.taggedPeople } }, {
						$set: {
							"timestamps.nameToFaceMomentTimestamp": timestampData
						}
					}, { multi: true }, function (error) {
						if (error) {
							console.log('error', error);
						}
					});
					const groupTimeStampsData = {
						momentId: doc._id,
						timestamp: timestampData.timestamp,
						taggedPeople: doc.taggedPeople
					}
					await Groups.updateAsync({ _id: timestampData.groupId }, {
						$set: {
							"timestamps.nameToFaceMomentTimestamp": groupTimeStampsData
						}
					});
				}
				break;
		}
	})

	Moments.after.update(async function (userId, doc, fieldNames, modifier, options) {
		const timestampData = {
			momentId: doc._id,
			timestamp: moment().valueOf()
		};
		switch (doc.momentType) {
			case MomentTypes.CHECK_IN:
				await Meteor.callAsync('afterUpdateMomentTimeCard', doc, modifier);
				timestampData.groupId = doc.checkInGroupId || null;
				timestampData.oldGroupId = null;
				// Only update if there's valid group id and tagged people is not empty
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.groupId) {
					await People.updateAsync({ _id: { $in: doc.taggedPeople } }, {
						$set: {
							"timestamps.moveMomentTimestamp": timestampData
						}
					}, { multi: true });
				}
				break;
			case MomentTypes.CHECK_OUT:
				await Meteor.callAsync('afterUpdateMomentTimeCard', doc, modifier);
				timestampData.groupId = null;
				timestampData.oldGroupId = doc.checkOutGroupId || null;
				// Only update if there's valid group id and tagged people is not empty
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.oldGroupId) {
					await People.updateAsync({ _id: { $in: doc.taggedPeople } }, {
						$set: {
							"timestamps.moveMomentTimestamp": timestampData
						}
					}, { multi: true });
				}
				break;
			/*case MomentTypes.MOVE:
				timestampData.groupId = doc.checkInGroupId || doc.checkOutGroupId || null;
				timestampData.oldGroupId = doc.checkOutGroupId || null;
				// Only update if there's valid group id and tagged people is not empty
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.groupId) {
					await People.updateAsync({_id: {$in: doc.taggedPeople}}, {
						$set: {
							"timestamps.moveMomentTimestamp": timestampData
						}
					}, {multi: true});
				}
				break;
			case MomentTypes.NAME_TO_FACE:
				timestampData.groupId = doc.nameToFaceGroupId || null;
				if (doc.taggedPeople && doc.taggedPeople.length && timestampData.groupId) {
					await People.updateAsync({_id: {$in: doc.taggedPeople}}, {
						$set: {
							"timestamps.nameToFaceMomentTimestamp": timestampData
						}
					}, {multi: true});
					const groupTimeStampsData = {
						momentId: doc._id,
						timestamp: timestampData.timestamp,
						taggedPeople: doc.taggedPeople
					}
					Groups.update({_id: timestampData.groupId}, {
						$set: {
							"timestamps.nameToFaceMomentTimestamp": groupTimeStampsData
						}
					});
				}

				break;*/
			default:
				if (doc.metaMomentId && doc.momentType == "sleep" && doc.endTime) {
					await MetaMoments.updateAsync({ _id: doc.metaMomentId }, { $pull: { moments: doc._id } });
				}
				break;
		}
	})

	if (Meteor.isServer && !Meteor.isTest) {
		const { afterDeleteMomentTimeCard } = require("../../server/timeCardMomentCallbacks");
		Moments.after.remove(function (userId, doc) {
			if (doc.momentType == "checkin" || doc.momentType == "checkout") {
				afterDeleteMomentTimeCard(doc._id);
			}
		})
	}
}
