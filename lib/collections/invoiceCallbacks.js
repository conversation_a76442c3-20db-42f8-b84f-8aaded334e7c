import currency from 'currency.js';
import { People } from './people';
import { Invoices } from './invoices';
import _ from '../util/underscore';

export const afterUpdateInvoice = async function(doc, modifier) {
	try {
		// TODO: Determine if we should be calling updateByIdWithJournalEntry or not here, as this is a callback function
		const currentUserId = doc.userId || null; // null is likely a bad assumption here, but we need to investigate where this function gets executed
		const currentOrgId = doc.orgId || null; // same assumption from the previous line. also could be worth considering using the currentOrg?
		const updateAmounts = {};
	  var updateFields = (modifier && modifier["$inc"]) ? modifier["$inc"] : {};
		
		if (updateFields["openAmount"]) {
			updateAmounts["openAmount"] = currency(doc["openAmount"]).value;
		}
		
		if(!_.isEmpty(updateAmounts)) {
			Invoices.updateByIdWithJournalEntry(doc["_id"], { "$set": updateAmounts }, {
				userId: currentUserId,
				personId: null, // null is ok because this is a system update
				orgId: currentOrgId,
				reason: `Executed afterUpdateInvoice callback (1)`,
				reasonLocation: 'server/invoiceCallbacks.js/afterUpdateInvoice'
			});
		}

		if (!doc.waitlistProcessed && doc.waitlistPeopleIds && !doc.voidedAt && currency(doc["openAmount"]).intValue <= 0 ) {
			await People.updateAsync({_id: { $in: doc.waitlistPeopleIds }}, {$set: { waitlistAddedDate: new Date().valueOf() }}, {multi: true});
			Invoices.updateByIdWithJournalEntry(doc["_id"], { $set: { waitlistProcessed: true } }, {
				userId: currentUserId,
				personId: null, // null is ok because this is a system update
				orgId: currentOrgId,
				reason: `Executed afterUpdateInvoice callback (2)`,
				reasonLocation: 'server/invoiceCallbacks.js/afterUpdateInvoice'
			});
		}

		if (!doc.waitlistProcessed && 
			_.find(doc.lineItems, li => li.type == "item" && li?.originalItem?.description?.toLowerCase()?.includes("enrollment fee")) && 
			!doc.voidedAt && 
			currency(doc["openAmount"]).intValue <= 0 
		) {
			await People.updateAsync({_id: doc.personId }, {$set: { waitlistAddedDate: new Date().valueOf() }});
			Invoices.updateByIdWithJournalEntry(doc["_id"], { $set: { waitlistProcessed: true } }, {
				userId: currentUserId,
				personId: null, // null is ok because this is a system update
				orgId: currentOrgId,
				reason: `Executed afterUpdateInvoice callback (3)`,
				reasonLocation: 'server/invoiceCallbacks.js/afterUpdateInvoice'
			});
		}

		console.log("Called invoice after update", updateAmounts);
	} catch(err) {
	  console.log("updateInvoice error", err);
	}
};