import { Meteor } from 'meteor/meteor';
import { CardProvidersShared } from '../../imports/card_providers_shared/cardProvidersShared';
import { RasWebhookActions } from '../rasWebhookActions';
import { updateCacheOnPersonChange } from '../updateCache';
import { theCheckinStatsCount } from './counts';
import { InvoiceUtils } from "../util/invoiceUtils";
import { Mongo } from "meteor/mongo";
import { Orgs } from "./orgs";
import { Relationships } from "./relationships";
import { Reservations } from "./reservations";
import { Engagements } from "./engagements";
import { UserInvitations } from "./userInvitations";
import _  from "../util/underscore";
import { PeopleUtils } from '../util/peopleUtils';
import { PeopleLib } from '../peopleLib';
import { addPersonToOngoingDataValidation } from './libUtil';
import { Groups } from './groups';
import { Invoices } from './invoices';
import moment from 'moment-timezone';
import { Curriculum, Curriculums } from './curriculum';
import { processPermissions } from '../permissions';
import { Announcements } from './announcements';
import { Messages } from './messages';
import logger from '../../imports/winston';

export const Person = function (doc) {
	_.extend(this, doc)
};

_.extend(Person.prototype, {
	calculateMerchantFee(accountType, plan, orgPaymentFees) {
		return InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
	},
	allowableProfileFields: function (context, passedOrg) {
		if (Meteor.isServer){
			const self = this;
            return (async () => {
				return await PeopleUtils.allowableProfileFieldsForType(self.type, context, passedOrg);
            })();
        }else{
            return People.allowableProfileFieldsForType(this.type, context, passedOrg);
        }
	},
	//only used in client so no mongo async changes required
	displayableProfileFields: function () {
		return People.filteredProfileFieldsForType(this.type);
	},
	canAddMoments: function () {
		return (this.type == "admin" || this.type == "staff") ? true : false;
	},
	canEditProfile: function () {
		return (this.type == "admin" || this.type == "staff") ? true : false;
	},
	getWithdrawDateRaw: function (hasProfileData) {
		const prefix = hasProfileData;
		if (prefix) {
			return this[`${prefix}`] && this[`${prefix}`]["withdrawDate"] && new moment(this[`${prefix}`]["withdrawDate"]).format("MM/DD/YYYY");
		} else if (this.withdrawDate) {
			return new moment(this.withdrawDate).format("MM/DD/YYYY");
		}
	},
	getEnrollmentDateRaw: function (hasProfileData) {
		const prefix = hasProfileData;
		if (prefix) {
			return this[`${prefix}`] && this[`${prefix}`]["enrollmentDate"] && new moment(this[`${prefix}`]["enrollmentDate"]).format("MM/DD/YYYY");
		} else if (this.enrollmentDate) {
			return new moment(this.enrollmentDate).format("MM/DD/YYYY");
		}
	},
	getWithdrawDate: function (options) {
		const currentOrg = options?.currentOrg ?? Orgs.current();
		const prefix = currentOrg?.profileDataPrefix();
		if (prefix) {
			return this[`${prefix}`] && this[`${prefix}`]["withdrawDate"] && new moment.tz(this[`${prefix}`]["withdrawDate"], currentOrg?.getTimezone()).format("MM/DD/YYYY");
		} else if (this.withdrawDate) {
			return new moment.tz(this.withdrawDate, currentOrg?.getTimezone()).format("MM/DD/YYYY");
		}
	},
	getEnrollmentDate: function (options) {
		const currentOrg = options?.currentOrg ?? Orgs.current();
		const prefix = currentOrg.profileDataPrefix();
		if (prefix) {
			return this[`${prefix}`] && this[`${prefix}`]["enrollmentDate"] && new moment.tz(this[`${prefix}`]["enrollmentDate"], currentOrg.getTimezone()).format("MM/DD/YYYY");
		} else if (this.enrollmentDate) {
			return new moment.tz(this.withdrawDate, currentOrg.getTimezone()).format("MM/DD/YYYY");
		}
	},
	getAvatarUrl: function () {
		if (this.avatarPath && this.avatarPath.length > 0)
			return Meteor.settings.public.photoBaseUrl + "resized/" + this.avatarPath + "-medium.jpg";
		else if (this.avatarUrl && this.avatarUrl.length > 0)
			return this.avatarUrl;
		else
			return "/img/default_user_image_500_500.png";
	},
	hasAvatar: function () {
		return (this.avatarPath || this.avatarUrl);
	},
	getActiveUserEmailAddress: async function () {
		//If use in client side make sure to have UserInvitations subscription
		var u = await this.findAssociatedUser();
		if (!u) return null;

		var emailAddress = u.fetchPrimaryEmail();
		if (u.pending) {
			var invite = await UserInvitations.findOneAsync({ orgId: this.orgId, personId: this._id });
			if (!invite) return null;
		}

		return (emailAddress) ? emailAddress : null;
	},
	getEmailAddress: function () {

		if(Meteor.isServer){
			const self = this;
			return(async ()=>{

				if (self.type == "prospect") {
					return this.profileEmailAddress;
				}

				var u = await self.findAssociatedUser();
				if (u && u.fetchPrimaryEmail()) {
					return u.fetchPrimaryEmail();
				}

				if (["family", "staff", "admin"].indexOf(self.type) > -1) {
					//this.email exists on custom imported users
					var emailToReturn = self.profileEmailAddress || self.email;
					if (emailToReturn) return emailToReturn;
				}

				return null;
			})();
		}else{
			if (this.type == "prospect") {
				return this.profileEmailAddress;
			}

			var u = this.findAssociatedUser();
			if (u && u.fetchPrimaryEmail()) {
				return u.fetchPrimaryEmail();
			}

			if (["family", "staff", "admin"].indexOf(this.type) > -1) {
				//this.email exists on custom imported users
				var emailToReturn = this.profileEmailAddress || this.email;
				if (emailToReturn) return emailToReturn;
			}

			return null;
		}

	},
	getPushTargets: async function () {
		const targets =[]
		const u = await this.findAssociatedUser()
		const snsTargets = 	u?.services?.sns?.deviceTokens
		const expoTarget = u?.services?.expo?.pushToken
		if (snsTargets && snsTargets.length > 0) {
			const recentSnsTargets = snsTargets.slice(-3)
			targets.push(...recentSnsTargets)
		}
		expoTarget && targets.push(expoTarget)
		return targets;
	},
	//getProfileFieldWithName is not been used anywhere
	async getProfileFieldWithName(fieldName) {
		const org = await this.findOrg();
		return await People.getProfileFieldWithNameForType(this.type, fieldName, org);
	},
	getProfileFieldValue: function (fieldName, allowableProfileFields = null, path = '', index = null) {
		if (Meteor.isServer){
			const self = this;
            return (async () => {
				return await PeopleUtils.getProfileFieldValue(self, fieldName, allowableProfileFields, path, index);
            })();
        }else{
			const org = this.findOrg();
			const profileFields = allowableProfileFields ?? this.allowableProfileFields(null, org);
			const getFieldValue = (profileField, path, index = null) => {
				let dataValue;
				if (path.length) {
					let key = (this.profileData ? 'profileData.' : '') + path;
					if (index !== null) {
						key += '.' + index;
					}

					key = key + "." + profileField.name;
					dataValue = _.deep(this, key);
				} else {
					dataValue = (this.profileData && this.profileData[profileField.name]) || this[profileField.name];
				}

				if (profileField && profileField.type === 'query') {
					const qd = org.queryData(profileField.source, { personId: this._id });
					const selectedRecord = _.find(qd, (d) => { return d.id == dataValue });
					if (selectedRecord && selectedRecord.id) {
						return selectedRecord.id;
					}
					if (selectedRecord && selectedRecord.value) {
						return selectedRecord.value;
					}
				}
				return dataValue;
			};

			// If the full path already included in fieldName, just return direct value.
			if (fieldName.includes('.')) {
				return getFieldValue({ name: fieldName }, '');
			}

			let matchedField = null;
			let i = 0;
			for (const profileField of profileFields) {
				if (profileField.name === fieldName) {
					matchedField = profileField;
					return getFieldValue(matchedField, path, index);
				} else if (profileField.type === 'fieldGroup' && profileField.fields?.length) {
					const newPath = path ? path + '.' + profileField.name : profileField.name;
					const index = profileField.multiple ? i : null;
					matchedField = this.getProfileFieldValue(fieldName, profileField.fields, newPath, index);
					if (matchedField) {
						return matchedField;
					}
				}
			}
			return undefined;
		}
	},
	isActive: function () {
		return (!this.inActive);
	},
	// This is used in client only so need to change to async.
	isCheckInAble: function () {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
		return currentPerson && ((this.type == "person" || this.type == "staff" || this.type == "admin") &&
			(currentPerson.type == "admin" || currentPerson.type == "staff")
		) ? true : false;
	},
	availableFamilyCheckins: async function () {

		const familyCheckIns = _.chain(await Relationships.find({ personId: this._id, relationshipType: "family" }).fetchAsync())
			.pluck('targetId')
			.take(async (targets) => { return await People.find({ "_id": { "$in": targets }, "checkedIn": { "$ne": true }, "inActive": { "$ne": true } }).fetchAsync(); })
			.value();
		return familyCheckIns;
	},
	currentFamilyCheckin: function () {
		if (this.familyCheckIn && this.familyCheckIn.checkInTime > new moment().startOf("day").valueOf())
			return this.familyCheckIn;
	},
	absent: function () {
		return !this.checkedIn && this.currentFamilyCheckin() && this.currentFamilyCheckin().absent;
	},
	reservationCancellationToday: function () {
		const startOfDay = new moment().startOf('day').valueOf(),
			endOfDay = new moment().endOf('day').valueOf(),
			canceledReservation = Reservations.findOne({ selectedPerson: this._id, scheduledDate: { "$gte": startOfDay, "$lt": endOfDay }, cancellationReason: { $exists: true } });
		return canceledReservation;
	},
	// This method only used at client side hence no need to change to async await for People.findOne
	currentFamilyCheckinPerson: function () {
		const cfc = this.currentFamilyCheckin(),
			fp = cfc && People.findOne(cfc.checkedInById);
		return fp;
	},
	/** only used in client function no need to change mongo calls to async */
	isInvitable: function () {
		return (this.type == "person" || Meteor.users.findOne({ personId: this._id })) ? false : true;
	},
	findAnnouncementsForToday: async function () {
		var startRange = new Date().setHours(0, 0, 0, 0);
		var endRange = moment(startRange).add(1, 'day').valueOf();
		const q = {
			orgId: this.orgId,
			scheduledDate: { $gte: startRange, $lt: endRange },
			$and: [
				{ $or: [{ selectedGroups: [] }, { selectedGroups: this.defaultGroupId }, { selectedGroups: null }] },
				{ $or: [{ selectedRoles: [] }, { selectedRoles: null }, { selectedRoles: this.type == "person" ? "family" : this.type }] }
			],
		};

		return await Announcements.find(q).fetchAsync();
	},
	findCurriculumForToday: async function () {
		var startRange = new Date().setHours(0, 0, 0, 0);
		var endRange = moment(startRange).add(1, 'day').valueOf();
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				return await Curriculums.find({
					$and: [{
						$or:
							[{ selectedGroups: [] },
								{ selectedGroups: self.defaultGroupId }]
					},
						{ orgId: self.orgId },
					{ scheduledDate: { $gte: startRange, $lt: endRange } }
					]
				}).fetchAsync();
			})();
		} else {
			if (!this.defaultGroupId) return [];

			const query = {
				scheduledDate: { $gte: startRange, $lt: endRange },
				orgId: this.orgId
			};

			const options = { defaultGroupId: this.defaultGroupId };

			const res = await Meteor.callAsync('getCurriculumData', query, options);
			return res.map(d => new Curriculum(d));
		}
	},
	findOrg: function () {
		if (Meteor.isServer){
			const self = this;
            return (async () => {
				return await Orgs.findOneAsync(self.orgId);
			})();
        }else{
			return Orgs.findOne(this.orgId);
		}
	},
	findOwnedRelationships: function (options) {
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				const out = [];
				const selector = {
					targetId: self._id
				};
				if (options?.onlyPrimaryCaregivers) {
					selector.primaryCaregiver = true;
				}
				let personSelector = null;
				await Relationships
					.find(selector)
					.forEachAsync(
						async function (r) {
							personSelector = {
								_id: r.personId
							};
							if (options?.onlyActive) {
								personSelector.inActive = { $ne: true };
							}
							if (await People.findOneAsync(personSelector)) {
								if (options?.noDuplicates && out.find(p => p.personId === r.personId)) {
									return;
								}
								out.push(r);
							}
						}
					);
					return out;
			})();
		} else {
			const out = [];
			const selector = {
				targetId: this._id
			};
			if (options?.onlyPrimaryCaregivers) {
				selector.primaryCaregiver = true;
			}
			let personSelector = null;
			Relationships
				.find(selector)
				.forEach(
					function (r) {
						personSelector = {
							_id: r.personId
						};
						if (options?.onlyActive) {
							personSelector.inActive = { $ne: true };
						}
						if (People.findOne(personSelector)) {
							if (options?.noDuplicates && out.find(p => p.personId === r.personId)) {
								return;
							}
							out.push(r);
						}
					}
				);
				return out;
		}

		//return out;
	},
	findInheritedRelationships: function () {
		return Relationships.find({ personId: this._id });
	},
	hasInheritedRelationships: function () {
		return Relationships.find({personId: this._id}, {limit: 1}).count() > 0 && Orgs.current().hasCustomization("people/showFamilyRelationships");
	},
	findInheritedRelationshipsAsArray: function () {
		return Relationships.find({ personId: this._id });
	},
	findRelatedFamilyPeople: async function (includePersonTypes = false, excludeMessageOptOuts = false) {
		if (this.type === 'person') {
			// If this person is a child, find all the caretakers of this child.
			const relationshipIds = await Relationships.find({ targetId: this._id }).mapAsync((r) => r.personId);
			return People.find({ _id: { $in: relationshipIds }, inActive: { $ne: true } });
		}

		// Otherwise, find all related children of this person
		const relationships = await Relationships.find({ orgId: this.orgId, relationshipType: 'family', personId: this._id }).fetchAsync();
		let allFamilyIds = [];
		let excludedFamilyIds = [];
		for (const relationship of relationships) {
			if (includePersonTypes) {
				allFamilyIds.push(relationship.targetId);
			}

			// Find all relationships to this child and add them in
			const childRelationships = await Relationships.find({ orgId: this.orgId, relationshipType: 'family', targetId: relationship.targetId }).fetchAsync();
			allFamilyIds = allFamilyIds.concat(_.pluck(childRelationships, 'personId'));
			if (excludeMessageOptOuts) {
				// If the family member is opted out from messages for any child, don't include them
				const excludedRelationships = await Relationships.find({
					orgId: this.orgId,
					relationshipType: 'family',
					targetId: relationship.targetId,
					familyMessagesOptOut: true
				}).fetchAsync();
				excludedFamilyIds = excludedFamilyIds.concat(_.pluck(excludedRelationships, 'personId'));
			}
		}

		return People.find({ _id: { $in: allFamilyIds, $nin: excludedFamilyIds }, inActive: { $ne: true } });
	},
	hastActiveMemberships: async function () {
		var currentPerson = this;
		if (currentPerson.type == 'family' || currentPerson.type == 'staff') {
			var user = await Meteor.users.findOneAsync({ $or: [{ personId: currentPerson._id }, { "membership.personId": currentPerson._id }] })
			if (user && user.membership) {
				var personIds = user.membership.map(m => m.personId);
				personIds.push(currentPerson._id);

				var uniqPersonIds = _.uniq(personIds);
				var activePersons = [];
				for(var p of uniqPersonIds){
					var person = await People.findOneAsync({ _id: p });
					if (person && person.isActive()) activePersons.push(p);
				};

				if (activePersons.length > 1) return true;
				return false;
			}
		}
		return false;
	},
	/**not used any where so not making mongo async changes */
	hasMemberships: function () {
		var currentPerson = this;
		if (currentPerson.type == 'family' || currentPerson.type == 'staff') {
			var user = Meteor.users.findOne({ $or: [{ personId: currentPerson._id }, { "membership.personId": currentPerson._id }] })
			if (user && user.membership) {
				return true;
			}
		}

		return false;
	},
	findAssociatedUser: function () {
		if(Meteor.isServer){
			const currentPerson = this;
			return(async ()=>{
				if (currentPerson.type == 'family' || currentPerson.type == 'staff') {
					return await Meteor.users.findOneAsync({ $or: [{ personId: currentPerson._id }, { "membership.personId": currentPerson._id }] })
				}
				return await Meteor.users.findOneAsync({ personId: currentPerson._id });
			})()
		}else{
			var currentPerson = this;
			if (currentPerson.type == 'family' || currentPerson.type == 'staff') {
				return Meteor.users.findOne({ $or: [{ personId: currentPerson._id }, { "membership.personId": currentPerson._id }] })
			}
			return Meteor.users.findOne({ personId: this._id });
		}
	},
	//This proto method used only in client side so no mongo call changes requires for this method
	findCheckedInGroup: function () {
		if (this.checkedIn) {
			return Groups.findOne(this.checkInGroupId);
		}
	},
	findDefaultGroup: function () {
		if(Meteor.isServer) {
			const self = this;
			return (async () => {
				return await Groups.findOneAsync(self.defaultGroupId);
			})();
		} else {
			return Groups.findOne(this.defaultGroupId);
		}
	},
	summarySubscribers: async function () {
		var currentPerson = this;
		var outputSubscribers = [];
		const relatives = await Relationships.find({ targetId: this._id, relationshipType: "family", suspendDailySummaryEmails: { $ne: true } }).fetchAsync();
		for (const r of relatives) {
			const p = await People.findOneAsync(r.personId);
			if (p && !p.inActive) {
				p.activeRelationship = r;
				outputSubscribers.push(p);
			}
		}
		if (currentPerson.type == "staff")
			outputSubscribers.push(currentPerson);
		return outputSubscribers;
	},
	// since used only in client side not need to add async/await
	engagementData: function () {
		var providerCount = 0, familyCount = 0, providerDayCount = 0, familyDayCount = 0, allDays = {};
		var providerTypes = Engagements.getProviderTypes();
		var cutoff = new moment.tz(Orgs.current().getTimezone()).startOf('day').valueOf();
		_.each(this.engagements, function (e) {
			var engagementLabel = e.type + '_' + e.subType;
			var thisDay = new moment(e.createdAt).startOf('day');
			allDays[thisDay] = true;
			if (_.indexOf(providerTypes, engagementLabel) >= 0) {
				providerCount++;
				if (e.createdAt > cutoff) providerDayCount++;
			}
			else {
				familyCount++;
				if (e.createdAt > cutoff) familyDayCount++;
			}
		});

		return {
			providerCount: providerCount,
			familyCount: familyCount,
			providerDayCount: providerDayCount,
			familyDayCount: familyDayCount,
			dayCount: _.size(allDays)
		};
	},
	hasPermissions: async function (action, detail) {
		var orgPermissions = (await Orgs.current()).permissionRules;
		const cuser = await Meteor.userAsync();
		var userPerson = cuser?.fetchPerson();
		if (userPerson.type == "admin") return true;
		var status = true;

		if (orgPermissions && userPerson) {
			var applicablePermissions = _.filter(orgPermissions, function (p) { return p.action == action && p.detail == detail; });

			var applicablePermissionsOrgLevel = _.find(applicablePermissions, function (p) { return p.target == "org" || p.target == "" || !p.target });
			if (applicablePermissionsOrgLevel) status = applicablePermissionsOrgLevel.permission;

			var applicablePermissionsRoleLevel = _.find(applicablePermissions, function (p) { return p.target == "role" && p.subTarget == userPerson.type; });
			if (applicablePermissionsRoleLevel) status = applicablePermissionsRoleLevel.permission;

			var applicablePermissionsStaffTypeLevel = _.find(applicablePermissions, function (p) { return p.target == "staffType" && userPerson.type == "staff" && p.subTarget == userPerson.staffType; });

			if (applicablePermissionsStaffTypeLevel) status = applicablePermissionsStaffTypeLevel.permission;
		}
		return status;
	},
	connectedBankAccount: function () {
		if(Meteor.isServer){
			const self = this;
			return (async () => {
				const org = await Orgs.findOneAsync(self.orgId);
				return PeopleLib.connectedBankAccount(self, org);
			})()
		} else {
			const org = Orgs.findOne(this.orgId);
			return PeopleLib.connectedBankAccount(this, org);
		}
	},
	connectedCreditCard: function () {
		if(Meteor.isServer){
			const self = this;
			return (async () => {
				const org = await Orgs.findOneAsync(self.orgId);
				return PeopleLib.connectedCreditCard(self, org);
			})()
		} else {
			const org = Orgs.findOne(this.orgId);
			return PeopleLib.connectedCreditCard(this, org);
		}
	},
	autoPayMethod: function () {
		return PeopleLib.autoPayMethod(this);
	},
	autoPayAmount: function () {
		return _.deep(this, "billing.autoPayAmount");
	},
	billingStatus: function () {
		if(Meteor.isServer){
			const self = this;
			return (async () => {
				const openInvoices = await Invoices.find({ openAmount: { $gt: 0 } }).fetchAsync();
				const startOfToday = new moment().startOf("day").valueOf();
				let openAmountSum = 0, pastDueAmountSum = 0, earliestDueDate, dueStatus = "";
				_.each(openInvoices, (i) => {
					openAmountSum += i.amountDueForFamilyMember(self._id);
					if (!earliestDueDate || i.dueDate < earliestDueDate) earliestDueDate = i.dueDate;
					if (i.dueDate < startOfToday) pastDueAmountSum += i.amountDueForFamilyMember(self._id);
				});
				if (earliestDueDate < new moment().startOf("day").valueOf())
					dueStatus = "PAST DUE";
				else if (earliestDueDate < new moment().add(1, "days").startOf("day").valueOf())
					dueStatus = "Due Today";
				else
					dueStatus = "Due " + new moment(earliestDueDate).format("M/DD/YYYY");

				return {
					hasAmountDue: openAmountSum > 0,
					currentAmountDue: openAmountSum,
					dueStatus: dueStatus,
					showPastDueAmount: (openAmountSum > pastDueAmountSum && pastDueAmountSum > 0),
					pastDueAmount: pastDueAmountSum
				};
			})();
		}else{
			const openInvoices = Invoices.find({ openAmount: { $gt: 0 } }).fetch();
			return PeopleLib.billingStatus(this, openInvoices);
		}
	},
	billingFamilySplits() {
		return _.deep(this, "billing.familySplits");
	},
	personInitials: function () {
		if (this.firstName && this.lastName)
			return this.firstName.charAt(0) + this.lastName.charAt(0);
		else if (this.firstName)
			return this.firstName.substring(0, 1);
		else if (this.lastName)
			return this.lastName.substring(0, 1);
		else
			return "";
	},
	getRecurringSchedule: function () {
		return Reservations.findOne({ selectedPerson: this._id, recurringProfileSchedule: true });
	},
	getChildCurrentSchedules: function () {
		const now = moment().unix() * 1000;
		const schedules = Reservations.find({ selectedPerson: { $eq: this._id } }).fetch();
		return _.filter(schedules, schedule => schedule.scheduledDate < now && (schedule.scheduledEndDate === null || schedule.scheduledEndDate > now));
	},
	getChildFutureSchedules: function () {
		const now = moment().unix() * 1000;
		const schedules = Reservations.find({ selectedPerson: { $eq: this._id } }).fetch();
		return _.filter(schedules, schedule => schedule.scheduledDate > now);
	},
	//This proto method used only in client side so no mongo call changes requires for this method
	getChildDefaultGroup: function () {
		if (!this.defaultGroupId) {
			return null;
		}
		return Groups.findOne({ _id: this.defaultGroupId });
	},
	accountPending: function () {
		if (this.type != "person" && this.type != "prospect" && !this.inActive) {
			const associatedUsers = this.findAssociatedUser();
			if ( associatedUsers ) {
				return associatedUsers.pending
			} else {
				return true
			}
		}
		return false;
	},
	showAccountStatus: function () {
		return this.type != "person" && this.type != "prospect";
	},
	//This proto method used only in client side so no mongo call changes requires for this method
	getAccountStatus: function () {
		const user = this.findAssociatedUser();
		if (this.inActive) return "";
		if (user && !user.pending) return ""; //active

		const invite = UserInvitations.findOne({ orgId: this.orgId, personId: this._id, used: false });
		if (invite && invite.lastSentAt) return "Invitation Sent";
		if (invite && !invite.lastSentAt) return "Invitation NOT Sent"
		return "";
	},
	openInvoices: async function (passedQuery) {
		const query = _.extend(passedQuery || {}, {
			"personId": { "$in": _.pluck(await Relationships.find({ personId: this._id, relationshipType: "family" }).fetchAsync(), "targetId") },
			"openAmount": { $gt: 0 }
		});

		return await Invoices.find(query, { sort: { createdAt: -1 } }).fetchAsync();
	},
	openInvoicesWithPayerBalances: async function (passedQuery) {
		const familyRelationships = await Relationships.find({
				personId: this._id,
				relationshipType: "family"
		}).fetchAsync();

		const familyMemberIds = _.pluck(familyRelationships, "targetId");

		const query = {
			$and: [
				{ personId: { $in: familyMemberIds } },
				{
					$or: [
						{ openAmount: { $gt: 0 } },
						//BUGS-3164: Look for open amounts in the openPayerAmounts object
						// Matches invoices where open payer has an amount > 0
						{
							$expr: {
								$gt: [
									{
										$size: {
											$filter: {
												input: { $objectToArray: "$openPayerAmounts" },
												as: "item",
												cond: { $gt: ["$$item.v", 0] }
											}
										}
									},
									0
								]
							}
						}
					]
				}
			]
		};

		return await Invoices.find(query, { sort: { createdAt: -1 } }).fetchAsync();
	},

	openSecurityDeposits: async function (options) {
		const openDeposits = [],
			depositPayments = _.chain((await People.find({
				_id: { "$in": _.pluck(await Relationships.find({ targetId: this._id, relationshipType: "family" }).fetchAsync(), "personId") },
				"billing.creditMemos": {
					"$elemMatch": {
						"$or": [
							{ "openAmount": { "$gt": 0 } },
							{ "seizedAmount": { "$gt": 0 } }
						],
						"type": "securityDepositAuto",
						"paidForPersonId": this._id
					}
				}
			}).fetchAsync()))
				.map(p => {
					let cms = _.filter(p.billing.creditMemos, cm => (cm.openAmount > 0 || cm.seizedAmount > 0) && cm.type == "securityDepositAuto" && cm.paidForPersonId == this._id);
					_.each(cms, cm => {
						cm.paidByPersonName = p.firstName + " " + p.lastName;
						cm.paidByPersonId = p._id;
					});
					return cms;
				})
				.flatten()
				.value();
		const query = { personId: this._id, voidedAt: { "$exists": false }, "lineItems.originalItem.refundableDeposit": true };
		if (!options?.includeResolved) query["lineItems.resolvedAt"] = { "$exists": false };
		await Invoices.find(query)
			.mapAsync(invoice => {
				const appliedPayments = depositPayments.filter(dp => dp.paidForInvoiceId == invoice._id),
					originalAmount = invoice.lineItems
						.filter(li => li.type == "item" && li?.originalItem?.refundableDeposit)
						.reduce((amt, li) => amt + li.amount, 0.00),
					resolvedAmount = invoice.lineItems
						.filter(li => li.type == "item" && li?.originalItem?.refundableDeposit && li.resolvedAt)
						.reduce((amt, li) => amt + li.amount, 0.00),
					invoicedAmount = invoice.lineItems
						.filter(li => li.type == "item" && li?.originalItem?.refundableDeposit && !li.resolvedAt)
						.reduce((amt, li) => amt + li.amount, 0.00);
				//console.log("invoice", invoice.invoiceNumber,"applied:", JSON.stringify(appliedPayments, null, 2));
				if (appliedPayments.length > 0) {
					appliedPayments.forEach(ap => {

						openDeposits.push({
							invoicedAt: invoice.createdAt,
							paidAt: ap.createdAt,
							paidByPersonName: ap.paidByPersonName,
							paidByPersonId: ap.paidByPersonId,
							paidForByPersonId: ap.createdBy,
							chargedAmount: invoicedAmount,
							openAmount: ap.openAmount,
							seizedAmount: ap.seizedAmount,
							creditMemoId: ap._id,
							invoiceId: invoice._id,
							invoiceNumber: invoice.invoiceNumber,
							paidAmount: ap.originalAmount,
							resolvedAmount,
							originalAmount
						});
						ap.matchedToInvoice = true;
					});
				}
			});

		const phantomDeposits = depositPayments.filter(dp => !dp.matchedToInvoice && (options?.includeResolved || !dp.manuallyResolvedAt));
		phantomDeposits.forEach(pd => {
			openDeposits.push({
				paidAt: pd.createdAt,
				paidByPersonName: pd.paidByPersonName,
				paidByPersonId: pd.paidByPersonId,
				paidForByPersonId: pd.createdBy,
				chargedAmount: pd.priginalAmount,
				openAmount: pd.openAmount,
				seizedAmount: pd.seizedAmount,
				creditMemoId: pd._id,
				paidAmount: pd.originalAmount,
				resolvedAmount: 0,
				isPhantomDeposit: true
			})
		});
		return openDeposits;
	},
	openUnappliedCash: async function (options) {
		const openOverPayments = [];
		const overPayments = await Invoices.find({
			orgId: options.orgId,
			personId: options.personId,
			$or: [
				{ "credits.payerOverpaymentAmount": { $exists: true } },
				{ "credits.payerOverpaymentAmountRemaining": { $gt: 0 } }
			],
			"credits.payerOverpaymentDestination": "unapplied-cash-account"
		}).fetchAsync();
		overPayments.forEach((invoice) => {
			invoice.credits.forEach((credit, index) => {
				if (credit.payerOverpaymentDestination === 'unapplied-cash-account') {
					if (credit.payerOverpaymentAmountRemaining !== undefined && credit.payerOverpaymentAmountRemaining !== null) {
						credit.heldAmount = credit.payerOverpaymentAmountRemaining;
					} else {
						credit.heldAmount = credit.payerOverpaymentAmount;
					}
					if (credit.heldAmount > 0) {
						credit.creditIndex = index;
						credit.invoiceId = invoice._id;
						credit.invoiceNumber = invoice.invoiceNumber;
						credit.invoiceDate = invoice.invoiceDate;
						openOverPayments.push(credit)
					}
				}
			})
		})
		return openOverPayments;
	},
	availableCreditMemos: function (showAll) {
		return _.filter(_.deep(this, "billing.creditMemos"), (cm) => { return showAll || cm.openAmount >= 0.01; })
	},
	availableCreditMemoBalance: function () {
		return _.reduce(this.availableCreditMemos(), function (memo, cm) { return memo + cm.openAmount; }, 0.00);
	},
	activeBillingSuspension: function () {
		const suspendUntil = _.deep(this, "billing.suspendUntil");
		const todayDate = new moment().startOf("day").valueOf();
		return suspendUntil && (suspendUntil > todayDate) && suspendUntil;
	},
	unreadMessageCount: function () {
		let query = {};
		query["markedArchived"] = { "$ne": this._id };
		query["markedAsRead"] = { "$ne": this._id };
		let count = Messages.find(query).count();
		return count;
	},
	currentMedications() {
		const startOfToday = new moment().startOf('day').valueOf();
		return _.map(_.filter(this.medicationEntries, (m) => { return m.durationType == "ongoing" || m.durationEnd >= startOfToday; }),
			(m) => {
				if (m.frequencyType == "everyhours") m.frequencyDescription = "Every " + m.frequencyAmount + " hours";
				else if (m.frequencyType == "timesperday") m.frequencyDescription = m.frequencyAmount + " times per day";
				else m.frequencyDescription = "Other";
				if (m.durationType == "end-date") m.durationDescription = "Ends " + new moment(m.durationEnd).format("M/D/YYYY");
				else m.durationDescription = "Ongoing";
				m.medicationDescription = m.name + " (" + m.dosage + " - " + m.frequencyDescription + ")";
				return m;
			});
	},
	// since used only in client side not need to add async/await
	glanceFields() {
		let fields = [];
		if (this.lastInformedArrival && this.lastInformedArrival.createdAt >= new moment().startOf('day').valueOf()) {
			fields.push({
				description: "Informed Arrival",
				values: _.map(this.lastInformedArrival.fieldValues, (fieldValue, fieldId) => {
					const fieldDef = _.find(Orgs.current().pinCodeCheckinFields(), (cif) => {
						return cif.dataId == fieldId;
					});
					if (!fieldDef) {
						return ""
					}
					return "<div class='aag-ia-header'>" + fieldDef.label + ":</div><div class='aag-ia-value'>" + fieldValue + "</div>";
				})
			});
		}
		if (this.currentMedications() && this.currentMedications().length > 0) {
			let medicationGlance = {
				description: "Medications",
				values: _.map(this.currentMedications(), (m) => {
					let desc = m.name + ', ' + m.dosage + ', ' + m.frequencyDescription;
					if (m.notes) desc += ', ' + m.notes;
					return desc;
				})
			};
			fields.push(medicationGlance);
		}
		if (this.allergies) {
			fields.push({
				description: "Allergies",
				values: [this.allergies]
			});
		}
		if (this.atAGlanceNotes) {
			fields.push({
				description: "Important Notes",
				values: [this.atAGlanceNotes]
			});
		}
		return fields;
	},
	availableContactMethods(options) {
		const currentOrg = options?.currentOrg ?? Orgs.current();
		const fieldsToCheck = [{ description: "Home Phone", id: "homePhone" }, { description: "Cell Phone", id: "cellPhone" }];
		let list = [], prefix = currentOrg.profileDataPrefix();
		if (prefix) prefix = prefix + ".";

		if (this.profileData && this.profileData.phonePrimary)
			list.push({ description: "Primary Phone", value: this.profileData.phonePrimary });
		else if (this.phonePrimary)
			list.push({ description: "Primary Phone", value: this.phonePrimary });

		if (this.profileData && this.profileData.address) {
			list.push({ description: "Address", value: this.profileData.address + ', ' + this.profileData.city + ', ' + this.profileData.state + ', ' + this.profileData.zipcode });
		}
		_.each(fieldsToCheck, (f) => {
			const idx = prefix + f.id;
			if (_.deep(this, idx))
				list.push({ description: f.description, value: _.deep(this, idx) });
		});
		return list;
	},
	'calcAgeRawFromDate': function (hasProfileData, method = "years", fromDateValue) {
		if ((this.type == "person" || this.type == "prospect")) {
			const prefix = hasProfileData;
			const val = prefix ? (this[prefix] && this[prefix].birthday) : this.birthday;
			let birthdayMoment = null;
			if (typeof val === "string") {
				birthdayMoment = val && new moment(val, 'MM/DD/YYYY');
			} else {
				birthdayMoment = val && new moment(val);
			}

			if (val && (birthdayMoment && birthdayMoment.isValid())) {
				const startDate = new moment.utc(fromDateValue);
				const diffDuration = moment.duration(startDate.diff(birthdayMoment));
				return method == "years" ? `${diffDuration.years()}y ${diffDuration.months()}m` : moment().diff(birthdayMoment, method, true).toFixed(1);;
			}
		}
	},
	'calcAgeRaw': function (hasProfileData, method = "years") {
		return this.calcAgeRawFromDate(hasProfileData, method, new moment().valueOf());
	},
	'calcAge': async function (method = 'years') {
		if ((this.type == "person" || this.type == "prospect")) {
			const prefix = (await Orgs.current()).profileDataPrefix();
			const timezone = (await Orgs.current()).getTimezone();
			const val = prefix ? (this[prefix] && this[prefix].birthday) : this.birthday;
			if (!val || typeof val === "undefined") {
				return "No Age"
			}
			else {
				const format = (typeof val === 'string') ? 'MM/DD/YYYY' : 'x';
				const birthdayMoment = val && new moment.tz(val, format, timezone);
				if (val && birthdayMoment.isValid()) {
					const diffDuration = moment.duration(moment().diff(birthdayMoment));
					return method == "years" ? `${diffDuration.years()}y ${diffDuration.months()}m` : moment().diff(birthdayMoment, method, true).toFixed(1);;
				}
			}

		}
	},
	groupedImmunizations(options) {
		const prefix = options?.prefix ?? Orgs.current().profileDataPrefix();
		const currentOrg = options?.currentOrg ?? Orgs.current();
		const val = prefix ? (this[prefix] && this[prefix].birthday) : this.birthday;
		const birthdayMoment = val && new moment(val),
			validBirthday = birthdayMoment && birthdayMoment.isValid(),
			ageMonths = validBirthday && moment().diff(birthdayMoment, 'months', true);
		let groupedRecords = [];
		_.each(currentOrg.availableImmunizations(), (immunization) => {
			const immunizationEntries = _.filter(this.immunizationEntries, i => {
				if (immunization._id) {
					return immunization._id == i.immunizationDefinitionId
				} else {
					return i.type == immunization.type
				}
			}),
				sortedEntries = _.sortBy(immunizationEntries, "date"),
				monthsRequired = (immunization.monthsRequired || []),
				dosesByNow = validBirthday && monthsRequired.filter(m => m <= ageMonths).length,
				dosesIn30 = validBirthday && monthsRequired.filter(m => m <= ageMonths + 1).length,
				nextDoseDue = validBirthday && monthsRequired.length > 0 &&
					monthsRequired.length > immunizationEntries.length &&
					monthsRequired[immunizationEntries.length],
				dueDate = (nextDoseDue >= 0) && birthdayMoment && birthdayMoment.clone().add(nextDoseDue, "months").format("MM/DD/YYYY"),
				exemptionEntry = _.find(immunizationEntries, ie => ie.exemption);

			let retDueDate = !immunization.exempt && !exemptionEntry && dueDate;
			let retDueSoon = !immunization.exempt && !exemptionEntry && sortedEntries.length < dosesIn30 && sortedEntries.length >= dosesByNow;
			let retOverdue = !immunization.exempt && !exemptionEntry && sortedEntries.length < dosesByNow;
			if (immunization.annual && !immunization.exempt && !exemptionEntry) {
				retDueSoon = false;
				retOverdue = false;
				retDueDate = "";
				if (sortedEntries.length > 0) {
					const lastEntry = _.last(sortedEntries);
					if (lastEntry) {
						const lastEntryMoment = new moment(lastEntry.date);
						const nowMoment = new moment();
						const nextEntry = lastEntryMoment.add(12, 'months');
						if (nextEntry.diff(nowMoment, 'days') < 30 && nextEntry.diff(nowMoment, 'days') > 0) {
							retDueSoon = true;
						} else if (nextEntry.diff(nowMoment, 'days') < 0) {
							retOverdue = true;
						}
						retDueDate = nextEntry.format("MM/DD/YYYY");
					}
				}
			}

			const rec = {
				immunizationDefinitionId: immunization._id || null,
				type: immunization.type,
				doses: sortedEntries.map(se => { return { date: se.date, _id: se._id, exemptDose: se.exemptDose } }),
				dose1: sortedEntries.length > 0 && sortedEntries[0].date,
				dose2: sortedEntries.length > 1 && sortedEntries[1].date,
				dose3: sortedEntries.length > 2 && sortedEntries[2].date,
				dose4: sortedEntries.length > 3 && sortedEntries[3].date,
				dueDate: retDueDate,
				dueSoon: retDueSoon,
				overdue: retOverdue,
				exemption: exemptionEntry && exemptionEntry.exemption
			};
			groupedRecords.push(rec);
		});

		return groupedRecords;
	},
	'findScopedOrgs': async function (options) {
		const org = await Orgs.findOneAsync(this.orgId);
		let sourceOrgs = [];
		if (options?.includeBrands) {
			const scopedOrg = await org.findScopedOrgs(options?.topmostOrgId ?? this.topmostOrgId);
			sourceOrgs = _.map(scopedOrg, o => ({
				_id: o._id,
				name: o.name,
				parentOrgId: o.parentOrgId,
				enableSwitchOrg: o.enableSwitchOrg,
				brands: o.brands,
				selectedBrand: o.selectedBrand
			}));
		} else {
			const scopedOrg = await org.findScopedOrgs(options?.topmostOrgId ?? this.topmostOrgId);
			sourceOrgs = _.map(scopedOrg, o => ({
				_id: o._id,
				name: o.name,
				parentOrgId: o.parentOrgId,
				enableSwitchOrg: o.enableSwitchOrg
			}));
		}
		const orgs = [];
		//console.log("sourceOrgs", sourceOrgs);
		const recursivelyFindName = function (oId, label) {
			const o = _.find(sourceOrgs, org => org._id == oId),
				p = o.parentOrgId && _.find(sourceOrgs, org => org._id == o.parentOrgId),
				newLabel = o.name + (label ? ">" + label : "");
			if (o.parentOrgId && p)
				return recursivelyFindName(o.parentOrgId, newLabel);
			else
				return newLabel;
		};

		_.each(sourceOrgs, o => {
			const newOrg = {};
			newOrg.edge = !_.find(orgs, lo => lo.parentOrgId == o._id);
			newOrg.topLevel = !o.parentOrgId;
			newOrg.fullName = recursivelyFindName(o._id);
			newOrg.enableSwitchOrg = o.enableSwitchOrg;
			newOrg._id = o._id;
			newOrg.name = o.name;
			newOrg.parentOrgId = o.parentOrgId;
			if (options?.includeBrands) {
				newOrg.selectedBrand = o.selectedBrand || null;
			}
			orgs.push(newOrg);
		});

		if (options?.includeBrands) {
			for (const org of sourceOrgs) {
				if (org.brands && org.brands.length) {
					for (const brand of org.brands) {
						const newOrg = {};
						newOrg.edge = !_.find(orgs, lo => lo.parentOrgId == org._id);
						newOrg.topLevel = !org.parentOrgId;
						newOrg.fullName = recursivelyFindName(org._id);
						newOrg.enableSwitchOrg = org.enableSwitchOrg;
						newOrg._id = brand._id;
						newOrg.name = brand.name;
						newOrg.parentOrgId = org.parentOrgId;
						newOrg.isBrand = true;
						orgs.push(newOrg);
					}
				}
			}
		}

		if (((this.masterAdmin && this.topmostOrgId) || options?.topmostOrgId || this.masterAdminGlobal || this.superAdmin) && !(options && options.childrenOnly)) {
			return orgs;
		}
		else if (org.parentOrgId && !org.enableSwitchOrg) {
			return _.filter(orgs, o => o.parentOrgId == org.parentOrgId || o._id == org.parentOrgId);
		}
		else {
			return _.filter(orgs, o => o.parentOrgId == org._id || (!(options && options.childrenOnly) && o._id == org._id));
		}
	},
	'topmostOrg': async function () {
		const allOrgs = await this.findScopedOrgs(),
			topmostOrg = (this.topmostOrgId && (allOrgs.find(o => o._id == this.topmostOrgId))) ||
				(this.masterAdmin && !(this.masterAdminGlobal || this.superAdmin) && allOrgs.find(o => o.enableSwitchOrg)) ||
				((this.masterAdminGlobal || this.superAdmin) && (allOrgs.find(o => o.topLevel))) ||
				{ _id: null, name: "N/A" };
		//console.log("topmostOrg", allOrgs, this.topmostOrgId, topmostOrg);
		return topmostOrg;
	},
	'isMasterAdmin': function () {
		return (this.masterAdmin || this.masterAdminGlobal) ? true : false;
	},
	availablePayMethodAction(action, method, currentOrg) {
		if (Meteor.isServer) {
			const self = this;
			return (async () => {
				currentOrg = currentOrg ?? await Orgs.current();
				const requireBankAccount = currentOrg?.hasCustomization("billing/queueAutopayments/enabled"),
					requirePaymentMethod = currentOrg?.hasCustomization("billing/requirePaymentMethod/enabled"),
					bankAccountAction = (requireBankAccount ||
						(requirePaymentMethod && (!await self.connectedBankAccount() || !await self.connectedCreditCard()))
					) ? "replace" : "remove",
					cardAction = requirePaymentMethod && (!await self.connectedBankAccount() || !await self.connectedCreditCard()) ? "replace" : "remove";

				return (method == "bank_account" && action == bankAccountAction) ||
					(method == "card" && action == cardAction);
			})()
		} else {
			const org = Orgs.current();
			return PeopleLib.availablePayMethodAction(this, org, action, method);
		}
	},
	enabledBillingPlans(todayDate, showExpired, org = null) {
		if (Meteor.isServer) {
			return (async () => {
				return await PeopleUtils.enabledBillingPlansAsync(todayDate, showExpired, org = null, this)
			})()
		} else {
			org = org ?? Orgs.current();
			return _.chain(_.deep(this, "billing.enrolledPlans"))
				.filter(ep => !ep.expirationDate || showExpired || ep.expirationDate > todayDate)
				.map((ep) => {
					const timezone = org.getTimezone();
					const planInfo = _.find(org.billing.plansAndItems, (p) => { return p._id == ep._id; });
					if (!planInfo) return;
					ep.masterPlanInfo = planInfo;
					ep.calculatedAmount = (ep.overrideRate !== null && ep.overrideRate !== undefined) ? ep.overrideRate : planInfo.amount;
					if (planInfo.scaledAmounts && planInfo.scaledAmounts.length && !ep.overrideRate && ep.reservationId) {
						const reservation = Reservations.findOne({ _id: ep.reservationId, selectedPerson: this._id });
						if (reservation && reservation.recurringDays && reservation.recurringDays.length) {
							// If the current scaled amounts is somehow a different length than when the plan was enrolled we need a fallback.
							const index = Math.min(reservation.recurringDays.length - 1, planInfo.scaledAmounts.length - 1);

							// This way the length of recurringDays won't exceed the potential lengths of scaledAmounts.
							ep.calculatedAmount = planInfo.scaledAmounts[index];
						}
					}
					ep.enrollmentDateFormatted = new moment.tz(ep.enrollmentDate, timezone).format("MM/DD/YYYY");
					ep.expirationDateFormatted = ep.expirationDate && new moment.tz(ep.expirationDate, timezone).format("MM/DD/YYYY");
					ep.enrollmentForecastStartDateFormatted = ep.enrollmentForecastStartDate && new moment.tz(ep.enrollmentForecastStartDate, timezone).format("MM/DD/YYYY");
					ep.enrollmentForecastEndDateFormatted = ep.enrollmentForecastEndDate && new moment.tz(ep.enrollmentForecastEndDate, timezone).format("MM/DD/YYYY");
					ep.isExpired = ep.expirationDate && ep.expirationDate <= new moment().valueOf();
					if (ep.discountAmount && ep.discountAmount > 0) {
						const discountDefinition = _.find(org.availableDiscountTypes(true, true), (d) => { return d.type == ep.discountType; });
						ep.discountDescription = "Discount/Credit: " + (discountDefinition ? discountDefinition.description : "") + " " + numeral(ep.discountAmount).format('$0.00');
					}
					if (ep.allocations) {
						ep.discountDescription = "";
						ep.allocations.forEach(a => {
							const isDiscount = a.allocationType === "discount";
							const isReimbursable = a.allocationType === "reimbursable";
							const isReimbursableWithCopay = a.allocationType === "reimbursable-with-copay";

							if (isDiscount) {
								const isExpired = a.discountExpires && a.discountExpires < todayDate;
								if (isExpired && !showExpired) {
									return;
								}

								if (a.discountType === "coupon" && a.code) {
									const coupon = _.find(org.availableCouponCodes(), (c) => { return a.code === c.code.toUpperCase(); });
									if (coupon) {
										ep.discountDescription += "Discount: Coupon";
										if (coupon.expirationDate && coupon.expirationDate < todayDate) {
											ep.discountDescription += " (exp. " + new moment.tz(coupon.expirationDate, timezone).format("MM/DD/YYYY") + ")";
										}
										ep.discountDescription += " " + ((coupon.amountType === "percent") ? coupon.amount + "%" : numeral(coupon.amount).format('$0.00'));
										ep.discountDescription += "<br/>";
									}
								} else {
									const discountDefinition = _.find(org.availableDiscountTypes(true, true), (d) => { return d.type == a.discountType; });
									if (discountDefinition) ep.discountDescription += "Discount: " + discountDefinition.description;
									if (a.discountExpires) ep.discountDescription += " (exp. " + new moment.tz(a.discountExpires, timezone).format("MM/DD/YYYY") + ")";
								}
							} else if (isReimbursable || isReimbursableWithCopay) {
								const isExpired = a.payerEndDate && a.payerEndDate < todayDate;
								if (isExpired && !showExpired) {
									return;
								}

								const availablePayers = org.availablePayerSources(true);
								const payerDefinition = availablePayers.find(payer => { return payer.type === a.reimbursementType; });

								if (payerDefinition && isReimbursable) {
									ep.discountDescription += "Reimbursable: " + payerDefinition.description;
								}

								if (isReimbursableWithCopay) {
									ep.discountDescription += a.allocationDescription;
								}

								if (a.payerStartDate || a.payerEndDate) {
									ep.discountDescription += " (" +
										(a.payerStartDate ? "starts " + new moment.tz(a.payerStartDate, timezone).format("MM/DD/YYYY") : "") +
										(a.payerEndDate ? " ends " + new moment.tz(a.payerEndDate, timezone).format("MM/DD/YYYY") : "") +
										")";
								}
							} else {
								ep.discountDescription += a.allocationDescription;
							}
							ep.discountDescription += " " + ((a.amountType === "percent") ? a.amount + "%" : numeral(a.amount).format('$0.00'));
							ep.discountDescription += "<br/>";
						});
					}
					return ep;
				})
				.filter(ep => ep)
				.value();
		}


	},
	async getPrimaryPhoneNumber() {
		const org = await Orgs.findOneAsync(this.orgId);
		const prefix = org.profileDataPrefix();
		if (prefix) {
			if (this[`${prefix}`] && this[`${prefix}`]['phonePrimary']) {
				/** @returns {string} Phone number */
				return this[`${prefix}`]['phonePrimary'];
			}

			if (this[`${prefix}phonePrimary`]) {
				/** @returns {string} Phone number */
				return this[`${prefix}phonePrimary`];
			}
		}

		if (this['profileData'] && this['profileData']['phonePrimary']) {
			/** @returns {string} Phone number */
			return this['profileData']['phonePrimary'];
		}
		if (this['phonePrimary']) {
			/** @returns {string} Phone number */
			return this['phonePrimary'];
		}

		/** @returns {null} */
		return null;
	},
	documentRepositoryItems(userPerson, showArchived) {
		const person = this;
		const org = Orgs.current();
		const docs = person && person.documentItems;
		const documents =
			  person &&
			  person.type == 'person' &&
			  userPerson &&
			  org &&
			  _.chain(org.documentDefinitions)
				.filter((dd) => {
				  const doc = docs && docs[dd._id];
				  return (
					(showArchived || !dd.deletedAt) &&
					(!dd.hideNotUploaded || doc) &&
					(showArchived || !doc || !doc.archivedAt) &&
					!_.contains(person.documentExemptions, dd._id) &&
					(!dd.assignmentType ||
					  dd.assignmentType == 'all' ||
					  (dd.assignmentType == 'groups' && _.contains(dd.selectedGroupIds, person.defaultGroupId)) ||
					  (dd.assignmentType == 'individuals' && _.contains(person.documentAssignments, dd._id)))
				  );
				})
				.map((dd, i) => {
				  const doc = docs && docs[dd._id];
				  dd.showViewTemplate = dd.repositoryKey && true;
				  dd.showUploadDocument = userPerson.type == 'admin' || userPerson.type == 'family';
				  dd.showReviewDocument = doc;
				  dd.showApproveRejectDocument = userPerson.type == 'admin' && doc && !(doc.approvedAt || docs.rejectedAt);
				  dd.showExemptDocument =
					userPerson.type == 'admin' &&
					(!dd.assignmentType || dd.assignmentType == 'all' || dd.assignmentType == 'groups');
				  dd.templateId = dd._id;
				  dd.documentId = doc && dd._id;
				  dd.showUnassignDocument =
					userPerson.type == 'admin' && _.contains(person.documentAssignments, dd._id) && !dd.documentId;
				  dd.showArchiveDocument = doc && !doc.archivedAt && (userPerson.type == 'admin' || userPerson.type == 'staff');
				  dd.isArchived = (doc && doc.archivedAt) || dd.deletedAt;
				  dd.signatureRequired = dd.templateOption == 'signature';
				  dd.acknowledgmentRequired = dd.templateOption == 'ack';

				  if (doc && doc.templateOptionResult) {
					if (doc.templateOptionResult.action == 'ack' && dd.acknowledgmentRequired) {
					  dd.displayTemplateOptionResult = `Acknowledged by ${doc.templateOptionResult.personName} on ${new moment(doc.templateOptionResult.date).format('M/D/YYYY')}`;
					  dd.ignoreForParent = true;
					}
				  }

				  if (doc && doc.approvedAt) {
					dd.status = 'Approved ' + moment(doc.approvedAt).format('M/D/YYYY');
					dd.ignoreForParent = true;
				  } else if (doc && doc.rejectedAt) {
					dd.status = 'Rejected ' + moment(doc.rejectedAt).format('M/D/YYYY');
				  } else if (doc && doc.createdAt) {
					dd.status = 'Uploaded ' + moment(doc.createdAt).format('M/D/YYYY');
					dd.ignoreForParent = true;
				  } else {
					dd.status = 'Not uploaded';
				  }
				  return dd;
				})
				.sortBy((dd) => {
				  return dd.section + '|' + dd.name;
				})
				.value();
		if (userPerson.type === 'family') {
			return documents.filter((doc) => !doc.ignoreForParent);
		}
		return documents;
	}
});

export const People = new Mongo.Collection('people', {
	transform: function (doc) {
		return new Person(doc);
	}
});

_.extend(People, {
	allowableProfileTypes: function () {
		return [{ name: "This" }, { name: "that" }];
	},
	/**
	 * Get all of the profile field fields that can actually have inputs.
	 * This excludes the fieldGroup item itself but not the fields in the field group.
	 *
	 * @param personType
	 * @param context
	 * @param passedOrg
	 * @returns {*[]}
	 */
	getAllProfileFieldInputFields: function (personType, context, passedOrg) {
		if (Meteor.isServer) {
            return (async () => {
				const profileFields = await People.allowableProfileFieldsForType(personType, context, passedOrg);
				const returnedFields = [];
				const addFields = (fields) => {
					for (const field of fields) {
						if (field.type === 'fieldGroup' && field.fields?.length) {
							addFields(field.fields);
						} else {
							returnedFields.push(field);
						}
					}
				};
				addFields(profileFields);
				return returnedFields;
			})();
		} else {
			const profileFields = People.allowableProfileFieldsForType(personType, context, passedOrg);
			const returnedFields = [];
			const addFields = (fields) => {
				for (const field of fields) {
					if (field.type === 'fieldGroup' && field.fields?.length) {
						addFields(field.fields);
					} else {
						returnedFields.push(field);
					}
				}
			};
			addFields(profileFields);
			return returnedFields;
		}
	},
	//when calling from server side user await as it will return promise
	allowableProfileFieldsForType: function (personType, context, passedOrg) {
		if (Meteor.isServer) {
			return (async () => {
				return await PeopleUtils.allowableProfileFieldsForType(personType, context, passedOrg);
			})();
		} else {
			let currentPerson, org = passedOrg;
			if (context) {
				currentPerson = context.person;
				org = context.org;
			} else if (!org) {
				const meteorUser = Meteor.user();
				currentPerson = meteorUser && meteorUser.fetchPerson();
				org = Orgs.current();
			}
			if (!org) return [];

			let options;

			switch (personType) {
				case "prospect":
					options = [
						{ name: "registrationSubmissionDate", description: "Submission Date", type: "date" },
						{ name: "enrollmentDate", description: "Enrollment Date", type: "date" },
					];
					if (org.hasCustomization("people/types/customerSpecificInquiryProfileFields")) {
						if (org.valueOverrides && org.valueOverrides.inquiryProfileFields)
							options = options.concat(org.valueOverrides.inquiryProfileFields);
					}
					break;
				case "person":
					options = [
						{ name: "birthday", description: "Birthday", type: "date" },
						{ name: "enrollmentDate", description: "Enrollment Date", type: "date" },
						{ name: "withdrawDate", description: "Withdraw Date", type: "date" },
						{ name: "favoriteColor", description: "Favorite Color", type: "string" },
						{ name: "medicalInformation", description: "Medical Information", type: "text" },
						{ name: "allergies", description: "Allergies", type: "string" },
						{ name: "atAGlanceNotes", description: "At A Glance Notes", type: "text" }
					];

					if (!org.hasCustomization("people/types/hideGuardianInformation"))
						options.push({ name: "guardianInformation", description: "Guardian Information", type: "text" });

					if (org.hasCustomization("people/types/customerSpecificProfileFields"))
						options = org.valueOverrides ? org.valueOverrides.profileFields : [];

					if (org.hasCustomization("customer/misc/VillageProjectAfrica")) {
						options = options.concat([
							{ name: "gender", description: "Gender", type: "select", values: ["Male", "Female"] },
							{ name: "grade", description: "Current Grade in School", type: "string" },
							{ name: "teacher", description: "Teacher Name", type: "string" },
							{ name: "yearEntered", description: "Year Entered School", type: "string" },
							{ name: "livesWith", description: "People in Household", type: "text" },
							{ name: "healthConcerns", description: "Health Concerns", type: "text" },
							{ name: "helpsAtHouse", description: "How do you help out around the house?", type: "select", values: ["Mop", "Wash Dishes", "Wash Clothes"] },
							{ name: "helpsAtDorm", description: "How do you help out around the dorm?", type: "select", values: ["Mop", "Wash Dishes", "Wash Clothes"] },
							{ name: "subjectLikedMost", description: "What do you like most about Heritage Academy?", type: "select", values: ["Science", "English", "Math"] },
							{ name: "likesPlaygroundActivity", description: "What do you like to do on the playground?", type: "select", values: ["Run", "Play Tag", "Basketball", "Soccer"] },
							{ name: "futureOccupation", description: "What would you like to be when you grow up?", type: "string" },
							{ name: "bestFriendName", description: "Who is your best friend?", type: "string" },
							{ name: "bestFriendActivity", description: "What do you like to do with your best friend?", type: "string" },
							{ name: "favoriteBibleVerse", description: "Favorite Bible verse or story?", type: "text" },
							{ name: "thankfulFor", description: "What are you thankful for?", type: "text" },
							{ name: "prayForMeFor", description: "How can we pray for you?", type: "text" },
							{ name: "infoFromChildsTeacher", description: "Info from child's teacher", type: "text" },
							{ name: "infoFromOthersAtHeritage", description: "Info from others at Heritage", type: "text" },
							{ name: "childEatsAtHome", description: "What does the child eat at home?", type: "text" },
							{ name: "otherInfo", description: "Anything else?", type: "text" },
							{ name: "attendanceRecordChecked", description: "Attendance record checked", type: "select", values: ["Yes", "No"] }
						]);
					}
					if (org.hasCustomization("people/types/showAdvancedAdultProfileFields")) {
						options = options.concat([
							{ name: "militaryServiceBranch", description: "Military Service Branch", type: "select", values: ["", "Army", "Navy", "Air Force", "Marines", "Coast Guard", "National Guard"] },
							{ name: "militaryServiceYears", description: "Military Service # Years", type: "string" },
							{ name: "maritalStatus", description: "Marital Status", type: "select", values: ["", "Single", "Married", "Widowed", "Divorced"] },
							{ name: "favoriteFood", description: "Favorite Food", type: "string" },
							{ name: "favoriteMusicGenre", description: "Favorite Music Genre", type: "select", values: ["", "Rock", "Country", "Classical", "Blues", "Hip Hop"] },
							{ name: "favoriteSongMusician", description: "Favorite Song/Musician", type: "string" },
							{ name: "previousOccupation", description: "Previous Occupation", type: "string" },
							{ name: "education", description: "Education", type: "text" },
							{ name: "familyChildren", description: "Family/Children", type: "text" },
							{ name: "interestingFacts", description: "Interesting Facts", type: "text" },
							{ name: "hobbies", description: "Hobbies", type: "text" }
						]);
					}

					if (org.hasCustomization("people/types/showPrimaryCaregiver"))
						options = options.concat([
							{ name: "primaryCaregiver", description: "Primary Caregiver", type: "query", source: "staffList" }
						]);

					if (org.hasCustomization("people/types/showPayer")) {
						options = options.concat([
							{ name: "payer", description: "Payer", type: "select", values: ["", "A&D Waiver", "Choice", "CICOA", "Private Pay", "Scholarship", "TBI Waiver", "VACCN", "Veteran"] }
						]);
					}
					if (org.hasCustomization("people/types/showEmergencyContact1"))
						options = options.concat([{ name: "emergencyContact1", description: "Emergency Contact 1", type: "string" }]);
					if (org.hasCustomization("people/types/showEmergencyContact2"))
						options = options.concat([{ name: "emergencyContact2", description: "Emergency Contact 2", type: "string" }]);
					if (org.hasCustomization("people/types/showHomeAddress"))
						options = options.concat([{ name: "homeAddress", description: "Home Address", type: "string" }]);
					if (org.hasCustomization("people/types/showMedicationFormPdf"))
						options = options.concat([{ name: "medicationFormPdf", description: "Medication Form", type: "file" }]);
					if (org.hasCustomization("people/types/showMedicationListPdf"))
						options = options.concat([{ name: "medicationListPdf", description: "Non-prescription Medication List", type: "file" }]);
					if (org.hasCustomization("people/types/showAllowPhotos"))
						options = options.concat([{ name: "allowPhotos", description: "Allow Photos?", type: "select", values: ["Yes", "No"] }]);

					// "outlook" field
					options = options.concat([
						{
							name: "standardOutlook",
							description: "Outlook Information",
							type: "fieldGroup",
							fields: [
								{
									name: "allergies",
									description: "Allergies",
									type: "text"
								},
								{
									name: "importantNotes",
									description: "Important Notes",
									type: "text",
									visibleOnlyToRoles: ["admin", "staff"]
								},
								{
									name: "specialNeeds",
									description: "Special Needs",
									type: "text",
									visibleOnlyToRoles: ["admin", "staff"]
								}
							]
						},
						{
							name: "mediaRequirements",
							description: "Media",
							type: "fieldGroup",
							fields: [
								{ name: "mediaReviewRequired", description: "Media Review Required", type: "select", values: ["Yes"] },
								{ name: "noMediaAllowed", description: "Media Forbidden", type: "select", values: ["Yes"] },
								{ name: "mediaRelease", description: "Media Release", type: "select", values: ["None", "Internal", "Internal and External"] }
							]
						}
					]);

					//if (Orgs.current().hasCustomization("people/pinCodeCheckin/enabled") && Meteor.user().fetchPerson().type=="admin")
					//	options.unshift({ name:"pinCode", description: "PIN Code", type:"string"});
					break;
				case "family":
					options = [
						{ name: "phonePrimary", description: "Primary Phone", type: "string" }
					];

					if (org.hasCustomization("people/types/showCriminalCheckExpiration"))
						options = options.concat([{ name: "criminalCheckExpiration", description: "Criminal Check Expiration", type: "string" }]);
					if (org.hasCustomization("people/types/showFobNumber"))
						options = options.concat([{ name: "fobNumber", description: "FOB Number", type: "string" }]);
					if (org.hasCustomization("people/types/showCarmelParentHomeSchool"))
						options = options.concat([{
							name: "parentHomeSchool", description: "Parent Home School", type: "select", values: [
								"Carmel Elementary", "Carmel High School", "Carmel Middle School", "Central Office", "Cherry Tree Elementary",
								"Clay Middle School", "College Wood Elementary", "Creekside Middle School", "Forest Dale Elementary",
								"Mohawk Trails Elementary", "Orchard Park Elementary", "Prairie Trace Elementary",
								"Smoky Row Elementary", "Towne Meadow Elementary", "Transportation", "West Clay Elementary", "Woodbrook Elementary"]
						}]);

					if (org.hasCustomization("people/pinCodeCheckin/enabled") && currentPerson?.type == "admin") {
						options.unshift({ name: "pinCodeSupplemental", description: "Supplemental PIN", type: "string" });
					}
					if (org.hasCustomization("people/pinCodeCheckin/enabled") && ['admin', 'family'].includes(currentPerson?.type)) {
						options.unshift({ name: "pinCode", description: "PIN Code", type: "string" });
					}

					const customFamilyFields = org.valueOverrides && org.valueOverrides.familyProfileFields;
					if (customFamilyFields) {
						if (customFamilyFields.filter(ff => ff.name == 'phonePrimary').length > 0)
							options = options.filter(ff => ff.name != 'phonePrimary');
						options = options.concat(org.valueOverrides.familyProfileFields || []);
					}
					break;
				case "staff":
				case "admin":
					options = [{ name: "phonePrimary", description: "Primary Phone", type: "string" }];
					if (org.hasCustomization("people/types/customerSpecificStaffProfileFields"))
						options = options.concat(org.valueOverrides ? org.valueOverrides.staffProfileFields || [] : []);
					if (org.hasCustomization("people/pinCodeCheckin/enabled") && currentPerson?.type == "admin") {
						options.unshift({ name: "pinCodeSupplemental", description: "Supplemental PIN", type: "string" });
						options.unshift({ name: "pinCode", description: "PIN Code", type: "string" });
					}
					const hasPayPerms = processPermissions({
						assertions: [{ context: "people/profile/pay", action: "edit" }],
						evaluator: (thisPerson) => thisPerson.type === "admin",
					});
					const payFields = [
						{
							name: 'payRate',
							description: 'Hourly Pay Rate',
							type: 'string',
							validation: 'currency'
						},
						{
							name: 'workDepartment',
							description: 'Work Department',
							type: 'string'
						},
						{
							name: "employeeClassification",
							description: "Employee Classification",
							type: "select",
							values: [
								"Exempt",
								"Non-Exempt"
							]
						}
					]
					for (const payField of payFields) {
						if (options.map(o => o.name).includes(payField.name)) {
							continue;
						}
						options.push(payField);
					}
					for (const option of options) {
						if (option.name === 'payRate') {
							option.validation = 'currency';
						}
					}
					const payFieldNames = payFields.map(f => f.name);
					// do the filtering here to catch custom profile fields as well
					if (!hasPayPerms) {
						options = options.filter(o => !payFieldNames.includes(o.name));
					}
					break;
				case 'vendor':
					options = [{ name: "pinCode", description: "PIN Code", type: "string" }]
					break;
				default:
					options = [];
			}
			const mapFieldPaths = function (fields, path) {
				fields = fields || [];
				const currentPath = path ? path + "." : "";
				fields.forEach(f => {
					if (f.type == "fieldGroup")
						return mapFieldPaths(f.fields, currentPath + f.name);
					else
						f.fieldPath = currentPath + f.name;
				});
				return fields;
			}
			return mapFieldPaths(options);
		}


	},
	//only used in client so no mongo async changes required
	filteredProfileFieldsForType(personType) {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser?.fetchPerson(),
			filterFields = function (fields) {
				const currentLevelFields = [];
				fields.forEach(f => {
					if (!f.visibleOnlyToRoles || _.include(f.visibleOnlyToRoles, currentPerson.type)) {
						if (f.type == "fieldGroup") {
							f.fields = filterFields(f.fields);
						}
						currentLevelFields.push(f);
					}
				});
				return currentLevelFields;
			};

		return filterFields(this.allowableProfileFieldsForType(personType));
	},
	async excludedProfileFieldsForType(personType, bypass = false, context = null) {
		let currentPerson;
		if (context) {
			currentPerson = context.person;
		} else {
			const meteorUser = await Meteor.userAsync();
			currentPerson = meteorUser && await meteorUser?.fetchPerson();
		}
		const currentOrg = await Orgs.current();
		const filteredFields = [],
			filterFields = function (fields, path) {
				fields.forEach(f => {
					if (f.type == "fieldGroup")
						filterFields(f.fields);
					else if (f.visibleOnlyToRoles && !_.include(f.visibleOnlyToRoles, currentPerson.type))
						filteredFields.push(f);
					else if (bypass)
						filteredFields.push(f);
				})
			};

		filterFields(await this.allowableProfileFieldsForType(personType, context, currentOrg));
		return filteredFields;
	},
	async getCollapsedProfileFieldList(personType, definedFieldsOnly, org) {
		const availableFieldValues = [];
		org = org ?? await Orgs.current();

		const collapseProfileFields = function (fields, prefix = "", prefixLabel = "") {
			_.each(fields, (f) => {
				if (f.type === "fieldGroup") {
					collapseProfileFields(f.fields, prefix + f.name + ".", prefixLabel + f.description + " / ");
				}
				else {
					f.prefixedName = prefix + f.name;
					f.prefixedLabel = prefixLabel + f.description;
					availableFieldValues.push(f);
				}
			});
		}
		collapseProfileFields(await this.allowableProfileFieldsForType(personType, null, org));

		if (!definedFieldsOnly) {
			if (['staff', 'admin', 'family'].includes(personType)) {
				availableFieldValues.push({ name: "attachedEmail", description: "Email Address", prefixedName: "attachedEmail", prefixedLabel: "Email Address" });
				availableFieldValues.push({ name: "pinCode", description: "PIN Code", prefixedName: "pinCode", prefixedLabel: "PIN Code" });
				availableFieldValues.push({ name: "pinCodeSupplemental", description: "Supplemental PIN", prefixedName: "pinCodeSupplemental", prefixedLabel: "Supplemental Pin" });
			}
			if (personType != "family") availableFieldValues.push({ name: "defaultGroupName", description: "Default Group Name", prefixedName: "defaultGroupName", prefixedLabel: "Default Group Name" });
			if (personType == "person") availableFieldValues.push({ name: "calcAge", description: "Age (calculated)", prefixedName: "calcAge", prefixedLabel: "Age (calculated)" });
			if (personType == "person") availableFieldValues.push({ name: "waitlistAddedDate", description: "Wait List Added Date", prefixedName: "waitlistAddedDate", prefixedLabel: "Wait List Added Date" });
			if (org.hasCustomization("people/requireRoles") && personType != "family" && personType != "person") availableFieldValues.push({ name: "Roles", description: "Roles", prefixedName: "Roles", prefixedLabel: "Roles" });
		}
		return availableFieldValues;
	},
	getProfileFieldWithNameForType(personType, fieldName, org = null) {
		if (Meteor.isServer) {
            return (async () => {
				org = org ?? await Orgs.current();
				if (!org) {
					return null;
				}

				/**
				 * Search fields for the specified field.
				 * Return the field or null.
				 *
				 * @param fields
				 * @returns {*|null}
				 */
				const searchFields = (fields) => {
					for (const field of fields) {
						if (field.name === fieldName) {
							return field;
						} else if (field.type === 'fieldGroup' && field.fields?.length) {
							const matchedField = searchFields(field.fields);
							if (matchedField) {
								return matchedField;
							}
						}
					}
					return null;
				};

				const profileFields = await People.allowableProfileFieldsForType(personType, null, org);
				return searchFields(profileFields);
			})();
		} else {
			org = org ?? Orgs.current();
			if (!org) {
				return null;
			}

			/**
			 * Search fields for the specified field.
			 * Return the field or null.
			 *
			 * @param fields
			 * @returns {*|null}
			 */
			const searchFields = (fields) => {
				for (const field of fields) {
					if (field.name === fieldName) {
						return field;
					} else if (field.type === 'fieldGroup' && field.fields?.length) {
						const matchedField = searchFields(field.fields);
						if (matchedField) {
							return matchedField;
						}
					}
				}
				return null;
			};

			const profileFields = People.allowableProfileFieldsForType(personType, null, org);
			return searchFields(profileFields);
		}
	}
});

if (Meteor.isServer && !Meteor.isTest) {
	const { RasService } = require('../../server/rightAtSchoolService');
	const { RightAtSchoolService } = require('../../server/rightAtSchoolService');
	const { scheduleGroupDashboardRecalculation } = require('../../server/agenda/agendaScheduler');
	People.after.insert(async function (userId, doc) {
		try {
			await theCheckinStatsCount();
		} catch (e) {
			console.log('checkin stats error', e);
		}
		addPersonToOngoingDataValidation(doc);
		Meteor.callAsync('setAutoPin', doc._id).then(async (res) => {
			if (!res) {
				await Meteor.callAsync('updateZkTecoPerson', doc._id);
			}
		});
		if (doc.type === 'person') {
			RasService.populateData(doc._id, RasWebhookActions.CHILD_CREATED);
		}
	});

	People.after.update(async function (userId, doc, fieldNames, modifier, options) {
		try {
			await theCheckinStatsCount();
		} catch (e) {
			console.log('checkin stats error', e);
		}
		const previousGroupId = this?.previous?.checkInGroupId;
		const currentGroupId = doc?.checkInGroupId || doc?.defaultGroupId;
		if(Meteor.isServer) {
			Meteor.defer(async function () {
				const { scheduleGroupDashboardRecalculation } = await import("../../server/agenda/agendaScheduler");
				scheduleGroupDashboardRecalculation(currentGroupId)
				if (previousGroupId && previousGroupId != currentGroupId) scheduleGroupDashboardRecalculation(previousGroupId)
			})
		}

		Meteor.defer(async function () {
			if (doc.childcareCrm) {
				await Meteor.callAsync('crmFamilyUpdate', doc);
			}

			if (doc.type === 'person') {
				RasService.populateData(doc._id, RasWebhookActions.CHILD_UPDATED);
			}
		});
		updateCacheOnPersonChange(fieldNames, doc._id);
	}, { fetchPrevious: true });

	People.before.remove(async function (userId, doc) {
		try {
			await theCheckinStatsCount();
		} catch (e) {
			console.log('checkin stats error', e);
		}
		if (doc.type === 'person') {
			RasService.populateData(doc._id, RasWebhookActions.CHILD_DELETED);
		}
	});

}

/**
 * @typedef {Object} FamilySearchResult
 * @property {string}                   org_id
 * @property {string | null}            [external_family_id]
 * @property {string | null}            [external_center_id]
 * @property {FamilySearchResultRecord} record - The record that matched the search criteria.
 * @property {Family}                   family
 */
/**
 * @typedef {Object} FamilySearchResultRecord
 * @property {string}              id
 * @property {string}              first_name
 * @property {string}              last_name
 * @property {("person"|"family")} type
 */
/**
 * @typedef {Object} Family
 * @property {Contact[] | any[]} contacts
 * @property {Child[] | any[]}   children
 *
 */
/**
 * Contact or Guardian is of this type.
 *
 * @typedef {Object} Contact
 * @property {string}                   id
 * @property {string}                   first_name
 * @property {string}                   last_name
 * @property {string}                   email
 * @property {string}                   phone
 * @property {string}                   address
 * @property {string}                   city
 * @property {string}                   state
 * @property {string}                   postcode
 * @property {string | null}            [external_guardian_id]
 * @property {boolean}                  is_authorize_pickup
 * @property {boolean}                  is_emergency
 * @property {boolean}                  is_primary
 * @property {SimpleRelation[] | any[]} relations
 */
/**
 * @typedef {Object} Child
 * @property {string}        id
 * @property {string}        first_name
 * @property {string}        last_name
 * @property {string}        dob
 * @property {string | null} classroom_id
 * @property {string | null} external_child_id
 */
/**
 * @typedef {Object} SimpleRelation
 * @property {string}        child_id
 * @property {string}        relation_type
 * @property {string | null} relation_description
 */

/**
 * @type {Family | Object}
 */
export const PersonFamilyObject = {};
