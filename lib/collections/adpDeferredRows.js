import _ from '../util/underscore';
import { Mongo } from 'meteor/mongo';
/**
 * Structure of AdpDeferredRows:
 * {
 *     rootOrgId: string (the org adp runs out of),
 *     originalFileName: string
 *     effectiveDate: string
 *     originalRow: original data object
 * }
 *
 * @param doc
 * @constructor
 */
const AdpDeferredRow = function(doc) {
	_.extend(this, doc)
};

export const AdpDeferredRows = new Mongo.Collection('adpDeferredRows', {
	transform: function(doc) {
		return new AdpDeferredRow(doc);
	}
});
