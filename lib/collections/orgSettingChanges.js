import { Mongo } from 'meteor/mongo';
import { PLAN_BUNDLE_TYPE } from '../constants/billingConstants';
import _ from '../../lib/util/underscore';
import { Orgs } from './orgs';

export const OrgSettingChange = function(doc) {
	_.extend(this, doc)
};

_.extend(OrgSettingChange.prototype, {
	dataTypeNameAndDescription: function() {
    const org = Orgs.findOne({ _id: this.orgId });
    switch (this.dataType) {
      case 'billing.plansAndItems':
        const matchingItem = _.find(org?.billing?.plansAndItems || [], (i) => i._id == this.value._id);
        let description = `${matchingItem?.description} with amount of ${matchingItem?.amount}`;
        if (matchingItem?.type === PLAN_BUNDLE_TYPE) {
            const plans = _.filter(org?.billing?.plansAndItems || [], i => (matchingItem?.plans ?? []).includes(i._id));
            description = 'Bundled ' + plans.map(i => i.description).join(' and ');
        }
        return {
          name: `Billing ${matchingItem?.type}`,
          description: description
        }

      default:
        return {
          name: "Not Defined",
          description: ""
        }
    }
  },
});

export const OrgSettingChanges = new Mongo.Collection('orgSettingChanges', {
	transform: function(doc) {
		return new OrgSettingChange(doc);
	}
});
