/*
{
  _id: <String>
  sourceData: <Object> source data of activity
  updateData: <Object> data for approval
  createdBy: <Object> personId, fName, lName
  approvedBy: <Object> personId, fName, lName
  modifiedBy: <Object> personId, fName, lName
  updateRequestBy:  <Object> personId, fName, lName
  published: bool,
  waitingForApproval: DatTime - set and unset this for workflow purposes
}

*/


import { Orgs } from './orgs';
import _ from '../util/underscore';
import { Mongo } from 'meteor/mongo';

const CurriculumBank = function(doc) {
	_.extend(this, doc)
};

_.extend(CurriculumBank.prototype, {
  sourceLabel() {
    const org = Orgs.current();
    if (org?.hasCustomization("curriculumBank/globalAndLocal")) {
      return (this.bankId == org._id) ? "Local" : "Global";			
    }
  }
});

export const CurriculumBanks = new Mongo.Collection('curriculumBank', {
	transform: function(doc) {
		return new CurriculumBank(doc);
	}
});
