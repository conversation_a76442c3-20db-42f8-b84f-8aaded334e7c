import { Cache } from "./util/cacheUtils";
import { CACHE_KEYS } from "./constants/cacheConstants";
export function updateCacheOnPersonChange(fieldNames, userId) {
	if (fieldNames.includes("type") || fieldNames.includes("roles") || fieldNames.includes("superAdmin")) {
		Cache.delete(`${userId}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`);
	}
}

export function updateCacheOnOrgChange(fieldNames, orgId) {
	if (fieldNames.includes("customizations")) {
		[
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOWTRANSPORTATION}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETPERSON}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_INVOICES}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETGROUP}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TGROUP}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_QR_CODE_CHECK_IN_ENABLED}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOW_STAFF_CERTIFICATION_MESSAGE}`,
			`${orgId}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRE_COMPANION_SELECTION}`
		].map(key => Cache.delete(key))
	}
}