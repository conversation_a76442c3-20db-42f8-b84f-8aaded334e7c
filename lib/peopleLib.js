import { AvailableCustomizations } from './customizations';
import { CardProvidersShared } from '../imports/card_providers_shared/cardProvidersShared';
import { PaymentSources } from './constants/billingConstants';
import _ from './util/underscore';
const moment = require("moment-timezone");

/**
 * Library for People collection functions
 */
export class PeopleLib {

    /**
     * Determines whether a specific payment action (replace or remove) is allowed for a person
     * based on the organization's customizations and the person's connected payment methods.
     *
     * @param {Object} person - The person object with payment methods.
     * @param {Object} org - The organization object with customizations.
     * @param {string} action - The action to perform, e.g., 'replace' or 'remove'.
     * @param {string} method - The payment method type, e.g., 'bank_account' or 'card'.
     * @returns {boolean} True if the action is allowed for the specified method, otherwise false.
     */
    static availablePayMethodAction(person, org, action, method) {
        const requireBankAccount = org.hasCustomization(AvailableCustomizations.QUEUE_AUTO_PAYMENTS);
        const requirePaymentMethod = org.hasCustomization(AvailableCustomizations.REQUIRE_PAYMENT_METHOD);
 
        const noConnectedBankAccount = !person.connectedBankAccount();
        const noConnectedCreditCard = !person.connectedCreditCard();
 
        // Determine bank account action
        const bankAccountAction =
            requireBankAccount || (requirePaymentMethod && (noConnectedBankAccount || noConnectedCreditCard))
                ? 'replace'
                : 'remove';
 
        // Determine card action
        const cardAction =
            requirePaymentMethod && (noConnectedBankAccount || noConnectedCreditCard) ? 'replace' : 'remove';
 
        // Compare action and method to determine the result
        const isBankAccountAction = method === PaymentSources.BANK_ACCOUNT && action === bankAccountAction;
        const isCardAction = method === PaymentSources.CARD && action === cardAction;
 
        return isBankAccountAction || isCardAction;
    }

    /**
     * Retrieves the connected bank account for a person from their payment sources.
     * If the bank account has no 'last4' information, it defaults to "On file".
     *
     * @param {Object} person - The person object containing billing information.
     * @param {Object} org - The organization object containing billing settings.
     * @returns {Object|undefined} The connected bank account object, or undefined if none exists.
     */
    static connectedBankAccount(person, org) {
        const paymentSources = this.getPaymentSourcesOnFileForPerson(person, org);
        const bankAccount = paymentSources.find(paymentSource => paymentSource.object === PaymentSources.BANK_ACCOUNT);

        if (bankAccount && !bankAccount.last4) {
            bankAccount.last4 = "On file";
        }

        return bankAccount;
    }

    /**
     * Retrieves the connected credit card for a person from their payment sources.
     *
     * @param {Object} person - The person object containing billing information.
     * @param {Object} org - The organization object containing billing settings.
     * @returns {Object|undefined} The connected credit card object, or undefined if none exists.
     */
    static connectedCreditCard(person, org) {
        const paymentSources = this.getPaymentSourcesOnFileForPerson(person, org);
        return paymentSources.find(paymentSource => paymentSource.object === PaymentSources.CARD);
    }

    /**
     * Retrieves the AutoPay method (e.g., 'bank_account', 'card') for a person.
     *
     * @param {Object} person - The person object containing billing information.
     * @returns {string|undefined} The AutoPay method, or undefined if not set.
     */
    static autoPayMethod(person) {
        return _.deep(person, "billing.autoPay");
    }

    /**
     * Fetches all payment sources on file for a person based on the billing provider.
     * Payment sources can include bank accounts or credit cards.
     *
     * @param {Object} person - The person object containing billing information.
     * @param {Object} org - The organization object containing billing settings.
     * @returns {Array} An array of payment source objects, or an empty array if none exist.
     */
    static getPaymentSourcesOnFileForPerson(person, org) {
        const paymentProviderName = org && org.billingCardProviderName();
        const billingProvider = paymentProviderName && CardProvidersShared.get(paymentProviderName, org);
        return billingProvider && billingProvider.mapPaymentSources(person) || [];
    }

    /**
     * 
     * @param {Object} openInvoices - open invoices
     * @returns {} - billing info of person
     */
    static billingStatus(person, openInvoices){
        
		const startOfToday = new moment().startOf("day").valueOf();
		let openAmountSum = 0;
        let pastDueAmountSum = 0; 
        let earliestDueDate; 
        let dueStatus = ""; 
        let amountsByDueDate = {}; 
        let autoPayAmount = 0;
        let autoPayAmountFuture = 0;
        let earliestDueDateFuture;

		_.each(openInvoices, (i) => {
			openAmountSum += i.amountDueForFamilyMember(person._id);
			if (!earliestDueDate || i.dueDate < earliestDueDate) earliestDueDate = i.dueDate;
            if (i.dueDate >= startOfToday && (!earliestDueDateFuture || i.dueDate < earliestDueDateFuture)) earliestDueDateFuture = i.dueDate;

			if (i.dueDate < startOfToday) pastDueAmountSum += i.amountDueForFamilyMember(person._id);
            amountsByDueDate[i.dueDate.toString()] = (amountsByDueDate[i.dueDate.toString()] || 0 ) + i.amountDueForFamilyMember(person._id);
		});
		if (earliestDueDate < new moment().startOf("day").valueOf())
			dueStatus = "PAST DUE";
		else if (earliestDueDate < new moment().add(1, "days").startOf("day").valueOf())
			dueStatus = "Due Today";
		else
			dueStatus = "Due " + new moment(earliestDueDate).format("M/DD/YYYY");

        const hasAutoPay = person.autoPayMethod() ? true : false;
        if (hasAutoPay) {
            if (earliestDueDate) {
                autoPayAmount = amountsByDueDate[earliestDueDate.toString()] || 0;
                if (person.billing.autoPayAmount && autoPayAmount > person.billing.autoPayAmount) {
                    autoPayAmount = person.billing.autoPayAmount;
                }
            }
            if (earliestDueDateFuture) {
                autoPayAmountFuture = amountsByDueDate[earliestDueDateFuture.toString()] || 0;
                if (person.billing.autoPayAmount && autoPayAmountFuture > person.billing.autoPayAmount) {
                    autoPayAmountFuture = person.billing.autoPayAmount;
                }
            }
        }
		return {
            hasAmountDue: openAmountSum > 0,
            currentAmountDue: openAmountSum,
            dueStatus: dueStatus,
            showPastDueAmount: (openAmountSum > pastDueAmountSum && pastDueAmountSum > 0),
            pastDueAmount: pastDueAmountSum,
            autoPayAmount: autoPayAmount,
            hasAutoPay: hasAutoPay,
            autoPaymentDate: earliestDueDate, 
            autoPayAmountFuture: autoPayAmountFuture,
            autoPaymentDateFuture: earliestDueDateFuture  
		};
    }

}