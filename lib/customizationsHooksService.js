import { AvailableCustomizations } from "./customizations";
import { AbleToWorkAtService } from "./ableToWorkAtService";

export class CustomizationsHooksService {
    /**
     * Place to add hooks for enabled customizations
     * @param org
     * @param customization
     */
    static async handleCustomizationAdded(org, customization) {
        switch (customization) {
            case AvailableCustomizations.STAFF_WORK_LOCATIONS:
                await AbleToWorkAtService.setPrimarySitesForOrg(org._id);
                break;
        }
    }

    /**
     * Place to add hooks for disabled customizations
     * @param org
     * @param customization
     */
    static handleCustomizationRemoved(org, customization) {
        switch (customization) {
        }
        // nothing to do here yet
    }

    /**
     * For use in orgs after update hook
     * @param orgNew
     * @param orgPrevious
     */
    static async handleCustomizationChanges(orgNew, orgPrevious) {
        const changes = this.getCustomizationsChanges(orgNew, orgPrevious);
        console.log({ id: orgNew._id, changes })
        for (const added of changes.added) {
            await this.handleCustomizationAdded(orgNew, added);
        }
        for (const removed of changes.removed) {
            this.handleCustomizationRemoved(orgNew, removed);
        }
    }

    /**
     * helper to get the added and removed customizations
     * @param orgNew
     * @param orgPrevious
     * @return {{removed: string[], added: string[]}}
     */
    static getCustomizationsChanges(orgNew, orgPrevious) {
        const currentCustomizations = this.getCustomizationsArray(orgNew);
        const previousCustomizations = this.getCustomizationsArray(orgPrevious);
        const added = currentCustomizations.filter(x => !previousCustomizations.includes(x));
        const removed = previousCustomizations.filter(x => !currentCustomizations.includes(x));
        return { added, removed };
    }

    /**
     * helper to get an array of enabled customizations
     * @param org
     * @return {[string]}
     */
    static getCustomizationsArray(org) {
        const customizations = [];
        for (const [customization, enabled] of Object.entries(org.customizations || [])) {
            if (enabled) {
                customizations.push(customization);
            }
        }
        return customizations;
    }
}