var generator = require('generate-password');
/**
     * Use below parameters in case wants changes in password format.
     *
     * @param {Integer} length - Default Value 10.
     * @param {Boolean} numbers - Default Value false.
     * @param {Boolean} symbols - Default Value false.
     * @param {Boolean} lowercase - Default Value true.
     * @param {Boolean} uppercase - Default Value true.
     * @param {Boolean} strict - Default Value true.
     * @param {Boolean} excludeSimilarCharacters -Default Value false
     * @param {String} exclude -Default Value ''
     * @returns {string}
     */
export function get16digitPassword() {
    const password = generator.generate({
        length: 16,
        numbers: true,
        symbols: true,
        exclude: "~,;:.{}<>[]()/\'`\""
    });
    return password;
}