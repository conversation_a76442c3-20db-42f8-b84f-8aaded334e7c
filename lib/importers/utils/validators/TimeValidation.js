import moment from 'moment';
import { RequiredFieldHelper } from './RequiredFieldHelper';
export class TimeValidation {
    constructor({ headerName = 'Time', required = false, format = 'hh:mm A' } = {}) {
        this.headerName = headerName;
        this.required = required;
        this.format = format;
    }

    validate(value) {
        const requiredCheck = RequiredFieldHelper.checkRequired(value, {
            headerName: this.headerName,
            required: this.required,
        });

        if (requiredCheck !== null) {
            return requiredCheck;
        }

        const isValid = moment(value.trim(), this.format, true).isValid();
        return isValid || `${this.headerName} must be in ${this.format} format.`;
    }
}