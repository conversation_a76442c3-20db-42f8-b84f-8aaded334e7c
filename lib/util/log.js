import { PublicConfigurationConstants, PublicConfigurationSettingsService } from '../config/publicConfigurationSettingsService';

/**
 * A utility class for logging messages to the console.
 */
export class Log {
    /**
     * Log a message to the console if debug mode is enabled.
     *
     * @param message
     */
    static debug(...message) {
        try {
            if (!PublicConfigurationSettingsService.getPublicConfigurationSetting(PublicConfigurationConstants.DEBUG_MODE)) {
                return;
            }
            Log.info(...message);
        } catch (error) {
            console.log(...message);
        }
    }

    /**
     * Log an error message to the console.
     *
     * @param message
     */
    static error(...message) {
        try {
            const formattedMessageParts = LogMessageBuilder.buildFormattedMessageParts(...message);
            console.error(...formattedMessageParts);
            console.trace();
        } catch (error) {
            console.log(...message);
        }
    }

    /**
     * Log a message to the console.
     *
     * @param message
     */
    static info(...message) {
        try {
            const formattedMessageParts = LogMessageBuilder.buildFormattedMessageParts(...message);
            console.log(...formattedMessageParts);
        } catch (error) {
            console.log(...message);
        }
    }
}

/**
 * A utility class for building log messages.
 */
class LogMessageBuilder {
    /**
     * Build the formatted message parts.
     *
     * @param message
     * @returns {*[]}
     */
    static buildFormattedMessageParts(...message) {
        let messageParts = [];
        const reference = LogMessageBuilder.getCallDetails();
        if (reference.file && reference.line) {
            messageParts.push(`(${ reference.file }:${ reference.line })`);
        }
        for (const part of message) {
            if (typeof part === 'object' || typeof part === 'array') {
                messageParts.push(JSON.stringify(part, null, 4));
            } else {
                messageParts.push(part);
            }
        }

        return messageParts;
    }

    /**
     * Get details about the file that called the logger.
     *
     * @returns {{
     *     file: string | undefined,
     *     line: string | undefined
     * }}
     */
    static getCallDetails() {
        const getStack = () => {
            const err = new Error;
            return err.stack;
        };

        const stack = getStack();
        if (!stack) {
            return {};
        }

        // Get the first line of the stack trace not including log.js
        let line;
        const lines = stack.split('\n').slice(1);
        for (line of lines) {
            if (!line.includes('lib/util/log.js')) {
                break;
            }
        }

        const details = {};

        // The format for FF is 'functionName@filePath:lineNumber'
        // The format for V8 is 'functionName (lib/util/log.js:81)' or
        //                      'lib/util/log.js:81'
        const match = /(?:[@(]| at )([^(]+?):([0-9:]+)(?:\)|$)/.exec(line);
        if (!match) {
            return details;
        }

        // in case the matched block here is line:column
        details.line = match[2].split(':')[0];
        details.file = match[1].split('/').slice(-1)[0].split('?')[0];

        return details;
    };
}
