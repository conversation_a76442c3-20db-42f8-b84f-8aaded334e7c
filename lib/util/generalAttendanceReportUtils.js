import moment from "moment-timezone";
import {Log} from '../../lib/util/log';
import { Orgs } from "../collections/orgs";

export class GeneralAttendanceReportUtils {

    static filterGroupedMoments(groupedMoments, orgIds, subsidyFilterValues=[]) {
        return new Promise((resolve, reject) => {
            Meteor.callAsync('filterOnSubsidy', orgIds).then( async (result) => {
                const availablePayerSources = ( await Orgs.current())?.availablePayerSources(true) || [];

                // Create a lookup object for reimbursement types and their descriptions
                const reimbursementTypeLookup = Object.fromEntries(
                    availablePayerSources.map(({ type, description }) => [type, description])
                );

                const allSelected = subsidyFilterValues.length === availablePayerSources.length;
                const finalResult = await this.getFilteredGroupedMomentsWithReimbursementTypes(
                    groupedMoments,
                    subsidyFilterValues,
                    reimbursementTypeLookup,
                    allSelected,
                    result
                );

                resolve(finalResult);
            }).catch((error) => {
                Log.error(error);
                reject(error);
            });
        });
    }

    static async getFilteredGroupedMomentsWithReimbursementTypes(groupedMoments, filterValues, reimbursementTypeLookup, allSelected, people) {
        const results = await Promise.all(groupedMoments.map(async (item) => {
            // Find matching person with reimbursement types
            const user = people.find(person => person._id === item.owner);
            const reimbursementTypes = [];

            // Check if the user is found
            if (user && user.billing && user.billing.enrolledPlans) {
                // Initialize an array to store reimbursement types

                for (const plan of user.billing.enrolledPlans) {
                    if (plan.allocations) {
                        for (const allocation of plan.allocations) {
                            // Skip if allocationType is not "reimbursable" or "reimbursable-with-copay"
                            if (!["reimbursable", "reimbursable-with-copay"].includes(allocation.allocationType)) {
                                continue;
                            }

                            // Check if the date is within the specified range
                            const org = await Orgs.findOneAsync(user.orgId);
                            const timezone = org?.getTimezone() || 'America/New_York';
                            const momentDate = moment.tz(item.date, 'MM/DD/YYYY', timezone).valueOf();
                            const isDateInRange = (() => {
                                if (allocation.payerStartDate && allocation.payerEndDate) {
                                    return momentDate >= allocation.payerStartDate &&
                                        momentDate <= allocation.payerEndDate &&
                                        isWithinEnrollmentAndExpirationDates();
                                } else if (allocation.payerStartDate) {
                                    return momentDate >= allocation.payerStartDate &&
                                        isWithinEnrollmentAndExpirationDates();
                                } else if (allocation.payerEndDate) {
                                    return momentDate <= allocation.payerEndDate &&
                                        isWithinEnrollmentAndExpirationDates();
                                } else {
                                    return isWithinEnrollmentAndExpirationDates();
                                }
                            })();

                            function isWithinEnrollmentAndExpirationDates() {
                                return momentDate >= plan.enrollmentDate &&
                                    (!plan.expirationDate || momentDate <= plan.expirationDate);
                            }

                            // If the date is in range, add the reimbursement type to the array
                            if (isDateInRange) {
                                reimbursementTypes.push(reimbursementTypeLookup[allocation.reimbursementType] || allocation.reimbursementType);
                            }
                        }
                    }
                }
            }

            return { ...item, reimbursementTypes: [...new Set(reimbursementTypes)] };
        }));

        return results.filter(item => {
            const user = people.find(person => person._id === item.owner);
            if (!user) return false;

            // Revert reimbursementTypes to original codes for filter check
            const originalCodes = item.reimbursementTypes.map(type => {
                for (const key in reimbursementTypeLookup) {
                    if (reimbursementTypeLookup[key] === type) {
                        return key;
                    }
                }
                return type; // Return original type if not found in mapping
            });

            if (allSelected) {
                return true; // Keep all items if all reimbursement types are selected
            }

            if (filterValues.length === 0) {
                return originalCodes.length === 0; // Keep items with no matching type
            }

            // Assign the result of filter operation back to originalCodes
            const filteredCodes = originalCodes.filter(code => filterValues.includes(code));

            return filteredCodes.length > 0; // Keep items with at least one matching type
        });
    }
}