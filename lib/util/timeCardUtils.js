import moment from 'moment';
export class TimeCardUtils {
    /**
     * @param timecard
     * @return {number}
     */
    static getTimeCardTotalHours(timecard) {
        return TimeCardUtils.getTotalHours(timecard.checkInDate, timecard.checkInTime, timecard.checkOutDate, timecard.checkOutTime);
    }

    static isTimeCardAutoCheckout(timecard) {
        return !this.isTimeCardEdited(timecard) && timecard.originalCheckOutMoment?.createdBy === "SYSTEM";
    }

    static isTimeCardEdited(timecard) {
        const updates = timecard.updateLog || [];
        return updates.filter(tc => tc.modifiedDoc === 'TimeCard').length > 0;
    }

    /**
     *
     * @param startDate
     * @param startTime
     * @param endDate
     * @param endTime
     * @return {number}
     */
    static getTotalHours(startDate, startTime, endDate, endTime) {
        const start = moment(startDate + ' ' + startTime, "M/D/YYYY h:mm a");
        const end = moment(endDate + ' ' + endTime, "M/D/YYYY h:mm a");
        return end.diff(start, 'hours', true) || 0;
    }

    /**
     * @param timeCard
     * @param otherTimeCards
     * @return {{start: moment, end: moment} | false}
     */
    static getConflictRange(timeCard, otherTimeCards) {
        const allRanges = otherTimeCards.map(tc => {
            if (!tc.checkOutDate || !tc.checkOutTime) {
                tc.checkOutDate = tc.checkInDate;
                tc.checkOutTime = '11:59 PM';
                tc.noCheckOut = true;
            }
            return {
                start: moment(tc.checkInDate + ' ' + tc.checkInTime, "M/D/YYYY h:mm a"),
                end: moment(tc.checkOutDate + ' ' + tc.checkOutTime, "M/D/YYYY h:mm a"),
                noCheckOut: tc.noCheckOut
            };
        });
        const newRange = {
            start: moment(timeCard.checkInDate + ' ' + timeCard.checkInTime, "M/D/YYYY h:mm a"),
            end: moment(timeCard.checkOutDate + ' ' + timeCard.checkOutTime, "M/D/YYYY h:mm a")
        };
        for (const range of allRanges) {
            if (newRange.start.isSame(range.start) || newRange.start.isBetween(range.start, range.end) || newRange.end.isBetween(range.start, range.end)
                || range.start.isBetween(newRange.start, newRange.end) || range.end.isBetween(newRange.start, newRange.end)
            ) {
                return range;
            }
        }
        return false;
    }
}