import { Orgs } from "../collections/orgs";
import { REGISTRATION_FEE } from "../constants/billingConstants";

export class RegFeeConfigUtil {
    static getDefaultConfig(billingObject) {
        const items = billingObject?.plansAndItems || [];
        const regFee = items.find(item => item.description === REGISTRATION_FEE);
        return {
            enabled: !!regFee,
            feeId: regFee?._id,
            perChild: false,
            enableMaxPerFamily: false,
            maxPerFamily: 1
        }
    }

    static async setDefaultConfigs() {
        const orgs = await Orgs.find({ 'billing.plansAndItems': { $exists: true }}).fetchAsync();
        for (const org of orgs) {
            const billing = org.billing;
            if (billing && !billing.regFeeConfig) {
                const config = this.getDefaultConfig(billing);
                await Orgs.updateAsync(org._id, { $set: { "billing.regFeeConfig": config } });
            }
        }
    }
}