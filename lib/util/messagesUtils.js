import { Log } from './log';
import { People } from '../collections/people';
import { Messages } from '../collections/messages';
import _ from './underscore';

/**
 * Utils for messages that can be used on the client or server.
 */
export class MessagesUtils {
    /**
     * Whether a given person can view a message.
     *
     * @param person
     * @param messageId
     * @param org
     * @returns {boolean}
     */
    static async canPersonViewMessage(person, messageId, org) {
        const message = await Messages.findOneAsync({ _id: messageId });
        if (!message) {
            return false;
        }
        const allRecipientIds = (message?.currentRecipients ?? []);
        allRecipientIds.push(message?.personId);
        return allRecipientIds.includes(person._id) ||
            (org.hasCustomization('messages/administrativeVisibility/enabled') && person.type === 'admin');
    }

    /**
     * Returns the query to use to search for messages.
     *
     * @param query
     * @param searchTerm
     * @param personId
     * @param queryOptions
     * @returns {[*]}
     */
    static getMessageQueryWithSearchTerm(query, searchTerm, personId, queryOptions) {
        const regexPattern = new RegExp(searchTerm, 'i');
        const aggregateQuery = [
            {
                $match: query
            },
            {
                $lookup: {
                    from: "people",
                    localField: "personId",
                    foreignField: "_id",
                    as: "threadFrom"
                }
            },
            {
                $unwind: {
                    path: "$threadFrom",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "people",
                    localField: "currentRecipients",
                    foreignField: "_id",
                    as: "recipientData"
                }
            },
            {
                $project: {
                    searchableTextArray: {
                        $map: {
                            input: "$recipientData",
                            as: "recipient",
                            in: {
                                $concat: [
                                    { $ifNull: ["$$recipient.firstName", ""] },
                                    " ",
                                    { $ifNull: ["$$recipient.lastName", ""] },
                                    " "
                                ]
                            }
                        }
                    },
                    lastActivity: 1,
                    subject: 1,
                    message: 1,
                    threadFromName: {
                        $concat: [
                            { $ifNull: ["$threadFrom.firstName", ""] },
                            " ",
                            { $ifNull: ["$threadFrom.lastName", ""] }
                        ]
                    }
                }
            },
            {
                $match: {
                    $or: [
                        {
                            searchableTextArray: {
                                $regex: regexPattern
                            }
                        },
                        {
                            threadFromName: {
                                $regex: regexPattern
                            }
                        },
                        {
                            subject: {
                                $regex: regexPattern
                            }
                        },
                        {
                            message: {
                                $regex: regexPattern
                            }
                        }
                    ]
                }
            }
        ];
        if (queryOptions?.sort) {
            aggregateQuery.push({ $sort: queryOptions.sort });
        }
        if (queryOptions?.skip) {
            aggregateQuery.push({ $skip: queryOptions.skip });
        }
        if (queryOptions?.limit) {
            aggregateQuery.push({ $limit: queryOptions.limit });
        }
        return aggregateQuery;
    }

    /**
     * Get the searchable text of the message(s).
     *
     * @returns {string}
     */
    static async getSearchableText(message, personId) {
        const threadFrom = await People.findOneAsync(message.personId);
        let result = message.subject;
        if (threadFrom) {
            result = result + threadFrom.firstName + " " + threadFrom.lastName + " " + await MessagesUtils.threadRecipients(message, personId, true) + " " +
                message.message + (_.pluck(message.replies, "message")).join(" ");
        } else {
            result = result + await MessagesUtils.threadRecipients(message, personId, true) + " " + message.message + (_.pluck(message.replies, "message")).join(" ");
        }

        return result;
    }

        /**
     * Get the searchable text of the message(s).
     *
     * @returns {string}
     */
        static getSearchableTextForClient(message, personId) {
            const threadFrom = People.findOne(message.personId);
            let result = message.subject;
            if (threadFrom) {
                result = result + threadFrom.firstName + " " + threadFrom.lastName + " " + MessagesUtils.threadRecipientsForClient(message, personId, true) + " " +
                    message.message + (_.pluck(message.replies, "message")).join(" ");
            } else {
                result = result + MessagesUtils.threadRecipientsForClient(message, personId, true) + " " + message.message + (_.pluck(message.replies, "message")).join(" ");
            }
    
            return result;
        }

    /**
     * Returns the items in the thread.
     *
     * @param message
     * @param personId
     * @returns {*[]}
     */
    static async threadItems(message, personId) {
        let items = [];

        const senderPerson = await People.findOneAsync(message.personId);
        if (senderPerson) {
            items.push({
                senderName: (message.personId == personId) ? "You" : senderPerson.firstName + " " + senderPerson.lastName,
                createdAt: message.createdAt,
                message: message.message
            });
        }

        const mappedReplies = [];
        
        if(message.replies && message.replies.length > 0) {
            for (const r of message.replies) {
                const senderPerson = await People.findOneAsync(r.personId);
                if (senderPerson) {
                    mappedReplies.push({
                        senderName: (r.personId == personId) ? "You" : senderPerson.firstName + " " + senderPerson.lastName,
                        createdAt: r.createdAt,
                        message: r.message
                    });
                }
            }
        }
        items = items.concat(mappedReplies);

        return items;
    }

    /**
     * Returns the items in the thread.
     *
     * @param message
     * @param personId
     * @returns {*[]}
     */
    static threadItemsForClient(message, personId) {
        let items = [];

        const senderPerson = People.findOne(message.personId);
        if (senderPerson) {
            items.push({
                senderName: (message.personId == personId) ? "You" : senderPerson.firstName + " " + senderPerson.lastName,
                createdAt: message.createdAt,
                message: message.message
            });
        }

        items = items.concat(_.map(message.replies, (r) => {
            const senderPerson = People.findOne(r.personId);
            if (senderPerson) {
                return {
                    senderName: (r.personId == personId) ? "You" : senderPerson.firstName + " " + senderPerson.lastName,
                    createdAt: r.createdAt,
                    message: r.message
                };
            }
        }));

        return items;
    }

    /**
     * Returns the recipients of a message as a string.
     *
     * @param message
     * @param currentPersonId
     * @param expand
     * @param initialsOnly
     * @returns {string|string}
     */
    static async threadRecipients(message, currentPersonId, expand, initialsOnly) {
        let output = "", initials;
        if (message.personId === currentPersonId) {
            if (!expand && message.currentRecipients.length > 3) {
                output = message.currentRecipients.length.toString() + " recipients";
            } else {
                if(message.currentRecipients && message.currentRecipients.length > 0) {
                    for (const [i, r] of message.currentRecipients.entries()) {
                        const p = await People.findOneAsync({ _id: r });
                        if (p) {
                            if (i > 0) {
                                output += (i === message.currentRecipients.length - 1) ? " and " : ", ";
                            }
                            output += p.firstName + " " + p.lastName;
                            if (initialsOnly && !initials) {
                                initials = p.firstName.substring(0, 1) + p.lastName.substring(0, 1);
                            }
                        }
                    }
                }
            }
        } else {
            const p = await People.findOneAsync({ _id: message.personId });
            if (p) {
                output = p.firstName + " " + p.lastName;
                if (initialsOnly && !initials) {
                    initials = p.firstName.substring(0, 1) + p.lastName.substring(0, 1);
                }
                if (!!message.includeAllFamily || !!message.lockRecipients) {
                    if (!expand && message.currentRecipients.length > 3) {
                        output = message.currentRecipients.length.toString() + " recipients";
                    } else {
                        const outputArray = [];
                        if(message.currentRecipients && message.currentRecipients.length > 0) {
                            for (const [i, r] of message.currentRecipients.entries()) {
                                const p = await People.findOneAsync({ _id: r });
                                if (p && r !== currentPersonId) {
                                    outputArray.push(p.firstName + ' ' + p.lastName);
                                    if (initialsOnly && !initials) {
                                        initials = p.firstName.substring(0, 1) + p.lastName.substring(0, 1);
                                    }
                                }
                            }
                        }
                        for (let i = 0; i < outputArray.length; i++) {
                            output += (i === outputArray.length - 1) ? ' and ' : ', ';
                            output += outputArray[i];
                        }
                    }
                }
            }
        }

        return initialsOnly ? (initials ? initials.toUpperCase() : "") : output;
    }

        /**
     * Returns the recipients of a message as a string.
     *
     * @param message
     * @param currentPersonId
     * @param expand
     * @param initialsOnly
     * @returns {string|string}
     */
        static threadRecipientsForClient(message, currentPersonId, expand, initialsOnly) {
            let output = "", initials;
            if (message.personId === currentPersonId) {
                if (!expand && message.currentRecipients.length > 3) {
                    output = message.currentRecipients.length.toString() + " recipients";
                } else {
                    _.each(message.currentRecipients, (r, i) => {
                        const p = People.findOne({ _id: r });
                        if (p) {
                            if (i > 0) {
                                output += (i === message.currentRecipients.length - 1) ? " and " : ", ";
                            }
                            output += p.firstName + " " + p.lastName;
                            if (initialsOnly && !initials) {
                                initials = p.firstName.substring(0, 1) + p.lastName.substring(0, 1);
                            }
                        }
                    });
                }
            } else {
                const p = People.findOne({ _id: message.personId });
                if (p) {
                    output = p.firstName + " " + p.lastName;
                    if (initialsOnly && !initials) {
                        initials = p.firstName.substring(0, 1) + p.lastName.substring(0, 1);
                    }
                    if (!!message.includeAllFamily || !!message.lockRecipients) {
                        if (!expand && message.currentRecipients.length > 3) {
                            output = message.currentRecipients.length.toString() + " recipients";
                        } else {
                            const outputArray = [];
                            _.each(message.currentRecipients, (r, i) => {
                                const p = People.findOne({ _id: r });
                                if (p && r !== currentPersonId) {
                                    outputArray.push(p.firstName + ' ' + p.lastName);
                                    if (initialsOnly && !initials) {
                                        initials = p.firstName.substring(0, 1) + p.lastName.substring(0, 1);
                                    }
                                }
                            });
                            for (let i = 0; i < outputArray.length; i++) {
                                output += (i === outputArray.length - 1) ? ' and ' : ', ';
                                output += outputArray[i];
                            }
                        }
                    }
                }
            }
    
            return initialsOnly ? (initials ? initials.toUpperCase() : "") : output;
        }
}
