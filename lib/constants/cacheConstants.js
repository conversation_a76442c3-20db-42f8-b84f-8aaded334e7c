/**
 * Cache constants
 *
 */
export const CACHE_KEYS = {
    PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT: 'processpermissionsPeopleMovementEdit',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION: 'requireReservation',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION: 'notifyWithoutReservation',
    OR<PERSON>_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN: 'staffLockdown',
    ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED: 'staffRequiredPINCodeCheckIn',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER: 'checkInOrder',
    ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED: 'familyCheckIn',
    ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS: 'punchCards',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION: 'showTransportation',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOWTRANSPORTATION: 'checkOutShowTransportation',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK: 'showHealthCheck',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETPERSON: 'targetPerson',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_INVOICES: 'invoices',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETGROUP: 'targetGroup',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TGROUP: 'tGroup',
    ORGS_CUSTOMIZATION_PEOPLE_QR_CODE_CHECK_IN_ENABLED: 'qrCodeCheckin',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOW_STAFF_CERTIFICATION_MESSAGE: 'showStaffCertificationMessage',
    ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRE_COMPANION_SELECTION: 'requireCompanionSelection',
}
Object.freeze(CACHE_KEYS);