import { Meteor } from 'meteor/meteor';
import { PUNCH_CARD_TYPE, ITEM_TYPE } from './constants/billingConstants';
import { DiscountTypes } from './discountTypes';
import { People } from './collections/people';
import { Orgs } from './collections/orgs';
import { Groups } from './collections/groups';
const moment = require('moment-timezone');
import _ from '../lib/util/underscore';

export class InsertBillingChargeService {
	static async getCurrentUserOrgPersonGroup(orgId, options) {
		const currentUser = orgId ? null : await Meteor.userAsync();
		const currentOrg = await Orgs.findOneAsync({ _id: orgId || currentUser.orgId });
		const person = options.personId && await People.findOneAsync({ orgId: currentOrg._id, _id: options.personId });
		const getGroup = await Groups.findOneAsync({ orgId: currentOrg._id, _id: options.groupId });
		const group = options.groupId && getGroup;
		return { currentUser, currentOrg, person, group };
	}

	static validateChargeOptions(options, person, group, currentOrg) {
		if ((options.personId && !person) || (person && person.type !== "person")) {
			throw new Meteor.Error(500, "Cannot charge this type of person");
		}
		if ((options.groupId && !group)) {
			throw new Meteor.Error(500, "Invalid group specified");
		}
		const currentItem = _.find(currentOrg.billing.plansAndItems, (i) => {
			return [ITEM_TYPE, PUNCH_CARD_TYPE].includes(i.type) && i._id === options.item;
		});
		if (!currentItem) {
			throw new Meteor.Error('500', 'Cannot find the selected item');
		}
		const price = parseFloat(options.price);
		if (isNaN(price)) {
			throw new Meteor.Error('500', 'You must supply a valid amount for the item charge.');
		}
		return { currentItem, price };
	}

	static getPushData(options, currentItem, currentOrg, price) {
		const newItem = {
			_id: Random.id(),
			originalItem: currentItem,
			createdAt: new moment().valueOf(),
			price: this.roundToTwo(price),
			quantity: options.quantity,
			notes: options.notes,
			type: "item"
		};
		// Add time period ids from registration fees to charges
		if (options.timePeriodIds?.length) {
			newItem.timePeriodIds = options.timePeriodIds;
		}
		if (options.chargebackReference) {
			newItem.chargebackReferencePspId = options.chargebackReference.chargebackPspId;
		}
		if (options.discount_reason) {
			if (newItem.price > currentItem.amount) {
				throw new Meteor.Error(500, "You cannot select a discount reason if the charged price is greater than the normal item price.");
			}
			const originalAllocation = _.find(currentOrg.availableDiscountTypes(false, true), (dt) => dt.type === options.discount_reason);
			let amount = this.roundToTwo(currentItem.amount - newItem.price);
			if (amount < 0) {
				amount = 0
			}
			newItem.appliedDiscounts = [
				{
					type: "discount",
					amount: amount * parseInt(options.quantity),
					source: options.discount_reason,
					originalAllocation
				}
			];
		}
		if (!options.discount_reason && options.allocations && options.allocations.length) {
			newItem.appliedDiscounts = [];
			for (const allocation of options.allocations) {
				newItem.appliedDiscounts.push({
					type: "discount",
					amount: allocation?.discountAmount * parseInt(options.quantity),
					source: DiscountTypes?.COUPON,
					originalAllocation: allocation
				});
			}
		}

		const pushData = {
			'billing.pendingCharges': newItem
		}
		if (currentItem?.type === ITEM_TYPE && currentItem?.details?.dateType) {
			pushData['billing.enrolledItems'] = newItem;
		} else if (currentItem?.type === PUNCH_CARD_TYPE) {
			newItem.datesUsed = Array(Number(currentItem?.numberOfDays)).fill(0);
			pushData['billing.enrolledPunchCards'] = newItem;
		}
		return pushData;
	}

	static async createPeopleIds(person, group, options, currentOrg) {
		let peopleIds = [];
		if (person) {
			peopleIds.push(person._id);
		} else if (group) {
			const groupPeopleIds = await People.find({
				orgId: currentOrg._id,
				type: "person",
				defaultGroupId: group._id,
				inActive: { "$ne": true }
			}).mapAsync(p => p._id);
			peopleIds.push(...groupPeopleIds);
		} else if (options.targets) {
			for (const target of options.targets) {
				if (target === "group:allenrollees") {
					peopleIds.push(...(await People.find({
						orgId: currentOrg._id,
						type: "person",
						inActive: { "$ne": true }
					}).mapAsync(p => p._id)));
				} else if (target.startsWith("group:")) {
					const groupId = target.replace("group:", "");
					peopleIds.push(...(await People.find({
						orgId: currentOrg._id,
						type: "person",
						defaultGroupId: groupId,
						inActive: { "$ne": true }
					}).mapAsync(p => p._id)));
				} else {
					const person = await People.findOneAsync({ orgId: currentOrg._id, _id: target });
					if (person) {
						peopleIds.push(target);
					}
				}
			}
		}
		return peopleIds;
	}

	static roundToTwo(num) {
		return +(Math.round(num + "e+2") + "e-2");
	}
}