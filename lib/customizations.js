import { Orgs } from "./collections/orgs";

/**
 * Since there are no enums yet, this is an object of customizations that a customer can have.
 *
 * @type {{COUPON_CODES: string}} {@link https://hmhcinc.atlassian.net/browse/CRM-13555}
 * @type {{NON_FAMILY_FUNDS_ENABLED: string}} {@link https://lineleader.atlassian.net/browse/CRM-14089}
 * @type {{PLAN_BUNDLES: string}} {@link https://hmhcinc.atlassian.net/browse/CRM-13636}
 * @type {{REGISTRATION_FLOW: string}} {@link https://hmhcinc.atlassian.net/browse/CRM-13537}
 */
export const AvailableCustomizations = {
    AIRSLATE_ENABLED: 'integrations/airslate/enabled',
    ALLOW_MOMENTS_WHEN_CHECKED_OUT: 'moments/postWhenCheckedOut/enabled', // Whether moments can be posted when checked out
    ALLOW_MOMENTS_WHEN_AUTO_CHECKED_OUT: 'moments/checkin/autocheckout', // Whether moments can be posted when auto checked out
    AUTO_PIN: 'people/automaticPin', // Whether to automatically generate PIN
    AUTO_PRORATE_BY_DATE: 'billing/autoProrateByDate/enabled', // Whether to auto prorate by enrollment date
    AUTO_PRORATE_BY_SCHEDULE: 'billing/autoProrateBySchedule/enabled', // Whether to auto prorate by child's schedule
    BILLING_PLATFORM_FEES: 'billing/platformFees', // Whether to show platform fees on the invoice
    COUPON_CODES: 'billing/configuration/couponCodes', // Whether the customer can see the coupon codes tab in Billing->Configuration.
    CURRICULUM_AGE_GROUPS: 'modules/curriculum/ageGroups',
    CURRICULUM_BANK_GLOBAL_AND_LOCAL: 'curriculumBank/globalAndLocal',
    CURRICULUM_BANK_REQUIRE_THEMES: 'modules/curriculum/requireThemes',
    DENY_CONTACT_INFO: 'people/staffViewContactInfo/disabled', // Whether users can see contact info in profile.
    DISABLE_ACH: 'billing/disableACH/enabled', // Disable ACH payment options
    DISABLE_CREDIT_CARDS: 'billing/disableCards/enabled', // Disable credit card payment options
    DISABLE_INVOICE_MODIFICATION: 'billing/disableInvoiceModification', // Disable invoice modification
    DISABLE_TAG_GROUPS: 'moments/disableTagGroups', // Disable tag groups
    DOOR_LOCKS_ZKTECO: 'integrations/doorlock/zkTeco',
    FAMILY_ADVANCED_DASHBOARD: 'registrationFlow/enhancedParentDashboard', // Whether the customer can see the enhanced parent dashboard
    FEATURE_CHECKIN_ASYNC: 'feature/checkin/async', // Set it to true if you want to run checkin function to run async
    HELDFUNDS_ENABLED: 'billing/configuration/heldfunds/enabled', // Whether the held funds table appears on the child's billing tab
    HIDE_EDIT_REGISTRATION: 'people/hideEditRegistration', // Whether the customer can see the registration flow work.
    INQUIRIES_ENABLED: 'inquiries/enabled',
    MOMENTS_CHECKIN_ORDER: 'moments/checkin/checkinorder',
    MOMENTS_CHECKIN_SHOW_HEALTH_CHECK: 'moments/checkin/showHealthCheck', // Whether to show the health check field in the checkin moments
    MOMENTS_CHECKIN_SHOW_TRANSPORTATION: 'moments/checkin/showTransportation', // Whether to show the transportation field in the checkin moments
    MOMENTS_CHECKIN_STAFF_LOCKDOWN: 'moments/checkin/staffLockdown', // Whether staff can change the time when checking in
    NON_FAMILY_FUNDS_ENABLED: 'billing/configuration/receiveNonFamilyFunds/enabled', // Payer funding that isn't for a specific family.
    ORG_HIERARCHY: 'featureflag/orgHierarchy', // Org Hierarchy feature flag
    PAYER_CASH_LEDGER: 'billing/configuration/payerCashPostingLedger', // Whether an alternate Cash Ledger Account can be configured for 3rd party payers.
    PEOPLE_CHAT_SUPPORT_ENABLED: 'people/chatSupport/enabled',
    PEOPLE_CHECKIN_PREK_DEFAULT: 'people/checkin/prekDefault',
    PEOPLE_FAMILY_CHECKIN_ENABLED: 'people/familyCheckin/enabled', // Whether the customer can see the family check-in work.
    PEOPLE_STAFF_PAY: 'people/staffPay/enabled', // Whether staff pay functionality is enabled
    PLAN_BUNDLES: 'billing/configuration/bundles', // Whether the customer can create bundles for plans.
    POST_USING_PERIOD_START_DATE: 'billing/configuration/postUsingPeriodStart', // Whether to post transactions using the period start date
    PREVENT_DEBIT_CARDS: 'billing/payments/preventDebitCards', // Prevent debit card payments
    PUNCH_CARDS: 'billing/configuration/punchCards', // Whether users can see punch card features
    QUEUE_AUTO_PAYMENTS: 'billing/queueAutopayments/enabled', // Whether to queue autopayments
    QUICKBOOKS_REVENUE_EXPORT: 'quickBooksOnlineRevenueExport/enabled', // Whether the payouts report exports the QuickBooks formatted report
    RAS_WEBHOOK_UPDATES: 'rasWebhookUpdates', // Whether to notify RAS via webhook whenever a child’s registration or schedule is created, changed, or removed
    REGISTRATION_FLOW: 'registrationFlow', // Whether the customer can see the registration flow work.
    REQUIRE_LEDGER_ACCOUNT: 'billing/requireLedgerAccountName/enabled', // Whether the customer must enter a ledger account name when creating a plan
    REQUIRE_PAYMENT_METHOD: 'billing/requirePaymentMethod/enabled', // Whether the customer must have a payment method on file
    RESERVATIONS_ENABLED: 'reservations/enabled', // Whether the customer can see reservations.
    SCHEDULE_TYPES_FORCED_LINK_TO_PLANS: 'scheduleTypes/forceLinkToPlans/enabled', // Whether the customer must link a schedule to a plan
    SHOW_EMPLOYEE_ID_PLR: 'parentLedRegistration/showEmployeeID', // Whether to hide the employee ID field in the parent-led registration
    SHOW_TRANSPORTATION: 'moments/checkout/showTransportation', // Whether to show the transportation field in the checkout moments
    STAFF_WORK_LOCATIONS: 'designateStaffWorkLocations', // Whether staff can be designated to work at multiple locations
    TIMECARDS_PROPAGATE_LOCK_SETTINGS: 'timeCards/propagateLockSettings/enabled',
    UPDATE_DEFAULT_GROUP_BY_SCHEDULE: 'people/updateDefaultGroupBySchedule/enabled',
    VARIABLE_MONTHLY_SUBSIDIES: 'billing/plans/variableMonthlySubsidies', // Whether variable monthly subsidies are enabled
    WEEKENDS_ENABLED: 'billing/weekends/enabled', // Whether the customer is open on weekends
    WL_PIN_LOGIN: 'whitelabeled/AdminStaffPinLogin/enabled', // Whether to enable direct pin login for admin & staff on mobile. Requires global pin uniqueness
    PICK_DROP_REASON_ENABLED: 'people/checkInCheckOutPickDropReason/enabled', // Whether to enable early pick / late drop reason while doing checkin / checkout,
    HIDE_SEND_INVITATION_FOR_STAFF_AND_ADMINS: 'people/hideSendInvitationForStaffAndAdmins',
    RAS_GRADE_LABEL: 'ras/GradeLabel', // Enables a different label to use rather than "Grade" in PLR/DIY
};

Object.freeze(AvailableCustomizations)

export class CustomizationsService {

    // This method only used at client side hence no need to change to async await
    static canViewContactInfo(personId = null) {
        if (!Orgs.current().hasCustomization(AvailableCustomizations.DENY_CONTACT_INFO)) {
            return true;
        }
        const currentPerson = Meteor.user() && Meteor.user().fetchPerson();
        if (!currentPerson) {
            return false;
        }
        if (personId && personId === currentPerson._id) {
            return true;
        }
        return currentPerson.type !== 'staff';
    }
}
