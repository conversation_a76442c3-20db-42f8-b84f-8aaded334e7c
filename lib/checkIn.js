import moment from "moment-timezone";
import { Meteor } from 'meteor/meteor';
import { Cache } from "./util/cacheUtils";
import { CACHE_KEYS } from "./constants/cacheConstants";
import { Log } from './util/log';
import { CHECKIN_ASYNC_ALERT_EMAIL } from './constants/emailConstants';
import { AvailableCustomizations } from "./customizations";
import { People } from "./collections/people";
import { processAnalytics } from "../server/analytics";
import _ from "./util/underscore";
import { Orgs } from "./collections/orgs";
import { Groups } from "./collections/groups";
import { Reservations } from "./collections/reservations";
import { Invoices } from "./collections/invoices";
import { processPermissions } from "./permissions";
import { recalculateGroupMediaDesignation } from "./momentHelpers";
import { Moments } from "./collections/moments";
import { StaffPayTypeAdpCodes } from './constants/orgConstants';

const toEmail = Meteor.settings.emails?.checkinAsyncAlert || CHECKIN_ASYNC_ALERT_EMAIL;

const checkIn = async (checkInData, dontUpdatePersonCollection = false) => {
  console.log("Checkin Start: ", Date.now())
  const currentUser = await Meteor.userAsync();
  const currentPerson = await currentUser?.fetchPerson();
  const org = await Orgs.current();
  let sortStamp, createdAt = new Date().valueOf();
  let timezone = org.getTimezone();
  let targetPerson = await People.findOneAsync(checkInData.personId);
  const createdByUser = checkInData.isKioskMode ? await Meteor.users.findOneAsync({ personId: checkInData.checkedInById }) : currentUser;
  const createdByPersonId = checkInData.isKioskMode ? checkInData.checkedInById : currentUser.personId;
  console.log("Checkin Start: targetPerson", targetPerson._id, Date.now())
  //get the pin code from current user
  let pinCode = getPinCode(currentPerson);
  //checking the permission
  await currentPersonPinCodeCheck(checkInData, currentPerson, pinCode);
  getTargetPersonDetails(dontUpdatePersonCollection, targetPerson, currentUser);
  const { staffLockdown } = await checkHasCustomization(org, targetPerson, currentPerson, currentUser, checkInData);
  let getcheckedInPersonDetails = await getCheckInDataByTargetPerson(dontUpdatePersonCollection, checkInData, targetPerson, currentUser, timezone, staffLockdown);
  checkInData = getcheckedInPersonDetails.checkInData;
  createdAt = getcheckedInPersonDetails.createdAt;
  sortStamp = getcheckedInPersonDetails.sortStamp;
  let checkInUpdateData = {
    checkedIn: true,
    checkedInOutTime: sortStamp,
    checkInGroupId: checkInData.groupId,
    checkInGroupName: checkInData.groupName,
    checkedInById: checkInData.checkedInById,
    previousClassroomGroupId: checkInData.previousClassroomGroupId,
    presenceLastGroupId: null,
  };
  checkInUpdateData = await getCheckInOrder(org, targetPerson, checkInUpdateData);
  const { checkInOutlook, checkInReminders, lastInformedArrival, familyCheckin_enabled } = getPincodeFormFieldByOrg(org, checkInData, targetPerson, sortStamp, checkInUpdateData);
  //checkInUpdateData.checkInReminders = checkInReminders?checkInReminders:{};
  if (checkInReminders) {
    checkInUpdateData.checkInReminders = checkInReminders;
  }
  if (_.size(checkInOutlook) > 0) {
    checkInUpdateData.checkInOutlook = checkInOutlook;
  }
  if (lastInformedArrival.source) {
    checkInUpdateData.lastInformedArrival = lastInformedArrival;
  }
  checkInUpdateData.familyCheckOut = {};
  if (!dontUpdatePersonCollection) {
    await People.updateAsync(checkInData.personId, {
      $set: checkInUpdateData,
      $unset: { familyCheckIn: 1 }
    });
  }

  /**Adding this feature to run a code block in async if not set then run it normally */
  if (org.hasCustomization(AvailableCustomizations.FEATURE_CHECKIN_ASYNC)) {
    if (Meteor.isServer) {
      Meteor.defer(Meteor.bindEnvironment(async function () {
        if (checkInData?.groupId) {
          await recalculateGroupMediaDesignation(checkInData.groupId);
        }
        const checkInDate = moment().tz(timezone).format("M/DD/YYYY");
        const todayStamp = new moment.tz(timezone).startOf("day").valueOf();
        const todayDay = new moment.tz(timezone).format("ddd").toLowerCase();
        const reservations = await Reservations.find({ selectedPerson: checkInData.personId }).fetchAsync() || [];
        const scheduledEntries = reservations.filter(entry => !entry.scheduledEndDate || entry.scheduledEndDate > todayStamp);
        const checkedInTime = moment.tz(checkInData.prettyTime, "hh:mm a", timezone);
        const orgScheduleTypes = org?.valueOverrides?.scheduleTypes || [];
        const dropInBuffer = org.hasCustomization("registrationFlow") ? (org?.billing?.scheduling?.dropInBuffer || '0') : '0';
        let childHasScheduleForToday = getChildHasScheduleTodayStatus(checkInData, org, todayStamp, todayDay, scheduledEntries, checkedInTime, orgScheduleTypes, timezone, dropInBuffer);
        let firstFoundDropInCharge = org?.billing?.plansAndItems?.find(item => (item.dropInDailyRate === true && (item.expires >= todayStamp || item.expires === undefined)));
        if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`)) {
          const getPunchCards = org.hasCustomization("billing/configuration/punchCards");
          Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`, getPunchCards);
        }
        const punchCards = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`);
        if ((punchCards) && firstFoundDropInCharge !== undefined) {
          const childEnrolledInPunchCards = targetPerson.billing?.enrolledPunchCards?.filter(card => !card.refunded).length > 0;
          const childPurchasedDropInToday = !! await Invoices.findOneAsync({
            $and: [
              { personId: checkInData.personId },
              { invoiceDate: checkInDate },
              { lineItems: { $elemMatch: { 'originalItem._id': { $in: [firstFoundDropInCharge._id] } } } }
            ]
          });
          const allPunchCardsAreUsed = targetPerson?.totalPunchCardDays?.every(day => Number(day) !== 0);
          const hasPunchCards = (!allPunchCardsAreUsed && childEnrolledInPunchCards);
          if (!childHasScheduleForToday && !dontUpdatePersonCollection) {
            // use punch card or charge drop in
            if (hasPunchCards) {
              // Have they been punched already today?

              const useDate = checkedInTime.format('MM/DD/YYYY');
              if (!targetPerson?.totalPunchCardDays?.find(day => day === useDate)) {
                markPunchCardUsed(checkInData, checkedInTime)
              }
            }
            else if (!childPurchasedDropInToday) {
              const billingCharge = {
                item: firstFoundDropInCharge._id,
                notes: "",
                price: firstFoundDropInCharge.amount,
                discount_reason: null,
                quantity: "1",
                amount: firstFoundDropInCharge.amount,
                personId: checkInData.personId
              };
              insertBillingChargeAction(billingCharge, org, checkInData)
            }
          }
        }
        await momentInsert(checkInData, createdByUser, createdByPersonId, currentUser, createdAt, sortStamp, currentPerson, org, targetPerson, pinCode, familyCheckin_enabled, timezone);
        await getProcessAnalyst(checkInData, org, targetPerson);
      }))
    }
    console.log("Async Checkin End and return to parent call: targetPerson", targetPerson._id, Date.now())
    return true;
  } else {
    if (checkInData?.groupId) {
      await recalculateGroupMediaDesignation(checkInData.groupId);
    }
    const checkInDate = moment().tz(timezone).format("M/DD/YYYY");
    const todayStamp = new moment.tz(timezone).startOf("day").valueOf();
    const todayDay = new moment.tz(timezone).format("ddd").toLowerCase();
    const reservations = await Reservations.find({ selectedPerson: checkInData.personId }).fetchAsync() || [];
    const scheduledEntries = reservations.filter(entry => !entry.scheduledEndDate || entry.scheduledEndDate > todayStamp);
    const checkedInTime = moment.tz(checkInData.prettyTime, "hh:mm a", timezone);
    const orgScheduleTypes = org?.valueOverrides?.scheduleTypes || [];
    const dropInBuffer = org.hasCustomization("registrationFlow") ? (org?.billing?.scheduling?.dropInBuffer || '0') : '0';
    let childHasScheduleForToday = getChildHasScheduleTodayStatus(checkInData, org, todayStamp, todayDay, scheduledEntries, checkedInTime, orgScheduleTypes, timezone, dropInBuffer);
    let firstFoundDropInCharge = org?.billing?.plansAndItems?.find(item => (item.dropInDailyRate === true && (item.expires >= todayStamp || item.expires === undefined)));
    if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`)) {
      const getPunchCards = org.hasCustomization("billing/configuration/punchCards");
      Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`, getPunchCards);
    }
    const punchCards = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS}`);
    if ((punchCards) && firstFoundDropInCharge !== undefined) {
      const childEnrolledInPunchCards = targetPerson.billing?.enrolledPunchCards?.filter(card => !card.refunded).length > 0;
      const childPurchasedDropInToday = !!await Invoices.findOneAsync({
        $and: [
          { personId: checkInData.personId },
          { invoiceDate: checkInDate },
          { lineItems: { $elemMatch: { 'originalItem._id': { $in: [firstFoundDropInCharge._id] } } } }
        ]
      });
      const allPunchCardsAreUsed = targetPerson?.totalPunchCardDays?.every(day => Number(day) !== 0);
      const hasPunchCards = (!allPunchCardsAreUsed && childEnrolledInPunchCards);
      if (!childHasScheduleForToday && !dontUpdatePersonCollection) {
        // use punch card or charge drop in
        if (hasPunchCards) {
          // Have they been punched already today?

          const useDate = checkedInTime.format('MM/DD/YYYY');
          if (!targetPerson?.totalPunchCardDays?.find(day => day === useDate)) {
            markPunchCardUsed(checkInData, checkedInTime)
          }
        }
        else if (!childPurchasedDropInToday) {
          const billingCharge = {
            item: firstFoundDropInCharge._id,
            notes: "",
            price: firstFoundDropInCharge.amount,
            discount_reason: null,
            quantity: "1",
            amount: firstFoundDropInCharge.amount,
            personId: checkInData.personId
          };
          insertBillingChargeAction(billingCharge, org, checkInData)
        }
      }
    }
    await momentInsert(checkInData, createdByUser, createdByPersonId, currentUser, createdAt, sortStamp, currentPerson, org, targetPerson, pinCode, familyCheckin_enabled, timezone);
    await getProcessAnalyst(checkInData, org, targetPerson);
    console.log("Normal Checkin End and return to parent call: targetPerson", targetPerson._id, Date.now())
  }
}

//get currentPerson Pincode
const getPinCode = (currentPerson) => {
  let { pinCode } = currentPerson;
  if (currentPerson?.pinCodeSupplemental?.length > 0) {
    pinCode = pinCode + currentPerson.pinCodeSupplemental;
  }
  return pinCode;
}

//get CurrentPersonPincode and check
const currentPersonPinCodeCheck = async (checkInData, currentPerson, pinCode) => {
  if (!currentPerson) {
    throw new Meteor.Error(403, "Access denied");
  }
  else if (currentPerson.type == "family" && checkInData.pinCode) {
    if (pinCode != checkInData.pinCode)
      throw new Meteor.Error(403, "Access denied");
  } else {
    if (!Cache.has(`${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`)) {
      const getProcessPermissions = await processPermissions({
        assertions: [{ context: "people/movement", action: "edit" }],
        evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
        throwError: true
      });
      Cache.set(`${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`, getProcessPermissions);
    }
    return Cache.get(`${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`);
  }
}

//get target person details
const getTargetPersonDetails = (dontUpdatePersonCollection, targetPerson, currentUser) => {
  let checkInPersonOrgId = targetPerson["orgId"];
  if (!(checkInPersonOrgId == currentUser["orgId"])) throw new Meteor.Error(403, "Access denied");
  if (targetPerson.inActive) throw new Meteor.Error(500, "That person is inactive and cannot be checked in.");
  if (targetPerson.checkedIn && !dontUpdatePersonCollection) throw new Meteor.Error(500, "Person already checked in");
}

//check hasCustomization
const checkHasCustomization = async (org, targetPerson, currentPerson, currentUser, checkInData) => {
  if (!org) throw new Meteor.Error(500, "No org");

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION}`)) {
    const getRequireReservation = org.hasCustomization("moments/checkin/requireReservation");
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION}`, getRequireReservation);
  }
  const requireReservation = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION}`);

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION}`)) {
    const getNotifyWithoutReservation = org.hasCustomization("moments/checkin/notifyWithoutReservation");
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION}`, getNotifyWithoutReservation);
  }
  const notifyWithoutReservation = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION}`);
  let unscheduledCheckin = false;

  if ((requireReservation || notifyWithoutReservation) && targetPerson.type == "person") {
    const reservations = await Reservations.findWithRecurrence({
      query: { selectedPerson: targetPerson._id }
    });
    if (reservations.length == 0) {
      unscheduledCheckin = true;
      if (requireReservation)
        throw new Meteor.Error(500, "Person does not have a current reservation. Please contact a staff member for assistance.");
    }
  }
  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`)) {
    const getStaffLockdown = org.hasCustomization("moments/checkin/staffLockdown")
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`, getStaffLockdown);
  }
  const staffLockdown = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`);
  if (currentPerson.type == "staff" && staffLockdown
    && (targetPerson.type == "staff" || targetPerson.type == "admin") && currentUser.personId != checkInData.personId)
    throw new Meteor.Error(403, "Access denied -- staff can only checkin themselves");

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`)) {
    const getStaffRequiredPinCodeCheckin_enabled = org.hasCustomization("people/staffRequiredPinCodeCheckin/enabled")
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`, getStaffRequiredPinCodeCheckin_enabled);
  }
  const staffRequiredPinCodeCheckin_enabled = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`);
  if (currentPerson.type == "staff" && targetPerson.type != "person"
    && staffRequiredPinCodeCheckin_enabled && checkInData.prettyTime)
    throw new Meteor.Error(403, "Access denied -- staff are required to use pin code checkin");

  return {
    staffLockdown
  }
}

//get checkin data based on current and target person details
const getCheckInDataByTargetPerson = async (dontUpdatePersonCollection, checkInData, targetPerson, currentUser, timezone, staffLockdown) => {
  if (!dontUpdatePersonCollection) {
    checkInData.date = new moment().tz(timezone).format("MM/DD/YYYY")
  }
  if (!checkInData.prettyTime || (targetPerson && targetPerson.type == "staff" && staffLockdown))
    checkInData.prettyTime = new moment().tz(timezone).format("h:mm a");
  var sortStamp, createdAt = new Date().valueOf();
  if (checkInData.date && checkInData.prettyTime)
    sortStamp = new moment.tz(checkInData.date + " " + checkInData.prettyTime, "MM/DD/YYYY h:mm a", timezone).valueOf();
  else
    sortStamp = createdAt;

  if (checkInData.checkInDefaultGroup && targetPerson.defaultGroupId) {
    const targetGroup = await Groups.findOneAsync({ "orgId": currentUser.orgId, "_id": targetPerson.defaultGroupId });
    if (targetGroup) {
      checkInData.groupId = targetGroup._id;
      checkInData.groupName = targetGroup.name;
    }
  }
  // for previousClassroomGroupId we find the target and set if classlist included
  if (checkInData.groupId) {
    const tGroup = await Groups.findOneAsync({ _id: checkInData.groupId });
    checkInData.previousClassroomGroupId = (tGroup && tGroup.includeClassList) ? tGroup._id : null;
  }

  return {
    checkInData,
    sortStamp,
    createdAt
  }
}
//get checkin count and checkinorder
const getCheckInOrder = async (org, targetPerson, checkInUpdateData) => {
  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`)) {
    const getCheckinorder = org.hasCustomization("moments/checkin/checkinorder");
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`, getCheckinorder);
  }
  const checkinorder = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`);
  if (checkinorder && targetPerson.type == "person" && Meteor.isServer) {
    var checkInCount = await org.nextCounterValue("checkinorder", { daily: true });
    checkInUpdateData.checkInOrder = checkInCount;
  }
  return checkInUpdateData;
}
//get pincodeFormField by org
const getPincodeFormFieldByOrg = (org, checkInData, targetPerson, sortStamp, checkInUpdateData) => {
  let lastInformedArrival = { fieldValues: {} };
  const checkInOutlook = [];
  let checkInReminders = {};

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED}`)) {
    const getFamilyCheckin_enabled = org.hasCustomization("people/familyCheckin/enabled");
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED}`, getFamilyCheckin_enabled);
  }
  const familyCheckin_enabled = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED}`);
  if (org.pinCodeCheckinFields() && checkInData.pinCodeFormFields) {
    for (const field of org.pinCodeCheckinFields()) {
      if (field.remind) checkInReminders[field.dataId] = checkInData.pinCodeFormFields[field.dataId];
      if (field.outlook || field.dataId === "notes") checkInOutlook.push(`${field.label}: ${checkInData.pinCodeFormFields[field.dataId]}`)
      if (checkInData.pinCodeFormFields[field.dataId]) {
        lastInformedArrival.source = "pinCode";
        lastInformedArrival.checkedInById = checkInData.checkedInById;
        lastInformedArrival.createdAt = sortStamp;
        lastInformedArrival.fieldValues[field.dataId] = checkInData.pinCodeFormFields[field.dataId];
      }
    }
    if (checkInData.pinCodeFormFields.comment) checkInReminders.comment = checkInData.pinCodeFormFields.comment;
    if (_.size(checkInReminders) > 0) checkInUpdateData.checkInReminders = checkInReminders;
    //return { checkInOutlook, checkInReminders, lastInformedArrival }
  } else if (familyCheckin_enabled && targetPerson &&
    targetPerson.currentFamilyCheckin()) {
    for (const field of org.pinCodeCheckinFields()) {
      if ((field.outlook || field.dataId === "notes") && targetPerson?.familyCheckIn?.formFieldData?.[field.dataId]) {
        checkInOutlook.push(`${field.label}: ${targetPerson.familyCheckIn.formFieldData[field.dataId]}`);
      }
    }

    //return { checkInOutlook };
  }
  return { checkInOutlook, checkInReminders, lastInformedArrival, familyCheckin_enabled }
}

//get child has schedul for today
const getChildHasScheduleTodayStatus = (checkInData, org, todayStamp, todayDay, scheduledEntries, checkedInTime, orgScheduleTypes, timezone, dropInBuffer) => {
  todayStamp = new moment.tz(timezone).startOf("day").valueOf();
  checkedInTime = moment.tz(checkInData.prettyTime, "hh:mm a", timezone);
  orgScheduleTypes = org?.valueOverrides?.scheduleTypes || [];
  for (const entry of scheduledEntries) {
    // it seems like we just need to check if the reservation is scheduled for today or this day of the week.
    const hasScheduleToday = (!entry.recurringDays && entry.scheduledDate === todayStamp) || (entry.recurringDays?.length && entry.recurringDays.includes(todayDay));
    const fallsInsideTimeRange = hasScheduleToday ? checkScheduleType(checkedInTime, orgScheduleTypes, entry, timezone, dropInBuffer) : false;
    if (fallsInsideTimeRange) {
      return true;
      break;
    }
  }
  return false;
}
//mark puchcard once used
const markPunchCardUsed = (checkInData, checkedInTime) => {
  Meteor.callAsync('markPunchCardAsUsed', checkInData.personId, checkedInTime.format('MM/DD/YYYY')).then((result) => {
    console.log('Punch card marked as used successfully:', result);
  }).catch(async (err) => {
    Log.error('An error occurred while attempting to mark punch card as used: ', err);
    const org = await Orgs.current();
    if (org.hasCustomization(AvailableCustomizations.FEATURE_CHECKIN_ASYNC)) {
      Log.info('Sending email to mark punch card as used errors email', toEmail);
      const sub = 'Async Job mark punch card as used Failed on date ' + moment().format('MM/DD/YYYY');
      const bodyMessage = 'An error occurred while attempting to mark punch card as used: ' + (err.reason || err.message);
      await Meteor.callAsync('sendSystemAdminEmail', { toEmail, sub, bodyMessage });
    }

    throw new Meteor.Error('Error', 'An error occurred while attempting to mark punch card as used: ', err.reason || err.message);
  });
}

const insertBillingChargeAction = (billingCharge, org, checkInData) => {
  Meteor.callAsync('insertBillingCharge', billingCharge, org._id, false).then(async (result) => {
    const invoiceData = {
      personId: checkInData.personId
    };
    return await Meteor.callAsync('generateManualInvoice', invoiceData, org._id, false);
  }).then((result) => {
    Log.info('Manual invoice generated successfully', result);
  }).catch(async (error) => {
    Log.error('An error occurred', error);
    if (org.hasCustomization(AvailableCustomizations.FEATURE_CHECKIN_ASYNC)) {
      const sub = 'Async Job Failed on date ' + moment().format('MM/DD/YYYY');
      const bodyMessage = 'An error occurred: ' + (error.reason || error.message);
      Log.info('Sending email to errors email', toEmail);
      await Meteor.callAsync('sendSystemAdminEmail', { toEmail, sub, bodyMessage });
    }
    throw new Meteor.Error('Error', 'An error occurred', error.reason || error.message);
  });
}

//moment insert
const momentInsert = async (checkInData, createdByUser, createdByPersonId, currentUser, createdAt, sortStamp, currentPerson, org, targetPerson, pinCode, familyCheckin_enabled, timezone) => {
  var newMoment = {};
  newMoment["createdAt"] = createdAt;
  newMoment["sortStamp"] = sortStamp;
  newMoment["createdBy"] = createdByUser?._id ?? currentUser._id;
  newMoment["createdByPersonId"] = createdByPersonId;
  newMoment.orgId = currentUser["orgId"];
  newMoment["owner"] = checkInData.personId;
  newMoment["taggedPeople"] = [checkInData.personId];
  newMoment["momentType"] = "checkin";
  newMoment["momentTypePretty"] = "Check In";
  newMoment["time"] = checkInData.prettyTime;
  newMoment["date"] = checkInData.date;
  newMoment["usedPin"] = checkInData.usedPin;
  if (checkInData.groupName) {
    newMoment["comment"] = "Checked into " + checkInData.groupName;
    newMoment["checkInGroupId"] = checkInData.groupId;
    newMoment["checkInGroupName"] = checkInData.groupName;
  }
  if (currentPerson && !checkInData.checkedInById) {
    newMoment["comment"] = `${newMoment["comment"]}. Checked in by ${currentPerson.firstName} ${currentPerson.lastName} \n`
  }
  if (pinCode) {
    newMoment["pinCodePersonId"] = targetPerson._id;
    // We need to check if they are a Pre-K staff member and has correct customization setup to set the pay type accordingly
    if (getPreKPayType(org) && isStaffPreK(targetPerson, org)) {
      newMoment["selectedPayTypeId"] = getPreKPayType(org);
    }
    else {
      newMoment["selectedPayTypeId"] = 'standard';
    }
  }
  if (checkInData.checkedInById) newMoment["checkedInById"] = checkInData.checkedInById;
  if (checkInData.selectedPayTypeId) newMoment["selectedPayTypeId"] = checkInData.selectedPayTypeId;

  if (checkInData.checkedInById) {
    var familyPerson = await People.findOneAsync({ "_id": checkInData.checkedInById })
    if (familyPerson) newMoment["comment"] = (newMoment["comment"] ? newMoment["comment"] + ". " : "") + "Checked in by " + familyPerson.firstName + " " + familyPerson.lastName;
  }
  if (checkInData.comments) {
    check(checkInData.comments, String);
    newMoment["comment"] = ((newMoment["comment"]) ? newMoment["comment"] + " - " : "") + checkInData.comments;
  }
  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION}`)) {
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION}`, org.hasCustomization("moments/checkin/showTransportation"));
  }
  const showTransportation = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION}`);
  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`)) {
    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`, org.hasCustomization("moments/checkin/showHealthCheck"));
  }
  const showHealthCheck = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`);
  if (showTransportation)
    newMoment["checkInTransportation"] = checkInData.transportation;
  if (showHealthCheck) {
    newMoment["checkInHealthChecks"] = checkInData.healthChecks;
    newMoment["checkInHealthCheckCovid19Symptoms"] = checkInData.healthCheckCovid19Symptoms;
    newMoment["checkInHealthCheckCovid19ProlongedContact"] = checkInData.healthCheckCovid19ProlongedContact;
    newMoment["checkInHealthCheckCovid19TemperatureAboveLimit"] = checkInData.healthCheckCovid19TemperatureAboveLimit;
    newMoment["checkInHealthCheckCovid19Temperature"] = checkInData.healthCheckCovid19Temperature;
  }

  // Since pincode checkin can edit the family checkin of informed arrival - we combine this into an if/else if block
  if (org.pinCodeCheckinFields() && checkInData.pinCodeFormFields) {
    if (checkInData.pinCodeFormFields.comment) newMoment["comment"] = checkInData.pinCodeFormFields.comment;

    let checkinComment = "";
    for (const field of org.pinCodeCheckinFields()) {
      newMoment[field.dataId] = checkInData.pinCodeFormFields[field.dataId];
      checkinComment += `${field.label}: ${checkInData.pinCodeFormFields[field.dataId]}\n`;
    }
    newMoment["comment"] = (newMoment["comment"] ? newMoment["comment"] + "\n" : "") + checkinComment;

  } else if (familyCheckin_enabled && targetPerson &&
    targetPerson.currentFamilyCheckin()) {
    const checkedInByPerson = await People.findOneAsync({ _id: targetPerson.familyCheckIn.checkedInById });
    const checkedInTime = new moment.tz(targetPerson.familyCheckIn.checkInTime, timezone).format("h:mm a");
    let familyCheckinComment = "Family check-in by " + checkedInByPerson.firstName + " " + checkedInByPerson.lastName + " at " + checkedInTime + "\n";
    for (const field of org.pinCodeCheckinFields()) {
      if (targetPerson.familyCheckIn.formFieldData[field.dataId])
        familyCheckinComment += field.label + ": " + targetPerson.familyCheckIn.formFieldData[field.dataId] + "\n";
    }
    newMoment["comment"] = (newMoment["comment"] ? newMoment["comment"] + "\n" : "") + familyCheckinComment;
  }

  // Check early late reason and add to moment
  if (org.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED)) {
    newMoment["reason"] = checkInData.earlyLateReason;
  }

  await Moments.insertAsync(newMoment);
}

//get Process Analyst
const getProcessAnalyst = async (checkInData, org, targetPerson) => {
  if (Meteor.isServer) {
    await Meteor.callAsync('checkinUnlockZkTeco', checkInData.personId);

    processAnalytics({
      metaCxData: {
        type: 'checkin', data: {
          orgId: org._id,
          orgName: org.name,
          personId: targetPerson._id,
          type: targetPerson.type,
          groupId: checkInData.groupId
        }
      }
    });
  }
}

export function checkScheduleType(checkedInMoment, orgScheduleTypes, entry, timezone = 'America/New_York', dropInBuffer = '0') {
  if (!orgScheduleTypes.length) {
    return true;
  }
  const scheduleType = orgScheduleTypes.find(scheduleType => scheduleType._id === entry.scheduleType);

  if (!scheduleType) {
    return true;
  }

  if (!scheduleType.startTime && !scheduleType.endTime) {
    return true;
  }

  const startMoment = moment.tz(scheduleType.startTime, "hh:mm a", timezone).subtract(parseInt(dropInBuffer), 'minutes');
  const endMoment = moment.tz(scheduleType.endTime, "hh:mm a", timezone);
  return !!(checkedInMoment.isSameOrAfter(startMoment) && checkedInMoment.isSameOrBefore(endMoment));
}

export function getPreKPayType(org) {
  const orgHasPreKCheckinCustomization = org.hasCustomization(AvailableCustomizations.PEOPLE_CHECKIN_PREK_DEFAULT);
  if (orgHasPreKCheckinCustomization) {
    const payTypes = org.getCustomPayTypes();
    const payType = payTypes.find(payType => payType.adpCode === StaffPayTypeAdpCodes.PKR);
    return payType ? payType._id : null;
  }
  return null;
}

export function isStaffPreK(person, org) {
  const prefix = org.profileDataPrefix();
  if (prefix)  {
      return person.profileData?.workDepartment?.toLowerCase() === 'prek';
  }
  return person.workDepartment?.toLowerCase() === 'prek';
}

export {
  checkIn, getPinCode, currentPersonPinCodeCheck, getTargetPersonDetails, checkHasCustomization, getCheckInDataByTargetPerson,
  getCheckInOrder, getPincodeFormFieldByOrg, getChildHasScheduleTodayStatus, markPunchCardUsed, momentInsert
}
