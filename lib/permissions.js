import { Meteor } from 'meteor/meteor';
import { UserPermissionService } from './userPermissionService';
import { Log } from './util/log';
import { User } from './collections/users';
import { Orgs } from './collections/orgs';

//processPermissions will provide an async promise on server so use await with it
export const processPermissions = function (obj) {
	if (Meteor.isServer){
		return (async () => {
			try {
				const currentUser = await Meteor.userAsync();
				// Requests made from Offline syncs in Engage will pass in the currentPerson and currentOrg
				obj.currentPerson = obj.currentPerson ?? await currentUser?.fetchPerson?.();
				obj.currentOrg = obj.currentOrg ?? await Orgs.current();
				return await UserPermissionService.processPermissionsAsync(obj);
			} catch (error) {
				Log.error(error.message || error.reason);
				throw new Meteor.Error(error.error || 403, error.message || error.reason);
			}
		})();
	}else{
		try {
			obj.currentPerson = Meteor?.user?.()?.fetchPerson?.();
			obj.currentOrg = obj.currentOrg ?? Orgs.current();
			return UserPermissionService.processPermissions(obj);
		} catch (error) {
			Log.error(error.message || error.reason);
			throw new Meteor.Error(error.error || 403, error.message || error.reason);
		}
	}
};
