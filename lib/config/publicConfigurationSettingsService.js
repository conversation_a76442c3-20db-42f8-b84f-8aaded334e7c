import { Meteor } from 'meteor/meteor';

export const PublicConfigurationConstants = {
    DEBUG_MODE: 'debug',
    COGNITO_USER_POOL_ID: 'cognitoUserPoolId'
}
Object.freeze(PublicConfigurationConstants);

export class PublicConfigurationSettingsService {
    /**
     * Get a server configuration setting.
     *
     * @param {string} setting
     * @returns {*|null}
     */
    static getPublicConfigurationSetting(setting) {
        switch (setting) {
            case PublicConfigurationConstants.DEBUG_MODE:
                return Meteor.settings.debug ?? Meteor.settings.public?.debug ?? false;
            case PublicConfigurationConstants.COGNITO_USER_POOL_ID:
                return Meteor.settings.cognitoUserPoolId;
            default:
                return null;
        }
    }
}