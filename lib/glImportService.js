import Papa from "papaparse";
import moment from "moment";
import {BillingUtils} from "./util/billingUtils";

export class GlImportService {
    static createGlImportCsv(transactions, exportValues) {
        const [exportFiscalYear, exportFiscalPeriod, exportWeekEndingDate, exportJournalNumber] = exportValues;
        const columns = [
            "GL and School Code",
            "Blank",
            "Fiscal Year",
            "Fiscal Period",
            "Week Ending Date",
            "JE",
            "Journal Number",
            "Blank",
            "Blank",
            "School Name",
            "Blank",
            "Blank",
            "Blank",
            "Currency Code",
            "Amount",
            "Blank",
            "Blank",
            "Blank",
            "Company Code"
        ];
        const data = [];
        let manualTotal = 0;
        let filteredTransactions = [];
        for (const t of transactions) {
            if (t.glImportIgnore) {
                continue;
            }
            filteredTransactions.push(t);
        }

        for (const t of filteredTransactions) {
            const out = [
                t.accountName + t.sageLocationCode,
                "",
                exportFiscalYear,
                exportFiscalPeriod,
                new moment(exportWeekEndingDate).format('YYYYMMDD'),
                "JE",
                exportJournalNumber,
                "",
                "",
                t.orgName,
                "",
                "",
                "",
                "USD",
                t.debitAmount > 0 ? BillingUtils.roundToTwo(t.debitAmount) : -1 * BillingUtils.roundToTwo(t.creditAmount),
                "",
                "",
                "",
                "CNI"
            ];
            if (t.accountNameInternal === "accountsReceivable" && t.debitAmount > 0 && t.creditAmount > 0) {
                let newOut = [...out];
                newOut[14] = -1 * t.creditAmount;
                data.push(newOut);
            }
            data.push(out);
        }
        data.unshift([
            "'@HDR'",
            "'BATCH'",
            new moment(exportWeekEndingDate).format('YYYYMMDD'),
            'Revenue Posting for ' + new moment(exportWeekEndingDate).format('DD MMM YYYY')
        ], [
            "'@HDR'",
            "'JOURNAL'",
            "'JE'"
        ]);
        return Papa.unparse({fields: columns, data }, { header: false });
    }
}