import { ReportSaves } from "../collections/reportSaves";

export class ReportSaveService {
    /**
     * Save a report to the ReportSaves collection.
     *
     * @param {Object} reportObject The report object to save.
     * @param {String} index The ID of the report index.
     * @param {String} personId The ID of the person saving the report.
     * @param {String} name The name of the saved report.
     */
    static async saveReport(reportObject, index, personId, name) {
        const existing = await this.getSavedReports(personId);
        const existingNames = existing.map(r => r.name);
        let actualName = null;
        if (!existingNames.includes(name)) {
            actualName = name;
        }
        if (!actualName) {
            for (let suffix = 1; suffix < 1000; suffix++) {
                if (!existingNames.includes(`${name} (${suffix})`)) {
                    actualName = `${name} (${suffix})`;
                    break;
                }
            }
        }
        if (!actualName) {
            // what are we even doing here
            throw new Meteor.Error("Unable to find a unique name");
        }
        await ReportSaves.insertAsync({
            personId,
            name: actualName,
            reportObject,
            index,
            createdOn: new Date()
        });
        return actualName;
    }

    /**
     * get saved reports list
     * @param personId
     * @return {Promise<*>}
     */
    static async getSavedReports(personId) {
        return await (await ReportSaves.aggregate([
            {
                $match: {
                    personId
                }
            },
            {
                $lookup: {
                    from: "people",
                    localField: "personId",
                    foreignField: "_id",
                    as: "person"
                }
            },
            {
                $unwind: "$person"
            },
            {
                $project: {
                    _id: 1,
                    personId: 1,
                    index: 1,
                    createdOn: 1,
                    name: 1,
                    fullName: {
                        $concat: ["$person.firstName", " ", "$person.lastName"]
                    }
                }
            }
        ])).toArray();
    }

    /**
     * Get single saved report with all details
     *
     * @param reportId
     * @return {Promise<*>}
     */
    static async getOneSavedReport(reportId) {
        return await ReportSaves.findOneAsync({_id: reportId});
    }

    static async deleteSavedReport(reportId) {
        return await ReportSaves.removeAsync({_id: reportId});
    }

    static async copySavedReport(reportId, newName) {
        const report = await this.getOneSavedReport(reportId);
        if (!report) {
            throw new Meteor.Error("Report not found");
        }
        return await this.saveReport(report.reportObject, report.index, report.personId, newName);
    }

}