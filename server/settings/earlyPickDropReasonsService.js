import { Orgs } from "../../lib/collections/orgs";

export class EarlyPickDropReasonsService {
   static getReasons(org) {
       const reasons = org?.pickDropReasons || [];
       reasons.sort((a, b) => (a.order || 0) - (b.order || 0));
       return reasons;
   }

   static async setReasons(reasons, orgIds, earlyPickDropMinute) {
       let order = 1;
       for (const reason of reasons) {
           reason.order = order;
           order++;
           if (!reason._id || Number(reason._id) < 0) {
               reason._id = Random.id();
           }
       }
       for (const orgId of orgIds) {
           let orgReasons = await Orgs.findOneAsync({_id: orgId})?.pickDropReasons || [];
           for (const reason of reasons) {
                const existingReason = orgReasons.find(r => r._id === reason._id);
                if (existingReason) {
                    orgReasons = orgReasons.map(r => r._id === reason._id ? reason : r);
                } else {
                    orgReasons.push(reason);
                }
           }
           await Orgs.updateAsync({_id: orgId}, {$set: {pickDropReasons: orgReasons, earlyPickDropMinute: earlyPickDropMinute}});
       }
       return reasons;
   }
}