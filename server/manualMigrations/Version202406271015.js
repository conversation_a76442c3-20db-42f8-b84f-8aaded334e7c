import { ManualMigrations } from "./manualMigrations";
import { ChildcareCrmUtil } from "../childcareCrmUtil";

ManualMigrations.addMigration({
    version: 202406271015,
    description: 'Clean up additional contact links',
    process: async function (orgId = null) {
        const allOrgIds = await ChildcareCrmUtil.getAllChildcareCrmOrgs();
        for (const orgId of allOrgIds) {
            await ChildcareCrmUtil.cleanupOtherContacts(orgId);
        }
    }
});