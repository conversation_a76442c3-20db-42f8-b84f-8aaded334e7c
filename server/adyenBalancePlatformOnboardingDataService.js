import {Log} from "../lib/util/log";
import {Orgs} from '../lib/collections/orgs';

export class AdyenBalancePlatformOnboardingDataService {
	static async saveLegalEntityId(legalEntityId, orgId) {
		Log.info("Saving legal entity ID", {legalEntityId, orgId});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.legalEntityId": legalEntityId,
			}
		});

		Log.info("Legal entity ID saved", {legalEntityId, orgId});

		return legalEntityId;
	}

	static async saveAccountHolderId(accountHolderId, orgId) {
		Log.info("Saving account holder ID", {accountHolderId, orgId});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.accountHolderId": accountHolderId,
				"billing.adyenInfo.accountHolderId": accountHolderId,
			}
		});

		Log.info("Account holder ID saved", {accountHolderId, orgId});

		return accountHolderId;
	}

	static async saveBusinessLineId(businessLineId, orgId) {
		Log.info("Saving business line ID", {businessLineId, orgId});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.businessLineId": businessLineId,
			}
		});

		Log.info("Business line ID saved", {businessLineId, orgId});

		return businessLineId;
	}

	static async saveStoreId(storeId, orgId) {
		Log.info("Saving store ID", {storeId, orgId});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.storeId": storeId,
			}
		});

		Log.info("Store ID saved", {storeId, orgId});

		return storeId;
	}

	static async savePaymentMethods(orgId, paymentMethods) {
		Log.info("Saving payment methods", {paymentMethods, orgId});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.paymentMethods": paymentMethods.map(paymentMethod => ({
					type: paymentMethod.type,
					paymentMethodId: paymentMethod.id,
					enabled: true,
				})),
			}
		});

		Log.info("Payment methods saved", {paymentMethods, orgId});
	}

	static async saveInitiatedState(orgId, initiatedById, initialData) {
		Log.info("Saving initiated state", {orgId, initiatedById, initialData});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.status": 'PendingKYC',
				"billing.adyenBalancePlatformOnboarding.initiatedByUserId": initiatedById,
				"billing.adyenBalancePlatformOnboarding.initiatedAt": new Date(),
				"billing.adyenBalancePlatformOnboarding.initialData": initialData,
			}
		});

		Log.info("Initiated state saved", {orgId, initiatedById, initialData});
	}

	static async updateInitiatedByUserId(orgId, initiatedById) {
		Log.info("Updating initiated by user ID", {orgId, initiatedById});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.initiatedByUserId": initiatedById,
			}
		});

		Log.info("Initiated by user ID updated", {orgId, initiatedById});
	}

	static async getAdyenBillingData(orgId) {
		const org = await Orgs.findOneAsync({_id: orgId});
		const accountHolderId = org?.billing?.adyenInfo?.accountHolderId;
		const onboardingData = org?.billing?.adyenBalancePlatformOnboarding || {};

		return {
			adyenInfo: org.billing?.adyenInfo,
			adyenBalancePlatformOnboarding: {
				...onboardingData,
				// We need to add accountHolderId from adyenInfo to onboardingData
				// As we should use accountHolderId from adyenInfo if it exists
				// It might exist only in adyenInfo in case of migration from classic adyen to adyen balance platform
				accountHolderId: accountHolderId ?? onboardingData.accountHolderId,
			},
			billing: org?.billing,
		};
	}

	static async resetSentKYCFailureEmailFlag(orgId) {
		Log.info("Resetting sent KYC failure email flag", {orgId});

		await Orgs.updateAsync({_id: orgId}, {
			"$set": {
				"billing.adyenBalancePlatformOnboarding.sentKYCFailureEmail": false,
			}
		})

		Log.info("Sent KYC failure email flag reset", {orgId});
	}
}
