import moment from "moment-timezone";
import { Random } from 'meteor/random';
import { BillingUtils } from '../lib/util/billingUtils';
import { PLANS_CHARGED_BY_CHECKINS } from '../lib/constants/billingConstants';
import { DEFAULT_PROGRAM_DAYS } from '../lib/constants/enrollmentConstants';
import { Log } from '../lib/util/log';
import { AllocationAmountTypes, AllocationTypes } from '../lib/discountTypes';
import { People } from "../lib/collections/people";
import { Invoices } from "../lib/collections/invoices";
import { ErrorCodes } from '../lib/constants/errorConstants';
import { Relationships } from "../lib/collections/relationships";
import { Reservations } from "../lib/collections/reservations";
import _ from '../lib/util/underscore';

export class BillingInvoiceService {

    /**
     * Reverses a reallocation by refunding or voiding the associated credit, allocation, and invoice based on the provided options.
     *
     * @param {object} options - The options object containing information for reversing the reallocation.
     * @param {object} options.invoice - The invoice object to reverse the reallocation for.
     * @param {number} options.existingCreditIndex - The index of the existing credit to reverse (optional).
     * @param {number} options.existingAllocationIndex - The index of the existing allocation to reverse (optional).
     * @param {string} options.createdInvoice - The ID of the created invoice to void (optional).
     * @param {string} options.notes - Notes to add for the reversal.
     * @param {string} options.timezone - The timezone to use for date and time operations.
     * @param {object} options.currentUser - The current user object performing the reversal.

     * @returns {void}
     * @throws {Meteor.Error} Throws an error if the invoice is not found, the credit or allocation entry is not found, or if the created invoice cannot be voided.
     */
    static async reverseReallocation(options) {

        const { invoice, existingCreditIndex, existingAllocationIndex, createdInvoice, notes, timezone, currentUser } = options;

        if (!invoice) {
            throw new Meteor.Error(500, "Invoice not found");
        }

        if (existingCreditIndex !== null) {
            // Reverse credit by basically doing a refund
            const creditLine = invoice.credits[existingCreditIndex];
            const allocationLine = invoice.allocationEntries?.find(ae => ae.createdAt === creditLine.createdAt && ae.amount === creditLine.amount);

            if (!creditLine) {
                throw new Meteor.Error(500, "Credit / payment not found.");
            }

            const amountAdjustment = parseFloat(creditLine.amount);
            creditLine.amount = 0;
            creditLine.reversedBy = currentUser.personId;
            creditLine.reversedAt = moment.tz(timezone).valueOf();
            creditLine.reversedNote = notes;
            creditLine.reversedAmount = amountAdjustment;

            const creditKey = `credits.${existingCreditIndex}`;
            const query = {
                $set: {
                    [creditKey]: creditLine
                },
                $inc: {
                    openAmount: amountAdjustment
                }
            };

            if (creditLine.payerDestination) {
                const payerKey = `openPayerAmounts.${creditLine.payerDestination}`;
                query.$inc[payerKey] = amountAdjustment * -1;
            }

            // Reverse allocation item by finding one created at the same time with the same amount.
            if (allocationLine) {
                allocationLine.amount = 0;
                allocationLine.reversedBy = currentUser.personId;
                allocationLine.reversedAt = moment.tz(timezone).valueOf();
                allocationLine.reversedNote = notes;
                allocationLine.reversedAmount = amountAdjustment;

                const allocationKey = `allocationEntries.${invoice.allocationEntries.indexOf(allocationLine)}`;
                query.$set[allocationKey] = allocationLine;
            }
            Log.info('Reversing reallocation', invoice?._id, query);
            await Invoices.updateByIdWithJournalEntry(invoice._id, query, {
                userId: currentUser._id,
                personId: currentUser.personId,
                orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
                reason: `Reversed allocation with amount of $${amountAdjustment} and notes: ${notes} (existingCreditIndex !== null)`,
                reasonLocation: 'server/billingInvoiceService.js:reverseReallocation'
            });
        } else if (existingAllocationIndex !== null) {
            // Reverse allocation items
            const allocationLine = invoice.allocationEntries[existingAllocationIndex];

            if (!allocationLine) {
                throw new Meteor.Error(500, "Allocation entry not found.");
            }

            const amountAdjustment = parseFloat(allocationLine.amount);
            allocationLine.amount = 0;
            allocationLine.reversedBy = currentUser.personId;
            allocationLine.reversedAt = moment.tz(timezone).valueOf();
            allocationLine.reversedNote = notes;
            allocationLine.reversedAmount = amountAdjustment;

            const allocationKey = `allocationEntries.${existingAllocationIndex}`;
            const query = {
                $set: {
                    [allocationKey]: allocationLine
                }
            };

            if (allocationLine.source !== 'family') {
                const payerKey = `openPayerAmounts.${allocationLine.source}`;
                query.$inc = {
                    [payerKey]: amountAdjustment
                };
            }

            Log.info('Reversing reallocation', invoice?._id, query);
            await Invoices.updateByIdWithJournalEntry(invoice._id, query, {
                userId: currentUser._id,
                personId: currentUser.personId,
                orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
                reason: `Reversed allocation with amount of $${amountAdjustment} and notes: ${notes} (existingAllocationIndex !== null)`,
                reasonLocation: 'server/billingInvoiceService.js:reverseReallocation'
            });
        }

        if (createdInvoice) {
            // Void new invoice created by reallocation to family
            const invoice = await Invoices.findOneAsync({ _id: createdInvoice });

            if (!invoice) {
                throw new Meteor.Error(500, "This invoice is not found.");
            }

            if (!invoice.isVoidable()) {
                throw new Meteor.Error(500, "This invoice is not voidable. Invoices that are already voided or have credits applied cannot be voided.");
            }

            Log.info('Voided invoice created by reallocation to family with notes', invoice?._id);
            await Invoices.updateByIdWithJournalEntry(invoice._id, {
                $set: {
                    openAmount: 0.00,
                    discountAmount: 0.00,
                    originalAmount: 0.00,
                    openPayerAmounts: {},
                    voided: true,
                    voidedBy: currentUser.personId,
                    voidedAt: moment.tz(timezone).valueOf()
                }
            }, {
                userId: currentUser._id,
                personId: currentUser.personId,
                orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
                reason: `Voided invoice created by reallocation to family with notes: ${notes}`,
                reasonLocation: 'server/billingInvoiceService.js:reverseReallocation'
            });
        }
    }

    /**
     * Adds a discount or reimbursement to a specific line item in an invoice based on the provided options.
     *
     * @param {object} options - The options object containing information for adding the discount.
     * @param {string} options.allocation_discount_type - The discount type to add (discount) (optional).
     * @param {string} options.allocation_reimbursement_type - The reimbursement type to add (reimbursable) (optional).
     * @param {string} options.invoiceId - The ID of the invoice to which the discount will be applied.
     * @param {string} options.lineItemId - The ID of the line item in the invoice to apply the discount.
     * @param {number} options.discount_amount - The amount of the discount to apply.
     * @param {string} options.discountType - The type of the discount (discount or reimbursable).
     * @param {object} options.currentUser - The current user object making the discount.
     * @param {object} options.org - The organization object for which the invoice belongs to.

     * @returns {void}
     * @throws {Meteor.Error} Throws an error if the discount type is not selected, the invoice is not found or not editable, the line item is not found, the discount amount is less than $0, or if the modified amount reduces the invoice open amount below $0.
     */
    static async addDiscount(options) {
        const { allocation_discount_type, allocation_reimbursement_type, invoiceId, lineItemId, discount_amount, discountType, currentUser, org } = options;

        if (!(allocation_discount_type || allocation_reimbursement_type)) {
            throw new Meteor.Error(500, "You must select a discount type.");
        }

        const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: invoiceId });
        if (!invoice) {
            throw new Meteor.Error(500, "This invoice is not found or is not editable.");
        }

        const selectedLineItem = invoice.lineItems.find((li) => li._id === lineItemId);
        if (!selectedLineItem) {
            throw new Meteor.Error(500, "Cannot find line item or discount.");
        }

        const discountAmount = parseFloat(discount_amount);
        const netChange = -1 * discountAmount;
        const matchedDiscount = discountType === "discount"
            ? org.availableDiscountTypes(false, true).find((dt) => dt.type === allocation_discount_type)
            : org.availablePayerSources().find((ps) => ps.type === allocation_reimbursement_type);
        if (discountAmount < 0) {
            throw new Meteor.Error(500, "Discount amount cannot be less than $0.");
        }

        if (invoice.openAmount + netChange < 0) {
            throw new Meteor.Error(500, "Modified amount cannot reduce invoice open amount below $0.");
        }

        const originalAllocation = {
            allocationType: discountType,
            allocationDescription: discountType.capitalizeFirstLetter() + (matchedDiscount && ": " + matchedDiscount.description),
            amount: discountAmount,
            amountType: "dollars",
            discountType: discountType === "discount" ? allocation_discount_type : allocation_reimbursement_type,
        };

        const appliedDiscount = {
            type: discountType,
            source: discountType === "discount" ? allocation_discount_type : allocation_reimbursement_type,
            amount: discountAmount,
            originalAllocation,
            createdByPersonId: currentUser.personId,
            createdAt: new moment.tz(org.getTimezone()).valueOf()
        };

        const updateQuery = {
            $push: { "lineItems.$.appliedDiscounts": appliedDiscount },
            $inc: {
                openAmount: BillingUtils.roundToTwo(netChange),
                originalAmount: BillingUtils.roundToTwo(netChange),
            }
        };

        if (discountType === "reimbursable") {
            const updatePayerKey = `openPayerAmounts.${allocation_reimbursement_type}`;
            updateQuery.$inc[updatePayerKey] = discountAmount;
        }

        await Invoices.updateByComplexQueryWithJournalEntry({ _id: invoice._id, "lineItems._id": selectedLineItem._id }, updateQuery, {
            userId: currentUser._id,
            personId: currentUser.personId,
            orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
            reason: `Added ${discountType} with amount of $${discountAmount} and description: ${originalAllocation.allocationDescription}`,
            reasonLocation: 'server/billingInvoiceService.js:addDiscount'
        });
    }

    /**
     * Adjusts the payment for an invoice and creates a credit memo if applicable based on the provided options.
     *
     * @param {object} options - The options object containing information for adjusting the payment.
     * @param {object} options.invoice - The invoice object to adjust the payment for.
     * @param {number} options.creditIndex - The index of the credit line to adjust.
     * @param {object} options.selectedInvoice - The selected invoice to apply the payment to (optional).
     * @param {number} options.invoiceAmount - The amount to apply to the selected invoice (optional).
     * @param {number} options.memoAmount - The amount to create a credit memo for (optional).
     * @param {string} options.timezone - The timezone to use for date and time operations.
     * @param {object} options.currentUser - The current user object making the adjustment.

     * @returns {void}
     * @throws {Meteor.Error} Throws an error if there's an issue with invoice or credit memo creation.
     */
    static async adjustPayment(options) {
        const { invoice, creditIndex, selectedInvoice, invoiceAmount, memoAmount, timezone, currentUser } = options;
        const creditLine = invoice.credits[creditIndex];
        let adjustmentNotes = 'Adjusted payment - ';

        if (selectedInvoice && invoiceAmount > 0) {
            // invoice path
            adjustmentNotes += `Applied $${invoiceAmount} to invoice #${selectedInvoice.invoiceNumber}`;

            const newCreditLine = {
                ...creditLine,
                creditReason: 'other',
                creditNote: `Adjusted from invoice #${invoice.invoiceNumber} `,
                amount: invoiceAmount,
                creditedBy: currentUser.personId,
                creditedAt: moment.tz(timezone).valueOf()
            };

            if (newCreditLine.adjustments?.length) {
                delete newCreditLine.adjustments;
            }

            const query = {
                $push: {
                    credits: newCreditLine
                },
                $inc: {
                    openAmount: -invoiceAmount
                }
            };
            try {
                Log.info('adjusted payment', selectedInvoice?._id, query);
                await Invoices.updateByIdWithJournalEntry(selectedInvoice._id, query, {
                    userId: currentUser._id,
                    personId: currentUser.personId,
                    orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
                    reason: `Adjusted payment with amount of $${invoiceAmount} and notes: ${adjustmentNotes}`,
                    reasonLocation: 'server/billingInvoiceService.js:adjustPayment'
                });
            } catch (e) {
                throw new Meteor.Error('500', 'Error adjusting payment', e);
            }
        }

        if (memoAmount > 0) {
            // memo path
            adjustmentNotes += selectedInvoice && invoiceAmount > 0 ? ` and $${memoAmount.toFixed(2)} for credit memo` : `Created credit memo for $${memoAmount.toFixed(2)}`;

            let recipient = null;

            if (creditLine.paidBy) {
                recipient = await People.findOneAsync({ _id: creditLine.paidBy });
            } else {
                const relationships = await Relationships.find({ targetId: invoice.personId, relationshipType: "family" }).fetchAsync();
                const primaryCaregiverRelationship = relationships.find(r => r.relationshipType === 'family' && r.primaryCaregiver);

                if (primaryCaregiverRelationship) {
                    recipient = await People.findOneAsync({ _id: primaryCaregiverRelationship.personId });
                } else {
                    recipient = await People.find({ _id: { $in: relationships }, type: 'family', inActive: false }).fetchAsync()[0];
                }
            }

            if (!recipient) {
                throw new Meteor.Error(500, "No valid credit memo recipient found.");
            }

            const creditMemo = {
                _id: Random.id(),
                type: 'other',
                createdAt: moment.tz(timezone).valueOf(),
                createdBy: currentUser._id,
                notes: `Adjusted from invoice #${invoice.invoiceNumber} `,
                openAmount: parseFloat(memoAmount),
                originalAmount: parseFloat(memoAmount)
            };

            await People.updateAsync({ _id: recipient._id }, {
                $push: {
                    "billing.creditMemos": creditMemo
                }
            });
        }

        // adjust invoice to reflect changes
        const amountAdjustment = invoiceAmount + memoAmount;
        const originalAmount = parseFloat(creditLine.originalAmount ?? creditLine.amount);
        const updatedAdjustments = creditLine.adjustments ? [...creditLine.adjustments] : [];
        updatedAdjustments.push({
            adjustedBy: currentUser.personId,
            adjustedAt: moment.tz(timezone).valueOf(),
            adjustedNote: adjustmentNotes,
            adjustedAmount: amountAdjustment
        });

        const updatedCreditLine = {
            ...creditLine,
            amount: creditLine.originalAmount ? creditLine.amount - amountAdjustment : originalAmount - amountAdjustment,
            originalAmount: originalAmount,
            adjustments: updatedAdjustments
        };

        const creditKey = `credits.${creditIndex}`;
        const query = {
            $set: {
                [creditKey]: updatedCreditLine
            },
            $inc: {
                openAmount: amountAdjustment
            }
        };
        try {
            Log.info('adjusted payment', invoice?._id, query);
             await Invoices.updateByIdWithJournalEntry(invoice._id, query, {
                userId: currentUser._id,
                personId: currentUser.personId,
                orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
                reason: `Adjusted payment with amount of $${amountAdjustment} and notes: ${adjustmentNotes}`,
                reasonLocation: 'server/billingInvoiceService.js:adjustPayment'
            });
        } catch (e) {
            throw new Meteor.Error('500', 'Error adjusting payment', e);
        }
    }

    /**
     * Prorates the adjusted plan for a parent based on the provided options.
     *
     * @param {object} options - The options object containing information for prorating the adjusted plan.
     * @param {Array} options.oldReservations - An array of old reservation objects.
     * @param {Array} options.newReservations - An array of new reservation objects.
     * @param {string} options.personId - The ID of the person (parent) for whom the plan is being prorated.
     * @param {object} options.org - The organization object.
     *
     * @returns {Promise} A Promise that resolves with the prorated amount if it is greater than 0.
     * @throws {Meteor.Error} Throws an error if the required parameters are missing or if there's an error during proration or invoice generation.
     */
    static async prorateParentAdjustedPlan(options) {
        const { oldReservations, newReservations, personId, org, periodStart, periodEnd } = options;
        if (!oldReservations.length || !newReservations.length || !personId || !org) {
            throw new Meteor.Error( "Missing required parameters in prorateParentAdjustedPlan.");
        }
        const child = await People.findOneAsync({_id: personId});

        if (!child) {
            throw new Meteor.Error(500, "Child not found in prorateParentAdjustedPlan.");
        }

        const gracePeriodDays = _.deep(org.billing, "scheduling.gracePeriodDays") || 0;
        const timezone = org.getTimezone();
        const today = moment().tz(org.getTimezone());
        const dueDate = today.clone().add(gracePeriodDays, 'days');
        const proratePlanAmounts = [];
        const planIds = [];
        const createdAts = [];

        Log.info('oldReservations in prorateParentAdjustedPlan:', oldReservations);
        Log.info('newReservations in prorateParentAdjustedPlan:', newReservations);
        for (let i = 0; i < oldReservations.length; i++) {
            // Find what was previously paid for the old plan.
            const oldPlan = await (await Reservations.findOneAsync({_id: oldReservations[i].reservationId})).linkedPlan();
            const adjustmentsMade = this.getAdjustmentsMade(child, periodStart, periodEnd, oldPlan);

            if (adjustmentsMade.cumulativePaidAmount > 0) {
                // If this isn't the first adjustment for this plan in this billing cycle the cumulativePaidAmount will be greater than 0
                // this means we don't really have to go searching for invoices.
                oldPlan.calculatedAmount = adjustmentsMade.cumulativePaidAmount;
                oldPlan.calculatedDiscountAmount = 0; // This way we maintain the records of the allocations on this plan, but we won't double-count them.
            } else {
                const previousInvoiceLineItem = await this.findPreviousInvoiceLineItem(oldPlan, org, personId, periodStart);
                if (previousInvoiceLineItem) {
                    oldPlan.calculatedAmount = previousInvoiceLineItem.amount;  // Set old plan amount to what was previously invoiced in case it was previously prorated
                    if (previousInvoiceLineItem.appliedDiscounts?.length) {
                        oldPlan.calculatedDiscountAmount = previousInvoiceLineItem.appliedDiscounts.reduce((acc, discount) => acc + discount.amount, 0);
                    }
                } else {
                    // If we can't find the previous invoice line item, we can't prorate off the old plan so set the amount to 0
                    // It's likely the relevant invoice was voided or issued in a different billing period.
                    oldPlan.calculatedAmount = 0;
                    oldPlan.calculatedDiscountAmount = 0;
                }
            }

            // Prorate new plan by date if necessary.
            const newPLan = await (await Reservations.findOneAsync({_id: newReservations[i].reservationId})).linkedPlan();
            this.proratePlanByDate(newPLan, org, moment.tz(periodStart, 'MM/DD/YYYY', timezone).valueOf(), moment.tz(periodEnd, 'MM/DD/YYYY', timezone).valueOf(), child, false);

            // Attach the linked reservations to the plans so that we have an easily accessible reference to the reservations
            oldPlan.linkedReservation = oldReservations[i];
            newPLan.linkedReservation = newReservations[i];

            try {
                const prorationResults = await BillingUtils.getProratedAmountForDIYAdjustments(oldPlan, newPLan, org, periodStart, periodEnd);

                if (prorationResults.amountToProrate > 0) { // Don't add plan to invoice list if there's nothing to prorate.
                    planIds.push(newPLan._id);
                    createdAts.push(newPLan.createdAt);
                    proratePlanAmounts.push({
                        planUid: newPLan.uid,
                        amount: prorationResults.amountToProrate
                    });
                }

                // Update the adjustments made for the child and save the previous invoiced amount for the old plan
                this.addPlanChange(adjustmentsMade, oldPlan, newPLan, prorationResults);

                // Update the child's adjustments in the database
                await this.updateChildAdjustments(child, adjustmentsMade);
            } catch (error) {
                Log.error('Error prorating parent adjusted plan', error);
                if (error.reason) {
                    throw new Meteor.Error(error.error, error.reason, error.details || '');
                } else {
                    throw new Meteor.Error('Error', error.message, '');
                }
            }
        }

        if (proratePlanAmounts.length) {
            const invoiceData = {
                plan_ids: planIds[0],
                period_start_date: today.format('MM/DD/YYYY'),
                invoice_due_date: dueDate.format('MM/DD/YYYY'),
                planIds,
                createdAts,
                includePendingItems: false,
                personId,
                periodStartDate: periodStart || today.format('MM/DD/YYYY'),
                invoiceDueDate: dueDate.format('MM/DD/YYYY'),
                parentAdjusted: true,
                proratePlanAmounts,
                isSystem: true
            }

            try {
                await Meteor.callAsync('sendManualPlanInvoice', invoiceData);
            } catch (error) {
                throw new Meteor.Error(500, error.reason)
            }
        }
        return proratePlanAmounts.reduce((acc, plan) => acc + plan.amount, 0);
    }

    /**
     * Prorates a plan based on a percentage calculated from dates.
     * (Follows what is done in invoicePerson.js)
     *
     * @param {Object} plan - The plan object to prorate.
     * @param {Object} org - The organization object.
     * @param {number} periodStartDate - The start date of the period (timestamp).
     * @param {number} periodEndDate - The end date of the period (timestamp).
     * @param {Object} child - The child object.
     * @param {boolean} isFromInvoiceGeneration - Flag to determine if this is for invoice generation.
     */
    static proratePlanByDate(plan, org, periodStartDate, periodEndDate, child, isFromInvoiceGeneration = false) {
        const timezone = org.getTimezone();

        // Get the appropriate enrollment and expiration dates based on the context (invoice generation or normal)
        let enrolledStartTime, enrolledEndTime;

        if (isFromInvoiceGeneration) {
            enrolledStartTime = plan.enrolledPlan.enrollmentDate > periodStartDate ? plan.enrolledPlan.enrollmentDate : plan.periodStartDate;
            enrolledEndTime = plan.enrolledPlan.expirationDate && plan.enrolledPlan.expirationDate <= periodEndDate ? plan.enrolledPlan.expirationDate : periodEndDate;
        } else {
            const planStartDateTimestamp = plan.enrollmentDate ? plan.enrollmentDate : moment.tz(plan.startDate, 'MM/DD/YYYY', timezone).startOf('day').valueOf();
            enrolledStartTime = planStartDateTimestamp > periodStartDate ? planStartDateTimestamp : periodStartDate;
            enrolledEndTime = plan.expirationDate <= periodEndDate ? plan.expirationDate : periodEndDate;
        }

        // Check if the expiration date occurs before the enrollment date
        if (enrolledEndTime < enrolledStartTime) {
            // If expiration is before enrollment, no prorating should be done
            plan.proratedAmount = 0;
            plan.amount = 0;
            plan.calculatedAmount = 0;

            const allocations = (isFromInvoiceGeneration ? plan.enrolledPlan?.allocations : plan.allocations) ?? [];

            for (const allocation of allocations) {
                allocation.amount = 0;
            }

            return;
        }

        // Get the period start and end dates for normal calculation
        const periodDates = BillingUtils.getPeriodByEffectiveDate(
            moment.tz(periodStartDate, timezone).format('MM/DD/YYYY'),
            plan.frequency || plan.masterPlanInfo.frequency,
            org,
            child,
            true
        );

        const normalStartTime = moment.tz(periodDates.start, 'MM/DD/YYYY', timezone).startOf('day').valueOf();
        const normalEndTime = moment.tz(periodDates.end, 'MM/DD/YYYY', timezone).startOf('day').valueOf();

        const programOfferedOn = plan.programOfferedOn || plan.masterPlanInfo?.programOfferedOn || DEFAULT_PROGRAM_DAYS;
        const normalOpenDays = org.getOpenDaysForPeriod(normalStartTime, normalEndTime, programOfferedOn);
        const enrolledOpenDays = org.getOpenDaysForPeriod(enrolledStartTime, enrolledEndTime, programOfferedOn);

        let amount = plan.amount || plan.calculatedAmount;
        const isDailyPlan = PLANS_CHARGED_BY_CHECKINS.includes(plan.frequency);

        if (!isDailyPlan) {
            amount = BillingUtils.roundToTwo(amount / normalOpenDays * enrolledOpenDays);
        }

        plan.proratedAmount = amount;
        if (plan.amount) {
            plan.amount = amount;
        } else if (plan.calculatedAmount) {
            plan.calculatedAmount = amount;
        }
        plan.proratedByDate = true;

        const allocations = (isFromInvoiceGeneration ? plan.enrolledPlan?.allocations : plan.allocations) ?? [];

        for (const allocation of allocations) {
            if (allocation.amountType === AllocationAmountTypes.DOLLARS) {
                let allocationOpenDays = enrolledOpenDays;
                if ((allocation.payerStartDate && allocation.payerStartDate > enrolledStartTime) || (allocation.payerEndDate && allocation.payerEndDate < enrolledEndTime)) {
                    allocationOpenDays = org.getOpenDaysForPeriod(
                        (allocation.payerStartDate ?? enrolledStartTime) > enrolledStartTime ? allocation.payerStartDate : enrolledStartTime,
                        (allocation.payerEndDate ?? enrolledEndTime) < enrolledEndTime ? allocation.payerEndDate : enrolledEndTime,
                        programOfferedOn
                    );
                }
                allocation.amount = BillingUtils.roundToTwo(allocation.amount / normalOpenDays * allocationOpenDays);
            }

            if (allocation.amount === 0 && allocation.allocationType === AllocationTypes.COPAY) {
                const index = allocations.indexOf(allocation);
                if (index > -1) {
                    allocations.splice(index, 1);
                }
            }
        }
    }

    /**
     * Prorate a plan and its discounts by a percentage amount.
     *
     * @param enrolledPlan
     * @param percentage
     */
    static proratePlanByPercentage(enrolledPlan, percentage) {
        // TODO look into this for scaled plans
        enrolledPlan.amount = BillingUtils.roundToTwo(enrolledPlan.amount * percentage);
        enrolledPlan.proratedByPercent = true;

        for (const allocation of enrolledPlan.enrolledPlan.allocations) {
            if (allocation.amountType === 'dollars') {
                allocation.amount = BillingUtils.roundToTwo(allocation.amount * percentage);
            }
        }
    }

    static proratePlanByAmount(enrolledPlan, amount) {
        // just charge a flat amount and zero out allocations for ease
        enrolledPlan.amount = amount;
        enrolledPlan.proratedByAmount = true;

        if (enrolledPlan.proratedByPercent) {
            delete enrolledPlan.proratedByPercent;
        }

        if (enrolledPlan.proratedByDate) {
            delete enrolledPlan.prorateStartDate;
            delete enrolledPlan.proratedByDate;
            delete enrolledPlan.proratedAmount;
        }

        for (const allocation of enrolledPlan.enrolledPlan.allocations) {
            if (allocation.amountType === 'dollars') {
                allocation.amount = 0;
            }
        }
    }

    /**
     * Finds the previous invoice containing the specified plan within its line items.
     *
     * @param {Object} plan - The plan object to find in the invoice line items.
     * @param {Object} org - The organization object containing org details.
     * @param {string} personId - The ID of the person related to the invoice.
     * @param {string} periodStart - The start date of the billing cycle (MM/DD/YYYY).
     * @returns {Object|null} The matching invoice line item if found, otherwise null.
     */
    static async findPreviousInvoiceLineItem(plan, org, personId, periodStart) {
        const timezone = org.getTimezone();
        // Get all the child's non-voided invoices and sort by newest first.
        const invoices = await Invoices.find({personId, orgId: org._id, voided: {$ne: true}}, {sort: {createdAt: -1}}).fetchAsync();
        // Iterate over each invoice to find the line item that matches the plan
        for (const invoice of invoices) {
            // Iterate over each line item in the invoice
            for (const lineItem of invoice.lineItems) {
                const lineItemPeriodStart = moment.tz(lineItem.periodStartDateSupplied || lineItem.periodStartDate, timezone).format('MM/DD/YYYY');

                if (lineItemPeriodStart !== periodStart) {
                    continue; // Skip line items that don't match the period start date
                }

                // Check if the line item has the enrolledPlan object and matches by UID first
                if (
                    lineItem.enrolledPlan &&
                    ((plan.uid && lineItem.enrolledPlan.uid === plan.uid) || // Match by UID if available
                        (lineItem.enrolledPlan._id === plan._id && // Match by _id and createdAt as fallback
                            lineItem.enrolledPlan.createdAt === plan.createdAt))
                ) {
                    return lineItem; // Return the invoice line item if a matching line item is found
                }
            }
        }

        // Return null if no matching invoice is found
        return null;
    }

    /**
     * Retrieves an existing adjustment record for the given child based on the billing cycle and plan ID,
     * or creates a new adjustment record if none exists. Tracks the history of changes to the plan.
     *
     * @param {object} child - The child record.
     * @param {number} periodStart - The start date of the billing cycle (timestamp).
     * @param {number} periodEnd - The end date of the billing cycle (timestamp).
     * @param {Object} plan - The plan object associated with the adjustment (containing _id).
     * @returns {Object} The existing or newly created adjustment record.
     */
    static getAdjustmentsMade(child, periodStart, periodEnd, plan) {
        if (!child || !periodStart || !periodEnd || !plan) {
            throw new Meteor.Error(500, "Missing required parameters in getAdjustmentsMade.");
        }

        // Ensure the child has a billing property with an adjustments array; initialize if not present
        if (!child.billing) {
            child.billing = {};
        }

        if (!child.billing.adjustments) {
            child.billing.adjustments = [];
        }

        // Try to find an existing adjustment record based on periodStart, periodEnd, and plan._id
        let adjustment = child.billing.adjustments.find(
            (adj) => adj.periodStart === periodStart && adj.periodEnd === periodEnd && adj.planId === plan._id
        );

        // Check if the plan is part of a bundle
        const bundleId = plan.bundlePlanId;

        // If no existing record is found, create a new one
        if (!adjustment) {
            adjustment = {
                periodStart: periodStart,
                periodEnd: periodEnd,
                planId: plan._id,
                cumulativePaidAmount: 0, // Initialize the cumulative paid amount to zero or appropriate starting value
                planChanges: [], // Initialize an empty array to track all changes to the plan
            };

            if (bundleId) {
                adjustment.bundlePlanId = bundleId;
            }

            // Add the new adjustment record to the child's billing adjustments array
            child.billing.adjustments.push(adjustment);
        }

        return adjustment;
    }

    /**
     * Adds a plan change record to the adjustment, detailing the modification.
     *
     * @param {Object} adjustment - The adjustment record to update.
     * @param {Object} oldSchedule - The old schedule object before the change.
     * @param {Object} newSchedule - The new schedule object after the change.
     * @param {object} prorationResults - Object that contains the prorated amount and other proration results.
     */
    static addPlanChange(adjustment, oldSchedule, newSchedule, prorationResults) {
        const changeRecord = {
            timestamp: Date.now(), // Record when the change was made
            oldSchedule,
            newSchedule,
            amountPaidForThisChange: prorationResults.amountToProrate, // Amount paid for this change
            amountPaidPreviously: prorationResults.oldPlanAmount, // Amount paid on the previous changes
            cumulativePaidAfterChange: prorationResults.oldPlanAmount + prorationResults.amountToProrate, // Calculate running total after this change
        };

        // Add the change record to the planChanges array
        adjustment.planChanges.push(changeRecord);

        // Update the cumulative paid amount to reflect the change
        adjustment.cumulativePaidAmount = changeRecord.cumulativePaidAfterChange;
    }

    /**
     * Updates the child's adjustments in the MongoDB document with the updated adjustment object.
     *
     * @param {object} child - The child object to update the adjustments for.
     * @param {Object} adjustment - The updated adjustment object to be saved to the database.
     */
    static async updateChildAdjustments(child, adjustment) {
        if (!child || !adjustment) {
            throw new Meteor.Error(500, "Missing required parameters in updateChildAdjustments.");
        }

        try {
            // Ensure the billing and adjustments array exist; initialize if not present
            if (!child.billing) {
                child.billing = {};
            }
            if (!child.billing.adjustments) {
                child.billing.adjustments = [];
            }

            // Find the index of the adjustment in the child's billing adjustments array
            const adjustmentIndex = child.billing.adjustments.findIndex(
                (adj) =>
                    adj.periodStart === adjustment.periodStart &&
                    adj.periodEnd === adjustment.periodEnd &&
                    adj.planId === adjustment.planId
            );

            // If the adjustment exists, update it; otherwise, add it as a new adjustment
            if (adjustmentIndex !== -1) {
                child.billing.adjustments[adjustmentIndex] = adjustment;
            } else {
                child.billing.adjustments.push(adjustment);
            }

            // Update the child's document in MongoDB
            await People.updateAsync(
                { _id: child._id }, // Find the child by its _id
                { $set: { 'billing.adjustments': child.billing.adjustments } } // Update the billing adjustments array
            );

            Log.info(`Child adjustments updated successfully for childId: ${child._id}`);
        } catch (error) {
            Log.error(`Failed to update adjustments for childId: ${child._id}`, error);
        }
    }

    /**
     * Void a discount as part of the invoice voiding process.
     *
     * @param invoice
     * @returns {Promise<void>}
     */
    static async voidDiscountsInInvoice(invoice) {
        for (const [lineItemIndex, lineItem] of (invoice.lineItems ?? []).entries()) {
            for (const [index, discount] of (lineItem.appliedDiscounts ?? []).entries()) {
                try {
                    await Meteor.callAsync("modifyDiscount", {
                        invoiceId: invoice._id,
                        lineItemId: lineItem._id,
                        lineItemIndex: lineItemIndex,
                        discountId: index,
                        void: true,
                        skipCustomizationCheck: true
                    });
                } catch (e) {
                    throw new Meteor.Error(e.error || ErrorCodes.UNKNOWN, e.reason || e.message);
                }
            }
        }
    }
}