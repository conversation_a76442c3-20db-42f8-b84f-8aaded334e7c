export class AdyenServiceError extends Error {
	name
	responseBody
	invalidFields
	initialError

	constructor(error) {
		// Attempt to parse the Adyen error response body.
		let parsedBody
		try {
			parsedBody = JSON.parse(error.responseBody || "{}")
			const errorMessage = `Details: ${parsedBody.errorCode} ${parsedBody.title} ${parsedBody.detail ? `(${parsedBody.detail})` : ""}`

			super(errorMessage)
		} catch {
			parsedBody = {message: "Unknown Adyen service error"}
			super(`Details: Unknown Adyen service error - ${error.message}`)
		}
		this.name = "AdyenServiceError"
		this.responseBody = parsedBody
		this.invalidFields = parsedBody.invalidFields
		this.initialError = error
		Object.setPrototypeOf(this, AdyenServiceError.prototype)
	}
}
