import {ConfigurationSettingsService, ServerConfigurationConstants} from "../config/configurationSettingsService";
import {formatShopperStatement} from "./utils";
import {AdyenServiceError} from "./adyenServiceError";
import {Log} from "../../lib/util/log";
import {
	BalancePlatformAPI,
	CheckoutAPI,
	Client,
	Config,
	LegalEntityManagementAPI,
	ManagementAPI
} from "@adyen/api-library";
import _ from "../../lib/util/underscore";

import {Orgs} from "../../lib/collections/orgs";

function roundToTwo(num) {
	return +(Math.round(num + "e+2")  + "e-2");
}

export class AdyenBalancePlatformProvider {
	/**
	 * Get the balance platform API key.
	 *
	 * @returns {string}
	 */
	static getBalancePlatformApiKey() {
		return ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_BALANCE_PLATFORM_API_KEY);
	}

	static getLegalEntityManagementApiKey() {
		return ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_LEGAL_ENTITY_MANAGEMENT_API_KEY);
	}

	static getManagementApiKey() {
		return ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_API_KEY);
	}

	static getAdyenAccountId() {
		return ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_ACCOUNT_ID);
	}

	static getManagementClient() {
		/**
		 * Management Client
		 */
		const managementApiKey = AdyenBalancePlatformProvider.getManagementApiKey();
		const liveEndpointUrlPrefix = _.deep(Meteor.settings, "public.adyen.environmentPrefix");
		const environment = _.deep(Meteor.settings, "public.adyen.environment") || "TEST";
		const serverEnvironmentKey = environment.startsWith("live") ? "LIVE" : "TEST";

		const managementClient = new Client({
			apiKey: managementApiKey,
			environment: serverEnvironmentKey,
			liveEndpointUrlPrefix
		})

		return new ManagementAPI(managementClient);
	}

	static getBalancePlatformClient() {
		/**
		 * Balance Platform Client
		 */
		const balancePlatformApiKey = AdyenBalancePlatformProvider.getBalancePlatformApiKey();
		const liveEndpointUrlPrefix = _.deep(Meteor.settings, "public.adyen.environmentPrefix");
		const environment = _.deep(Meteor.settings, "public.adyen.environment") || "TEST";
		const serverEnvironmentKey = environment.startsWith("live") ? "LIVE" : "TEST";

		Log.info("Balance platform client created ");
		const platformClient = new Client({
			config: new Config({
				apiKey: balancePlatformApiKey,
				environment: serverEnvironmentKey,
			}),
		})
		platformClient.setEnvironment(
			serverEnvironmentKey,
			liveEndpointUrlPrefix
		)

		return new BalancePlatformAPI(platformClient);
	}

	static getLegalEntityManagementClient() {
		const legalEntityManagementApiKey = AdyenBalancePlatformProvider.getLegalEntityManagementApiKey();
		const liveEndpointUrlPrefix = _.deep(Meteor.settings, "public.adyen.environmentPrefix");
		const environment = _.deep(Meteor.settings, "public.adyen.environment") || "TEST";
		const serverEnvironmentKey = environment.startsWith("live") ? "LIVE" : "TEST";


		const client = new Client({
			apiKey: legalEntityManagementApiKey,
			environment: serverEnvironmentKey,
			liveEndpointUrlPrefix
		})

		return new LegalEntityManagementAPI(client);
	}

	/*
	* Create a legal entity.
	* @param {object} legalEntityData - The legal entity data.
	* @param {string} legalEntityData.legalName - The legal name of the entity.
	* @param {string} legalEntityData.registeredAddress.country - The two-letter ISO 3166-1 alpha-2 country code.
	*/
	static async createLegalEntity(legalEntityData) {
		try {
			Log.info("Create legal entity request", legalEntityData);

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			const legalEntity = await legalEntityManagementClient.LegalEntitiesApi.createLegalEntity(legalEntityData);

			Log.info("Legal entity created", {legalEntityId: legalEntity.id});

			return legalEntity;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to create legal entity with an error", adyenError);
			throw adyenError;
		}
	}

		/*
		* Create a legal entity.
		* @param {object} legalEntityData - The legal entity data.
		* @param {string} legalEntityData.legalName - The legal name of the entity.
		* @param {string} legalEntityData.registeredAddress.country - The two-letter ISO 3166-1 alpha-2 country code.
		*/
	static async getLegalEntity(legalEntityId) {
		try {
			Log.info("Get legal entity request", legalEntityId);

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			return await legalEntityManagementClient.LegalEntitiesApi.getLegalEntity(legalEntityId);
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to get legal entity with an error", adyenError);
			throw adyenError;
		}
	}

	/*
	* Create a legal entity.
	* @param {string} legalEntityId - Legal entity ID.
	* @param {object} legalEntityData - The legal entity data.
	* @param {string} legalEntityData.legalName - The legal name of the entity.
	* @param {string} legalEntityData.registeredAddress.country - The two-letter ISO 3166-1 alpha-2 country code.
	*/
	static async updateLegalEntity(legalEntityId, legalEntityData) {
		try {
			Log.info("Update legal entity request", {
				legalEntityId,
				legalEntityData,
			});

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			const legalEntity = await legalEntityManagementClient.LegalEntitiesApi.updateLegalEntity(legalEntityId, legalEntityData);

			Log.info("Legal entity update", {legalEntityId: legalEntity.id});

			return legalEntity;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to update legal entity with an error", adyenError);
			throw adyenError;
		}
	}

	/*
	* Create an account holder.
	* @param {object} accountHolderData - The account holder data.
	* @param {string} accountHolderData.legalEntityId - The legal entity ID.
	* @param {string} accountHolderData.reference - The reference to the account holder.
	* @returns {object} accountHolder - The account holder object.
	 */
	static async createAccountHolder(accountHolderData) {
		try {
			Log.info("Create account holder request", accountHolderData);

			const balancePlatformClient = AdyenBalancePlatformProvider.getBalancePlatformClient();

			const accountHolder = await balancePlatformClient.AccountHoldersApi.createAccountHolder(accountHolderData);

			Log.info("Account holder created", {accountHolderId: accountHolder.id});

			return accountHolder;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to create account holder with an error", adyenError);
			throw adyenError;
		}
	}

	/*
	* Update an account holder.
	* @param {string} accountHolderId - The account holder ID.
	* @param {object} accountHolderData - The account holder data.
	* @param {string} accountHolderData.legalEntityId - The legal entity ID.
	* @param {string} accountHolderData.reference - The reference to the account holder.
	* @param {{ [key: string]: AccountHolderCapability }} accountHolderData.capabilities - The capabilities of the account holder.
	* @returns {object} accountHolder - The account holder object.
	 */
	static async updateAccountHolder(accountHolderId, accountHolderData) {
		try {
			Log.info("Update account holder request", {
				accountHolderId,
				accountHolderData,
			});

			const balancePlatformClient = AdyenBalancePlatformProvider.getBalancePlatformClient();

			const accountHolder = await balancePlatformClient.AccountHoldersApi.updateAccountHolder(accountHolderId, accountHolderData);

			Log.info("Account holder updated", {accountHolderId: accountHolder.id});

			return accountHolder;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to update account holder with an error", adyenError);
			throw adyenError;
		}
	}

	/*
	* Get an account holder.
	* @param {string} accountHolderId - The account holder ID.
	* @returns {object} accountHolder - The account holder object.
	 */
	static async getAccountHolder(accountHolderId) {
		try {
			Log.info("Get account holder request", {accountHolderId});

			const balancePlatformClient = AdyenBalancePlatformProvider.getBalancePlatformClient();

			const accountHolder = await balancePlatformClient.AccountHoldersApi.getAccountHolder(accountHolderId);

			Log.info("Account holder retrieved", {accountHolderId});

			return accountHolder;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to retrieve account holder with an error", adyenError);
			throw adyenError;
		}
	}

	static async createBusinessLine(businessLineData) {
		try {
			Log.info("Create business line request", businessLineData);

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			const businessLine = await legalEntityManagementClient.BusinessLinesApi.createBusinessLine(businessLineData);

			Log.info("Business line created", {businessLineId: businessLine.id});

			return businessLine;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to create business line with an error", adyenError);

			throw adyenError;
		}
	}

	static async retrieveLegalEntityFromAccountHolder(accountHolderCode) {
		const accountHolder = await this.getAccountHolder(accountHolderCode);
		const legalEntityId = accountHolder?.legalEntityId;
		if(!legalEntityId) {
			throw new Meteor.Error(500, "Legal entity not found for account holder");
		}
		return await this.getLegalEntity(legalEntityId);
	}
	static async getIndividualLegalEntityEmail(legalEntity) {
		let {entityAssociations} = legalEntity;
		if(!entityAssociations) {
			throw new Meteor.Error(500, "Legal entity has no entity associations");
		}
		entityAssociations = Array.from(new Set(entityAssociations
			.filter((item) => item.entityType === 'individual')
			.map((item) => item.legalEntityId)))
			.map(legalEntityId => entityAssociations.find((item) => item.legalEntityId === legalEntityId));

		// Only uses the first individual legal entity available on the entityAssociations list
		const individualLegalEntityId = entityAssociations[0].legalEntityId;
		const {individual} = await AdyenBalancePlatformProvider.getLegalEntity(individualLegalEntityId)

		let {email} = individual
		if (!email) {
			throw new Meteor.Error(500, "Individual legal entity has no email");
			return;
		}
		return email;
	}

	static async updateBusinessLine(businessLineId, businessLineData) {
		try {
			Log.info("Update business line request", {
				businessLineId,
				businessLineData,
			});

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			const businessLine = await legalEntityManagementClient.BusinessLinesApi.updateBusinessLine(businessLineId, businessLineData);

			Log.info("Business line updated", {businessLineId: businessLine.id});

			return businessLine;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to update business line with an error", adyenError);

			throw adyenError;
		}
	}

	static getAllBusinessLinesOfLegalEntity(legalEntityId) {
		try {
			Log.info("Get all business lines of legal entity request", {legalEntityId});

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			return legalEntityManagementClient.LegalEntitiesApi.getAllBusinessLinesUnderLegalEntity(legalEntityId);
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to get all business lines of legal entity with an error", adyenError);

			throw adyenError;
		}
	}

	static async createStore(storeData) {
		try {
			Log.info("Create store request", storeData);

			const managementClient = AdyenBalancePlatformProvider.getManagementClient();

			const store = await managementClient.AccountStoreLevelApi.createStore({
				...storeData,
				shopperStatement: formatShopperStatement(storeData.legalName),
				merchantId: AdyenBalancePlatformProvider.getAdyenAccountId()
			});

			Log.info("Store created", {storeCode: store.id});

			return store;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to create store with an error", adyenError);
			throw adyenError;
		}
	}

	static async updateStore(storeId, storeData) {
		try {
			Log.info("Update store request", {
				storeId,
				storeData,
			});

			const managementClient = AdyenBalancePlatformProvider.getManagementClient();

			const store = await managementClient.AccountStoreLevelApi.updateStoreById(storeId, {
				...storeData,
				shopperStatement: formatShopperStatement(storeData.legalName),
				merchantId: AdyenBalancePlatformProvider.getAdyenAccountId()
			});

			Log.info("Store updated", {storeCode: store.id});

			return store;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to update store with an error", adyenError);
			throw adyenError;
		}
	}

	static async getBusinessLinePaymentMethods(storeId, businessLineId) {
		try {
			Log.info("Get business line payment methods request", {storeId, businessLineId});

			const managementClient = AdyenBalancePlatformProvider.getManagementClient();
			const adyenAccountId = AdyenBalancePlatformProvider.getAdyenAccountId();
			const paymentMethods = await managementClient.PaymentMethodsMerchantLevelApi.getAllPaymentMethods(adyenAccountId, storeId, businessLineId);

			Log.info("Business line payment methods retrieved");

			return paymentMethods;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			Log.error("Failed to retrieve business line payment methods with an error", adyenError);

			return adyenError;
		}
	}

	static async enablePaymentMethod(paymentMethodData) {
		try {
			Log.info("Request payment method request", paymentMethodData);

			const managementClient = AdyenBalancePlatformProvider.getManagementClient();
			const adyenAccountId = AdyenBalancePlatformProvider.getAdyenAccountId();
			const paymentMethod = await managementClient.PaymentMethodsMerchantLevelApi.requestPaymentMethod(adyenAccountId, paymentMethodData);

			Log.info("Payment method requested", {paymentMethodCode: paymentMethod.id});

			return paymentMethod;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);
			if (adyenError.responseBody.detail === "Payment method already configured") {
				Log.info("Payment method already configured", {paymentMethodData});
				throw new Error("PAYMENT_METHOD_ALREADY_CONFIGURED");
			}

			Log.error("Failed to request payment method with an error", adyenError);
			throw adyenError;
		}
	}

	static async createHostedOnboardingLink(legalEntityId, onboardingLinkInfo) {
		try {
			Log.info("Create hosted onboarding link request");

			const legalEntityManagementClient = AdyenBalancePlatformProvider.getLegalEntityManagementClient();

			const hostedOnboardingLink = await legalEntityManagementClient.HostedOnboardingApi.getLinkToAdyenhostedOnboardingPage(legalEntityId, onboardingLinkInfo);

			Log.info("Hosted onboarding link created");

			return hostedOnboardingLink.url;
		} catch (error) {
			const adyenError = new AdyenServiceError(error);

			Log.error("Failed to create hosted onboarding link with an error", adyenError);
			throw adyenError;
		}
	}

    static async getBalanceAccount(balanceAccountId) {
        try {
            const balancePlatformClient = AdyenBalancePlatformProvider.getBalancePlatformClient();

            return await balancePlatformClient.BalanceAccountsApi.getBalanceAccount(balanceAccountId)


        } catch (error) {
            const adyenError = new AdyenServiceError(error);

            Log.error('Failed to retrieve balance account in Adyen', adyenError);
            throw error;
        }
    }
}
