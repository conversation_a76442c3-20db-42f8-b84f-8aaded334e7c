import { Orgs } from "../lib/collections/orgs";
import { SSR } from "../lib/util/ssrUtils";

export const processOnboardingVerificationCodeEmail = async function (userId) {
	if (Meteor.isServer) {
		SSR.compileTemplate('verificationCodeEmail', await Assets.getTextAsync('email_templates/onboarding_verification_code_email.html'));

		const currentUser = await Meteor.users.findOneAsync(userId);
		var org = await Orgs.findOneAsync(currentUser.orgId);

		var baseUrl = (org && org.whiteLabel && org.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
		if (baseUrl.slice(baseUrl.length-1) != "/") baseUrl+= "/";
		var emailData = {
			activationCode: currentUser.activationCode.split(""),
			activationUrl: baseUrl + "getting-started/verify?code=" + currentUser.activationCode,
			recipientEmail: currentUser.emails[0].address
		};

		const emailOptions = {
			from: "LineLeader Support <<EMAIL>>",
			to: currentUser.emails[0].address,
			subject: "Your MomentPath Activation Code",
			html: SSR.render('verificationCodeEmail', emailData)
		};
		try {
			await Email.sendAsync(emailOptions);
		} catch(e) {
			console.log(e);
		}
	}
};
