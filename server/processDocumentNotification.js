import { SSR } from "../lib/util/ssrUtils";
import { Blaze } from "meteor/blaze";
import { generateEmailWhiteLabelData } from "./util";
import { Orgs } from "../lib/collections/orgs";
import moment from 'moment-timezone';

export const processDocumentNotification = async (options) => {
	const currentRecipient = options.targetPerson;

	const userEmails = await currentRecipient.getActiveUserEmailAddress()
	if ( userEmails ) {
		console.log("processing document notification email for recipient");
		SSR.compileTemplate('messageEmail', await Assets.getTextAsync('email_templates/v2021/document_email.html'));

		const org = await currentRecipient.findOrg();
		Blaze.Template.messageEmail.helpers({
			"equals": function(val1, val2) {
				return (val1 === val2) ? true : false;
			},
			"formatTime": function(val) {
				return (val.toUpperCase());
			},
			"appUrl": function(){
				var baseUrl = (org && org.whiteLabel && org.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
				  if (baseUrl.slice(baseUrl.length-1) != "/") baseUrl+= "/";
				return baseUrl;
			}
		});

		let currentMessage = "";
		switch( options.type) {
			case "reject":
				currentMessage = "Your document submission for '" + options.documentDefinition.name + "' has been rejected. ";
				if (options.additionalNotes) currentMessage += " The following notes were included: '" + options.additionalNotes + "'. ";
				currentMessage += " Please contact your provider for any additional details.";
				break;
		}

		var emailData = {
			message: currentMessage,
			person: currentRecipient,
			recipientId: currentRecipient._id,
			emailDateStamp: new moment().tz(org.getTimezone()).format("dddd MMMM Do, YYYY")
		};

		const currentOrgId = currentRecipient.orgId, currentOrg = await Orgs.findOneAsync(currentOrgId);
		const whiteLabel = generateEmailWhiteLabelData(currentOrg);
		emailData.whiteLabel = whiteLabel
		emailData.backgroundColor = `${whiteLabel.primaryColor}1A`;
		emailData.headerOrgNameColor = "#8E8E93";
		emailData.headerBgColor = whiteLabel.primaryColor;
		emailData.secondaryColor = whiteLabel.secondaryColor;
		emailData.assetPrefix = `emailAssets/${whiteLabel.emailAssetPrefix}`;
		emailData.currentYear = new moment().format("YYYY");


		const emailOptions = {
			from: "LineLeader support <<EMAIL>>",
			to: userEmails,
			subject: "A document you submitted has been rejected",
			html: SSR.render('messageEmail', emailData)
		};
		if (org.replyToAddress) emailOptions.replyTo = org.replyToAddress;
		if (org.fromAddress) emailOptions.from = org.fromAddress;
		try {
			await Email.sendAsync(emailOptions);
		} catch(e) {
			console.log(e);
		}
	}
}
