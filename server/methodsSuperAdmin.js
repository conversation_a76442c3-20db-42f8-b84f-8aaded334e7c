import { AutoPayService } from './invoices/autoPayService';
import { InvoiceMonitoringService } from './invoices/invoiceMonitoringService';
import { ChargebackAutomationService } from './invoices/chargebackAutomationService';
import { RerunInvoiceGenerationCronProcess } from './migrations/rerunInvoiceGenerationCronProcess';
import { syncDefaultGroup } from './syncDefaultGroup';
import { AwsBillingService } from "./awsBillingService";
import { Log } from "../lib/util/log";
import { SyncedCronService } from './syncedCronService';
import {processAnnouncementReminder} from "./agenda/processAnnouncementReminder";
import { Announcements } from "../lib/collections/announcements";
import {Orgs} from "../lib/collections/orgs";


Meteor.methods({
    'runDeferredEmailsNow': async function (orgId) {
        await SyncedCronService.processSummaryEmailDeferrals(orgId);
    },
    /**
     * Run the chargeback invoicing.
     */
    runChargebacksInvoicing() {
        Meteor.defer(async () => {
            try {
                await ChargebackAutomationService.runChargebacks();
            } catch (e) {
                // no opp
            }
            try {
                await ChargebackAutomationService.runChargebackMonitoring();
            } catch (e) {
                // no opp
            }
        });
    },
    /**
     * Transfer funds for chargebacks.
     */
    runChargebackInvoicesTransferFunds() {
        Meteor.defer(async () => {
            try {
                await ChargebackAutomationService.retryChargebacks();
            } catch (e) {
                // no opp
            }
        });
    },
    /**
     * View the failed auto-pay report.
     * This will send an email of the report to the designated people for billing errors.
     *
     * @param { { date: string }, { timezone: BillingTimezoneMap } } options
     */
    async viewFailedAutoPayReport(options) {
        const user = await Meteor.userAsync();
        const currentPerson = await user?.fetchPerson();

        if (!currentPerson || !currentPerson.superAdmin) {
            throw new Meteor.Error('SuperAdmin Failed Auto-Pay', 'The current user is not a super admin.');
        }

        if (!options.dateString || !options.timezone) {
            throw new Meteor.Error('SuperAdmin Failed Auto-Pay Bad Data', 'The date and timezone are required.');
        }

        Meteor.defer(async () => {
            await AutoPayService.runAutoPayMonitoring(options);
        });
    },
    /**
     * View the failed invoicing report.
     * This will send an email of the report to the designated people for billing errors.
     *
     * @param { { timezone: BillingTimezoneMap } } options
     */
    async viewFailedInvoicingReport(options) {
        const user = await Meteor.userAsync();
        const currentPerson = await user?.fetchPerson();

        if (!currentPerson || !currentPerson.superAdmin) {
            throw new Meteor.Error('SuperAdmin Failed Invoicing', 'The current user is not a super admin.');
        }

        if (!options.timezone) {
            throw new Meteor.Error('SuperAdmin Failed Auto-Pay Bad Data', 'The timezone is required.');
        }

        Meteor.defer(async () => {
            await InvoiceMonitoringService.monitorRunInvoices(options);
        });
    },
    async reRunInvoicesCronJob() {
        try {
            await AwsBillingService.runAllInvoices();
        } catch (err) {
            Log.error('Error invoking full invoicing queues', err);
        }
    },
    async runGroupSyncJob() {
        try {
            await syncDefaultGroup.runWeeklyGroupSyncJob();
        } catch (err) {
            Log.error('Error running group sync job', err.message || err.reason);
            throw new Error('Error running group sync job: ' + err.message || err.reason);
        }
    },
    async runAnnouncementRemindersJob() {
        const currentOrg = await Orgs.current();
        const allAnnouncements = await Announcements.find({ orgId: currentOrg._id }, { sort: { createdAt: -1 } }).fetchAsync();
        const announcement = allAnnouncements[0]

        if (announcement.reminders) {
            for (const reminder of announcement.reminders) {
                await processAnnouncementReminder(announcement._id, reminder._id);
            }
        }
    }
});
