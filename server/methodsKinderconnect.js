import { Orgs } from "../lib/collections/orgs";
import { User } from "../lib/collections/users";
import { ERROR_MESSAGES } from "../lib/constants/messageConstants";

Meteor.methods({
	async "upsertProviders"(options) {
    this.unblock();
		let currentUser = await Meteor.userAsync();
        let currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson.type !== "admin"){
            throw new Meteor.Error(403, ERROR_MESSAGES.ACCESS_DENIED);
        }

        const groupPassword = options?.kinderConnectorSettings?.groupPassword;
        const providers = options?.kinderConnectorSettings?.providers;
        const orgId = (await Orgs.current())._id;

        if (providers?.length) {
            providers.forEach((provider) => {
                if (!/^[0-9]+$/.test(provider.id)) {
                    throw new Meteor.Error(500, ERROR_MESSAGES.PROVIDER_ID_MUST_BE_NUMBER);
                }
            })
        }

        if (providers?.length) {
            for (const provider of providers){
                let providerId = parseInt(provider.id);
                if (provider.mode === "add") {
                    await Orgs.updateAsync({ _id: orgId }, { $push: { "kinderConnectorSettings.providers": { id: providerId } } });
                } else if (provider.mode === "edit") {
                    let oldProviderId = parseInt(provider.oldValue.id);
                    await Orgs.updateAsync({ _id: orgId, "kinderConnectorSettings.providers.id": oldProviderId }, { $set: { "kinderConnectorSettings.providers.$.id": providerId } });
                } else if (provider.mode === "delete") {
                    await Orgs.updateAsync({ _id: orgId }, { $pull: { "kinderConnectorSettings.providers":{"id": providerId }} });
                }
            };
        }

        if (groupPassword && groupPassword !== '') {
            await Orgs.updateAsync({ _id: orgId }, { $set: { "kinderConnectorSettings.groupPassword": groupPassword } });
        } else {
            await Orgs.updateAsync({ _id: orgId }, { $unset: { "kinderConnectorSettings.groupPassword": 1 } });
        }
        var org = await Orgs.findOneAsync({ _id: orgId });
        if (org.kinderConnectorSettings?.providers?.length === 0) {
            await Orgs.updateAsync({ _id: orgId }, { $unset: { "kinderConnectorSettings.providers": 1 } });
        }
        if (org.kinderConnectorSettings && Object.keys(org.kinderConnectorSettings).length === 0) {
            await Orgs.updateAsync({ _id: orgId }, { $unset: { "kinderConnectorSettings": 1 } });
        }

	},
    async getKinderConnectSettings(options) {
		var currentUser = await Meteor.userAsync();
        const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const currentOrg = await Orgs.current();
		return currentOrg.kinderConnectorSettings ? currentOrg.kinderConnectorSettings : null ;
	}
})
