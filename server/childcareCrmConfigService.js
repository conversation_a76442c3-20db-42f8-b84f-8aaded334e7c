import {MongoConstants} from "../lib/constants/mongoConstants";
import { ChildcareCrmAccounts } from "../lib/collections/childcareCrmAccounts";

export class ChildcareCrmConfigService {
    /**
     *
     * @returns {[
     *  {
     *      id: string,
     *      username: string
     *  }
     * ]}
     */
    static async getChildcareCrmAccountStubs() {
        const accounts = await ChildcareCrmAccounts.find(
            { password: { $nin: [null, ''] } },
            { fields: { _id: 1, username: 1 }, ...MongoConstants.READ_SECONDARY }
        ).fetchAsync();
        return accounts.map(acct => ({
            id: acct._id,
            username: acct.username
        }));
    }

    /**
     * @typedef { Object } ChildcareCrmConfig
     * @property { number } client_id
     * @property { string } [client_name]
     * @property { string } [username]
     * @property { string } [password]
     * @property { ChildcareCrmCentersConfig[] } centers
     * @property { ChildcareCrmRolesConfig[] } [roles]
     */
    /**
     * @typedef { Object } ChildcareCrmCentersConfig
     * @property { number } id
     * @property { string | null } org_id
     * @property { string } name
     * @property { ChildcareCrmClassroomsConfig[] } classrooms
     */
    /**
     * @typedef { Object } ChildcareCrmClassroomsConfig
     * @property { number } id
     * @property { string | null } group_id
     * @property { string } name
     */
    /**
     * @typedef { Object } ChildcareCrmRolesConfig
     * @property { string } identifier
     * @property { string | array } position
     */

    /**
     *
     * @param { ChildcareCrmConfig } config
     *
     * @returns string
     */
    static async createChildcareCrmAccount(config) {
        const newCrmAccount = {
            clientId: config.client_id,
            name: config.client_name,
            username: config.username,
            password: config.password,
            syncStatus: 'integrated'
        }
        newCrmAccount.centers = this.crmAccountCenters(config);

        const roles = this.crmAccountRoles(config);
        if (roles && roles.length) {
            newCrmAccount.roles = roles;
        }

        return await ChildcareCrmAccounts.insertAsync(newCrmAccount);
    }

    /**
     * Update Account Config
     * @param { string } accountId
     * @param { ChildcareCrmConfig } config
     */
    static async updateChildcareCrmAccount(accountId, config) {
        const centers = this.crmAccountCenters(config);
        const roles = this.crmAccountRoles(config);

        // Backfill
        const clientId = config.client_id;
        const setData = { centers, clientId};
        if (roles && roles.length) {
            setData.roles = roles;
        }

        await ChildcareCrmAccounts.updateAsync({ _id: accountId }, { $set: setData });
    }

    /**
     * Update Account Config for a single location.
     * @param { string } accountId          The Manage Org._id
     * @param { ChildcareCrmConfig } config The new config from the CRM
     */
    static async updateChildcareCrmAccountLocation(accountId, config) {
        const clientId = config.client_id;

        // Get the centers from the config
        const centers = this.crmAccountCenters(config);

        // Get the stored account information for the CRM customer
        let crmAccount = await ChildcareCrmAccounts.findOneAsync({ _id: accountId, clientId: clientId });
        if (!crmAccount) {
            return {
                statusCode: 404,
                message: { error: 'Account Not found' }
            };
        }

        // Loop through the centers from the config and update the account information
        for (const center of centers) {
                const centerIdx = crmAccount.centers.findIndex(c => c.id === center.id);

                // If the center doesn't exist, add it.
                if (centerIdx === -1 || isNaN(centerIdx)) {
                    // Add the new center.
                    await ChildcareCrmAccounts.updateAsync(
                        { _id: accountId },
                        {
                            $push: {
                                centers: center
                            }
                        }
                    );
                } else {
                    // Update the center integration info.
                    await ChildcareCrmAccounts.updateAsync(
                        {
                            _id: accountId,
                            'centers.id': center.id
                        },
                        {
                            $set: {
                                'centers.$': center
                            }
                        }
                    );
                }
            }

        return { statusCode: 200 };
    }

    /**
     * @param { ChildcareCrmConfig } config
     * @returns {[{
     *     id: number,
     *     name: string,
     *     orgId: string | null,
     *     classrooms: [
     *         {
     *             id: number,
     *             name: string,
     *             groupId: string | null
     *         }
     *     ]
     * }]}
     */
    static crmAccountCenters(config) {
        if (config.centers && config.centers.length) {
            return config.centers.map(center => (
                    {
                        id: center.id,
                        name: center.name,
                        orgId: center.org_id,
                        classrooms: (center.classrooms || []).map(classroom => (
                                {
                                    id: classroom.id,
                                    name: classroom.name,
                                    groupId: classroom.group_id
                                }
                            )
                        )
                    }
                )
            );
        }

        return [];
    }

    /**
     * @param { ChildcareCrmConfig } config
     *
     * @returns {[{
     *    identifier: string,
     *    position: number | null
     * }]}
     */
    static crmAccountRoles(config) {
        // TODO remove early return
        return [];
        // if (config.roles && config.roles.length) {
        //     return config.roles.map(role => ({
        //         identifier: role.identifier,
        //         position: role.position || null
        //     }))
        // }
        //
        // return [];
    }
}
