import { Groups } from "../lib/collections/groups";
import { Orgs } from "../lib/collections/orgs";
import { Log } from "../lib/util/log";

export class GroupsService {
  static async setScheduleTypeToPlans(orgId = null, groupId = null) {
    // Track changes for bulk operations
    const groupsBatch = Groups.rawCollection().initializeUnorderedBulkOp();
    let hasUpdates = false;

    // Build query based on whether orgId is provided
    const query = orgId ? { _id: orgId } : {};

    // Get all orgs first to avoid repeated database calls
    const orgs = await Orgs.find(query, {
      fields: {
        _id: 1,
        'valueOverrides.scheduleTypes': 1,
        'billing.plansAndItems': 1
      }
    }).fetchAsync();

    // Process each org
    for (const org of orgs) {
      try {
        // Get all groups for this org or group by groupId
        const grpQuery = groupId ? { _id: groupId , orgId: org._id } : { orgId: org._id };
        
        const groups = await Groups.find( grpQuery, { fields: { _id: 1 } }).fetchAsync();

        // Skip if org has no groups
        if (!groups.length) return;

        // Get schedule types and billing plans
        const scheduleTypes = org.valueOverrides?.scheduleTypes || [];
        const billingPlans = org.billing?.plansAndItems || [];

        // Create scheduleTypeToPlans object
        const scheduleTypeToPlans = {};

        // Initialize each schedule type with all billing plan IDs
        scheduleTypes.forEach(scheduleType => {
          // Only include schedule types that have an _id
          if (scheduleType._id) {
            // Get all billing plan IDs
            const planIds = billingPlans
              .filter(plan => {
                // Include only non-archived plans
                return !plan.archived &&
                  plan._id &&
                  ['plan'].includes(plan.type);
              })
              .map(plan => plan._id);

            scheduleTypeToPlans[scheduleType._id] = planIds;
          }
        });

        // Update each group for this org
        groups.forEach(group => {
          groupsBatch.find({ _id: group._id }).updateOne({
            $set: { scheduleTypeToPlans }
          });
          hasUpdates = true;
        });

      } catch (error) {
        Log.error(`Error processing org ${org._id}: ${error}`);
      }
    }

    // Execute the bulk operation if there are updates
    if (hasUpdates) {
      const execute = Meteor.wrapAsync(groupsBatch.execute, groupsBatch);
      return execute();
    }

    return true;
  }
  static async addScheduleTypeToPlansForAllGroupsInOrg(orgId, scheduleTypeId){
    // Need all billing plans _id's (query)
    const activePlans = await this.getAllActivePlansForOrg(orgId);

    // Loop through all groups in the org
    const groups = await Groups.find({ orgId: orgId }).fetchAsync();

    // Extract just the IDs from the active plans
    const planIds = activePlans.map(plan => plan._id);

    // For each group add the schedule type and all billing plans into the scheduleTypeToPlans object
    for (const group of groups) {
      // Create update object using scheduleTypeId as the key
      const updateObject = {};
      updateObject[`scheduleTypeToPlans.${scheduleTypeId}`] = planIds;

      await Groups.updateAsync(
        { _id: group._id },
        { $set: updateObject }
      );
    }
  }

  static async removeScheduleTypeFromPlansForAllGroupsInOrg(orgId, scheduleTypeId) {
    // Get all groups in the org
    const groups = await Groups.find({ orgId: orgId }).fetchAsync();

    // For each group, remove the schedule type from scheduleTypeToPlans object
    for (const group of groups) {
      // Skip groups with null scheduleTypeToPlans
      if (group.scheduleTypeToPlans === null) {
        continue;
      }

      // Initialize scheduleTypeToPlans if undefined
      if (!group.scheduleTypeToPlans) {
        group.scheduleTypeToPlans = {};
      }

      // Create unset object using scheduleTypeId as the key to remove
      const updateObject = {};
      updateObject[`scheduleTypeToPlans.${scheduleTypeId}`] = 1;

      await Groups.updateAsync(
        { _id: group._id },
        { $unset: updateObject }
      );
    }
  }

  static async getAllActivePlansForOrg(orgId, countOnly = false) {
    let aggregation = [
      { $match: { _id: orgId } },
      { $unwind: '$billing.plansAndItems' },
      { $match: {
          $or: [
            { 'billing.plansAndItems.archived': { $exists: false } },
            { 'billing.plansAndItems.archived': false }
          ]
        }},
      { $project: {
          _id: '$billing.plansAndItems._id'
        }}
    ];

    if (countOnly) {
      // Add a count stage instead of returning the array
      aggregation.push({ $count: 'total' });
      return (await Orgs.aggregate(aggregation)).toArray()
        .then(result => result[0]?.total || 0);
    }

    return await (await Orgs.aggregate(aggregation)).toArray();
  }

  static async addBillingPlanToAllGroupsWithSelectAllInOrg(orgId, billingPlans, billingPlanId) {
    const activePlansLength = billingPlans.length;
    if (activePlansLength === 0) {
      return;
    }

    const groups = await Groups.find({ orgId }).fetchAsync();
    for (const group of groups) {
      if (!group.scheduleTypeToPlans) continue;

      const updates = {};
      Object.entries(group.scheduleTypeToPlans).forEach(([scheduleTypeId, planIds]) => {
        if (Array.isArray(planIds) &&
          planIds.length >= activePlansLength &&
          !planIds.includes(billingPlanId)) {
          updates[`scheduleTypeToPlans.${scheduleTypeId}`] = billingPlanId;
        }
      });

      if (Object.keys(updates).length > 0) {
        await Groups.updateAsync(
          { _id: group._id },
          { $push: updates }
        );
      }
    }
  }

  static async removeBillingPlanFromAllGroupsInOrg(orgId, billingPlanId) {
    const groups = await Groups.find({ orgId }).fetchAsync();

    for (const group of groups) {
      if (!group.scheduleTypeToPlans) continue;
      const updates = {};
      Object.entries(group.scheduleTypeToPlans).forEach(([scheduleTypeId, planIds]) => {
        // Check if planIds is an array and contains the billingPlanId
        if (Array.isArray(planIds) && planIds.includes(billingPlanId)) {
          updates[`scheduleTypeToPlans.${scheduleTypeId}`] = billingPlanId;
        }
      });

      // If we found any instances of the billingPlanId, remove them
      if (Object.keys(updates).length > 0) {
        await Groups.updateAsync(
          { _id: group._id },
          { $pull: updates }  // $pull will remove the billingPlanId from each array
        );
      }
    }
  }
}