import moment from 'moment-timezone';
import {GeneralAttendanceReportUtils} from "../../lib/util/generalAttendanceReportUtils";
import { People } from '../../lib/collections/people';
import { Moments } from '../../lib/collections/moments';
import { Reservations } from '../../lib/collections/reservations';
import _ from '../../lib/util/underscore';

/**
 * A service class for generating general attendance reports.
 */
export class ReportGeneralAttendanceService {

    static async aggregateData(query, orgIds, sortType, ignoreStaff = false, absenceQuery = null) {
        const pipeline = [
            {
                $match: {
                    orgId: {$in: orgIds},
                    ...query,
                },
            },
            {
                $lookup: {
                    from: "people", // The collection name for People
                    localField: "owner",
                    foreignField: "_id",
                    as: "ownerData",
                },
            },
            {
                $unwind: "$ownerData",
            },
            {
                $addFields: {
                    ownerData: { $ifNull: [ "$ownerData", {} ] }
                }
            },
            {
                $match: {
                    $or: [
                        {"ownerData.type": "person"},
                        {
                            $and: [
                                {$or: [{"ownerData.type": "staff"}, {"ownerData.type": "admin"}]},
                                {$expr: {$eq: [ignoreStaff, false]}},
                            ],
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: "people", // The collection name for People (for checked-in by)
                    localField: "checkedInById",
                    foreignField: "_id",
                    as: "checkedInByData",
                },
            },
            {
                $unwind: {
                    path: "$checkedInByData",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "people", // The collection name for People (for checked-out by)
                    localField: "checkedOutById",
                    foreignField: "_id",
                    as: "checkedOutByData",
                },
            },
            {
                $unwind: {
                    path: "$checkedOutByData",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "people", // The collection name for People (for created by)
                    localField: "createdByPersonId",
                    foreignField: "_id",
                    as: "createdByData",
                },
            },
            {
                $unwind: {
                    path: "$createdByData",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    sortStamp: 1,
                    momentType: 1,
                    owner: "$ownerData._id",
                    firstName: "$ownerData.firstName",
                    lastName: "$ownerData.lastName",
                    person: {
                        firstName: "$ownerData.firstName",
                        lastName: "$ownerData.lastName",
                        _id: "$ownerData._id",
                        orgId: "$ownerData.orgId",
                        inActive: "$ownerData.inActive",
                        type: "$ownerData.type",
                        payer: "$ownerData.payer",
                        carePlan: "$ownerData.carePlan",
                    },
                    checkedInByName: {
                        $concat: [
                            { $ifNull: ["$checkedInByData.firstName", "$createdByData.firstName"] },
                            " ",
                            { $ifNull: ["$checkedInByData.lastName", "$createdByData.lastName"] },
                        ],
                    },
                    checkedOutByName: {
                        $concat: [
                            { $ifNull: ["$checkedOutByData.firstName", "$createdByData.firstName"] },
                            " ",
                            { $ifNull: ["$checkedOutByData.lastName", "$createdByData.lastName"] },
                        ],
                    },
                    time: 1,
                    checkInTransportation: 1,
                    checkOutTransportation: 1,
                    createdBy: 1,
                    createdByPersonName: {
                        $concat: [
                            { $ifNull: ["$createdByData.firstName", ""] },
                            " ",
                            { $ifNull: ["$createdByData.lastName", ""] },
                        ],
                    },
                    orgId: 1,
                    reason: 1,
                },
            }
        ];

        const absentPipeline = [
            {
                $match: {
                    orgId: {$in: orgIds},
                    ...absenceQuery,
                },
            },
            {
                $lookup: {
                    from: "people", // The collection name for People
                    localField: "selectedPerson",
                    foreignField: "_id",
                    as: "ownerData",
                },
            },
            {
                $unwind: "$ownerData",
            },
            {
                $addFields: {
                    ownerData: { $ifNull: [ "$ownerData", {} ] },
                }
            },
            {
                $match: {
                        "ownerData.type": "person",
                },
            },
            {
                $project: {
                    firstName: "$ownerData.firstName",
                    lastName: "$ownerData.lastName",
                    sortStamp: "$scheduledDate",
                    checkedInByName: {
                        $concat: [
                            "ABSENT -",
                            " ",
                            "$cancellationReason",
                        ],
                    },
                    person: {
                        firstName: "$ownerData.firstName",
                        lastName: "$ownerData.lastName",
                        _id: "$ownerData._id",
                        orgId: "$ownerData.orgId",
                        inActive: "$ownerData.inActive",
                        type: "$ownerData.type",
                        payer: "$ownerData.payer",
                        carePlan: "$ownerData.carePlan",
                    },
                    owner: "$selectedPerson",
                    scheduledTime: 1,
                    scheduledEndTime: 1,
                }
            }
    ];
        let sort = null;
        if (sortType === "attendeeLastName") {
            sort = {
                $sort: {
                    lastName: 1,
                    firstName: 1,
                    sortStamp: 1,
                },
            };
        } else {
            sort = {
                $sort: {
                    firstName: 1,
                    lastName: 1,
                    sortStamp: 1,
                },
            };
        }

        pipeline.push(sort);
        if (absenceQuery) absentPipeline.push(sort);

        const moments = await (await Moments.aggregate(pipeline)).map(moment => ({
            ...moment,
            owner: moment.owner.toString(),
        })).toArray();

        let absences = null;
        if (absenceQuery) {
            absences = await (await Reservations.aggregate(absentPipeline)).map(res => ({
                ...res,
                absentRecord: true,
            })).toArray();
            if (absences.length) {
                moments.push(...absences);
            }
            //re-sort the combined array
            this.sortCombined(moments, sortType);
        }

        return moments;
    }

    /**
     * Builds the query and query options for retrieving attendance moments.
     * @param {object} options - Options for building the query.
     * @param {string} options.reportStartDate - Start date of the report.
     * @param {string} options.reportEndDate - End date of the report.
     * @param {string} options.personId - ID of the person.
     * @param {string} options.groupId - ID of the group.
     * @param {string} options.personType - Type of the person.
     * @param {object} options.org - Organization details.
     * @param {object} options.currentUser - Current user details.
     * @returns {object} An object containing the query and query options.
     */
    static async buildMomentQuery(options) {
        const { reportStartDate, reportEndDate, personId, groupId, personType, org, currentUser, orgIds } = options;
        const timezone = org.getTimezone();
        const startDateNum = moment.tz(reportStartDate, "MM/DD/YYYY", timezone).startOf('day').valueOf();
        const endDateNum = moment.tz(reportEndDate, "MM/DD/YYYY", timezone).endOf('day').valueOf();

        const query = {
            $and: [
                { sortStamp: { $gte: startDateNum, $lte: endDateNum } },
                { momentType: { $in: ["checkin", "checkout"] } },
            ]
        };

        if (personId || groupId || personType) {
            const peopleFilter = { orgId: currentUser.orgId, designations: { $nin: ["Wait List"] } };

            if (personId) peopleFilter._id = personId;
            if (groupId) peopleFilter.defaultGroupId = groupId;
            if (personType) {
                peopleFilter.type = personType;
                if (!groupId && !personId && orgIds?.length) {
                    peopleFilter.orgId = { $in: orgIds };
                }
            }

            const peopleIdsCursor = await People.find(peopleFilter, { fields: { _id: 1 } }).fetchAsync();
            const peopleIds = peopleIdsCursor.map(p => p._id);
            query.$and.push({ owner: { $in: peopleIds } });
        }

        return query;
    }

    /**
     * Builds the query and query options for retrieving cancelled reservations.
     * @param {object} options - Options for building the query.
     * @param {string} options.reportStartDate - Start date of the report.
     * @param {string} options.reportEndDate - End date of the report.
     * @param {string} options.personId - ID of the person.
     * @param {string} options.groupId - ID of the group.
     * @param {string} options.personType - Type of the person.
     * @param {object} options.org - Organization details.
     * @param {object} options.currentUser - Current user details.
     * @returns {object} An object containing the query and query options.
     */
    static async buildReservationQuery(options) {
        const { reportStartDate, reportEndDate, personId, groupId, personType, org, currentUser, orgIds } = options;
        const timezone = org.getTimezone();
        const startDateNum = moment.tz(reportStartDate, "MM/DD/YYYY", timezone).startOf('day').valueOf();
        const endDateNum = moment.tz(reportEndDate, "MM/DD/YYYY", timezone).endOf('day').valueOf();

        const query = {
            $and: [
                { scheduledDate: { $gte: startDateNum, $lte: endDateNum } },
                { cancellationDate: { $nin: [null, ''] } },
                { reservationType: 'person' },
            ]
        };

        if (personId || groupId || personType) {
            const peopleFilter = { orgId: currentUser.orgId, designations: { $nin: ["Wait List"] } };

            if (personId) peopleFilter._id = personId;
            if (groupId) peopleFilter.defaultGroupId = groupId;
            if (personType) {
                peopleFilter.type = personType;
                if (!groupId && !personId && orgIds?.length) {
                    peopleFilter.orgId = { $in: orgIds };
                }
            }

            const peopleIdsCursor = await People.find(peopleFilter, { fields: { _id: 1 } }).fetchAsync();
            const peopleIds = peopleIdsCursor.map(p => p._id);
            query.$and.push({ selectedPerson: { $in: peopleIds } });
        }

        return query;
    }

    /**
     * Builds the output object for a single attendance record.
     * @param {object} options - Options for building the output.
     * @param {string} options.date - Attendance date.
     * @param {object} options.person - Person's details.
     * @param {object} options.checkInTime - Check-in time details.
     * @param {object} options.checkOutTime - Check-out time details.
     * @param {string} options.checkedInByName - Name of the person who checked in.
     * @param {string} options.checkedOutByName - Name of the person who checked out.
     * @param {string} options.elapsedTime - Elapsed time.
     * @param {string} options.scheduleOnDay - Schedule on the day.
     * @param {number} options.checkOutVariance - Check-out variance.
     * @param {string} options.staffMemberName - Name of the staff member.
     * @param {array} options.orgsScope - Scoped organizations.
     * @param {object} options.org - Organization details.
     * @param {string[]} options.reimbursementTypes - Person Reimbursement Types in allocations
     * @returns {object} The built output object for the attendance record.
     */
    static buildOutput(options) {
        const {
            date,
            person,
            checkInTime,
            checkOutTime,
            checkedInByName,
            checkedOutByName,
            elapsedTime,
            scheduleOnDay,
            checkOutVariance,
            staffMemberName,
            orgsScope,
            org,
            reimbursementTypes
        } = options;
        const reasonCheckin = org.pickDropReasons?.find((reason) => reason._id === checkInTime?.reason);
        const reasonCheckout = org.pickDropReasons?.find((reason) => reason._id === checkOutTime?.reason);
        let reason = '';
        if (reasonCheckin?.reason) {
            reason += `Late arrivals: ${reasonCheckin.reason}`;
        }
        if (reasonCheckout?.reason) {
            reason += `\nEarly departure: ${reasonCheckout.reason}`;
        }
        return {
            _id: person._id,
            reimbursementTypes,
            name: `${person.firstName} ${person.lastName}`,
            inActive: person.inActive,
            date: date,
            cInTime: checkInTime?.time ? `${checkInTime.time}${reasonCheckin?.reason ? ` - ${reasonCheckin?.reason}` : ''}` : null,
            cOutTime: checkOutTime?.time ? `${checkOutTime.time}${reasonCheckout?.reason ? ` - ${reasonCheckout?.reason}` : ''}` : null,
            cInBy: checkedInByName,
            cOutBy: checkedOutByName,
            elapsedTime,
            payer: person.payer || null,
            trans: checkInTime?.checkInTransportation || null,
            cOutTrans: checkOutTime?.checkOutTransportation || null,
            staffName: staffMemberName,
            scheduleOnDay,
            variance: checkOutVariance && checkOutVariance > 0 && checkOutVariance,
            carePlan: (org.hasCustomization("report/attendance/showCarePlanObjective") && person.carePlan && person.carePlan.objective) || null,
            orgName: orgsScope && (orgsScope.find(o => o._id === person.orgId) || {}).name,
            reason
        }
    }

    /**
     * Processes check-in and check-out times to calculate additional details.
     * @param {object} options - Options for processing times.
     * @param {object} options.person - Person's details.
     * @param {object} options.checkInTime - Check-in time details.
     * @param {object} options.checkOutTime - Check-out time details.
     * @param {object} options.org - Organization details.
     * @param {array} options.orgsScope - Scoped organizations.
     * @param {string} options.date - Attendance date.
     * @param {string[]} options.reimbursementTypes - Person Reimbursement Types in allocations
     * @returns {object} The processed output object with additional details.
     */
    static async processCheckInAndCheckoutTimes(options) {
        const { person, checkInTime, checkOutTime, org, orgsScope, date, reimbursementTypes } = options;
        if (checkInTime.absentRecord) {
            const outputOptions = {
                date,
                person,
                checkInTime,
                checkOutTime: {},
                checkedInByName: checkInTime.checkedInByName,
                checkedOutByName: '',
                elapsedTime: '',
                scheduleOnDay: `${checkInTime.scheduledTime} - ${checkInTime.scheduledEndTime}`,
                checkOutVariance: null,
                staffMemberName: '',
                orgsScope,
                org,
                reimbursementTypes
            };

            return this.buildOutput(outputOptions);
        }

        let overrideElapsedTime;
        if (org.hasCustomization("report/attendance/useQuarterIntervals") && _.contains(['staff', 'admin'], person.type)) {
            const checkInTimeMoment = moment(checkInTime.sortStamp);
            const roundedCheckInTime = checkInTimeMoment.minute(Math.round(checkInTimeMoment.minute() / 15) * 15).second(0).valueOf();
            if (checkOutTime) {
                const checkOutTimeMoment = moment(checkOutTime.sortStamp);
                const roundedCheckOutTime = checkOutTimeMoment.minute(Math.round(checkOutTimeMoment.minute() / 15) * 15).second(0).valueOf();
                overrideElapsedTime = ((roundedCheckOutTime - roundedCheckInTime) / 1000 / 60 / 60).toFixed(2);
            }
        }
        const staffMemberName = org.language === "translationsEnAdultCare" && checkInTime.createdByPersonName || null;

        const elapsedTime = checkInTime && checkOutTime ?
            ((moment(checkOutTime.time, "HH:mm a") - moment(checkInTime.time, "HH:mm a")) / 1000 / 60 / 60).toFixed(2) : null;
        const checkedInByName = checkInTime.checkedInByName;
        let checkedOutByName = checkOutTime ? checkOutTime.checkedOutByName : null;
        if (!checkedOutByName && checkOutTime && checkOutTime.createdBy === 'SYSTEM') {
            checkedOutByName = 'System';
        }

        const reservationOnDay = person && checkInTime && await Reservations.findOneAsync({
            orgId: person.orgId,
            selectedPerson: person._id,
            scheduledDate: moment.tz(checkInTime.sortStamp, org.getTimezone()).startOf('day').valueOf(),
            cancellationReason: { "$exists": false }
        }, { fields: { scheduledTime: 1, scheduledEndTime: 1 } });

        const scheduleOnDay = reservationOnDay && reservationOnDay.scheduledTime && (reservationOnDay.scheduledTime + ' - ' + reservationOnDay.scheduledEndTime);
        const checkOutVariance = scheduleOnDay && checkOutTime && moment(checkOutTime.time, "h:mm a").diff(moment(reservationOnDay.scheduledEndTime, "h:mm a"), "minutes");

        const outputOptions = {
            date,
            person,
            checkInTime,
            checkOutTime,
            checkedInByName,
            checkedOutByName,
            elapsedTime: overrideElapsedTime || elapsedTime,
            scheduleOnDay,
            checkOutVariance,
            staffMemberName,
            orgsScope,
            org,
            reimbursementTypes
        };

        return this.buildOutput(outputOptions);
    }

    /**
     * Fetches attendance moment data based on the provided options.
     * @param {object} options - Options for fetching moment data.
     * @param {object} options.query - Query for fetching moments.
     * @param {array} options.orgIds - Array of organization IDs.
     * @param {object} options.currentPerson - Current person's details.
     * @param {object} options.currentUser - Current user's details.
     * @param {string} options.sortType - Sorting options.
     * @param {boolean} options.ignoreStaff - Whether to ignore staff/admin records.
     * @param {object} options.absenceQuery - Query for fetching absent reservations (optional).
     * @returns {Promise<object>} An object containing fetched moments and scoped organizations.
     */
    static async fetchMomentData(options) {
        const { query, orgIds, currentPerson, currentUser, sortType , ignoreStaff, absenceQuery} = options;
        const batchSize = 15; // size to batch orgs in
        let allMoments = [];
        let orgsScope = null;

        if (orgIds && orgIds.length > 0 && (currentPerson.masterAdmin || currentPerson.superAdmin)) {
            // if multiple orgs, let's batch them in groups of 15
            orgsScope = await currentPerson.findScopedOrgs();
            const orgsScopeList = orgsScope && orgsScope.map(o => o._id);
            const orgIdsInScope = orgsScopeList.filter(o => orgIds.includes(o));

            // Create an array of promises for each batch
            const batchPromises = [];
            for (let i = 0; i < orgIdsInScope.length; i += batchSize) {
                const orgIdsBatch = orgIdsInScope.slice(i, i + batchSize);
                const batchPromise = this.aggregateData(query, orgIdsBatch, sortType, ignoreStaff, absenceQuery);
                batchPromises.push(batchPromise);
            }

            const batchResults = await Promise.all(batchPromises);

            // Combine results from all batches
            for (const batchResult of batchResults) {
                allMoments = allMoments.concat(batchResult);
            }
        } else {
            // otherwise just use the current user's org
            const orgId = [currentUser.orgId];
            const moments = await this.aggregateData(query, orgId, sortType, ignoreStaff, absenceQuery);
            allMoments = allMoments.concat(moments);
        }



        return { moments: allMoments, orgsScope };
    }

    /**
     * Generates a general attendance report.
     * @param {object} currentUser - Current user details.
     * @param {object} currentPerson - Current person's details.
     * @param {object} org - Organization details.
     * @param {object} options - Options for generating the report.
     * @param {string} options.reportStartDate - Start date of the report.
     * @param {string} options.reportEndDate - End date of the report.
     * @param {string} options.personId - ID of the person.
     * @param {string} options.groupId - ID of the group.
     * @param {string} options.personType - Type of the person.
     * @param {array} options.orgIds - Array of organization IDs.
     * @param {string} options.sortType - Type of sorting.
     * @param {boolean} options.ignoreStaff - Whether to ignore staff records.
     * @returns {Promise<array>} The generated general attendance report.
     */
    static async generateReport(currentUser, currentPerson, org, options) {

        const {groupedMoments, orgsScope } = await this.getRawReportData(currentUser, currentPerson, org, options);

        const filteredOrgIds = options.orgIds && options.orgIds.length > 0 ? options.orgIds : [org._id];

        const filteredGroupMoments = options?.subsidyFilterValues?.length
            ? await GeneralAttendanceReportUtils.filterGroupedMoments(groupedMoments, filteredOrgIds, options.subsidyFilterValues)
            : groupedMoments;


        const outputOptions = {
            ...options,
            groupedMoments: filteredGroupMoments,
            orgsScope
        }
        return this.getOutputData(org, outputOptions);
    }

    /**
     * Fetches raw attendance moment data and groups it for report generation.
     * @param {object} currentUser - Current user details.
     * @param {object} currentPerson - Current person's details.
     * @param {object} org - Organization details.
     * @param {object} options - Options for fetching and processing data.
     * @returns {Promise<object>} An object containing grouped attendance moments and scoped organizations.
     */
    static async getRawReportData(currentUser, currentPerson, org, options) {
        try {
            const { reportStartDate, reportEndDate, personId, groupId, personType, orgIds, sortType, ignoreStaff, showAbsences } = options;
            const start = moment();
            const momentQueryOptions = { reportStartDate, reportEndDate, personId, groupId, personType, org, currentUser, orgIds };
            const query = await this.buildMomentQuery(momentQueryOptions);

            let absenceQuery = null;
            if (showAbsences) {
                absenceQuery = await this.buildReservationQuery(momentQueryOptions);
            }

            const {moments, orgsScope} = await this.fetchMomentData({query, orgIds, currentPerson, currentUser, sortType, ignoreStaff, absenceQuery});
            const groupedMoments = [];
            let currentGroup = null;
            const timezone = org.getTimezone();
            moments.forEach(m => {
                const date = moment.tz(m.sortStamp, timezone).format("MM/DD/YYYY");
                if (!currentGroup || currentGroup.owner !== m.owner || currentGroup.date !== date) {
                    currentGroup = {
                        owner: m.owner,
                        date,
                        moments: [m],
                    };
                    groupedMoments.push(currentGroup);
                } else {
                    currentGroup.moments.push(m);
                }
            });

            if (sortType === 'attendanceDate') {
                groupedMoments.sort((a, b) => {
                    return a.date > b.date ? 1 : -1;
                });
            }

            return {groupedMoments, orgsScope};
        } catch (error) {
            console.error("Error in fetching report data:", error);
            throw new Meteor.Error('500',"Error in fetching report data", error.message);
        }
    }

    /**
     * Generates the final output data for the attendance report.
     * @param {object} org - Organization details.
     * @param {object} options - Options for generating the report output.
     * @param {array} options.groupedMoments - Grouped attendance moments.
     * @param {array} options.orgsScope - Scoped organizations.
     * @param {boolean} options.ignoreStaff - Whether to ignore staff records.
     * @param {string[]} options.groupedMoments.reimbursementTypes - Person Reimbursement Types in allocations
     * @returns {string} The generated attendance report output data.
     */
    static async getOutputData(org, options) {
        try {
            const { groupedMoments, orgsScope, showSingleChildPerPage, sortType } = options;
            console.log("Options", options)
            const start = moment();
            const output = [];

            // Create a map to store checkInTimes by owner and date
            const checkInTimeMap = new Map();

            for (const group of groupedMoments) {
                const { date, moments, reimbursementTypes } = group;
                const person = moments[0].person;

                const checkInTimes = moments
                    .filter(i => i.momentType === "checkin" || i.absentRecord)
                    .sort((a, b) => a.sortStamp - b.sortStamp);

                for (const checkInTime of checkInTimes) {
                    const key = `${checkInTime.owner}_${checkInTime.sortStamp}`;
                    if (!checkInTimeMap.has(key)) {
                        checkInTimeMap.set(key, {
                            person,
                            checkInTime,
                            date,
                            reimbursementTypes,
                            moments, // Pass moments here
                        });
                    }
                }
            }

            // Generator function to batch process checkInTimes
            function* batchCheckInTimes() {
                const batchSize = 100;
                let batch = [];
                for (const [, { person, checkInTime, date, moments, reimbursementTypes }] of checkInTimeMap) {
                    batch.push({ person, checkInTime, date, moments, reimbursementTypes });
                    if (batch.length >= batchSize) {
                        yield batch;
                        batch = [];
                    }
                }
                if (batch.length > 0) {
                    yield batch;
                }
            }

            // Process checkInTimes in batches
            for (const batch of batchCheckInTimes()) {
                for (const { person, checkInTime, date, moments, reimbursementTypes } of batch) {
                    const checkOutTime = moments
                        .filter(i => i.momentType === "checkout" && i.sortStamp > checkInTime.sortStamp)
                        .sort((a, b) => a.sortStamp - b.sortStamp)
                        .shift();

                    const processOptions = { person, checkInTime, checkOutTime, org, orgsScope, date, reimbursementTypes };
                    const processedTimes = await this.processCheckInAndCheckoutTimes(processOptions);
                    output.push(processedTimes);
                }
            }

            // Parse the output back into an array
            let outputArray = JSON.parse(JSON.stringify(output));

            if (showSingleChildPerPage) {

                // Group the objects by the child's name
                const groupedByChild = outputArray.reduce((groups, item) => {
                    const childName = item.name;
                    if (!groups[childName]) {
                        groups[childName] = [];
                    }
                    groups[childName].push(item);
                    return groups;
                }, {});

                if (sortType === "attendanceDate") {
                    // Sort the groups by the date
                    for (const childName in groupedByChild) {
                        const group = groupedByChild[childName];
                        group.sort((a, b) => new Date(a.date) - new Date(b.date));
                    }
                }

                // Add the new property to the last object in each group
                for (const childName in groupedByChild) {
                    const group = groupedByChild[childName];
                    if (group.length > 0) {
                        group[group.length - 1].addPageBreak = true;
                    }
                }

                // Flatten the groups back into a single array
                outputArray = [].concat(...Object.values(groupedByChild));
            }

            return JSON.stringify(outputArray);
        } catch (e) {
            console.error("Error generating output:", e);
            throw new Meteor.Error('500', 'Error generating report output', e.message);
        }
    }

    static sortCombined(array, sortType) {
        return array.sort((a, b) => {
            if (sortType === "attendeeLastName") {
                if (a.lastName === b.lastName) {
                    if (a.firstName === b.firstName) {
                        return a.sortStamp - b.sortStamp;
                    }
                    return a.firstName.localeCompare(b.firstName);
                }
                return a.lastName.localeCompare(b.lastName);
            } else {
                if (a.firstName === b.firstName) {
                    if (a.lastName === b.lastName) {
                        return a.sortStamp - b.sortStamp;
                    }
                    return a.lastName.localeCompare(b.lastName);
                }
                return a.firstName.localeCompare(b.firstName);
            }
        });
    }
}