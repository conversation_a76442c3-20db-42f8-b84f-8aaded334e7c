import { Groups } from '../../lib/collections/groups';
import { Orgs } from '../../lib/collections/orgs';
import { People } from '../../lib/collections/people';
import { Reservations } from '../../lib/collections/reservations';
import { getFteForMonth } from '../classList';
const moment = require('moment-timezone');
import _ from '../../lib/util/underscore';
export class WaitListReport{

	static async classListScheduleBased(options) {
        const org = await Orgs.current(),
            timezone = org.getTimezone(),
            holidays = org.getHolidays(),
            output = {},
            daySlots = ["mon", "tue", "wed", "thu", "fri"],
            scheduleTypes = _.filter(org.getScheduleTypes(), st => !st.hideInForecasting),
            prefix = org.profileDataPrefix(),
            startDate = new moment.tz(options.startDate, "MM/DD/YYYY", timezone),
            endDate = startDate.clone().add(6, "days"),
            startDateNum = startDate.startOf('day').valueOf(),
            endDateNum = endDate.endOf('day').valueOf();

        output.groups = [];
        output.summaries = {
            totalDiscounts: 0,
            totalCurrentRevenue: 0,
            totalFullTimeRevenue: 0,
            totalVariance: 0,
            varianceToIdealRevenue: 0,
            totalPayroll: 0
        };

        const groupsQuery = { orgId: org._id, includeClassList: { $ne: false } };

        const availableGroups = await Groups.find(groupsQuery).fetchAsync();
        if (options.groupId)
            groupsQuery._id = options.groupId;

        const groups = await Groups.find(groupsQuery).fetchAsync(),
            resQuery = {
                orgId: org._id,
                $or: [{ scheduledEndDate: null }, { scheduledEndDate: { "$gte": startDateNum } }],
                recurringFrequency: { "$exists": true }
            },allReservations = await Reservations.find(resQuery).fetchAsync();

        for (const group of groups) {
            group.groupPeople = [];
            group.dayTotals = {};
            group.dayTotalsList = {};
            group.totalTuition = 0;
            group.totalDiscount = 0;
            group.totalCurrentRevenue = 0;
            group.totalVariance = 0;
            group.fullTuition = 0;
            group.defaultTuition = 0;
            group.ageRangeLabel = group.getAgeRangeLabel();
            group.slotCounts = 0;
            group.slotTotals = 0;
            group.fteTotals = 0;
            group.totalEnrolled = 0;
            group.totalCapacity = 0;
            group.dailyCapacities = {};

            const activeGroupReservations = allReservations.filter(r => r.groupId == group._id), //Organize reservations by group
            groupPeopleIds = activeGroupReservations.map(r => r.selectedPerson), //Get PeopleIds from active reservations 
            defaultReservationsOnly = (allReservations.filter(r => !r.groupId)), // Get just the reservations default (didn't have an group Id)
			defaultReservationsOnlyPeopleIds = defaultReservationsOnly.map(r => r.selectedPerson), //Get People Ids from defaults Reservations
            groupPeopleQuery = { //Query to get all the people in the reservations
                orgId: org._id,
                $or: [
                    { _id: { "$in": groupPeopleIds } }
                ],
                type: "person"
            };
            if (!options.onlyReservationPeople) {
                groupPeopleQuery["$or"].push({ defaultGroupId: group._id });
            } else {
                groupPeopleQuery["$or"].push({ defaultGroupId: group._id, _id: { "$in": defaultReservationsOnlyPeopleIds } });
            }
            
            const beforeParsePeople = await People.find(groupPeopleQuery, { sort: { lastName: 1, firstName: 1 } }).fetchAsync();
			let groupPeople = beforeParsePeople.filter(p => {
				let hasFutureSchedule = allReservations.filter(r => (r.selectedPerson == p._id && r.scheduledDate > startDateNum ))
				if( p?.designations && p.designations.includes("Wait List") && hasFutureSchedule.length >= 1){
					return false
				}else{
						return true
				}
			})
			
            for (const person of groupPeople) {
                const outputPerson = { //Parse just necessary data
                    _id: person._id,
                    name: person.lastName + ", " + person.firstName,
                    birthday: prefix ? (person[prefix] && person[prefix].birthday) : person.birthday,
                    age: person.calcAgeRawFromDate(prefix, "years", startDateNum),
                    withdrawDate: person.getWithdrawDate({currentOrg:org}),
                    destination: "",
                    daySlots: {},
                    nextTransitionSlots: {},
                    tuition: 0,
                    discount: 0,
                    currentRevenue: 0,
                    variance: 0,
                    inActive: person.inActive
                }, 
                personScheduledGroupReservations = activeGroupReservations.filter(r => r.selectedPerson == person._id), //get All the reservations where match userId and selectedPerson
                //maybe the bug is here: next cases  (Res_id equal to p.defaultGroupId and res.selectedPerson equal to person  and (din't exist  groupId) or res.groupId eq group.id and Negation of(exist id in array))
                extraReservations = allReservations.filter(r => group._id == person.defaultGroupId && r.selectedPerson == person._id && (!r.groupId || r.groupId == group._id) && !_.contains(personScheduledGroupReservations.map(psgr => psgr._id), r._id)),       
                personReservations = personScheduledGroupReservations.concat(extraReservations); //Concat reservations whit group and default res
                let nextTransition = _.chain(allReservations)
                    .filter(r => r.selectedPerson == person._id && r.scheduledDate > startDateNum)
                    .sortBy(r => r.scheduledDate)
                    .first()
                    .value();

                if(person.defaultGroupId !== group._id) {
                    outputPerson.matchedByReservation = true;
                }
                // Only calculate this person for the group if a reservation exists for THIS group or its a prospect
                const hasGroupReservation = personReservations?.length > 0 || nextTransition?.length > 0;
                if (!hasGroupReservation) continue;

                if (outputPerson.withdrawDate) {
                    const withdrawDateValue = new moment.tz(outputPerson.withdrawDate, "MM/DD/YYYY", timezone).valueOf();
                    if (withdrawDateValue >= startDateNum && (!nextTransition||withdrawDateValue < nextTransition.scheduledDate)) {
                        nextTransition = { scheduledDate: withdrawDateValue };
                        outputPerson.destination = "Withdraw";
                    }   
                }
                if (nextTransition) {
                    outputPerson.transition = nextTransition.scheduledDate;
                    outputPerson.nextTransition = nextTransition;
                    const gId = nextTransition?.groupId ?? person.defaultGroupId;
                    const transitionGroup = _.find(availableGroups, (g) => g._id == gId);
                    if (transitionGroup && outputPerson?.destination != "Withdraw") {
                        outputPerson.destination = transitionGroup.name;
                        outputPerson.destinationGroupId = transitionGroup._id;
                    }
                }
                let personGroupEndDate = endDate.clone();
                let personGroupSlotsCount = 0, personGroupTransitionsCount = 0;
                _.each(daySlots, (daySlot, daySlotIndex) => {
                    const dateOfDay = startDate.clone().add(daySlotIndex, "days").format("YYYY-MM-DD");
                    if (_.contains(_.map(holidays, h => h.date), dateOfDay)) return;
                    if (!group.dayTotals[daySlot]) { group.dayTotals[daySlot] = {}; group.dayTotalsList[daySlot] = {}; }

                    outputPerson.daySlots[daySlot] = {};
                    outputPerson.nextTransitionSlots[daySlot] = {};

                    _.each(personReservations.filter(r => _.contains(r.recurringDays, daySlot)), matchedDayReservation => {
                        const matchedScheduleType = matchedDayReservation && scheduleTypes.find(st => st._id == matchedDayReservation.scheduleType);

                        let startDateSlot = 0, exitDateSlot = 7;
                        if (matchedDayReservation && matchedDayReservation.scheduledDate > startDateNum) {
                            startDateSlot = new moment(matchedDayReservation.scheduledDate).diff(startDate, "days");

                        }
                        
                        let exitDateSource;
                        if (nextTransition &&
                            outputPerson.destinationGroupId != group._id &&
                            nextTransition.scheduledDate < endDateNum &&
                            (!matchedDayReservation.scheduledEndDate || matchedDayReservation.scheduledEndDate > nextTransition.scheduledDate)) {
                            exitDateSlot = new moment(nextTransition.scheduledDate).diff(startDate, "days");
                            personGroupEndDate = startDate.clone().add(exitDateSlot, "days");
                            exitDateSource = "nextTransition";
                        } else if (matchedDayReservation?.scheduledEndDate && matchedDayReservation.scheduledEndDate < endDateNum) {
                            exitDateSlot = new moment(matchedDayReservation.scheduledEndDate).add(1, "days").diff(startDate, "days");
                            personGroupEndDate = startDate.clone().add(exitDateSlot, "days");
                            exitDateSource = "schedule";
                        }                        

                        const isNextTransition = nextTransition
                            && nextTransition._id == matchedDayReservation._id
                            && (nextTransition.groupId == group._id || (!nextTransition.groupId && (person.defaultGroupId == group._id || outputPerson.matchedByReservation)))
                            && nextTransition.scheduledDate > endDateNum;


                        if (isNextTransition || (matchedDayReservation.scheduledDate < endDateNum && daySlotIndex >= startDateSlot && (daySlotIndex < exitDateSlot || (outputPerson?.destination == "Withdraw" && exitDateSource == "nextTransition" && daySlotIndex == exitDateSlot)))) {

                            _.each(scheduleTypes.filter(st => st.startTime), st => {
                                const stLabel = st.startTime + " - " + st.endTime,
                                    presentInSlot = (matchedScheduleType && (matchedScheduleType._id == st._id || !matchedScheduleType.startTime)) ? true : false;

                                if (!isNextTransition) {
                                    outputPerson.daySlots[daySlot][stLabel] = outputPerson.daySlots[daySlot][stLabel] || presentInSlot;
                                    if (!group.dayTotals[daySlot][stLabel]) {
                                        group.dayTotals[daySlot][stLabel] = 0;
                                        group.dayTotalsList[daySlot][stLabel] = [];
                                    }
                                    if (presentInSlot) {
                                        group.dayTotals[daySlot][stLabel] += 1;
                                        group.dayTotalsList[daySlot][stLabel].push(outputPerson.name);
                                        personGroupSlotsCount++;
                                    }
                                } else if (presentInSlot) {
                                    outputPerson.nextTransitionSlots[daySlot][stLabel] = true;
                                    personGroupTransitionsCount++;
                                }
                            });

                        }

                    });
                });

                outputPerson.hasSameGroupTransition = personGroupSlotsCount > 0 && personGroupTransitionsCount > 0;
                if (!_.isEmpty(outputPerson.daySlots) || nextTransition?.groupId == group._id || outputPerson.groupId == group._id) {
                    if (personGroupSlotsCount > 0 || outputPerson.matchedByReservation || (!options.onlyCurrentSchedules && (personGroupTransitionsCount > 0 || person.defaultGroupId == group._id))) {
                        group.groupPeople.push(outputPerson);
                    }

                }
            }
            //END PEOPLE LOOP
            const averageFTE = (group.fteTotals / 5);
            group.weeklyFTEPercent = (group.capacity > 0) ? averageFTE / (group.preferredCapacity || group.capacity) * 100 : 0;
            group.totalEnrolled = group.slotTotals;
            group.staffNeeded = group.ratio > 0 ? Math.ceil(averageFTE / group.ratio) : 0;
            group.weeklyEnrollmentPercent = (group.totalCapacity > 0) && (group.totalEnrolled / group.totalCapacity * 100);
            group.averageFTE = averageFTE;

            const groupPayroll = await group.getGroupPayroll();
            if(groupPayroll !== undefined) {
                output.summaries.totalPayroll += groupPayroll.weeklyPayroll;
            }
            output.groups.push(group);
        }

        output.summaries.idealRevenuePercent = org?.forecasting?.idealRevenue || 100;
        output.summaries.idealRevenue = output.summaries.totalFullTimeRevenue * (output.summaries.idealRevenuePercent / 100);
        output.summaries.varianceToIdealRevenue = output.summaries.totalCurrentRevenue - output.summaries.idealRevenue;
        output.summaries.totalPayrollPercentage = output.summaries.totalCurrentRevenue && (output.summaries.totalPayroll / output.summaries.totalCurrentRevenue * 100);
        output.summaries.targetPayrollPercentage = org?.forecasting?.targetPayroll || 0;
        output.summaries.targetPayrollVariance = output.summaries.targetPayrollPercentage - output.summaries.totalPayrollPercentage;
        
        return output;
    }


    static async GenerateReport(){
		
		const org = await Orgs.current(); 
		let timezone = org.getTimezone();
		const prefix = org.profileDataPrefix(); // Check Prefix "profileData" or random fields
		const startMonth = new moment().format("MM/DD/YYYY"); 
		let todayDate = new moment.tz(startMonth, "MM/DD/YYYY", timezone)
		const results = await WaitListReport.classListScheduleBased({startDate: startMonth, includeChildDetails: true, onlyReservationPeople: true });
		const designatedGroupIdsData = await Groups.find({orgId:org._id, includeClassList: true}, {fields:{_id:1}}).fetchAsync();
        const designatedGroupIds = designatedGroupIdsData.map( g => g._id);
		results.groups = _.sortBy(_.filter(results.groups, (g) => _.contains(designatedGroupIds, g._id)) , (g) => g.sortOrder);
		let dayMomentValue = new moment.tz(startMonth, "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf(),
			scheduledDateQueryValue = new moment(dayMomentValue).endOf('week').endOf('day').add(2, 'months').valueOf(),
            resQuery = {
                orgId: org._id,
                scheduledDate: { "$lte": scheduledDateQueryValue },
                "$or": [ {scheduledEndDate: null}, {scheduledEndDate: {"$gt": dayMomentValue}} ],
                recurringFrequency: {"$exists" : true }
            },
			allReservations = await Reservations.find(resQuery).fetchAsync();

		results.waitListTotals = {
			capacity: 0,
			preferredCapacity: 0,
			enrolled: 0,
			variance: 0,
			occupancy: 0,
			unassignedPeople: [],
		};

        let ignoreArray = [];

		for (const group of results.groups) {
			const currentFteResult = await getFteForMonth({
				startMonth,
				orgId: group.orgId,
				groupId: group._id,
				allReservations,
			});

			const preferredCapacity = isNaN(parseInt(group.preferredCapacity)) ? 1 : parseInt(group.preferredCapacity);
			const regCapacity = isNaN(parseInt(group.capacity)) ? 1 : parseInt(group.capacity);
			group.fteTotals = 0;

			group.groupPeople = _.sortBy( group.groupPeople, gp => gp.birthday);

			for (const person of group.groupPeople) {
				const fteResult = _.find(currentFteResult.groupChildren, (gc) => gc._id == person._id );
				if (fteResult) {
					person.currentFte = fteResult?.currentFte ?? 0;
					person.scheduleLabel = fteResult?.scheduleLabel ?? null;
					person.scheduleLabelDaysOnly = fteResult?.scheduleLabelDaysOnly ?? null;
					group.fteTotals += person.currentFte;
				}
			}

			group.totalOccupancy = (group.fteTotals / preferredCapacity * 100).toFixed(2);
			group.totalVariance = (group.fteTotals - preferredCapacity).toFixed(2);
			results.waitListTotals.capacity += regCapacity;
			results.waitListTotals.preferredCapacity += preferredCapacity;
			results.waitListTotals.enrolled += group.fteTotals;
			group.prospects = [];
			
		};


        resQuery = {
            orgId: org._id,
            $or: [{ scheduledEndDate: null }, { scheduledEndDate: { "$gte": todayDate.valueOf() } }],
            recurringFrequency: { "$exists": true }
        }, allReservations = await Reservations.find(resQuery).fetchAsync();
        const allReservationsResults = await Promise.all(allReservations.map(async r => {
            let p = await People.findOneAsync({_id:r.selectedPerson})
            if( p?.designations && p.designations.includes("Wait List") && r.scheduledDate > todayDate.valueOf() ){
                return false
            }else{
                return true
            }
        }));
        allReservations = allReservations.filter((_,index) => allReservationsResults[index] );
		ignoreArray = allReservations.map(r => r.selectedPerson)

		for (const group of results.groups) {
			const prospects = await People.find({_id:{$nin:ignoreArray},orgId: org._id, type: { $in: ["prospect", "person"]}, designations: {$in: ["Wait List"]}, targetGroupId: group._id, inActive: { $ne: true }}, {sort: {waitlistAddedDate: 1}}).fetchAsync();
			for (const person of prospects) {
				const outputPerson = {
					_id: person._id,
					name: person.lastName + ", " + person.firstName,
					birthday: prefix ? (person[prefix] && person[prefix].birthday) : person.birthday,
					age:  await person.calcAge(),
					destination: group.name,
					daySlots: {},
					scheduleLabel: prefix ? (person[prefix] && person[prefix].preferredSchedule) : person.preferredSchedule,
					waitlistAddedDate: person.waitlistAddedDate,
					desiredStartDate: prefix ? (person[prefix] && person[prefix].desiredStartDate) : person.desiredStartDate,
					anticipatedStartDate: prefix ? (person[prefix] && person[prefix].anticipatedStartDate) : person.anticipatedStartDate,
					enrollmentDate: prefix ? (person[prefix] && person[prefix].enrollmentDate) : person.enrollmentDate
				}
				group.prospects.push(outputPerson)
			};

		   
		};

        

		const unassignedProspects = await People.find({_id:{$nin:ignoreArray},orgId: org._id, type: { $in: ["prospect", "person"]}, designations: {$in: ["Wait List"]}, inActive: { $ne: true }, $or: [{targetGroupId: ""}, {targetGroupId: {$exists: false}}]}, {sort: {waitlistAddedDate: 1}}).fetchAsync();
		const unassignedPeople = [];
		for (const person of unassignedProspects) {
			unassignedPeople.push({
				_id: person._id,
				name: person.lastName + ", " + person.firstName,
				birthday: prefix ? (person[prefix] && person[prefix].birthday) : person.birthday,
				age: await person.calcAge(),
				destination: "",
				daySlots: {},
				scheduleLabel: prefix ? (person[prefix] && person[prefix].preferredSchedule) : person.preferredSchedule,
				waitlistAddedDate: person.waitlistAddedDate,
				desiredStartDate: prefix ? (person[prefix] && person[prefix].desiredStartDate) : person.desiredStartDate,
				anticipatedStartDate: prefix ? (person[prefix] && person[prefix].anticipatedStartDate) : person.anticipatedStartDate,
			});
		}

		results.waitListTotals.unassignedPeople = unassignedPeople;
		results.waitListTotals.variance = (results.waitListTotals.preferredCapacity - results.waitListTotals.enrolled).toFixed(2);
		results.waitListTotals.occupancy = (results.waitListTotals.enrolled / results.waitListTotals.preferredCapacity * 100).toFixed(2);
		results.waitListTotals.enrolled = (results.waitListTotals.enrolled).toFixed(2);
		return results;
    }
    

}
		