import { SSR } from '../lib/util/ssrUtils';
import { Blaze } from 'meteor/blaze';
import { People } from '../lib/collections/people';
import { generateEmailWhiteLabelData } from './util';
import { Orgs } from '../lib/collections/orgs';
import { Messages } from '../lib/collections/messages';
import { sendExpoPushNotification } from './expoService';
import { NewMessages } from '../lib/collections/newMessages';
import moment from 'moment-timezone';
import _ from '../lib/util/underscore';
import { sendPushNotification } from './snsHelper';

export const processMessageNotifications = async function (messageId) {
  console.log('got here with messageid:', messageId);

  let currentMessage = await Messages.findOneAsync(messageId);
  if (currentMessage && !currentMessage.realtimeProcessed) {
    const totalRecipientList = _.union([currentMessage.personId], currentMessage.currentRecipients);
    const currentRecipientList = _.difference(totalRecipientList, currentMessage.markedAsRead);
    for (const recipientId of currentRecipientList) {
      const currentRecipient = await People.findOneAsync(recipientId);
      console.log('found current Recipient to process', recipientId);
      await NewMessages.upsertAsync(
        { personId: recipientId },
        { $set: { hasNotification: true }, $setOnInsert: { personId: recipientId } }
      );
      const currentOrgId = currentRecipient.orgId;
      const currentOrg = await Orgs.findOneAsync(currentOrgId);
      if (
        currentRecipient.type === 'staff' &&
        currentOrg.hasCustomization('messages/suppressStaffMessageCenterNotifications/enabled')
      ) {
        continue;
      }

      let subjectOrg = 'LineLeader';
      if (currentOrg && currentOrg.whiteLabel) {
        subjectOrg = currentOrg.getLongName();
      }

      const userEmails = await currentRecipient.getActiveUserEmailAddress();
      if (userEmails) {
        console.log('processing email for recipient');
        SSR.compileTemplate('messageEmail', await Assets.getTextAsync('email_templates/v2021/message_email.html'));

        Blaze.Template.messageEmail.helpers({
          equals: function (val1, val2) {
            return val1 === val2 ? true : false;
          },
          formatTime: function (val) {
            return val.toUpperCase();
          },
          appUrl: function () {
            var baseUrl =
              (currentOrg && currentOrg.whiteLabel && currentOrg.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
            if (baseUrl.slice(baseUrl.length - 1) != '/') baseUrl += '/';
            return baseUrl;
          }
        });

        var emailData = {
          message: currentMessage,
          person: currentRecipient,
          recipientId: currentRecipient._id,
          emailDateStamp: new moment().tz(currentOrg.getTimezone()).format('dddd MMMM Do, YYYY')
        };

        const whiteLabel = generateEmailWhiteLabelData(currentOrg);
        emailData.whiteLabel = whiteLabel;
        emailData.backgroundColor = `${whiteLabel.primaryColor}1A`;
        emailData.headerOrgNameColor = '#8E8E93';
        emailData.headerBgColor = whiteLabel.primaryColor;
        emailData.secondaryColor = whiteLabel.secondaryColor;
        emailData.assetPrefix = `emailAssets/${whiteLabel.emailAssetPrefix}`;
        emailData.currentYear = new moment().format('YYYY');

        const emailOptions = {
          from: 'LineLeader support <<EMAIL>>',
          to: userEmails,
          subject: `You have a new message waiting in ${subjectOrg}`,
          html: SSR.render('messageEmail', emailData)
        };
        if (currentOrg.replyToAddress) emailOptions.replyTo = currentOrg.replyToAddress;
        if (currentOrg.fromAddress) emailOptions.from = currentOrg.fromAddress;
        try {
          await Email.sendAsync(emailOptions);
        } catch (e) {
          console.log(e);
        }
      }
      const targets = await currentRecipient.getPushTargets();
      if (currentRecipient && targets) {
        console.log('processing push for real time message center recipient');

        for (const target of targets) {
          const message = {
            title: `${subjectOrg} - New Message`,
            body: `You have a new message in the ${subjectOrg} message center.`
          };
          if (target && target?.endpointArn) {
            await sendPushNotification(target.endpointArn, message);
          } else if (target) {
            await sendExpoPushNotification({
              target,
              message
            });
          }
        }
      }
    }
  }
};
