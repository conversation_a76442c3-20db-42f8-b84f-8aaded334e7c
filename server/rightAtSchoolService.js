import { Meteor } from 'meteor/meteor';
import { fetch } from "meteor/fetch"
import {AvailableCustomizations} from "../lib/customizations";
import { People } from "../lib/collections/people";
import { Orgs } from "../lib/collections/orgs";
import { Reservations } from "../lib/collections/reservations";
const baseUrl = Meteor.settings.rightAtSchoolUrl;
const baseWehookUrl = Meteor.settings.rightAtSchoolWebhookUrl;

export class RightAtSchoolService {
    static async validateEmployeeId (employeeId) {
        if(!employeeId){
            return;
        }
        let response = await fetch(`${baseUrl}?employeeCode=${employeeId}`);
        const rightAtSchoolResponse = await response.text();
        return rightAtSchoolResponse === 'Active';
    }

    static async populateData(personId, actionString) {
        const person = await People.findOneAsync({_id: personId});
        if (!person) {
            return
        }
        const org = person.orgId && await Orgs.findOneAsync({_id: person.orgId});
        if (!org || !org.hasCustomization(AvailableCustomizations.RAS_WEBHOOK_UPDATES)) {
            return;
        }
        const reservations = await Reservations.find(
            {selectedPerson: personId},
            {orgId: org._id},
            {readPreference:"secondaryPreferred"},
        ).fetchAsync();
        const data = {
            id: personId,
            action: actionString,
            child: person,
            billing: person.billing ? person.billing : [],
            schedules: reservations && reservations.length ? reservations : []
        }
        const headers = { 'Content-Type': 'application/json'};

        await fetch(`${baseWehookUrl}`, {
          method: 'POST',
          body: JSON.stringify(data),
          headers
        });
    }
}

// allow usage in lib/* files
export const RasService = RightAtSchoolService;
