import { Meteor } from 'meteor/meteor';
import { BillingUtils } from "../../lib/util/billingUtils";
import logger from "../../imports/winston/index"
import { Client, CheckoutAPI, RecurringAPI, Platforms } from '@adyen/api-library';
import { CardDetails, AchDetails, Split, PaymentRequest, PaymentRefundResponse } from "@adyen/api-library/lib/src/typings/checkout/models";
import { Recurring } from "@adyen/api-library/lib/src/typings/recurring/recurring";
import Papa from 'papaparse';
import currency from 'currency.js';
import { ConfigurationSettingsService, ServerConfigurationConstants } from '../config/configurationSettingsService';
import { Log } from '../../lib/util/log';
import { HTTP } from 'meteor/http';
import { HistoryAuditChangeTypes } from '../../lib/constants/historyAuditConstants';
import { HistoryAuditService } from '../historyAuditService';
import { AdyenReportsService } from "../adyen/adyenReportsService";
import { TopUpsUtil } from "../../lib/topUpsUtil";
import { EmailService } from '../emails/emailService';
import { AdyenNotifications } from "../collections/adyenNotifications";
import { AdyenReports } from "../../lib/collections/adyenReports";
import { AdyenTransactions } from "../../lib/collections/adyenTransactions";
import { People } from "../../lib/collections/people";
import { Orgs } from "../../lib/collections/orgs";
import { Invoices } from "../../lib/collections/invoices";
import { processBillingEmail } from "../processBillingEmail";
import moment from "moment-timezone";
import _ from "../../lib/util/underscore";
import { CustomerChargebacksInvoices } from "../../lib/collections/customerChargebacksInvoices";
import { AdyenPayouts } from "../collections/adyenPayouts";
import { AdyenBalancePaymentProvider } from './adyenBalancePaymentProvider';
import { cloneDeep } from 'lodash';

export const ADYEN_WEBHOOKS = {
	TRANSFER_FUNDS: 'TRANSFER_FUNDS',
	DIRECT_DEBIT_INITIATED: "DIRECT_DEBIT_INITIATED",
};
Object.freeze(ADYEN_WEBHOOKS);

export const ADYEN_WEBHOOK_STATUS_CODES = {
	FAILED: 'Failed',
	SUCCESS: 'Success'
};
Object.freeze(ADYEN_WEBHOOK_STATUS_CODES);

export class AdyenProvider {
	static getApiKey() {
		return ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_API_KEY);
	}
	static getApiClient() {
		const liveEndpointUrlPrefix = _.deep(Meteor.settings, "public.adyen.environmentPrefix");
		const environment = _.deep(Meteor.settings, "public.adyen.environment") || "TEST",
		serverEnvironmentKey = environment.startsWith("live") ? "LIVE" : "TEST";

		const client = new Client({
			apiKey: AdyenProvider.getApiKey(),
			environment: serverEnvironmentKey,
			liveEndpointUrlPrefix
		});

		console.log("access settings", serverEnvironmentKey, liveEndpointUrlPrefix);

		return client;
	}

	static async addBankAccount(options) {

		const client = AdyenProvider.getApiClient();
		const checkout = new CheckoutAPI(client);

		const reference = "org_" + options.orgId + "_bank_account_" + new moment().valueOf();

		const currentOrg = await Orgs.findOneAsync(options.orgId);
		const returnUrl = _.deep(currentOrg, "whiteLabel.ROOT_URL") || "https://app.momentpath.com";

		const params = {
			amount: {
				currency: "USD",
				value: 0,
			},
			storePaymentMethod: true,
			reference,
			shopperReference: options.personId,
			countryCode: "US",
			channel: PaymentRequest.ChannelEnum.Web,
			merchantAccount: 'TendlyLLCDbaMomentPathMP',
			shopperInteraction: PaymentRequest.ShopperInteractionEnum.Ecommerce,
			recurringProcessingModel: PaymentRequest.RecurringProcessingModelEnum.Subscription,
			paymentMethod: {
				type: AchDetails.TypeEnum.Ach,
				bankAccountNumber: options.bankAccountNumber,
				bankLocationId: options.bankLocationId,
				ownerName: options.ownerName
			},
			returnUrl
		};

		console.log("adyen params", params);
		let paymentsResponse;
		try {
			paymentsResponse = await checkout.PaymentsApi.payments(params);
		}
		catch (e) {
			console.log("adyen exception", JSON.stringify(e, null, 2));
			return {error: e.message, errorDetails: e};
		}

		console.log("adyen response", JSON.stringify(paymentsResponse,null,2));
		if (paymentsResponse.resultCode == "Authorised") {
			if (paymentsResponse.additionalData) {
				paymentsResponse.last4 = paymentsResponse.additionalData.bankSummary;
				paymentsResponse.status = "verified";

				if (paymentsResponse.additionalData.bankVerificationResultRaw?.startsWith("RT00")) {
					const currentPerson = await People.findOneAsync({_id:options.personId});
					if (currentPerson?.type == "family")
						return {"error": "Could not verify bank account", errorDetails: paymentsResponse.additionalData.bankVerificationResultRaw};
				}
			}
			//clean up adyen keys to remove periods
			paymentsResponse.mappedAdditionalData = {};

			_.each(paymentsResponse.additionalData, (v, k) => {
				const mappedKey = k.replace(/\./g, "_");
				paymentsResponse.mappedAdditionalData[mappedKey] = v;
			});

			delete paymentsResponse.additionalData;
			const setKey = "billing.adyenInfo.sources.bank_account",
				updateQuery = {"$set": {}};
			updateQuery["$set"][setKey] = paymentsResponse;
			await People.updateAsync( { _id: options.personId }, updateQuery );

		} else {

			return {error: paymentsResponse.resultCode, errorDetails: paymentsResponse}
		}
		return {result: "success", resultDetails: paymentsResponse};
	}

	static getBasicAuthClient() {
		const environment = _.deep(Meteor.settings, "public.adyen.environment") || "TEST",
			serverEnvironmentKey = environment.startsWith("live") ? "LIVE" : "TEST";
		return new Client({
			username: _.deep(Meteor.settings, "adyen.mpapiuser"),
			password: _.deep(Meteor.settings, "adyen.mpapipw"),
			environment: serverEnvironmentKey
		});
	}

	/**
	 * Get the payment amount object that should be applied to the invoice for the given person.
	 *
	 * @param {Invoice} invoice
	 * @param {Person} person
	 * @param {Org} org
	 * @param {number} originalPaymentAmount
	 * @param {bool} overridePaymentLimit
	 * @returns {{overpaymentAmount: number, appliedAmount: number}}
	 */
	static getPersonPaymentAmountForInvoice(invoice, person, org, originalPaymentAmount, overridePaymentLimit) {
		let overpaymentAmount = 0.00;
		let appliedAmount = originalPaymentAmount;
		const personAmountDue = invoice.amountDueForFamilyMember(person._id);
		const invoiceOpenAmount = BillingUtils.roundToTwo(invoice.openAmount);
		if (invoice.getFamilySplits() && originalPaymentAmount > personAmountDue) {
			if (!overridePaymentLimit || org.hasCustomization("billing/preventOverpayment/enabled")) {
				throw new Meteor.Error('Family Invoice Overpayment', "You cannot pay more than is due for this invoice.");
			}
			appliedAmount = personAmountDue;
			overpaymentAmount = roundToTwo(originalPaymentAmount - personAmountDue);
        } else if (originalPaymentAmount > invoiceOpenAmount) {
			if (!overridePaymentLimit || org.hasCustomization("billing/preventOverpayment/enabled")) {
				throw new Meteor.Error('Invoice Overpayment', "You cannot pay more than is due for this invoice.");
			}
			appliedAmount = invoiceOpenAmount;
			overpaymentAmount = roundToTwo(originalPaymentAmount - invoiceOpenAmount);
		}

		return {
			appliedAmount,
			overpaymentAmount,
		}
	}

	static async payInvoice(options) {
		const currentUser = options.currentUser || null;
		const currentPerson = await People.findOneAsync({ _id: options.personId});
		const invoice = await Invoices.findOneAsync({_id: options.invoiceId});
		const currentOrg = options.orgId ? await Orgs.findOneAsync({_id:options.orgId}) : await Orgs.current();

		const paidByDesc = currentPerson.firstName + " " + currentPerson.lastName;

		let original_payment_amount = options.payment_amount, payment_amount = original_payment_amount, service_charge = 0.00;

		// check for payments-before-due-date-flag
		const todayDateStamp = new moment.tz(currentOrg.getTimezone()).startOf('day').valueOf();
		if (_.deep(currentOrg, "billing.scheduling.disablePaymentsBeforeDueDate") && todayDateStamp < invoice.dueDate)
			throw new Meteor.Error(500, "We are unable to accept payments until the invoice due date.");

		// restrict payments under $1
		if (parseFloat(payment_amount) < 1)
			throw new Meteor.Error(500, "We are unable to accept payments for less than $1.");

		// handle passthrough fees
		const orgPaymentFees = currentOrg.billing.paymentFees,
			cardFee = (orgPaymentFees && orgPaymentFees.cardFee != null) ? orgPaymentFees.cardFee : 0.30,
			cardRate = (orgPaymentFees && orgPaymentFees.cardRate) || 0.029,
			achFee = (orgPaymentFees && orgPaymentFees.achFee != null) ? orgPaymentFees.achFee : 0.40,
			merchantFeeCalculated = options.account_type == "bank_account" ? achFee : (parseFloat(payment_amount) * cardRate) + cardFee;

		if (currentOrg.billing.passthroughFees && (!currentOrg.billing.passthroughFeesAccountTypes || _.contains(currentOrg.billing.passthroughFeesAccountTypes, options.account_type))) {
			//const transaction_fee = options.account_type == "bank_account" ? achFee : cardFee;
			//const transaction_percent = options.account_type == "bank_account" ? 0 : cardRate;
			payment_amount = Math.round(((parseFloat(options.payment_amount) + merchantFeeCalculated) + Number.EPSILON) * 100) / 100;
			service_charge = merchantFeeCalculated.toFixed(2);
			if (options.account_type == "bank_account" && service_charge > 5) {
				payment_amount = options.payment_amount - 5.00;
				service_charge = 5.00;
			}
		}

		// Handle overpayment
		const paymentAmountObj = AdyenProvider.getPersonPaymentAmountForInvoice(invoice, currentPerson, currentOrg, original_payment_amount, options.overridePaymentLimit);
		const overpayment_amount = paymentAmountObj.overpaymentAmount;
		const applied_amount = paymentAmountObj.appliedAmount;

		// ADD CHECK FOR INVOICE PERMISSIONS
		const client = AdyenProvider.getApiClient();
		const checkout = new CheckoutAPI(client);
		const currentSource = options.account_type == "card" ?
			currentPerson.billing.adyenInfo.sources.card :
			currentPerson.billing.adyenInfo.sources.bank_account;
		if (!currentSource) throw new Meteor.Error(500, "No matching payment source found: ", options.account_type);

		const totalAmount = Math.round(payment_amount*100),
			merchantFee = Math.round(merchantFeeCalculated * 100),
			merchantAmount = totalAmount - merchantFee;

		console.log("totalAmount", totalAmount, "merchantFee", merchantFee, "merchantAmount", merchantAmount);
		const chargeOptions = {
			amount: {
				value: totalAmount,
				currency: 'USD'
			},
			paymentMethod: {
				type: options.account_type == "card" ? CardDetails.TypeEnum.Scheme : AchDetails.TypeEnum.Ach,
				recurringDetailReference: currentSource.mappedAdditionalData.recurring_recurringDetailReference
			},
			reference: options.invoiceId,
			merchantAccount: 'TendlyLLCDbaMomentPathMP',
			shopperInteraction: PaymentRequest.ShopperInteractionEnum.ContAuth,
			recurringProcessingModel: PaymentRequest.RecurringProcessingModelEnum.Subscription,
			metadata: {
				paidBy: options.personId,
				paidByDesc: paidByDesc,
				invoiceId: options.invoiceId,
				invoiceNumber: invoice.invoiceNumber
			},
			shopperStatement: currentOrg.name + " #" + invoice.invoiceNumber,
			shopperReference: currentSource.mappedAdditionalData.recurring_shopperReference, //***change to shopperReference currentPerson.billing.adyenInfo.sources[options.account_type].merchantReference
			splits: [{
					"amount":{
						"value":merchantAmount,
					},
					"type": Split.TypeEnum.MarketPlace,
					"account": currentOrg.billing.adyenInfo.accountCode,
					"reference": currentOrg._id + '_' + options.invoiceId
				},
				{
					"amount":{
						"value": merchantFee,
					},
					"type": Split.TypeEnum.Commission,
					"reference": 'fee_' + currentOrg._id + '_' + options.invoiceId
				}
		  	],
			returnUrl: _.deep(currentOrg, "whiteLabel.ROOT_URL") || "https://app.momentpath.com"
			//customer: currentPerson.billing.stripeInfo.id,
			//description: invoice.invoiceNumber,
			//metadata: { invoiceId: options.invoiceId, invoiceNumber: options.invoiceNumber, paidBy: paidByDesc }
		};
		console.log("chargeOptions", chargeOptions);

		let charge;
		try {
			charge = await checkout.PaymentsApi.payments(chargeOptions);
		} catch (error) {
			console.log("adyen error", error);
			let message = "We're sorry, but we're encountering an issue with payment processing. Please try again later.";
			if (error.errorCode && error.errorCode == '803')
				message = "Your payment could not be processed at this time.  It is possible you are using an unsupported payment method.  Please try to setup your payment method again or try another payment method. (Code 803)";
			throw new Meteor.Error(500, message);
		}

		console.log("Charge result: ", charge);
		//clean up adyen keys to remove periods
		charge.mappedAdditionalData = {};
		_.each(charge.additionalData, (v, k) => {
			const mappedKey = k.replace(".","_");
			charge.mappedAdditionalData[mappedKey] = charge.additionalData[k];
		});
		delete charge.additionalData;
		charge.createdAt = new Date().valueOf();
		charge.totalAmount = payment_amount;
		charge.paidByDesc = paidByDesc;

		Invoices.updateByIdWithJournalEntry(options.invoiceId, {
			$push: { paymentTransactions: charge }
		}, {
			userId: currentUser ? currentUser._id : 'SYSTEM',
			personId: currentUser ? currentUser.personId : '',
			orgId: currentOrg._id,
			reason: `Executed payInvoice callback (1)`,
			reasonLocation: 'server/card_providers/adyenProvider.js:payInvoice'
		});

		if (charge.resultCode == "Authorised") {

			const paymentLine = {
				type: "payment",
				payment_type: options.account_type,
				amount: applied_amount,
				overpaymentAmount: overpayment_amount,
				serviceCharge: service_charge,
				createdAt: new Date().valueOf(),
				paidBy: options.personId,
				paidByDesc: paidByDesc,
				adyenInfo: charge
			};
			Invoices.updateByIdWithJournalEntry(options.invoiceId, {
				$set: { openAmount: currency(invoice.openAmount).subtract(applied_amount).value, paymentRefused: false },
				$push: { credits: paymentLine }
			}, {
				userId: currentUser ? currentUser._id : 'SYSTEM',
				personId: currentUser ? currentUser.personId : '',
				orgId: currentOrg._id,
				reason: `Executed payInvoice callback (2)`,
				reasonLocation: 'server/card_providers/adyenProvider.js:payInvoice'
			});
			if (overpayment_amount > 0) {
				const overpaymentDescription = "Overpayment on invoice " + invoice.invoiceNumber
					+ " with " + (options.account_type == "card" ? "card" : "bank account") + " txn #" + charge.pspReference;
				await People.updateAsync({_id: options.personId}, {$push: {"billing.creditMemos": {
					_id: Random.id(),
					type: "systemOverpayment",
					createdAt: new moment().valueOf(),
					createdBy: currentPerson._id,
					notes: overpaymentDescription,
					openAmount: parseFloat(overpayment_amount),
					originalAmount: parseFloat(overpayment_amount)
				}}});
			}
			return paymentLine;
		} else if (charge.resultCode == "Refused") {
			console.log("Charge Refused ", charge);
			Invoices.updateByIdWithJournalEntry(options.invoiceId, {
				$set: { paymentRefused: true }
			}, {
				userId: currentUser ? currentUser._id : 'SYSTEM',
				personId: currentUser ? currentUser.personId : '',
				orgId: currentOrg._id,
				reason: `Payment Refused`,
				reasonLocation: 'server/card_providers/adyenProvider.js:payInvoice'
			});
			Meteor.defer(async function() {
				await processBillingEmail({emailType: "refusal_notice", paidByPersonId: currentPerson._id, invoiceId: invoice._id, charge, totalAmount: payment_amount, accountType: options.account_type});
			});
			throw new Meteor.Error(500, `The payment method was refused: ${charge?.refusalReason}`);
		} else {
			console.log("Adyen processing error:", charge);
			throw new Meteor.Error(500, "There was an error processing your payment. Please try again later.");
		}
	}

	static async payOrgInvoice(options) {
		const currentOrg = options.orgId ? await Orgs.findOneAsync({_id:options.orgId}) : await Orgs.current();

		const client = AdyenProvider.getApiClient();
		const checkout = new CheckoutAPI(client).PaymentsApi;
		const currentSource = currentOrg.planPaymentSource,
			accountType = currentSource.mappedAdditionalData.paymentMethod == "ach" ? "ach" : "card" ;
		if (!currentSource) throw new Meteor.Error(500, "No matching payment source found: ", options.account_type);

		const payment_amount = options.amount,
			totalAmount = Math.round(payment_amount*100),
			invoiceDate = new moment().format("MM/DD/YYYY"),
			invoiceId = options.invoiceId || Random.id();

		let paidByDesc;
		if (options.createdForPersonId) {
			const person = await People.findOneAsync({_id:options.createdForPersonId});
			if (person) paidByDesc = person.firstName + " " + person.lastName;
		}

		const chargeOptions = {
			amount: {
				value: totalAmount,
				currency: 'USD'
			},
			paymentMethod: {
				type: accountType == "card" ? CardDetails.TypeEnum.Scheme : AchDetails.TypeEnum.Ach,
				recurringDetailReference: currentSource.mappedAdditionalData.recurring_recurringDetailReference
			},
			reference: invoiceId,
			merchantAccount: 'TendlyLLCDbaMomentPathMP',
			shopperInteraction: PaymentRequest.ShopperInteractionEnum.ContAuth,
			recurringProcessingModel: PaymentRequest.RecurringProcessingModelEnum.Subscription,
			metadata: {
				paidBy: options.createdForPersonId,
				paidByDesc,
				invoiceId: invoiceId,
				invoiceDate
			},
			shopperStatement: "MomentPath Subscription " + invoiceDate,
			shopperReference: currentSource.mappedAdditionalData.recurring_shopperReference, //***change to shopperReference currentPerson.billing.adyenInfo.sources[options.account_type].merchantReference
			splits: [{
					"amount":{
						"value":totalAmount,
					},
					"type": Split.TypeEnum.MarketPlace,
					"account": Meteor.settings.adyen.defaultAccountCode,
					"reference": currentOrg._id + '_' + options.invoiceId
				}
		  	],
			returnUrl: _.deep(currentOrg, "whiteLabel.ROOT_URL") || "https://app.momentpath.com"
		};
		console.log("chargeOptions", chargeOptions);

		let charge;
		try {
			charge = await checkout.payments(chargeOptions);
		} catch (error) {
			console.log("adyen error", error);
			let message = "We're sorry, but we're encountering an issue with payment processing. Please try again later.";
			if (error.errorCode && error.errorCode == '803')
				message = "Your payment could not be processed at this time.  It is possible you are using an unsupported payment method.  Please try to setup your payment method again or try another payment method. (Code 803)";
			throw new Meteor.Error(500, message);
		}

		console.log("Charge result: ", charge);

		if (charge.resultCode == "Authorised") {
			//clean up adyen keys to remove periods
			charge.mappedAdditionalData = {};
			_.each(charge.additionalData, (v, k) => {
				const mappedKey = k.replace(".","_");
				charge.mappedAdditionalData[mappedKey] = charge.additionalData[k];
			});
			delete charge.additionalData;
			const paymentLine = {
				type: "payment",
				payment_type: accountType,
				amount: payment_amount,
				createdAt: new Date().valueOf(),
				paidBy: options.createdForPersonId,
				adyenInfo: charge
			};
			if (!options.invoiceId) {
				await OrgInvoices.insertAsync({
					_id: invoiceId,
					orgId: currentOrg._id,
					openAmount: 0,
					originalAmount: payment_amount,
					description: options.description,
					credits: [paymentLine],
					createdAt: new Date().valueOf(),
					invoiceDate
				});
			} else {
				await OrgInvoices.updateAsync({_id: options.invoiceId},
					{$inc: {openAmount: payment_amount * -1},
						$push: {credits: paymentLine}
					},
				);
			}
			return paymentLine;
		} else if (charge.resultCode == "Refused") {
			console.log("Charge Refused ", charge);
			throw new Meteor.Error(500, "The payment method was refused.");
		} else {
			console.log("Adyen processing error:", charge);
			throw new Meteor.Error(500, "There was an error processing your payment. Please try again later.");
		}
	}

	static async refundCharge(options) {
		const currentUser = options.currentUser || null;
		const currentOrg = options.orgId ? await Orgs.findOneAsync({_id:options.orgId}) : await Orgs.current();

		const client = AdyenProvider.getApiClient();
		const modification = new CheckoutAPI(client).ModificationsApi;
		let refund;

		try {
			 refund = await modification.refundCapturedPayment(options.charge_id, {
				merchantAccount: 'TendlyLLCDbaMomentPathMP',
				amount: {
					"value": options.refund_amount * 100,
					"currency": "USD"
				},
				reference: options.invoiceId,
				splits:[
					{
						"amount":{
							"value":options.refund_amount * 100
						},
						"type": Split.TypeEnum.MarketPlace,
						"account": currentOrg.billing.adyenInfo.accountCode,
						"reference": currentOrg._id + '_refund_' + options.invoiceId
					}
				]
	//			metadata: {
	//				invoiceId: options.invoiceId,
	//				chargeDesc: options.chargeDesc
	//			}
			});
		} catch (err) {
			console.log("Error with refund:", err);
			throw new Meteor.Error(500, "There was an error processing the refund.");
		}

		if (refund && refund.status == PaymentRefundResponse.StatusEnum.Received) {
			const invoice = await Invoices.findOneAsync({_id: options.invoiceId});
			const creditLine = invoice.credits.find( c => c.type == "payment" && c?.adyenInfo?.pspReference == options.charge_id);
			if (options.isRetry) {
				const refundLineIndex = options.retryCreditIndex;
				const refundLineUpdateAccessor = `credits.${refundLineIndex}.`;
				const updateQuery = {};

				updateQuery[refundLineUpdateAccessor + "refundPended"] = false;
				updateQuery[refundLineUpdateAccessor + "refundLastAttempted"] = new Date().valueOf();
				updateQuery[refundLineUpdateAccessor + "adyenInfo"] = refund;
				await Invoices.updateByIdWithJournalEntry(invoice._id, { "$set": updateQuery }, {
					userId: currentUser ? currentUser._id : 'SYSTEM',
					personId: currentUser ? currentUser.personId : '',
					orgId: currentOrg._id,
					reason: `Executed refundCharge callback (1) ... charge ID: ${options.charge_id}`,
					reasonLocation: 'server/card_providers/adydenProvider.js:refundCharge'
				});

				await HistoryAuditService.logRefund({
					amount: parseFloat(options.refund_amount),
					invoiceNumber: invoice.invoiceNumber,
					invoiceId: invoice._id,
					parentId: creditLine.paidBy,
					childId: invoice.personId,
					performedByUser: currentUser,
				});
				return refund;
			} else {
				const refundLine = {
						type: "refund",
						amount: parseFloat(options.refund_amount),
						createdAt: new Date().valueOf(),
						creditedBy: options.creditedByPersonId,
						creditReason: options.refund_reason,
						creditNote: options.refund_note,
						originalReference: options.charge_id,
						refundedToPersonId: creditLine.paidBy,
						adyenInfo: refund,
						refundConfirmed: false,
						refundLastAttempted: new Date().valueOf()
					}
				const updateQuery = {$push: {credits: refundLine}};
				if (!options.noChangeOpenAmount) {
					updateQuery["$inc"] = {openAmount: roundToTwo(parseFloat(options.refund_amount))}
				}

				await Invoices.updateByIdWithJournalEntry(options.invoiceId, updateQuery, {
					userId: currentUser ? currentUser._id : 'SYSTEM',
					personId: currentUser ? currentUser.personId : '',
					orgId: currentOrg._id,
					reason: `Executed refundCharge callback (2) ... charge ID: ${options.charge_id}`,
					reasonLocation: 'server/card_providers/adydenProvider.js:refundCharge'
				});

				await HistoryAuditService.logRefund({
					amount: parseFloat(options.refund_amount),
					invoiceNumber: invoice.invoiceNumber,
					invoiceId: invoice._id,
					parentId: creditLine.paidBy,
					childId: invoice.personId,
					performedByUser: currentUser,
				});
				return refundLine;
			}
		} else {
			throw new Meteor.Error(500, "There was an issue processing this refund. Please try again later or contact LineLeader support.");
		}
	}

	static async processChargeback(notificationItem) {

		const { chargebackLine, invoiceId } = this.createChargebackLineObj(notificationItem);

		await Invoices.updateAsync({_id: invoiceId},
			{$push: {credits: chargebackLine},
			$inc: {openAmount: roundToTwo(parseFloat(chargebackLine.amount))}}
		);

		const invoice = await Invoices.findOneAsync(invoiceId);

		const securityDepositCreditMemoId = this.getSecurityDepositId(invoice);

		if (securityDepositCreditMemoId) {
			const paidByPersonId = this.getPaidByPersonId(invoice);
			await People.updateAsync(
				{ _id: paidByPersonId },
				{ $pull: { "billing.creditMemos": { _id: securityDepositCreditMemoId } } }
			);
		}

		Meteor.defer(async function() {
			await processBillingEmail({emailType: "chargeback_notice", invoiceId, chargebackLine});
		});

		return chargebackLine;

	}

	static createChargebackLineObj(notificationItem) {
		const chargebackAmount = notificationItem.amount.value / 100;

		_.each(notificationItem.additionalData, (v, k) => {
			const mappedKey = k.replace(".","_");
			notificationItem.additionalData[mappedKey] = v;
			if (mappedKey != k) delete notificationItem.additionalData[k]
		});

		const chargebackLine = {
			type: "chargeback",
			amount: chargebackAmount,
			createdAt: new Date().valueOf(),
			creditedBy: "SYSTEM",
			creditReason: "",
			creditNote: notificationItem.reason,
			adyenInfo: notificationItem,
			pendingFeeAssessment: true
		};
		const invoiceId = notificationItem.merchantReference;
		return {chargebackLine, invoiceId};
	}
	static getSecurityDepositId(invoice) {
		const securityDepositItems = invoice && _.filter(invoice.lineItems, li => {
			return li.type == "item" && li?.originalItem?.refundableDeposit;
		});
		const securityDepositCreditMemoId = securityDepositItems[0]._id;
		return securityDepositCreditMemoId;
	}

	static getPaidByPersonId(invoice) {
		return invoice.credits.find(credit => credit.paidBy)?.paidBy;
	}

	/**
	 * Process the chargeback fee for the given chargeback.
	 * Transfer funds from the customer account to ours.
	 *
	 * @param options
	 * @returns {Promise<*>}
	 */
	static async assessChargebackFee(options) {
		const mpAccountCode = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_DEFAULT_ACCOUNT_CODE);
		const adyenUser = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_API_USER);
		const adyenPassword = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_API_PASSWORD);

		if (!mpAccountCode) {
			return;
		}
		const currentOrg = await Orgs.findOneAsync({ _id: options.orgId });

		if (!currentOrg) {
			throw new Meteor.Error("Invalid for chargeback fee processing:" + options);
		}

		let chargebackFeeResponse;
		const chargebackFeeItem = {
			sourceAccountCode: currentOrg?.billing?.adyenInfo?.accountCode,
			destinationAccountCode: mpAccountCode,
			amount: {
				"value": options.amount * 100,
				"currency": "USD"
			},
			merchantReference: options.merchantReference,
			transferCode: "DEBIT_CHARGEBACK_FEE"
		};
		const headers = {
			'Content-Type': 'application/json',
			// 'idempotency-key': options.chargebackId, // We have added our own duplicate checking on requests, so removing this for now unless we learn differently
		};
		const url = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_TRANSFER_FUNDS_URL);
		Log.info("sending chargeback fee req:", chargebackFeeItem, 'headers', headers, 'url', url);
		try {
			const chargebackFeeResponse = await fetch(url, {
				method: 'POST',
				headers: {
					'Authorization': 'Basic ' + btoa(adyenUser + ":" + adyenPassword),
					...headers
				},
				body: JSON.stringify(chargebackFeeItem)
			});
			if (!chargebackFeeResponse.ok) {
				throw new Error(`HTTP error! status: ${chargebackFeeResponse.status}`);
			}
		} catch (err) {
			Log.error("Error with fund transfer (chargeback):", err);
			throw new Meteor.Error("There was an error processing the chargeback.");
		}
		Log.info("chargeback fee response", chargebackFeeResponse);

		return chargebackFeeResponse;
	}

	static async processRefundNotification(notificationItem, currentUser = null) {

		const invoice = await Invoices.findOneAsync({_id: notificationItem.merchantReference}),
			refundLineIdx = invoice.credits.findIndex( c => c.type == "refund" && notificationItem.pspReference == c.adyenInfo?.pspReference),
			refundLine = invoice.credits[refundLineIdx],
			setQueryBody= {};

		if (notificationItem.success && notificationItem.success != "false") {
			setQueryBody[`credits.${refundLineIdx}.refundConfirmed`] = true;
			setQueryBody[`credits.${refundLineIdx}.refundConfirmedAt`] = new Date().valueOf();
		} else {
			setQueryBody[`credits.${refundLineIdx}.refundPended`] = true;
			setQueryBody[`credits.${refundLineIdx}.refundPendedAt`] = new Date().valueOf();
			setQueryBody[`credits.${refundLineIdx}.refundPendCount`] = refundLine?.refundPendCount ? refundLine.refundPendCount += 1 : 1;
		}

		Invoices.updateByIdWithJournalEntry(invoice._id, { "$set": setQueryBody }, {
			userId: currentUser ? currentUser._id : 'SYSTEM',
			personId: currentUser ? currentUser.personId : '',
			orgId: invoice.orgId, // TODO: Verify this
			reason: `Executed processRefundNotification callback`, // Should there be a note/reason here?
			reasonLocation: 'server/card_providers/adyenProvider.js:processRefundNotification'
		});

		return refundLine;
	}

	static async removeCustomerSourceByType(options) {
		const currentPerson = await People.findOneAsync({_id: options.personId});
		const existingMethod = currentPerson.billing.adyenInfo.sources[options.paymentType];

		const client = AdyenProvider.getApiClient();
		const recurring = new RecurringAPI(client);
		try {
			let disableResponse = await recurring.disable({
				shopperReference: existingMethod.mappedAdditionalData.recurring_shopperReference,
				recurringDetailReference: existingMethod.mappedAdditionalData.recurring_recurringDetailReference,
				"merchantAccount": "TendlyLLCDbaMomentPathMP"
			});

			Log.info("adyen disableResponse:", disableResponse);

			if (disableResponse && disableResponse.response && disableResponse.response === "[detail-successfully-disabled]") {
				const subquerykey = "billing.adyenInfo.sources." + options.paymentType;
				const unsetQuery = { "$unset": {} };
				unsetQuery["$unset"][subquerykey] = "";

				// Capture previous state before the update
				const previousState = { ...currentPerson };

				// Update the person's document
				await People.updateAsync({ _id: currentPerson._id }, unsetQuery);

				// Capture current state after the update
				const currentState = await People.findOneAsync({ _id: currentPerson._id });
				const paymentTypePretty = options.paymentType === "card" ? "Credit Card" : "Bank Account";

				// Log the payment method change
				await HistoryAuditService.logPaymentChange({
					personId: currentPerson._id,
					orgId: currentPerson.orgId,
					changeType: HistoryAuditChangeTypes.DELETE,
					performedByUser: options.performedByUser,
					details: `${paymentTypePretty} ending in ${options.lastFour} removed`,
					currentState,
					previousState
				});
			} else {
				throw new Meteor.Error(500, "Unable to deactivate payment source. Please contact support.");
			}
		} catch (error) {
			Log.error(error);
			throw new Meteor.Error(500, "Issue with account removal: " + error.message);
		}
	}

	static async retrieveSubscriptionInfo(options) {
		const currentPerson = await People.findOneAsync({_id: options.personId}),
			existingMethod = currentPerson.billing.adyenInfo.sources[options.paymentType];

		const client = AdyenProvider.getApiClient();
		const recurring = new RecurringAPI(client);

		if (!existingMethod?.mappedAdditionalData?.recurring_shopperReference)
			throw new Meteor.Error(500, `No available recurring method for user ${options.personId}`);
		try {
			const detailsResponse = await recurring.listRecurringDetails({
				"recurring": {
					"contract": Recurring.ContractEnum.Recurring
				  },
				  "shopperReference": existingMethod.mappedAdditionalData.recurring_shopperReference,
				  "merchantAccount": "TendlyLLCDbaMomentPathMP"
			});
			const methodDetails = detailsResponse?.details?.find( d => d.RecurringDetail?.firstPspReference == existingMethod.pspReference);
			return methodDetails?.RecurringDetail;
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Issue with subscription retrieval:" + error.message);
		}

	}

	static async retrieveAccountEmail(orgId) {
		const org = await Orgs.findOneAsync({_id: orgId}),
			accountCode = org?.billing?.adyenInfo?.accountCode;
		if (!accountCode) return;

		const client = this.getBasicAuthClient();
		client.config.password = _.deep(Meteor.settings,"adyen.mpapipw");
		client.config.username = _.deep(Meteor.settings,"adyen.mpapiuser");
		const platforms = new Platforms(client);

		try {
			const detailsResponse = await platforms.Account.getAccountHolder({
				"accountCode": accountCode
			});
			//console.log("detailsresponse", detailsResponse);
			return detailsResponse?.accountHolderDetails?.email;
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Issue with account retrieval:" + error.message);
		}

	}

	static async billingTransactionReport(options) {
		const startDateMoment = new moment(options.startDate, "MM/DD/YYYY");
		const endDateMoment = new moment(options.endDate, "MM/DD/YYYY");
		const startDate = startDateMoment.format("YYYY-MM-DD");
		const endDate = endDateMoment.format("YYYY-MM-DD");
		const currentOrg = await Orgs.current();
		const accountCode = currentOrg.billing.adyenInfo.accountCode;

		let payoutsDetail = await AdyenReports.find({name:"marketplace_payments_accounting_report", date:{$gte:startDate, $lte: endDate}}, {sort: { date: 1, chunkCount: 1}, readPreference:"secondaryPreferred"}).fetchAsync();
		let totalAmount = 0.00;

		let transactions = [];
		_.each(payoutsDetail, (payoutsDetailItem) => {
			var parsed = Papa.parse(payoutsDetailItem.content);
			if (parsed.data && parsed.data.length > 0) {
				const payoutTransactions = _.filter(parsed.data, (row) => row[0] == "TendlyLLCDbaMomentPath" && row[2] == accountCode );

				_.each(payoutTransactions, (pot) => {
					const created = new moment(pot[10], "YYYY-MM-DD[T]HH:mm:ss").valueOf();

					const paymentDescription = pot[12] || "";

					transactions.push({
						created,
						amount: pot[14],
						id: pot[3],
						paymentPspReference: pot[4],
						paymentDescription,
						data: pot
					});
				});
			}
		});
		let output = {};
		output.data = await Promise.all(_.map(transactions, async (item) => {
			const decoratedItem = adyenDescriptionMapper(item);
			if (decoratedItem.invoiceId) {

				const invoice = await Invoices.findOneAsync({_id: decoratedItem.invoiceId, orgId: currentOrg._id });

				if (invoice) {
					decoratedItem.invoiceNumber = invoice.invoiceNumber;
					const paymentLine = invoice?.credits.find( c => _.deep(c, "adyenInfo.pspReference") == item.paymentPspReference);
					if (paymentLine) {
						decoratedItem.paidByDesc = paymentLine.paidByDesc;
						decoratedItem.originalAmount = item.paymentDescription.startsWith("fee_") ? paymentLine.serviceCharge : paymentLine.amount;
						decoratedItem.overpaymentAmount = paymentLine.overpaymentAmount;
					}
				}
			}
			return decoratedItem;
		}));
		let refusalTransactions = [];

		await Invoices.find({orgId:currentOrg._id, "paymentTransactions":{
				"$elemMatch": {
					"createdAt": {"$gte":startDateMoment.valueOf(), "$lt":endDateMoment.endOf("day").valueOf()},
					"resultCode": "Refused"
				}
			}})
			.forEachAsync(invoice => {
				// Filter out refusal transactions that are not related to the classic platform
				invoice.paymentTransactions.filter(pt => !pt.merchantReference.includes("_")).forEach( pt => {
					refusalTransactions.push({
						created: pt.createdAt,
						amount: pt.totalAmount,
						id: invoice._id,
						desc: "Charge refused: " + (pt.refusalReason ?? ""),
						type: "Charge Failed",
						typeId: "charge.failed",
						invoiceId: invoice._id,
						invoiceNumber: invoice.invoiceNumber,
						paidByDesc: pt.paidByDesc
					});
				});
			});

		output.data = output.data.concat(refusalTransactions);
		if (options.type) {
			output.data = _.filter(output.data, i => { return i.typeId == options.type });
			_.each(output.data, (i) => {
				totalAmount = totalAmount + (typeof i.amount == "number" ? i.amount : parseFloat(i.amount.replace(/\$|,/g, '')));
			});
			output.totalAmount = roundToTwo(totalAmount);

		}
		return output;
	}

	/**
	 * Generates a report of Adyen payouts and top-up transactions for specified organizations.
	 *
	 * @typedef {Object} BillingPayoutOptions
	 * @property {string} startDate - Start date in MM/DD/YYYY format
	 * @property {string} endDate - End date in MM/DD/YYYY format
	 * @property {boolean} [excludeEndDate] - If true, excludes the end date from the range
	 * @property {string[]} [orgIds] - Array of organization IDs. Defaults to current org
	 *
	 * @param {BillingPayoutOptions} options
	 * @returns {Promise<{data: Array<{
	 *   arrival_date: number,
	 *   amount: number,
	 *   id: string,
	 *   ref: string,
	 *   org: string,
	 *   orgId: string,
	 *   type?: 'TOP_UP'
	 * }>>} Array of payout and top-up transactions
	 */
	static async billingPayoutsReport(options) {
		const orgIds = options.orgIds || [(await Orgs.current())._id];

		let output = []

		for (const orgId of orgIds) {
			const currentOrg = await Orgs.findOneAsync(orgId)
			//Ensure that currentOrg and currentOrg.billing.adyenInfo are defined to avoid potential errors.
			if (!currentOrg || !currentOrg.billing || !currentOrg.billing.adyenInfo) {
				continue;
			}
			if (currentOrg.billing.adyenInfo.balanceAccountId) {
				const tmpOptions = cloneDeep(options);
				tmpOptions.orgIds = [orgId];
				const adyenBalancePayouts = await AdyenBalancePaymentProvider.billingPayoutsReport(tmpOptions);
				output.push(...adyenBalancePayouts.data);
			}
			if (!currentOrg.billing.adyenInfo.accountCode) {
				continue;
			}
			const accountCode = String(currentOrg.billing.adyenInfo.accountCode);
			const startDate = new moment(options.startDate, "MM/DD/YYYY").subtract(6, 'hours').format("YYYY-MM-DD");
			const endDate = new moment(options.endDate, "MM/DD/YYYY").add(options.excludeEndDate ? -1 : 0, "days").format("YYYY-MM-DD");

			let payoutsDetail = await ( await AdyenReports.aggregate([
				{"$match": {name: "marketpay_payout", date: {$gte: startDate, $lte: endDate}}},
				{"$project": {datarows: {"$split": ["$content", "\n"]}, _id: 1, date: 1}},
				{"$unwind": "$datarows"},
				{"$project": {_id: 1, date: 1, cols: {"$split": ["$datarows", ","]}}},
				{"$match": {"cols.2": accountCode, "cols.3": "Payout"}},
				{"$sort": {date: 1}}
			], {readPreference: "secondaryPreferred"})).toArray();

			// Fetch Adyen transactions
			const adyenTransactions = await AdyenTransactions.find({
				accountCode: currentOrg.billing.adyenInfo.accountCode,
				datetime: {
					$gte: new Date(options.startDate).toISOString(),
					$lte: new Date(options.endDate).toISOString()
				},
				type: 'TOP_UP'
			}).fetchAsync();

			_.each(payoutsDetail, (payoutsDetailItem) => {
				if (payoutsDetailItem.cols[11].startsWith(payoutsDetailItem.date)) {
					const arrival_date = new moment.tz(payoutsDetailItem.cols[11], "YYYY-MM-DD[T]HH:mm:ss", getMomentTzByTz(payoutsDetailItem.cols[12])).valueOf();
					if (new moment.tz(arrival_date, currentOrg.getTimezone()).format('YYYY-MM-DD') > startDate && new moment.tz(arrival_date, currentOrg.getTimezone()).format('YYYY-MM-DD') <= endDate){
					output.push({
							arrival_date,
							amount: -1 * payoutsDetailItem.cols[14],
							id: payoutsDetailItem._id.toString(),
							ref: payoutsDetailItem.cols[4],
							org: currentOrg.name,
							orgId: currentOrg._id
						});
					}
				}
			});

			// Add Adyen top-up transactions to the output
			_.each(adyenTransactions, (transaction) => {
				const arrival_date = new moment(transaction.datetime).valueOf();
				if (new moment(arrival_date).format('YYYY-MM-DD') > startDate && new moment(arrival_date).format('YYYY-MM-DD') <= endDate){
					output.push({
						arrival_date: new moment(transaction.datetime).valueOf(),
						amount: -Math.abs(transaction.amount / 100),
						id: transaction._id._str || transaction._id,
						ref: transaction.accountCode,
						org: currentOrg.name,
						orgId: currentOrg._id,
						type: 'TOP_UP'
					});
				}
			});
		}

		return {
			data: output
		};
	}

	/**
	 * Processes a billing payout detail top-up transaction and returns formatted payment data.
	 * This function retrieves an Adyen transaction and converts it into a standardized payment detail format.
	 * The amount is always converted to a negative value (except for zero) to represent a debit.
	 *
	 * @param {Object} options - The options for processing the payout detail
	 * @param {string} options.payoutId - The ID of the Adyen transaction to find (supports both ObjectId and string formats)
	 *
	 * @returns {Promise<{
	 *   data: Array<{
	 *     type: string,
	 *     arrival_date: string,
	 *     amount: number,
	 *     fee: number,
	 *     net: number,
	 *     description: string,
	 *     subDescription: string,
	 *     source: {id: string},
	 *     id: string,
	 *     destinationType: null
	 *   }>,
	 *   transferData: {
	 *     available_on: string,
	 *     net: number,
	 *     fee: number,
	 *     chargebackFee: number
	 *   }
	 * }>} Returns an object containing the formatted payment data and transfer details
	 *
	 * @throws {Error} Throws an error if no transaction is found for the given payout ID
	 */
	static async billingPayoutDetailTopUp(options) {
		const adyenTransaction = await AdyenTransactions.findOneAsync({ _id: { $in: [options.payoutId, new Mongo.ObjectID(options.payoutId)] } });
		if (!adyenTransaction) {
			throw new Error('Transaction not found');
		}

		const gross = adyenTransaction.amount === 0 ? 0 : -Math.abs(adyenTransaction.amount / 100);
		const fees = 0;
		const output = {
			data: [{
				type: 'balance_adjustment',
				arrival_date: adyenTransaction.datetime,
				amount: gross,
				fee: fees,
				net: gross - fees,
				description: 'Balance Adjustment',
				subDescription: 'Transaction resolves negative balance in payment account due to refunds, chargebacks, and other payment errors',
				source: { id: adyenTransaction.response.pspReference },
				id: '',
				destinationType: null
			}]
		};

		output.transferData = {
			available_on: adyenTransaction.datetime,
			net: adyenTransaction.amount === 0 ? 0 : -Math.abs(adyenTransaction.amount / 100),
			fee: 0,
			chargebackFee: 0,
		};

		return output;
	}

	static async billingPayoutDetailReport(options) {
		const currentOrg = await Orgs.findOneAsync({ _id: options.orgId ?? options.org?._id }) || await Orgs.current();
		const accountCode = currentOrg.billing.adyenInfo.accountCode;
		let payoutsDetail = await pullAdyenReportsFromCache({reportId: options.payoutId});

		if (payoutsDetail.length > 0) {
			const payoutsDetailItem = payoutsDetail[0];
			const parsed = Papa.parse(payoutsDetailItem.content);
			let paymentDetailData = [];
			if (parsed?.data && parsed.data.length > 0) {
				const payoutTransactions = _.filter(parsed.data, (row) => row[0] === "TendlyLLCDbaMomentPath" && row[2] === accountCode);
				const cachedPaymentReports = {};
				const payoutPspReferenceField = payoutTransactions[0].length;
				const amendedPayoutTransactions = [];
				let currentTransactionSet = [];

				_.each(payoutTransactions, pot => {
					if (pot[3] !== "Payout")
						currentTransactionSet.push(pot);
					else {
						const payoutPsp = pot[4];
						if (payoutPsp === options.payoutReference) {
							_.each(currentTransactionSet, ct => {
								ct[payoutPspReferenceField] = payoutPsp;
								amendedPayoutTransactions.push(ct);
							});
							pot[payoutPspReferenceField] = payoutPsp;
							amendedPayoutTransactions.push(pot);
						}
						currentTransactionSet = [];
					}
				});

				let data = [];
				for (const pot of amendedPayoutTransactions) {
					const arrival_date_moment = new moment(pot[11], "YYYY-MM-DD[T]HH:mm:ss");
					const arrival_date = arrival_date_moment.valueOf();
					const net = parseFloat(pot[14]);
					let	entryType = "";
					if (pot[3] === "Credited") {
						entryType = "payment"
					}
					if (pot[3] === "Debited") {
						entryType = "refund"
					}
					if (pot[3] === "Payout") {
						entryType = "transfer"
					}
					if (pot[3] === "Chargeback") {
						entryType = "adjustment"
					}
					if (pot[3] === "FundTransfer") {
						entryType = "adjustment"
					}

					const arrival_date_string = arrival_date_moment.format("YYYY-MM-DD");
					const arrival_start_date_string = arrival_date_moment.clone().add(-7, "days").format("YYYY-MM-DD");
					if (!cachedPaymentReports[arrival_date_string]) {
						const prs = await pullAdyenReportsFromCache( {name:"marketplace_payments_accounting_report", endDate: arrival_date_string, startDate: arrival_start_date_string} );
						_.each(prs, pr => {
							const prDate = pr.length > 0 && pr[0].date;
							if (pr.length > 0 && !cachedPaymentReports[prDate]) {
								cachedPaymentReports[prDate] = pr;
								paymentDetailData = paymentDetailData.concat(pr);
							}
						});
					}
					// console.log("merged", paymentDetailData);
					const matchedFeeEntry = pot[5] !== "" && !['transfer', 'refund'].includes(entryType) && paymentDetailData && _.find(paymentDetailData, (row) => row.cols[4] === pot[5]);
					let	fee = matchedFeeEntry ? parseFloat(matchedFeeEntry.cols[14]) * ( entryType === "payment" ? 1 : -1) : 0;

					// console.log ("matchedFeeEntry", matchedFeeEntry);
					const destinationType = entryType === "payment" ? "invoice" : null;

					const matchedPaymentEntrySourceInvoice = (entryType === "payment"  || entryType === "adjustment") && await Invoices.findOneAsync(pot[6]);
					const matchedPaymentEntry = matchedPaymentEntrySourceInvoice && _.find(matchedPaymentEntrySourceInvoice.credits, c => c.adyenInfo && c.adyenInfo.pspReference === pot[5]);
					const matchedInvoicePerson = matchedPaymentEntrySourceInvoice && await People.findOneAsync(matchedPaymentEntrySourceInvoice.personId);
					const matchedPaymentPerson = matchedPaymentEntry && await People.findOneAsync(matchedPaymentEntry.paidBy);
					if (matchedPaymentEntry && matchedPaymentEntry.serviceCharge  && entryType !== "adjustment") {
						fee = fee - matchedPaymentEntry.serviceCharge;
					}
					else if (entryType === "adjustment") {
						fee = 0;
					}

					const gross = roundToTwo(fee + net);

					let description = "";
					let subDescription = "";
					if (pot[3] === "FundTransfer") {
						description = "Chargeback fee(s) for prior month";
					}
					if (entryType === "payment" && matchedPaymentEntrySourceInvoice && matchedPaymentPerson) {
						description = "Invoice #" + matchedPaymentEntrySourceInvoice.invoiceNumber + " issued " + matchedPaymentEntrySourceInvoice.invoiceDate;
						if (matchedInvoicePerson) {
							description += " for " + matchedInvoicePerson.firstName + " " + matchedInvoicePerson.lastName;
						}
						subDescription = "Paid by " + matchedPaymentPerson.firstName + " " + matchedPaymentPerson.lastName + " on " + new moment(matchedPaymentEntry.createdAt).format("M/D/YYYY") + " with " + matchedPaymentEntry.payment_type;
					}
					 data.push( {
						type: entryType,
						arrival_date,
						amount: gross,
						fee,
						net,
						description,
						subDescription,
						source: {id: pot[5], },
						id: pot[6],
						destinationType
					});
				}

				const output = {
					data: data
				};

				output.transferData = {
					available_on: payoutsDetailItem.date,
					net: _.chain(output.data).filter(i => i.type =="transfer").reduce( (memo, i) => memo + i.net, 0).value() * -1,
					fee: _.reduce(output.data, (memo, i) => memo + i.fee, 0),
					chargebackFee: _.chain(output.data).filter(i => i.description.startsWith("Chargeback fee")).reduce( (memo,i) => memo +i.amount, 0).value() * -1
				};
				return output;
			}
		} else if (currentOrg.billing?.adyenInfo?.balanceAccountId) {
			return await AdyenBalancePaymentProvider.billingPayoutDetailReport(options);
		}
		return {};
	}

	/**
	 * Finds the payout details for a given payment and invoice.
	 * Searches both cached and database payout reports to locate the matching payout.
	 *
	 * @param {Object} options - Options object.
	 * @param {string} options.invoiceId - The ID of the invoice.
	 * @param {Object} options.payment - The payment object.
	 * @param {Object} [options.payment.adyenInfo] - Adyen-specific payment info.
	 * @param {string} [options.payment.adyenInfo.pspReference] - Payment PSP reference ID.
	 * @param {Object} [options.dataCache] - Cache for storing payout data.
	 * @returns {Object|undefined} - Payout details if found, otherwise undefined.
	 */
	static async findPayoutForPayment(options) {
		const invoice = await Invoices.findOneAsync(options.invoiceId);
		const { payment, dataCache } = options;

		if (!invoice || !payment) {
			return;
		}

		const paymentId = payment.adyenInfo?.pspReference;
		const currentOrg = await Orgs.findOneAsync({ _id: invoice.orgId });
		const accountCode = currentOrg.billing?.adyenInfo?.accountCode;
		const paymentDateMoment = new moment(payment.createdAt);

		// Search payout data for 7 days starting from payment date
		for (let i = 0; i < 7; i++) {
			const dateString = paymentDateMoment.clone().add(i, "days").format("YYYY-MM-DD");
			const payoutData = await this.getPayoutDataForDate(dateString, dataCache);

			if (payoutData.length > 0) {
				const parsedData = this.parsePayoutData(payoutData);

				if (parsedData) {
					const payout = this.findMatchingPayout(parsedData, accountCode, paymentId);
					if (payout) {
						return {
							payoutId: payoutData[0]._id,
							payoutDate: payout.payoutDate,
							refId: payout.refId,
						};
					}
				}
			}
		}

		// Fallback: Search using regex if no payout is found
		const regexReports = await this.findRegexReports(accountCode, paymentId, dataCache);
		for (const report of regexReports) {
			this.cacheRegexReport(report, dataCache);

			const parsedData = Papa.parse(report.content).data;
			if (parsedData) {
				const payout = this.findMatchingPayout(parsedData, accountCode, paymentId);
				if (payout) {
					return {
						payoutId: report._id,
						payoutDate: payout.payoutDate,
						refId: payout.refId,
					};
				}
			}
		}
	}

	/**
	 * Retrieves payout data for a given date, either from cache or from the database.
	 *
	 * @param {string} dateString - Date string in "YYYY-MM-DD" format.
	 * @param {Object} [dataCache] - Cache object for payout data.
	 * @returns {Array} - Array of payout data objects.
	 */

	static async getPayoutDataForDate(dateString, dataCache) {
		if (dataCache?.[dateString]) {
			return dataCache[dateString];
		}

		const payoutData = await AdyenReports.find(
			{ name: "marketpay_payout", date: dateString },
			{ readPreference: "secondaryPreferred" }
		).fetchAsync();

		if (dataCache) {
			dataCache[dateString] = payoutData;
		}

		return payoutData;
	}

	/**
	 * Retrieves payout data for a given date, either from cache or from the database.
	 *
	 * @param {string} dateString - Date string in "YYYY-MM-DD" format.
	 * @param {Object} [dataCache] - Cache object for payout data.
	 * @returns {Array} - Array of payout data objects.
	 */

	static parsePayoutData(payoutData) {
		const content = payoutData.map(pd => pd.content).join("\n");
		const parsed = Papa.parse(content);
		return parsed?.data?.length > 0 ? parsed.data : null;
	}

	/**
	 * Finds a matching payout and transaction in the parsed payout data.
	 *
	 * @param {Array} parsedData - Parsed payout data rows.
	 * @param {string} accountCode - Adyen account code to match.
	 * @param {string} paymentId - Payment ID (PSP reference) to match.
	 * @returns {Object|null} - Matching payout details or null if not found.
	 */

	static findMatchingPayout(parsedData, accountCode, paymentId) {
		const transactionRow = parsedData.find(row =>
			row[0] === "TendlyLLCDbaMomentPath" &&
			row[2] === accountCode &&
			row[5] === paymentId
		);

		const payoutRow = transactionRow && parsedData.find(row =>
			row[0] === "TendlyLLCDbaMomentPath" &&
			row[2] === accountCode &&
			row[3] === "Payout"
		);

		if (payoutRow) {
			return {
				payoutDate: payoutRow[11],
				refId: payoutRow[4],
			};
		}

		return null;
	}

	/**
	 * Finds a matching payout and transaction in the parsed payout data.
	 *
	 * @param {Array} parsedData - Parsed payout data rows.
	 * @param {string} accountCode - Adyen account code to match.
	 * @param {string} paymentId - Payment ID (PSP reference) to match.
	 * @returns {Object|null} - Matching payout details or null if not found.
	 */

	static async findRegexReports(accountCode, paymentId, dataCache) {
		const regex = new RegExp(`TendlyLLCDbaMomentPath.*${accountCode}.*${paymentId}`);
		const reports = [];

		// Search cached data
		if (dataCache) {
			Object.values(dataCache).forEach(report => {
				if (regex.test(report[0]?.content)) {
					reports.push(report[0]);
				}
			});
		}

		// Search database if no cached reports are found
		if (reports.length === 0) {
			return await AdyenReports.find(
				{ name: "marketpay_payout", content: { $regex: regex } },
				{ readPreference: "secondaryPreferred" }
			).fetchAsync();
		}

		return reports;
	}

	/**
	 * Finds a matching payout and transaction in the parsed payout data.
	 *
	 * @param {Array} parsedData - Parsed payout data rows.
	 * @param {string} accountCode - Adyen account code to match.
	 * @param {string} paymentId - Payment ID (PSP reference) to match.
	 * @returns {Object|null} - Matching payout details or null if not found.
	 */

	static cacheRegexReport(report, dataCache) {
		if (dataCache && !dataCache[report.date]) {
			dataCache[report.date] = [report];
		}
	}


	static async handleNotification(item) {
		await AdyenNotifications.insertAsync({
			timestamp: Date.now(),
			rawData: JSON.stringify(item),
			processed: false,
			eventType: item?.eventType
		});
		if (item && item.eventType && item.eventType == "REPORT_AVAILABLE") {
			console.log("Got Adyen Report Notification:", item);
			if (item.pspReference.indexOf("marketplace_payments_accounting_report") > -1 || item.pspReference.indexOf("marketpay_payout") > -1)  {
				console.log("pursuing match of marketplace report")
				const authString = Meteor.settings.adyen.reportingUsername + ":" + Meteor.settings.adyen.reportingPassword;
				console.log("usign auth:", authString);
				const accessUrl = item.content && item.content.remoteAccessUrl;
				const dateMatcher = /\d[_\d]+\.csv$/i, nameMatcher = /(.+?)_\d/;
				const extractedDates = item.pspReference.match(dateMatcher),
					extractedDate = extractedDates && extractedDates[0],
					parsedDate = extractedDate.replace(".csv","").replace(/_/g, "-");
				const nameMatches = item.pspReference.match(nameMatcher),
					nameMatch = nameMatches.length > 0 && nameMatches[1];
				console.log(extractedDate, nameMatch);
				const response = await fetch(accessUrl, {
					method: "GET",
					headers: {
						"Authorization": `Basic ${Buffer.from(authString).toString("base64")}`
					}
				});
				if (response.ok) {
					const newReportIds = await AdyenReportsService.saveReport(nameMatch, Date.now(), parsedDate, (await response.text()));
					await AdyenReportsService.parseReports(newReportIds);
				}
			}
		}
		if (item && item.eventType && item.eventType == "ACCOUNT_HOLDER_PAYOUT" ) {
			const lookupOrgId = item?.content?.accountHolderCode,
				org = await Orgs.findOneAsync({_id: lookupOrgId}),
				amount = item?.content?.amount?.value,
				parsedAmount = amount && roundToTwo(amount / 100),
				parsedDate = item.eventDate.substring(0, 10);

			await AdyenPayouts.insertAsync({
				orgId: org?._id,
				amount: parsedAmount,
				integerAmount: amount,
				date: parsedDate,
				pspReference: item.pspReference,
				estimatedArrivalDate: item?.content?.estimatedArrivalDate,
				createdAt: Date.now(),
				originalItem: item
			});
		}
		if (item && item.eventType && item.eventType === ADYEN_WEBHOOKS.TRANSFER_FUNDS) {
			if (item.content?.status?.statusCode === ADYEN_WEBHOOK_STATUS_CODES.SUCCESS) {
				await CustomerChargebacksInvoices.updateAsync(
					{
						$or: [
							{ "confirmationResponse.pspReference": item.pspReference },
							{ "confirmationResponse.data.pspReference": item.pspReference }
						]
					},
					{
						$set: {
							"confirmedAt": new Date().valueOf(),
							"confirmedMessage": item
						}
					}
				);
			} else {
				await CustomerChargebacksInvoices.updateAsync(
					{
						$or: [
							{ "confirmationResponse.pspReference": item.pspReference },
							{ "confirmationResponse.data.pspReference": item.pspReference }
						]
					},
					{
						$set: {
							"failedAt": new Date().valueOf(),
							"failedMessage": item
						}
					}
				);
			}
		}
    if (item?.eventType === ADYEN_WEBHOOKS.DIRECT_DEBIT_INITIATED) {
			const statusCode = item?.content?.status?.statusCode;
			if (statusCode === 'Failed'){
				logger.error("Direct debit initiated email was not sent. Status code was failed.")
				return;
			}

			const accountCode = item?.content?.accountCode;
			const amount = item?.content?.amount?.value;
			const org = await Orgs.findOneAsync(
				{"billing.adyenInfo.accountCode": accountCode},
				{
					fields: {
						_id: 1,
						name: 1,
						"billing.adyenInfo.email": 1,
						"valueOverrides.chargebackInvoiceEmail": 1
					}
				}
			);
			const adyenEmail = org?.billing?.adyenInfo?.email
			const chargebackInvoiceEmail = org?.valueOverrides?.chargebackInvoiceEmail;
			const sendToEmail = adyenEmail || chargebackInvoiceEmail;

			if (!org || !sendToEmail) {
				logger.error("Direct debit initiated email was not sent. Org or email were undefined", {org, sendToEmail});
				return;
			}

			EmailService.sendEmail(
            org._id,
            'directDebitInitiatedEmail',
            'email_templates/direct_debit_initiated_email.html',
            `${org.name} Registrations <<EMAIL>>`,
            sendToEmail,
            `Balance Adjustment for ${org.name}`,
						{ amount: amount }
        );
		}
		if (item && item.notificationItems && item.notificationItems.length > 0) {
			for (const ni of item.notificationItems) {
				if (ni?.NotificationRequestItem?.eventCode === "NOTIFICATION_OF_CHARGEBACK") {
					try {
						const chargeback = await this.processChargeback(ni.NotificationRequestItem);
					} catch (error) {
						console.log("chargeback processing error", error);
					}
				} else if (ni?.NotificationRequestItem?.eventCode === "REFUND") {
					try {
						const chargeback = await this.processRefundNotification(ni.NotificationRequestItem);
					} catch (error) {
						console.log("refund processing error", error);
					}
				}
			}
		}
	}
}

let myReportCache = {};

export async function pullAdyenReportsFromCache( options ) {
	console.log('pulling from cache', options)
	const nowStamp = new Date().valueOf();

	if (options.reportId) {
		const cachedReport = myReportCache[options.reportId];
		if (!cachedReport || ((nowStamp - cachedReport.timestamp) > 300000)) {
			console.log('searching for report to cache')
			const report = await AdyenReports.find({_id: options.reportId}, {readPreference:"secondaryPreferred"}).fetchAsync();
			if (report.length && report[0].groupId) {
				const groupReports = await AdyenReports.find({groupId: report[0].groupId }, {sort: { chunkCount : 1}, readPreference:"secondaryPreferred"}).fetchAsync();
				report[0].content = '';
				for (const groupReport of groupReports) {
					report[0].content += groupReport.content + "\n";
				}
			}
			console.log('found report', report._id)
			myReportCache[options.reportId] = {
				timestamp: new Date().valueOf(),
				report
			};
			console.log('cached report is being returned')

			return report;
		} else {
			console.log('hit else block cashing report here')
			return cachedReport.report;
		}
	} else if (options.name) {
		let curDate = options.startDate, output = [];
		while (curDate <= options.endDate) {
			console.log("startDate", options.startDate, "endDate", options.endDate, "looking for cache date", curDate);
			//console.log("cache lookup", _.values(myReportCache).map(r => r.reportDate))
			const curDateReport = _.find( _.values(myReportCache), r => r.reportDate == curDate);
			if (!curDateReport || ((nowStamp - curDateReport.timestamp) > 300000)) {
				//const report = AdyenReports.findOne({name:"marketplace_payments_accounting_report", date: curDate});
				const report = await (await AdyenReports.aggregate([
					{"$match": {name:"marketplace_payments_accounting_report", date: curDate}},
					{"$project": { datarows: {"$split": ["$content", "\n"]}, _id: 1, date: 1}},
					{"$unwind": "$datarows" },
					{"$project": { _id: 1, date: 1, cols: {"$split": ["$datarows", ","]}}},
					{"$match": {"cols.1": {"$regex": "^Liable.*"}}}
				], {readPreference:"secondaryPreferred"})).toArray();

				if (!report || report.length === 0) {
					console.log("no report found for date", curDate);
					curDate = new moment(curDate, "YYYY-MM-DD").add(1, "day").format("YYYY-MM-DD");
					continue;
				}

				myReportCache[report[0]._id] = {
					timestamp: new Date().valueOf(),
					reportDate: curDate,
					report
				};
				console.log("cache payments report miss", curDate);
				output.push(report);
			} else {
				output.push(curDateReport.report);
				console.log("cache payments report hit", curDate);
			}
			curDate = new moment(curDate, "YYYY-MM-DD").add(1, "day").format("YYYY-MM-DD");
		}

		return output;
	}

}

export function clearCache (){
	myReportCache = {};
}

function roundToTwo(num) {
    return +(Math.round(num + "e+2")  + "e-2");
}

let adyenDescriptionMapper = (obj) => {
	let response = {};

	response.created = obj.created;
	response.id = obj.id;
	response.amount = obj.amount;
	if (obj.data[15] == "Credited") {
		//const invoiceId = obj.data[5], invoice = await Invoices.findOneAsync(invoiceId);
		//response.invoiceNumber= invoice && invoice.invoiceNumber;

		response.desc = "Charge successful  for " + numeral(response.amount).format('$0.00');
		response.type = "Charge Succeeded";
		response.typeId = "charge.succeeded";
		response.invoiceId = obj.data[5];
	} else if (obj.data[15] == "Chargeback") {
		//const invoiceId = obj.data[5], invoice = await Invoices.findOneAsync(invoiceId);
		//response.invoiceNumber= invoice && invoice.invoiceNumber;

		response.desc = "Chargeback of " + numeral(response.amount).format('$0.00');
		response.type = "Chargeback";
		response.typeId = "chargeback";
		response.invoiceId = obj.data[5];
	} else if (obj.data[15] == "ChargebackReceived") {
		//const invoiceId = obj.data[5], invoice = await Invoices.findOneAsync(invoiceId);
		//response.invoiceNumber= invoice && invoice.invoiceNumber;

		response.desc = "Chargeback notification of " + numeral(response.amount).format('$0.00');
		response.type = "ChargebackReceived";
		response.typeId = "chargeback.received";
		response.invoiceId = obj.data[5];
	} else if (obj.data[15] == "PendingCredit") {

		response.desc = "Charge pending for " + numeral(response.amount).format('$0.00');
		response.type = "Charge Pending";
		response.invoiceId = obj.data[5];
	} else if (obj.data[15] == "charge.failed") {
		response.desc = "Charge failed to " + ("x" || obj.data.object.metadata.paidBy) + " for " + numeral(response.amount).format('$0.00');
		//response.desc += "<br/>Reason: " + obj.data.object.outcome.seller_message;
		response.type = "Charge Failed";
		response.typeId = "charge.failed";
		//response.invoiceNumber = obj.data.object.description;
	} else if (obj.data[15] == "Debited") {
		response.desc = "Charge refunded  for " + numeral(response.amount).format('$0.00');;
		response.type = "Charge Refunded";
		response.typeId = "charge.refunded";
		response.invoiceId = obj.data[5];
	} else {
		response.desc = "N/A";
		response.type = obj.data[15];
	}
	response = TopUpsUtil.transformMarketplaceRecord(obj.data, response);

	return response;
}

export function getMomentTzByTz(tz) {
	const timezoneMap = {
		EDT: "America/New_York", // Eastern Daylight Time
		EST: "America/New_York", // Eastern Standard Time
		PST: "America/Los_Angeles", // Pacific Standard Time
		PDT: "America/Los_Angeles", // Pacific Daylight Time
		CST: "America/Chicago", // Central Standard Time
		CDT: "America/Chicago", // Central Daylight Time
		MST: "America/Denver", // Mountain Standard Time
		MDT: "America/Denver", // Mountain Daylight Time
	};
    return timezoneMap[tz];
}
