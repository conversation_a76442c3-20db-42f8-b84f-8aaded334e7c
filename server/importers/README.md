# Importer Framework

This system provides a modular, scalable CSV import framework for onboarding bulk data into the application. It supports preview and commit modes, validation, transformation, uniqueness checks, and clean integration with the UI.

---

## Features

- Declarative importer configuration
- UI-driven modal upload flow
- Server-side parsing and validation
- Support for preview mode (validate-only)
- Dynamic importer registration
- Flexible context-passing via options

---

## File Structure
/server/importers
- BaseImporter.js 
- OrgImporter.js 
- importerRegistry.js 
- methods.js
- README.md

/client/app/admin/importer
- _importerModal.html 
- _importerModal.js 
- _uploadModal.html 
- _uploadModal.js 
- importerConfigs.js
- services/importerDocumentService.js
- utils/importerUtils.js

/lib/constants
- importerConstants.js

---

## How It Works

1. **Importer Configs (`importerConfigs.js`)**
    - Define how the importer appears in the UI: title, description, download and upload behavior.
    - Upload handler triggers a shared modal with importer-specific context.

2. **Upload Modal (`_uploadModal`)**
    - Collects a CSV file and context (like `rootOrg`)
    - Sends the file content to the server via `Meteor.call('runImport', ...)`

3. **Importer Execution (`runImport` method)**
    - Dynamically finds the importer class from `importerClassMap`
    - Passes in file content, preview flag, and additional options
    - Runs validation and import logic (preview or commit)

4. **Importer Subclasses (`OrgImporter`, etc.)**
    - Extend `BaseImporter`
    - Override methods like:
        - `requiredFields`
        - `fieldValidators`
        - `transformRow`
        - `insertRow`
        - `isUnique`

---


## Adding a New Importer

To add a new importer:

### 1. Create a new subclass of `BaseImporter`

```js
// /server/importers/UserImporter.js
import { BaseImporter } from './BaseImporter';

export class UserImporter extends BaseImporter {
  get requiredFields() {
    return ['Email', 'Name'];
  }

  get fieldValidators() {
    return {
      Email: (value) => value.includes('@') || 'Invalid email format'
    };
  }

  async transformRow(row) {
    return {
      email: row.Email.trim(),
      name: row.Name.trim()
    };
  }

  async isUnique(user) {
    return !(await Meteor.users.findOneAsync({ email: user.email }));
  }

  async insertRow(user) {
    await Accounts.createUserAsync(user);
  }
}
```

### 2. Register it in importerRegistry.js

```js
// /server/importers/importerRegistry.js
import { importerTypes } from '/imports/shared/importerTypes';
import { UserImporter } from './UserImporter';

export const importerClassMap = {
  [importerTypes.USER]: UserImporter,
  // other importers...
};
```

### 3. Add an entry to importerConfigs.js

```js
// /client/app/admin/importers/importerConfigs.js
{
  id: importerTypes.USER,
  title: 'User Importer',
  description: 'Bulk upload user accounts',
  downloadTemplate: () => ImporterDocumentService.downloadStaticTemplate({
    path: '/templates/user-import-template.csv',
    filename: 'User Import Template.csv'
  }),
  uploadCSV: (options) => ImporterDocumentService.showUploadModal({
    importerTitle: 'User Importer',
    importerId: importerTypes.USER,
    ...options
  })
}
```

### 4. Add importerTypes key

```js
// /lib/constants/importerConstants.js
export const importerTypes = {
  ORG: 'org-importer',
  USER: 'user-importer',
  // etc.
};
```

### 5. Define importer-specific options
```js
// /client/app/admin/importer/utils/importerUtils.js

import { importerTypes } from '/imports/shared/importerTypes';

export function getImporterOptionsById(importerId, context = {}) {
  switch (importerId) {
    case importerTypes.ORG:
      return { rootOrg: context.rootOrg };

    case importerTypes.USER:
      return { currentUserId: context.currentUserId };

    default:
      return {};
  }
}
```

### Example Usage

```js
Meteor.call('runImport', {
  importerId: 'org-importer',
  fileContent: csvString,
  preview: true,
  rootOrg: { _id: 'abc123', name: 'Acme Corp' }
});
```

Notes

    All importers must extend BaseImporter

    Use preview mode to validate without writing data

    Validation errors are returned as an array of strings

    Modal state and context are passed via ImporterDocumentService
