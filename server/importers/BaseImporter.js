import <PERSON> from 'papa<PERSON><PERSON>';

/**
 * BaseImporter provides the abstract foundation for all CSV-based data importers.
 * It handles core functionality such as:
 * - Parsing CSV input
 * - Validating required fields and field-specific constraints
 * - Transforming raw rows into structured data
 * - Inserting validated data into the system
 * - Handling preview vs commit modes
 *
 * Subclasses are expected to override methods such as `requiredFields`, `fieldValidators`,
 * `transformRow`, and `insertRow` to implement their domain-specific logic.
 *
 * @abstract
 */
export class BaseImporter {
    /**
     * @param {Object} options - Import configuration options
     * @param {string} options.fileContent - Raw CSV content as a string
     * @param {boolean} [options.preview=true] - Whether this run is in preview mode
     * @param {...any} [options] - Any additional context passed to the importer subclass (e.g., rootOrg)
     */
    constructor({ fileContent, preview = true, ...options }) {
        this.fileContent = fileContent;
        this.preview = preview;
        this.errors = [];
        this.data = [];

        Object.assign(this, options);
    }

    /**
     * A list of fields required to be present and non-empty in each row.
     * @returns {string[]}
     */
    get requiredFields() {
        return [];
    }

    /**
     * A map of field validators that validate values for specific columns.
     * Each validator should return `true` or a string describing the error.
     * @returns {Object<string, function>}
     */
    get fieldValidators() {
        return {};
    }

    /**
     * Override to check whether a transformed object is unique.
     * @param {Object} _object - The transformed data object
     * @returns {Promise<boolean>}
     */
    async isUnique(_object) {
        return true;
    }

    /**
     * Override to transform a raw CSV row into a valid object for insertion.
     * @param {Object} row - Raw CSV row
     * @returns {Promise<Object>}
     */
    async transformRow(row) {
        return row;
    }

    /**
     * Override to define how the object should be inserted into your data store.
     * @param {Object} _object - Transformed and validated data
     * @throws {Error} If not implemented in subclass
     */
    async insertRow(_object) {
        throw new Error('insertRow must be implemented by subclass');
    }

    /**
     * Parses the CSV content and returns structured data.
     * If parsing errors occur, they are added to the importer's error list.
     * @returns {Object[]} Parsed rows
     */
    parseCSV() {
        const { data, errors } = Papa.parse(this.fileContent, {
            header: true,
            skipEmptyLines: true,
        });

        if (errors.length) {
            this.errors.push(...errors.map(err => `CSV Parse Error: ${err.message}`));
            return [];
        }

        return data;
    }

    /**
     * Validates a single row based on required fields and field validators.
     * Any validation errors are recorded in `this.errors`.
     * @param {Object} row - The raw row to validate
     * @param {number} index - The row index in the file
     * @returns {boolean} Whether the row passed all validation
     */
    validateRow(row, index) {
        const missingFields = this.requiredFields.filter(field => !(field in row && row[field].trim() !== ''));
        if (missingFields.length > 0) {
            this.errors.push(`Row ${index + 1}: Missing required fields: ${missingFields.join(', ')}`);
            return false;
        }

        const validators = this.fieldValidators;

        for (const [field, validateFn] of Object.entries(validators)) {
            const value = row[field];
            const result = validateFn(value, row, index);

            if (result !== true) {
                this.errors.push(`Row ${index + 1}: ${field} - ${result}`);
            }
        }

        return true;
    }

    /**
     * Executes the import logic in either preview or commit mode depending on `this.preview`.
     * Each row is parsed, validated, transformed, and optionally inserted.
     *
     * @returns {Promise<string[]>} Array of error messages encountered during import
     */
    async run() {
        this.data = this.parseCSV();

        for (let i = 0; i < this.data.length; i++) {
            const rawRow = this.data[i];

            if (!this.validateRow(rawRow, i)) {
                continue;
            }

            const transformed = await this.transformRow(rawRow);
            const unique = await this.isUnique(transformed);

            if (!unique) {
                this.errors.push(`Row ${i + 1}: Duplicate or conflicting entry.`);
                continue;
            }

            if (!this.preview) {
                await this.insertRow(transformed);
            }
        }

        return this.errors;
    }

    /**
     * Runs the importer in full "commit" mode.
     * First executes a preview pass. If there are no errors, commits the data.
     *
     * @returns {Promise<string[]>} Array of error messages or empty if successful
     */
    async commit() {
        this.preview = true;
        const previewErrors = await this.run();

        if (previewErrors.length > 0) {
            return previewErrors;
        }

        this.preview = false;
        return await this.run();
    }
}