import { BaseImporter } from './BaseImporter';
import { Log } from '../../lib/util/log';
import { Orgs } from '../../lib/collections/orgs';
import { Random } from 'meteor/random';

/**
 * DiscountsImporter is a specialized importer subclass for importing discounts for orgs
 *
 * @extends BaseImporter
 */
export class DiscountsImporter extends BaseImporter {
  /**
   * Constructs the DiscountsImporter instance.
   *
   * @param {Object} options - Importer configuration options
   * @param {Object} options.rootOrg - The top-level org context for imported records
   */
  constructor(options) {
    super(options);

    if (!this.rootOrg) {
      throw new Error('DiscountsImporter requires a rootOrg');
    }
  }

  /**
   * Required fields that must be present in the CSV file.
   * @returns {string[]}
   */
  get requiredFields() {
    return ['Site/Organization Name', 'Discount Code', 'Description', 'Amount', 'Amount Type'];
  }

  /**
   * Field-level validation rules for CSV row values.
   * Each validator returns true if valid, or a string error message.
   * @returns {Object<string, Function>}
   */
  get fieldValidators() {
    return {
      'Site/Organization Name': (value) => {
        return value ? true : 'Site/Organization Name is required';
      },
      'Discount Code': (value, row, rowIndex) => {
        if (!value) {
          return 'Discount Code is required';
        }

        if (value.includes(' ')) {
          return 'Discount Code cannot contain spaces';
        }

        // Check for duplicate discount codes for the same org in the current file
        const orgName = row['Site/Organization Name'];
        const duplicateIdx = this.data.findIndex(
          (r, i) => i !== rowIndex &&
            r['Site/Organization Name'] === orgName &&
            r['Discount Code'] === value
        );

        if (duplicateIdx !== -1) {
          return `Discount Code ${value} is duplicated for organization ${orgName}`;
        }

        return true;
      },
      'Description': (value) => {
        return value ? true : 'Description is required';
      },
      'Amount': (value) => {
        if (!value) {
          return 'Amount is required';
        }

        if (!/^\d+(\.\d+)?$/.test(value)) {
          return 'Amount must be a number';
        }

        return true;
      },
      'Amount Type': (value) => {
        if (!value) {
          return 'Amount Type is required';
        }

        const normalizedValue = value.toLowerCase();
        return ['dollars', 'percent'].includes(normalizedValue) ? true : 'Amount Type must be either "Dollars" or "Percent"';
      },
      'Expires at end of grace period': (value) => {
        if (value) {
          const lowerValue = value.toLowerCase();
          return ['yes', 'no', 'true', 'false', '1', '0'].includes(lowerValue) ? true : 'Expires at end of grace period must be a boolean value';
        }
        return true;
      },
      'Override single discount rule': (value) => {
        if (value) {
          const lowerValue = value.toLowerCase();
          return ['yes', 'no', 'true', 'false', '1', '0'].includes(lowerValue) ? true : 'Override single discount rule must be a boolean value';
        }
        return true;
      }
    };
  }

  /**
   * Transforms a raw CSV row into a discount object ready for insertion.
   * Also validates organization and discount code uniqueness.
   *
   * @param {Object} row - The raw CSV row
   * @returns {Promise<Object>} The transformed discount object
   */
  async transformRow(row) {
    Object.keys(row).forEach(k => {
      if (typeof row[k] === 'string') row[k] = row[k].trim();
    });

    const orgName = row['Site/Organization Name'];
    const discountCode = row['Discount Code'];
    let orgId = null;

    // In preview mode, do full validation
    if (this.preview) {
      try {
        const org = await Orgs.findOneAsync({ name: orgName });

        if (!org) {
          this.errors.push(`Row ${this.data.indexOf(row) + 1}: Organization ${orgName} not found`);
          return null;
        }

        // Check if org exists in hierarchy (instead of checking if it's a direct child)
        const orgExistsInHierarchy = this.allOrgs && this.allOrgs.some(hierarchyOrg => hierarchyOrg.name === orgName);
        if (!orgExistsInHierarchy) {
          this.errors.push(`Row ${this.data.indexOf(row) + 1}: Organization ${orgName} not found in hierarchy`);
          return null;
        }

        // Check for existing discount types with the same code
        if (org.valueOverrides && org.valueOverrides.discountTypes) {
          const existingDiscountType = org.valueOverrides.discountTypes.find(
            discount => discount.type === discountCode
          );

          if (existingDiscountType) {
            this.errors.push(`Row ${this.data.indexOf(row) + 1}: Discount Code "${discountCode}" already exists in organization ${orgName}`);
            return null;
          }
        }

        orgId = org._id;
      } catch (error) {
        this.errors.push(`Row ${this.data.indexOf(row) + 1}: Error checking organization: ${error.message}`);
        return null;
      }
    } else {
      // In non-preview mode, just get the org ID
      const org = await Orgs.findOneAsync({ name: orgName });
      if (org) {
        orgId = org._id;
      }
    }

    return {
      orgId,
      code: discountCode,
      description: row['Description'],
      ledgerAccountCode: row['Ledger Account Code'] || null,
      expiresAtEndOfGracePeriod: this.parseBoolean(row['Expires at end of grace period']),
      overrideSingleDiscountRule: this.parseBoolean(row['Override single discount rule']),
      value: parseFloat(row['Amount']),
      amountType: row['Amount Type']
    };
  }

  /**
   * Converts various string representations of boolean values into actual booleans.
   * @param {string} value - The string value to parse
   * @returns {boolean} The parsed boolean value
   */
  parseBoolean(value) {
    if (!value) return false;

    const lowerValue = value.toLowerCase();
    return ['yes', 'true', '1'].includes(lowerValue);
  }

  /**
   * Checks whether a discount object is valid for insertion.
   * Additional validation that happens after transformation.
   * @param {Object} discount - The transformed discount object
   * @returns {Promise<boolean>} True if the discount is valid for insertion
   */
  async isUnique(discount) {
    if (this.preview || !discount) return true;

    try {
      const org = await Orgs.findOneAsync({ _id: discount.orgId });
      if (!org) {
        this.errors.push(`Organization with ID ${discount.orgId} not found during uniqueness check`);
        return false;
      }

      // Check if the discount code already exists as a type in the org's discount types
      if (org.valueOverrides && org.valueOverrides.discountTypes) {
        const existingType = org.valueOverrides.discountTypes.find(dt => dt.type === discount.code);
        if (existingType) {
          this.errors.push(`Discount code "${discount.code}" already exists in organization`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.errors.push(`Error checking discount uniqueness: ${error.message}`);
      return false;
    }
  }

  /**
   * Inserts a new discount into the database.
   * @param {Object} discount - The discount to insert
   * @returns {Promise<string>}
   */
  async insertRow(discount) {
    if (this.preview || !discount) return;

    try {
      const org = await Orgs.findOneAsync({ _id: discount.orgId });
      if (!org) {
        throw new Error(`Organization with ID ${discount.orgId} not found during insertion`);
      }

      const discountType = {
        _id: Random.id(),
        type: discount.code,
        description: discount.description,
        amount: discount.value,
        amountType: discount.amountType.toLowerCase(),
        expiresWithGracePeriod: discount.expiresAtEndOfGracePeriod || false,
        overrideSingleDiscount: discount.overrideSingleDiscountRule || false
      };

      if (discount.ledgerAccountCode) {
        discountType.ledgerAccountName = discount.ledgerAccountCode;
      }

      await Orgs.updateAsync(
        { _id: org._id },
        { $push: { 'valueOverrides.discountTypes': discountType } }
      );

      return true;
    } catch (e) {
      Log.error(`Error inserting discount ${discount.code} for org ${discount.orgId}: ${e}`);
      throw e;
    }
  }
}