import moment from 'moment-timezone';
import { MiscUtils } from '../../lib/util/miscUtils';
import { People } from "../../lib/collections/people";
import { Orgs } from "../../lib/collections/orgs";
import _ from '../../lib/util/underscore';
import { payInvoiceWithCreditMemo } from '../methodsBilling';
import { processBillingEmail } from '../processBillingEmail';

export class CreditMemosService {
    /**
     * Credits open invoices for a family person, applying available credit memos.
     *
     * Logic is preserved from the original implementation.
     * - First applies general credits to family balances (invoice openAmount).
     * - Then applies prepaid/excess payer credits to payer-specific balances (openPayerAmounts).
     * - Sends billing receipt emails for family-level credits.
     *
     * @param {Object} options - Options for the operation. Must include either `familyPerson` or `familyPersonId`.
     * @returns {Promise<Array>} Array of unique credited invoice objects.
     */
    static async creditFamilyPersonInvoices(options) {
        const familyPerson =
            options.familyPerson || await People.findOneAsync({ _id: options.familyPersonId });
        if (!familyPerson) return [];
        let invoicesCredited = [];

        // Get sorted open invoices
        const openInvoices = await familyPerson.openInvoicesWithPayerBalances();
        const sortedOpenInvoices = _.sortBy(openInvoices, i => i.createdAt);

        for (const invoice of sortedOpenInvoices) {
            const amountDueForFamilyMember = invoice.amountDueForFamilyMember(familyPerson._id);
            const invoiceOrg = await Orgs.findOneAsync({ _id: invoice.orgId });

            // Filter valid general credit memos
            const generalCreditMemos = _.filter(
                familyPerson.availableCreditMemos(),
                c =>
                    c.type !== "securityDepositAuto" &&
                    !c.type.startsWith("prepaid_") &&
                    !(
                        invoiceOrg.hasCustomization("billing/applyExcessSubsidyOnlyToPayer") &&
                        c.type.startsWith("excess_")
                    )
            );

            // 1. Apply general credits to family-level openAmount
            if (amountDueForFamilyMember > 0 && invoice.openAmount > 0) {
                let appliedCreditAmount = 0.00;
                let remainingAmountDue = amountDueForFamilyMember;
                let appliedCredits = [];

                for (const creditMemo of generalCreditMemos) {
                    if (creditMemo.openAmount >= 0.01) {
                        const amountToPay = (creditMemo.openAmount > remainingAmountDue ? remainingAmountDue : creditMemo.openAmount) || 0;
                        console.log("should be applying to family");
                        if (amountToPay >= 0.01) {
                            const paymentItem = await payInvoiceWithCreditMemo({
                                personId: familyPerson._id,
                                invoiceId: invoice._id,
                                payment_amount: amountToPay,
                                orgId: familyPerson.orgId,
                                creditMemoId: creditMemo._id
                            });

                            if (paymentItem) {
                                appliedCreditAmount += paymentItem.amount;
                                remainingAmountDue -= paymentItem.amount;
                                creditMemo.openAmount -= paymentItem.amount;
                                invoicesCredited.push(invoice);
                                appliedCredits.push(creditMemo);
                            }
                        } else {
                            console.log("Calculated NaN Item in auto apply credit memos:", invoice, options, amountDueForFamilyMember, creditMemo);
                        }
                    }
                }
                if (appliedCreditAmount > 0.00) {
                    await processBillingEmail({
                        emailType: "creditmemo_receipt",
                        familyPersonId: familyPerson._id,
                        invoiceId: invoice._id,
                        paymentAmount: appliedCreditAmount,
                        appliedCredits
                    });
                }
            }

            // 2. Apply payer credits to openPayerAmounts
            const openPayerSources = _.filter(
                invoice.totalAmountsForPayers(),
                ta => ta.openAmount > 0
            );

            for (const openPayerSource of openPayerSources) {
                // Filter available payer-specific credits for this payer
                const payerCreditMemos = _.filter(
                    familyPerson.availableCreditMemos(),
                    c =>
                        (
                            c.type === "prepaid_" + openPayerSource.source ||
                            (invoiceOrg.hasCustomization("billing/applyExcessSubsidyOnlyToPayer") &&
                                c.type === "excess_" + openPayerSource.source)
                        ) &&
                        c.openAmount > 0
                );

                for (const creditMemo of payerCreditMemos) {
                    const remainingAmountDue = openPayerSource.openAmount;
                    const amountToPay = creditMemo.openAmount > remainingAmountDue ? remainingAmountDue : creditMemo.openAmount;
                    console.log("should be applying to payer");

                    const paymentItem = await payInvoiceWithCreditMemo({
                        personId: familyPerson._id,
                        invoiceId: invoice._id,
                        payment_amount: amountToPay,
                        orgId: familyPerson.orgId,
                        creditMemoId: creditMemo._id
                    });

                    if (paymentItem) {
                        creditMemo.openAmount -= paymentItem.amount;
                        invoicesCredited.push(invoice);
                    }
                }
            }
        }

        // Return unique invoices credited (by _id)
        return _.uniq(invoicesCredited, false, (i, k, a) => i._id);
    }
}