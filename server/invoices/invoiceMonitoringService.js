import moment from "moment-timezone";
import { BillingTimezoneMap } from '../../lib/constants/timezoneConstants';
import { Log } from '../../lib/util/log';
import { InvoiceService } from '../invoiceService';
import <PERSON> from 'papa<PERSON><PERSON>';
import { EmailService } from '../emails/emailService';
import { ConfigurationSettingsService, ServerConfigurationConstants } from '../config/configurationSettingsService';
import { BillingFrequencies } from '../../lib/constants/billingConstants';
import { InvoiceDateUtils } from '../../lib/util/invoiceDateUtils';
import { BillingUtils } from '../../lib/util/billingUtils';
import { InvoiceGenerationService } from './invoiceGenerationService';
import { AvailableCustomizations } from '../../lib/customizations';
import { People } from "../../lib/collections/people";
import { Orgs } from "../../lib/collections/orgs";
import { Moments } from "../../lib/collections/moments";
import { Reservations } from "../../lib/collections/reservations";
import _ from "../../lib/util/underscore";

/**
 * Invoice monitoring service.
 * Contains methods to monitor invoices and make sure that they were created correctly.
 */
export class InvoiceMonitoringService {
    /**
     * Monitor invoices to make sure that they were run and all people were correctly invoiced.
     *
     * @param runOptions
     */
    static async monitorRunInvoices(runOptions) {
        try {
            const orgQuery = InvoiceService.getOrgQueryForInvoices(runOptions, BillingTimezoneMap);
            const billingOrgs = await Orgs.find(orgQuery, { disableOpLog: true }).fetchAsync();
            const invoicingMissedChildren = [];

            const orgsMap = new Map();
            for (const org of billingOrgs) {
                const todayStartStamp = new moment.tz(org.getTimezone()).startOf('day').valueOf();
                if (org.billing.suspendInvoicingUntilDate && org.billing.suspendInvoicingUntilDate > todayStartStamp) {
                    // Skip org if the invoicing is suspended
                    continue;
                }
                const monthlyPlanCutOff = InvoiceDateUtils.ifShouldBeLastDayOfMonth(org.billing.scheduling.generateMonthDay);
                const generateDay = org.billing.scheduling.generateDay;
                const gracePeriodDays = org.billing.scheduling.gracePeriodDays || 0;
                orgsMap.set(org._id, org);
                const peopleData = await People.find(InvoiceMonitoringService.getPeopleQueryForInvoices(org._id, todayStartStamp)).fetchAsync();
                
                for (const person of peopleData) {
                    const personLastInvoiced = person.billing?.lastInvoiced || 0;
                    if (personLastInvoiced >= todayStartStamp) {
                        // They have already gone through the invoice process for the day
                        continue;
                    }

                    // Check the plans for the person
                    const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayStartStamp, monthlyPlanCutOff, generateDay, gracePeriodDays);
                    if (!validPlans.length) {
                        continue;
                    }
                    
                    invoicingMissedChildren.push({ person, orgId: org._id });
                }
                
            }

            if (invoicingMissedChildren.length > 0) {
                // Log.info('Found people missing invoices');
                const columns = [
                    'Org id',
                    'Org name',
                    'Org long name',
                    'Child Id',
                    'Child First Name',
                    'Child Last Name',
                ];
                const data = [];
                let currentOrg = null;
                for (const invoiceData of invoicingMissedChildren) {
                    // Log.debug("invoiceData", invoiceData);
                    currentOrg = orgsMap.get(invoiceData.orgId);
                    data.push([
                        currentOrg?._id ?? 'Unknown',
                        currentOrg?.name ?? 'Unknown',
                        currentOrg?.longName ?? 'Unknown',
                        invoiceData.person?._id ?? 'Unknown',
                        invoiceData.person?.firstName ?? 'Unknown',
                        invoiceData.person?.lastName ?? 'Unknown'
                    ]);
                }

                const dateString = moment().format('MM/DD/YYYY');
                let filename = 'failed_invoicing_' + dateString.replace(/\//g, '-');
                if (runOptions?.timezone && BillingTimezoneMap[runOptions.timezone]) {
                    filename += '_' + runOptions.timezone;
                } else {
                    filename += '_others';
                }

                const content = Papa.unparse({ fields: columns, data });
                const attachments = [{ filename: filename + '.csv', content }];
                const billingErrorsEmail = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.BILLING_ERRORS_EMAIL);
                // Log.info('Sending email to billing errors email', billingErrorsEmail);
                await EmailService.sendSystemAdminEmail(
                    undefined,
                    'systemNotificationEmail',
                    'email_templates/system_notification_email.html',
                    '<<EMAIL>>',
                    billingErrorsEmail,
                    'Auto-invoicing failed on date ' + dateString,
                    { bodyMessage: 'Please invoice the following children, as their auto-invoicing failed.' },
                    { attachments: attachments }
                );
            } else {
                // No invoicing missed children, send success email
                const dateString = moment().format('MM/DD/YYYY');
                const timezone = runOptions?.timezone || 'others';
                const billingErrorsEmail = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.BILLING_ERRORS_EMAIL);
                Log.info('Sending success email to billing errors email', billingErrorsEmail);
                await EmailService.sendSystemAdminEmail(
                    undefined,
                    'systemNotificationEmail',
                    'email_templates/system_notification_email.html',
                    '<<EMAIL>>',
                    billingErrorsEmail,
                    'Auto-invoicing successful on date ' + dateString,
                    { bodyMessage: 'Auto-invoicing ran successfully for ' + timezone + '. No further action is needed at this time.' },
                    { attachments: [] }
                );
            }
            
        } catch (error) {
            console.log("Error Occured", JSON.stringify(error))
            Log.error(JSON.stringify(error));
        }
    }

    /**
     * Get the people query for invoices.
     *
     * @param orgId
     * @param todayStartStamp
     * @returns {{}}
     */
    static getPeopleQueryForInvoices(orgId, todayStartStamp) {
        return {
            orgId: orgId,
            inActive: { $ne: true },
            $and: [
                { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
            ]
        };
    }

    /**
     * Get the plans for the person that are valid for being checked for invoicing.
     *
     * @param person
     * @param org
     * @param todayStartStamp
     * @param monthlyPlanCutOff
     * @param generateDay
     * @param gracePeriodDays
     * @returns {*[]}
     */
    static async getValidPlansForPerson(person, org, todayStartStamp, monthlyPlanCutOff, generateDay, gracePeriodDays) {

            const availablePlans = person.billing?.enrolledPlans ?? [];
            const lastInvoiced = new moment.tz(person.billing.lastInvoiced || 0, org.getTimezone()).startOf("day").valueOf();
            const response = [];
            for (const plan of availablePlans) {
                const originalPlan = org.billing.plansAndItems.find(orgPlan => {
                    return orgPlan._id === plan._id;
                });

                // Skip if no original
                if (!originalPlan) {
                    continue;
                }

                // Skip if original plan is suspended
                if (originalPlan.suspendUntil && originalPlan.suspendUntil > todayStartStamp) {
                    continue;
                }

                // Skip if expired
                if (plan.expirationDate && plan.expirationDate <= todayStartStamp) {
                    continue;
                }

                // Skip if not started yet
                if (plan.enrollmentDate && plan.enrollmentDate >= todayStartStamp) {
                    if (!org.hasCustomization(AvailableCustomizations.AUTO_PRORATE_BY_DATE)) {
                        continue;
                    }
                    // Check that the start date is within the plan's frequency's next invoice period
                    const generateWhen = org.billing.scheduling.generateWhen;
                    if (generateWhen !== 'advance') {
                        // If the org isn't billed in advance, ignore this plan
                        continue;
                    }
                    
                    const invoicePlan = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                        org,
                        person,
                        plan,
                        todayStartStamp,
                        generateDay,
                        monthlyPlanCutOff,
                        lastInvoiced,
                        gracePeriodDays,
                        plan.enrollmentDate
                    );

                    if (invoicePlan){
                        response.push(plan);  
                    } else {
                        continue;
                    }
                }

                if (plan.frequency === BillingFrequencies.DAILY || plan.planDetails?.frequency === BillingFrequencies.DAILY) {

                    const invoicePlan = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                        org,
                        person,
                        plan,
                        todayStartStamp,
                        generateDay,
                        monthlyPlanCutOff,
                        lastInvoiced,
                        gracePeriodDays,
                        plan.enrollmentDate
                    );
                    if (invoicePlan){
                        response.push(plan);  
                    } else {
                        continue;
                    }
                }

                response.push(plan); 
            }
            return response;
    }

    /**
     * Determine if a plan should be invoiced based on its frequency and other parameters.
     * This is done under the assumption that the plan enrollment is in the future.
     *
     * @param {*} org - The organization associated with the plan.
     * @param {*} person - The person associated with the plan.
     * @param {*} plan - The plan.
     * @param {number} todayDate - The current date.
     * @param {string | undefined} generateDay - The day on which the plan should be generated.
     * @param {number} monthlyPlanCutOff - The cutoff day for monthly plans.
     * @param {number} lastInvoicedDate - The date when the plan was last invoiced.
     * @param {string} gracePeriodDays - The grace period for the plan.
     * @param {number} enrollmentDate - The date of enrollment for the plan.
     * @returns {boolean} - Returns true if the plan should be invoiced; otherwise, false.
     */
    static async planShouldBeInvoicedToday(
        org,
        person,
        plan,
        todayDate,
        generateDay,
        monthlyPlanCutOff,
        lastInvoicedDate,
        gracePeriodDays,
        enrollmentDate
    ) {
        if (!plan.frequency && !plan.planDetails?.frequency) {
            return false;
        }
        const frequency = plan.frequency || plan.planDetails?.frequency;
        const isInvoiceDay = InvoiceMonitoringService.planInvoiceDayIsToday(
            org,
            frequency,
            todayDate,
            generateDay,
            monthlyPlanCutOff,
            lastInvoicedDate,
            gracePeriodDays
        );
        if (!isInvoiceDay) {
            return false;
        }
        const timezone = org.getTimezone();
        switch (frequency) {
            case BillingFrequencies.WEEKLY:
            case BillingFrequencies.SCALED_WEEKLY:
                return (new moment.tz(todayDate, timezone).day(8)).clone().add(4, 'days').valueOf() >= enrollmentDate;
            case BillingFrequencies.WEEKLY_SCHEDULED_DAILY:
                const coversPlanStartDate = new moment.tz(todayDate, timezone).day(8).startOf("day");
                const coversPlanEndDate = coversPlanStartDate.clone().add(4, 'days');
                const coveredDays = await InvoiceMonitoringService.getReservations(coversPlanStartDate, coversPlanEndDate, {
                    orgId: org._id,
                    selectedPerson: person._id
                });
                return coveredDays?.length > 0;
            case BillingFrequencies.DAILY:
                const dailyCutoff = _.max(
                    [
                        0,
                        person.billing.lastInvoicedDaily || 0,
                        enrollmentDate || 0
                    ]
                );

                const personCheckIns = await Moments.find({
                    orgId: org._id,
                    taggedPeople: person._id,
                    momentType: 'checkin',
                    sortStamp: { $gte: dailyCutoff }
                }).fetchAsync();

                const uniqueDays = _.uniq(
                    _.map(
                        personCheckIns,
                        (ci) => {
                            return new moment(ci.sortStamp).format('MM/DD/YYYY');
                        }
                    )
                );

                return uniqueDays.length > 0;
            case BillingFrequencies.BIWEEKLY:
            case BillingFrequencies.SCALED_BIWEEKLY:
                const periodDates = BillingUtils.getPeriodByEffectiveDate(
                    moment.tz(org.nextBiWeeklyBillingTimestamp(todayDate), timezone).format('MM/DD/YYYY'),
                    frequency,
                    org,
                    person,
                    true
                );

                return new moment.tz(periodDates.end, 'MM/DD/YYYY', timezone).startOf("day").valueOf() >= enrollmentDate;
            case BillingFrequencies.BIMONTHLY:
                return new moment.tz(todayDate, timezone).add(2, 'months').endOf('month').startOf("day").valueOf() >= enrollmentDate;
            case BillingFrequencies.SEMIMONTHLY:
                if (new moment.tz(todayDate, timezone).add(gracePeriodDays, 'days').date() === 1) {
                    // Enrollment date must be before the 15th
                    return new moment.tz(todayDate, timezone).add(gracePeriodDays, 'days').date(15).startOf("day").valueOf() >= enrollmentDate;
                }
                // Enrollment date must be before the 1st
                return new moment.tz(todayDate, timezone).add(gracePeriodDays, 'days').add(1, 'months').date(1).startOf("day").valueOf() >= enrollmentDate;
            case BillingFrequencies.MONTHLY:
            case BillingFrequencies.SCALED_MONTHLY:
                return new moment.tz(todayDate, timezone).add(1, 'months').endOf("month").valueOf() >= enrollmentDate;
            case BillingFrequencies.DAILY_CHARGED_MONTHLY:
                return await InvoiceGenerationService.calulateMonthlyChargedDailyRate(plan, new moment.tz(todayDate, timezone).startOf('month'), org, person) !== false;
            default:
                return false;
        }
    }

    /**
     * Check if the plan's frequency type is set to be invoiced today.
     *
     * @param {*} org - The organization associated with the plan.
     * @param {string | undefined} frequency - The frequency of the plan.
     * @param {number} todayDate - The current date.
     * @param {string | undefined} generateDay - The day on which the plan should be generated.
     * @param {number} monthlyPlanCutOff - The cutoff day for monthly plans.
     * @param {number} lastInvoicedDate - The date when the plan was last invoiced.
     * @param {string} gracePeriodDays - The grace period for the plan.
     * @returns {boolean} - Returns true if the plan should be invoiced; otherwise, false.
     */
    static planInvoiceDayIsToday(
        org,
        frequency,
        todayDate,
        generateDay,
        monthlyPlanCutOff,
        lastInvoicedDate,
        gracePeriodDays
    ) {
        if (!frequency) {
            return false;
        }
        const timezone = org.getTimezone();
        const todayIsGenerateMonthDay = monthlyPlanCutOff === new moment.tz(todayDate, timezone).date();
        switch (frequency) {
            case BillingFrequencies.WEEKLY:
            case BillingFrequencies.SCALED_WEEKLY:
            case BillingFrequencies.DAILY:
            case BillingFrequencies.WEEKLY_SCHEDULED_DAILY:
                if (!generateDay) {
                    return false;
                }
                return org.billing.scheduling.generateDay === new moment.tz(todayDate, timezone).format('dddd').toLowerCase();
            case BillingFrequencies.BIWEEKLY:
            case BillingFrequencies.SCALED_BIWEEKLY:
                return !!org.billing?.scheduling?.generateBiWeeklyDate &&
                    new moment.tz(todayDate, timezone).add(gracePeriodDays, "days").startOf("day").valueOf() === org.nextBiWeeklyBillingTimestamp(todayDate);
            case BillingFrequencies.BIMONTHLY:
                return todayIsGenerateMonthDay && (new moment.tz(todayDate, timezone)).diff(new moment.tz(lastInvoicedDate, timezone), "months", true) > 1;
            case BillingFrequencies.SEMIMONTHLY:
                return new moment.tz(todayDate, timezone).add(gracePeriodDays, 'days').date() === 1 ||
                    new moment.tz(todayDate, timezone).add(gracePeriodDays, 'days').date() === 15;
            case BillingFrequencies.MONTHLY:
            case BillingFrequencies.SCALED_MONTHLY:
            case BillingFrequencies.DAILY_CHARGED_MONTHLY:
                return todayIsGenerateMonthDay;
            default:
                return false;
        }
    }

    static async getReservations(startDate, endDate, query) {
        return await Reservations.findWithRecurrence({
            startDateValue: startDate.valueOf(),
            endDateValue: endDate.clone().add(1, 'day').valueOf(),
            query: query
        });
    }
}