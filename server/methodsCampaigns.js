import { CampaignResults } from "../lib/collections/campaignResults";
import { Campaigns } from "../lib/collections/campaigns";
import { User } from "../lib/collections/users";

Meteor.methods({
	async "upsertCampaign"(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson.type != "admin" || !currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const rec = {
			title: options.title,
			announcement: options.announcement,
			resourceTitle: options.resourceTitle,
			resourceDescription: options.resourceDescription,
			resourceLink: options.resourceLink,
			campaignType: options.campaignType,
			orgIds: options.orgIds,
			targetTypes: options.targetTypes,
			active: options.active
		};

		if (options.campaignId) {
			await Campaigns.updateAsync({_id: options.campaignId}, {"$set": rec});
		} else {
			rec.createdAt = new Date().valueOf();
			rec.createdByPersonId = currentPerson._id;
			await Campaigns.insertAsync(rec);
		}
	},
	async "getCampaigns"(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson.type != "admin" || !currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		return await Campaigns.find({}).fetchAsync();
	},
	async "getCampaign"(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson.type != "admin" || !currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		return await Campaigns.findOneAsync({_id: options.campaignId});
	},
	async "getAvailableCampaign"(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = await currentUser?.fetchPerson();
		const completedCampaignIdsData = await CampaignResults.find({personId: currentPerson._id}, {fields: {campaignId: 1}}).fetchAsync();
		const completedCampaignIds = completedCampaignIdsData.map(cc => cc.campaignId);
		if (options?.multiple) {
			return {
				campaigns: await Campaigns.find({targetTypes: currentPerson.type, active: true, orgIds: currentPerson.orgId, _id:{"$nin": completedCampaignIds}}).fetchAsync()
			};
		} else {
			return await Campaigns.findOneAsync({targetTypes: currentPerson.type, active: true, orgIds: currentPerson.orgId, _id:{"$nin": completedCampaignIds}});
		}
	},
	async "saveCampaignResult"(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = await currentUser?.fetchPerson();
		const org = await currentUser.fetchOrg();

		const completedCampaign = await CampaignResults.findOneAsync({campaignId: options.campaignId, personId: currentPerson._id});
		const	campaign = await Campaigns.findOneAsync({_id: options.campaignId, targetTypes: currentPerson.type, orgIds: currentPerson.orgId});

		if (completedCampaign || !campaign)
			throw new Meteor.Error(403, "Access denied");
		
		console.log("Sent", options);
		await CampaignResults.insertAsync({
			personId: currentPerson._id,
			campaignId: options.campaignId,
			answerNumber: options.answerNumber,
			responseText: options.responseText,
			createdAt: new Date().valueOf(),
			createdByPersonId: currentPerson._id,
			orgId: currentPerson.orgId,
			orgName: org.name,
			orgLongName: org.longName,
			personType: currentPerson.type,
			personFirstName: currentPerson.firstName,
			personLastName: currentPerson.lastName,
			deviceInfo: options.deviceInfo
		});
	},
	async "getCampaignResults"(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson.type != "admin" || !currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const campaign = await Campaigns.findOneAsync(options.campaignId);
		const	campaignResults = await CampaignResults.find({campaignId: options.campaignId}).fetchAsync();
		
		return {
			campaign,
			campaignResults
		};
	}
});