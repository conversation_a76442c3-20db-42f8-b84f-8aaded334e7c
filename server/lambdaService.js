import { InvokeCommand, LambdaClient, LogType } from '@aws-sdk/client-lambda';

let lambdaClient = null;
export class LambdaService {
    /**
     * @param {string} functionName
     * @param {string} payload
     * @return {*}
     */
    static async invokeFunction(functionName, payload) {
        if (!lambdaClient) {
            const config = {
                region: Meteor.settings.awsBillingRegion,
            }
            if (Meteor.settings.awsBillingKey && Meteor.settings.awsBillingSecret) {
                config.credentials = {
                    accessKeyId: Meteor.settings.awsBillingKey,
                    secretAccessKey: Meteor.settings.awsBillingSecret,
                };
            }
            if (Meteor.settings.awsBillingEndpoint) {
                config.endpoint = Meteor.settings.awsBillingEndpoint;
            }
            lambdaClient = new LambdaClient(config);
        }

        const invokeCommand= new InvokeCommand({
            FunctionName: functionName,
            Payload: payload,
            LogType: LogType.Tail,
        });

        return await lambdaClient.send(invokeCommand);
    }
}