import moment from 'moment-timezone';
import { SSR } from "../lib/util/ssrUtils";
import { Blaze } from "meteor/blaze";
import { People } from "../lib/collections/people";
import { generateEmailWhiteLabelData } from "./util";
import { Orgs } from "../lib/collections/orgs";
import _ from "../lib/util/underscore";
import { PeopleDataValidations } from "../lib/collections/peopleDataValidation";

export const processDataValidationReminder = async function (dvId) {
	if (Meteor.isServer) {
		const dv = await PeopleDataValidations.findOneAsync(dvId);
    const peopleRespondedIds = _.map(dv.responses || [], (r) => r.personId);
    const peopleIds = _.difference(dv.peopleIds, peopleRespondedIds)

    const org = await Orgs.findOneAsync(dv.orgId);
    const subject = `${org.getLongName()} - is your information up-to-date?`
    const body = dv.description;

    SSR.compileTemplate("dataValidationReminder", await Assets.getTextAsync('email_templates/v2021/data_validation_reminder.html'));

    Blaze.Template.dataValidationReminder.helpers({
      "equals": function(val1, val2) {
        return (val1 === val2) ? true : false;
      }
    });

    let baseUrl = (org && org.whiteLabel && org.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
    if (baseUrl.slice(baseUrl.length-1) != "/") baseUrl+= "/";
    const whiteLabel = generateEmailWhiteLabelData(org);
    const emailData = {
      org,
      body,
      baseUrl,
    };

    emailData.whiteLabel = whiteLabel
    emailData.backgroundColor = `${whiteLabel.primaryColor}1A`;
    emailData.headerOrgNameColor = "#8E8E93";
    emailData.headerBgColor = whiteLabel.primaryColor;
    emailData.secondaryColor = whiteLabel.secondaryColor;
    emailData.assetPrefix = `emailAssets/${whiteLabel.emailAssetPrefix}`;
    emailData.currentYear = new moment().format("YYYY");

    const emailOptions = {
      from: "LineLeader Support <<EMAIL>>",
      // to: currentInvitation.email,
      subject,
      html: SSR.render("dataValidationReminder", emailData)
    };
    if (emailData.org.replyToAddress) emailOptions.replyTo = emailData.org.replyToAddress;
    if (emailData.org.fromAddress) emailOptions.from = emailData.org.fromAddress;

    const allPeoplList = await People.find({ _id: { $in: peopleIds }, inActive: { $ne: true }}).fetchAsync();
    for(const person of allPeoplList) {
      try {
				const email = await person.getActiveUserEmailAddress();
				if (email && email.includes("@")) {
					console.log(email);
					emailOptions.to = email;
					await Email.sendAsync(emailOptions);
				}
        console.log("data validation reminder sent.")
      }
      catch (error) {
        console.log("data validation reminder error:", error);
      }
    };
  }
};
