import {AdyenBalancePlatformProvider} from "./balancePlatformProvider/adyenBalancePlatformProvider";
import {ConfigurationSettingsService, ServerConfigurationConstants} from "./config/configurationSettingsService";
import {AdyenBalancePlatformOnboardingDataService} from "./adyenBalancePlatformOnboardingDataService";
import {AdyenServiceError} from "./balancePlatformProvider/adyenServiceError";
import {Log} from "../lib/util/log";
import {AwsBillingService} from "./awsBillingService";
import {Orgs} from '../lib/collections/orgs';
import _ from '../lib/util/underscore';

const CHILD_CARE_SERVICES_INDUSTRY_CODE = '6244';
const COUNTRY_CODE = 'US';

export class AdyenBalancePlatformOnboardingService {
	/*
	  * Start balance platform onboarding
	  * If the onboarding is started, it will update the existing entities.
	  * @param {string} orgId - The organization ID.
	  * @param {object} inputData
	   	* @param {string} inputData.legalName - The legal name of the company.
		* @param {object} inputData.address - The address object containing location details.
		* @param {string} inputData.address.city - The city where the company is located.
		* @param {string} inputData.address.line1 - The first line of the company's address.
		* @param {string} inputData.address.line2 - The second line of the company's address.
		* @param {string} inputData.address.postalCode - The postal code of the company's address.
		* @param {string} inputData.address.state - The state where the company is located.
		* @param {string} inputData.phone - The phone number of the company.
		* @param {string} inputData.webAddress - The company's web address.
	  * @param {object} existingOnboardingData - An object containing existing onboarding data.
	    * @param {string} existingOnboardingData.legalEntityId - The ID of the legal entity.
	    * @param {string} existingOnboardingData.accountHolderId - The ID of the account holder.
	    * @param {string} existingOnboardingData.businessLineId - The ID of the business line.
	    * @param {string} existingOnboardingData.storeId - The ID of the store.
	  * @param {string} initiatedByUserId - The ID of the user who initiated the onboarding.
	  * @param {object} adyenInfo - The Adyen information object from billing.
	  	* @param {string} adyenInfo.accountCode - The account code of the organization from Adyen classic platform.
	 */
	static startBalancePlatformOnboarding = async (orgId, inputData = {}, onboardingData, initiatedByUserId, adyenInfo) => {
		let organizationOnboardingInputData = {
			legalName: inputData.legalName,
			address: inputData.address,
			phone: inputData.phone,
			webAddress: inputData.webAddress,
		}

		/*
		 * Adyen Balance Platform Onboarding docs: https://docs.adyen.com/platforms/onboard-users/onboarding-steps/?location=us&legal_entity=organization
		 */
		const accountHolderId = onboardingData?.accountHolderId
		let accountHolder;
		let legalEntity;

		const isAdyenClassicPlatformAccount = adyenInfo?.accountCode && accountHolderId;
		if (adyenInfo?.accountCode && !accountHolderId) {
			Log.info("Account Holder ID is missing in billing. Organization from classic platform should have accountHolderId to be migrated to the Balance Platform", {orgId});
			throw new Error("CONTACT_SUPPORT_FOR_MIGRATION");
		}

		if (!isAdyenClassicPlatformAccount) {
			/*
			 * Create legal entity
			 */
			legalEntity = await AdyenBalancePlatformOnboardingService.createOrUpdateLegalEntity(organizationOnboardingInputData, onboardingData);
			await AdyenBalancePlatformOnboardingDataService.saveLegalEntityId(legalEntity.id, orgId);

			/*
			* Create account holder
 			*/
			accountHolder = await AdyenBalancePlatformOnboardingService.createAccountHolder(legalEntity.id, orgId, onboardingData);
			await AdyenBalancePlatformOnboardingDataService.saveAccountHolderId(accountHolder.id, orgId);
		} else {
			Log.info("Migrating merchant account from classic platform to balance platform", {orgId, accountHolderId});

			accountHolder = await AdyenBalancePlatformOnboardingService.requestCapabilitiesForMerchantFromClassicPlatform(accountHolderId, orgId);

			/*
			 * Get legal entity ID from account holder
			 * This is needed for the existing merchant accounts from classic platform to be migrated to the balance platform
			 * as they will have only account holder id and not legal entity id
			 */
			onboardingData.legalEntityId = accountHolder.legalEntityId;
			legalEntity = await AdyenBalancePlatformProvider.getLegalEntity(accountHolder.legalEntityId);
			organizationOnboardingInputData = {
				...organizationOnboardingInputData,
				legalName: legalEntity.organization?.legalName,
				address: {
					city: legalEntity.organization?.registeredAddress?.city,
					line1: legalEntity.organization?.registeredAddress?.street,
					line2: legalEntity.organization?.registeredAddress?.street2,
					postalCode: legalEntity.organization?.registeredAddress?.postalCode,
					state: legalEntity.organization?.registeredAddress?.stateOrProvince,
				},
			}
			Log.info('Organization onboarding data from classic platform', {
				orgId,
				legalEntityId: accountHolder.legalEntityId,
				organizationOnboardingInputData
			});

			console.log('organizationOnboardingInputData', organizationOnboardingInputData)

			await AdyenBalancePlatformOnboardingDataService.saveLegalEntityId(accountHolder.legalEntityId, orgId);
		}

		/*
		 * Create business line
		 */
		const businessLine = await AdyenBalancePlatformOnboardingService.createOrUpdateBusinessLine(legalEntity.id, organizationOnboardingInputData, onboardingData);
		await AdyenBalancePlatformOnboardingDataService.saveBusinessLineId(businessLine.id, orgId);


		/*
		 * Create store
		 */
		const store = await AdyenBalancePlatformOnboardingService.createOrUpdateStore(businessLine.id, organizationOnboardingInputData, onboardingData);
		await AdyenBalancePlatformOnboardingDataService.saveStoreId(store.id, orgId);

		/*
		 * Enable payment methods
		 */
		const PAYMENT_METHODS_TO_ENABLE = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_PAYMENT_METHODS);
		const paymentMethods = await AdyenBalancePlatformOnboardingService.requestMissingPaymentMethods(store.id, businessLine.id, PAYMENT_METHODS_TO_ENABLE);
		await AdyenBalancePlatformOnboardingDataService.savePaymentMethods(orgId, paymentMethods);

		/*
		 * Create hosted onboarding link
		 */
		const onboardingLink = await AdyenBalancePlatformOnboardingService.generateHopUrl(orgId, legalEntity.id);

		/*
		 * Save initiated state
		 */
		await AdyenBalancePlatformOnboardingDataService.saveInitiatedState(orgId, initiatedByUserId, organizationOnboardingInputData);

		Log.info("Onboarding link created", {orgId: orgId});

		if (isAdyenClassicPlatformAccount) {
			// We need to trigger the complete onboarding checks for the migrated merchant accounts from classic platform
			// as they might have already passed the KYC checks but we don't receive any webhook events for Adyen
			// This function will check the KYC status and update the onboarding status accordingly if the KYC is passed
			await AdyenBalancePlatformOnboardingService.triggerCompleteAdyenBalancePlatformOnboardingChecks(orgId);
		}

		return {
			onboardingLink
		}
	}

	static async requestCapabilitiesForMerchantFromClassicPlatform(accountHolderId, orgId) {
		try {
			const capabilities = ["receivePayments"];

			Log.info("Requesting capabilities for the account holder", {orgId, accountHolderId, capabilities});
			/*
			 * Request capabilities for the account holder
			 * This is needed only for the existing merchant accounts from classic platform to be migrated to the balance platform
			 */
			const accountHolder = await AdyenBalancePlatformProvider.updateAccountHolder(accountHolderId, {
				capabilities: capabilities.reduce((acc, capability) => {
					acc[capability] = {
						requested: true
					};
					return acc;
				}, {})
			});

			Log.info("ReceivePayments capabilities requested", {orgId, accountHolderId});

			return accountHolder;
		} catch (error) {
			// Possible error if the capabilities are already requested. Error message "Capability: ${nameOfCapability} is already requested"
			const isCapabilityAlreadyRequestedError = error instanceof AdyenServiceError && error.invalidFields.some(
				field => field.name === "capabilities" && field.message.match(/^Capability: (\w+) is already requested$/)
			);
			if (isCapabilityAlreadyRequestedError) {
				Log.info("ReceivePayments capabilities already requested", {orgId, accountHolderId});
				return await AdyenBalancePlatformProvider.getAccountHolder(accountHolderId);
			}

			Log.error("Failed requesting receivePayments capabilities", {orgId, accountHolderId, error});
			throw error;
		}
	}

	static async createOrUpdateLegalEntity(inputData, adyenBalancePlatformOnboarding) {
		const organization = {
			legalName: inputData.legalName,
			registeredAddress: {
				country: COUNTRY_CODE,
			}
		}

		if (adyenBalancePlatformOnboarding?.legalEntityId) {
			return await AdyenBalancePlatformProvider.updateLegalEntity(adyenBalancePlatformOnboarding?.legalEntityId, {organization});
		}

		return await AdyenBalancePlatformProvider.createLegalEntity({
			type: 'organization',
			organization
		});
	}

	static async createAccountHolder(legalEntityId, orgId, adyenBalancePlatformOnboarding) {
		const accountHolderData = {
			legalEntityId,
		}

		if (adyenBalancePlatformOnboarding?.accountHolderId) {
			return await AdyenBalancePlatformProvider.getAccountHolder(adyenBalancePlatformOnboarding?.accountHolderId);
		}

		return await AdyenBalancePlatformProvider.createAccountHolder(accountHolderData);
	}

	static async createOrUpdateBusinessLine(legalEntityId, inputData, adyenBalancePlatformOnboarding) {
		try {
			const businessLineData = {
				legalEntityId,
				industryCode: CHILD_CARE_SERVICES_INDUSTRY_CODE,
				salesChannels: ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.ADYEN_SALES_CHANNELS),
				service: 'paymentProcessing',
				webData: [{
					webAddress: inputData.webAddress,
				}]
			}

			const existingBusinessLines = await AdyenBalancePlatformProvider.getAllBusinessLinesOfLegalEntity(legalEntityId);
			const existingBusinessLine = existingBusinessLines.businessLines
				.find(businessLine => businessLine.industryCode === CHILD_CARE_SERVICES_INDUSTRY_CODE && businessLine.service === 'paymentProcessing');

			if (existingBusinessLine) {
				return await AdyenBalancePlatformProvider.updateBusinessLine(existingBusinessLine.id, businessLineData);
			}

			return await AdyenBalancePlatformProvider.createBusinessLine(businessLineData);
		} catch (error) {
			if (error instanceof AdyenServiceError && error.responseBody.invalidFields.some(field => field.name === "WEB_ADDRESS")) {
				throw new Error("INVALID_WEB_ADDRESS");
			}

			throw error;
		}
	}

	static async createOrUpdateStore(businessLineId, inputData, adyenBalancePlatformOnboarding) {
		const storeData = {
			businessLinesIds: [businessLineId],
			address: {
				city: inputData.address.city,
				country: COUNTRY_CODE,
				line1: inputData.address.line1,
				line2: inputData.address.line2,
				postalCode: inputData.address.postalCode,
				stateOrProvince: inputData.address.state,
			},
			phoneNumber: inputData.phone,
			description: `${inputData.legalName} store`,
			legalName: inputData.legalName,
		}

		if (adyenBalancePlatformOnboarding?.storeId) {
			return await AdyenBalancePlatformProvider.updateStore(adyenBalancePlatformOnboarding.storeId, storeData);
		}

		return await AdyenBalancePlatformProvider.createStore(storeData);
	}

	static async requestPaymentMethod(storeId, businessLineId, paymentMethod) {
		try {
			return await AdyenBalancePlatformProvider.enablePaymentMethod({
				storeIds: [storeId],
				businessLineId,
				type: paymentMethod,
				countries: ['US'],
				currencies: ['USD'],
			})
		} catch (error) {
			if (error instanceof AdyenServiceError && error.responseBody.detail === "Missing or invalid data when setting up the payment method (merchantShopUrl)") {
				Log.info("Invalid web address provided for enabling payment method", {
					storeId,
					businessLineId,
					paymentMethod
				});
				throw new Error("INVALID_WEB_ADDRESS");
			}

			if (error.message === "PAYMENT_METHOD_ALREADY_CONFIGURED") {
				Log.info("Payment method already configured", {storeId, businessLineId, paymentMethod});
				return null;
			}

			throw error;
		}
	}

	static async requestMissingPaymentMethods(storeId, businessLineId, requiredPaymentMethods) {
		// It's possible that the payment methods were already requested by the previous call and some of them are not requested due to some error, so we need to check the existing payment methods
		const existingPaymentMethods = await AdyenBalancePlatformProvider.getBusinessLinePaymentMethods(storeId, businessLineId);
		const existingPaymentMethodsMap = existingPaymentMethods.data.reduce((acc, paymentMethod) => {
			acc[paymentMethod.type] = paymentMethod;
			return acc;
		}, {});
		const missingPaymentMethods = requiredPaymentMethods.filter((paymentMethod) => !existingPaymentMethodsMap[paymentMethod]);

		const requestedPaymentMethods = await Promise.all(missingPaymentMethods.map((paymentMethod) => AdyenBalancePlatformOnboardingService.requestPaymentMethod(storeId, businessLineId, paymentMethod)))

		const paymentMethodsWithoutDuplicatesMap = [...existingPaymentMethods.data, ...requestedPaymentMethods].reduce((acc, paymentMethod) => {
			if (paymentMethod.type) {
				acc[paymentMethod.type] = paymentMethod;
			}
			return acc
		}, {});

		const paymentMethods = Object.values(paymentMethodsWithoutDuplicatesMap);
		Log.info("Payment methods requested", {paymentMethods});

		return paymentMethods;
	}

	static async getAdyenBalancePlatformReturnUrl(orgId) {
		const currentOrg = await Orgs.findOneAsync({_id: orgId});
		const returnUrlBase = _.deep(currentOrg, "whiteLabel.ROOT_URL") || "https://app.momentpath.com";
		return returnUrlBase + "/billing/admin/hosted-balance-platform-setup";
	}

	static async generateHopUrl(orgId, legalEntityId) {
		const redirectUrl = await AdyenBalancePlatformOnboardingService.getAdyenBalancePlatformReturnUrl(orgId);

		await AdyenBalancePlatformOnboardingDataService.resetSentKYCFailureEmailFlag(orgId);

		return await AdyenBalancePlatformProvider.createHostedOnboardingLink(legalEntityId, {
			redirectUrl,
			settings: {
				editPrefilledCountry: false,
				transferInstrumentLimit: 1,
				changeLegalEntityType: false,
			}
		});
	}

	static async triggerCompleteAdyenBalancePlatformOnboardingChecks(orgId) {
		try {
			await AwsBillingService.runCompleteAdyenBalancePlatformOnboardingChecks(orgId);
		} catch (error) {
			Log.error(`Failed to trigger complete adyen balance platform onboarding ${error}`, error);
		}
	}
}
