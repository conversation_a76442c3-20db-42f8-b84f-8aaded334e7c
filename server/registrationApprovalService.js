import { Meteor } from 'meteor/meteor';
import moment from "moment-timezone";
import { getAllocations, tokenString } from './methodsRegistration';
import { ITEM_TYPE, ItemDateTypes, PLAN_TYPE, PUNCH_CARD_TYPE } from '../lib/constants/billingConstants';
import { BillingUtils } from '../lib/util/billingUtils';
import { formatSelectedDays, RegistrationService } from './registration/registrationService';
import { BillingInvoiceService } from './billingInvoiceService';
import { RegistrationUtils } from "../lib/util/registrationUtils";
import { ChildcareCrmFamilyService } from "./childcareCrmFamilyService";
import { Log } from '../lib/util/log';
import { AvailableCustomizations } from '../lib/customizations';
import { EnrollmentsService } from './enrollments/enrollmentsService';
import logger from "../imports/winston/index";
import { People } from "../lib/collections/people";
import { processBillingEmail } from "./processBillingEmail";
import { Orgs } from "../lib/collections/orgs";
import { Relationships } from "../lib/collections/relationships";
import { Reservations } from "../lib/collections/reservations";
import { UserInvitations } from "../lib/collections/userInvitations";
import { processInvitation } from "./processInvitation";

export class RegistrationApprovalService {
    /**
     * Get the days used in calculation for schedules.
     *
     * @returns {string[]}
     * @constructor
     */
    static get DAYS() {
        return ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    }

    static getUniqueDistinctContacts(contacts){
        const chosenContacts = [];
        return contacts.filter(contact => {
            if(!chosenContacts.includes(contact.profileEmailAddress)){
                chosenContacts.push(contact.profileEmailAddress);
                return true;
            }
            return false;
        })
    }

    static async approveRegistration(registrationData, regId = null, orgId = null, fromRegistration = false, isUpdate = false) {
        const regData = { ...registrationData };
        const currentOrg = orgId ? await Orgs.findOneAsync({ _id: orgId }) : await Orgs.current();
        orgId = currentOrg?._id;
        Log.info('creating/updating children');
        const childIds = await this.addUpdateChildren(registrationData, currentOrg, fromRegistration);
        Log.info('creating/updating guardians');
        const guardianIds = await this.addUpdateGuardians(registrationData, orgId, fromRegistration, regId, isUpdate);
        Log.info('setting ids');
        childIds.forEach((childId, index) => regData.children[index].personId = childId);

        guardianIds.forEach(({email, personId}) => {
            regData.contacts.forEach((contact) => {
                if(contact.profileEmailAddress === email){
                    contact.personId = personId
                }
            })
        })

        // create relationships
        Log.info('building relationships');
        const allChildren = [];
        const allContacts = [];
        for (const contact of regData.contacts) {
            let contactPersonId = contact.personId;
            if (!contactPersonId) {
                // Find another equivalent contact with a personId
                const matchingContact = regData.contacts.find(c => {
                    return c.profileEmailAddress === contact.profileEmailAddress
                        && ('personId' in c);
                });

                if (!matchingContact) {
                    continue;
                }
                contactPersonId = matchingContact.personId;
            }

            const doesCopyToChildren = contact.copyToChildren === 'Yes';
            const connectedChildren = [];

            if(doesCopyToChildren){
                const contactsWithSameEmail = regData.contacts.filter((tempContact) => tempContact.profileEmailAddress === contact.profileEmailAddress);
                const listOfChildIndexesWithSameEmails = contactsWithSameEmail.map(v => v.childIndex);
                let chosenChildren = [];
                // Get array of children who DON'T have a contact
                for (let i = 0; i <= regData.children.length - 1;  i++) {
                    if (!listOfChildIndexesWithSameEmails.includes(i)) {
                        chosenChildren.push(i);
                    }
                }
                connectedChildren.push(...regData.children.filter((child, index) => chosenChildren.includes(index)));
            }

            // Add contacts with direct child connection
            connectedChildren.push(...regData.children.filter((child, index) => contact.childIndex === index));

            if (!allContacts.includes(contactPersonId)) {
                allContacts.push(contactPersonId);
            }
            if (!connectedChildren.length) {
                continue;
            }
            for (const child of connectedChildren) {
                if (!child.personId) {
                    continue;
                }
                if (!allChildren.includes(child.personId)) {
                    allChildren.push(child.personId);
                }
                const relationshipData = {
                    relationshipType: [],
                    relationshipDescription: contact.relationshipDescription,
                    primaryCaregiver: contact.primaryCaregiver === 'Yes',
                    personId: contactPersonId,
                    targetPersonId: child.personId
                }
                const previousRelationships = await Relationships.find({ personId: contactPersonId, targetId: child.personId }).fetchAsync();
                const previousPrimary = previousRelationships ? previousRelationships.find(relationship => relationship.relationshipType === 'family' && relationship.primaryCaregiver) : null;
                const previousPickup = previousRelationships ? previousRelationships.find(relationship => relationship.relationshipType === 'authorizedPickup') : null;
                const previousContact = previousRelationships ? previousRelationships.find(relationship => relationship.relationshipType === 'emergencyContact') : null;

                //if a previous relationship has been removed
                if (contact.primaryCaregiver === 'No' && previousPrimary) {
                    await Relationships.removeAsync(previousPrimary._id);
                    if (previousPickup) await Relationships.updateAsync({ _id: previousPickup._id }, { "$set": { "primaryCaregiver": false } });
                    if (previousContact) await Relationships.updateAsync({ _id: previousContact._id }, { "$set": { "primaryCaregiver": false } });
                }
                if (contact.authorizedPickup === 'No' && previousPickup) {
                    await Relationships.removeAsync(previousPickup._id);
                }
                if (contact.emergencyContact === 'No' && previousContact) {
                    await Relationships.removeAsync(previousContact._id);
                }
                //update descriptions
                if (contact.primaryCaregiver === 'Yes' && previousPrimary && previousPrimary.relationshipDescription !== contact.relationshipDescription) {
                    await Relationships.direct.updateAsync({ _id: previousPrimary._id }, { "$set": { "relationshipDescription": contact.relationshipDescription } });
                }
                if ((contact.primaryCaregiver === 'Yes' || contact.authorizedPickup === 'Yes') && previousPickup && previousPickup.relationshipDescription !== contact.relationshipDescription) {
                    await Relationships.direct.updateAsync({ _id: previousPickup._id }, { "$set": { "relationshipDescription": contact.relationshipDescription } });
                }
                if ((contact.primaryCaregiver === 'Yes' || contact.emergencyContact === 'Yes') && previousContact && previousContact.relationshipDescription !== contact.relationshipDescription) {
                    await Relationships.direct.updateAsync({ _id: previousContact._id }, { "$set": { "relationshipDescription": contact.relationshipDescription } });
                }

                if (contact.primaryCaregiver === 'Yes' && !previousPrimary) {
                    relationshipData.relationshipType.push('family');
                    if (previousPickup) {
                        await Relationships.direct.updateAsync({ _id: previousPickup._id }, { "$set": { "primaryCaregiver": true } });
                    }
                    if (previousContact) {
                        await Relationships.direct.updateAsync({ _id: previousContact._id }, { "$set": { "primaryCaregiver": true } });
                    }
                }
                if ((contact.primaryCaregiver === 'Yes' || contact.authorizedPickup === 'Yes') && !previousPickup) {
                    relationshipData.relationshipType.push('authorizedPickup');
                }
                if ((contact.primaryCaregiver === 'Yes' || contact.emergencyContact === 'Yes') && !previousContact) {
                    relationshipData.relationshipType.push('emergencyContact');
                }
                if (relationshipData.relationshipType.length) {
                    await Meteor.callAsync('addRelationship', relationshipData, orgId, fromRegistration, true);
                }
            }
        }
        for (const allChildId of allChildren) {
            await ChildcareCrmFamilyService.sendUpdateAfterAddingRelationship(allChildId);
        }
        for (const allContactId of allContacts) {
            Meteor.callAsync('setAutoPin', allContactId);
        }
        Log.info('adding/updating billing plans');

        // Enroll into plans and set schedule
        // First split out the selective week plans into individual plans to make it easier to enroll each plan correctly
        RegistrationService.splitOutSelectWeekPlans(regData);
        // Use the start date on selective week plans now to determine when the plan starts in regard to the billing cycle
        const useSelectiveWeekMinWeek = false;
        // Create the schedule and plans
        Log.info('regData.savedPlanOptions in approveRegistration method before addUpdateChildPlansAndSchedules:', regData.savedPlanOptions);
        const allReservationIds = await this.addUpdateChildPlansAndSchedules(regData, currentOrg, fromRegistration);

        Log.info('adding charges');
        // Create charges and invoicing
        // Get first parent with a personId
        const contactWithPersonId = regData.contacts.find(contact => 'personId' in contact);
        let newTotal;
        // Calculate registration fees
        if (regData.registrationFee) {
            // Check if 'registrationFeeAmountDue' is already calculated in regData
            if ('registrationFeeAmountDue' in regData) {
                newTotal = {
                    fee: regData.registrationFeeAmountDue,
                    timePeriodIds: regData.registrationFeeAmountDueTimePeriodIds ?? []
                };
            } else {
                // Calculate the total registration fees using the utility method
                newTotal = await RegistrationUtils.totalRegistrationFees(
                    regData,
                    regData.registrationFee.planTotal,
                    false,
                    contactWithPersonId.personId,
                    currentOrg._id,
                    true
                );
            }
        }

        for (const child of regData.children) {
            const index = regData.children.indexOf(child);
            const responsibleParty = regData.payer_id || contactWithPersonId.personId;
            const items = (regData.plans.length >= index) ? regData.plans[index].filter(plan => plan.type === ITEM_TYPE || plan.type === PUNCH_CARD_TYPE) : []
            const plans = (regData.plans.length >= index) ? regData.plans[index].filter(plan => plan.type === PLAN_TYPE) : [];

            if (regData.registrationFee) {
                // Determine whether to add the registration fee to the items array
                if (newTotal.fee > 0) {
                    // Add registration fee item with the calculated total fee
                    items.push({
                        ...regData.registrationFee,
                        amount: newTotal.fee,
                        planTotal: newTotal.fee,
                        timePeriodIds: newTotal.timePeriodIds
                    });
                } else if (newTotal.totalFee > 0) {
                    // Find the fee for the current child
                    const childFee = newTotal.childrenFees?.find(childFee => childFee.childIndex === index);
                    if (childFee && childFee.fee > 0) {
                        // Add registration fee item with the child's fee
                        items.push({
                            ...regData.registrationFee,
                            amount: childFee.fee,
                            planTotal: childFee.fee,
                            timePeriodIds: childFee.timePeriodIds
                        });
                    }
                }
            }


            if (items.length || (plans.length && !registrationData.savedPlanOptions?.length)) {
                // Check for new plans and make sure that they are charged immediately if necessary
                let plansPaymentStatus = undefined;
                let chargesStartDate = moment.tz(currentOrg.getTimezone()).startOf('day').format('MM/DD/YYYY');
                let foundPlan = false;
                if (plans.length) {
                    plansPaymentStatus = await RegistrationService.getNewPlansInvoicingStatus(registrationData, currentOrg._id, index, useSelectiveWeekMinWeek);
                    const childPerson = await People.findOneAsync({ _id: child._id || child.personId });
                    items.push(...plansPaymentStatus.plansWithPaymentsDueToday.map(p => {
                        const periodDates = BillingUtils.getPeriodByEffectiveDate(p.startDate, p.frequency, currentOrg, childPerson, true);
                        if (!foundPlan) {
                            chargesStartDate = periodDates.start;
                            foundPlan = true;
                        } else {
                            chargesStartDate = chargesStartDate > periodDates.start ? periodDates.start : chargesStartDate;
                        }
                        p.isPlan = true;
                        return p;
                    }));
                }

                const options = {
                    charges: items,
                    childId: child._id || child.personId,
                    payerId: responsibleParty,
                    payment_source: regData.payment_source || null,
                    registrationData: regData,
                    chargesStartDate
                }
                options.registrationData.contacts = this.getUniqueDistinctContacts(regData.contacts);
                await this.addNewBillingCharges(options, orgId, fromRegistration, allReservationIds);
                if (plansPaymentStatus?.plansNeedingInvoices?.length) {
                    RegistrationApprovalService.generateInvoiceForPlans(child._id || child.personId, plansPaymentStatus.plansNeedingInvoices, currentOrg);
                }
                if (!isUpdate && fromRegistration) {
                    const options = {
                        account_type: regData.payment_source,
                        payment_amount_type: 'full',
                        specific_amount: '',
                        personId: responsibleParty
                    }
                    await Meteor.callAsync('configureAutoPay', options, orgId, fromRegistration);
                }
            }
            if (isUpdate && regData.savedPlanOptions?.length) {
                Log.info('regData.savedPlanOptions in approveRegistration method after addUpdateChildPlansAndSchedules:', regData.savedPlanOptions);
                for (const savedOptions of regData.savedPlanOptions) {
                    const timezone = currentOrg.getTimezone();
                    const needsProration = RegistrationService.needsProration(savedOptions.planOptions, savedOptions.bundlePlanOptions || null);
                    const formattedEffectiveDate = moment.tz(savedOptions.planOptions.effectiveDate, timezone).format('MM/DD/YYYY');
                    const period = BillingUtils.getPeriodByEffectiveDate(formattedEffectiveDate, savedOptions.planOptions.linkedPlan.planDetails.frequency, currentOrg, child, true);

                    if (needsProration) {
                        const prorateOptions = {
                            oldReservations: savedOptions.oldReservations,
                            newReservations: savedOptions.newReservations,
                            personId: child.personId,
                            periodStart: period ? period.start : moment.tz(timezone).format('MM/DD/YYYY'),
                            periodEnd: period ? period.end : moment.tz(timezone).add(1, 'days').format('MM/DD/YYYY'),
                            org: currentOrg
                        }
                        Log.info('prorateOptions.oldReservations in prorateParentAdjustedPlan:', prorateOptions.oldReservations);
                        Log.info('prorateOptions.newReservations in prorateParentAdjustedPlan:', prorateOptions.newReservations);
                        try {
                            await BillingInvoiceService.prorateParentAdjustedPlan(prorateOptions);
                        } catch (e) {
                            console.error('Error prorating parent adjusted plan: ', e.reason || e.message || e);
                            throw new Meteor.Error('Error prorating parent adjusted plan', e.reason || e.message, e.details || '');
                        }
                    }
                }
            }
        }
        return {
            children: childIds,
            org: currentOrg._id
        };
    }

    static async addUpdateGuardians(regData, orgId, fromRegistration, regId, isUpdate) {
        const contactIds = [];
        const currentOrg = orgId ? await Orgs.findOneAsync({ _id: orgId }) : await Orgs.current();
        const prefix = currentOrg.profileDataPrefix();
        const contacts = this.getUniqueDistinctContacts(regData.contacts);
        for (const contact of contacts) {
            if (!contact._id) {
                contact.type = 'family';
                contact.overrideNameCheck = true;
                contact.sendInvite = !isUpdate;
                contact.emailAddress = contact.profileEmailAddress;
                const isPrimary = regData.contacts.some(c => c.primaryCaregiver === 'Yes' && c.profileEmailAddress === contact.profileEmailAddress);
                if (regData.designation && isPrimary) {
                    contact.designations = [regData.designation];
                }
                let result;
                try {
                    result = await Meteor.callAsync('insertPerson', contact, orgId, fromRegistration, regId);
                } catch (e) {
                    console.error('Error inserting person: ', e.reason || e.message || e);
                    throw new Meteor.Error('Error inserting person', e.reason || e.message, e.details || '');
                }
                if (contact.primaryCaregiver === 'Yes' && regData.payLater) {
                    await processBillingEmail({emailType:"family_statement", familyPersonId: result});
                }
                contactIds.push({email: contact.emailAddress, personId: result});
                const update = {};
                const householdInfo = {
                    parentCity: contact.city ?? '',
                    parentState: contact.state ?? '',
                    parentStreetAddress: contact.address ?? '',
                    parentZip: contact.zip ?? ''
                }
                if (prefix) {
                    update[`${prefix}.householdInformation`] = householdInfo;
                } else {
                    update['householdInformation'] = householdInfo
                }
                await People.updateAsync({ _id: result }, { $set: update });
            } else {
                let isNew = false;
                const person = await People.findOneAsync({ _id: contact._id, orgId: orgId });
                if (!person.profileEmailAddress && !isUpdate) {
                    // This person is new and needs history
                    isNew = true;
                }
                contact.emailAddress = contact.profileEmailAddress;
                contact.personId = contact._id;
                const contactDesignations = contact.designations || [];
                const isPrimary = regData.contacts.some(c => c.primaryCaregiver === 'Yes' && c.profileEmailAddress === contact.profileEmailAddress);
                if (regData.designation && isPrimary && !contactDesignations.includes(regData.designation)) {
                    contactDesignations.push(regData.designation);
                    contact.designations = contactDesignations; // Add the new designation to the contact
                }
                let result;
                try {
                    result = await Meteor.callAsync('updatePerson', contact, orgId, fromRegistration, regId, isNew);
                } catch (e) {
                    console.error('Error updating person: ', e.reason || e.message || e);
                    throw new Meteor.Error('Error updating person', e.reason || e.message, e.details || '');
                }
                contactIds.push({email: contact.emailAddress, personId: contact._id});
                const update = {};
                const householdInfo = {
                    parentCity: contact.city ?? '',
                    parentState: contact.state ?? '',
                    parentStreetAddress: contact.address ?? '',
                    parentZip: contact.zip ?? ''
                }
                if (prefix) {
                    update[`${prefix}.householdInformation`] = householdInfo;
                } else {
                    update['householdInformation'] = householdInfo
                }
                await People.updateAsync({ _id: contact._id }, { $set: update });
                if (fromRegistration && orgId) {
                    const invitationId = await UserInvitations.insertAsync({
                        token: tokenString(),
                        email: contact.profileEmailAddress.trim().toLowerCase(),
                        orgId: currentOrg._id,
                        personId: contact._id,
                        used: false
                    });
                    await processInvitation(invitationId);
                }
            }
        }
        return contactIds;
    }

    static async addUpdateChildren(regData, org, fromRegistration) {
        const childIds = [];
        for (const child of regData.children) {
            let childId = child._id;
            const childFields = Object.keys(child);
            //const profileFields = childFields.map(async fieldName => await People.getProfileFieldWithNameForType('person', fieldName, org));
            const profileFields = await Promise.all(
                childFields.map(async fieldName => {
                  return await People.getProfileFieldWithNameForType('person', fieldName, org);
                })
              );
            const baseDateFields = profileFields.filter(field => field?.fieldPath && !field?.fieldPath?.includes('.') && field?.type === 'date');
            baseDateFields.forEach(field => {
                if (child[field.name]) {
                    try {
                        const pattern = new RegExp(/^(0[1-9]|1[0-2])\/(0[1-9]|1\d|2\d|3[01])\/(19|20)\d{2}$/);
                        if (typeof child[field.name] === 'string' && pattern.test(child[field.name])) {
                            child[field.name] = (new moment.tz(child[field.name], 'MM/DD/YYYY', org.getTimezone())).valueOf();
                        } else {
                            child[field.name] = (new moment.tz(child[field.name], org.getTimezone())).valueOf();
                        }
                    } catch (e) {
                        // no op
                        console.error(e);
                        throw new Meteor.Error('Error', e.message, '');
                    }
                }
            });
            if (org.familyRegistrationSettings?.autoGroupSelection) {
                child.defaultGroupId = org.familyRegistrationSettings.autoGroupSelection;
            }
            if (child._id) {
                child.personId = child._id;
                const childDesignations = child.designations || [];
                if (regData.designation && !childDesignations.includes(regData.designation)) {
                    childDesignations.push(regData.designation);
                    child.designations = childDesignations;
                }
                await Meteor.callAsync('updatePerson', child, org._id, fromRegistration);
                childIds.push(child._id);
            } else {
                child.type = 'person';
                child.overrideNameCheck = true;
                if (regData.designation) {
                    child.designations = [regData.designation];
                }
                let result;
                try {
                    result = await Meteor.callAsync('insertPerson', child, org._id, fromRegistration);
                } catch (e) {
                    console.error('Error inserting person: ', e.reason || e.message || e);
                    throw new Meteor.Error('Error inserting person', e.reason || e.message, e.details || '');
                }

                childId = result;
                childIds.push(childId);
            }
            const person = await People.findOneAsync({ _id: childId, orgId: org._id });
            if (!person) {
                continue;
            }
            try {
                // Only get the profile fields that are part of a field group from the submitted data
                const profileFieldGroupFields = profileFields.filter(field => field?.fieldPath?.includes('.'));
                const fieldData = [];
                for (const field of profileFieldGroupFields) {
                    fieldData.push({
                        fieldId: field.fieldPath,
                        fieldType: field.type,
                        fieldPath: '', // Don't set field path since that is already handled by fieldId
                        fieldIndex: '',
                        newFieldValue: child[field.name]
                    });
                }
                if (fieldData.length) {
                    await Meteor.callAsync('updateAllProfileFieldValues', childId, fieldData, org._id);
                }
            } catch (e) {
                console.error('Error getting some field group values', e.reason || e.message || e);
                throw new Meteor.Error('Error getting some field group values', e.reason || e.message, e.details || '');
            }
        }
        return childIds;
    }

    static async addNewBillingCharges(options, orgId, fromRegistration, allReservationIds) {
        const org = orgId ? await Orgs.findOneAsync({ _id: orgId }) : await Orgs.current();
        const hasAutoProrateByDate = org.hasCustomization(AvailableCustomizations.AUTO_PRORATE_BY_DATE);
        let amount = 0;
        let planIds = [];
        for (const newCharge of options.charges.filter(charge => !charge.isPlan)) {
            const billingCharge = {
                item: newCharge._id,
                notes: "",
                price: newCharge.amount,
                discount_reason: null,
                quantity: "1",
                amount: newCharge.amount,
                personId: options.childId,
            };
            if (newCharge.timePeriodIds?.length) {
                billingCharge.timePeriodIds = newCharge.timePeriodIds;
            }

            if (newCharge.allocations && newCharge.allocations.length) {
                billingCharge.allocations = newCharge.allocations;
                const discountedAmount = BillingUtils.getDiscountedAmount(newCharge.amount, newCharge.allocations, org).familyAmount;
                amount += discountedAmount
            } else {
                amount += newCharge.amount;
            }
            try {
                await Meteor.callAsync("insertBillingCharge", billingCharge, orgId, fromRegistration, allReservationIds);
            } catch (e) {
                console.error('Error inserting billing charge', e.reason || e.message || e);
                throw new Meteor.Error('Error inserting billing charge', e.reason || e.message, e.details || '');
            }
        }

        const invoiceData = {
            personId: options.childId,
            isSystem: true,
            userId: fromRegistration ? null : Meteor.userId(),
            orgId
        }

        const child = await People.findOneAsync({ _id: options.childId });
        let createdAts = [];
        const hasSelectiveWeekPlans = options.charges.filter(charge => charge.isPlan && charge.selectedWeeks?.length > 0)?.length > 0;
        for (const newCharge of options.charges.filter(charge => charge.isPlan)) {
            planIds.push(newCharge._id);
            amount += newCharge.planTotal;
            if (hasSelectiveWeekPlans) {
                const enrollmentDate = new moment.tz(newCharge.startDate, "MM/DD/YYYY", org.getTimezone()).valueOf();
                const childBillingPlan = (child?.billing?.enrolledPlans ?? []).find((p) => p._id === newCharge._id && p.enrollmentDate === enrollmentDate);
                if (childBillingPlan) {
                    createdAts.push(childBillingPlan.createdAt);
                }
            }
        }
        invoiceData.manualInvoicePlanIds = [];
        if (planIds.length) {
            invoiceData.manualInvoicePlanIds = planIds;
            invoiceData.prorateByDate = hasAutoProrateByDate;
            invoiceData.manualInvoicePeriodStartDate = options.chargesStartDate;
            invoiceData.createdAts = createdAts;
        }
        invoiceData.fromRegistration = fromRegistration ?? false;
        if (fromRegistration && options.payment_source) {
            invoiceData.payInvoice = true;
            invoiceData.payerId = options.payerId;
            invoiceData.paymentSource = options.payment_source;
            invoiceData.registrationData = options.registrationData;
        }
        try {
            return await Meteor.callAsync('generateManualInvoice', invoiceData, orgId, fromRegistration, allReservationIds);
        } catch (e) {
            console.error('Error generating manual invoice: ', e.reason || e.message || e);
            throw new Meteor.Error('Error generating manual invoice', e.reason || e.message, e.details || '');
        }
    }

    /**
     * Add or update child plans and schedules.
     *
     * @param regData
     * @param org
     * @param fromRegistration
     */
    static async addUpdateChildPlansAndSchedules(regData, org, fromRegistration) {
        const currentOrgTimePeriod = org.billing?.timePeriods || null;
        const allReservationIds = []
        for (const child of regData.children) {
            if (!child.personId) {
                continue;
            }
            const timezone = org.getTimezone();
            const index = regData.children.indexOf(child);
            let dateIndex = 0;
            for (const plan of regData.plans[index]) {
                if ((plan.type === ITEM_TYPE && !plan.details?.dateType) || plan.type === PUNCH_CARD_TYPE) {
                    continue;
                }
                // handle plan updates
                if (plan.parentAdjusted) {
                    logger.info('Processing parent adjusted plan', { planId: plan._id, childId: child._id, effectiveDate: plan.parentAdjustedEffectiveDate });

                    const reservation = await Reservations.findOneAsync({ _id: plan.reservationId });
                    const oldResUpdate = {
                        reservation,
                        effectiveDate: plan.parentAdjustedEffectiveDate
                    }

                    try {
                        await RegistrationService.updateOldReservation(oldResUpdate);
                        logger.info('Successfully updated old reservation', { reservationId: plan.reservationId });
                    } catch (e) {
                        logger.error('Error updating old reservation', { error: e.reason || e.message,  planId: plan._id,  reservationId: plan.reservationId });
                        throw new Meteor.Error('Error updating old reservation', e.reason || e.message, e.details || '');
                    }
                } else if (plan.createdAt && child._id) {
                    const childPerson = await People.findOneAsync({ _id: child._id });
                    const oldPlan = childPerson?.billing?.enrolledPlans?.find(op => plan._id === op._id && op.createdAt === plan.createdAt);

                    if (!oldPlan) {
                        logger.info('No matching old plan found - skipping update', { planId: plan._id, childId: child._id });
                        continue;
                    }

                    oldPlan.allocations = plan.allocations || [];

                    try {
                        await People.updateAsync(
                            {
                                _id: child._id,
                                "billing.enrolledPlans._id": plan._id,
                                "billing.enrolledPlans.createdAt": plan.createdAt
                            },
                            { $set: { "billing.enrolledPlans.$": oldPlan } }
                        );
                        logger.info('Updated plan allocations', { planId: plan._id, childId: child._id });
                    } catch (err) {
                        logger.error('Error updating plan allocations', { error: err.message,  planId: plan._id,  childId: child._id });
                        throw new Meteor.Error('Error updating person record', err.reason || err.message, err.details || '');
                    }
                } else if (!plan.createdAt) {
                    // new charges
                    const scheduleData = {
                        selectedPerson: [child.personId],
                        reservationType: "person",
                        groupId: 'default',
                        overrideOverlap: true
                    }
                    const schedules = [];
                    const individualTimePeriod = plan.details?.timePeriod;

                    if (plan.type === PLAN_TYPE) {
                        let hasSelectiveWeeks = false;
                        if (plan.details?.selectiveWeeks?.length && plan.selectedWeeks?.length) {
                            hasSelectiveWeeks = true;
                        }
                        logger.info('generateScheduleDataForItemCharges > PLAN_TYPE startDate', { "startDate": plan.startDate, "timezone": timezone });
                        scheduleData.scheduledDate = new moment.tz(plan.startDate, 'MM/DD/YYYY', timezone).valueOf();
                        scheduleData.scheduleType = plan.details?.scheduleType ?? '';
                        scheduleData.recurringFrequency = 1;
                        scheduleData.recurringDays = plan.selectedDays.map(day => formatSelectedDays(day));
                        if (hasSelectiveWeeks) {
                            scheduleData.scheduledEndDate = new moment.tz(plan.endDate, 'MM/DD/YYYY', timezone).endOf('day').valueOf();
                        } else {
                            scheduleData.scheduledEndDate = currentOrgTimePeriod ? (currentOrgTimePeriod.find(selectedTime => selectedTime._id === individualTimePeriod)?.endDate ?? null) : null;
                        }
                        scheduleData.scheduledTime = plan.details?.startTime ?? '';
                        scheduleData.scheduledEndTime = plan.details?.endTime ?? '';
                        // Plan will be enrolled inside insertReservation
                        scheduleData.linkedPlan = {
                            plan: plan._id,
                            allocations: getAllocations(plan.allocations, regData),
                            enrollment_date: plan.startDate,
                            bundlePlanId: plan.bundlePlanId ?? null,
                            incrementCreatedAt: hasSelectiveWeeks ? dateIndex++ : undefined
                        };
                        scheduleData.generatedFromBillingCharge = plan._id;
                        // Make the plan's end date the same as the schedule's end date.
                        if (scheduleData.scheduledEndDate) {
                            scheduleData.linkedPlan.expiration_date = new moment.tz(scheduleData.scheduledEndDate, timezone).format('MM/DD/YYYY');
                        }
                        // But if the plan has a specific end date, lets overwrite the schedules end date. (this may never be needed)
                        if (plan.endDate) {
                            scheduleData.linkedPlan.expiration_date = plan.endDate;
                        }

                        if (plan.overrideRate) {
                            scheduleData.linkedPlan.override_rate = plan.overrideRate;
                        }

                        if (plan.enrollmentForecastStartDate || hasSelectiveWeeks) {
                            scheduleData.linkedPlan.enrollment_forecast_start = plan.enrollmentForecastStartDate || plan.startDate;
                        }
                        if (plan.enrollmentForecastEndDate) {
                            scheduleData.linkedPlan.enrollment_forecast_end = plan.enrollmentForecastEndDate;
                        }
                        logger.info('generateScheduleDataForItemCharges > PLAN_TYPE', { "scheduleData": scheduleData });
                        schedules.push(scheduleData);
                    }else if (plan.type === ITEM_TYPE) {
                        logger.info('generateScheduleDataForItemCharges > ITEM_TYPE', { "personId": child._id, "timezone": timezone, "plan": plan });
                        const itemChargesSchedule = EnrollmentsService.generateScheduleDataForItemCharges(plan, child.personId, timezone);
                        logger.info('generateScheduleDataForItemCharges > returned schedule data', { "personId": child._id,  "scheduleData": itemChargesSchedule });
                        schedules.push(...itemChargesSchedule);
                    }
                    for (const schedule of schedules) {
                        // we need that reservation ID on onHold updates in case we need to prorate later on
                        let result;
                        if (regData.savedPlanOptions && regData.savedPlanOptions.find(savedOptions => savedOptions.personId === child._id)) {
                            const savedOptions = regData.savedPlanOptions.find(savedOptions => savedOptions.personId === child._id);

                            // Check that newReservations exist and is an array
                            if (!savedOptions.newReservations || !Array.isArray(savedOptions.newReservations)) {
                                throw new Meteor.Error('Error with savedOptions.newReservations data', { "savedOptions": savedOptions });
                            }

                            const newReservationIndex = savedOptions.newReservations.findIndex(res => 
                                res.scheduledDate === schedule.scheduledDate && 
                                res.recurringDays.length === schedule.recurringDays.length && 
                                res.linkedPlan.plan === schedule.linkedPlan.plan);
                            
                            if (newReservationIndex !== -1) {
                                try {
                                    logger.info('generateScheduleDataForItemCharges > insertReservation 1', { "orgId": org._id, "fromRegistration": fromRegistration, "schedule": schedule });
                                    result = await Meteor.callAsync('insertReservation', schedule, org._id, fromRegistration);
                                    allReservationIds.push(result?.newReservationId)
                                    savedOptions.newReservations[newReservationIndex].reservationId = result.newReservationId;
                                    
                                    // Update all other matching entries in other savedPlanOptions
                                    for (const otherOption of regData.savedPlanOptions) {
                                        if (otherOption !== savedOptions && otherOption.personId === child._id) {
                                            for (const newRes of otherOption.newReservations) {
                                                if (newRes.scheduledDate === schedule.scheduledDate && 
                                                    newRes.recurringDays.length === schedule.recurringDays.length && 
                                                    newRes.linkedPlan.plan === schedule.linkedPlan.plan) {
                                                    newRes.reservationId = result.newReservationId;
                                                }
                                            }
                                        }
                                    }
                                } catch (e) {
                                    console.error('Error inserting reservation: ', e.reason || e.message || e);
                                    throw new Meteor.Error('Error inserting reservation: ', e.reason || e.message, e.details || '');
                                }
                            } else {
                                try {
                                    logger.info('generateScheduleDataForItemCharges > insertReservation 2', { "orgId": org._id, "fromRegistration": fromRegistration, "schedule": schedule });
                                    const res = await Meteor.callAsync('insertReservation', schedule, org._id, fromRegistration);
                                    allReservationIds.push(res?.newReservationId)
                                } catch (e) {
                                    console.error('Error inserting reservation: ', e.reason || e.message || e);
                                    throw new Meteor.Error('Error inserting reservation: ', e.reason || e.message, e.details || '');
                                }
                            }
                        } else {
                            try {
                                logger.info('generateScheduleDataForItemCharges > insertReservation 3', { "orgId": org._id, "fromRegistration": fromRegistration, "schedule": schedule });
                                const res = await Meteor.callAsync('insertReservation', schedule, org._id, fromRegistration);
                                allReservationIds.push(res?.newReservationId)
                            } catch (e) {
                                console.error('Error inserting reservation: ', e.reason || e.message || e);
                                throw new Meteor.Error('Error inserting reservation: ', e.reason || e.message, e.details || '');
                            }
                        }
                    }
                }
            }
        }
        return allReservationIds
    }

    /**
     * Generate invoice for plans that are already past their normal invoice date.
     *
     * @param childId
     * @param plans
     * @param org
     * @returns {Promise<void>}
     */
    static async generateInvoiceForPlans(childId, plans, org) {
        if (!plans.length) {
            return;
        }
        const child = await People.findOneAsync({ _id: childId });
        for (const plan of plans) {
            const frequency = plan.planDetails?.frequency ?? plan.frequency;
            const periodDates = BillingUtils.getPeriodByEffectiveDate(plan.startDate, frequency, org, child, true);
            let createdAts = undefined;
            if (plan.details?.selectiveWeeks?.length && plan.selectedWeeks?.length) {
                const enrollmentDate = new moment.tz(plan.startDate, "MM/DD/YYYY", org.getTimezone()).valueOf();
                const childBillingPlan = (child?.billing?.enrolledPlans ?? []).find((p) => p._id === plan._id && p.enrollmentDate === enrollmentDate);
                if (childBillingPlan) {
                    createdAts = [childBillingPlan.createdAt];
                }
            }
            const invoiceData = {
                planIds: [plan._id],
                createdAts,
                logInvoice: true,
                periodStartDate: periodDates.start,
                parentAdjusted: true, // kinda misleading but gets past the permission check
                includePendingItems: false,
                personId: childId,
                orgId: org._id,
                isSystem: true
            };

            Meteor.callAsync('sendManualPlanInvoice', invoiceData)
            .catch((error) => {
                console.error('Error generating invoice', error);
                throw new Meteor.Error('Error generating invoice', error.reason || error.message, error.details || '');
            });
        }
    }
}
