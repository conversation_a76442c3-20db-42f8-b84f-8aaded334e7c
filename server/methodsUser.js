import { Meteor } from 'meteor/meteor';
import { People } from '../lib/collections/people';
export const deleteUser = async (personId) => {
  const currentUser = await Meteor.userAsync();
  const currentPerson = await currentUser.fetchPerson();
  const person = await People.findOneAsync(personId);
  const associatedUser = await Meteor.users.findOneAsync({"$or": [{ personId: personId }, {"membership.personId": personId}]});

  if (!associatedUser) {
    throw new Meteor.Error(404, 'User not found');
  }
  if (!currentUser || currentPerson?.type !== 'admin' || !currentPerson.superAdmin || currentPerson.orgId !== currentUser['orgId']) {
    throw new Meteor.Error(403, 'Access denied');
  }
  try {
    await Meteor.users.removeAsync(associatedUser._id);
    if (Meteor.isServer) {
      await Meteor.callAsync('deleteZkTecoPerson', personId);
    }
    if (person.type === 'admin') {
      await Meteor.callAsync('sendManageUserUpdates', associatedUser, true);
    }
    return { message: 'User has been deleted' };
  } catch (error) {
    throw new Meteor.Error(500, 'Error deleting user');
  }
};

Meteor.methods({
  deleteUser
});
