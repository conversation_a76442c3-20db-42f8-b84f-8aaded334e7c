import { parsePhoneNumber } from 'libphonenumber-js';
import moment from 'moment-timezone';
import { ChildcareCRM } from './childcareCrmService';
import {CRM_STATUSES} from "../lib/constants/childcaremCrmConstants";
import { People } from '../lib/collections/people';
import { ChildcareCrmAccounts } from '../lib/collections/childcareCrmAccounts';
import { Orgs } from '../lib/collections/orgs';
import { Relationships } from '../lib/collections/relationships';
import { Reservations } from '../lib/collections/reservations';

export class ChildcareCrmFamilyService {
    static getUrl() {
        return Meteor.settings.childcareCrmUrl;
    }

    static get INQUIRY_TYPE() {
        return 'inquirySourceUnifiedIntegration';
    }
    static get MANAGE_INTEGRATION_PARTNER_NAME() {
        return 'Manage';
    }
    static get SEARCH_CHILD() {
        return 'child';
    }
    static get SEARCH_CONTACT() {
        return 'contact';
    }
    static get SEARCH_LEAD() {
        return 'lead';
    }
    static get SOURCE_TYPE() {
        return 'leadSourceUnifiedIntegration';
    }

    /**
     * Get the id of the Manage integration.
     *
     * @param account
     * @param token
     * @returns {Promise<number|null>}
     */
    static async getManageIntegrationId(account, token = null) {
        // Get the token
        const headers = await ChildcareCRM.getBasicCrmRequestHeaders(account, token);

        const integrationsResponse = await fetch(`${this.getUrl()}/api/v3/integrations/all`, {
            method: 'GET',
            headers: headers,
        });
        const integrationsJson = await integrationsResponse.json();
        if (!integrationsJson) {
            console.log('Could not get the Manage integration id.');
            return null;
        }

        return integrationsJson.find(i => i.name === this.MANAGE_INTEGRATION_PARTNER_NAME)?.id ?? null;
    }

    static async sendFamiliesToCrm(request) {
        for (const orgId of request.org_ids || []) {
            await this.sendOrgFamiliesToCrm(orgId);
        }
    }

    static async sendOrgFamiliesToCrm(orgId) {
        const org = await Orgs.findOneAsync(orgId);
        if (!org || org.sendingToEnroll) {
            console.log(`Invalid org or currently sending to CRM ${orgId}.`);
            return;
        }
        console.log(`Sending families to CRM for org ${orgId}`);
        await Orgs.updateAsync({ _id: orgId }, { $set: { sendingToEnroll: true } });
        const children = await People.find({ orgId: orgId, type: 'person', inActive: { $ne: true } }, { fields: { _id: 1 }}).fetchAsync();
        for (const child of children) {
            await this.sendUpdateAfterAddingRelationship(child._id);
        }
        await Orgs.updateAsync({ _id: orgId }, { $set: { sendingToEnroll: false } });
    }

    /**
     * Send the family data to CRM after an update has been made to a relationship.
     *
     * @param childPersonId
     * @returns {Promise<void>}
     */
    static async sendUpdateAfterAddingRelationship(childPersonId) {
        const person = await People.findOneAsync({ _id: childPersonId });
        if (!person || person.type !== 'person') {
            return;
        }

        // Get the Enroll account for the current org and login
        const account = await ChildcareCrmAccounts.findOneAsync({ 'centers.orgId': person.orgId });
        const org = await Orgs.findOneAsync({ _id: person.orgId });
        if (!account || !org) {
            return;
        }
        const orgId = org._id;

        const center = account.centers.find(center => center.orgId === org._id);
        if (!center || !center.id || !Number.isInteger(center.id)) {
            return;
        }
        const centerId = center.id;
        const prefix = org.profileDataPrefix();

        const token = await ChildcareCRM.getAccountToken(account);
        const headers = await ChildcareCRM.getBasicCrmRequestHeaders(account, token);
        if (!headers) {
            console.log('Invalid headers');
            return;
        }
        const manageIntegrationId = await this.getManageIntegrationId(account, token);
        if (!headers) {
            console.log('Could not retrieve the Manage integration id.');
            return;
        }

        // Build out the family member of this child: contacts and other children

        // Find the contact relationships with this person (child)
        const relationships = await person.findOwnedRelationships();
        const allFamilyIds = [];
        const otherContactIds = [];
        const childIds = [];
        const contactRelationshipMap = new Map();
        const familyRelationshipMap = new Map();
        let primaryFound = false;
        const relationshipIds = relationships.map(rel => rel._id);
        const onlyOtherContactIds = [];
        for (const relationship of relationships) {
            contactRelationshipMap.set(relationship.personId, relationship);
            allFamilyIds.push(relationship.personId);
            if (relationship.relationshipType === 'family') {
                primaryFound = true;
                familyRelationshipMap.set(relationship.personId, relationship);
                // Get the children that have a relationship with the contact
                const childRelationships = await Relationships.find({ orgId: person.orgId, personId: relationship.personId, relationshipType: 'family' }).fetchAsync();
                childIds.push(...childRelationships.map(rel => rel.targetId));
            } else {
                otherContactIds.push(relationship.personId);
            }
        }
        for (const possibleId of allFamilyIds) {
            if (otherContactIds.includes(possibleId) && !familyRelationshipMap.has(possibleId)) {
                onlyOtherContactIds.push(possibleId);
            }
        }

        if (!primaryFound) {
            console.log('could not find primary');
            return;
        }

        const children = await People.find({_id: { $in: childIds }, inActive: { $ne: true }}).fetchAsync();
        const otherRels = await Relationships.find({
            _id: { $nin: relationshipIds },
            orgId: person.orgId,
            targetId: { $in: childIds },
            relationshipType: 'family'
        }).fetchAsync();
        for (const rel of otherRels) {
            contactRelationshipMap.set(rel.personId, rel);
            allFamilyIds.push(rel.personId);
            familyRelationshipMap.set(rel.personId, rel);
        }
        const contacts = await People.find({_id: { $in: allFamilyIds }, inActive: { $ne: true }}, { sort: { createdAt: 1, _id: 1 } }).fetchAsync();
        for (const contact of contacts) {
            const relationships = await Relationships.find( { orgId, personId: contact._id, targetId: { $in: childIds } }).fetchAsync();
            const relTypes = relationships.map(rel => rel.relationshipType);
            contact.isFamily = relTypes.includes('family');
            contact.isAuthorizedPickup = relTypes.includes('authorizedPickup');
            contact.isEmergencyContact = relTypes.includes('emergencyContact');
            contact.isPrimary = relationships.some(rel => rel.primaryCaregiver);
            contact.cmsId = contact.profileData?.historicalCMSid;
        }
        contacts.sort((a, b) => {
            if (a.isFamily !== b.isFamily) {
                return a.isFamily ? -1 : 1;
            }
            if (!a.isFamily && !b.isFamily && a.isAuthorizedPickup !== b.isAuthorizedPickup) {
                return a.isAuthorizedPickup ? -1 : 1;
            }
            if (!a.isFamily && !b.isFamily && !a.isAuthorizedPickup && !b.isAuthorizedPickup && a.isEmergencyContact !== b.isEmergencyContact) {
                return a.isEmergencyContact ? -1 : 1;
            }
            if (a.isPrimary !== b.isPrimary) {
                return a.isPrimary ? -1 : 1;
            }
            if (a.createdAt !== b.createdAt) {
                return a.createdAt < b.createdAt ? -1 : 1;
            }
            return a._id < b._id ? -1 : 1;
        });

        let idx = 0;
        let hasSecondary = false;
        let secondaryManageId = null;
        for (const contact of contacts) {
            if (idx === 0) {
                contact.isPrimaryGuardian = true;
            }
            if (idx === 1 && contact.isFamily) {
                contact.isSecondaryGuardian = true;
                hasSecondary = true;
                secondaryManageId = contact._id;
            }
            idx++;
        }

        let familyId = null;
        let primaryContactId = null;
        const familyData = {
            is_active: true,
            primary_guardian: {},
            good_standing: true
        };

        const contactsData = [];
        const childrenData = [];
        const contactCrmIdsMap = new Map();
        const childrenCrmIdsMap = new Map();
        const otherContactCrmIdsMap = new Map();
        const childrenCmsIdsMap = new Map();

        // Create the primary guardian first
        primaryFound = false;
        for (const contact of contacts) {
            const relationship = contactRelationshipMap.get(contact._id);
            if (!relationship) {
                continue;
            }
            const familyRelationship = familyRelationshipMap.get(contact._id);
            const data = {
                first_name: contact.firstName,
                last_name: contact.lastName,
                address: {
                    address1: prefix ? contact[prefix]?.householdInformation?.parentStreetAddress : contact.householdInformation?.parentStreetAddress ?? '',
                    locality: prefix ? contact[prefix]?.householdInformation?.parentCity : contact.householdInformation?.parentCity ?? '',
                    region: prefix ? contact[prefix]?.householdInformation?.parentState : contact.householdInformation?.parentState ?? '',
                    postcode: prefix ? contact[prefix]?.householdInformation?.parentZip : contact.householdInformation?.parentZip ?? '',
                },
                email: contact.profileEmailAddress ?? '',
                integrations: [
                    {
                        integration_partner_id: manageIntegrationId,
                        id: contact._id
                    }
                ]
            }
            try {
                const phoneNumber = parsePhoneNumber(prefix ? contact[prefix]?.phonePrimary : contact.phonePrimary, 'US');
                if (phoneNumber) {
                    data.primary_phone = {
                        number: phoneNumber.number,
                        type: null
                    }
                }
            } catch (e) {
                // noop
            }

            if (familyRelationshipMap && !primaryFound) {
                data.center_id = centerId;
                data.status = CRM_STATUSES.NEW_LEAD;
                familyData.primary_guardian = data;
                primaryFound = true;
                primaryContactId = contact._id;
            } else {
                data.is_family = contact.isFamily;
                data.is_authorized_pickup = contact.isAuthorizedPickup;
                data.is_emergency_contact = contact.isEmergencyContact;
                contactsData.push(data);
            }

            if (contact.childcareCrm) {
                if (contact.childcareCrm.contactId) {
                    contactCrmIdsMap.set(contact._id, contact.childcareCrm);
                }
                if (contact.childcareCrm.familyId && !familyId) {
                    familyId = contact.childcareCrm.familyId;
                }
            }
            if (otherContactIds.includes(contact._id)) {
                otherContactCrmIdsMap.set(contact._id, contact.childcareCrmOtherContacts || []);
            }
        }

        if (!primaryFound) {
            console.log('could not find primary contact');
            return;
        }

        for (const child of children) {
            const birthdayValue = prefix ? child[prefix]?.birthday : child.birthday;
            const data = {
                first_name: child.firstName,
                last_name: child.lastName,
                status: CRM_STATUSES.REGISTERED,
                date_of_birth: birthdayValue ? moment(birthdayValue).format('YYYY-MM-DD') : null,
                good_standing: true,
                integrations: [
                    {
                        integration_partner_id: manageIntegrationId,
                        id: child._id
                    }
                ]
            };

            childrenData.push(data);

            if (child.childcareCrm) {
                childrenCrmIdsMap.set(child._id, child.childcareCrm);
            }
            if (child.profileData?.historicalCMSid) {
                childrenCmsIdsMap.set(child._id, child.profileData?.historicalCMSid);
            }
        }

        let existingFamily = null;
        if (familyId) {
            existingFamily = await ChildcareCRM.getFamily({ token, familyId });
        }
        let retrievedFamily = null;
        if (!familyId) {
            const duplicate = await ChildcareCRM.searchDupeFamilies({ token, centerId, primary: contacts[0], secondary: hasSecondary ? contacts[1] : null, org });
            if (duplicate && duplicate.length > 1) {
                console.log('Too many duplicates, noting potential duplicate ids.');
                await People.direct.updateAsync({ _id: primaryContactId }, { $set: { 'childcareCrm.potentials': duplicate } });
                return;
            }

            if (duplicate && duplicate.length === 1) {
                // Get the family
                retrievedFamily = await ChildcareCRM.getFamily({ token, familyId: duplicate[0] });
                if (!retrievedFamily) {
                    console.log('Could not add the family');
                    return;
                }
                familyId = retrievedFamily.id;
                const guardianCrmData = {
                    familyId: null,
                    guardianId: null,
                    centerId: centerId
                };
                guardianCrmData.familyId = familyId;
                guardianCrmData.guardianId = retrievedFamily.primary_guardian.id;

                let manageIntegrationFound = false;
                const tmpIntegrations = retrievedFamily.primary_guardian.integrations.map(i => {
                    if (i.integration_partner_id === manageIntegrationId) {
                        manageIntegrationFound = true;
                    }
                    return {
                        integration_partner_id: i.integration_partner_id,
                        id: i.id
                    };
                });

                // Update the integrations if needed
                if (!manageIntegrationFound) {
                    tmpIntegrations.push({
                        integration_partner_id: manageIntegrationId,
                        id: familyData.primary_guardian.integrations[0].id
                    });
                    familyData.primary_guardian.integrations = tmpIntegrations;
                }

                familyData.primary_guardian.id = retrievedFamily.primary_guardian.id;

                const familyResponse = await fetch(`${this.getUrl()}/api/v3/families/${retrievedFamily['id']}?fromManage=1`, {
                    method: 'PUT',
                    body: JSON.stringify(familyData),
                    headers
                });
                guardianCrmData.familyId = familyId;
                guardianCrmData.guardianId = retrievedFamily.primary_guardian.id;

                // Set the integration data
                await People.direct.updateAsync({ _id: primaryContactId }, { $set: { childcareCrm: guardianCrmData } });
                contactCrmIdsMap.set(primaryContactId, guardianCrmData);
            }

        }
        if (!familyId) {
            const inquiryType = await ChildcareCRM.getTypeByIdentifier(token, account, 'family/inquiry', this.INQUIRY_TYPE);
            const sourceType = await ChildcareCRM.getTypeByIdentifier(token, account, 'family/source', this.SOURCE_TYPE);

            // If the above aren't found, this will throw an error on insert, so return
            if (!inquiryType || !sourceType) {
                console.log('Could not retrieve required data.');
                return;
            }

            // Set required data for inserts/updates
            familyData.inquiry_type = inquiryType;
            familyData.source_type = sourceType;

            const guardianCrmData = {
                familyId: null,
                guardianId: null,
                centerId: centerId
            };

            // Add the base family first
            let familyResponse = await fetch(`${this.getUrl()}/api/v3/families?allow_duplicates=1`, {
                method: 'POST',
                body: JSON.stringify(familyData),
                headers
            });

            if (![200, 201].includes(familyResponse.status)) {
                console.log('Could not add/update the family', await familyResponse.text());
                console.log('here is the payload', familyData);
                return;
            }

            const returnedFamilyData = await familyResponse.json();
            familyId = returnedFamilyData.id;
            guardianCrmData.familyId = familyId;
            guardianCrmData.guardianId = returnedFamilyData.primary_guardian.id;

            // Set the integration data
            await People.direct.updateAsync({ _id: primaryContactId }, { $set: { childcareCrm: guardianCrmData } });
            contactCrmIdsMap.set(primaryContactId, guardianCrmData);
        } else if (existingFamily) {
            // update lead if our priority order has changed since last time
            if (contacts[0].childcareCrm?.contactId) {
                // the primary contact is now the lead, so let's make sure we first delete it as a contact
                await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${contacts[0].childcareCrm.contactId}?fromManage=1`, {
                    method: 'DELETE',
                    headers
                });
            }
            const guardianUpdate = familyData.primary_guardian;
            guardianUpdate.id = existingFamily.primary_guardian.id;
            const updateRes = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/guardians/${existingFamily.primary_guardian.id}?fromManage=1`, {
                method: 'PUT',
                body: JSON.stringify(guardianUpdate),
                headers
            });
            await People.direct.updateAsync({ _id: primaryContactId }, { $set: { childcareCrm: { familyId , guardianId: existingFamily.primary_guardian.id } } });
        }
        if (!hasSecondary && existingFamily && existingFamily.guardians?.length > 1) {
            // we no longer have a secondary guardian, let's remove it from CRM
            await fetch(`${this.getUrl()}/api/v3/families/${familyId}/guardians/${existingFamily.guardians[1].id}?fromManage=1`, {
                method: 'DELETE',
                headers
            });
        }
        if (hasSecondary) {
            const guardianData = contactsData.shift();
            if (contacts[1].childcareCrm?.contactId) {
                // secondary guardian is currently a contact, let's remove it first
                const delRes = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${contacts[1].childcareCrm.contactId}?fromManage=1`, {
                    method: 'DELETE',
                    headers
                });
            } else {
                const otherContactMatch = (contacts[1].childcareCrmOtherContacts || []).find(c => c.familyId === familyId);
                if (otherContactMatch) {
                    // secondary guardian is currently a contact, let's remove it first
                    const delRes = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${otherContactMatch.contactId}?fromManage=1`, {
                        method: 'DELETE',
                        headers
                    });
                    await People.updateAsync({_id: contacts[1]._id}, { $pull: { childcareCrmOtherContacts: { familyId } } });
                }
            }
            const crmData = {
                familyId
            };
            if (existingFamily && existingFamily.guardians?.length > 1) {
                // update the existing secondary guardian if it exists
                guardianData.id = existingFamily.guardians[1].id;
                const updateRes = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/guardians/${existingFamily.guardians[1].id}?fromManage=true`, {
                    method: 'PUT',
                    body: JSON.stringify(guardianData),
                    headers
                });
                crmData.guardianId = existingFamily.guardians[1].id;
            } else {
                // or create the secondary guardian if it doesn't
                let guardianResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/guardians?fromManage=true`, {
                    method: 'POST',
                    body: JSON.stringify([guardianData]),
                    headers
                });
                if (guardianResponse.status === 409) {
                    const familyCheck = await ChildcareCRM.getFamily({token, familyId: familyId});
                    const matchedContact = familyCheck.contacts.find(c => c.name === guardianData.first_name + ' ' + guardianData.last_name);
                    const delRes = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${matchedContact.id}?fromManage=1`, {
                        method: 'DELETE',
                        headers
                    });
                    guardianResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/guardians?fromManage=true`, {
                        method: 'POST',
                        body: JSON.stringify([guardianData]),
                        headers
                    });
                }
                crmData.guardianId = (await guardianResponse.json())[0].id;
            }
            await People.direct.updateAsync({ _id: secondaryManageId }, { $set: { childcareCrm: crmData } });
        }

        // Update the contacts
        for (const contactData of contactsData) {
            if (contactCrmIdsMap.has(contactData.integrations[0].id)) {
                // make sure the contact type, etc is still set correctly
                await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${contactCrmIdsMap.get(contactData.integrations[0].id).contactId}?fromManage=true`, {
                    method: 'PUT',
                    body: JSON.stringify(contactData),
                    headers
                });
                continue;
            }

            const otherContactCrmId = otherContactCrmIdsMap.get(contactData.integrations[0].id)?.find(crmId => crmId.familyId === familyId);
            if (otherContactCrmId) {
                await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${otherContactCrmId.contactId}?fromManage=true`, {
                    method: 'PUT',
                    body: JSON.stringify(contactData),
                    headers
                });
                continue;

            }

            // Add the contact
            let contactResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts?fromManage=true`, {
                method: 'POST',
                body: JSON.stringify([contactData]),
                headers
            });

            // If duplicate
            if (contactResponse.status === 409) {
                // check secondary guardian
                if (existingFamily?.guardians?.length > 1) {
                   if (existingFamily.guardians[1].first_name === contactData.first_name && existingFamily.guardians[1].last_name === contactData.last_name) {
                       await fetch(`${this.getUrl()}/api/v3/families/${familyId}/guardians/${existingFamily.guardians[1].id}?fromManage=1`, {
                           method: 'DELETE',
                           headers
                       });
                       contactResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts?fromManage=true`, {
                           method: 'POST',
                           body: JSON.stringify([contactData]),
                           headers
                       });
                       continue;
                   }
                }
                // Find the duplicate
                const duplicate = await this.findDuplicatePerson(centerId, this.SEARCH_CONTACT, contactData.first_name, contactData.last_name, headers, familyId);
                if (!duplicate) {
                    console.log('Could not find the contact');
                    continue;
                }
                if (duplicate.length > 1) {
                    console.log('Too many duplicate contacts, noting potential duplicate ids.');
                    await People.direct.updateAsync({ _id: contactData.integrations[0].id }, { $set: { 'childcareCrm.potentials': duplicate } });
                    continue;
                }
                // update duplicate with new data
                await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${duplicate[0]}?fromManage=true`, {
                    method: 'PUT',
                    body: JSON.stringify(contactData),
                    headers
                });
                const retrievedContact = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${duplicate[0]}`, {
                    method: 'GET',
                    headers
                });
                if (!retrievedContact) {
                    console.log('Could not find the contact');
                    continue;
                }

                let manageIntegrationFound = false;
                const tmpIntegrations = (retrievedContact?.integrations || []).map(i => {
                    if (i.integration_partner_id === manageIntegrationId) {
                        manageIntegrationFound = true;
                    }
                    return {
                        integration_partner_id: i.integration_partner_id,
                        id: i.id
                    };
                });

                // Update the integrations
                if (!manageIntegrationFound) {
                    tmpIntegrations.push({
                        integration_partner_id: manageIntegrationId,
                        id: contactData.integrations[0].id
                    });

                    contactData.integrations = tmpIntegrations;

                    contactResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/contacts/${retrievedContact.id}?fromManage=1`, {
                        method: 'PUT',
                        body: JSON.stringify(contactData),
                        headers
                    });
                }
            } else if (![200, 201, 409].includes(contactResponse.status)) {
                console.log('Could not add/update the contact', contactResponse.status);
                console.log(await contactResponse.text());
            }
        }

        const contactCrmData = {
            familyId: familyId,
            contactId: null,
            centerId: centerId
        };
        // Update the childcareCrm data for the contacts here since the id isn't returned in the post/put
        let firstContactFamilyCheck = true;
        for (const contactData of contactsData) {
            const contactManageId = contactData.integrations.find(i => i.integration_partner_id === manageIntegrationId)?.id;
            if (!contactManageId) {
                console.log('Could not update the contact integration data');
                continue;
            }
            if (contactCrmIdsMap.has(contactManageId) && !onlyOtherContactIds.includes(contactManageId)) {
                continue;
            }
            if (firstContactFamilyCheck) {
                retrievedFamily = await ChildcareCRM.getFamily({token, familyId: familyId});
                firstContactFamilyCheck = false;
            }
            if (!retrievedFamily) {
                console.log('Could not update the contact integration data');
                continue;
            }

            const matchedContact = retrievedFamily.contacts.find(c => c.name === contactData.first_name + ' ' + contactData.last_name);
            if (!matchedContact) {
                continue;
            }
            contactCrmData.contactId = matchedContact.id;
            if (onlyOtherContactIds.includes(contactManageId)) {
                await People.direct.updateAsync({ _id: contactManageId },
                    { $addToSet: { childcareCrmOtherContacts: contactCrmData } },
                    { $setOnInsert: { childcareCrmOtherContacts: [contactCrmData] } }
                );
                const contactPerson = await People.findOneAsync({ _id: contactManageId });
                if (contactPerson?.childcareCrm?.familyId === familyId) {
                    await People.direct.updateAsync({ _id: contactManageId }, { $unset: { childcareCrm: 1 } });
                }
            } else {
                await People.direct.updateAsync({ _id: contactManageId }, { $set: { childcareCrm: contactCrmData } });
                await People.direct.updateAsync({_id: contactManageId}, { $pull: { childcareCrmOtherContacts: { familyId } } });
            }
        }

        const childCrmData = {
            familyId: familyId,
            childId: null,
            centerId: centerId
        };
        // Add the children
        for (const childData of childrenData) {
            const childManageId = childData.integrations[0].id;
            if (childrenCrmIdsMap.has(childManageId)) {
                continue;
            }

            let cmsDupeCheck = null;
            // search for cms hit
            const cmsId = childrenCmsIdsMap.get(childData.integrations[0].id);
            if (cmsId) {
                const cmsDupeCheckResponse = await fetch(`${this.getUrl()}/api/v3/families/manage/dupe-cms-id-check?other_cms_id=${cmsId}&center_id=${centerId}&type=child`, {
                    method: 'GET',
                    headers
                });
                if (cmsDupeCheckResponse.status === 200) {
                    const cmsDupeCheckObject = await cmsDupeCheckResponse.json();
                    if (cmsDupeCheckObject?.familyId === familyId) {
                        cmsDupeCheck = cmsDupeCheckObject.childId;
                    }
                }
            }

            let childResponse = null;
            if (!cmsDupeCheck) {
                childResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/children?fromManage=1`, {
                    method: 'POST',
                    body: JSON.stringify([childData]),
                    headers
                });
            }

            let childId = null;
            if (cmsDupeCheck || childResponse?.status === 409) {
                let duplicate = null;
                if (cmsDupeCheck) {
                    duplicate = [cmsDupeCheck];
                } else {
                    duplicate = await this.findDuplicatePerson(centerId, this.SEARCH_CHILD, childData.first_name, childData.last_name, headers, familyId);
                    if (!duplicate) {
                        console.log('Could not find the child');
                        continue;
                    }
                    if (duplicate.length > 1) {
                        console.log('Too many duplicate children, noting potential duplicate ids.');
                        await People.direct.updateAsync({ _id: childManageId }, { $set: { 'childcareCrm.potentials': duplicate } });
                        continue;
                    }
                }

                if (!retrievedFamily) {
                    retrievedFamily = await ChildcareCRM.getFamily({token, familyId});
                }
                if (!retrievedFamily) {
                    console.log('Could not find the family');
                    return;
                }

                const retrievedChild = retrievedFamily.children.find(c => c.id === duplicate[0]);
                if (!retrievedChild) {
                    console.log('Could not find the child for integrations');
                    continue;
                }
                childId = retrievedChild.id;
                let manageIntegrationFound = false;
                const tmpIntegrations = retrievedChild.integrations.map(i => {
                    if (i.integration_partner_id === manageIntegrationId) {
                        manageIntegrationFound = true;
                    }
                    return {
                        integration_partner_id: i.integration_partner_id,
                        id: i.id
                    };
                });

                // Update the integrations
                if (!manageIntegrationFound) {
                    tmpIntegrations.push({
                        integration_partner_id: manageIntegrationId,
                        id: childData.integrations[0].id
                    });

                    childData.integrations = tmpIntegrations;
                }

                childResponse = await fetch(`${this.getUrl()}/api/v3/families/${familyId}/children/${retrievedChild.id}?fromManage=1`, {
                    method: 'PUT',
                    body: JSON.stringify(childData),
                    headers
                });
            } else if (childResponse.status === 201) {
                childId = (await childResponse.json())[0]['id'];
            }

            if (!cmsDupeCheck && ![200, 201, 409].includes(childResponse.status)) {
                console.log('Could not add/update the child', childResponse.status);
                continue;
            }

            childCrmData.childId = childId;
            // Set the integration data
            await People.direct.updateAsync({ _id: childManageId }, { $set: { childcareCrm: childCrmData } });
            childrenCrmIdsMap.set(childManageId, childCrmData);
            // Make sure the child status is correct; only change the status if there are reservations
            const reservations = await Reservations.find({ selectedPerson: childManageId }).fetchAsync();
            if (reservations.length) {
                Meteor.defer(function () {
                    ChildcareCRM.syncChildStatusFromSchedules(childManageId);
                });
            }
        }
    }

    /**
     * Find a duplicate person in CRM given the first and last name.
     *
     * @param centerId
     * @param searchPersonType
     * @param firstName
     * @param lastName
     * @param headers
     * @param familyId
     * @returns {Promise<*|null>}
     */
    static async findDuplicatePerson(centerId, searchPersonType, firstName, lastName, headers, familyId = null) {
        const tokens = (firstName + ' ' + lastName).split(' ');
        for (const token of tokens) {
            // Hyphenated names should be searched as individual tokens, as well as compound
            if (token.includes('-')) {
                for (const subToken of token.split('-')) {
                    tokens.push(subToken);
                }
            }
        }
        const paramsArray = {};
        if (searchPersonType === this.SEARCH_CHILD) {
            paramsArray.child_first_name = firstName;
            paramsArray.child_last_name = lastName;
        } else {
            paramsArray.guardian_first_name = firstName;
            paramsArray.guardian_last_name = lastName;
        }
        if (searchPersonType === this.SEARCH_CONTACT) {
            paramsArray['contact_mode'] = 1;
        }

        const queriesString = new URLSearchParams(paramsArray).toString();
        const searchUrl = `${this.getUrl()}/api/v3/search?center_ids=${centerId}&duplicate_check=1&${queriesString}`;
        const duplicateResponse = await fetch(searchUrl, {
            method: 'GET',
            headers: headers,
        });
        if (duplicateResponse.status !== 200) {
            console.log('Could not perform search');
            console.log(await duplicateResponse.text());
            return null;
        }

        const duplicateResults = await duplicateResponse.json();
        const filteredResults = [];
        for (const result of duplicateResults?.results || []) {
            const familyMatch = familyId ? result.id === familyId : true;
            switch (searchPersonType) {
                case this.SEARCH_LEAD:
                    if (result.primary_guardian?.first_name?.toLowerCase()?.trim() === firstName.toLowerCase().trim() &&
                        result.primary_guardian?.last_name?.toLowerCase()?.trim() === lastName.toLowerCase().trim()) {
                        filteredResults.push(result.id);
                    }
                    break;
                case this.SEARCH_CHILD:
                    if (familyMatch) {
                        for (const child of result.children || []) {
                            if (child.first_name?.toLowerCase()?.trim() === firstName.toLowerCase().trim() &&
                                child.last_name?.toLowerCase()?.trim() === lastName.toLowerCase().trim()) {
                                filteredResults.push(child.id);
                            }
                        }
                    }
                    break;
                case this.SEARCH_CONTACT:
                    if (familyMatch) {
                        for (const contact of result.contacts || []) {
                            if (contact.name?.toLowerCase()?.trim() ===
                                firstName.toLowerCase().trim() + ' ' + lastName.toLowerCase().trim()) {
                                filteredResults.push(contact.id);
                            }
                        }
                    }
                    break;
            }
        }
        if (filteredResults.length === 0) {
            return null;
        }

        return filteredResults;
    }
}