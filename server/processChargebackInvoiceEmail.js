import { SSR } from "../lib/util/ssrUtils";
import { Blaze } from "meteor/blaze";
import { generateEmailWhiteLabelData } from "./util";
import { Orgs } from "../lib/collections/orgs";
import moment from 'moment-timezone';
/**
 * Handles emails for chargebacks.
 *
 * @class ChargebackEmailService
 */
export class ChargebackEmailService {
	/**
	 * Sends an email to the account holder for a chargeback invoice.
	 *
	 * @param options
	 */
	static async processChargebackInvoiceEmail(options) {
		const currentRecipient = options.accountHolderEmail,
			chargebackInvoice = options.chargebackInvoice;

		if (currentRecipient) {
			console.log("processing chargeback invoice email for recipient");
			SSR.compileTemplate('messageEmail', await Assets.getTextAsync('email_templates/v2021/chargeback_notice.html'));

			const org = await Orgs.findOneAsync(chargebackInvoice.orgId);
			Blaze.Template.messageEmail.helpers({
				"equals": function (val1, val2) {
					return (val1 === val2) ? true : false;
				},
				"formatTime": function (val) {
					return (val.toUpperCase());
				},
				"appUrl": function () {
					var baseUrl = (org && org.whiteLabel && org.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
					if (baseUrl.slice(baseUrl.length - 1) != "/") baseUrl += "/";
					return baseUrl;
				}
			});

			let message = `This is a summary of chargeback fees incurred for ${ chargebackInvoice.periodString }:<br/><br/>`;

			chargebackInvoice.allChargebackItems.forEach(cbi => {
				message += `${ cbi.detailString }<br/>`
			});

			message += `<br/>Total Items: ${ chargebackInvoice.allChargebackItems.length }`;
			message += `<br/>Total Fee Amount: ${ numeral(chargebackInvoice.feeTotal).format('$0.00') }<br/><br/>`;
			message += `This amount will be deducted from the next available settlement.<br/><br/>`


			var emailData = {
				message,
				emailDateStamp: new moment().tz(org.getTimezone()).format("dddd MMMM Do, YYYY")
			};


			const whiteLabel = generateEmailWhiteLabelData(org);
			emailData.whiteLabel = whiteLabel
			emailData.backgroundColor = `${ whiteLabel.primaryColor }1A`;
			emailData.headerOrgNameColor = "#8E8E93";
			emailData.headerBgColor = whiteLabel.primaryColor;
			emailData.secondaryColor = whiteLabel.secondaryColor;
			emailData.assetPrefix = `emailAssets/${ whiteLabel.emailAssetPrefix }`;
			emailData.currentYear = new moment().format("YYYY");


			const emailOptions = {
				from: "LineLeader support <<EMAIL>>",
				to: currentRecipient,
				subject: "Notice of chargeback for " + org.name,
				html: SSR.render('messageEmail', emailData)
			};
			if (org.replyToAddress) emailOptions.replyTo = org.replyToAddress;
			if (org.fromAddress) emailOptions.from = org.fromAddress;
			try {
				await Email.sendAsync(emailOptions);
			} catch (e) {
				console.log(e);
			}
		}
	}
}
